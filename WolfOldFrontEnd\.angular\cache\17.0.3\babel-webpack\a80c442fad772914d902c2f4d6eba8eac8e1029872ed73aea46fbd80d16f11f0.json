{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport * as graphic from '../../util/graphic.js';\nimport { toggleHoverEmphasis } from '../../util/states.js';\nimport { createSymbol, normalizeSymbolOffset } from '../../util/symbol.js';\nimport { parsePercent, isNumeric } from '../../util/number.js';\nimport ChartView from '../../view/Chart.js';\nimport { getDefaultLabel } from '../helper/labelHelper.js';\nimport { setLabelStyle, getLabelStatesModels } from '../../label/labelStyle.js';\nimport ZRImage from 'zrender/lib/graphic/Image.js';\nimport { getECData } from '../../util/innerStore.js';\nvar BAR_BORDER_WIDTH_QUERY = ['itemStyle', 'borderWidth']; // index: +isHorizontal\n\nvar LAYOUT_ATTRS = [{\n  xy: 'x',\n  wh: 'width',\n  index: 0,\n  posDesc: ['left', 'right']\n}, {\n  xy: 'y',\n  wh: 'height',\n  index: 1,\n  posDesc: ['top', 'bottom']\n}];\nvar pathForLineWidth = new graphic.Circle();\nvar PictorialBarView = /** @class */\nfunction (_super) {\n  __extends(PictorialBarView, _super);\n  function PictorialBarView() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.type = PictorialBarView.type;\n    return _this;\n  }\n  PictorialBarView.prototype.render = function (seriesModel, ecModel, api) {\n    var group = this.group;\n    var data = seriesModel.getData();\n    var oldData = this._data;\n    var cartesian = seriesModel.coordinateSystem;\n    var baseAxis = cartesian.getBaseAxis();\n    var isHorizontal = baseAxis.isHorizontal();\n    var coordSysRect = cartesian.master.getRect();\n    var opt = {\n      ecSize: {\n        width: api.getWidth(),\n        height: api.getHeight()\n      },\n      seriesModel: seriesModel,\n      coordSys: cartesian,\n      coordSysExtent: [[coordSysRect.x, coordSysRect.x + coordSysRect.width], [coordSysRect.y, coordSysRect.y + coordSysRect.height]],\n      isHorizontal: isHorizontal,\n      valueDim: LAYOUT_ATTRS[+isHorizontal],\n      categoryDim: LAYOUT_ATTRS[1 - +isHorizontal]\n    };\n    data.diff(oldData).add(function (dataIndex) {\n      if (!data.hasValue(dataIndex)) {\n        return;\n      }\n      var itemModel = getItemModel(data, dataIndex);\n      var symbolMeta = getSymbolMeta(data, dataIndex, itemModel, opt);\n      var bar = createBar(data, opt, symbolMeta);\n      data.setItemGraphicEl(dataIndex, bar);\n      group.add(bar);\n      updateCommon(bar, opt, symbolMeta);\n    }).update(function (newIndex, oldIndex) {\n      var bar = oldData.getItemGraphicEl(oldIndex);\n      if (!data.hasValue(newIndex)) {\n        group.remove(bar);\n        return;\n      }\n      var itemModel = getItemModel(data, newIndex);\n      var symbolMeta = getSymbolMeta(data, newIndex, itemModel, opt);\n      var pictorialShapeStr = getShapeStr(data, symbolMeta);\n      if (bar && pictorialShapeStr !== bar.__pictorialShapeStr) {\n        group.remove(bar);\n        data.setItemGraphicEl(newIndex, null);\n        bar = null;\n      }\n      if (bar) {\n        updateBar(bar, opt, symbolMeta);\n      } else {\n        bar = createBar(data, opt, symbolMeta, true);\n      }\n      data.setItemGraphicEl(newIndex, bar);\n      bar.__pictorialSymbolMeta = symbolMeta; // Add back\n\n      group.add(bar);\n      updateCommon(bar, opt, symbolMeta);\n    }).remove(function (dataIndex) {\n      var bar = oldData.getItemGraphicEl(dataIndex);\n      bar && removeBar(oldData, dataIndex, bar.__pictorialSymbolMeta.animationModel, bar);\n    }).execute();\n    this._data = data;\n    return this.group;\n  };\n  PictorialBarView.prototype.remove = function (ecModel, api) {\n    var group = this.group;\n    var data = this._data;\n    if (ecModel.get('animation')) {\n      if (data) {\n        data.eachItemGraphicEl(function (bar) {\n          removeBar(data, getECData(bar).dataIndex, ecModel, bar);\n        });\n      }\n    } else {\n      group.removeAll();\n    }\n  };\n  PictorialBarView.type = 'pictorialBar';\n  return PictorialBarView;\n}(ChartView); // Set or calculate default value about symbol, and calculate layout info.\n\nfunction getSymbolMeta(data, dataIndex, itemModel, opt) {\n  var layout = data.getItemLayout(dataIndex);\n  var symbolRepeat = itemModel.get('symbolRepeat');\n  var symbolClip = itemModel.get('symbolClip');\n  var symbolPosition = itemModel.get('symbolPosition') || 'start';\n  var symbolRotate = itemModel.get('symbolRotate');\n  var rotation = (symbolRotate || 0) * Math.PI / 180 || 0;\n  var symbolPatternSize = itemModel.get('symbolPatternSize') || 2;\n  var isAnimationEnabled = itemModel.isAnimationEnabled();\n  var symbolMeta = {\n    dataIndex: dataIndex,\n    layout: layout,\n    itemModel: itemModel,\n    symbolType: data.getItemVisual(dataIndex, 'symbol') || 'circle',\n    style: data.getItemVisual(dataIndex, 'style'),\n    symbolClip: symbolClip,\n    symbolRepeat: symbolRepeat,\n    symbolRepeatDirection: itemModel.get('symbolRepeatDirection'),\n    symbolPatternSize: symbolPatternSize,\n    rotation: rotation,\n    animationModel: isAnimationEnabled ? itemModel : null,\n    hoverScale: isAnimationEnabled && itemModel.get(['emphasis', 'scale']),\n    z2: itemModel.getShallow('z', true) || 0\n  };\n  prepareBarLength(itemModel, symbolRepeat, layout, opt, symbolMeta);\n  prepareSymbolSize(data, dataIndex, layout, symbolRepeat, symbolClip, symbolMeta.boundingLength, symbolMeta.pxSign, symbolPatternSize, opt, symbolMeta);\n  prepareLineWidth(itemModel, symbolMeta.symbolScale, rotation, opt, symbolMeta);\n  var symbolSize = symbolMeta.symbolSize;\n  var symbolOffset = normalizeSymbolOffset(itemModel.get('symbolOffset'), symbolSize);\n  prepareLayoutInfo(itemModel, symbolSize, layout, symbolRepeat, symbolClip, symbolOffset, symbolPosition, symbolMeta.valueLineWidth, symbolMeta.boundingLength, symbolMeta.repeatCutLength, opt, symbolMeta);\n  return symbolMeta;\n} // bar length can be negative.\n\nfunction prepareBarLength(itemModel, symbolRepeat, layout, opt, outputSymbolMeta) {\n  var valueDim = opt.valueDim;\n  var symbolBoundingData = itemModel.get('symbolBoundingData');\n  var valueAxis = opt.coordSys.getOtherAxis(opt.coordSys.getBaseAxis());\n  var zeroPx = valueAxis.toGlobalCoord(valueAxis.dataToCoord(0));\n  var pxSignIdx = 1 - +(layout[valueDim.wh] <= 0);\n  var boundingLength;\n  if (zrUtil.isArray(symbolBoundingData)) {\n    var symbolBoundingExtent = [convertToCoordOnAxis(valueAxis, symbolBoundingData[0]) - zeroPx, convertToCoordOnAxis(valueAxis, symbolBoundingData[1]) - zeroPx];\n    symbolBoundingExtent[1] < symbolBoundingExtent[0] && symbolBoundingExtent.reverse();\n    boundingLength = symbolBoundingExtent[pxSignIdx];\n  } else if (symbolBoundingData != null) {\n    boundingLength = convertToCoordOnAxis(valueAxis, symbolBoundingData) - zeroPx;\n  } else if (symbolRepeat) {\n    boundingLength = opt.coordSysExtent[valueDim.index][pxSignIdx] - zeroPx;\n  } else {\n    boundingLength = layout[valueDim.wh];\n  }\n  outputSymbolMeta.boundingLength = boundingLength;\n  if (symbolRepeat) {\n    outputSymbolMeta.repeatCutLength = layout[valueDim.wh];\n  } // if 'pxSign' means sign of pixel,  it can't be zero, or symbolScale will be zero\n  // and when borderWidth be settled, the actual linewidth will be NaN\n\n  outputSymbolMeta.pxSign = boundingLength > 0 ? 1 : -1;\n}\nfunction convertToCoordOnAxis(axis, value) {\n  return axis.toGlobalCoord(axis.dataToCoord(axis.scale.parse(value)));\n} // Support ['100%', '100%']\n\nfunction prepareSymbolSize(data, dataIndex, layout, symbolRepeat, symbolClip, boundingLength, pxSign, symbolPatternSize, opt, outputSymbolMeta) {\n  var valueDim = opt.valueDim;\n  var categoryDim = opt.categoryDim;\n  var categorySize = Math.abs(layout[categoryDim.wh]);\n  var symbolSize = data.getItemVisual(dataIndex, 'symbolSize');\n  var parsedSymbolSize;\n  if (zrUtil.isArray(symbolSize)) {\n    parsedSymbolSize = symbolSize.slice();\n  } else {\n    if (symbolSize == null) {\n      // will parse to number below\n      parsedSymbolSize = ['100%', '100%'];\n    } else {\n      parsedSymbolSize = [symbolSize, symbolSize];\n    }\n  } // Note: percentage symbolSize (like '100%') do not consider lineWidth, because it is\n  // to complicated to calculate real percent value if considering scaled lineWidth.\n  // So the actual size will bigger than layout size if lineWidth is bigger than zero,\n  // which can be tolerated in pictorial chart.\n\n  parsedSymbolSize[categoryDim.index] = parsePercent(parsedSymbolSize[categoryDim.index], categorySize);\n  parsedSymbolSize[valueDim.index] = parsePercent(parsedSymbolSize[valueDim.index], symbolRepeat ? categorySize : Math.abs(boundingLength));\n  outputSymbolMeta.symbolSize = parsedSymbolSize; // If x or y is less than zero, show reversed shape.\n\n  var symbolScale = outputSymbolMeta.symbolScale = [parsedSymbolSize[0] / symbolPatternSize, parsedSymbolSize[1] / symbolPatternSize]; // Follow convention, 'right' and 'top' is the normal scale.\n\n  symbolScale[valueDim.index] *= (opt.isHorizontal ? -1 : 1) * pxSign;\n}\nfunction prepareLineWidth(itemModel, symbolScale, rotation, opt, outputSymbolMeta) {\n  // In symbols are drawn with scale, so do not need to care about the case that width\n  // or height are too small. But symbol use strokeNoScale, where acture lineWidth should\n  // be calculated.\n  var valueLineWidth = itemModel.get(BAR_BORDER_WIDTH_QUERY) || 0;\n  if (valueLineWidth) {\n    pathForLineWidth.attr({\n      scaleX: symbolScale[0],\n      scaleY: symbolScale[1],\n      rotation: rotation\n    });\n    pathForLineWidth.updateTransform();\n    valueLineWidth /= pathForLineWidth.getLineScale();\n    valueLineWidth *= symbolScale[opt.valueDim.index];\n  }\n  outputSymbolMeta.valueLineWidth = valueLineWidth || 0;\n}\nfunction prepareLayoutInfo(itemModel, symbolSize, layout, symbolRepeat, symbolClip, symbolOffset, symbolPosition, valueLineWidth, boundingLength, repeatCutLength, opt, outputSymbolMeta) {\n  var categoryDim = opt.categoryDim;\n  var valueDim = opt.valueDim;\n  var pxSign = outputSymbolMeta.pxSign;\n  var unitLength = Math.max(symbolSize[valueDim.index] + valueLineWidth, 0);\n  var pathLen = unitLength; // Note: rotation will not effect the layout of symbols, because user may\n  // want symbols to rotate on its center, which should not be translated\n  // when rotating.\n\n  if (symbolRepeat) {\n    var absBoundingLength = Math.abs(boundingLength);\n    var symbolMargin = zrUtil.retrieve(itemModel.get('symbolMargin'), '15%') + '';\n    var hasEndGap = false;\n    if (symbolMargin.lastIndexOf('!') === symbolMargin.length - 1) {\n      hasEndGap = true;\n      symbolMargin = symbolMargin.slice(0, symbolMargin.length - 1);\n    }\n    var symbolMarginNumeric = parsePercent(symbolMargin, symbolSize[valueDim.index]);\n    var uLenWithMargin = Math.max(unitLength + symbolMarginNumeric * 2, 0); // When symbol margin is less than 0, margin at both ends will be subtracted\n    // to ensure that all of the symbols will not be overflow the given area.\n\n    var endFix = hasEndGap ? 0 : symbolMarginNumeric * 2; // Both final repeatTimes and final symbolMarginNumeric area calculated based on\n    // boundingLength.\n\n    var repeatSpecified = isNumeric(symbolRepeat);\n    var repeatTimes = repeatSpecified ? symbolRepeat : toIntTimes((absBoundingLength + endFix) / uLenWithMargin); // Adjust calculate margin, to ensure each symbol is displayed\n    // entirely in the given layout area.\n\n    var mDiff = absBoundingLength - repeatTimes * unitLength;\n    symbolMarginNumeric = mDiff / 2 / (hasEndGap ? repeatTimes : Math.max(repeatTimes - 1, 1));\n    uLenWithMargin = unitLength + symbolMarginNumeric * 2;\n    endFix = hasEndGap ? 0 : symbolMarginNumeric * 2; // Update repeatTimes when not all symbol will be shown.\n\n    if (!repeatSpecified && symbolRepeat !== 'fixed') {\n      repeatTimes = repeatCutLength ? toIntTimes((Math.abs(repeatCutLength) + endFix) / uLenWithMargin) : 0;\n    }\n    pathLen = repeatTimes * uLenWithMargin - endFix;\n    outputSymbolMeta.repeatTimes = repeatTimes;\n    outputSymbolMeta.symbolMargin = symbolMarginNumeric;\n  }\n  var sizeFix = pxSign * (pathLen / 2);\n  var pathPosition = outputSymbolMeta.pathPosition = [];\n  pathPosition[categoryDim.index] = layout[categoryDim.wh] / 2;\n  pathPosition[valueDim.index] = symbolPosition === 'start' ? sizeFix : symbolPosition === 'end' ? boundingLength - sizeFix : boundingLength / 2; // 'center'\n\n  if (symbolOffset) {\n    pathPosition[0] += symbolOffset[0];\n    pathPosition[1] += symbolOffset[1];\n  }\n  var bundlePosition = outputSymbolMeta.bundlePosition = [];\n  bundlePosition[categoryDim.index] = layout[categoryDim.xy];\n  bundlePosition[valueDim.index] = layout[valueDim.xy];\n  var barRectShape = outputSymbolMeta.barRectShape = zrUtil.extend({}, layout);\n  barRectShape[valueDim.wh] = pxSign * Math.max(Math.abs(layout[valueDim.wh]), Math.abs(pathPosition[valueDim.index] + sizeFix));\n  barRectShape[categoryDim.wh] = layout[categoryDim.wh];\n  var clipShape = outputSymbolMeta.clipShape = {}; // Consider that symbol may be overflow layout rect.\n\n  clipShape[categoryDim.xy] = -layout[categoryDim.xy];\n  clipShape[categoryDim.wh] = opt.ecSize[categoryDim.wh];\n  clipShape[valueDim.xy] = 0;\n  clipShape[valueDim.wh] = layout[valueDim.wh];\n}\nfunction createPath(symbolMeta) {\n  var symbolPatternSize = symbolMeta.symbolPatternSize;\n  var path = createSymbol(\n  // Consider texture img, make a big size.\n  symbolMeta.symbolType, -symbolPatternSize / 2, -symbolPatternSize / 2, symbolPatternSize, symbolPatternSize);\n  path.attr({\n    culling: true\n  });\n  path.type !== 'image' && path.setStyle({\n    strokeNoScale: true\n  });\n  return path;\n}\nfunction createOrUpdateRepeatSymbols(bar, opt, symbolMeta, isUpdate) {\n  var bundle = bar.__pictorialBundle;\n  var symbolSize = symbolMeta.symbolSize;\n  var valueLineWidth = symbolMeta.valueLineWidth;\n  var pathPosition = symbolMeta.pathPosition;\n  var valueDim = opt.valueDim;\n  var repeatTimes = symbolMeta.repeatTimes || 0;\n  var index = 0;\n  var unit = symbolSize[opt.valueDim.index] + valueLineWidth + symbolMeta.symbolMargin * 2;\n  eachPath(bar, function (path) {\n    path.__pictorialAnimationIndex = index;\n    path.__pictorialRepeatTimes = repeatTimes;\n    if (index < repeatTimes) {\n      updateAttr(path, null, makeTarget(index), symbolMeta, isUpdate);\n    } else {\n      updateAttr(path, null, {\n        scaleX: 0,\n        scaleY: 0\n      }, symbolMeta, isUpdate, function () {\n        bundle.remove(path);\n      });\n    } // updateHoverAnimation(path, symbolMeta);\n\n    index++;\n  });\n  for (; index < repeatTimes; index++) {\n    var path = createPath(symbolMeta);\n    path.__pictorialAnimationIndex = index;\n    path.__pictorialRepeatTimes = repeatTimes;\n    bundle.add(path);\n    var target = makeTarget(index);\n    updateAttr(path, {\n      x: target.x,\n      y: target.y,\n      scaleX: 0,\n      scaleY: 0\n    }, {\n      scaleX: target.scaleX,\n      scaleY: target.scaleY,\n      rotation: target.rotation\n    }, symbolMeta, isUpdate);\n  }\n  function makeTarget(index) {\n    var position = pathPosition.slice(); // (start && pxSign > 0) || (end && pxSign < 0): i = repeatTimes - index\n    // Otherwise: i = index;\n\n    var pxSign = symbolMeta.pxSign;\n    var i = index;\n    if (symbolMeta.symbolRepeatDirection === 'start' ? pxSign > 0 : pxSign < 0) {\n      i = repeatTimes - 1 - index;\n    }\n    position[valueDim.index] = unit * (i - repeatTimes / 2 + 0.5) + pathPosition[valueDim.index];\n    return {\n      x: position[0],\n      y: position[1],\n      scaleX: symbolMeta.symbolScale[0],\n      scaleY: symbolMeta.symbolScale[1],\n      rotation: symbolMeta.rotation\n    };\n  }\n}\nfunction createOrUpdateSingleSymbol(bar, opt, symbolMeta, isUpdate) {\n  var bundle = bar.__pictorialBundle;\n  var mainPath = bar.__pictorialMainPath;\n  if (!mainPath) {\n    mainPath = bar.__pictorialMainPath = createPath(symbolMeta);\n    bundle.add(mainPath);\n    updateAttr(mainPath, {\n      x: symbolMeta.pathPosition[0],\n      y: symbolMeta.pathPosition[1],\n      scaleX: 0,\n      scaleY: 0,\n      rotation: symbolMeta.rotation\n    }, {\n      scaleX: symbolMeta.symbolScale[0],\n      scaleY: symbolMeta.symbolScale[1]\n    }, symbolMeta, isUpdate);\n  } else {\n    updateAttr(mainPath, null, {\n      x: symbolMeta.pathPosition[0],\n      y: symbolMeta.pathPosition[1],\n      scaleX: symbolMeta.symbolScale[0],\n      scaleY: symbolMeta.symbolScale[1],\n      rotation: symbolMeta.rotation\n    }, symbolMeta, isUpdate);\n  }\n} // bar rect is used for label.\n\nfunction createOrUpdateBarRect(bar, symbolMeta, isUpdate) {\n  var rectShape = zrUtil.extend({}, symbolMeta.barRectShape);\n  var barRect = bar.__pictorialBarRect;\n  if (!barRect) {\n    barRect = bar.__pictorialBarRect = new graphic.Rect({\n      z2: 2,\n      shape: rectShape,\n      silent: true,\n      style: {\n        stroke: 'transparent',\n        fill: 'transparent',\n        lineWidth: 0\n      }\n    });\n    barRect.disableMorphing = true;\n    bar.add(barRect);\n  } else {\n    updateAttr(barRect, null, {\n      shape: rectShape\n    }, symbolMeta, isUpdate);\n  }\n}\nfunction createOrUpdateClip(bar, opt, symbolMeta, isUpdate) {\n  // If not clip, symbol will be remove and rebuilt.\n  if (symbolMeta.symbolClip) {\n    var clipPath = bar.__pictorialClipPath;\n    var clipShape = zrUtil.extend({}, symbolMeta.clipShape);\n    var valueDim = opt.valueDim;\n    var animationModel = symbolMeta.animationModel;\n    var dataIndex = symbolMeta.dataIndex;\n    if (clipPath) {\n      graphic.updateProps(clipPath, {\n        shape: clipShape\n      }, animationModel, dataIndex);\n    } else {\n      clipShape[valueDim.wh] = 0;\n      clipPath = new graphic.Rect({\n        shape: clipShape\n      });\n      bar.__pictorialBundle.setClipPath(clipPath);\n      bar.__pictorialClipPath = clipPath;\n      var target = {};\n      target[valueDim.wh] = symbolMeta.clipShape[valueDim.wh];\n      graphic[isUpdate ? 'updateProps' : 'initProps'](clipPath, {\n        shape: target\n      }, animationModel, dataIndex);\n    }\n  }\n}\nfunction getItemModel(data, dataIndex) {\n  var itemModel = data.getItemModel(dataIndex);\n  itemModel.getAnimationDelayParams = getAnimationDelayParams;\n  itemModel.isAnimationEnabled = isAnimationEnabled;\n  return itemModel;\n}\nfunction getAnimationDelayParams(path) {\n  // The order is the same as the z-order, see `symbolRepeatDiretion`.\n  return {\n    index: path.__pictorialAnimationIndex,\n    count: path.__pictorialRepeatTimes\n  };\n}\nfunction isAnimationEnabled() {\n  // `animation` prop can be set on itemModel in pictorial bar chart.\n  return this.parentModel.isAnimationEnabled() && !!this.getShallow('animation');\n}\nfunction createBar(data, opt, symbolMeta, isUpdate) {\n  // bar is the main element for each data.\n  var bar = new graphic.Group(); // bundle is used for location and clip.\n\n  var bundle = new graphic.Group();\n  bar.add(bundle);\n  bar.__pictorialBundle = bundle;\n  bundle.x = symbolMeta.bundlePosition[0];\n  bundle.y = symbolMeta.bundlePosition[1];\n  if (symbolMeta.symbolRepeat) {\n    createOrUpdateRepeatSymbols(bar, opt, symbolMeta);\n  } else {\n    createOrUpdateSingleSymbol(bar, opt, symbolMeta);\n  }\n  createOrUpdateBarRect(bar, symbolMeta, isUpdate);\n  createOrUpdateClip(bar, opt, symbolMeta, isUpdate);\n  bar.__pictorialShapeStr = getShapeStr(data, symbolMeta);\n  bar.__pictorialSymbolMeta = symbolMeta;\n  return bar;\n}\nfunction updateBar(bar, opt, symbolMeta) {\n  var animationModel = symbolMeta.animationModel;\n  var dataIndex = symbolMeta.dataIndex;\n  var bundle = bar.__pictorialBundle;\n  graphic.updateProps(bundle, {\n    x: symbolMeta.bundlePosition[0],\n    y: symbolMeta.bundlePosition[1]\n  }, animationModel, dataIndex);\n  if (symbolMeta.symbolRepeat) {\n    createOrUpdateRepeatSymbols(bar, opt, symbolMeta, true);\n  } else {\n    createOrUpdateSingleSymbol(bar, opt, symbolMeta, true);\n  }\n  createOrUpdateBarRect(bar, symbolMeta, true);\n  createOrUpdateClip(bar, opt, symbolMeta, true);\n}\nfunction removeBar(data, dataIndex, animationModel, bar) {\n  // Not show text when animating\n  var labelRect = bar.__pictorialBarRect;\n  labelRect && labelRect.removeTextContent();\n  var paths = [];\n  eachPath(bar, function (path) {\n    paths.push(path);\n  });\n  bar.__pictorialMainPath && paths.push(bar.__pictorialMainPath); // I do not find proper remove animation for clip yet.\n\n  bar.__pictorialClipPath && (animationModel = null);\n  zrUtil.each(paths, function (path) {\n    graphic.removeElement(path, {\n      scaleX: 0,\n      scaleY: 0\n    }, animationModel, dataIndex, function () {\n      bar.parent && bar.parent.remove(bar);\n    });\n  });\n  data.setItemGraphicEl(dataIndex, null);\n}\nfunction getShapeStr(data, symbolMeta) {\n  return [data.getItemVisual(symbolMeta.dataIndex, 'symbol') || 'none', !!symbolMeta.symbolRepeat, !!symbolMeta.symbolClip].join(':');\n}\nfunction eachPath(bar, cb, context) {\n  // Do not use Group#eachChild, because it do not support remove.\n  zrUtil.each(bar.__pictorialBundle.children(), function (el) {\n    el !== bar.__pictorialBarRect && cb.call(context, el);\n  });\n}\nfunction updateAttr(el, immediateAttrs, animationAttrs, symbolMeta, isUpdate, cb) {\n  immediateAttrs && el.attr(immediateAttrs); // when symbolCip used, only clip path has init animation, otherwise it would be weird effect.\n\n  if (symbolMeta.symbolClip && !isUpdate) {\n    animationAttrs && el.attr(animationAttrs);\n  } else {\n    animationAttrs && graphic[isUpdate ? 'updateProps' : 'initProps'](el, animationAttrs, symbolMeta.animationModel, symbolMeta.dataIndex, cb);\n  }\n}\nfunction updateCommon(bar, opt, symbolMeta) {\n  var dataIndex = symbolMeta.dataIndex;\n  var itemModel = symbolMeta.itemModel; // Color must be excluded.\n  // Because symbol provide setColor individually to set fill and stroke\n\n  var emphasisModel = itemModel.getModel('emphasis');\n  var emphasisStyle = emphasisModel.getModel('itemStyle').getItemStyle();\n  var blurStyle = itemModel.getModel(['blur', 'itemStyle']).getItemStyle();\n  var selectStyle = itemModel.getModel(['select', 'itemStyle']).getItemStyle();\n  var cursorStyle = itemModel.getShallow('cursor');\n  var focus = emphasisModel.get('focus');\n  var blurScope = emphasisModel.get('blurScope');\n  var hoverScale = emphasisModel.get('scale');\n  eachPath(bar, function (path) {\n    if (path instanceof ZRImage) {\n      var pathStyle = path.style;\n      path.useStyle(zrUtil.extend({\n        // TODO other properties like dx, dy ?\n        image: pathStyle.image,\n        x: pathStyle.x,\n        y: pathStyle.y,\n        width: pathStyle.width,\n        height: pathStyle.height\n      }, symbolMeta.style));\n    } else {\n      path.useStyle(symbolMeta.style);\n    }\n    var emphasisState = path.ensureState('emphasis');\n    emphasisState.style = emphasisStyle;\n    if (hoverScale) {\n      // NOTE: Must after scale is set after updateAttr\n      emphasisState.scaleX = path.scaleX * 1.1;\n      emphasisState.scaleY = path.scaleY * 1.1;\n    }\n    path.ensureState('blur').style = blurStyle;\n    path.ensureState('select').style = selectStyle;\n    cursorStyle && (path.cursor = cursorStyle);\n    path.z2 = symbolMeta.z2;\n  });\n  var barPositionOutside = opt.valueDim.posDesc[+(symbolMeta.boundingLength > 0)];\n  var barRect = bar.__pictorialBarRect;\n  setLabelStyle(barRect, getLabelStatesModels(itemModel), {\n    labelFetcher: opt.seriesModel,\n    labelDataIndex: dataIndex,\n    defaultText: getDefaultLabel(opt.seriesModel.getData(), dataIndex),\n    inheritColor: symbolMeta.style.fill,\n    defaultOpacity: symbolMeta.style.opacity,\n    defaultOutsidePosition: barPositionOutside\n  });\n  toggleHoverEmphasis(bar, focus, blurScope, emphasisModel.get('disabled'));\n}\nfunction toIntTimes(times) {\n  var roundedTimes = Math.round(times); // Escapse accurate error\n\n  return Math.abs(times - roundedTimes) < 1e-4 ? roundedTimes : Math.ceil(times);\n}\nexport default PictorialBarView;", "map": {"version": 3, "names": ["__extends", "zrUtil", "graphic", "toggleHoverEmphasis", "createSymbol", "normalizeSymbolOffset", "parsePercent", "isNumeric", "ChartView", "getDefaultLabel", "setLabelStyle", "getLabelStatesModels", "ZRImage", "getECData", "BAR_BORDER_WIDTH_QUERY", "LAYOUT_ATTRS", "xy", "wh", "index", "posDesc", "pathForLineWidth", "Circle", "PictorialBarView", "_super", "_this", "apply", "arguments", "type", "prototype", "render", "seriesModel", "ecModel", "api", "group", "data", "getData", "oldData", "_data", "cartesian", "coordinateSystem", "baseAxis", "getBaseAxis", "isHorizontal", "coordSysRect", "master", "getRect", "opt", "ecSize", "width", "getWidth", "height", "getHeight", "coordSys", "coordSysExtent", "x", "y", "valueDim", "categoryDim", "diff", "add", "dataIndex", "hasValue", "itemModel", "getItemModel", "symbolMeta", "getSymbolMeta", "bar", "createBar", "setItemGraphicEl", "updateCommon", "update", "newIndex", "oldIndex", "getItemGraphicEl", "remove", "pictorialShapeStr", "getShapeStr", "__pictorialShapeStr", "updateBar", "__pictorialSymbolMeta", "removeBar", "animationModel", "execute", "get", "eachItemGraphicEl", "removeAll", "layout", "getItemLayout", "symbolRepeat", "symbolClip", "symbolPosition", "symbolRotate", "rotation", "Math", "PI", "symbolPatternSize", "isAnimationEnabled", "symbolType", "getItemVisual", "style", "symbolRepeatDirection", "hoverScale", "z2", "getShallow", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "prepareSymbolSize", "bounding<PERSON>ength", "pxSign", "prepareLineWidth", "symbolScale", "symbolSize", "symbolOffset", "prepareLayoutInfo", "valueLineWidth", "repeatCutLength", "outputSymbolMeta", "symbolBoundingData", "valueAxis", "getOtherAxis", "zeroPx", "toGlobalCoord", "dataToCoord", "pxSignIdx", "isArray", "symbolBoundingExtent", "convertToCoordOnAxis", "reverse", "axis", "value", "scale", "parse", "categorySize", "abs", "parsedSymbolSize", "slice", "attr", "scaleX", "scaleY", "updateTransform", "getLineScale", "unitLength", "max", "pathLen", "absBoundingLength", "symbol<PERSON><PERSON><PERSON>", "retrieve", "hasEndGap", "lastIndexOf", "length", "symbolMarginNumeric", "uLenWithMargin", "endFix", "repeatSpecified", "repeatTimes", "toIntTimes", "mDiff", "sizeFix", "pathPosition", "bundlePosition", "barRectShape", "extend", "clipShape", "createPath", "path", "culling", "setStyle", "strokeNoScale", "createOrUpdateRepeatSymbols", "isUpdate", "bundle", "__pictorialBundle", "unit", "eachPath", "__pictorialAnimationIndex", "__pictorialRepeatTimes", "updateAttr", "makeTarget", "target", "position", "i", "createOrUpdateSingleSymbol", "mainP<PERSON>", "__pictorial<PERSON><PERSON><PERSON><PERSON>", "createOrUpdateBarRect", "rectShape", "barRect", "__pictorialBarRect", "Rect", "shape", "silent", "stroke", "fill", "lineWidth", "disableMorphing", "createOrUpdateClip", "clipPath", "__pictorialClipPath", "updateProps", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "getAnimationDelayParams", "count", "parentModel", "Group", "labelRect", "removeTextContent", "paths", "push", "each", "removeElement", "parent", "join", "cb", "context", "children", "el", "call", "immediateAttrs", "animationAttrs", "emphasisModel", "getModel", "emphasisStyle", "getItemStyle", "blurStyle", "selectStyle", "cursorStyle", "focus", "blurScope", "pathStyle", "useStyle", "image", "emphasisState", "ensureState", "cursor", "barPositionOutside", "labelFetcher", "labelDataIndex", "defaultText", "inheritColor", "defaultOpacity", "opacity", "defaultOutsidePosition", "times", "roundedTimes", "round", "ceil"], "sources": ["C:/WolfSystemV2/WolfOldFrontEnd/node_modules/echarts/lib/chart/bar/PictorialBarView.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport * as graphic from '../../util/graphic.js';\nimport { toggleHoverEmphasis } from '../../util/states.js';\nimport { createSymbol, normalizeSymbolOffset } from '../../util/symbol.js';\nimport { parsePercent, isNumeric } from '../../util/number.js';\nimport ChartView from '../../view/Chart.js';\nimport { getDefaultLabel } from '../helper/labelHelper.js';\nimport { setLabelStyle, getLabelStatesModels } from '../../label/labelStyle.js';\nimport ZRImage from 'zrender/lib/graphic/Image.js';\nimport { getECData } from '../../util/innerStore.js';\nvar BAR_BORDER_WIDTH_QUERY = ['itemStyle', 'borderWidth']; // index: +isHorizontal\n\nvar LAYOUT_ATTRS = [{\n  xy: 'x',\n  wh: 'width',\n  index: 0,\n  posDesc: ['left', 'right']\n}, {\n  xy: 'y',\n  wh: 'height',\n  index: 1,\n  posDesc: ['top', 'bottom']\n}];\nvar pathForLineWidth = new graphic.Circle();\n\nvar PictorialBarView =\n/** @class */\nfunction (_super) {\n  __extends(PictorialBarView, _super);\n\n  function PictorialBarView() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n\n    _this.type = PictorialBarView.type;\n    return _this;\n  }\n\n  PictorialBarView.prototype.render = function (seriesModel, ecModel, api) {\n    var group = this.group;\n    var data = seriesModel.getData();\n    var oldData = this._data;\n    var cartesian = seriesModel.coordinateSystem;\n    var baseAxis = cartesian.getBaseAxis();\n    var isHorizontal = baseAxis.isHorizontal();\n    var coordSysRect = cartesian.master.getRect();\n    var opt = {\n      ecSize: {\n        width: api.getWidth(),\n        height: api.getHeight()\n      },\n      seriesModel: seriesModel,\n      coordSys: cartesian,\n      coordSysExtent: [[coordSysRect.x, coordSysRect.x + coordSysRect.width], [coordSysRect.y, coordSysRect.y + coordSysRect.height]],\n      isHorizontal: isHorizontal,\n      valueDim: LAYOUT_ATTRS[+isHorizontal],\n      categoryDim: LAYOUT_ATTRS[1 - +isHorizontal]\n    };\n    data.diff(oldData).add(function (dataIndex) {\n      if (!data.hasValue(dataIndex)) {\n        return;\n      }\n\n      var itemModel = getItemModel(data, dataIndex);\n      var symbolMeta = getSymbolMeta(data, dataIndex, itemModel, opt);\n      var bar = createBar(data, opt, symbolMeta);\n      data.setItemGraphicEl(dataIndex, bar);\n      group.add(bar);\n      updateCommon(bar, opt, symbolMeta);\n    }).update(function (newIndex, oldIndex) {\n      var bar = oldData.getItemGraphicEl(oldIndex);\n\n      if (!data.hasValue(newIndex)) {\n        group.remove(bar);\n        return;\n      }\n\n      var itemModel = getItemModel(data, newIndex);\n      var symbolMeta = getSymbolMeta(data, newIndex, itemModel, opt);\n      var pictorialShapeStr = getShapeStr(data, symbolMeta);\n\n      if (bar && pictorialShapeStr !== bar.__pictorialShapeStr) {\n        group.remove(bar);\n        data.setItemGraphicEl(newIndex, null);\n        bar = null;\n      }\n\n      if (bar) {\n        updateBar(bar, opt, symbolMeta);\n      } else {\n        bar = createBar(data, opt, symbolMeta, true);\n      }\n\n      data.setItemGraphicEl(newIndex, bar);\n      bar.__pictorialSymbolMeta = symbolMeta; // Add back\n\n      group.add(bar);\n      updateCommon(bar, opt, symbolMeta);\n    }).remove(function (dataIndex) {\n      var bar = oldData.getItemGraphicEl(dataIndex);\n      bar && removeBar(oldData, dataIndex, bar.__pictorialSymbolMeta.animationModel, bar);\n    }).execute();\n    this._data = data;\n    return this.group;\n  };\n\n  PictorialBarView.prototype.remove = function (ecModel, api) {\n    var group = this.group;\n    var data = this._data;\n\n    if (ecModel.get('animation')) {\n      if (data) {\n        data.eachItemGraphicEl(function (bar) {\n          removeBar(data, getECData(bar).dataIndex, ecModel, bar);\n        });\n      }\n    } else {\n      group.removeAll();\n    }\n  };\n\n  PictorialBarView.type = 'pictorialBar';\n  return PictorialBarView;\n}(ChartView); // Set or calculate default value about symbol, and calculate layout info.\n\n\nfunction getSymbolMeta(data, dataIndex, itemModel, opt) {\n  var layout = data.getItemLayout(dataIndex);\n  var symbolRepeat = itemModel.get('symbolRepeat');\n  var symbolClip = itemModel.get('symbolClip');\n  var symbolPosition = itemModel.get('symbolPosition') || 'start';\n  var symbolRotate = itemModel.get('symbolRotate');\n  var rotation = (symbolRotate || 0) * Math.PI / 180 || 0;\n  var symbolPatternSize = itemModel.get('symbolPatternSize') || 2;\n  var isAnimationEnabled = itemModel.isAnimationEnabled();\n  var symbolMeta = {\n    dataIndex: dataIndex,\n    layout: layout,\n    itemModel: itemModel,\n    symbolType: data.getItemVisual(dataIndex, 'symbol') || 'circle',\n    style: data.getItemVisual(dataIndex, 'style'),\n    symbolClip: symbolClip,\n    symbolRepeat: symbolRepeat,\n    symbolRepeatDirection: itemModel.get('symbolRepeatDirection'),\n    symbolPatternSize: symbolPatternSize,\n    rotation: rotation,\n    animationModel: isAnimationEnabled ? itemModel : null,\n    hoverScale: isAnimationEnabled && itemModel.get(['emphasis', 'scale']),\n    z2: itemModel.getShallow('z', true) || 0\n  };\n  prepareBarLength(itemModel, symbolRepeat, layout, opt, symbolMeta);\n  prepareSymbolSize(data, dataIndex, layout, symbolRepeat, symbolClip, symbolMeta.boundingLength, symbolMeta.pxSign, symbolPatternSize, opt, symbolMeta);\n  prepareLineWidth(itemModel, symbolMeta.symbolScale, rotation, opt, symbolMeta);\n  var symbolSize = symbolMeta.symbolSize;\n  var symbolOffset = normalizeSymbolOffset(itemModel.get('symbolOffset'), symbolSize);\n  prepareLayoutInfo(itemModel, symbolSize, layout, symbolRepeat, symbolClip, symbolOffset, symbolPosition, symbolMeta.valueLineWidth, symbolMeta.boundingLength, symbolMeta.repeatCutLength, opt, symbolMeta);\n  return symbolMeta;\n} // bar length can be negative.\n\n\nfunction prepareBarLength(itemModel, symbolRepeat, layout, opt, outputSymbolMeta) {\n  var valueDim = opt.valueDim;\n  var symbolBoundingData = itemModel.get('symbolBoundingData');\n  var valueAxis = opt.coordSys.getOtherAxis(opt.coordSys.getBaseAxis());\n  var zeroPx = valueAxis.toGlobalCoord(valueAxis.dataToCoord(0));\n  var pxSignIdx = 1 - +(layout[valueDim.wh] <= 0);\n  var boundingLength;\n\n  if (zrUtil.isArray(symbolBoundingData)) {\n    var symbolBoundingExtent = [convertToCoordOnAxis(valueAxis, symbolBoundingData[0]) - zeroPx, convertToCoordOnAxis(valueAxis, symbolBoundingData[1]) - zeroPx];\n    symbolBoundingExtent[1] < symbolBoundingExtent[0] && symbolBoundingExtent.reverse();\n    boundingLength = symbolBoundingExtent[pxSignIdx];\n  } else if (symbolBoundingData != null) {\n    boundingLength = convertToCoordOnAxis(valueAxis, symbolBoundingData) - zeroPx;\n  } else if (symbolRepeat) {\n    boundingLength = opt.coordSysExtent[valueDim.index][pxSignIdx] - zeroPx;\n  } else {\n    boundingLength = layout[valueDim.wh];\n  }\n\n  outputSymbolMeta.boundingLength = boundingLength;\n\n  if (symbolRepeat) {\n    outputSymbolMeta.repeatCutLength = layout[valueDim.wh];\n  } // if 'pxSign' means sign of pixel,  it can't be zero, or symbolScale will be zero\n  // and when borderWidth be settled, the actual linewidth will be NaN\n\n\n  outputSymbolMeta.pxSign = boundingLength > 0 ? 1 : -1;\n}\n\nfunction convertToCoordOnAxis(axis, value) {\n  return axis.toGlobalCoord(axis.dataToCoord(axis.scale.parse(value)));\n} // Support ['100%', '100%']\n\n\nfunction prepareSymbolSize(data, dataIndex, layout, symbolRepeat, symbolClip, boundingLength, pxSign, symbolPatternSize, opt, outputSymbolMeta) {\n  var valueDim = opt.valueDim;\n  var categoryDim = opt.categoryDim;\n  var categorySize = Math.abs(layout[categoryDim.wh]);\n  var symbolSize = data.getItemVisual(dataIndex, 'symbolSize');\n  var parsedSymbolSize;\n\n  if (zrUtil.isArray(symbolSize)) {\n    parsedSymbolSize = symbolSize.slice();\n  } else {\n    if (symbolSize == null) {\n      // will parse to number below\n      parsedSymbolSize = ['100%', '100%'];\n    } else {\n      parsedSymbolSize = [symbolSize, symbolSize];\n    }\n  } // Note: percentage symbolSize (like '100%') do not consider lineWidth, because it is\n  // to complicated to calculate real percent value if considering scaled lineWidth.\n  // So the actual size will bigger than layout size if lineWidth is bigger than zero,\n  // which can be tolerated in pictorial chart.\n\n\n  parsedSymbolSize[categoryDim.index] = parsePercent(parsedSymbolSize[categoryDim.index], categorySize);\n  parsedSymbolSize[valueDim.index] = parsePercent(parsedSymbolSize[valueDim.index], symbolRepeat ? categorySize : Math.abs(boundingLength));\n  outputSymbolMeta.symbolSize = parsedSymbolSize; // If x or y is less than zero, show reversed shape.\n\n  var symbolScale = outputSymbolMeta.symbolScale = [parsedSymbolSize[0] / symbolPatternSize, parsedSymbolSize[1] / symbolPatternSize]; // Follow convention, 'right' and 'top' is the normal scale.\n\n  symbolScale[valueDim.index] *= (opt.isHorizontal ? -1 : 1) * pxSign;\n}\n\nfunction prepareLineWidth(itemModel, symbolScale, rotation, opt, outputSymbolMeta) {\n  // In symbols are drawn with scale, so do not need to care about the case that width\n  // or height are too small. But symbol use strokeNoScale, where acture lineWidth should\n  // be calculated.\n  var valueLineWidth = itemModel.get(BAR_BORDER_WIDTH_QUERY) || 0;\n\n  if (valueLineWidth) {\n    pathForLineWidth.attr({\n      scaleX: symbolScale[0],\n      scaleY: symbolScale[1],\n      rotation: rotation\n    });\n    pathForLineWidth.updateTransform();\n    valueLineWidth /= pathForLineWidth.getLineScale();\n    valueLineWidth *= symbolScale[opt.valueDim.index];\n  }\n\n  outputSymbolMeta.valueLineWidth = valueLineWidth || 0;\n}\n\nfunction prepareLayoutInfo(itemModel, symbolSize, layout, symbolRepeat, symbolClip, symbolOffset, symbolPosition, valueLineWidth, boundingLength, repeatCutLength, opt, outputSymbolMeta) {\n  var categoryDim = opt.categoryDim;\n  var valueDim = opt.valueDim;\n  var pxSign = outputSymbolMeta.pxSign;\n  var unitLength = Math.max(symbolSize[valueDim.index] + valueLineWidth, 0);\n  var pathLen = unitLength; // Note: rotation will not effect the layout of symbols, because user may\n  // want symbols to rotate on its center, which should not be translated\n  // when rotating.\n\n  if (symbolRepeat) {\n    var absBoundingLength = Math.abs(boundingLength);\n    var symbolMargin = zrUtil.retrieve(itemModel.get('symbolMargin'), '15%') + '';\n    var hasEndGap = false;\n\n    if (symbolMargin.lastIndexOf('!') === symbolMargin.length - 1) {\n      hasEndGap = true;\n      symbolMargin = symbolMargin.slice(0, symbolMargin.length - 1);\n    }\n\n    var symbolMarginNumeric = parsePercent(symbolMargin, symbolSize[valueDim.index]);\n    var uLenWithMargin = Math.max(unitLength + symbolMarginNumeric * 2, 0); // When symbol margin is less than 0, margin at both ends will be subtracted\n    // to ensure that all of the symbols will not be overflow the given area.\n\n    var endFix = hasEndGap ? 0 : symbolMarginNumeric * 2; // Both final repeatTimes and final symbolMarginNumeric area calculated based on\n    // boundingLength.\n\n    var repeatSpecified = isNumeric(symbolRepeat);\n    var repeatTimes = repeatSpecified ? symbolRepeat : toIntTimes((absBoundingLength + endFix) / uLenWithMargin); // Adjust calculate margin, to ensure each symbol is displayed\n    // entirely in the given layout area.\n\n    var mDiff = absBoundingLength - repeatTimes * unitLength;\n    symbolMarginNumeric = mDiff / 2 / (hasEndGap ? repeatTimes : Math.max(repeatTimes - 1, 1));\n    uLenWithMargin = unitLength + symbolMarginNumeric * 2;\n    endFix = hasEndGap ? 0 : symbolMarginNumeric * 2; // Update repeatTimes when not all symbol will be shown.\n\n    if (!repeatSpecified && symbolRepeat !== 'fixed') {\n      repeatTimes = repeatCutLength ? toIntTimes((Math.abs(repeatCutLength) + endFix) / uLenWithMargin) : 0;\n    }\n\n    pathLen = repeatTimes * uLenWithMargin - endFix;\n    outputSymbolMeta.repeatTimes = repeatTimes;\n    outputSymbolMeta.symbolMargin = symbolMarginNumeric;\n  }\n\n  var sizeFix = pxSign * (pathLen / 2);\n  var pathPosition = outputSymbolMeta.pathPosition = [];\n  pathPosition[categoryDim.index] = layout[categoryDim.wh] / 2;\n  pathPosition[valueDim.index] = symbolPosition === 'start' ? sizeFix : symbolPosition === 'end' ? boundingLength - sizeFix : boundingLength / 2; // 'center'\n\n  if (symbolOffset) {\n    pathPosition[0] += symbolOffset[0];\n    pathPosition[1] += symbolOffset[1];\n  }\n\n  var bundlePosition = outputSymbolMeta.bundlePosition = [];\n  bundlePosition[categoryDim.index] = layout[categoryDim.xy];\n  bundlePosition[valueDim.index] = layout[valueDim.xy];\n  var barRectShape = outputSymbolMeta.barRectShape = zrUtil.extend({}, layout);\n  barRectShape[valueDim.wh] = pxSign * Math.max(Math.abs(layout[valueDim.wh]), Math.abs(pathPosition[valueDim.index] + sizeFix));\n  barRectShape[categoryDim.wh] = layout[categoryDim.wh];\n  var clipShape = outputSymbolMeta.clipShape = {}; // Consider that symbol may be overflow layout rect.\n\n  clipShape[categoryDim.xy] = -layout[categoryDim.xy];\n  clipShape[categoryDim.wh] = opt.ecSize[categoryDim.wh];\n  clipShape[valueDim.xy] = 0;\n  clipShape[valueDim.wh] = layout[valueDim.wh];\n}\n\nfunction createPath(symbolMeta) {\n  var symbolPatternSize = symbolMeta.symbolPatternSize;\n  var path = createSymbol( // Consider texture img, make a big size.\n  symbolMeta.symbolType, -symbolPatternSize / 2, -symbolPatternSize / 2, symbolPatternSize, symbolPatternSize);\n  path.attr({\n    culling: true\n  });\n  path.type !== 'image' && path.setStyle({\n    strokeNoScale: true\n  });\n  return path;\n}\n\nfunction createOrUpdateRepeatSymbols(bar, opt, symbolMeta, isUpdate) {\n  var bundle = bar.__pictorialBundle;\n  var symbolSize = symbolMeta.symbolSize;\n  var valueLineWidth = symbolMeta.valueLineWidth;\n  var pathPosition = symbolMeta.pathPosition;\n  var valueDim = opt.valueDim;\n  var repeatTimes = symbolMeta.repeatTimes || 0;\n  var index = 0;\n  var unit = symbolSize[opt.valueDim.index] + valueLineWidth + symbolMeta.symbolMargin * 2;\n  eachPath(bar, function (path) {\n    path.__pictorialAnimationIndex = index;\n    path.__pictorialRepeatTimes = repeatTimes;\n\n    if (index < repeatTimes) {\n      updateAttr(path, null, makeTarget(index), symbolMeta, isUpdate);\n    } else {\n      updateAttr(path, null, {\n        scaleX: 0,\n        scaleY: 0\n      }, symbolMeta, isUpdate, function () {\n        bundle.remove(path);\n      });\n    } // updateHoverAnimation(path, symbolMeta);\n\n\n    index++;\n  });\n\n  for (; index < repeatTimes; index++) {\n    var path = createPath(symbolMeta);\n    path.__pictorialAnimationIndex = index;\n    path.__pictorialRepeatTimes = repeatTimes;\n    bundle.add(path);\n    var target = makeTarget(index);\n    updateAttr(path, {\n      x: target.x,\n      y: target.y,\n      scaleX: 0,\n      scaleY: 0\n    }, {\n      scaleX: target.scaleX,\n      scaleY: target.scaleY,\n      rotation: target.rotation\n    }, symbolMeta, isUpdate);\n  }\n\n  function makeTarget(index) {\n    var position = pathPosition.slice(); // (start && pxSign > 0) || (end && pxSign < 0): i = repeatTimes - index\n    // Otherwise: i = index;\n\n    var pxSign = symbolMeta.pxSign;\n    var i = index;\n\n    if (symbolMeta.symbolRepeatDirection === 'start' ? pxSign > 0 : pxSign < 0) {\n      i = repeatTimes - 1 - index;\n    }\n\n    position[valueDim.index] = unit * (i - repeatTimes / 2 + 0.5) + pathPosition[valueDim.index];\n    return {\n      x: position[0],\n      y: position[1],\n      scaleX: symbolMeta.symbolScale[0],\n      scaleY: symbolMeta.symbolScale[1],\n      rotation: symbolMeta.rotation\n    };\n  }\n}\n\nfunction createOrUpdateSingleSymbol(bar, opt, symbolMeta, isUpdate) {\n  var bundle = bar.__pictorialBundle;\n  var mainPath = bar.__pictorialMainPath;\n\n  if (!mainPath) {\n    mainPath = bar.__pictorialMainPath = createPath(symbolMeta);\n    bundle.add(mainPath);\n    updateAttr(mainPath, {\n      x: symbolMeta.pathPosition[0],\n      y: symbolMeta.pathPosition[1],\n      scaleX: 0,\n      scaleY: 0,\n      rotation: symbolMeta.rotation\n    }, {\n      scaleX: symbolMeta.symbolScale[0],\n      scaleY: symbolMeta.symbolScale[1]\n    }, symbolMeta, isUpdate);\n  } else {\n    updateAttr(mainPath, null, {\n      x: symbolMeta.pathPosition[0],\n      y: symbolMeta.pathPosition[1],\n      scaleX: symbolMeta.symbolScale[0],\n      scaleY: symbolMeta.symbolScale[1],\n      rotation: symbolMeta.rotation\n    }, symbolMeta, isUpdate);\n  }\n} // bar rect is used for label.\n\n\nfunction createOrUpdateBarRect(bar, symbolMeta, isUpdate) {\n  var rectShape = zrUtil.extend({}, symbolMeta.barRectShape);\n  var barRect = bar.__pictorialBarRect;\n\n  if (!barRect) {\n    barRect = bar.__pictorialBarRect = new graphic.Rect({\n      z2: 2,\n      shape: rectShape,\n      silent: true,\n      style: {\n        stroke: 'transparent',\n        fill: 'transparent',\n        lineWidth: 0\n      }\n    });\n    barRect.disableMorphing = true;\n    bar.add(barRect);\n  } else {\n    updateAttr(barRect, null, {\n      shape: rectShape\n    }, symbolMeta, isUpdate);\n  }\n}\n\nfunction createOrUpdateClip(bar, opt, symbolMeta, isUpdate) {\n  // If not clip, symbol will be remove and rebuilt.\n  if (symbolMeta.symbolClip) {\n    var clipPath = bar.__pictorialClipPath;\n    var clipShape = zrUtil.extend({}, symbolMeta.clipShape);\n    var valueDim = opt.valueDim;\n    var animationModel = symbolMeta.animationModel;\n    var dataIndex = symbolMeta.dataIndex;\n\n    if (clipPath) {\n      graphic.updateProps(clipPath, {\n        shape: clipShape\n      }, animationModel, dataIndex);\n    } else {\n      clipShape[valueDim.wh] = 0;\n      clipPath = new graphic.Rect({\n        shape: clipShape\n      });\n\n      bar.__pictorialBundle.setClipPath(clipPath);\n\n      bar.__pictorialClipPath = clipPath;\n      var target = {};\n      target[valueDim.wh] = symbolMeta.clipShape[valueDim.wh];\n      graphic[isUpdate ? 'updateProps' : 'initProps'](clipPath, {\n        shape: target\n      }, animationModel, dataIndex);\n    }\n  }\n}\n\nfunction getItemModel(data, dataIndex) {\n  var itemModel = data.getItemModel(dataIndex);\n  itemModel.getAnimationDelayParams = getAnimationDelayParams;\n  itemModel.isAnimationEnabled = isAnimationEnabled;\n  return itemModel;\n}\n\nfunction getAnimationDelayParams(path) {\n  // The order is the same as the z-order, see `symbolRepeatDiretion`.\n  return {\n    index: path.__pictorialAnimationIndex,\n    count: path.__pictorialRepeatTimes\n  };\n}\n\nfunction isAnimationEnabled() {\n  // `animation` prop can be set on itemModel in pictorial bar chart.\n  return this.parentModel.isAnimationEnabled() && !!this.getShallow('animation');\n}\n\nfunction createBar(data, opt, symbolMeta, isUpdate) {\n  // bar is the main element for each data.\n  var bar = new graphic.Group(); // bundle is used for location and clip.\n\n  var bundle = new graphic.Group();\n  bar.add(bundle);\n  bar.__pictorialBundle = bundle;\n  bundle.x = symbolMeta.bundlePosition[0];\n  bundle.y = symbolMeta.bundlePosition[1];\n\n  if (symbolMeta.symbolRepeat) {\n    createOrUpdateRepeatSymbols(bar, opt, symbolMeta);\n  } else {\n    createOrUpdateSingleSymbol(bar, opt, symbolMeta);\n  }\n\n  createOrUpdateBarRect(bar, symbolMeta, isUpdate);\n  createOrUpdateClip(bar, opt, symbolMeta, isUpdate);\n  bar.__pictorialShapeStr = getShapeStr(data, symbolMeta);\n  bar.__pictorialSymbolMeta = symbolMeta;\n  return bar;\n}\n\nfunction updateBar(bar, opt, symbolMeta) {\n  var animationModel = symbolMeta.animationModel;\n  var dataIndex = symbolMeta.dataIndex;\n  var bundle = bar.__pictorialBundle;\n  graphic.updateProps(bundle, {\n    x: symbolMeta.bundlePosition[0],\n    y: symbolMeta.bundlePosition[1]\n  }, animationModel, dataIndex);\n\n  if (symbolMeta.symbolRepeat) {\n    createOrUpdateRepeatSymbols(bar, opt, symbolMeta, true);\n  } else {\n    createOrUpdateSingleSymbol(bar, opt, symbolMeta, true);\n  }\n\n  createOrUpdateBarRect(bar, symbolMeta, true);\n  createOrUpdateClip(bar, opt, symbolMeta, true);\n}\n\nfunction removeBar(data, dataIndex, animationModel, bar) {\n  // Not show text when animating\n  var labelRect = bar.__pictorialBarRect;\n  labelRect && labelRect.removeTextContent();\n  var paths = [];\n  eachPath(bar, function (path) {\n    paths.push(path);\n  });\n  bar.__pictorialMainPath && paths.push(bar.__pictorialMainPath); // I do not find proper remove animation for clip yet.\n\n  bar.__pictorialClipPath && (animationModel = null);\n  zrUtil.each(paths, function (path) {\n    graphic.removeElement(path, {\n      scaleX: 0,\n      scaleY: 0\n    }, animationModel, dataIndex, function () {\n      bar.parent && bar.parent.remove(bar);\n    });\n  });\n  data.setItemGraphicEl(dataIndex, null);\n}\n\nfunction getShapeStr(data, symbolMeta) {\n  return [data.getItemVisual(symbolMeta.dataIndex, 'symbol') || 'none', !!symbolMeta.symbolRepeat, !!symbolMeta.symbolClip].join(':');\n}\n\nfunction eachPath(bar, cb, context) {\n  // Do not use Group#eachChild, because it do not support remove.\n  zrUtil.each(bar.__pictorialBundle.children(), function (el) {\n    el !== bar.__pictorialBarRect && cb.call(context, el);\n  });\n}\n\nfunction updateAttr(el, immediateAttrs, animationAttrs, symbolMeta, isUpdate, cb) {\n  immediateAttrs && el.attr(immediateAttrs); // when symbolCip used, only clip path has init animation, otherwise it would be weird effect.\n\n  if (symbolMeta.symbolClip && !isUpdate) {\n    animationAttrs && el.attr(animationAttrs);\n  } else {\n    animationAttrs && graphic[isUpdate ? 'updateProps' : 'initProps'](el, animationAttrs, symbolMeta.animationModel, symbolMeta.dataIndex, cb);\n  }\n}\n\nfunction updateCommon(bar, opt, symbolMeta) {\n  var dataIndex = symbolMeta.dataIndex;\n  var itemModel = symbolMeta.itemModel; // Color must be excluded.\n  // Because symbol provide setColor individually to set fill and stroke\n\n  var emphasisModel = itemModel.getModel('emphasis');\n  var emphasisStyle = emphasisModel.getModel('itemStyle').getItemStyle();\n  var blurStyle = itemModel.getModel(['blur', 'itemStyle']).getItemStyle();\n  var selectStyle = itemModel.getModel(['select', 'itemStyle']).getItemStyle();\n  var cursorStyle = itemModel.getShallow('cursor');\n  var focus = emphasisModel.get('focus');\n  var blurScope = emphasisModel.get('blurScope');\n  var hoverScale = emphasisModel.get('scale');\n  eachPath(bar, function (path) {\n    if (path instanceof ZRImage) {\n      var pathStyle = path.style;\n      path.useStyle(zrUtil.extend({\n        // TODO other properties like dx, dy ?\n        image: pathStyle.image,\n        x: pathStyle.x,\n        y: pathStyle.y,\n        width: pathStyle.width,\n        height: pathStyle.height\n      }, symbolMeta.style));\n    } else {\n      path.useStyle(symbolMeta.style);\n    }\n\n    var emphasisState = path.ensureState('emphasis');\n    emphasisState.style = emphasisStyle;\n\n    if (hoverScale) {\n      // NOTE: Must after scale is set after updateAttr\n      emphasisState.scaleX = path.scaleX * 1.1;\n      emphasisState.scaleY = path.scaleY * 1.1;\n    }\n\n    path.ensureState('blur').style = blurStyle;\n    path.ensureState('select').style = selectStyle;\n    cursorStyle && (path.cursor = cursorStyle);\n    path.z2 = symbolMeta.z2;\n  });\n  var barPositionOutside = opt.valueDim.posDesc[+(symbolMeta.boundingLength > 0)];\n  var barRect = bar.__pictorialBarRect;\n  setLabelStyle(barRect, getLabelStatesModels(itemModel), {\n    labelFetcher: opt.seriesModel,\n    labelDataIndex: dataIndex,\n    defaultText: getDefaultLabel(opt.seriesModel.getData(), dataIndex),\n    inheritColor: symbolMeta.style.fill,\n    defaultOpacity: symbolMeta.style.opacity,\n    defaultOutsidePosition: barPositionOutside\n  });\n  toggleHoverEmphasis(bar, focus, blurScope, emphasisModel.get('disabled'));\n}\n\nfunction toIntTimes(times) {\n  var roundedTimes = Math.round(times); // Escapse accurate error\n\n  return Math.abs(times - roundedTimes) < 1e-4 ? roundedTimes : Math.ceil(times);\n}\n\nexport default PictorialBarView;"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,SAAS,QAAQ,OAAO;AACjC,OAAO,KAAKC,MAAM,MAAM,0BAA0B;AAClD,OAAO,KAAKC,OAAO,MAAM,uBAAuB;AAChD,SAASC,mBAAmB,QAAQ,sBAAsB;AAC1D,SAASC,YAAY,EAAEC,qBAAqB,QAAQ,sBAAsB;AAC1E,SAASC,YAAY,EAAEC,SAAS,QAAQ,sBAAsB;AAC9D,OAAOC,SAAS,MAAM,qBAAqB;AAC3C,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,aAAa,EAAEC,oBAAoB,QAAQ,2BAA2B;AAC/E,OAAOC,OAAO,MAAM,8BAA8B;AAClD,SAASC,SAAS,QAAQ,0BAA0B;AACpD,IAAIC,sBAAsB,GAAG,CAAC,WAAW,EAAE,aAAa,CAAC,CAAC,CAAC;;AAE3D,IAAIC,YAAY,GAAG,CAAC;EAClBC,EAAE,EAAE,GAAG;EACPC,EAAE,EAAE,OAAO;EACXC,KAAK,EAAE,CAAC;EACRC,OAAO,EAAE,CAAC,MAAM,EAAE,OAAO;AAC3B,CAAC,EAAE;EACDH,EAAE,EAAE,GAAG;EACPC,EAAE,EAAE,QAAQ;EACZC,KAAK,EAAE,CAAC;EACRC,OAAO,EAAE,CAAC,KAAK,EAAE,QAAQ;AAC3B,CAAC,CAAC;AACF,IAAIC,gBAAgB,GAAG,IAAIlB,OAAO,CAACmB,MAAM,CAAC,CAAC;AAE3C,IAAIC,gBAAgB,GACpB;AACA,UAAUC,MAAM,EAAE;EAChBvB,SAAS,CAACsB,gBAAgB,EAAEC,MAAM,CAAC;EAEnC,SAASD,gBAAgBA,CAAA,EAAG;IAC1B,IAAIE,KAAK,GAAGD,MAAM,KAAK,IAAI,IAAIA,MAAM,CAACE,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC,IAAI,IAAI;IAEpEF,KAAK,CAACG,IAAI,GAAGL,gBAAgB,CAACK,IAAI;IAClC,OAAOH,KAAK;EACd;EAEAF,gBAAgB,CAACM,SAAS,CAACC,MAAM,GAAG,UAAUC,WAAW,EAAEC,OAAO,EAAEC,GAAG,EAAE;IACvE,IAAIC,KAAK,GAAG,IAAI,CAACA,KAAK;IACtB,IAAIC,IAAI,GAAGJ,WAAW,CAACK,OAAO,CAAC,CAAC;IAChC,IAAIC,OAAO,GAAG,IAAI,CAACC,KAAK;IACxB,IAAIC,SAAS,GAAGR,WAAW,CAACS,gBAAgB;IAC5C,IAAIC,QAAQ,GAAGF,SAAS,CAACG,WAAW,CAAC,CAAC;IACtC,IAAIC,YAAY,GAAGF,QAAQ,CAACE,YAAY,CAAC,CAAC;IAC1C,IAAIC,YAAY,GAAGL,SAAS,CAACM,MAAM,CAACC,OAAO,CAAC,CAAC;IAC7C,IAAIC,GAAG,GAAG;MACRC,MAAM,EAAE;QACNC,KAAK,EAAEhB,GAAG,CAACiB,QAAQ,CAAC,CAAC;QACrBC,MAAM,EAAElB,GAAG,CAACmB,SAAS,CAAC;MACxB,CAAC;MACDrB,WAAW,EAAEA,WAAW;MACxBsB,QAAQ,EAAEd,SAAS;MACnBe,cAAc,EAAE,CAAC,CAACV,YAAY,CAACW,CAAC,EAAEX,YAAY,CAACW,CAAC,GAAGX,YAAY,CAACK,KAAK,CAAC,EAAE,CAACL,YAAY,CAACY,CAAC,EAAEZ,YAAY,CAACY,CAAC,GAAGZ,YAAY,CAACO,MAAM,CAAC,CAAC;MAC/HR,YAAY,EAAEA,YAAY;MAC1Bc,QAAQ,EAAEzC,YAAY,CAAC,CAAC2B,YAAY,CAAC;MACrCe,WAAW,EAAE1C,YAAY,CAAC,CAAC,GAAG,CAAC2B,YAAY;IAC7C,CAAC;IACDR,IAAI,CAACwB,IAAI,CAACtB,OAAO,CAAC,CAACuB,GAAG,CAAC,UAAUC,SAAS,EAAE;MAC1C,IAAI,CAAC1B,IAAI,CAAC2B,QAAQ,CAACD,SAAS,CAAC,EAAE;QAC7B;MACF;MAEA,IAAIE,SAAS,GAAGC,YAAY,CAAC7B,IAAI,EAAE0B,SAAS,CAAC;MAC7C,IAAII,UAAU,GAAGC,aAAa,CAAC/B,IAAI,EAAE0B,SAAS,EAAEE,SAAS,EAAEhB,GAAG,CAAC;MAC/D,IAAIoB,GAAG,GAAGC,SAAS,CAACjC,IAAI,EAAEY,GAAG,EAAEkB,UAAU,CAAC;MAC1C9B,IAAI,CAACkC,gBAAgB,CAACR,SAAS,EAAEM,GAAG,CAAC;MACrCjC,KAAK,CAAC0B,GAAG,CAACO,GAAG,CAAC;MACdG,YAAY,CAACH,GAAG,EAAEpB,GAAG,EAAEkB,UAAU,CAAC;IACpC,CAAC,CAAC,CAACM,MAAM,CAAC,UAAUC,QAAQ,EAAEC,QAAQ,EAAE;MACtC,IAAIN,GAAG,GAAG9B,OAAO,CAACqC,gBAAgB,CAACD,QAAQ,CAAC;MAE5C,IAAI,CAACtC,IAAI,CAAC2B,QAAQ,CAACU,QAAQ,CAAC,EAAE;QAC5BtC,KAAK,CAACyC,MAAM,CAACR,GAAG,CAAC;QACjB;MACF;MAEA,IAAIJ,SAAS,GAAGC,YAAY,CAAC7B,IAAI,EAAEqC,QAAQ,CAAC;MAC5C,IAAIP,UAAU,GAAGC,aAAa,CAAC/B,IAAI,EAAEqC,QAAQ,EAAET,SAAS,EAAEhB,GAAG,CAAC;MAC9D,IAAI6B,iBAAiB,GAAGC,WAAW,CAAC1C,IAAI,EAAE8B,UAAU,CAAC;MAErD,IAAIE,GAAG,IAAIS,iBAAiB,KAAKT,GAAG,CAACW,mBAAmB,EAAE;QACxD5C,KAAK,CAACyC,MAAM,CAACR,GAAG,CAAC;QACjBhC,IAAI,CAACkC,gBAAgB,CAACG,QAAQ,EAAE,IAAI,CAAC;QACrCL,GAAG,GAAG,IAAI;MACZ;MAEA,IAAIA,GAAG,EAAE;QACPY,SAAS,CAACZ,GAAG,EAAEpB,GAAG,EAAEkB,UAAU,CAAC;MACjC,CAAC,MAAM;QACLE,GAAG,GAAGC,SAAS,CAACjC,IAAI,EAAEY,GAAG,EAAEkB,UAAU,EAAE,IAAI,CAAC;MAC9C;MAEA9B,IAAI,CAACkC,gBAAgB,CAACG,QAAQ,EAAEL,GAAG,CAAC;MACpCA,GAAG,CAACa,qBAAqB,GAAGf,UAAU,CAAC,CAAC;;MAExC/B,KAAK,CAAC0B,GAAG,CAACO,GAAG,CAAC;MACdG,YAAY,CAACH,GAAG,EAAEpB,GAAG,EAAEkB,UAAU,CAAC;IACpC,CAAC,CAAC,CAACU,MAAM,CAAC,UAAUd,SAAS,EAAE;MAC7B,IAAIM,GAAG,GAAG9B,OAAO,CAACqC,gBAAgB,CAACb,SAAS,CAAC;MAC7CM,GAAG,IAAIc,SAAS,CAAC5C,OAAO,EAAEwB,SAAS,EAAEM,GAAG,CAACa,qBAAqB,CAACE,cAAc,EAAEf,GAAG,CAAC;IACrF,CAAC,CAAC,CAACgB,OAAO,CAAC,CAAC;IACZ,IAAI,CAAC7C,KAAK,GAAGH,IAAI;IACjB,OAAO,IAAI,CAACD,KAAK;EACnB,CAAC;EAEDX,gBAAgB,CAACM,SAAS,CAAC8C,MAAM,GAAG,UAAU3C,OAAO,EAAEC,GAAG,EAAE;IAC1D,IAAIC,KAAK,GAAG,IAAI,CAACA,KAAK;IACtB,IAAIC,IAAI,GAAG,IAAI,CAACG,KAAK;IAErB,IAAIN,OAAO,CAACoD,GAAG,CAAC,WAAW,CAAC,EAAE;MAC5B,IAAIjD,IAAI,EAAE;QACRA,IAAI,CAACkD,iBAAiB,CAAC,UAAUlB,GAAG,EAAE;UACpCc,SAAS,CAAC9C,IAAI,EAAErB,SAAS,CAACqD,GAAG,CAAC,CAACN,SAAS,EAAE7B,OAAO,EAAEmC,GAAG,CAAC;QACzD,CAAC,CAAC;MACJ;IACF,CAAC,MAAM;MACLjC,KAAK,CAACoD,SAAS,CAAC,CAAC;IACnB;EACF,CAAC;EAED/D,gBAAgB,CAACK,IAAI,GAAG,cAAc;EACtC,OAAOL,gBAAgB;AACzB,CAAC,CAACd,SAAS,CAAC,CAAC,CAAC;;AAGd,SAASyD,aAAaA,CAAC/B,IAAI,EAAE0B,SAAS,EAAEE,SAAS,EAAEhB,GAAG,EAAE;EACtD,IAAIwC,MAAM,GAAGpD,IAAI,CAACqD,aAAa,CAAC3B,SAAS,CAAC;EAC1C,IAAI4B,YAAY,GAAG1B,SAAS,CAACqB,GAAG,CAAC,cAAc,CAAC;EAChD,IAAIM,UAAU,GAAG3B,SAAS,CAACqB,GAAG,CAAC,YAAY,CAAC;EAC5C,IAAIO,cAAc,GAAG5B,SAAS,CAACqB,GAAG,CAAC,gBAAgB,CAAC,IAAI,OAAO;EAC/D,IAAIQ,YAAY,GAAG7B,SAAS,CAACqB,GAAG,CAAC,cAAc,CAAC;EAChD,IAAIS,QAAQ,GAAG,CAACD,YAAY,IAAI,CAAC,IAAIE,IAAI,CAACC,EAAE,GAAG,GAAG,IAAI,CAAC;EACvD,IAAIC,iBAAiB,GAAGjC,SAAS,CAACqB,GAAG,CAAC,mBAAmB,CAAC,IAAI,CAAC;EAC/D,IAAIa,kBAAkB,GAAGlC,SAAS,CAACkC,kBAAkB,CAAC,CAAC;EACvD,IAAIhC,UAAU,GAAG;IACfJ,SAAS,EAAEA,SAAS;IACpB0B,MAAM,EAAEA,MAAM;IACdxB,SAAS,EAAEA,SAAS;IACpBmC,UAAU,EAAE/D,IAAI,CAACgE,aAAa,CAACtC,SAAS,EAAE,QAAQ,CAAC,IAAI,QAAQ;IAC/DuC,KAAK,EAAEjE,IAAI,CAACgE,aAAa,CAACtC,SAAS,EAAE,OAAO,CAAC;IAC7C6B,UAAU,EAAEA,UAAU;IACtBD,YAAY,EAAEA,YAAY;IAC1BY,qBAAqB,EAAEtC,SAAS,CAACqB,GAAG,CAAC,uBAAuB,CAAC;IAC7DY,iBAAiB,EAAEA,iBAAiB;IACpCH,QAAQ,EAAEA,QAAQ;IAClBX,cAAc,EAAEe,kBAAkB,GAAGlC,SAAS,GAAG,IAAI;IACrDuC,UAAU,EAAEL,kBAAkB,IAAIlC,SAAS,CAACqB,GAAG,CAAC,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC;IACtEmB,EAAE,EAAExC,SAAS,CAACyC,UAAU,CAAC,GAAG,EAAE,IAAI,CAAC,IAAI;EACzC,CAAC;EACDC,gBAAgB,CAAC1C,SAAS,EAAE0B,YAAY,EAAEF,MAAM,EAAExC,GAAG,EAAEkB,UAAU,CAAC;EAClEyC,iBAAiB,CAACvE,IAAI,EAAE0B,SAAS,EAAE0B,MAAM,EAAEE,YAAY,EAAEC,UAAU,EAAEzB,UAAU,CAAC0C,cAAc,EAAE1C,UAAU,CAAC2C,MAAM,EAAEZ,iBAAiB,EAAEjD,GAAG,EAAEkB,UAAU,CAAC;EACtJ4C,gBAAgB,CAAC9C,SAAS,EAAEE,UAAU,CAAC6C,WAAW,EAAEjB,QAAQ,EAAE9C,GAAG,EAAEkB,UAAU,CAAC;EAC9E,IAAI8C,UAAU,GAAG9C,UAAU,CAAC8C,UAAU;EACtC,IAAIC,YAAY,GAAG1G,qBAAqB,CAACyD,SAAS,CAACqB,GAAG,CAAC,cAAc,CAAC,EAAE2B,UAAU,CAAC;EACnFE,iBAAiB,CAAClD,SAAS,EAAEgD,UAAU,EAAExB,MAAM,EAAEE,YAAY,EAAEC,UAAU,EAAEsB,YAAY,EAAErB,cAAc,EAAE1B,UAAU,CAACiD,cAAc,EAAEjD,UAAU,CAAC0C,cAAc,EAAE1C,UAAU,CAACkD,eAAe,EAAEpE,GAAG,EAAEkB,UAAU,CAAC;EAC3M,OAAOA,UAAU;AACnB,CAAC,CAAC;;AAGF,SAASwC,gBAAgBA,CAAC1C,SAAS,EAAE0B,YAAY,EAAEF,MAAM,EAAExC,GAAG,EAAEqE,gBAAgB,EAAE;EAChF,IAAI3D,QAAQ,GAAGV,GAAG,CAACU,QAAQ;EAC3B,IAAI4D,kBAAkB,GAAGtD,SAAS,CAACqB,GAAG,CAAC,oBAAoB,CAAC;EAC5D,IAAIkC,SAAS,GAAGvE,GAAG,CAACM,QAAQ,CAACkE,YAAY,CAACxE,GAAG,CAACM,QAAQ,CAACX,WAAW,CAAC,CAAC,CAAC;EACrE,IAAI8E,MAAM,GAAGF,SAAS,CAACG,aAAa,CAACH,SAAS,CAACI,WAAW,CAAC,CAAC,CAAC,CAAC;EAC9D,IAAIC,SAAS,GAAG,CAAC,GAAG,EAAEpC,MAAM,CAAC9B,QAAQ,CAACvC,EAAE,CAAC,IAAI,CAAC,CAAC;EAC/C,IAAIyF,cAAc;EAElB,IAAIzG,MAAM,CAAC0H,OAAO,CAACP,kBAAkB,CAAC,EAAE;IACtC,IAAIQ,oBAAoB,GAAG,CAACC,oBAAoB,CAACR,SAAS,EAAED,kBAAkB,CAAC,CAAC,CAAC,CAAC,GAAGG,MAAM,EAAEM,oBAAoB,CAACR,SAAS,EAAED,kBAAkB,CAAC,CAAC,CAAC,CAAC,GAAGG,MAAM,CAAC;IAC7JK,oBAAoB,CAAC,CAAC,CAAC,GAAGA,oBAAoB,CAAC,CAAC,CAAC,IAAIA,oBAAoB,CAACE,OAAO,CAAC,CAAC;IACnFpB,cAAc,GAAGkB,oBAAoB,CAACF,SAAS,CAAC;EAClD,CAAC,MAAM,IAAIN,kBAAkB,IAAI,IAAI,EAAE;IACrCV,cAAc,GAAGmB,oBAAoB,CAACR,SAAS,EAAED,kBAAkB,CAAC,GAAGG,MAAM;EAC/E,CAAC,MAAM,IAAI/B,YAAY,EAAE;IACvBkB,cAAc,GAAG5D,GAAG,CAACO,cAAc,CAACG,QAAQ,CAACtC,KAAK,CAAC,CAACwG,SAAS,CAAC,GAAGH,MAAM;EACzE,CAAC,MAAM;IACLb,cAAc,GAAGpB,MAAM,CAAC9B,QAAQ,CAACvC,EAAE,CAAC;EACtC;EAEAkG,gBAAgB,CAACT,cAAc,GAAGA,cAAc;EAEhD,IAAIlB,YAAY,EAAE;IAChB2B,gBAAgB,CAACD,eAAe,GAAG5B,MAAM,CAAC9B,QAAQ,CAACvC,EAAE,CAAC;EACxD,CAAC,CAAC;EACF;;EAGAkG,gBAAgB,CAACR,MAAM,GAAGD,cAAc,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;AACvD;AAEA,SAASmB,oBAAoBA,CAACE,IAAI,EAAEC,KAAK,EAAE;EACzC,OAAOD,IAAI,CAACP,aAAa,CAACO,IAAI,CAACN,WAAW,CAACM,IAAI,CAACE,KAAK,CAACC,KAAK,CAACF,KAAK,CAAC,CAAC,CAAC;AACtE,CAAC,CAAC;;AAGF,SAASvB,iBAAiBA,CAACvE,IAAI,EAAE0B,SAAS,EAAE0B,MAAM,EAAEE,YAAY,EAAEC,UAAU,EAAEiB,cAAc,EAAEC,MAAM,EAAEZ,iBAAiB,EAAEjD,GAAG,EAAEqE,gBAAgB,EAAE;EAC9I,IAAI3D,QAAQ,GAAGV,GAAG,CAACU,QAAQ;EAC3B,IAAIC,WAAW,GAAGX,GAAG,CAACW,WAAW;EACjC,IAAI0E,YAAY,GAAGtC,IAAI,CAACuC,GAAG,CAAC9C,MAAM,CAAC7B,WAAW,CAACxC,EAAE,CAAC,CAAC;EACnD,IAAI6F,UAAU,GAAG5E,IAAI,CAACgE,aAAa,CAACtC,SAAS,EAAE,YAAY,CAAC;EAC5D,IAAIyE,gBAAgB;EAEpB,IAAIpI,MAAM,CAAC0H,OAAO,CAACb,UAAU,CAAC,EAAE;IAC9BuB,gBAAgB,GAAGvB,UAAU,CAACwB,KAAK,CAAC,CAAC;EACvC,CAAC,MAAM;IACL,IAAIxB,UAAU,IAAI,IAAI,EAAE;MACtB;MACAuB,gBAAgB,GAAG,CAAC,MAAM,EAAE,MAAM,CAAC;IACrC,CAAC,MAAM;MACLA,gBAAgB,GAAG,CAACvB,UAAU,EAAEA,UAAU,CAAC;IAC7C;EACF,CAAC,CAAC;EACF;EACA;EACA;;EAGAuB,gBAAgB,CAAC5E,WAAW,CAACvC,KAAK,CAAC,GAAGZ,YAAY,CAAC+H,gBAAgB,CAAC5E,WAAW,CAACvC,KAAK,CAAC,EAAEiH,YAAY,CAAC;EACrGE,gBAAgB,CAAC7E,QAAQ,CAACtC,KAAK,CAAC,GAAGZ,YAAY,CAAC+H,gBAAgB,CAAC7E,QAAQ,CAACtC,KAAK,CAAC,EAAEsE,YAAY,GAAG2C,YAAY,GAAGtC,IAAI,CAACuC,GAAG,CAAC1B,cAAc,CAAC,CAAC;EACzIS,gBAAgB,CAACL,UAAU,GAAGuB,gBAAgB,CAAC,CAAC;;EAEhD,IAAIxB,WAAW,GAAGM,gBAAgB,CAACN,WAAW,GAAG,CAACwB,gBAAgB,CAAC,CAAC,CAAC,GAAGtC,iBAAiB,EAAEsC,gBAAgB,CAAC,CAAC,CAAC,GAAGtC,iBAAiB,CAAC,CAAC,CAAC;;EAErIc,WAAW,CAACrD,QAAQ,CAACtC,KAAK,CAAC,IAAI,CAAC4B,GAAG,CAACJ,YAAY,GAAG,CAAC,CAAC,GAAG,CAAC,IAAIiE,MAAM;AACrE;AAEA,SAASC,gBAAgBA,CAAC9C,SAAS,EAAE+C,WAAW,EAAEjB,QAAQ,EAAE9C,GAAG,EAAEqE,gBAAgB,EAAE;EACjF;EACA;EACA;EACA,IAAIF,cAAc,GAAGnD,SAAS,CAACqB,GAAG,CAACrE,sBAAsB,CAAC,IAAI,CAAC;EAE/D,IAAImG,cAAc,EAAE;IAClB7F,gBAAgB,CAACmH,IAAI,CAAC;MACpBC,MAAM,EAAE3B,WAAW,CAAC,CAAC,CAAC;MACtB4B,MAAM,EAAE5B,WAAW,CAAC,CAAC,CAAC;MACtBjB,QAAQ,EAAEA;IACZ,CAAC,CAAC;IACFxE,gBAAgB,CAACsH,eAAe,CAAC,CAAC;IAClCzB,cAAc,IAAI7F,gBAAgB,CAACuH,YAAY,CAAC,CAAC;IACjD1B,cAAc,IAAIJ,WAAW,CAAC/D,GAAG,CAACU,QAAQ,CAACtC,KAAK,CAAC;EACnD;EAEAiG,gBAAgB,CAACF,cAAc,GAAGA,cAAc,IAAI,CAAC;AACvD;AAEA,SAASD,iBAAiBA,CAAClD,SAAS,EAAEgD,UAAU,EAAExB,MAAM,EAAEE,YAAY,EAAEC,UAAU,EAAEsB,YAAY,EAAErB,cAAc,EAAEuB,cAAc,EAAEP,cAAc,EAAEQ,eAAe,EAAEpE,GAAG,EAAEqE,gBAAgB,EAAE;EACxL,IAAI1D,WAAW,GAAGX,GAAG,CAACW,WAAW;EACjC,IAAID,QAAQ,GAAGV,GAAG,CAACU,QAAQ;EAC3B,IAAImD,MAAM,GAAGQ,gBAAgB,CAACR,MAAM;EACpC,IAAIiC,UAAU,GAAG/C,IAAI,CAACgD,GAAG,CAAC/B,UAAU,CAACtD,QAAQ,CAACtC,KAAK,CAAC,GAAG+F,cAAc,EAAE,CAAC,CAAC;EACzE,IAAI6B,OAAO,GAAGF,UAAU,CAAC,CAAC;EAC1B;EACA;;EAEA,IAAIpD,YAAY,EAAE;IAChB,IAAIuD,iBAAiB,GAAGlD,IAAI,CAACuC,GAAG,CAAC1B,cAAc,CAAC;IAChD,IAAIsC,YAAY,GAAG/I,MAAM,CAACgJ,QAAQ,CAACnF,SAAS,CAACqB,GAAG,CAAC,cAAc,CAAC,EAAE,KAAK,CAAC,GAAG,EAAE;IAC7E,IAAI+D,SAAS,GAAG,KAAK;IAErB,IAAIF,YAAY,CAACG,WAAW,CAAC,GAAG,CAAC,KAAKH,YAAY,CAACI,MAAM,GAAG,CAAC,EAAE;MAC7DF,SAAS,GAAG,IAAI;MAChBF,YAAY,GAAGA,YAAY,CAACV,KAAK,CAAC,CAAC,EAAEU,YAAY,CAACI,MAAM,GAAG,CAAC,CAAC;IAC/D;IAEA,IAAIC,mBAAmB,GAAG/I,YAAY,CAAC0I,YAAY,EAAElC,UAAU,CAACtD,QAAQ,CAACtC,KAAK,CAAC,CAAC;IAChF,IAAIoI,cAAc,GAAGzD,IAAI,CAACgD,GAAG,CAACD,UAAU,GAAGS,mBAAmB,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACxE;;IAEA,IAAIE,MAAM,GAAGL,SAAS,GAAG,CAAC,GAAGG,mBAAmB,GAAG,CAAC,CAAC,CAAC;IACtD;;IAEA,IAAIG,eAAe,GAAGjJ,SAAS,CAACiF,YAAY,CAAC;IAC7C,IAAIiE,WAAW,GAAGD,eAAe,GAAGhE,YAAY,GAAGkE,UAAU,CAAC,CAACX,iBAAiB,GAAGQ,MAAM,IAAID,cAAc,CAAC,CAAC,CAAC;IAC9G;;IAEA,IAAIK,KAAK,GAAGZ,iBAAiB,GAAGU,WAAW,GAAGb,UAAU;IACxDS,mBAAmB,GAAGM,KAAK,GAAG,CAAC,IAAIT,SAAS,GAAGO,WAAW,GAAG5D,IAAI,CAACgD,GAAG,CAACY,WAAW,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC;IAC1FH,cAAc,GAAGV,UAAU,GAAGS,mBAAmB,GAAG,CAAC;IACrDE,MAAM,GAAGL,SAAS,GAAG,CAAC,GAAGG,mBAAmB,GAAG,CAAC,CAAC,CAAC;;IAElD,IAAI,CAACG,eAAe,IAAIhE,YAAY,KAAK,OAAO,EAAE;MAChDiE,WAAW,GAAGvC,eAAe,GAAGwC,UAAU,CAAC,CAAC7D,IAAI,CAACuC,GAAG,CAAClB,eAAe,CAAC,GAAGqC,MAAM,IAAID,cAAc,CAAC,GAAG,CAAC;IACvG;IAEAR,OAAO,GAAGW,WAAW,GAAGH,cAAc,GAAGC,MAAM;IAC/CpC,gBAAgB,CAACsC,WAAW,GAAGA,WAAW;IAC1CtC,gBAAgB,CAAC6B,YAAY,GAAGK,mBAAmB;EACrD;EAEA,IAAIO,OAAO,GAAGjD,MAAM,IAAImC,OAAO,GAAG,CAAC,CAAC;EACpC,IAAIe,YAAY,GAAG1C,gBAAgB,CAAC0C,YAAY,GAAG,EAAE;EACrDA,YAAY,CAACpG,WAAW,CAACvC,KAAK,CAAC,GAAGoE,MAAM,CAAC7B,WAAW,CAACxC,EAAE,CAAC,GAAG,CAAC;EAC5D4I,YAAY,CAACrG,QAAQ,CAACtC,KAAK,CAAC,GAAGwE,cAAc,KAAK,OAAO,GAAGkE,OAAO,GAAGlE,cAAc,KAAK,KAAK,GAAGgB,cAAc,GAAGkD,OAAO,GAAGlD,cAAc,GAAG,CAAC,CAAC,CAAC;;EAEhJ,IAAIK,YAAY,EAAE;IAChB8C,YAAY,CAAC,CAAC,CAAC,IAAI9C,YAAY,CAAC,CAAC,CAAC;IAClC8C,YAAY,CAAC,CAAC,CAAC,IAAI9C,YAAY,CAAC,CAAC,CAAC;EACpC;EAEA,IAAI+C,cAAc,GAAG3C,gBAAgB,CAAC2C,cAAc,GAAG,EAAE;EACzDA,cAAc,CAACrG,WAAW,CAACvC,KAAK,CAAC,GAAGoE,MAAM,CAAC7B,WAAW,CAACzC,EAAE,CAAC;EAC1D8I,cAAc,CAACtG,QAAQ,CAACtC,KAAK,CAAC,GAAGoE,MAAM,CAAC9B,QAAQ,CAACxC,EAAE,CAAC;EACpD,IAAI+I,YAAY,GAAG5C,gBAAgB,CAAC4C,YAAY,GAAG9J,MAAM,CAAC+J,MAAM,CAAC,CAAC,CAAC,EAAE1E,MAAM,CAAC;EAC5EyE,YAAY,CAACvG,QAAQ,CAACvC,EAAE,CAAC,GAAG0F,MAAM,GAAGd,IAAI,CAACgD,GAAG,CAAChD,IAAI,CAACuC,GAAG,CAAC9C,MAAM,CAAC9B,QAAQ,CAACvC,EAAE,CAAC,CAAC,EAAE4E,IAAI,CAACuC,GAAG,CAACyB,YAAY,CAACrG,QAAQ,CAACtC,KAAK,CAAC,GAAG0I,OAAO,CAAC,CAAC;EAC9HG,YAAY,CAACtG,WAAW,CAACxC,EAAE,CAAC,GAAGqE,MAAM,CAAC7B,WAAW,CAACxC,EAAE,CAAC;EACrD,IAAIgJ,SAAS,GAAG9C,gBAAgB,CAAC8C,SAAS,GAAG,CAAC,CAAC,CAAC,CAAC;;EAEjDA,SAAS,CAACxG,WAAW,CAACzC,EAAE,CAAC,GAAG,CAACsE,MAAM,CAAC7B,WAAW,CAACzC,EAAE,CAAC;EACnDiJ,SAAS,CAACxG,WAAW,CAACxC,EAAE,CAAC,GAAG6B,GAAG,CAACC,MAAM,CAACU,WAAW,CAACxC,EAAE,CAAC;EACtDgJ,SAAS,CAACzG,QAAQ,CAACxC,EAAE,CAAC,GAAG,CAAC;EAC1BiJ,SAAS,CAACzG,QAAQ,CAACvC,EAAE,CAAC,GAAGqE,MAAM,CAAC9B,QAAQ,CAACvC,EAAE,CAAC;AAC9C;AAEA,SAASiJ,UAAUA,CAAClG,UAAU,EAAE;EAC9B,IAAI+B,iBAAiB,GAAG/B,UAAU,CAAC+B,iBAAiB;EACpD,IAAIoE,IAAI,GAAG/J,YAAY;EAAE;EACzB4D,UAAU,CAACiC,UAAU,EAAE,CAACF,iBAAiB,GAAG,CAAC,EAAE,CAACA,iBAAiB,GAAG,CAAC,EAAEA,iBAAiB,EAAEA,iBAAiB,CAAC;EAC5GoE,IAAI,CAAC5B,IAAI,CAAC;IACR6B,OAAO,EAAE;EACX,CAAC,CAAC;EACFD,IAAI,CAACxI,IAAI,KAAK,OAAO,IAAIwI,IAAI,CAACE,QAAQ,CAAC;IACrCC,aAAa,EAAE;EACjB,CAAC,CAAC;EACF,OAAOH,IAAI;AACb;AAEA,SAASI,2BAA2BA,CAACrG,GAAG,EAAEpB,GAAG,EAAEkB,UAAU,EAAEwG,QAAQ,EAAE;EACnE,IAAIC,MAAM,GAAGvG,GAAG,CAACwG,iBAAiB;EAClC,IAAI5D,UAAU,GAAG9C,UAAU,CAAC8C,UAAU;EACtC,IAAIG,cAAc,GAAGjD,UAAU,CAACiD,cAAc;EAC9C,IAAI4C,YAAY,GAAG7F,UAAU,CAAC6F,YAAY;EAC1C,IAAIrG,QAAQ,GAAGV,GAAG,CAACU,QAAQ;EAC3B,IAAIiG,WAAW,GAAGzF,UAAU,CAACyF,WAAW,IAAI,CAAC;EAC7C,IAAIvI,KAAK,GAAG,CAAC;EACb,IAAIyJ,IAAI,GAAG7D,UAAU,CAAChE,GAAG,CAACU,QAAQ,CAACtC,KAAK,CAAC,GAAG+F,cAAc,GAAGjD,UAAU,CAACgF,YAAY,GAAG,CAAC;EACxF4B,QAAQ,CAAC1G,GAAG,EAAE,UAAUiG,IAAI,EAAE;IAC5BA,IAAI,CAACU,yBAAyB,GAAG3J,KAAK;IACtCiJ,IAAI,CAACW,sBAAsB,GAAGrB,WAAW;IAEzC,IAAIvI,KAAK,GAAGuI,WAAW,EAAE;MACvBsB,UAAU,CAACZ,IAAI,EAAE,IAAI,EAAEa,UAAU,CAAC9J,KAAK,CAAC,EAAE8C,UAAU,EAAEwG,QAAQ,CAAC;IACjE,CAAC,MAAM;MACLO,UAAU,CAACZ,IAAI,EAAE,IAAI,EAAE;QACrB3B,MAAM,EAAE,CAAC;QACTC,MAAM,EAAE;MACV,CAAC,EAAEzE,UAAU,EAAEwG,QAAQ,EAAE,YAAY;QACnCC,MAAM,CAAC/F,MAAM,CAACyF,IAAI,CAAC;MACrB,CAAC,CAAC;IACJ,CAAC,CAAC;;IAGFjJ,KAAK,EAAE;EACT,CAAC,CAAC;EAEF,OAAOA,KAAK,GAAGuI,WAAW,EAAEvI,KAAK,EAAE,EAAE;IACnC,IAAIiJ,IAAI,GAAGD,UAAU,CAAClG,UAAU,CAAC;IACjCmG,IAAI,CAACU,yBAAyB,GAAG3J,KAAK;IACtCiJ,IAAI,CAACW,sBAAsB,GAAGrB,WAAW;IACzCgB,MAAM,CAAC9G,GAAG,CAACwG,IAAI,CAAC;IAChB,IAAIc,MAAM,GAAGD,UAAU,CAAC9J,KAAK,CAAC;IAC9B6J,UAAU,CAACZ,IAAI,EAAE;MACf7G,CAAC,EAAE2H,MAAM,CAAC3H,CAAC;MACXC,CAAC,EAAE0H,MAAM,CAAC1H,CAAC;MACXiF,MAAM,EAAE,CAAC;MACTC,MAAM,EAAE;IACV,CAAC,EAAE;MACDD,MAAM,EAAEyC,MAAM,CAACzC,MAAM;MACrBC,MAAM,EAAEwC,MAAM,CAACxC,MAAM;MACrB7C,QAAQ,EAAEqF,MAAM,CAACrF;IACnB,CAAC,EAAE5B,UAAU,EAAEwG,QAAQ,CAAC;EAC1B;EAEA,SAASQ,UAAUA,CAAC9J,KAAK,EAAE;IACzB,IAAIgK,QAAQ,GAAGrB,YAAY,CAACvB,KAAK,CAAC,CAAC,CAAC,CAAC;IACrC;;IAEA,IAAI3B,MAAM,GAAG3C,UAAU,CAAC2C,MAAM;IAC9B,IAAIwE,CAAC,GAAGjK,KAAK;IAEb,IAAI8C,UAAU,CAACoC,qBAAqB,KAAK,OAAO,GAAGO,MAAM,GAAG,CAAC,GAAGA,MAAM,GAAG,CAAC,EAAE;MAC1EwE,CAAC,GAAG1B,WAAW,GAAG,CAAC,GAAGvI,KAAK;IAC7B;IAEAgK,QAAQ,CAAC1H,QAAQ,CAACtC,KAAK,CAAC,GAAGyJ,IAAI,IAAIQ,CAAC,GAAG1B,WAAW,GAAG,CAAC,GAAG,GAAG,CAAC,GAAGI,YAAY,CAACrG,QAAQ,CAACtC,KAAK,CAAC;IAC5F,OAAO;MACLoC,CAAC,EAAE4H,QAAQ,CAAC,CAAC,CAAC;MACd3H,CAAC,EAAE2H,QAAQ,CAAC,CAAC,CAAC;MACd1C,MAAM,EAAExE,UAAU,CAAC6C,WAAW,CAAC,CAAC,CAAC;MACjC4B,MAAM,EAAEzE,UAAU,CAAC6C,WAAW,CAAC,CAAC,CAAC;MACjCjB,QAAQ,EAAE5B,UAAU,CAAC4B;IACvB,CAAC;EACH;AACF;AAEA,SAASwF,0BAA0BA,CAAClH,GAAG,EAAEpB,GAAG,EAAEkB,UAAU,EAAEwG,QAAQ,EAAE;EAClE,IAAIC,MAAM,GAAGvG,GAAG,CAACwG,iBAAiB;EAClC,IAAIW,QAAQ,GAAGnH,GAAG,CAACoH,mBAAmB;EAEtC,IAAI,CAACD,QAAQ,EAAE;IACbA,QAAQ,GAAGnH,GAAG,CAACoH,mBAAmB,GAAGpB,UAAU,CAAClG,UAAU,CAAC;IAC3DyG,MAAM,CAAC9G,GAAG,CAAC0H,QAAQ,CAAC;IACpBN,UAAU,CAACM,QAAQ,EAAE;MACnB/H,CAAC,EAAEU,UAAU,CAAC6F,YAAY,CAAC,CAAC,CAAC;MAC7BtG,CAAC,EAAES,UAAU,CAAC6F,YAAY,CAAC,CAAC,CAAC;MAC7BrB,MAAM,EAAE,CAAC;MACTC,MAAM,EAAE,CAAC;MACT7C,QAAQ,EAAE5B,UAAU,CAAC4B;IACvB,CAAC,EAAE;MACD4C,MAAM,EAAExE,UAAU,CAAC6C,WAAW,CAAC,CAAC,CAAC;MACjC4B,MAAM,EAAEzE,UAAU,CAAC6C,WAAW,CAAC,CAAC;IAClC,CAAC,EAAE7C,UAAU,EAAEwG,QAAQ,CAAC;EAC1B,CAAC,MAAM;IACLO,UAAU,CAACM,QAAQ,EAAE,IAAI,EAAE;MACzB/H,CAAC,EAAEU,UAAU,CAAC6F,YAAY,CAAC,CAAC,CAAC;MAC7BtG,CAAC,EAAES,UAAU,CAAC6F,YAAY,CAAC,CAAC,CAAC;MAC7BrB,MAAM,EAAExE,UAAU,CAAC6C,WAAW,CAAC,CAAC,CAAC;MACjC4B,MAAM,EAAEzE,UAAU,CAAC6C,WAAW,CAAC,CAAC,CAAC;MACjCjB,QAAQ,EAAE5B,UAAU,CAAC4B;IACvB,CAAC,EAAE5B,UAAU,EAAEwG,QAAQ,CAAC;EAC1B;AACF,CAAC,CAAC;;AAGF,SAASe,qBAAqBA,CAACrH,GAAG,EAAEF,UAAU,EAAEwG,QAAQ,EAAE;EACxD,IAAIgB,SAAS,GAAGvL,MAAM,CAAC+J,MAAM,CAAC,CAAC,CAAC,EAAEhG,UAAU,CAAC+F,YAAY,CAAC;EAC1D,IAAI0B,OAAO,GAAGvH,GAAG,CAACwH,kBAAkB;EAEpC,IAAI,CAACD,OAAO,EAAE;IACZA,OAAO,GAAGvH,GAAG,CAACwH,kBAAkB,GAAG,IAAIxL,OAAO,CAACyL,IAAI,CAAC;MAClDrF,EAAE,EAAE,CAAC;MACLsF,KAAK,EAAEJ,SAAS;MAChBK,MAAM,EAAE,IAAI;MACZ1F,KAAK,EAAE;QACL2F,MAAM,EAAE,aAAa;QACrBC,IAAI,EAAE,aAAa;QACnBC,SAAS,EAAE;MACb;IACF,CAAC,CAAC;IACFP,OAAO,CAACQ,eAAe,GAAG,IAAI;IAC9B/H,GAAG,CAACP,GAAG,CAAC8H,OAAO,CAAC;EAClB,CAAC,MAAM;IACLV,UAAU,CAACU,OAAO,EAAE,IAAI,EAAE;MACxBG,KAAK,EAAEJ;IACT,CAAC,EAAExH,UAAU,EAAEwG,QAAQ,CAAC;EAC1B;AACF;AAEA,SAAS0B,kBAAkBA,CAAChI,GAAG,EAAEpB,GAAG,EAAEkB,UAAU,EAAEwG,QAAQ,EAAE;EAC1D;EACA,IAAIxG,UAAU,CAACyB,UAAU,EAAE;IACzB,IAAI0G,QAAQ,GAAGjI,GAAG,CAACkI,mBAAmB;IACtC,IAAInC,SAAS,GAAGhK,MAAM,CAAC+J,MAAM,CAAC,CAAC,CAAC,EAAEhG,UAAU,CAACiG,SAAS,CAAC;IACvD,IAAIzG,QAAQ,GAAGV,GAAG,CAACU,QAAQ;IAC3B,IAAIyB,cAAc,GAAGjB,UAAU,CAACiB,cAAc;IAC9C,IAAIrB,SAAS,GAAGI,UAAU,CAACJ,SAAS;IAEpC,IAAIuI,QAAQ,EAAE;MACZjM,OAAO,CAACmM,WAAW,CAACF,QAAQ,EAAE;QAC5BP,KAAK,EAAE3B;MACT,CAAC,EAAEhF,cAAc,EAAErB,SAAS,CAAC;IAC/B,CAAC,MAAM;MACLqG,SAAS,CAACzG,QAAQ,CAACvC,EAAE,CAAC,GAAG,CAAC;MAC1BkL,QAAQ,GAAG,IAAIjM,OAAO,CAACyL,IAAI,CAAC;QAC1BC,KAAK,EAAE3B;MACT,CAAC,CAAC;MAEF/F,GAAG,CAACwG,iBAAiB,CAAC4B,WAAW,CAACH,QAAQ,CAAC;MAE3CjI,GAAG,CAACkI,mBAAmB,GAAGD,QAAQ;MAClC,IAAIlB,MAAM,GAAG,CAAC,CAAC;MACfA,MAAM,CAACzH,QAAQ,CAACvC,EAAE,CAAC,GAAG+C,UAAU,CAACiG,SAAS,CAACzG,QAAQ,CAACvC,EAAE,CAAC;MACvDf,OAAO,CAACsK,QAAQ,GAAG,aAAa,GAAG,WAAW,CAAC,CAAC2B,QAAQ,EAAE;QACxDP,KAAK,EAAEX;MACT,CAAC,EAAEhG,cAAc,EAAErB,SAAS,CAAC;IAC/B;EACF;AACF;AAEA,SAASG,YAAYA,CAAC7B,IAAI,EAAE0B,SAAS,EAAE;EACrC,IAAIE,SAAS,GAAG5B,IAAI,CAAC6B,YAAY,CAACH,SAAS,CAAC;EAC5CE,SAAS,CAACyI,uBAAuB,GAAGA,uBAAuB;EAC3DzI,SAAS,CAACkC,kBAAkB,GAAGA,kBAAkB;EACjD,OAAOlC,SAAS;AAClB;AAEA,SAASyI,uBAAuBA,CAACpC,IAAI,EAAE;EACrC;EACA,OAAO;IACLjJ,KAAK,EAAEiJ,IAAI,CAACU,yBAAyB;IACrC2B,KAAK,EAAErC,IAAI,CAACW;EACd,CAAC;AACH;AAEA,SAAS9E,kBAAkBA,CAAA,EAAG;EAC5B;EACA,OAAO,IAAI,CAACyG,WAAW,CAACzG,kBAAkB,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,CAACO,UAAU,CAAC,WAAW,CAAC;AAChF;AAEA,SAASpC,SAASA,CAACjC,IAAI,EAAEY,GAAG,EAAEkB,UAAU,EAAEwG,QAAQ,EAAE;EAClD;EACA,IAAItG,GAAG,GAAG,IAAIhE,OAAO,CAACwM,KAAK,CAAC,CAAC,CAAC,CAAC;;EAE/B,IAAIjC,MAAM,GAAG,IAAIvK,OAAO,CAACwM,KAAK,CAAC,CAAC;EAChCxI,GAAG,CAACP,GAAG,CAAC8G,MAAM,CAAC;EACfvG,GAAG,CAACwG,iBAAiB,GAAGD,MAAM;EAC9BA,MAAM,CAACnH,CAAC,GAAGU,UAAU,CAAC8F,cAAc,CAAC,CAAC,CAAC;EACvCW,MAAM,CAAClH,CAAC,GAAGS,UAAU,CAAC8F,cAAc,CAAC,CAAC,CAAC;EAEvC,IAAI9F,UAAU,CAACwB,YAAY,EAAE;IAC3B+E,2BAA2B,CAACrG,GAAG,EAAEpB,GAAG,EAAEkB,UAAU,CAAC;EACnD,CAAC,MAAM;IACLoH,0BAA0B,CAAClH,GAAG,EAAEpB,GAAG,EAAEkB,UAAU,CAAC;EAClD;EAEAuH,qBAAqB,CAACrH,GAAG,EAAEF,UAAU,EAAEwG,QAAQ,CAAC;EAChD0B,kBAAkB,CAAChI,GAAG,EAAEpB,GAAG,EAAEkB,UAAU,EAAEwG,QAAQ,CAAC;EAClDtG,GAAG,CAACW,mBAAmB,GAAGD,WAAW,CAAC1C,IAAI,EAAE8B,UAAU,CAAC;EACvDE,GAAG,CAACa,qBAAqB,GAAGf,UAAU;EACtC,OAAOE,GAAG;AACZ;AAEA,SAASY,SAASA,CAACZ,GAAG,EAAEpB,GAAG,EAAEkB,UAAU,EAAE;EACvC,IAAIiB,cAAc,GAAGjB,UAAU,CAACiB,cAAc;EAC9C,IAAIrB,SAAS,GAAGI,UAAU,CAACJ,SAAS;EACpC,IAAI6G,MAAM,GAAGvG,GAAG,CAACwG,iBAAiB;EAClCxK,OAAO,CAACmM,WAAW,CAAC5B,MAAM,EAAE;IAC1BnH,CAAC,EAAEU,UAAU,CAAC8F,cAAc,CAAC,CAAC,CAAC;IAC/BvG,CAAC,EAAES,UAAU,CAAC8F,cAAc,CAAC,CAAC;EAChC,CAAC,EAAE7E,cAAc,EAAErB,SAAS,CAAC;EAE7B,IAAII,UAAU,CAACwB,YAAY,EAAE;IAC3B+E,2BAA2B,CAACrG,GAAG,EAAEpB,GAAG,EAAEkB,UAAU,EAAE,IAAI,CAAC;EACzD,CAAC,MAAM;IACLoH,0BAA0B,CAAClH,GAAG,EAAEpB,GAAG,EAAEkB,UAAU,EAAE,IAAI,CAAC;EACxD;EAEAuH,qBAAqB,CAACrH,GAAG,EAAEF,UAAU,EAAE,IAAI,CAAC;EAC5CkI,kBAAkB,CAAChI,GAAG,EAAEpB,GAAG,EAAEkB,UAAU,EAAE,IAAI,CAAC;AAChD;AAEA,SAASgB,SAASA,CAAC9C,IAAI,EAAE0B,SAAS,EAAEqB,cAAc,EAAEf,GAAG,EAAE;EACvD;EACA,IAAIyI,SAAS,GAAGzI,GAAG,CAACwH,kBAAkB;EACtCiB,SAAS,IAAIA,SAAS,CAACC,iBAAiB,CAAC,CAAC;EAC1C,IAAIC,KAAK,GAAG,EAAE;EACdjC,QAAQ,CAAC1G,GAAG,EAAE,UAAUiG,IAAI,EAAE;IAC5B0C,KAAK,CAACC,IAAI,CAAC3C,IAAI,CAAC;EAClB,CAAC,CAAC;EACFjG,GAAG,CAACoH,mBAAmB,IAAIuB,KAAK,CAACC,IAAI,CAAC5I,GAAG,CAACoH,mBAAmB,CAAC,CAAC,CAAC;;EAEhEpH,GAAG,CAACkI,mBAAmB,KAAKnH,cAAc,GAAG,IAAI,CAAC;EAClDhF,MAAM,CAAC8M,IAAI,CAACF,KAAK,EAAE,UAAU1C,IAAI,EAAE;IACjCjK,OAAO,CAAC8M,aAAa,CAAC7C,IAAI,EAAE;MAC1B3B,MAAM,EAAE,CAAC;MACTC,MAAM,EAAE;IACV,CAAC,EAAExD,cAAc,EAAErB,SAAS,EAAE,YAAY;MACxCM,GAAG,CAAC+I,MAAM,IAAI/I,GAAG,CAAC+I,MAAM,CAACvI,MAAM,CAACR,GAAG,CAAC;IACtC,CAAC,CAAC;EACJ,CAAC,CAAC;EACFhC,IAAI,CAACkC,gBAAgB,CAACR,SAAS,EAAE,IAAI,CAAC;AACxC;AAEA,SAASgB,WAAWA,CAAC1C,IAAI,EAAE8B,UAAU,EAAE;EACrC,OAAO,CAAC9B,IAAI,CAACgE,aAAa,CAAClC,UAAU,CAACJ,SAAS,EAAE,QAAQ,CAAC,IAAI,MAAM,EAAE,CAAC,CAACI,UAAU,CAACwB,YAAY,EAAE,CAAC,CAACxB,UAAU,CAACyB,UAAU,CAAC,CAACyH,IAAI,CAAC,GAAG,CAAC;AACrI;AAEA,SAAStC,QAAQA,CAAC1G,GAAG,EAAEiJ,EAAE,EAAEC,OAAO,EAAE;EAClC;EACAnN,MAAM,CAAC8M,IAAI,CAAC7I,GAAG,CAACwG,iBAAiB,CAAC2C,QAAQ,CAAC,CAAC,EAAE,UAAUC,EAAE,EAAE;IAC1DA,EAAE,KAAKpJ,GAAG,CAACwH,kBAAkB,IAAIyB,EAAE,CAACI,IAAI,CAACH,OAAO,EAAEE,EAAE,CAAC;EACvD,CAAC,CAAC;AACJ;AAEA,SAASvC,UAAUA,CAACuC,EAAE,EAAEE,cAAc,EAAEC,cAAc,EAAEzJ,UAAU,EAAEwG,QAAQ,EAAE2C,EAAE,EAAE;EAChFK,cAAc,IAAIF,EAAE,CAAC/E,IAAI,CAACiF,cAAc,CAAC,CAAC,CAAC;;EAE3C,IAAIxJ,UAAU,CAACyB,UAAU,IAAI,CAAC+E,QAAQ,EAAE;IACtCiD,cAAc,IAAIH,EAAE,CAAC/E,IAAI,CAACkF,cAAc,CAAC;EAC3C,CAAC,MAAM;IACLA,cAAc,IAAIvN,OAAO,CAACsK,QAAQ,GAAG,aAAa,GAAG,WAAW,CAAC,CAAC8C,EAAE,EAAEG,cAAc,EAAEzJ,UAAU,CAACiB,cAAc,EAAEjB,UAAU,CAACJ,SAAS,EAAEuJ,EAAE,CAAC;EAC5I;AACF;AAEA,SAAS9I,YAAYA,CAACH,GAAG,EAAEpB,GAAG,EAAEkB,UAAU,EAAE;EAC1C,IAAIJ,SAAS,GAAGI,UAAU,CAACJ,SAAS;EACpC,IAAIE,SAAS,GAAGE,UAAU,CAACF,SAAS,CAAC,CAAC;EACtC;;EAEA,IAAI4J,aAAa,GAAG5J,SAAS,CAAC6J,QAAQ,CAAC,UAAU,CAAC;EAClD,IAAIC,aAAa,GAAGF,aAAa,CAACC,QAAQ,CAAC,WAAW,CAAC,CAACE,YAAY,CAAC,CAAC;EACtE,IAAIC,SAAS,GAAGhK,SAAS,CAAC6J,QAAQ,CAAC,CAAC,MAAM,EAAE,WAAW,CAAC,CAAC,CAACE,YAAY,CAAC,CAAC;EACxE,IAAIE,WAAW,GAAGjK,SAAS,CAAC6J,QAAQ,CAAC,CAAC,QAAQ,EAAE,WAAW,CAAC,CAAC,CAACE,YAAY,CAAC,CAAC;EAC5E,IAAIG,WAAW,GAAGlK,SAAS,CAACyC,UAAU,CAAC,QAAQ,CAAC;EAChD,IAAI0H,KAAK,GAAGP,aAAa,CAACvI,GAAG,CAAC,OAAO,CAAC;EACtC,IAAI+I,SAAS,GAAGR,aAAa,CAACvI,GAAG,CAAC,WAAW,CAAC;EAC9C,IAAIkB,UAAU,GAAGqH,aAAa,CAACvI,GAAG,CAAC,OAAO,CAAC;EAC3CyF,QAAQ,CAAC1G,GAAG,EAAE,UAAUiG,IAAI,EAAE;IAC5B,IAAIA,IAAI,YAAYvJ,OAAO,EAAE;MAC3B,IAAIuN,SAAS,GAAGhE,IAAI,CAAChE,KAAK;MAC1BgE,IAAI,CAACiE,QAAQ,CAACnO,MAAM,CAAC+J,MAAM,CAAC;QAC1B;QACAqE,KAAK,EAAEF,SAAS,CAACE,KAAK;QACtB/K,CAAC,EAAE6K,SAAS,CAAC7K,CAAC;QACdC,CAAC,EAAE4K,SAAS,CAAC5K,CAAC;QACdP,KAAK,EAAEmL,SAAS,CAACnL,KAAK;QACtBE,MAAM,EAAEiL,SAAS,CAACjL;MACpB,CAAC,EAAEc,UAAU,CAACmC,KAAK,CAAC,CAAC;IACvB,CAAC,MAAM;MACLgE,IAAI,CAACiE,QAAQ,CAACpK,UAAU,CAACmC,KAAK,CAAC;IACjC;IAEA,IAAImI,aAAa,GAAGnE,IAAI,CAACoE,WAAW,CAAC,UAAU,CAAC;IAChDD,aAAa,CAACnI,KAAK,GAAGyH,aAAa;IAEnC,IAAIvH,UAAU,EAAE;MACd;MACAiI,aAAa,CAAC9F,MAAM,GAAG2B,IAAI,CAAC3B,MAAM,GAAG,GAAG;MACxC8F,aAAa,CAAC7F,MAAM,GAAG0B,IAAI,CAAC1B,MAAM,GAAG,GAAG;IAC1C;IAEA0B,IAAI,CAACoE,WAAW,CAAC,MAAM,CAAC,CAACpI,KAAK,GAAG2H,SAAS;IAC1C3D,IAAI,CAACoE,WAAW,CAAC,QAAQ,CAAC,CAACpI,KAAK,GAAG4H,WAAW;IAC9CC,WAAW,KAAK7D,IAAI,CAACqE,MAAM,GAAGR,WAAW,CAAC;IAC1C7D,IAAI,CAAC7D,EAAE,GAAGtC,UAAU,CAACsC,EAAE;EACzB,CAAC,CAAC;EACF,IAAImI,kBAAkB,GAAG3L,GAAG,CAACU,QAAQ,CAACrC,OAAO,CAAC,EAAE6C,UAAU,CAAC0C,cAAc,GAAG,CAAC,CAAC,CAAC;EAC/E,IAAI+E,OAAO,GAAGvH,GAAG,CAACwH,kBAAkB;EACpChL,aAAa,CAAC+K,OAAO,EAAE9K,oBAAoB,CAACmD,SAAS,CAAC,EAAE;IACtD4K,YAAY,EAAE5L,GAAG,CAAChB,WAAW;IAC7B6M,cAAc,EAAE/K,SAAS;IACzBgL,WAAW,EAAEnO,eAAe,CAACqC,GAAG,CAAChB,WAAW,CAACK,OAAO,CAAC,CAAC,EAAEyB,SAAS,CAAC;IAClEiL,YAAY,EAAE7K,UAAU,CAACmC,KAAK,CAAC4F,IAAI;IACnC+C,cAAc,EAAE9K,UAAU,CAACmC,KAAK,CAAC4I,OAAO;IACxCC,sBAAsB,EAAEP;EAC1B,CAAC,CAAC;EACFtO,mBAAmB,CAAC+D,GAAG,EAAE+J,KAAK,EAAEC,SAAS,EAAER,aAAa,CAACvI,GAAG,CAAC,UAAU,CAAC,CAAC;AAC3E;AAEA,SAASuE,UAAUA,CAACuF,KAAK,EAAE;EACzB,IAAIC,YAAY,GAAGrJ,IAAI,CAACsJ,KAAK,CAACF,KAAK,CAAC,CAAC,CAAC;;EAEtC,OAAOpJ,IAAI,CAACuC,GAAG,CAAC6G,KAAK,GAAGC,YAAY,CAAC,GAAG,IAAI,GAAGA,YAAY,GAAGrJ,IAAI,CAACuJ,IAAI,CAACH,KAAK,CAAC;AAChF;AAEA,eAAe3N,gBAAgB"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}