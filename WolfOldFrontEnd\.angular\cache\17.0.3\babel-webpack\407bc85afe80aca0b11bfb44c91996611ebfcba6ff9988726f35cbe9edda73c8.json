{"ast": null, "code": "//! moment.js locale configuration\n//! locale : Arabic (Algeria) [ar-dz]\n//! author : <PERSON><PERSON>: https://github.com/Amine27\n//! author : <PERSON><PERSON> Said: https://github.com/abdelsaid\n//! author : <PERSON>\n//! author : forabi https://github.com/forabi\n//! author : Noureddine LOUAHEDJ : https://github.com/noureddinem\n\n;\n(function (global, factory) {\n  typeof exports === 'object' && typeof module !== 'undefined' && typeof require === 'function' ? factory(require('../moment')) : typeof define === 'function' && define.amd ? define(['../moment'], factory) : factory(global.moment);\n})(this, function (moment) {\n  'use strict';\n\n  //! moment.js locale configuration\n  var pluralForm = function (n) {\n      return n === 0 ? 0 : n === 1 ? 1 : n === 2 ? 2 : n % 100 >= 3 && n % 100 <= 10 ? 3 : n % 100 >= 11 ? 4 : 5;\n    },\n    plurals = {\n      s: ['أقل من ثانية', 'ثانية واحدة', ['ثانيتان', 'ثانيتين'], '%d ثوان', '%d ثانية', '%d ثانية'],\n      m: ['أقل من دقيقة', 'دقيقة واحدة', ['دقيقتان', 'دقيقتين'], '%d دقائق', '%d دقيقة', '%d دقيقة'],\n      h: ['أقل من ساعة', 'ساعة واحدة', ['ساعتان', 'ساعتين'], '%d ساعات', '%d ساعة', '%d ساعة'],\n      d: ['أقل من يوم', 'يوم واحد', ['يومان', 'يومين'], '%d أيام', '%d يومًا', '%d يوم'],\n      M: ['أقل من شهر', 'شهر واحد', ['شهران', 'شهرين'], '%d أشهر', '%d شهرا', '%d شهر'],\n      y: ['أقل من عام', 'عام واحد', ['عامان', 'عامين'], '%d أعوام', '%d عامًا', '%d عام']\n    },\n    pluralize = function (u) {\n      return function (number, withoutSuffix, string, isFuture) {\n        var f = pluralForm(number),\n          str = plurals[u][pluralForm(number)];\n        if (f === 2) {\n          str = str[withoutSuffix ? 0 : 1];\n        }\n        return str.replace(/%d/i, number);\n      };\n    },\n    months = ['جانفي', 'فيفري', 'مارس', 'أفريل', 'ماي', 'جوان', 'جويلية', 'أوت', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'];\n  var arDz = moment.defineLocale('ar-dz', {\n    months: months,\n    monthsShort: months,\n    weekdays: 'الأحد_الإثنين_الثلاثاء_الأربعاء_الخميس_الجمعة_السبت'.split('_'),\n    weekdaysShort: 'أحد_إثنين_ثلاثاء_أربعاء_خميس_جمعة_سبت'.split('_'),\n    weekdaysMin: 'ح_ن_ث_ر_خ_ج_س'.split('_'),\n    weekdaysParseExact: true,\n    longDateFormat: {\n      LT: 'HH:mm',\n      LTS: 'HH:mm:ss',\n      L: 'D/\\u200FM/\\u200FYYYY',\n      LL: 'D MMMM YYYY',\n      LLL: 'D MMMM YYYY HH:mm',\n      LLLL: 'dddd D MMMM YYYY HH:mm'\n    },\n    meridiemParse: /ص|م/,\n    isPM: function (input) {\n      return 'م' === input;\n    },\n    meridiem: function (hour, minute, isLower) {\n      if (hour < 12) {\n        return 'ص';\n      } else {\n        return 'م';\n      }\n    },\n    calendar: {\n      sameDay: '[اليوم عند الساعة] LT',\n      nextDay: '[غدًا عند الساعة] LT',\n      nextWeek: 'dddd [عند الساعة] LT',\n      lastDay: '[أمس عند الساعة] LT',\n      lastWeek: 'dddd [عند الساعة] LT',\n      sameElse: 'L'\n    },\n    relativeTime: {\n      future: 'بعد %s',\n      past: 'منذ %s',\n      s: pluralize('s'),\n      ss: pluralize('s'),\n      m: pluralize('m'),\n      mm: pluralize('m'),\n      h: pluralize('h'),\n      hh: pluralize('h'),\n      d: pluralize('d'),\n      dd: pluralize('d'),\n      M: pluralize('M'),\n      MM: pluralize('M'),\n      y: pluralize('y'),\n      yy: pluralize('y')\n    },\n    postformat: function (string) {\n      return string.replace(/,/g, '،');\n    },\n    week: {\n      dow: 0,\n      // Sunday is the first day of the week.\n      doy: 4 // The week that contains Jan 4th is the first week of the year.\n    }\n  });\n\n  return arDz;\n});", "map": {"version": 3, "names": ["global", "factory", "exports", "module", "require", "define", "amd", "moment", "pluralForm", "n", "plurals", "s", "m", "h", "d", "M", "y", "pluralize", "u", "number", "withoutSuffix", "string", "isFuture", "f", "str", "replace", "months", "arDz", "defineLocale", "monthsShort", "weekdays", "split", "weekdaysShort", "weekdaysMin", "weekdaysParseExact", "longDateFormat", "LT", "LTS", "L", "LL", "LLL", "LLLL", "meridiemParse", "isPM", "input", "meridiem", "hour", "minute", "isLower", "calendar", "sameDay", "nextDay", "nextWeek", "lastDay", "lastWeek", "same<PERSON><PERSON><PERSON>", "relativeTime", "future", "past", "ss", "mm", "hh", "dd", "MM", "yy", "postformat", "week", "dow", "doy"], "sources": ["C:/WolfSystemV2/WolfOldFrontEnd/node_modules/moment/locale/ar-dz.js"], "sourcesContent": ["//! moment.js locale configuration\n//! locale : Arabic (Algeria) [ar-dz]\n//! author : <PERSON><PERSON>: https://github.com/Amine27\n//! author : <PERSON><PERSON> Said: https://github.com/abdelsaid\n//! author : <PERSON>\n//! author : forabi https://github.com/forabi\n//! author : Noureddine LOUAHEDJ : https://github.com/noureddinem\n\n;(function (global, factory) {\n   typeof exports === 'object' && typeof module !== 'undefined'\n       && typeof require === 'function' ? factory(require('../moment')) :\n   typeof define === 'function' && define.amd ? define(['../moment'], factory) :\n   factory(global.moment)\n}(this, (function (moment) { 'use strict';\n\n    //! moment.js locale configuration\n\n    var pluralForm = function (n) {\n            return n === 0\n                ? 0\n                : n === 1\n                ? 1\n                : n === 2\n                ? 2\n                : n % 100 >= 3 && n % 100 <= 10\n                ? 3\n                : n % 100 >= 11\n                ? 4\n                : 5;\n        },\n        plurals = {\n            s: [\n                'أقل من ثانية',\n                'ثانية واحدة',\n                ['ثانيتان', 'ثانيتين'],\n                '%d ثوان',\n                '%d ثانية',\n                '%d ثانية',\n            ],\n            m: [\n                'أقل من دقيقة',\n                'دقيقة واحدة',\n                ['دقيقتان', 'دقيقتين'],\n                '%d دقائق',\n                '%d دقيقة',\n                '%d دقيقة',\n            ],\n            h: [\n                'أقل من ساعة',\n                'ساعة واحدة',\n                ['ساعتان', 'ساعتين'],\n                '%d ساعات',\n                '%d ساعة',\n                '%d ساعة',\n            ],\n            d: [\n                'أقل من يوم',\n                'يوم واحد',\n                ['يومان', 'يومين'],\n                '%d أيام',\n                '%d يومًا',\n                '%d يوم',\n            ],\n            M: [\n                'أقل من شهر',\n                'شهر واحد',\n                ['شهران', 'شهرين'],\n                '%d أشهر',\n                '%d شهرا',\n                '%d شهر',\n            ],\n            y: [\n                'أقل من عام',\n                'عام واحد',\n                ['عامان', 'عامين'],\n                '%d أعوام',\n                '%d عامًا',\n                '%d عام',\n            ],\n        },\n        pluralize = function (u) {\n            return function (number, withoutSuffix, string, isFuture) {\n                var f = pluralForm(number),\n                    str = plurals[u][pluralForm(number)];\n                if (f === 2) {\n                    str = str[withoutSuffix ? 0 : 1];\n                }\n                return str.replace(/%d/i, number);\n            };\n        },\n        months = [\n            'جانفي',\n            'فيفري',\n            'مارس',\n            'أفريل',\n            'ماي',\n            'جوان',\n            'جويلية',\n            'أوت',\n            'سبتمبر',\n            'أكتوبر',\n            'نوفمبر',\n            'ديسمبر',\n        ];\n\n    var arDz = moment.defineLocale('ar-dz', {\n        months: months,\n        monthsShort: months,\n        weekdays: 'الأحد_الإثنين_الثلاثاء_الأربعاء_الخميس_الجمعة_السبت'.split('_'),\n        weekdaysShort: 'أحد_إثنين_ثلاثاء_أربعاء_خميس_جمعة_سبت'.split('_'),\n        weekdaysMin: 'ح_ن_ث_ر_خ_ج_س'.split('_'),\n        weekdaysParseExact: true,\n        longDateFormat: {\n            LT: 'HH:mm',\n            LTS: 'HH:mm:ss',\n            L: 'D/\\u200FM/\\u200FYYYY',\n            LL: 'D MMMM YYYY',\n            LLL: 'D MMMM YYYY HH:mm',\n            LLLL: 'dddd D MMMM YYYY HH:mm',\n        },\n        meridiemParse: /ص|م/,\n        isPM: function (input) {\n            return 'م' === input;\n        },\n        meridiem: function (hour, minute, isLower) {\n            if (hour < 12) {\n                return 'ص';\n            } else {\n                return 'م';\n            }\n        },\n        calendar: {\n            sameDay: '[اليوم عند الساعة] LT',\n            nextDay: '[غدًا عند الساعة] LT',\n            nextWeek: 'dddd [عند الساعة] LT',\n            lastDay: '[أمس عند الساعة] LT',\n            lastWeek: 'dddd [عند الساعة] LT',\n            sameElse: 'L',\n        },\n        relativeTime: {\n            future: 'بعد %s',\n            past: 'منذ %s',\n            s: pluralize('s'),\n            ss: pluralize('s'),\n            m: pluralize('m'),\n            mm: pluralize('m'),\n            h: pluralize('h'),\n            hh: pluralize('h'),\n            d: pluralize('d'),\n            dd: pluralize('d'),\n            M: pluralize('M'),\n            MM: pluralize('M'),\n            y: pluralize('y'),\n            yy: pluralize('y'),\n        },\n        postformat: function (string) {\n            return string.replace(/,/g, '،');\n        },\n        week: {\n            dow: 0, // Sunday is the first day of the week.\n            doy: 4, // The week that contains Jan 4th is the first week of the year.\n        },\n    });\n\n    return arDz;\n\n})));\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AAAE,WAAUA,MAAM,EAAEC,OAAO,EAAE;EAC1B,OAAOC,OAAO,KAAK,QAAQ,IAAI,OAAOC,MAAM,KAAK,WAAW,IACrD,OAAOC,OAAO,KAAK,UAAU,GAAGH,OAAO,CAACG,OAAO,CAAC,WAAW,CAAC,CAAC,GACpE,OAAOC,MAAM,KAAK,UAAU,IAAIA,MAAM,CAACC,GAAG,GAAGD,MAAM,CAAC,CAAC,WAAW,CAAC,EAAEJ,OAAO,CAAC,GAC3EA,OAAO,CAACD,MAAM,CAACO,MAAM,CAAC;AACzB,CAAC,EAAC,IAAI,EAAG,UAAUA,MAAM,EAAE;EAAE,YAAY;;EAErC;EAEA,IAAIC,UAAU,GAAG,SAAAA,CAAUC,CAAC,EAAE;MACtB,OAAOA,CAAC,KAAK,CAAC,GACR,CAAC,GACDA,CAAC,KAAK,CAAC,GACP,CAAC,GACDA,CAAC,KAAK,CAAC,GACP,CAAC,GACDA,CAAC,GAAG,GAAG,IAAI,CAAC,IAAIA,CAAC,GAAG,GAAG,IAAI,EAAE,GAC7B,CAAC,GACDA,CAAC,GAAG,GAAG,IAAI,EAAE,GACb,CAAC,GACD,CAAC;IACX,CAAC;IACDC,OAAO,GAAG;MACNC,CAAC,EAAE,CACC,cAAc,EACd,aAAa,EACb,CAAC,SAAS,EAAE,SAAS,CAAC,EACtB,SAAS,EACT,UAAU,EACV,UAAU,CACb;MACDC,CAAC,EAAE,CACC,cAAc,EACd,aAAa,EACb,CAAC,SAAS,EAAE,SAAS,CAAC,EACtB,UAAU,EACV,UAAU,EACV,UAAU,CACb;MACDC,CAAC,EAAE,CACC,aAAa,EACb,YAAY,EACZ,CAAC,QAAQ,EAAE,QAAQ,CAAC,EACpB,UAAU,EACV,SAAS,EACT,SAAS,CACZ;MACDC,CAAC,EAAE,CACC,YAAY,EACZ,UAAU,EACV,CAAC,OAAO,EAAE,OAAO,CAAC,EAClB,SAAS,EACT,UAAU,EACV,QAAQ,CACX;MACDC,CAAC,EAAE,CACC,YAAY,EACZ,UAAU,EACV,CAAC,OAAO,EAAE,OAAO,CAAC,EAClB,SAAS,EACT,SAAS,EACT,QAAQ,CACX;MACDC,CAAC,EAAE,CACC,YAAY,EACZ,UAAU,EACV,CAAC,OAAO,EAAE,OAAO,CAAC,EAClB,UAAU,EACV,UAAU,EACV,QAAQ;IAEhB,CAAC;IACDC,SAAS,GAAG,SAAAA,CAAUC,CAAC,EAAE;MACrB,OAAO,UAAUC,MAAM,EAAEC,aAAa,EAAEC,MAAM,EAAEC,QAAQ,EAAE;QACtD,IAAIC,CAAC,GAAGf,UAAU,CAACW,MAAM,CAAC;UACtBK,GAAG,GAAGd,OAAO,CAACQ,CAAC,CAAC,CAACV,UAAU,CAACW,MAAM,CAAC,CAAC;QACxC,IAAII,CAAC,KAAK,CAAC,EAAE;UACTC,GAAG,GAAGA,GAAG,CAACJ,aAAa,GAAG,CAAC,GAAG,CAAC,CAAC;QACpC;QACA,OAAOI,GAAG,CAACC,OAAO,CAAC,KAAK,EAAEN,MAAM,CAAC;MACrC,CAAC;IACL,CAAC;IACDO,MAAM,GAAG,CACL,OAAO,EACP,OAAO,EACP,MAAM,EACN,OAAO,EACP,KAAK,EACL,MAAM,EACN,QAAQ,EACR,KAAK,EACL,QAAQ,EACR,QAAQ,EACR,QAAQ,EACR,QAAQ,CACX;EAEL,IAAIC,IAAI,GAAGpB,MAAM,CAACqB,YAAY,CAAC,OAAO,EAAE;IACpCF,MAAM,EAAEA,MAAM;IACdG,WAAW,EAAEH,MAAM;IACnBI,QAAQ,EAAE,qDAAqD,CAACC,KAAK,CAAC,GAAG,CAAC;IAC1EC,aAAa,EAAE,uCAAuC,CAACD,KAAK,CAAC,GAAG,CAAC;IACjEE,WAAW,EAAE,eAAe,CAACF,KAAK,CAAC,GAAG,CAAC;IACvCG,kBAAkB,EAAE,IAAI;IACxBC,cAAc,EAAE;MACZC,EAAE,EAAE,OAAO;MACXC,GAAG,EAAE,UAAU;MACfC,CAAC,EAAE,sBAAsB;MACzBC,EAAE,EAAE,aAAa;MACjBC,GAAG,EAAE,mBAAmB;MACxBC,IAAI,EAAE;IACV,CAAC;IACDC,aAAa,EAAE,KAAK;IACpBC,IAAI,EAAE,SAAAA,CAAUC,KAAK,EAAE;MACnB,OAAO,GAAG,KAAKA,KAAK;IACxB,CAAC;IACDC,QAAQ,EAAE,SAAAA,CAAUC,IAAI,EAAEC,MAAM,EAAEC,OAAO,EAAE;MACvC,IAAIF,IAAI,GAAG,EAAE,EAAE;QACX,OAAO,GAAG;MACd,CAAC,MAAM;QACH,OAAO,GAAG;MACd;IACJ,CAAC;IACDG,QAAQ,EAAE;MACNC,OAAO,EAAE,uBAAuB;MAChCC,OAAO,EAAE,sBAAsB;MAC/BC,QAAQ,EAAE,sBAAsB;MAChCC,OAAO,EAAE,qBAAqB;MAC9BC,QAAQ,EAAE,sBAAsB;MAChCC,QAAQ,EAAE;IACd,CAAC;IACDC,YAAY,EAAE;MACVC,MAAM,EAAE,QAAQ;MAChBC,IAAI,EAAE,QAAQ;MACd/C,CAAC,EAAEM,SAAS,CAAC,GAAG,CAAC;MACjB0C,EAAE,EAAE1C,SAAS,CAAC,GAAG,CAAC;MAClBL,CAAC,EAAEK,SAAS,CAAC,GAAG,CAAC;MACjB2C,EAAE,EAAE3C,SAAS,CAAC,GAAG,CAAC;MAClBJ,CAAC,EAAEI,SAAS,CAAC,GAAG,CAAC;MACjB4C,EAAE,EAAE5C,SAAS,CAAC,GAAG,CAAC;MAClBH,CAAC,EAAEG,SAAS,CAAC,GAAG,CAAC;MACjB6C,EAAE,EAAE7C,SAAS,CAAC,GAAG,CAAC;MAClBF,CAAC,EAAEE,SAAS,CAAC,GAAG,CAAC;MACjB8C,EAAE,EAAE9C,SAAS,CAAC,GAAG,CAAC;MAClBD,CAAC,EAAEC,SAAS,CAAC,GAAG,CAAC;MACjB+C,EAAE,EAAE/C,SAAS,CAAC,GAAG;IACrB,CAAC;IACDgD,UAAU,EAAE,SAAAA,CAAU5C,MAAM,EAAE;MAC1B,OAAOA,MAAM,CAACI,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC;IACpC,CAAC;IACDyC,IAAI,EAAE;MACFC,GAAG,EAAE,CAAC;MAAE;MACRC,GAAG,EAAE,CAAC,CAAE;IACZ;EACJ,CAAC,CAAC;;EAEF,OAAOzC,IAAI;AAEf,CAAE,CAAC"}, "metadata": {}, "sourceType": "script", "externalDependencies": []}