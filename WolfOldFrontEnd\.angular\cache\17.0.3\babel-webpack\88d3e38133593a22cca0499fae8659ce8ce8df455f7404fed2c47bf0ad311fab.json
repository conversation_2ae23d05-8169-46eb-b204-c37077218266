{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\nimport SeriesModel from '../../model/Series.js';\nimport createSeriesData from '../helper/createSeriesData.js';\nimport { each } from 'zrender/lib/core/util.js';\nvar BaseBarSeriesModel = /** @class */\nfunction (_super) {\n  __extends(BaseBarSeriesModel, _super);\n  function BaseBarSeriesModel() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.type = BaseBarSeriesModel.type;\n    return _this;\n  }\n  BaseBarSeriesModel.prototype.getInitialData = function (option, ecModel) {\n    return createSeriesData(null, this, {\n      useEncodeDefaulter: true\n    });\n  };\n  BaseBarSeriesModel.prototype.getMarkerPosition = function (value, dims, startingAtTick) {\n    var coordSys = this.coordinateSystem;\n    if (coordSys && coordSys.clampData) {\n      // PENDING if clamp ?\n      var clampData_1 = coordSys.clampData(value);\n      var pt_1 = coordSys.dataToPoint(clampData_1);\n      if (startingAtTick) {\n        each(coordSys.getAxes(), function (axis, idx) {\n          // If axis type is category, use tick coords instead\n          if (axis.type === 'category' && dims != null) {\n            var tickCoords = axis.getTicksCoords();\n            var targetTickId = clampData_1[idx]; // The index of rightmost tick of markArea is 1 larger than x1/y1 index\n\n            var isEnd = dims[idx] === 'x1' || dims[idx] === 'y1';\n            if (isEnd) {\n              targetTickId += 1;\n            } // The only contains one tick, tickCoords is\n            // like [{coord: 0, tickValue: 0}, {coord: 0}]\n            // to the length should always be larger than 1\n\n            if (tickCoords.length < 2) {\n              return;\n            } else if (tickCoords.length === 2) {\n              // The left value and right value of the axis are\n              // the same. coord is 0 in both items. Use the max\n              // value of the axis as the coord\n              pt_1[idx] = axis.toGlobalCoord(axis.getExtent()[isEnd ? 1 : 0]);\n              return;\n            }\n            var leftCoord = void 0;\n            var coord = void 0;\n            var stepTickValue = 1;\n            for (var i = 0; i < tickCoords.length; i++) {\n              var tickCoord = tickCoords[i].coord; // The last item of tickCoords doesn't contain\n              // tickValue\n\n              var tickValue = i === tickCoords.length - 1 ? tickCoords[i - 1].tickValue + stepTickValue : tickCoords[i].tickValue;\n              if (tickValue === targetTickId) {\n                coord = tickCoord;\n                break;\n              } else if (tickValue < targetTickId) {\n                leftCoord = tickCoord;\n              } else if (leftCoord != null && tickValue > targetTickId) {\n                coord = (tickCoord + leftCoord) / 2;\n                break;\n              }\n              if (i === 1) {\n                // Here we assume the step of category axes is\n                // the same\n                stepTickValue = tickValue - tickCoords[0].tickValue;\n              }\n            }\n            if (coord == null) {\n              if (!leftCoord) {\n                // targetTickId is smaller than all tick ids in the\n                // visible area, use the leftmost tick coord\n                coord = tickCoords[0].coord;\n              } else if (leftCoord) {\n                // targetTickId is larger than all tick ids in the\n                // visible area, use the rightmost tick coord\n                coord = tickCoords[tickCoords.length - 1].coord;\n              }\n            }\n            pt_1[idx] = axis.toGlobalCoord(coord);\n          }\n        });\n      } else {\n        var data = this.getData();\n        var offset = data.getLayout('offset');\n        var size = data.getLayout('size');\n        var offsetIndex = coordSys.getBaseAxis().isHorizontal() ? 0 : 1;\n        pt_1[offsetIndex] += offset + size / 2;\n      }\n      return pt_1;\n    }\n    return [NaN, NaN];\n  };\n  BaseBarSeriesModel.type = 'series.__base_bar__';\n  BaseBarSeriesModel.defaultOption = {\n    // zlevel: 0,\n    z: 2,\n    coordinateSystem: 'cartesian2d',\n    legendHoverLink: true,\n    // stack: null\n    // Cartesian coordinate system\n    // xAxisIndex: 0,\n    // yAxisIndex: 0,\n    barMinHeight: 0,\n    barMinAngle: 0,\n    // cursor: null,\n    large: false,\n    largeThreshold: 400,\n    progressive: 3e3,\n    progressiveChunkMode: 'mod'\n  };\n  return BaseBarSeriesModel;\n}(SeriesModel);\nSeriesModel.registerClass(BaseBarSeriesModel);\nexport default BaseBarSeriesModel;", "map": {"version": 3, "names": ["__extends", "SeriesModel", "createSeriesData", "each", "BaseBarSeriesModel", "_super", "_this", "apply", "arguments", "type", "prototype", "getInitialData", "option", "ecModel", "useEncodeDefaulter", "getMarkerPosition", "value", "dims", "startingAtTick", "coordSys", "coordinateSystem", "clampData", "clampData_1", "pt_1", "dataToPoint", "getAxes", "axis", "idx", "tickCoords", "getTicksCoords", "targetTickId", "isEnd", "length", "toGlobalCoord", "getExtent", "leftCoord", "coord", "step<PERSON><PERSON><PERSON><PERSON><PERSON>", "i", "tickCoord", "tickValue", "data", "getData", "offset", "getLayout", "size", "offsetIndex", "getBaseAxis", "isHorizontal", "NaN", "defaultOption", "z", "legendHoverLink", "barMinHeight", "barMinAngle", "large", "largeThreshold", "progressive", "progressiveChunkMode", "registerClass"], "sources": ["C:/WolfSystemV2/WolfOldFrontEnd/node_modules/echarts/lib/chart/bar/BaseBarSeries.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\nimport SeriesModel from '../../model/Series.js';\nimport createSeriesData from '../helper/createSeriesData.js';\nimport { each } from 'zrender/lib/core/util.js';\n\nvar BaseBarSeriesModel =\n/** @class */\nfunction (_super) {\n  __extends(BaseBarSeriesModel, _super);\n\n  function BaseBarSeriesModel() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n\n    _this.type = BaseBarSeriesModel.type;\n    return _this;\n  }\n\n  BaseBarSeriesModel.prototype.getInitialData = function (option, ecModel) {\n    return createSeriesData(null, this, {\n      useEncodeDefaulter: true\n    });\n  };\n\n  BaseBarSeriesModel.prototype.getMarkerPosition = function (value, dims, startingAtTick) {\n    var coordSys = this.coordinateSystem;\n\n    if (coordSys && coordSys.clampData) {\n      // PENDING if clamp ?\n      var clampData_1 = coordSys.clampData(value);\n      var pt_1 = coordSys.dataToPoint(clampData_1);\n\n      if (startingAtTick) {\n        each(coordSys.getAxes(), function (axis, idx) {\n          // If axis type is category, use tick coords instead\n          if (axis.type === 'category' && dims != null) {\n            var tickCoords = axis.getTicksCoords();\n            var targetTickId = clampData_1[idx]; // The index of rightmost tick of markArea is 1 larger than x1/y1 index\n\n            var isEnd = dims[idx] === 'x1' || dims[idx] === 'y1';\n\n            if (isEnd) {\n              targetTickId += 1;\n            } // The only contains one tick, tickCoords is\n            // like [{coord: 0, tickValue: 0}, {coord: 0}]\n            // to the length should always be larger than 1\n\n\n            if (tickCoords.length < 2) {\n              return;\n            } else if (tickCoords.length === 2) {\n              // The left value and right value of the axis are\n              // the same. coord is 0 in both items. Use the max\n              // value of the axis as the coord\n              pt_1[idx] = axis.toGlobalCoord(axis.getExtent()[isEnd ? 1 : 0]);\n              return;\n            }\n\n            var leftCoord = void 0;\n            var coord = void 0;\n            var stepTickValue = 1;\n\n            for (var i = 0; i < tickCoords.length; i++) {\n              var tickCoord = tickCoords[i].coord; // The last item of tickCoords doesn't contain\n              // tickValue\n\n              var tickValue = i === tickCoords.length - 1 ? tickCoords[i - 1].tickValue + stepTickValue : tickCoords[i].tickValue;\n\n              if (tickValue === targetTickId) {\n                coord = tickCoord;\n                break;\n              } else if (tickValue < targetTickId) {\n                leftCoord = tickCoord;\n              } else if (leftCoord != null && tickValue > targetTickId) {\n                coord = (tickCoord + leftCoord) / 2;\n                break;\n              }\n\n              if (i === 1) {\n                // Here we assume the step of category axes is\n                // the same\n                stepTickValue = tickValue - tickCoords[0].tickValue;\n              }\n            }\n\n            if (coord == null) {\n              if (!leftCoord) {\n                // targetTickId is smaller than all tick ids in the\n                // visible area, use the leftmost tick coord\n                coord = tickCoords[0].coord;\n              } else if (leftCoord) {\n                // targetTickId is larger than all tick ids in the\n                // visible area, use the rightmost tick coord\n                coord = tickCoords[tickCoords.length - 1].coord;\n              }\n            }\n\n            pt_1[idx] = axis.toGlobalCoord(coord);\n          }\n        });\n      } else {\n        var data = this.getData();\n        var offset = data.getLayout('offset');\n        var size = data.getLayout('size');\n        var offsetIndex = coordSys.getBaseAxis().isHorizontal() ? 0 : 1;\n        pt_1[offsetIndex] += offset + size / 2;\n      }\n\n      return pt_1;\n    }\n\n    return [NaN, NaN];\n  };\n\n  BaseBarSeriesModel.type = 'series.__base_bar__';\n  BaseBarSeriesModel.defaultOption = {\n    // zlevel: 0,\n    z: 2,\n    coordinateSystem: 'cartesian2d',\n    legendHoverLink: true,\n    // stack: null\n    // Cartesian coordinate system\n    // xAxisIndex: 0,\n    // yAxisIndex: 0,\n    barMinHeight: 0,\n    barMinAngle: 0,\n    // cursor: null,\n    large: false,\n    largeThreshold: 400,\n    progressive: 3e3,\n    progressiveChunkMode: 'mod'\n  };\n  return BaseBarSeriesModel;\n}(SeriesModel);\n\nSeriesModel.registerClass(BaseBarSeriesModel);\nexport default BaseBarSeriesModel;"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,SAAS,QAAQ,OAAO;AACjC,OAAOC,WAAW,MAAM,uBAAuB;AAC/C,OAAOC,gBAAgB,MAAM,+BAA+B;AAC5D,SAASC,IAAI,QAAQ,0BAA0B;AAE/C,IAAIC,kBAAkB,GACtB;AACA,UAAUC,MAAM,EAAE;EAChBL,SAAS,CAACI,kBAAkB,EAAEC,MAAM,CAAC;EAErC,SAASD,kBAAkBA,CAAA,EAAG;IAC5B,IAAIE,KAAK,GAAGD,MAAM,KAAK,IAAI,IAAIA,MAAM,CAACE,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC,IAAI,IAAI;IAEpEF,KAAK,CAACG,IAAI,GAAGL,kBAAkB,CAACK,IAAI;IACpC,OAAOH,KAAK;EACd;EAEAF,kBAAkB,CAACM,SAAS,CAACC,cAAc,GAAG,UAAUC,MAAM,EAAEC,OAAO,EAAE;IACvE,OAAOX,gBAAgB,CAAC,IAAI,EAAE,IAAI,EAAE;MAClCY,kBAAkB,EAAE;IACtB,CAAC,CAAC;EACJ,CAAC;EAEDV,kBAAkB,CAACM,SAAS,CAACK,iBAAiB,GAAG,UAAUC,KAAK,EAAEC,IAAI,EAAEC,cAAc,EAAE;IACtF,IAAIC,QAAQ,GAAG,IAAI,CAACC,gBAAgB;IAEpC,IAAID,QAAQ,IAAIA,QAAQ,CAACE,SAAS,EAAE;MAClC;MACA,IAAIC,WAAW,GAAGH,QAAQ,CAACE,SAAS,CAACL,KAAK,CAAC;MAC3C,IAAIO,IAAI,GAAGJ,QAAQ,CAACK,WAAW,CAACF,WAAW,CAAC;MAE5C,IAAIJ,cAAc,EAAE;QAClBf,IAAI,CAACgB,QAAQ,CAACM,OAAO,CAAC,CAAC,EAAE,UAAUC,IAAI,EAAEC,GAAG,EAAE;UAC5C;UACA,IAAID,IAAI,CAACjB,IAAI,KAAK,UAAU,IAAIQ,IAAI,IAAI,IAAI,EAAE;YAC5C,IAAIW,UAAU,GAAGF,IAAI,CAACG,cAAc,CAAC,CAAC;YACtC,IAAIC,YAAY,GAAGR,WAAW,CAACK,GAAG,CAAC,CAAC,CAAC;;YAErC,IAAII,KAAK,GAAGd,IAAI,CAACU,GAAG,CAAC,KAAK,IAAI,IAAIV,IAAI,CAACU,GAAG,CAAC,KAAK,IAAI;YAEpD,IAAII,KAAK,EAAE;cACTD,YAAY,IAAI,CAAC;YACnB,CAAC,CAAC;YACF;YACA;;YAGA,IAAIF,UAAU,CAACI,MAAM,GAAG,CAAC,EAAE;cACzB;YACF,CAAC,MAAM,IAAIJ,UAAU,CAACI,MAAM,KAAK,CAAC,EAAE;cAClC;cACA;cACA;cACAT,IAAI,CAACI,GAAG,CAAC,GAAGD,IAAI,CAACO,aAAa,CAACP,IAAI,CAACQ,SAAS,CAAC,CAAC,CAACH,KAAK,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;cAC/D;YACF;YAEA,IAAII,SAAS,GAAG,KAAK,CAAC;YACtB,IAAIC,KAAK,GAAG,KAAK,CAAC;YAClB,IAAIC,aAAa,GAAG,CAAC;YAErB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGV,UAAU,CAACI,MAAM,EAAEM,CAAC,EAAE,EAAE;cAC1C,IAAIC,SAAS,GAAGX,UAAU,CAACU,CAAC,CAAC,CAACF,KAAK,CAAC,CAAC;cACrC;;cAEA,IAAII,SAAS,GAAGF,CAAC,KAAKV,UAAU,CAACI,MAAM,GAAG,CAAC,GAAGJ,UAAU,CAACU,CAAC,GAAG,CAAC,CAAC,CAACE,SAAS,GAAGH,aAAa,GAAGT,UAAU,CAACU,CAAC,CAAC,CAACE,SAAS;cAEnH,IAAIA,SAAS,KAAKV,YAAY,EAAE;gBAC9BM,KAAK,GAAGG,SAAS;gBACjB;cACF,CAAC,MAAM,IAAIC,SAAS,GAAGV,YAAY,EAAE;gBACnCK,SAAS,GAAGI,SAAS;cACvB,CAAC,MAAM,IAAIJ,SAAS,IAAI,IAAI,IAAIK,SAAS,GAAGV,YAAY,EAAE;gBACxDM,KAAK,GAAG,CAACG,SAAS,GAAGJ,SAAS,IAAI,CAAC;gBACnC;cACF;cAEA,IAAIG,CAAC,KAAK,CAAC,EAAE;gBACX;gBACA;gBACAD,aAAa,GAAGG,SAAS,GAAGZ,UAAU,CAAC,CAAC,CAAC,CAACY,SAAS;cACrD;YACF;YAEA,IAAIJ,KAAK,IAAI,IAAI,EAAE;cACjB,IAAI,CAACD,SAAS,EAAE;gBACd;gBACA;gBACAC,KAAK,GAAGR,UAAU,CAAC,CAAC,CAAC,CAACQ,KAAK;cAC7B,CAAC,MAAM,IAAID,SAAS,EAAE;gBACpB;gBACA;gBACAC,KAAK,GAAGR,UAAU,CAACA,UAAU,CAACI,MAAM,GAAG,CAAC,CAAC,CAACI,KAAK;cACjD;YACF;YAEAb,IAAI,CAACI,GAAG,CAAC,GAAGD,IAAI,CAACO,aAAa,CAACG,KAAK,CAAC;UACvC;QACF,CAAC,CAAC;MACJ,CAAC,MAAM;QACL,IAAIK,IAAI,GAAG,IAAI,CAACC,OAAO,CAAC,CAAC;QACzB,IAAIC,MAAM,GAAGF,IAAI,CAACG,SAAS,CAAC,QAAQ,CAAC;QACrC,IAAIC,IAAI,GAAGJ,IAAI,CAACG,SAAS,CAAC,MAAM,CAAC;QACjC,IAAIE,WAAW,GAAG3B,QAAQ,CAAC4B,WAAW,CAAC,CAAC,CAACC,YAAY,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC;QAC/DzB,IAAI,CAACuB,WAAW,CAAC,IAAIH,MAAM,GAAGE,IAAI,GAAG,CAAC;MACxC;MAEA,OAAOtB,IAAI;IACb;IAEA,OAAO,CAAC0B,GAAG,EAAEA,GAAG,CAAC;EACnB,CAAC;EAED7C,kBAAkB,CAACK,IAAI,GAAG,qBAAqB;EAC/CL,kBAAkB,CAAC8C,aAAa,GAAG;IACjC;IACAC,CAAC,EAAE,CAAC;IACJ/B,gBAAgB,EAAE,aAAa;IAC/BgC,eAAe,EAAE,IAAI;IACrB;IACA;IACA;IACA;IACAC,YAAY,EAAE,CAAC;IACfC,WAAW,EAAE,CAAC;IACd;IACAC,KAAK,EAAE,KAAK;IACZC,cAAc,EAAE,GAAG;IACnBC,WAAW,EAAE,GAAG;IAChBC,oBAAoB,EAAE;EACxB,CAAC;EACD,OAAOtD,kBAAkB;AAC3B,CAAC,CAACH,WAAW,CAAC;AAEdA,WAAW,CAAC0D,aAAa,CAACvD,kBAAkB,CAAC;AAC7C,eAAeA,kBAAkB"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}