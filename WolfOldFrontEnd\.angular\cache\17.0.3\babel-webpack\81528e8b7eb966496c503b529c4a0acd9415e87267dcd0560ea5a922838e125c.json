{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { getDecalFromPalette } from '../../model/mixin/palette.js';\nexport default function enableAriaDecalForTree(seriesModel) {\n  var data = seriesModel.getData();\n  var tree = data.tree;\n  var decalPaletteScope = {};\n  tree.eachNode(function (node) {\n    // Use decal of level 1 node\n    var current = node;\n    while (current && current.depth > 1) {\n      current = current.parentNode;\n    }\n    var decal = getDecalFromPalette(seriesModel.ecModel, current.name || current.dataIndex + '', decalPaletteScope);\n    node.setVisual('decal', decal);\n  });\n}", "map": {"version": 3, "names": ["getDecalFromPalette", "enableAriaDecalForTree", "seriesModel", "data", "getData", "tree", "decalPaletteScope", "eachNode", "node", "current", "depth", "parentNode", "decal", "ecModel", "name", "dataIndex", "setVisual"], "sources": ["C:/WolfSystemV2/WolfOldFrontEnd/node_modules/echarts/lib/chart/helper/enableAriaDecalForTree.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { getDecalFromPalette } from '../../model/mixin/palette.js';\nexport default function enableAriaDecalForTree(seriesModel) {\n  var data = seriesModel.getData();\n  var tree = data.tree;\n  var decalPaletteScope = {};\n  tree.eachNode(function (node) {\n    // Use decal of level 1 node\n    var current = node;\n\n    while (current && current.depth > 1) {\n      current = current.parentNode;\n    }\n\n    var decal = getDecalFromPalette(seriesModel.ecModel, current.name || current.dataIndex + '', decalPaletteScope);\n    node.setVisual('decal', decal);\n  });\n}"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,mBAAmB,QAAQ,8BAA8B;AAClE,eAAe,SAASC,sBAAsBA,CAACC,WAAW,EAAE;EAC1D,IAAIC,IAAI,GAAGD,WAAW,CAACE,OAAO,CAAC,CAAC;EAChC,IAAIC,IAAI,GAAGF,IAAI,CAACE,IAAI;EACpB,IAAIC,iBAAiB,GAAG,CAAC,CAAC;EAC1BD,IAAI,CAACE,QAAQ,CAAC,UAAUC,IAAI,EAAE;IAC5B;IACA,IAAIC,OAAO,GAAGD,IAAI;IAElB,OAAOC,OAAO,IAAIA,OAAO,CAACC,KAAK,GAAG,CAAC,EAAE;MACnCD,OAAO,GAAGA,OAAO,CAACE,UAAU;IAC9B;IAEA,IAAIC,KAAK,GAAGZ,mBAAmB,CAACE,WAAW,CAACW,OAAO,EAAEJ,OAAO,CAACK,IAAI,IAAIL,OAAO,CAACM,SAAS,GAAG,EAAE,EAAET,iBAAiB,CAAC;IAC/GE,IAAI,CAACQ,SAAS,CAAC,OAAO,EAAEJ,KAAK,CAAC;EAChC,CAAC,CAAC;AACJ"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}