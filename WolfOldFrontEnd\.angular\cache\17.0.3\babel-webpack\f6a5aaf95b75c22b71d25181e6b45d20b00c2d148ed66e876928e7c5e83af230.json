{"ast": null, "code": "import ListCache from './_ListCache.js';\n\n/**\n * Removes all key-value entries from the stack.\n *\n * @private\n * @name clear\n * @memberOf Stack\n */\nfunction stackClear() {\n  this.__data__ = new ListCache();\n  this.size = 0;\n}\nexport default stackClear;", "map": {"version": 3, "names": ["ListCache", "stackClear", "__data__", "size"], "sources": ["C:/WolfSystemV2/WolfOldFrontEnd/node_modules/lodash-es/_stackClear.js"], "sourcesContent": ["import ListCache from './_ListCache.js';\n\n/**\n * Removes all key-value entries from the stack.\n *\n * @private\n * @name clear\n * @memberOf Stack\n */\nfunction stackClear() {\n  this.__data__ = new ListCache;\n  this.size = 0;\n}\n\nexport default stackClear;\n"], "mappings": "AAAA,OAAOA,SAAS,MAAM,iBAAiB;;AAEvC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,UAAUA,CAAA,EAAG;EACpB,IAAI,CAACC,QAAQ,GAAG,IAAIF,SAAS,CAAD,CAAC;EAC7B,IAAI,CAACG,IAAI,GAAG,CAAC;AACf;AAEA,eAAeF,UAAU"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}