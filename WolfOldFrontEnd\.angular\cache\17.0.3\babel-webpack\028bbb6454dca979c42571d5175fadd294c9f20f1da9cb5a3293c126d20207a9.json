{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\nimport { bind, each, indexOf, curry, extend, normalizeCssArray, isFunction } from 'zrender/lib/core/util.js';\nimport * as graphic from '../../util/graphic.js';\nimport { getECData } from '../../util/innerStore.js';\nimport { isHighDownDispatcher, setAsHighDownDispatcher, setDefaultStateProxy, enableHoverFocus, Z2_EMPHASIS_LIFT } from '../../util/states.js';\nimport DataDiffer from '../../data/DataDiffer.js';\nimport * as helper from '../helper/treeHelper.js';\nimport Breadcrumb from './Breadcrumb.js';\nimport RoamController from '../../component/helper/RoamController.js';\nimport BoundingRect from 'zrender/lib/core/BoundingRect.js';\nimport * as matrix from 'zrender/lib/core/matrix.js';\nimport * as animationUtil from '../../util/animation.js';\nimport makeStyleMapper from '../../model/mixin/makeStyleMapper.js';\nimport ChartView from '../../view/Chart.js';\nimport Displayable from 'zrender/lib/graphic/Displayable.js';\nimport { makeInner, convertOptionIdName } from '../../util/model.js';\nimport { windowOpen } from '../../util/format.js';\nimport { setLabelStyle, getLabelStatesModels } from '../../label/labelStyle.js';\nvar Group = graphic.Group;\nvar Rect = graphic.Rect;\nvar DRAG_THRESHOLD = 3;\nvar PATH_LABEL_NOAMAL = 'label';\nvar PATH_UPPERLABEL_NORMAL = 'upperLabel'; // Should larger than emphasis states lift z\n\nvar Z2_BASE = Z2_EMPHASIS_LIFT * 10; // Should bigger than every z2.\n\nvar Z2_BG = Z2_EMPHASIS_LIFT * 2;\nvar Z2_CONTENT = Z2_EMPHASIS_LIFT * 3;\nvar getStateItemStyle = makeStyleMapper([['fill', 'color'],\n// `borderColor` and `borderWidth` has been occupied,\n// so use `stroke` to indicate the stroke of the rect.\n['stroke', 'strokeColor'], ['lineWidth', 'strokeWidth'], ['shadowBlur'], ['shadowOffsetX'], ['shadowOffsetY'], ['shadowColor'] // Option decal is in `DecalObject` but style.decal is in `PatternObject`.\n// So do not transfer decal directly.\n]);\n\nvar getItemStyleNormal = function (model) {\n  // Normal style props should include emphasis style props.\n  var itemStyle = getStateItemStyle(model); // Clear styles set by emphasis.\n\n  itemStyle.stroke = itemStyle.fill = itemStyle.lineWidth = null;\n  return itemStyle;\n};\nvar inner = makeInner();\nvar TreemapView = /** @class */\nfunction (_super) {\n  __extends(TreemapView, _super);\n  function TreemapView() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.type = TreemapView.type;\n    _this._state = 'ready';\n    _this._storage = createStorage();\n    return _this;\n  }\n  /**\r\n   * @override\r\n   */\n\n  TreemapView.prototype.render = function (seriesModel, ecModel, api, payload) {\n    var models = ecModel.findComponents({\n      mainType: 'series',\n      subType: 'treemap',\n      query: payload\n    });\n    if (indexOf(models, seriesModel) < 0) {\n      return;\n    }\n    this.seriesModel = seriesModel;\n    this.api = api;\n    this.ecModel = ecModel;\n    var types = ['treemapZoomToNode', 'treemapRootToNode'];\n    var targetInfo = helper.retrieveTargetInfo(payload, types, seriesModel);\n    var payloadType = payload && payload.type;\n    var layoutInfo = seriesModel.layoutInfo;\n    var isInit = !this._oldTree;\n    var thisStorage = this._storage; // Mark new root when action is treemapRootToNode.\n\n    var reRoot = payloadType === 'treemapRootToNode' && targetInfo && thisStorage ? {\n      rootNodeGroup: thisStorage.nodeGroup[targetInfo.node.getRawIndex()],\n      direction: payload.direction\n    } : null;\n    var containerGroup = this._giveContainerGroup(layoutInfo);\n    var hasAnimation = seriesModel.get('animation');\n    var renderResult = this._doRender(containerGroup, seriesModel, reRoot);\n    hasAnimation && !isInit && (!payloadType || payloadType === 'treemapZoomToNode' || payloadType === 'treemapRootToNode') ? this._doAnimation(containerGroup, renderResult, seriesModel, reRoot) : renderResult.renderFinally();\n    this._resetController(api);\n    this._renderBreadcrumb(seriesModel, api, targetInfo);\n  };\n  TreemapView.prototype._giveContainerGroup = function (layoutInfo) {\n    var containerGroup = this._containerGroup;\n    if (!containerGroup) {\n      // FIXME\n      // 加一层containerGroup是为了clip，但是现在clip功能并没有实现。\n      containerGroup = this._containerGroup = new Group();\n      this._initEvents(containerGroup);\n      this.group.add(containerGroup);\n    }\n    containerGroup.x = layoutInfo.x;\n    containerGroup.y = layoutInfo.y;\n    return containerGroup;\n  };\n  TreemapView.prototype._doRender = function (containerGroup, seriesModel, reRoot) {\n    var thisTree = seriesModel.getData().tree;\n    var oldTree = this._oldTree; // Clear last shape records.\n\n    var lastsForAnimation = createStorage();\n    var thisStorage = createStorage();\n    var oldStorage = this._storage;\n    var willInvisibleEls = [];\n    function doRenderNode(thisNode, oldNode, parentGroup, depth) {\n      return renderNode(seriesModel, thisStorage, oldStorage, reRoot, lastsForAnimation, willInvisibleEls, thisNode, oldNode, parentGroup, depth);\n    } // Notice: When thisTree and oldTree are the same tree (see list.cloneShallow),\n    // the oldTree is actually losted, so we cannot find all of the old graphic\n    // elements from tree. So we use this strategy: make element storage, move\n    // from old storage to new storage, clear old storage.\n\n    dualTravel(thisTree.root ? [thisTree.root] : [], oldTree && oldTree.root ? [oldTree.root] : [], containerGroup, thisTree === oldTree || !oldTree, 0); // Process all removing.\n\n    var willDeleteEls = clearStorage(oldStorage);\n    this._oldTree = thisTree;\n    this._storage = thisStorage;\n    return {\n      lastsForAnimation: lastsForAnimation,\n      willDeleteEls: willDeleteEls,\n      renderFinally: renderFinally\n    };\n    function dualTravel(thisViewChildren, oldViewChildren, parentGroup, sameTree, depth) {\n      // When 'render' is triggered by action,\n      // 'this' and 'old' may be the same tree,\n      // we use rawIndex in that case.\n      if (sameTree) {\n        oldViewChildren = thisViewChildren;\n        each(thisViewChildren, function (child, index) {\n          !child.isRemoved() && processNode(index, index);\n        });\n      } // Diff hierarchically (diff only in each subtree, but not whole).\n      // because, consistency of view is important.\n      else {\n        new DataDiffer(oldViewChildren, thisViewChildren, getKey, getKey).add(processNode).update(processNode).remove(curry(processNode, null)).execute();\n      }\n      function getKey(node) {\n        // Identify by name or raw index.\n        return node.getId();\n      }\n      function processNode(newIndex, oldIndex) {\n        var thisNode = newIndex != null ? thisViewChildren[newIndex] : null;\n        var oldNode = oldIndex != null ? oldViewChildren[oldIndex] : null;\n        var group = doRenderNode(thisNode, oldNode, parentGroup, depth);\n        group && dualTravel(thisNode && thisNode.viewChildren || [], oldNode && oldNode.viewChildren || [], group, sameTree, depth + 1);\n      }\n    }\n    function clearStorage(storage) {\n      var willDeleteEls = createStorage();\n      storage && each(storage, function (store, storageName) {\n        var delEls = willDeleteEls[storageName];\n        each(store, function (el) {\n          el && (delEls.push(el), inner(el).willDelete = true);\n        });\n      });\n      return willDeleteEls;\n    }\n    function renderFinally() {\n      each(willDeleteEls, function (els) {\n        each(els, function (el) {\n          el.parent && el.parent.remove(el);\n        });\n      });\n      each(willInvisibleEls, function (el) {\n        el.invisible = true; // Setting invisible is for optimizing, so no need to set dirty,\n        // just mark as invisible.\n\n        el.dirty();\n      });\n    }\n  };\n  TreemapView.prototype._doAnimation = function (containerGroup, renderResult, seriesModel, reRoot) {\n    var durationOption = seriesModel.get('animationDurationUpdate');\n    var easingOption = seriesModel.get('animationEasing'); // TODO: do not support function until necessary.\n\n    var duration = (isFunction(durationOption) ? 0 : durationOption) || 0;\n    var easing = (isFunction(easingOption) ? null : easingOption) || 'cubicOut';\n    var animationWrap = animationUtil.createWrap(); // Make delete animations.\n\n    each(renderResult.willDeleteEls, function (store, storageName) {\n      each(store, function (el, rawIndex) {\n        if (el.invisible) {\n          return;\n        }\n        var parent = el.parent; // Always has parent, and parent is nodeGroup.\n\n        var target;\n        var innerStore = inner(parent);\n        if (reRoot && reRoot.direction === 'drillDown') {\n          target = parent === reRoot.rootNodeGroup // This is the content element of view root.\n          // Only `content` will enter this branch, because\n          // `background` and `nodeGroup` will not be deleted.\n          ? {\n            shape: {\n              x: 0,\n              y: 0,\n              width: innerStore.nodeWidth,\n              height: innerStore.nodeHeight\n            },\n            style: {\n              opacity: 0\n            }\n          } // Others.\n          : {\n            style: {\n              opacity: 0\n            }\n          };\n        } else {\n          var targetX = 0;\n          var targetY = 0;\n          if (!innerStore.willDelete) {\n            // Let node animate to right-bottom corner, cooperating with fadeout,\n            // which is appropriate for user understanding.\n            // Divided by 2 for reRoot rolling up effect.\n            targetX = innerStore.nodeWidth / 2;\n            targetY = innerStore.nodeHeight / 2;\n          }\n          target = storageName === 'nodeGroup' ? {\n            x: targetX,\n            y: targetY,\n            style: {\n              opacity: 0\n            }\n          } : {\n            shape: {\n              x: targetX,\n              y: targetY,\n              width: 0,\n              height: 0\n            },\n            style: {\n              opacity: 0\n            }\n          };\n        } // TODO: do not support delay until necessary.\n\n        target && animationWrap.add(el, target, duration, 0, easing);\n      });\n    }); // Make other animations\n\n    each(this._storage, function (store, storageName) {\n      each(store, function (el, rawIndex) {\n        var last = renderResult.lastsForAnimation[storageName][rawIndex];\n        var target = {};\n        if (!last) {\n          return;\n        }\n        if (el instanceof graphic.Group) {\n          if (last.oldX != null) {\n            target.x = el.x;\n            target.y = el.y;\n            el.x = last.oldX;\n            el.y = last.oldY;\n          }\n        } else {\n          if (last.oldShape) {\n            target.shape = extend({}, el.shape);\n            el.setShape(last.oldShape);\n          }\n          if (last.fadein) {\n            el.setStyle('opacity', 0);\n            target.style = {\n              opacity: 1\n            };\n          } // When animation is stopped for succedent animation starting,\n          // el.style.opacity might not be 1\n          else if (el.style.opacity !== 1) {\n            target.style = {\n              opacity: 1\n            };\n          }\n        }\n        animationWrap.add(el, target, duration, 0, easing);\n      });\n    }, this);\n    this._state = 'animating';\n    animationWrap.finished(bind(function () {\n      this._state = 'ready';\n      renderResult.renderFinally();\n    }, this)).start();\n  };\n  TreemapView.prototype._resetController = function (api) {\n    var controller = this._controller; // Init controller.\n\n    if (!controller) {\n      controller = this._controller = new RoamController(api.getZr());\n      controller.enable(this.seriesModel.get('roam'));\n      controller.on('pan', bind(this._onPan, this));\n      controller.on('zoom', bind(this._onZoom, this));\n    }\n    var rect = new BoundingRect(0, 0, api.getWidth(), api.getHeight());\n    controller.setPointerChecker(function (e, x, y) {\n      return rect.contain(x, y);\n    });\n  };\n  TreemapView.prototype._clearController = function () {\n    var controller = this._controller;\n    if (controller) {\n      controller.dispose();\n      controller = null;\n    }\n  };\n  TreemapView.prototype._onPan = function (e) {\n    if (this._state !== 'animating' && (Math.abs(e.dx) > DRAG_THRESHOLD || Math.abs(e.dy) > DRAG_THRESHOLD)) {\n      // These param must not be cached.\n      var root = this.seriesModel.getData().tree.root;\n      if (!root) {\n        return;\n      }\n      var rootLayout = root.getLayout();\n      if (!rootLayout) {\n        return;\n      }\n      this.api.dispatchAction({\n        type: 'treemapMove',\n        from: this.uid,\n        seriesId: this.seriesModel.id,\n        rootRect: {\n          x: rootLayout.x + e.dx,\n          y: rootLayout.y + e.dy,\n          width: rootLayout.width,\n          height: rootLayout.height\n        }\n      });\n    }\n  };\n  TreemapView.prototype._onZoom = function (e) {\n    var mouseX = e.originX;\n    var mouseY = e.originY;\n    if (this._state !== 'animating') {\n      // These param must not be cached.\n      var root = this.seriesModel.getData().tree.root;\n      if (!root) {\n        return;\n      }\n      var rootLayout = root.getLayout();\n      if (!rootLayout) {\n        return;\n      }\n      var rect = new BoundingRect(rootLayout.x, rootLayout.y, rootLayout.width, rootLayout.height);\n      var layoutInfo = this.seriesModel.layoutInfo; // Transform mouse coord from global to containerGroup.\n\n      mouseX -= layoutInfo.x;\n      mouseY -= layoutInfo.y; // Scale root bounding rect.\n\n      var m = matrix.create();\n      matrix.translate(m, m, [-mouseX, -mouseY]);\n      matrix.scale(m, m, [e.scale, e.scale]);\n      matrix.translate(m, m, [mouseX, mouseY]);\n      rect.applyTransform(m);\n      this.api.dispatchAction({\n        type: 'treemapRender',\n        from: this.uid,\n        seriesId: this.seriesModel.id,\n        rootRect: {\n          x: rect.x,\n          y: rect.y,\n          width: rect.width,\n          height: rect.height\n        }\n      });\n    }\n  };\n  TreemapView.prototype._initEvents = function (containerGroup) {\n    var _this = this;\n    containerGroup.on('click', function (e) {\n      if (_this._state !== 'ready') {\n        return;\n      }\n      var nodeClick = _this.seriesModel.get('nodeClick', true);\n      if (!nodeClick) {\n        return;\n      }\n      var targetInfo = _this.findTarget(e.offsetX, e.offsetY);\n      if (!targetInfo) {\n        return;\n      }\n      var node = targetInfo.node;\n      if (node.getLayout().isLeafRoot) {\n        _this._rootToNode(targetInfo);\n      } else {\n        if (nodeClick === 'zoomToNode') {\n          _this._zoomToNode(targetInfo);\n        } else if (nodeClick === 'link') {\n          var itemModel = node.hostTree.data.getItemModel(node.dataIndex);\n          var link = itemModel.get('link', true);\n          var linkTarget = itemModel.get('target', true) || 'blank';\n          link && windowOpen(link, linkTarget);\n        }\n      }\n    }, this);\n  };\n  TreemapView.prototype._renderBreadcrumb = function (seriesModel, api, targetInfo) {\n    var _this = this;\n    if (!targetInfo) {\n      targetInfo = seriesModel.get('leafDepth', true) != null ? {\n        node: seriesModel.getViewRoot()\n      } // FIXME\n      // better way?\n      // Find breadcrumb tail on center of containerGroup.\n      : this.findTarget(api.getWidth() / 2, api.getHeight() / 2);\n      if (!targetInfo) {\n        targetInfo = {\n          node: seriesModel.getData().tree.root\n        };\n      }\n    }\n    (this._breadcrumb || (this._breadcrumb = new Breadcrumb(this.group))).render(seriesModel, api, targetInfo.node, function (node) {\n      if (_this._state !== 'animating') {\n        helper.aboveViewRoot(seriesModel.getViewRoot(), node) ? _this._rootToNode({\n          node: node\n        }) : _this._zoomToNode({\n          node: node\n        });\n      }\n    });\n  };\n  /**\r\n   * @override\r\n   */\n\n  TreemapView.prototype.remove = function () {\n    this._clearController();\n    this._containerGroup && this._containerGroup.removeAll();\n    this._storage = createStorage();\n    this._state = 'ready';\n    this._breadcrumb && this._breadcrumb.remove();\n  };\n  TreemapView.prototype.dispose = function () {\n    this._clearController();\n  };\n  TreemapView.prototype._zoomToNode = function (targetInfo) {\n    this.api.dispatchAction({\n      type: 'treemapZoomToNode',\n      from: this.uid,\n      seriesId: this.seriesModel.id,\n      targetNode: targetInfo.node\n    });\n  };\n  TreemapView.prototype._rootToNode = function (targetInfo) {\n    this.api.dispatchAction({\n      type: 'treemapRootToNode',\n      from: this.uid,\n      seriesId: this.seriesModel.id,\n      targetNode: targetInfo.node\n    });\n  };\n  /**\r\n   * @public\r\n   * @param {number} x Global coord x.\r\n   * @param {number} y Global coord y.\r\n   * @return {Object} info If not found, return undefined;\r\n   * @return {number} info.node Target node.\r\n   * @return {number} info.offsetX x refer to target node.\r\n   * @return {number} info.offsetY y refer to target node.\r\n   */\n\n  TreemapView.prototype.findTarget = function (x, y) {\n    var targetInfo;\n    var viewRoot = this.seriesModel.getViewRoot();\n    viewRoot.eachNode({\n      attr: 'viewChildren',\n      order: 'preorder'\n    }, function (node) {\n      var bgEl = this._storage.background[node.getRawIndex()]; // If invisible, there might be no element.\n\n      if (bgEl) {\n        var point = bgEl.transformCoordToLocal(x, y);\n        var shape = bgEl.shape; // For performance consideration, don't use 'getBoundingRect'.\n\n        if (shape.x <= point[0] && point[0] <= shape.x + shape.width && shape.y <= point[1] && point[1] <= shape.y + shape.height) {\n          targetInfo = {\n            node: node,\n            offsetX: point[0],\n            offsetY: point[1]\n          };\n        } else {\n          return false; // Suppress visit subtree.\n        }\n      }\n    }, this);\n    return targetInfo;\n  };\n  TreemapView.type = 'treemap';\n  return TreemapView;\n}(ChartView);\n/**\r\n * @inner\r\n */\n\nfunction createStorage() {\n  return {\n    nodeGroup: [],\n    background: [],\n    content: []\n  };\n}\n/**\r\n * @inner\r\n * @return Return undefined means do not travel further.\r\n */\n\nfunction renderNode(seriesModel, thisStorage, oldStorage, reRoot, lastsForAnimation, willInvisibleEls, thisNode, oldNode, parentGroup, depth) {\n  // Whether under viewRoot.\n  if (!thisNode) {\n    // Deleting nodes will be performed finally. This method just find\n    // element from old storage, or create new element, set them to new\n    // storage, and set styles.\n    return;\n  } // -------------------------------------------------------------------\n  // Start of closure variables available in \"Procedures in renderNode\".\n\n  var thisLayout = thisNode.getLayout();\n  var data = seriesModel.getData();\n  var nodeModel = thisNode.getModel(); // Only for enabling highlight/downplay. Clear firstly.\n  // Because some node will not be rendered.\n\n  data.setItemGraphicEl(thisNode.dataIndex, null);\n  if (!thisLayout || !thisLayout.isInView) {\n    return;\n  }\n  var thisWidth = thisLayout.width;\n  var thisHeight = thisLayout.height;\n  var borderWidth = thisLayout.borderWidth;\n  var thisInvisible = thisLayout.invisible;\n  var thisRawIndex = thisNode.getRawIndex();\n  var oldRawIndex = oldNode && oldNode.getRawIndex();\n  var thisViewChildren = thisNode.viewChildren;\n  var upperHeight = thisLayout.upperHeight;\n  var isParent = thisViewChildren && thisViewChildren.length;\n  var itemStyleNormalModel = nodeModel.getModel('itemStyle');\n  var itemStyleEmphasisModel = nodeModel.getModel(['emphasis', 'itemStyle']);\n  var itemStyleBlurModel = nodeModel.getModel(['blur', 'itemStyle']);\n  var itemStyleSelectModel = nodeModel.getModel(['select', 'itemStyle']);\n  var borderRadius = itemStyleNormalModel.get('borderRadius') || 0; // End of closure ariables available in \"Procedures in renderNode\".\n  // -----------------------------------------------------------------\n  // Node group\n\n  var group = giveGraphic('nodeGroup', Group);\n  if (!group) {\n    return;\n  }\n  parentGroup.add(group); // x,y are not set when el is above view root.\n\n  group.x = thisLayout.x || 0;\n  group.y = thisLayout.y || 0;\n  group.markRedraw();\n  inner(group).nodeWidth = thisWidth;\n  inner(group).nodeHeight = thisHeight;\n  if (thisLayout.isAboveViewRoot) {\n    return group;\n  } // Background\n\n  var bg = giveGraphic('background', Rect, depth, Z2_BG);\n  bg && renderBackground(group, bg, isParent && thisLayout.upperLabelHeight);\n  var emphasisModel = nodeModel.getModel('emphasis');\n  var focus = emphasisModel.get('focus');\n  var blurScope = emphasisModel.get('blurScope');\n  var isDisabled = emphasisModel.get('disabled');\n  var focusOrIndices = focus === 'ancestor' ? thisNode.getAncestorsIndices() : focus === 'descendant' ? thisNode.getDescendantIndices() : focus; // No children, render content.\n\n  if (isParent) {\n    // Because of the implementation about \"traverse\" in graphic hover style, we\n    // can not set hover listener on the \"group\" of non-leaf node. Otherwise the\n    // hover event from the descendents will be listenered.\n    if (isHighDownDispatcher(group)) {\n      setAsHighDownDispatcher(group, false);\n    }\n    if (bg) {\n      setAsHighDownDispatcher(bg, !isDisabled); // Only for enabling highlight/downplay.\n\n      data.setItemGraphicEl(thisNode.dataIndex, bg);\n      enableHoverFocus(bg, focusOrIndices, blurScope);\n    }\n  } else {\n    var content = giveGraphic('content', Rect, depth, Z2_CONTENT);\n    content && renderContent(group, content);\n    bg.disableMorphing = true;\n    if (bg && isHighDownDispatcher(bg)) {\n      setAsHighDownDispatcher(bg, false);\n    }\n    setAsHighDownDispatcher(group, !isDisabled); // Only for enabling highlight/downplay.\n\n    data.setItemGraphicEl(thisNode.dataIndex, group);\n    enableHoverFocus(group, focusOrIndices, blurScope);\n  }\n  return group; // ----------------------------\n  // | Procedures in renderNode |\n  // ----------------------------\n\n  function renderBackground(group, bg, useUpperLabel) {\n    var ecData = getECData(bg); // For tooltip.\n\n    ecData.dataIndex = thisNode.dataIndex;\n    ecData.seriesIndex = seriesModel.seriesIndex;\n    bg.setShape({\n      x: 0,\n      y: 0,\n      width: thisWidth,\n      height: thisHeight,\n      r: borderRadius\n    });\n    if (thisInvisible) {\n      // If invisible, do not set visual, otherwise the element will\n      // change immediately before animation. We think it is OK to\n      // remain its origin color when moving out of the view window.\n      processInvisible(bg);\n    } else {\n      bg.invisible = false;\n      var style = thisNode.getVisual('style');\n      var visualBorderColor = style.stroke;\n      var normalStyle = getItemStyleNormal(itemStyleNormalModel);\n      normalStyle.fill = visualBorderColor;\n      var emphasisStyle = getStateItemStyle(itemStyleEmphasisModel);\n      emphasisStyle.fill = itemStyleEmphasisModel.get('borderColor');\n      var blurStyle = getStateItemStyle(itemStyleBlurModel);\n      blurStyle.fill = itemStyleBlurModel.get('borderColor');\n      var selectStyle = getStateItemStyle(itemStyleSelectModel);\n      selectStyle.fill = itemStyleSelectModel.get('borderColor');\n      if (useUpperLabel) {\n        var upperLabelWidth = thisWidth - 2 * borderWidth;\n        prepareText(\n        // PENDING: convert ZRColor to ColorString for text.\n        bg, visualBorderColor, style.opacity, {\n          x: borderWidth,\n          y: 0,\n          width: upperLabelWidth,\n          height: upperHeight\n        });\n      } // For old bg.\n      else {\n        bg.removeTextContent();\n      }\n      bg.setStyle(normalStyle);\n      bg.ensureState('emphasis').style = emphasisStyle;\n      bg.ensureState('blur').style = blurStyle;\n      bg.ensureState('select').style = selectStyle;\n      setDefaultStateProxy(bg);\n    }\n    group.add(bg);\n  }\n  function renderContent(group, content) {\n    var ecData = getECData(content); // For tooltip.\n\n    ecData.dataIndex = thisNode.dataIndex;\n    ecData.seriesIndex = seriesModel.seriesIndex;\n    var contentWidth = Math.max(thisWidth - 2 * borderWidth, 0);\n    var contentHeight = Math.max(thisHeight - 2 * borderWidth, 0);\n    content.culling = true;\n    content.setShape({\n      x: borderWidth,\n      y: borderWidth,\n      width: contentWidth,\n      height: contentHeight,\n      r: borderRadius\n    });\n    if (thisInvisible) {\n      // If invisible, do not set visual, otherwise the element will\n      // change immediately before animation. We think it is OK to\n      // remain its origin color when moving out of the view window.\n      processInvisible(content);\n    } else {\n      content.invisible = false;\n      var nodeStyle = thisNode.getVisual('style');\n      var visualColor = nodeStyle.fill;\n      var normalStyle = getItemStyleNormal(itemStyleNormalModel);\n      normalStyle.fill = visualColor;\n      normalStyle.decal = nodeStyle.decal;\n      var emphasisStyle = getStateItemStyle(itemStyleEmphasisModel);\n      var blurStyle = getStateItemStyle(itemStyleBlurModel);\n      var selectStyle = getStateItemStyle(itemStyleSelectModel); // PENDING: convert ZRColor to ColorString for text.\n\n      prepareText(content, visualColor, nodeStyle.opacity, null);\n      content.setStyle(normalStyle);\n      content.ensureState('emphasis').style = emphasisStyle;\n      content.ensureState('blur').style = blurStyle;\n      content.ensureState('select').style = selectStyle;\n      setDefaultStateProxy(content);\n    }\n    group.add(content);\n  }\n  function processInvisible(element) {\n    // Delay invisible setting utill animation finished,\n    // avoid element vanish suddenly before animation.\n    !element.invisible && willInvisibleEls.push(element);\n  }\n  function prepareText(rectEl, visualColor, visualOpacity,\n  // Can be null/undefined\n  upperLabelRect) {\n    var normalLabelModel = nodeModel.getModel(upperLabelRect ? PATH_UPPERLABEL_NORMAL : PATH_LABEL_NOAMAL);\n    var defaultText = convertOptionIdName(nodeModel.get('name'), null);\n    var isShow = normalLabelModel.getShallow('show');\n    setLabelStyle(rectEl, getLabelStatesModels(nodeModel, upperLabelRect ? PATH_UPPERLABEL_NORMAL : PATH_LABEL_NOAMAL), {\n      defaultText: isShow ? defaultText : null,\n      inheritColor: visualColor,\n      defaultOpacity: visualOpacity,\n      labelFetcher: seriesModel,\n      labelDataIndex: thisNode.dataIndex\n    });\n    var textEl = rectEl.getTextContent();\n    if (!textEl) {\n      return;\n    }\n    var textStyle = textEl.style;\n    var textPadding = normalizeCssArray(textStyle.padding || 0);\n    if (upperLabelRect) {\n      rectEl.setTextConfig({\n        layoutRect: upperLabelRect\n      });\n      textEl.disableLabelLayout = true;\n    }\n    textEl.beforeUpdate = function () {\n      var width = Math.max((upperLabelRect ? upperLabelRect.width : rectEl.shape.width) - textPadding[1] - textPadding[3], 0);\n      var height = Math.max((upperLabelRect ? upperLabelRect.height : rectEl.shape.height) - textPadding[0] - textPadding[2], 0);\n      if (textStyle.width !== width || textStyle.height !== height) {\n        textEl.setStyle({\n          width: width,\n          height: height\n        });\n      }\n    };\n    textStyle.truncateMinChar = 2;\n    textStyle.lineOverflow = 'truncate';\n    addDrillDownIcon(textStyle, upperLabelRect, thisLayout);\n    var textEmphasisState = textEl.getState('emphasis');\n    addDrillDownIcon(textEmphasisState ? textEmphasisState.style : null, upperLabelRect, thisLayout);\n  }\n  function addDrillDownIcon(style, upperLabelRect, thisLayout) {\n    var text = style ? style.text : null;\n    if (!upperLabelRect && thisLayout.isLeafRoot && text != null) {\n      var iconChar = seriesModel.get('drillDownIcon', true);\n      style.text = iconChar ? iconChar + ' ' + text : text;\n    }\n  }\n  function giveGraphic(storageName, Ctor, depth, z) {\n    var element = oldRawIndex != null && oldStorage[storageName][oldRawIndex];\n    var lasts = lastsForAnimation[storageName];\n    if (element) {\n      // Remove from oldStorage\n      oldStorage[storageName][oldRawIndex] = null;\n      prepareAnimationWhenHasOld(lasts, element);\n    } // If invisible and no old element, do not create new element (for optimizing).\n    else if (!thisInvisible) {\n      element = new Ctor();\n      if (element instanceof Displayable) {\n        element.z2 = calculateZ2(depth, z);\n      }\n      prepareAnimationWhenNoOld(lasts, element);\n    } // Set to thisStorage\n\n    return thisStorage[storageName][thisRawIndex] = element;\n  }\n  function prepareAnimationWhenHasOld(lasts, element) {\n    var lastCfg = lasts[thisRawIndex] = {};\n    if (element instanceof Group) {\n      lastCfg.oldX = element.x;\n      lastCfg.oldY = element.y;\n    } else {\n      lastCfg.oldShape = extend({}, element.shape);\n    }\n  } // If a element is new, we need to find the animation start point carefully,\n  // otherwise it will looks strange when 'zoomToNode'.\n\n  function prepareAnimationWhenNoOld(lasts, element) {\n    var lastCfg = lasts[thisRawIndex] = {};\n    var parentNode = thisNode.parentNode;\n    var isGroup = element instanceof graphic.Group;\n    if (parentNode && (!reRoot || reRoot.direction === 'drillDown')) {\n      var parentOldX = 0;\n      var parentOldY = 0; // New nodes appear from right-bottom corner in 'zoomToNode' animation.\n      // For convenience, get old bounding rect from background.\n\n      var parentOldBg = lastsForAnimation.background[parentNode.getRawIndex()];\n      if (!reRoot && parentOldBg && parentOldBg.oldShape) {\n        parentOldX = parentOldBg.oldShape.width;\n        parentOldY = parentOldBg.oldShape.height;\n      } // When no parent old shape found, its parent is new too,\n      // so we can just use {x:0, y:0}.\n\n      if (isGroup) {\n        lastCfg.oldX = 0;\n        lastCfg.oldY = parentOldY;\n      } else {\n        lastCfg.oldShape = {\n          x: parentOldX,\n          y: parentOldY,\n          width: 0,\n          height: 0\n        };\n      }\n    } // Fade in, user can be aware that these nodes are new.\n\n    lastCfg.fadein = !isGroup;\n  }\n} // We cannot set all background with the same z, because the behaviour of\n// drill down and roll up differ background creation sequence from tree\n// hierarchy sequence, which cause lower background elements to overlap\n// upper ones. So we calculate z based on depth.\n// Moreover, we try to shrink down z interval to [0, 1] to avoid that\n// treemap with large z overlaps other components.\n\nfunction calculateZ2(depth, z2InLevel) {\n  return depth * Z2_BASE + z2InLevel;\n}\nexport default TreemapView;", "map": {"version": 3, "names": ["__extends", "bind", "each", "indexOf", "curry", "extend", "normalizeCssArray", "isFunction", "graphic", "getECData", "is<PERSON>ighD<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "setAs<PERSON>ighDownD<PERSON><PERSON><PERSON><PERSON>", "setDefaultStateProxy", "enableHoverFocus", "Z2_EMPHASIS_LIFT", "<PERSON><PERSON><PERSON><PERSON>", "helper", "Breadcrumb", "RoamController", "BoundingRect", "matrix", "animationUtil", "makeStyleMapper", "ChartView", "Displayable", "makeInner", "convertOptionIdName", "windowOpen", "setLabelStyle", "getLabelStatesModels", "Group", "Rect", "DRAG_THRESHOLD", "PATH_LABEL_NOAMAL", "PATH_UPPERLABEL_NORMAL", "Z2_BASE", "Z2_BG", "Z2_CONTENT", "getStateItemStyle", "getItemStyleNormal", "model", "itemStyle", "stroke", "fill", "lineWidth", "inner", "TreemapView", "_super", "_this", "apply", "arguments", "type", "_state", "_storage", "createStorage", "prototype", "render", "seriesModel", "ecModel", "api", "payload", "models", "findComponents", "mainType", "subType", "query", "types", "targetInfo", "retrieveTargetInfo", "payloadType", "layoutInfo", "isInit", "_old<PERSON><PERSON>", "thisStorage", "reRoot", "rootNodeGroup", "nodeGroup", "node", "getRawIndex", "direction", "containerGroup", "_giveContainerGroup", "hasAnimation", "get", "renderResult", "_doRender", "_doAnimation", "renderFinally", "_resetController", "_renderBreadcrumb", "_containerGroup", "_initEvents", "group", "add", "x", "y", "thisTree", "getData", "tree", "oldTree", "lastsForAnimation", "oldStorage", "willInvisibleEls", "doRenderNode", "thisNode", "oldNode", "parentGroup", "depth", "renderNode", "dualTravel", "root", "willDeleteEls", "clearStorage", "this<PERSON>iew<PERSON><PERSON><PERSON>n", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sameTree", "child", "index", "isRemoved", "processNode", "<PERSON><PERSON><PERSON>", "update", "remove", "execute", "getId", "newIndex", "oldIndex", "viewChil<PERSON>n", "storage", "store", "storageName", "delEls", "el", "push", "will<PERSON>elete", "els", "parent", "invisible", "dirty", "durationOption", "easingOption", "duration", "easing", "animationWrap", "createWrap", "rawIndex", "target", "innerStore", "shape", "width", "nodeWidth", "height", "nodeHeight", "style", "opacity", "targetX", "targetY", "last", "oldX", "oldY", "oldShape", "setShape", "fadein", "setStyle", "finished", "start", "controller", "_controller", "getZr", "enable", "on", "_on<PERSON>an", "_onZoom", "rect", "getWidth", "getHeight", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "e", "contain", "_clearController", "dispose", "Math", "abs", "dx", "dy", "rootLayout", "getLayout", "dispatchAction", "from", "uid", "seriesId", "id", "rootRect", "mouseX", "originX", "mouseY", "originY", "m", "create", "translate", "scale", "applyTransform", "nodeClick", "<PERSON><PERSON><PERSON><PERSON>", "offsetX", "offsetY", "isLeafRoot", "_rootToNode", "_zoomToNode", "itemModel", "hostTree", "data", "getItemModel", "dataIndex", "link", "linkTarget", "getViewRoot", "_breadcrumb", "aboveViewRoot", "removeAll", "targetNode", "viewRoot", "eachNode", "attr", "order", "bgEl", "background", "point", "transformCoordToLocal", "content", "thisLayout", "nodeModel", "getModel", "setItemGraphicEl", "isInView", "thisWidth", "thisHeight", "borderWidth", "thisInvisible", "thisRawIndex", "oldRawIndex", "upperHeight", "isParent", "length", "itemStyleNormalModel", "itemStyleEmphasisModel", "itemStyleBlurModel", "itemStyleSelectModel", "borderRadius", "giveGraphic", "mark<PERSON><PERSON><PERSON>", "isAboveViewRoot", "bg", "renderBackground", "upperLabelHeight", "emphasisModel", "focus", "blurScope", "isDisabled", "focusOrIndices", "getAncestorsIndices", "getDescendantIndices", "renderContent", "disableMorphing", "useUpperLabel", "ecData", "seriesIndex", "r", "processInvisible", "getVisual", "visualBorderColor", "normalStyle", "emphasisStyle", "blurStyle", "selectStyle", "upperLabelWidth", "prepareText", "removeTextContent", "ensureState", "contentWidth", "max", "contentHeight", "culling", "nodeStyle", "visualColor", "decal", "element", "rectEl", "visualOpacity", "upperLabelRect", "normalLabelModel", "defaultText", "isShow", "getShallow", "inheritColor", "defaultOpacity", "labelFetcher", "labelDataIndex", "textEl", "getTextContent", "textStyle", "textPadding", "padding", "setTextConfig", "layoutRect", "disableLabelLayout", "beforeUpdate", "truncateMinChar", "lineOverflow", "addDrillDownIcon", "textEmphasisState", "getState", "text", "iconChar", "Ctor", "z", "lasts", "prepareAnimationWhenHasOld", "z2", "calculateZ2", "prepareAnimationWhenNoOld", "lastCfg", "parentNode", "isGroup", "parentOldX", "parentOldY", "parentOldBg", "z2InLevel"], "sources": ["C:/WolfSystemV2/WolfOldFrontEnd/node_modules/echarts/lib/chart/treemap/TreemapView.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\nimport { bind, each, indexOf, curry, extend, normalizeCssArray, isFunction } from 'zrender/lib/core/util.js';\nimport * as graphic from '../../util/graphic.js';\nimport { getECData } from '../../util/innerStore.js';\nimport { isHighDownDispatcher, setAsHighDownDispatcher, setDefaultStateProxy, enableHoverFocus, Z2_EMPHASIS_LIFT } from '../../util/states.js';\nimport DataDiffer from '../../data/DataDiffer.js';\nimport * as helper from '../helper/treeHelper.js';\nimport Breadcrumb from './Breadcrumb.js';\nimport RoamController from '../../component/helper/RoamController.js';\nimport BoundingRect from 'zrender/lib/core/BoundingRect.js';\nimport * as matrix from 'zrender/lib/core/matrix.js';\nimport * as animationUtil from '../../util/animation.js';\nimport makeStyleMapper from '../../model/mixin/makeStyleMapper.js';\nimport ChartView from '../../view/Chart.js';\nimport Displayable from 'zrender/lib/graphic/Displayable.js';\nimport { makeInner, convertOptionIdName } from '../../util/model.js';\nimport { windowOpen } from '../../util/format.js';\nimport { setLabelStyle, getLabelStatesModels } from '../../label/labelStyle.js';\nvar Group = graphic.Group;\nvar Rect = graphic.Rect;\nvar DRAG_THRESHOLD = 3;\nvar PATH_LABEL_NOAMAL = 'label';\nvar PATH_UPPERLABEL_NORMAL = 'upperLabel'; // Should larger than emphasis states lift z\n\nvar Z2_BASE = Z2_EMPHASIS_LIFT * 10; // Should bigger than every z2.\n\nvar Z2_BG = Z2_EMPHASIS_LIFT * 2;\nvar Z2_CONTENT = Z2_EMPHASIS_LIFT * 3;\nvar getStateItemStyle = makeStyleMapper([['fill', 'color'], // `borderColor` and `borderWidth` has been occupied,\n// so use `stroke` to indicate the stroke of the rect.\n['stroke', 'strokeColor'], ['lineWidth', 'strokeWidth'], ['shadowBlur'], ['shadowOffsetX'], ['shadowOffsetY'], ['shadowColor'] // Option decal is in `DecalObject` but style.decal is in `PatternObject`.\n// So do not transfer decal directly.\n]);\n\nvar getItemStyleNormal = function (model) {\n  // Normal style props should include emphasis style props.\n  var itemStyle = getStateItemStyle(model); // Clear styles set by emphasis.\n\n  itemStyle.stroke = itemStyle.fill = itemStyle.lineWidth = null;\n  return itemStyle;\n};\n\nvar inner = makeInner();\n\nvar TreemapView =\n/** @class */\nfunction (_super) {\n  __extends(TreemapView, _super);\n\n  function TreemapView() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n\n    _this.type = TreemapView.type;\n    _this._state = 'ready';\n    _this._storage = createStorage();\n    return _this;\n  }\n  /**\r\n   * @override\r\n   */\n\n\n  TreemapView.prototype.render = function (seriesModel, ecModel, api, payload) {\n    var models = ecModel.findComponents({\n      mainType: 'series',\n      subType: 'treemap',\n      query: payload\n    });\n\n    if (indexOf(models, seriesModel) < 0) {\n      return;\n    }\n\n    this.seriesModel = seriesModel;\n    this.api = api;\n    this.ecModel = ecModel;\n    var types = ['treemapZoomToNode', 'treemapRootToNode'];\n    var targetInfo = helper.retrieveTargetInfo(payload, types, seriesModel);\n    var payloadType = payload && payload.type;\n    var layoutInfo = seriesModel.layoutInfo;\n    var isInit = !this._oldTree;\n    var thisStorage = this._storage; // Mark new root when action is treemapRootToNode.\n\n    var reRoot = payloadType === 'treemapRootToNode' && targetInfo && thisStorage ? {\n      rootNodeGroup: thisStorage.nodeGroup[targetInfo.node.getRawIndex()],\n      direction: payload.direction\n    } : null;\n\n    var containerGroup = this._giveContainerGroup(layoutInfo);\n\n    var hasAnimation = seriesModel.get('animation');\n\n    var renderResult = this._doRender(containerGroup, seriesModel, reRoot);\n\n    hasAnimation && !isInit && (!payloadType || payloadType === 'treemapZoomToNode' || payloadType === 'treemapRootToNode') ? this._doAnimation(containerGroup, renderResult, seriesModel, reRoot) : renderResult.renderFinally();\n\n    this._resetController(api);\n\n    this._renderBreadcrumb(seriesModel, api, targetInfo);\n  };\n\n  TreemapView.prototype._giveContainerGroup = function (layoutInfo) {\n    var containerGroup = this._containerGroup;\n\n    if (!containerGroup) {\n      // FIXME\n      // 加一层containerGroup是为了clip，但是现在clip功能并没有实现。\n      containerGroup = this._containerGroup = new Group();\n\n      this._initEvents(containerGroup);\n\n      this.group.add(containerGroup);\n    }\n\n    containerGroup.x = layoutInfo.x;\n    containerGroup.y = layoutInfo.y;\n    return containerGroup;\n  };\n\n  TreemapView.prototype._doRender = function (containerGroup, seriesModel, reRoot) {\n    var thisTree = seriesModel.getData().tree;\n    var oldTree = this._oldTree; // Clear last shape records.\n\n    var lastsForAnimation = createStorage();\n    var thisStorage = createStorage();\n    var oldStorage = this._storage;\n    var willInvisibleEls = [];\n\n    function doRenderNode(thisNode, oldNode, parentGroup, depth) {\n      return renderNode(seriesModel, thisStorage, oldStorage, reRoot, lastsForAnimation, willInvisibleEls, thisNode, oldNode, parentGroup, depth);\n    } // Notice: When thisTree and oldTree are the same tree (see list.cloneShallow),\n    // the oldTree is actually losted, so we cannot find all of the old graphic\n    // elements from tree. So we use this strategy: make element storage, move\n    // from old storage to new storage, clear old storage.\n\n\n    dualTravel(thisTree.root ? [thisTree.root] : [], oldTree && oldTree.root ? [oldTree.root] : [], containerGroup, thisTree === oldTree || !oldTree, 0); // Process all removing.\n\n    var willDeleteEls = clearStorage(oldStorage);\n    this._oldTree = thisTree;\n    this._storage = thisStorage;\n    return {\n      lastsForAnimation: lastsForAnimation,\n      willDeleteEls: willDeleteEls,\n      renderFinally: renderFinally\n    };\n\n    function dualTravel(thisViewChildren, oldViewChildren, parentGroup, sameTree, depth) {\n      // When 'render' is triggered by action,\n      // 'this' and 'old' may be the same tree,\n      // we use rawIndex in that case.\n      if (sameTree) {\n        oldViewChildren = thisViewChildren;\n        each(thisViewChildren, function (child, index) {\n          !child.isRemoved() && processNode(index, index);\n        });\n      } // Diff hierarchically (diff only in each subtree, but not whole).\n      // because, consistency of view is important.\n      else {\n          new DataDiffer(oldViewChildren, thisViewChildren, getKey, getKey).add(processNode).update(processNode).remove(curry(processNode, null)).execute();\n        }\n\n      function getKey(node) {\n        // Identify by name or raw index.\n        return node.getId();\n      }\n\n      function processNode(newIndex, oldIndex) {\n        var thisNode = newIndex != null ? thisViewChildren[newIndex] : null;\n        var oldNode = oldIndex != null ? oldViewChildren[oldIndex] : null;\n        var group = doRenderNode(thisNode, oldNode, parentGroup, depth);\n        group && dualTravel(thisNode && thisNode.viewChildren || [], oldNode && oldNode.viewChildren || [], group, sameTree, depth + 1);\n      }\n    }\n\n    function clearStorage(storage) {\n      var willDeleteEls = createStorage();\n      storage && each(storage, function (store, storageName) {\n        var delEls = willDeleteEls[storageName];\n        each(store, function (el) {\n          el && (delEls.push(el), inner(el).willDelete = true);\n        });\n      });\n      return willDeleteEls;\n    }\n\n    function renderFinally() {\n      each(willDeleteEls, function (els) {\n        each(els, function (el) {\n          el.parent && el.parent.remove(el);\n        });\n      });\n      each(willInvisibleEls, function (el) {\n        el.invisible = true; // Setting invisible is for optimizing, so no need to set dirty,\n        // just mark as invisible.\n\n        el.dirty();\n      });\n    }\n  };\n\n  TreemapView.prototype._doAnimation = function (containerGroup, renderResult, seriesModel, reRoot) {\n    var durationOption = seriesModel.get('animationDurationUpdate');\n    var easingOption = seriesModel.get('animationEasing'); // TODO: do not support function until necessary.\n\n    var duration = (isFunction(durationOption) ? 0 : durationOption) || 0;\n    var easing = (isFunction(easingOption) ? null : easingOption) || 'cubicOut';\n    var animationWrap = animationUtil.createWrap(); // Make delete animations.\n\n    each(renderResult.willDeleteEls, function (store, storageName) {\n      each(store, function (el, rawIndex) {\n        if (el.invisible) {\n          return;\n        }\n\n        var parent = el.parent; // Always has parent, and parent is nodeGroup.\n\n        var target;\n        var innerStore = inner(parent);\n\n        if (reRoot && reRoot.direction === 'drillDown') {\n          target = parent === reRoot.rootNodeGroup // This is the content element of view root.\n          // Only `content` will enter this branch, because\n          // `background` and `nodeGroup` will not be deleted.\n          ? {\n            shape: {\n              x: 0,\n              y: 0,\n              width: innerStore.nodeWidth,\n              height: innerStore.nodeHeight\n            },\n            style: {\n              opacity: 0\n            }\n          } // Others.\n          : {\n            style: {\n              opacity: 0\n            }\n          };\n        } else {\n          var targetX = 0;\n          var targetY = 0;\n\n          if (!innerStore.willDelete) {\n            // Let node animate to right-bottom corner, cooperating with fadeout,\n            // which is appropriate for user understanding.\n            // Divided by 2 for reRoot rolling up effect.\n            targetX = innerStore.nodeWidth / 2;\n            targetY = innerStore.nodeHeight / 2;\n          }\n\n          target = storageName === 'nodeGroup' ? {\n            x: targetX,\n            y: targetY,\n            style: {\n              opacity: 0\n            }\n          } : {\n            shape: {\n              x: targetX,\n              y: targetY,\n              width: 0,\n              height: 0\n            },\n            style: {\n              opacity: 0\n            }\n          };\n        } // TODO: do not support delay until necessary.\n\n\n        target && animationWrap.add(el, target, duration, 0, easing);\n      });\n    }); // Make other animations\n\n    each(this._storage, function (store, storageName) {\n      each(store, function (el, rawIndex) {\n        var last = renderResult.lastsForAnimation[storageName][rawIndex];\n        var target = {};\n\n        if (!last) {\n          return;\n        }\n\n        if (el instanceof graphic.Group) {\n          if (last.oldX != null) {\n            target.x = el.x;\n            target.y = el.y;\n            el.x = last.oldX;\n            el.y = last.oldY;\n          }\n        } else {\n          if (last.oldShape) {\n            target.shape = extend({}, el.shape);\n            el.setShape(last.oldShape);\n          }\n\n          if (last.fadein) {\n            el.setStyle('opacity', 0);\n            target.style = {\n              opacity: 1\n            };\n          } // When animation is stopped for succedent animation starting,\n          // el.style.opacity might not be 1\n          else if (el.style.opacity !== 1) {\n              target.style = {\n                opacity: 1\n              };\n            }\n        }\n\n        animationWrap.add(el, target, duration, 0, easing);\n      });\n    }, this);\n    this._state = 'animating';\n    animationWrap.finished(bind(function () {\n      this._state = 'ready';\n      renderResult.renderFinally();\n    }, this)).start();\n  };\n\n  TreemapView.prototype._resetController = function (api) {\n    var controller = this._controller; // Init controller.\n\n    if (!controller) {\n      controller = this._controller = new RoamController(api.getZr());\n      controller.enable(this.seriesModel.get('roam'));\n      controller.on('pan', bind(this._onPan, this));\n      controller.on('zoom', bind(this._onZoom, this));\n    }\n\n    var rect = new BoundingRect(0, 0, api.getWidth(), api.getHeight());\n    controller.setPointerChecker(function (e, x, y) {\n      return rect.contain(x, y);\n    });\n  };\n\n  TreemapView.prototype._clearController = function () {\n    var controller = this._controller;\n\n    if (controller) {\n      controller.dispose();\n      controller = null;\n    }\n  };\n\n  TreemapView.prototype._onPan = function (e) {\n    if (this._state !== 'animating' && (Math.abs(e.dx) > DRAG_THRESHOLD || Math.abs(e.dy) > DRAG_THRESHOLD)) {\n      // These param must not be cached.\n      var root = this.seriesModel.getData().tree.root;\n\n      if (!root) {\n        return;\n      }\n\n      var rootLayout = root.getLayout();\n\n      if (!rootLayout) {\n        return;\n      }\n\n      this.api.dispatchAction({\n        type: 'treemapMove',\n        from: this.uid,\n        seriesId: this.seriesModel.id,\n        rootRect: {\n          x: rootLayout.x + e.dx,\n          y: rootLayout.y + e.dy,\n          width: rootLayout.width,\n          height: rootLayout.height\n        }\n      });\n    }\n  };\n\n  TreemapView.prototype._onZoom = function (e) {\n    var mouseX = e.originX;\n    var mouseY = e.originY;\n\n    if (this._state !== 'animating') {\n      // These param must not be cached.\n      var root = this.seriesModel.getData().tree.root;\n\n      if (!root) {\n        return;\n      }\n\n      var rootLayout = root.getLayout();\n\n      if (!rootLayout) {\n        return;\n      }\n\n      var rect = new BoundingRect(rootLayout.x, rootLayout.y, rootLayout.width, rootLayout.height);\n      var layoutInfo = this.seriesModel.layoutInfo; // Transform mouse coord from global to containerGroup.\n\n      mouseX -= layoutInfo.x;\n      mouseY -= layoutInfo.y; // Scale root bounding rect.\n\n      var m = matrix.create();\n      matrix.translate(m, m, [-mouseX, -mouseY]);\n      matrix.scale(m, m, [e.scale, e.scale]);\n      matrix.translate(m, m, [mouseX, mouseY]);\n      rect.applyTransform(m);\n      this.api.dispatchAction({\n        type: 'treemapRender',\n        from: this.uid,\n        seriesId: this.seriesModel.id,\n        rootRect: {\n          x: rect.x,\n          y: rect.y,\n          width: rect.width,\n          height: rect.height\n        }\n      });\n    }\n  };\n\n  TreemapView.prototype._initEvents = function (containerGroup) {\n    var _this = this;\n\n    containerGroup.on('click', function (e) {\n      if (_this._state !== 'ready') {\n        return;\n      }\n\n      var nodeClick = _this.seriesModel.get('nodeClick', true);\n\n      if (!nodeClick) {\n        return;\n      }\n\n      var targetInfo = _this.findTarget(e.offsetX, e.offsetY);\n\n      if (!targetInfo) {\n        return;\n      }\n\n      var node = targetInfo.node;\n\n      if (node.getLayout().isLeafRoot) {\n        _this._rootToNode(targetInfo);\n      } else {\n        if (nodeClick === 'zoomToNode') {\n          _this._zoomToNode(targetInfo);\n        } else if (nodeClick === 'link') {\n          var itemModel = node.hostTree.data.getItemModel(node.dataIndex);\n          var link = itemModel.get('link', true);\n          var linkTarget = itemModel.get('target', true) || 'blank';\n          link && windowOpen(link, linkTarget);\n        }\n      }\n    }, this);\n  };\n\n  TreemapView.prototype._renderBreadcrumb = function (seriesModel, api, targetInfo) {\n    var _this = this;\n\n    if (!targetInfo) {\n      targetInfo = seriesModel.get('leafDepth', true) != null ? {\n        node: seriesModel.getViewRoot()\n      } // FIXME\n      // better way?\n      // Find breadcrumb tail on center of containerGroup.\n      : this.findTarget(api.getWidth() / 2, api.getHeight() / 2);\n\n      if (!targetInfo) {\n        targetInfo = {\n          node: seriesModel.getData().tree.root\n        };\n      }\n    }\n\n    (this._breadcrumb || (this._breadcrumb = new Breadcrumb(this.group))).render(seriesModel, api, targetInfo.node, function (node) {\n      if (_this._state !== 'animating') {\n        helper.aboveViewRoot(seriesModel.getViewRoot(), node) ? _this._rootToNode({\n          node: node\n        }) : _this._zoomToNode({\n          node: node\n        });\n      }\n    });\n  };\n  /**\r\n   * @override\r\n   */\n\n\n  TreemapView.prototype.remove = function () {\n    this._clearController();\n\n    this._containerGroup && this._containerGroup.removeAll();\n    this._storage = createStorage();\n    this._state = 'ready';\n    this._breadcrumb && this._breadcrumb.remove();\n  };\n\n  TreemapView.prototype.dispose = function () {\n    this._clearController();\n  };\n\n  TreemapView.prototype._zoomToNode = function (targetInfo) {\n    this.api.dispatchAction({\n      type: 'treemapZoomToNode',\n      from: this.uid,\n      seriesId: this.seriesModel.id,\n      targetNode: targetInfo.node\n    });\n  };\n\n  TreemapView.prototype._rootToNode = function (targetInfo) {\n    this.api.dispatchAction({\n      type: 'treemapRootToNode',\n      from: this.uid,\n      seriesId: this.seriesModel.id,\n      targetNode: targetInfo.node\n    });\n  };\n  /**\r\n   * @public\r\n   * @param {number} x Global coord x.\r\n   * @param {number} y Global coord y.\r\n   * @return {Object} info If not found, return undefined;\r\n   * @return {number} info.node Target node.\r\n   * @return {number} info.offsetX x refer to target node.\r\n   * @return {number} info.offsetY y refer to target node.\r\n   */\n\n\n  TreemapView.prototype.findTarget = function (x, y) {\n    var targetInfo;\n    var viewRoot = this.seriesModel.getViewRoot();\n    viewRoot.eachNode({\n      attr: 'viewChildren',\n      order: 'preorder'\n    }, function (node) {\n      var bgEl = this._storage.background[node.getRawIndex()]; // If invisible, there might be no element.\n\n\n      if (bgEl) {\n        var point = bgEl.transformCoordToLocal(x, y);\n        var shape = bgEl.shape; // For performance consideration, don't use 'getBoundingRect'.\n\n        if (shape.x <= point[0] && point[0] <= shape.x + shape.width && shape.y <= point[1] && point[1] <= shape.y + shape.height) {\n          targetInfo = {\n            node: node,\n            offsetX: point[0],\n            offsetY: point[1]\n          };\n        } else {\n          return false; // Suppress visit subtree.\n        }\n      }\n    }, this);\n    return targetInfo;\n  };\n\n  TreemapView.type = 'treemap';\n  return TreemapView;\n}(ChartView);\n/**\r\n * @inner\r\n */\n\n\nfunction createStorage() {\n  return {\n    nodeGroup: [],\n    background: [],\n    content: []\n  };\n}\n/**\r\n * @inner\r\n * @return Return undefined means do not travel further.\r\n */\n\n\nfunction renderNode(seriesModel, thisStorage, oldStorage, reRoot, lastsForAnimation, willInvisibleEls, thisNode, oldNode, parentGroup, depth) {\n  // Whether under viewRoot.\n  if (!thisNode) {\n    // Deleting nodes will be performed finally. This method just find\n    // element from old storage, or create new element, set them to new\n    // storage, and set styles.\n    return;\n  } // -------------------------------------------------------------------\n  // Start of closure variables available in \"Procedures in renderNode\".\n\n\n  var thisLayout = thisNode.getLayout();\n  var data = seriesModel.getData();\n  var nodeModel = thisNode.getModel(); // Only for enabling highlight/downplay. Clear firstly.\n  // Because some node will not be rendered.\n\n  data.setItemGraphicEl(thisNode.dataIndex, null);\n\n  if (!thisLayout || !thisLayout.isInView) {\n    return;\n  }\n\n  var thisWidth = thisLayout.width;\n  var thisHeight = thisLayout.height;\n  var borderWidth = thisLayout.borderWidth;\n  var thisInvisible = thisLayout.invisible;\n  var thisRawIndex = thisNode.getRawIndex();\n  var oldRawIndex = oldNode && oldNode.getRawIndex();\n  var thisViewChildren = thisNode.viewChildren;\n  var upperHeight = thisLayout.upperHeight;\n  var isParent = thisViewChildren && thisViewChildren.length;\n  var itemStyleNormalModel = nodeModel.getModel('itemStyle');\n  var itemStyleEmphasisModel = nodeModel.getModel(['emphasis', 'itemStyle']);\n  var itemStyleBlurModel = nodeModel.getModel(['blur', 'itemStyle']);\n  var itemStyleSelectModel = nodeModel.getModel(['select', 'itemStyle']);\n  var borderRadius = itemStyleNormalModel.get('borderRadius') || 0; // End of closure ariables available in \"Procedures in renderNode\".\n  // -----------------------------------------------------------------\n  // Node group\n\n  var group = giveGraphic('nodeGroup', Group);\n\n  if (!group) {\n    return;\n  }\n\n  parentGroup.add(group); // x,y are not set when el is above view root.\n\n  group.x = thisLayout.x || 0;\n  group.y = thisLayout.y || 0;\n  group.markRedraw();\n  inner(group).nodeWidth = thisWidth;\n  inner(group).nodeHeight = thisHeight;\n\n  if (thisLayout.isAboveViewRoot) {\n    return group;\n  } // Background\n\n\n  var bg = giveGraphic('background', Rect, depth, Z2_BG);\n  bg && renderBackground(group, bg, isParent && thisLayout.upperLabelHeight);\n  var emphasisModel = nodeModel.getModel('emphasis');\n  var focus = emphasisModel.get('focus');\n  var blurScope = emphasisModel.get('blurScope');\n  var isDisabled = emphasisModel.get('disabled');\n  var focusOrIndices = focus === 'ancestor' ? thisNode.getAncestorsIndices() : focus === 'descendant' ? thisNode.getDescendantIndices() : focus; // No children, render content.\n\n  if (isParent) {\n    // Because of the implementation about \"traverse\" in graphic hover style, we\n    // can not set hover listener on the \"group\" of non-leaf node. Otherwise the\n    // hover event from the descendents will be listenered.\n    if (isHighDownDispatcher(group)) {\n      setAsHighDownDispatcher(group, false);\n    }\n\n    if (bg) {\n      setAsHighDownDispatcher(bg, !isDisabled); // Only for enabling highlight/downplay.\n\n      data.setItemGraphicEl(thisNode.dataIndex, bg);\n      enableHoverFocus(bg, focusOrIndices, blurScope);\n    }\n  } else {\n    var content = giveGraphic('content', Rect, depth, Z2_CONTENT);\n    content && renderContent(group, content);\n    bg.disableMorphing = true;\n\n    if (bg && isHighDownDispatcher(bg)) {\n      setAsHighDownDispatcher(bg, false);\n    }\n\n    setAsHighDownDispatcher(group, !isDisabled); // Only for enabling highlight/downplay.\n\n    data.setItemGraphicEl(thisNode.dataIndex, group);\n    enableHoverFocus(group, focusOrIndices, blurScope);\n  }\n\n  return group; // ----------------------------\n  // | Procedures in renderNode |\n  // ----------------------------\n\n  function renderBackground(group, bg, useUpperLabel) {\n    var ecData = getECData(bg); // For tooltip.\n\n    ecData.dataIndex = thisNode.dataIndex;\n    ecData.seriesIndex = seriesModel.seriesIndex;\n    bg.setShape({\n      x: 0,\n      y: 0,\n      width: thisWidth,\n      height: thisHeight,\n      r: borderRadius\n    });\n\n    if (thisInvisible) {\n      // If invisible, do not set visual, otherwise the element will\n      // change immediately before animation. We think it is OK to\n      // remain its origin color when moving out of the view window.\n      processInvisible(bg);\n    } else {\n      bg.invisible = false;\n      var style = thisNode.getVisual('style');\n      var visualBorderColor = style.stroke;\n      var normalStyle = getItemStyleNormal(itemStyleNormalModel);\n      normalStyle.fill = visualBorderColor;\n      var emphasisStyle = getStateItemStyle(itemStyleEmphasisModel);\n      emphasisStyle.fill = itemStyleEmphasisModel.get('borderColor');\n      var blurStyle = getStateItemStyle(itemStyleBlurModel);\n      blurStyle.fill = itemStyleBlurModel.get('borderColor');\n      var selectStyle = getStateItemStyle(itemStyleSelectModel);\n      selectStyle.fill = itemStyleSelectModel.get('borderColor');\n\n      if (useUpperLabel) {\n        var upperLabelWidth = thisWidth - 2 * borderWidth;\n        prepareText( // PENDING: convert ZRColor to ColorString for text.\n        bg, visualBorderColor, style.opacity, {\n          x: borderWidth,\n          y: 0,\n          width: upperLabelWidth,\n          height: upperHeight\n        });\n      } // For old bg.\n      else {\n          bg.removeTextContent();\n        }\n\n      bg.setStyle(normalStyle);\n      bg.ensureState('emphasis').style = emphasisStyle;\n      bg.ensureState('blur').style = blurStyle;\n      bg.ensureState('select').style = selectStyle;\n      setDefaultStateProxy(bg);\n    }\n\n    group.add(bg);\n  }\n\n  function renderContent(group, content) {\n    var ecData = getECData(content); // For tooltip.\n\n    ecData.dataIndex = thisNode.dataIndex;\n    ecData.seriesIndex = seriesModel.seriesIndex;\n    var contentWidth = Math.max(thisWidth - 2 * borderWidth, 0);\n    var contentHeight = Math.max(thisHeight - 2 * borderWidth, 0);\n    content.culling = true;\n    content.setShape({\n      x: borderWidth,\n      y: borderWidth,\n      width: contentWidth,\n      height: contentHeight,\n      r: borderRadius\n    });\n\n    if (thisInvisible) {\n      // If invisible, do not set visual, otherwise the element will\n      // change immediately before animation. We think it is OK to\n      // remain its origin color when moving out of the view window.\n      processInvisible(content);\n    } else {\n      content.invisible = false;\n      var nodeStyle = thisNode.getVisual('style');\n      var visualColor = nodeStyle.fill;\n      var normalStyle = getItemStyleNormal(itemStyleNormalModel);\n      normalStyle.fill = visualColor;\n      normalStyle.decal = nodeStyle.decal;\n      var emphasisStyle = getStateItemStyle(itemStyleEmphasisModel);\n      var blurStyle = getStateItemStyle(itemStyleBlurModel);\n      var selectStyle = getStateItemStyle(itemStyleSelectModel); // PENDING: convert ZRColor to ColorString for text.\n\n      prepareText(content, visualColor, nodeStyle.opacity, null);\n      content.setStyle(normalStyle);\n      content.ensureState('emphasis').style = emphasisStyle;\n      content.ensureState('blur').style = blurStyle;\n      content.ensureState('select').style = selectStyle;\n      setDefaultStateProxy(content);\n    }\n\n    group.add(content);\n  }\n\n  function processInvisible(element) {\n    // Delay invisible setting utill animation finished,\n    // avoid element vanish suddenly before animation.\n    !element.invisible && willInvisibleEls.push(element);\n  }\n\n  function prepareText(rectEl, visualColor, visualOpacity, // Can be null/undefined\n  upperLabelRect) {\n    var normalLabelModel = nodeModel.getModel(upperLabelRect ? PATH_UPPERLABEL_NORMAL : PATH_LABEL_NOAMAL);\n    var defaultText = convertOptionIdName(nodeModel.get('name'), null);\n    var isShow = normalLabelModel.getShallow('show');\n    setLabelStyle(rectEl, getLabelStatesModels(nodeModel, upperLabelRect ? PATH_UPPERLABEL_NORMAL : PATH_LABEL_NOAMAL), {\n      defaultText: isShow ? defaultText : null,\n      inheritColor: visualColor,\n      defaultOpacity: visualOpacity,\n      labelFetcher: seriesModel,\n      labelDataIndex: thisNode.dataIndex\n    });\n    var textEl = rectEl.getTextContent();\n\n    if (!textEl) {\n      return;\n    }\n\n    var textStyle = textEl.style;\n    var textPadding = normalizeCssArray(textStyle.padding || 0);\n\n    if (upperLabelRect) {\n      rectEl.setTextConfig({\n        layoutRect: upperLabelRect\n      });\n      textEl.disableLabelLayout = true;\n    }\n\n    textEl.beforeUpdate = function () {\n      var width = Math.max((upperLabelRect ? upperLabelRect.width : rectEl.shape.width) - textPadding[1] - textPadding[3], 0);\n      var height = Math.max((upperLabelRect ? upperLabelRect.height : rectEl.shape.height) - textPadding[0] - textPadding[2], 0);\n\n      if (textStyle.width !== width || textStyle.height !== height) {\n        textEl.setStyle({\n          width: width,\n          height: height\n        });\n      }\n    };\n\n    textStyle.truncateMinChar = 2;\n    textStyle.lineOverflow = 'truncate';\n    addDrillDownIcon(textStyle, upperLabelRect, thisLayout);\n    var textEmphasisState = textEl.getState('emphasis');\n    addDrillDownIcon(textEmphasisState ? textEmphasisState.style : null, upperLabelRect, thisLayout);\n  }\n\n  function addDrillDownIcon(style, upperLabelRect, thisLayout) {\n    var text = style ? style.text : null;\n\n    if (!upperLabelRect && thisLayout.isLeafRoot && text != null) {\n      var iconChar = seriesModel.get('drillDownIcon', true);\n      style.text = iconChar ? iconChar + ' ' + text : text;\n    }\n  }\n\n  function giveGraphic(storageName, Ctor, depth, z) {\n    var element = oldRawIndex != null && oldStorage[storageName][oldRawIndex];\n    var lasts = lastsForAnimation[storageName];\n\n    if (element) {\n      // Remove from oldStorage\n      oldStorage[storageName][oldRawIndex] = null;\n      prepareAnimationWhenHasOld(lasts, element);\n    } // If invisible and no old element, do not create new element (for optimizing).\n    else if (!thisInvisible) {\n        element = new Ctor();\n\n        if (element instanceof Displayable) {\n          element.z2 = calculateZ2(depth, z);\n        }\n\n        prepareAnimationWhenNoOld(lasts, element);\n      } // Set to thisStorage\n\n\n    return thisStorage[storageName][thisRawIndex] = element;\n  }\n\n  function prepareAnimationWhenHasOld(lasts, element) {\n    var lastCfg = lasts[thisRawIndex] = {};\n\n    if (element instanceof Group) {\n      lastCfg.oldX = element.x;\n      lastCfg.oldY = element.y;\n    } else {\n      lastCfg.oldShape = extend({}, element.shape);\n    }\n  } // If a element is new, we need to find the animation start point carefully,\n  // otherwise it will looks strange when 'zoomToNode'.\n\n\n  function prepareAnimationWhenNoOld(lasts, element) {\n    var lastCfg = lasts[thisRawIndex] = {};\n    var parentNode = thisNode.parentNode;\n    var isGroup = element instanceof graphic.Group;\n\n    if (parentNode && (!reRoot || reRoot.direction === 'drillDown')) {\n      var parentOldX = 0;\n      var parentOldY = 0; // New nodes appear from right-bottom corner in 'zoomToNode' animation.\n      // For convenience, get old bounding rect from background.\n\n      var parentOldBg = lastsForAnimation.background[parentNode.getRawIndex()];\n\n      if (!reRoot && parentOldBg && parentOldBg.oldShape) {\n        parentOldX = parentOldBg.oldShape.width;\n        parentOldY = parentOldBg.oldShape.height;\n      } // When no parent old shape found, its parent is new too,\n      // so we can just use {x:0, y:0}.\n\n\n      if (isGroup) {\n        lastCfg.oldX = 0;\n        lastCfg.oldY = parentOldY;\n      } else {\n        lastCfg.oldShape = {\n          x: parentOldX,\n          y: parentOldY,\n          width: 0,\n          height: 0\n        };\n      }\n    } // Fade in, user can be aware that these nodes are new.\n\n\n    lastCfg.fadein = !isGroup;\n  }\n} // We cannot set all background with the same z, because the behaviour of\n// drill down and roll up differ background creation sequence from tree\n// hierarchy sequence, which cause lower background elements to overlap\n// upper ones. So we calculate z based on depth.\n// Moreover, we try to shrink down z interval to [0, 1] to avoid that\n// treemap with large z overlaps other components.\n\n\nfunction calculateZ2(depth, z2InLevel) {\n  return depth * Z2_BASE + z2InLevel;\n}\n\nexport default TreemapView;"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,SAAS,QAAQ,OAAO;AACjC,SAASC,IAAI,EAAEC,IAAI,EAAEC,OAAO,EAAEC,KAAK,EAAEC,MAAM,EAAEC,iBAAiB,EAAEC,UAAU,QAAQ,0BAA0B;AAC5G,OAAO,KAAKC,OAAO,MAAM,uBAAuB;AAChD,SAASC,SAAS,QAAQ,0BAA0B;AACpD,SAASC,oBAAoB,EAAEC,uBAAuB,EAAEC,oBAAoB,EAAEC,gBAAgB,EAAEC,gBAAgB,QAAQ,sBAAsB;AAC9I,OAAOC,UAAU,MAAM,0BAA0B;AACjD,OAAO,KAAKC,MAAM,MAAM,yBAAyB;AACjD,OAAOC,UAAU,MAAM,iBAAiB;AACxC,OAAOC,cAAc,MAAM,0CAA0C;AACrE,OAAOC,YAAY,MAAM,kCAAkC;AAC3D,OAAO,KAAKC,MAAM,MAAM,4BAA4B;AACpD,OAAO,KAAKC,aAAa,MAAM,yBAAyB;AACxD,OAAOC,eAAe,MAAM,sCAAsC;AAClE,OAAOC,SAAS,MAAM,qBAAqB;AAC3C,OAAOC,WAAW,MAAM,oCAAoC;AAC5D,SAASC,SAAS,EAAEC,mBAAmB,QAAQ,qBAAqB;AACpE,SAASC,UAAU,QAAQ,sBAAsB;AACjD,SAASC,aAAa,EAAEC,oBAAoB,QAAQ,2BAA2B;AAC/E,IAAIC,KAAK,GAAGtB,OAAO,CAACsB,KAAK;AACzB,IAAIC,IAAI,GAAGvB,OAAO,CAACuB,IAAI;AACvB,IAAIC,cAAc,GAAG,CAAC;AACtB,IAAIC,iBAAiB,GAAG,OAAO;AAC/B,IAAIC,sBAAsB,GAAG,YAAY,CAAC,CAAC;;AAE3C,IAAIC,OAAO,GAAGrB,gBAAgB,GAAG,EAAE,CAAC,CAAC;;AAErC,IAAIsB,KAAK,GAAGtB,gBAAgB,GAAG,CAAC;AAChC,IAAIuB,UAAU,GAAGvB,gBAAgB,GAAG,CAAC;AACrC,IAAIwB,iBAAiB,GAAGhB,eAAe,CAAC,CAAC,CAAC,MAAM,EAAE,OAAO,CAAC;AAAE;AAC5D;AACA,CAAC,QAAQ,EAAE,aAAa,CAAC,EAAE,CAAC,WAAW,EAAE,aAAa,CAAC,EAAE,CAAC,YAAY,CAAC,EAAE,CAAC,eAAe,CAAC,EAAE,CAAC,eAAe,CAAC,EAAE,CAAC,aAAa,CAAC,CAAC;AAC/H;AAAA,CACC,CAAC;;AAEF,IAAIiB,kBAAkB,GAAG,SAAAA,CAAUC,KAAK,EAAE;EACxC;EACA,IAAIC,SAAS,GAAGH,iBAAiB,CAACE,KAAK,CAAC,CAAC,CAAC;;EAE1CC,SAAS,CAACC,MAAM,GAAGD,SAAS,CAACE,IAAI,GAAGF,SAAS,CAACG,SAAS,GAAG,IAAI;EAC9D,OAAOH,SAAS;AAClB,CAAC;AAED,IAAII,KAAK,GAAGpB,SAAS,CAAC,CAAC;AAEvB,IAAIqB,WAAW,GACf;AACA,UAAUC,MAAM,EAAE;EAChB/C,SAAS,CAAC8C,WAAW,EAAEC,MAAM,CAAC;EAE9B,SAASD,WAAWA,CAAA,EAAG;IACrB,IAAIE,KAAK,GAAGD,MAAM,KAAK,IAAI,IAAIA,MAAM,CAACE,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC,IAAI,IAAI;IAEpEF,KAAK,CAACG,IAAI,GAAGL,WAAW,CAACK,IAAI;IAC7BH,KAAK,CAACI,MAAM,GAAG,OAAO;IACtBJ,KAAK,CAACK,QAAQ,GAAGC,aAAa,CAAC,CAAC;IAChC,OAAON,KAAK;EACd;EACA;AACF;AACA;;EAGEF,WAAW,CAACS,SAAS,CAACC,MAAM,GAAG,UAAUC,WAAW,EAAEC,OAAO,EAAEC,GAAG,EAAEC,OAAO,EAAE;IAC3E,IAAIC,MAAM,GAAGH,OAAO,CAACI,cAAc,CAAC;MAClCC,QAAQ,EAAE,QAAQ;MAClBC,OAAO,EAAE,SAAS;MAClBC,KAAK,EAAEL;IACT,CAAC,CAAC;IAEF,IAAIzD,OAAO,CAAC0D,MAAM,EAAEJ,WAAW,CAAC,GAAG,CAAC,EAAE;MACpC;IACF;IAEA,IAAI,CAACA,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAACE,GAAG,GAAGA,GAAG;IACd,IAAI,CAACD,OAAO,GAAGA,OAAO;IACtB,IAAIQ,KAAK,GAAG,CAAC,mBAAmB,EAAE,mBAAmB,CAAC;IACtD,IAAIC,UAAU,GAAGnD,MAAM,CAACoD,kBAAkB,CAACR,OAAO,EAAEM,KAAK,EAAET,WAAW,CAAC;IACvE,IAAIY,WAAW,GAAGT,OAAO,IAAIA,OAAO,CAACT,IAAI;IACzC,IAAImB,UAAU,GAAGb,WAAW,CAACa,UAAU;IACvC,IAAIC,MAAM,GAAG,CAAC,IAAI,CAACC,QAAQ;IAC3B,IAAIC,WAAW,GAAG,IAAI,CAACpB,QAAQ,CAAC,CAAC;;IAEjC,IAAIqB,MAAM,GAAGL,WAAW,KAAK,mBAAmB,IAAIF,UAAU,IAAIM,WAAW,GAAG;MAC9EE,aAAa,EAAEF,WAAW,CAACG,SAAS,CAACT,UAAU,CAACU,IAAI,CAACC,WAAW,CAAC,CAAC,CAAC;MACnEC,SAAS,EAAEnB,OAAO,CAACmB;IACrB,CAAC,GAAG,IAAI;IAER,IAAIC,cAAc,GAAG,IAAI,CAACC,mBAAmB,CAACX,UAAU,CAAC;IAEzD,IAAIY,YAAY,GAAGzB,WAAW,CAAC0B,GAAG,CAAC,WAAW,CAAC;IAE/C,IAAIC,YAAY,GAAG,IAAI,CAACC,SAAS,CAACL,cAAc,EAAEvB,WAAW,EAAEiB,MAAM,CAAC;IAEtEQ,YAAY,IAAI,CAACX,MAAM,KAAK,CAACF,WAAW,IAAIA,WAAW,KAAK,mBAAmB,IAAIA,WAAW,KAAK,mBAAmB,CAAC,GAAG,IAAI,CAACiB,YAAY,CAACN,cAAc,EAAEI,YAAY,EAAE3B,WAAW,EAAEiB,MAAM,CAAC,GAAGU,YAAY,CAACG,aAAa,CAAC,CAAC;IAE7N,IAAI,CAACC,gBAAgB,CAAC7B,GAAG,CAAC;IAE1B,IAAI,CAAC8B,iBAAiB,CAAChC,WAAW,EAAEE,GAAG,EAAEQ,UAAU,CAAC;EACtD,CAAC;EAEDrB,WAAW,CAACS,SAAS,CAAC0B,mBAAmB,GAAG,UAAUX,UAAU,EAAE;IAChE,IAAIU,cAAc,GAAG,IAAI,CAACU,eAAe;IAEzC,IAAI,CAACV,cAAc,EAAE;MACnB;MACA;MACAA,cAAc,GAAG,IAAI,CAACU,eAAe,GAAG,IAAI5D,KAAK,CAAC,CAAC;MAEnD,IAAI,CAAC6D,WAAW,CAACX,cAAc,CAAC;MAEhC,IAAI,CAACY,KAAK,CAACC,GAAG,CAACb,cAAc,CAAC;IAChC;IAEAA,cAAc,CAACc,CAAC,GAAGxB,UAAU,CAACwB,CAAC;IAC/Bd,cAAc,CAACe,CAAC,GAAGzB,UAAU,CAACyB,CAAC;IAC/B,OAAOf,cAAc;EACvB,CAAC;EAEDlC,WAAW,CAACS,SAAS,CAAC8B,SAAS,GAAG,UAAUL,cAAc,EAAEvB,WAAW,EAAEiB,MAAM,EAAE;IAC/E,IAAIsB,QAAQ,GAAGvC,WAAW,CAACwC,OAAO,CAAC,CAAC,CAACC,IAAI;IACzC,IAAIC,OAAO,GAAG,IAAI,CAAC3B,QAAQ,CAAC,CAAC;;IAE7B,IAAI4B,iBAAiB,GAAG9C,aAAa,CAAC,CAAC;IACvC,IAAImB,WAAW,GAAGnB,aAAa,CAAC,CAAC;IACjC,IAAI+C,UAAU,GAAG,IAAI,CAAChD,QAAQ;IAC9B,IAAIiD,gBAAgB,GAAG,EAAE;IAEzB,SAASC,YAAYA,CAACC,QAAQ,EAAEC,OAAO,EAAEC,WAAW,EAAEC,KAAK,EAAE;MAC3D,OAAOC,UAAU,CAACnD,WAAW,EAAEgB,WAAW,EAAE4B,UAAU,EAAE3B,MAAM,EAAE0B,iBAAiB,EAAEE,gBAAgB,EAAEE,QAAQ,EAAEC,OAAO,EAAEC,WAAW,EAAEC,KAAK,CAAC;IAC7I,CAAC,CAAC;IACF;IACA;IACA;;IAGAE,UAAU,CAACb,QAAQ,CAACc,IAAI,GAAG,CAACd,QAAQ,CAACc,IAAI,CAAC,GAAG,EAAE,EAAEX,OAAO,IAAIA,OAAO,CAACW,IAAI,GAAG,CAACX,OAAO,CAACW,IAAI,CAAC,GAAG,EAAE,EAAE9B,cAAc,EAAEgB,QAAQ,KAAKG,OAAO,IAAI,CAACA,OAAO,EAAE,CAAC,CAAC,CAAC,CAAC;;IAEtJ,IAAIY,aAAa,GAAGC,YAAY,CAACX,UAAU,CAAC;IAC5C,IAAI,CAAC7B,QAAQ,GAAGwB,QAAQ;IACxB,IAAI,CAAC3C,QAAQ,GAAGoB,WAAW;IAC3B,OAAO;MACL2B,iBAAiB,EAAEA,iBAAiB;MACpCW,aAAa,EAAEA,aAAa;MAC5BxB,aAAa,EAAEA;IACjB,CAAC;IAED,SAASsB,UAAUA,CAACI,gBAAgB,EAAEC,eAAe,EAAER,WAAW,EAAES,QAAQ,EAAER,KAAK,EAAE;MACnF;MACA;MACA;MACA,IAAIQ,QAAQ,EAAE;QACZD,eAAe,GAAGD,gBAAgB;QAClC/G,IAAI,CAAC+G,gBAAgB,EAAE,UAAUG,KAAK,EAAEC,KAAK,EAAE;UAC7C,CAACD,KAAK,CAACE,SAAS,CAAC,CAAC,IAAIC,WAAW,CAACF,KAAK,EAAEA,KAAK,CAAC;QACjD,CAAC,CAAC;MACJ,CAAC,CAAC;MACF;MAAA,KACK;QACD,IAAItG,UAAU,CAACmG,eAAe,EAAED,gBAAgB,EAAEO,MAAM,EAAEA,MAAM,CAAC,CAAC3B,GAAG,CAAC0B,WAAW,CAAC,CAACE,MAAM,CAACF,WAAW,CAAC,CAACG,MAAM,CAACtH,KAAK,CAACmH,WAAW,EAAE,IAAI,CAAC,CAAC,CAACI,OAAO,CAAC,CAAC;MACnJ;MAEF,SAASH,MAAMA,CAAC3C,IAAI,EAAE;QACpB;QACA,OAAOA,IAAI,CAAC+C,KAAK,CAAC,CAAC;MACrB;MAEA,SAASL,WAAWA,CAACM,QAAQ,EAAEC,QAAQ,EAAE;QACvC,IAAItB,QAAQ,GAAGqB,QAAQ,IAAI,IAAI,GAAGZ,gBAAgB,CAACY,QAAQ,CAAC,GAAG,IAAI;QACnE,IAAIpB,OAAO,GAAGqB,QAAQ,IAAI,IAAI,GAAGZ,eAAe,CAACY,QAAQ,CAAC,GAAG,IAAI;QACjE,IAAIlC,KAAK,GAAGW,YAAY,CAACC,QAAQ,EAAEC,OAAO,EAAEC,WAAW,EAAEC,KAAK,CAAC;QAC/Df,KAAK,IAAIiB,UAAU,CAACL,QAAQ,IAAIA,QAAQ,CAACuB,YAAY,IAAI,EAAE,EAAEtB,OAAO,IAAIA,OAAO,CAACsB,YAAY,IAAI,EAAE,EAAEnC,KAAK,EAAEuB,QAAQ,EAAER,KAAK,GAAG,CAAC,CAAC;MACjI;IACF;IAEA,SAASK,YAAYA,CAACgB,OAAO,EAAE;MAC7B,IAAIjB,aAAa,GAAGzD,aAAa,CAAC,CAAC;MACnC0E,OAAO,IAAI9H,IAAI,CAAC8H,OAAO,EAAE,UAAUC,KAAK,EAAEC,WAAW,EAAE;QACrD,IAAIC,MAAM,GAAGpB,aAAa,CAACmB,WAAW,CAAC;QACvChI,IAAI,CAAC+H,KAAK,EAAE,UAAUG,EAAE,EAAE;UACxBA,EAAE,KAAKD,MAAM,CAACE,IAAI,CAACD,EAAE,CAAC,EAAEvF,KAAK,CAACuF,EAAE,CAAC,CAACE,UAAU,GAAG,IAAI,CAAC;QACtD,CAAC,CAAC;MACJ,CAAC,CAAC;MACF,OAAOvB,aAAa;IACtB;IAEA,SAASxB,aAAaA,CAAA,EAAG;MACvBrF,IAAI,CAAC6G,aAAa,EAAE,UAAUwB,GAAG,EAAE;QACjCrI,IAAI,CAACqI,GAAG,EAAE,UAAUH,EAAE,EAAE;UACtBA,EAAE,CAACI,MAAM,IAAIJ,EAAE,CAACI,MAAM,CAACd,MAAM,CAACU,EAAE,CAAC;QACnC,CAAC,CAAC;MACJ,CAAC,CAAC;MACFlI,IAAI,CAACoG,gBAAgB,EAAE,UAAU8B,EAAE,EAAE;QACnCA,EAAE,CAACK,SAAS,GAAG,IAAI,CAAC,CAAC;QACrB;;QAEAL,EAAE,CAACM,KAAK,CAAC,CAAC;MACZ,CAAC,CAAC;IACJ;EACF,CAAC;EAED5F,WAAW,CAACS,SAAS,CAAC+B,YAAY,GAAG,UAAUN,cAAc,EAAEI,YAAY,EAAE3B,WAAW,EAAEiB,MAAM,EAAE;IAChG,IAAIiE,cAAc,GAAGlF,WAAW,CAAC0B,GAAG,CAAC,yBAAyB,CAAC;IAC/D,IAAIyD,YAAY,GAAGnF,WAAW,CAAC0B,GAAG,CAAC,iBAAiB,CAAC,CAAC,CAAC;;IAEvD,IAAI0D,QAAQ,GAAG,CAACtI,UAAU,CAACoI,cAAc,CAAC,GAAG,CAAC,GAAGA,cAAc,KAAK,CAAC;IACrE,IAAIG,MAAM,GAAG,CAACvI,UAAU,CAACqI,YAAY,CAAC,GAAG,IAAI,GAAGA,YAAY,KAAK,UAAU;IAC3E,IAAIG,aAAa,GAAG1H,aAAa,CAAC2H,UAAU,CAAC,CAAC,CAAC,CAAC;;IAEhD9I,IAAI,CAACkF,YAAY,CAAC2B,aAAa,EAAE,UAAUkB,KAAK,EAAEC,WAAW,EAAE;MAC7DhI,IAAI,CAAC+H,KAAK,EAAE,UAAUG,EAAE,EAAEa,QAAQ,EAAE;QAClC,IAAIb,EAAE,CAACK,SAAS,EAAE;UAChB;QACF;QAEA,IAAID,MAAM,GAAGJ,EAAE,CAACI,MAAM,CAAC,CAAC;;QAExB,IAAIU,MAAM;QACV,IAAIC,UAAU,GAAGtG,KAAK,CAAC2F,MAAM,CAAC;QAE9B,IAAI9D,MAAM,IAAIA,MAAM,CAACK,SAAS,KAAK,WAAW,EAAE;UAC9CmE,MAAM,GAAGV,MAAM,KAAK9D,MAAM,CAACC,aAAa,CAAC;UACzC;UACA;UAAA,EACE;YACAyE,KAAK,EAAE;cACLtD,CAAC,EAAE,CAAC;cACJC,CAAC,EAAE,CAAC;cACJsD,KAAK,EAAEF,UAAU,CAACG,SAAS;cAC3BC,MAAM,EAAEJ,UAAU,CAACK;YACrB,CAAC;YACDC,KAAK,EAAE;cACLC,OAAO,EAAE;YACX;UACF,CAAC,CAAC;UAAA,EACA;YACAD,KAAK,EAAE;cACLC,OAAO,EAAE;YACX;UACF,CAAC;QACH,CAAC,MAAM;UACL,IAAIC,OAAO,GAAG,CAAC;UACf,IAAIC,OAAO,GAAG,CAAC;UAEf,IAAI,CAACT,UAAU,CAACb,UAAU,EAAE;YAC1B;YACA;YACA;YACAqB,OAAO,GAAGR,UAAU,CAACG,SAAS,GAAG,CAAC;YAClCM,OAAO,GAAGT,UAAU,CAACK,UAAU,GAAG,CAAC;UACrC;UAEAN,MAAM,GAAGhB,WAAW,KAAK,WAAW,GAAG;YACrCpC,CAAC,EAAE6D,OAAO;YACV5D,CAAC,EAAE6D,OAAO;YACVH,KAAK,EAAE;cACLC,OAAO,EAAE;YACX;UACF,CAAC,GAAG;YACFN,KAAK,EAAE;cACLtD,CAAC,EAAE6D,OAAO;cACV5D,CAAC,EAAE6D,OAAO;cACVP,KAAK,EAAE,CAAC;cACRE,MAAM,EAAE;YACV,CAAC;YACDE,KAAK,EAAE;cACLC,OAAO,EAAE;YACX;UACF,CAAC;QACH,CAAC,CAAC;;QAGFR,MAAM,IAAIH,aAAa,CAAClD,GAAG,CAACuC,EAAE,EAAEc,MAAM,EAAEL,QAAQ,EAAE,CAAC,EAAEC,MAAM,CAAC;MAC9D,CAAC,CAAC;IACJ,CAAC,CAAC,CAAC,CAAC;;IAEJ5I,IAAI,CAAC,IAAI,CAACmD,QAAQ,EAAE,UAAU4E,KAAK,EAAEC,WAAW,EAAE;MAChDhI,IAAI,CAAC+H,KAAK,EAAE,UAAUG,EAAE,EAAEa,QAAQ,EAAE;QAClC,IAAIY,IAAI,GAAGzE,YAAY,CAACgB,iBAAiB,CAAC8B,WAAW,CAAC,CAACe,QAAQ,CAAC;QAChE,IAAIC,MAAM,GAAG,CAAC,CAAC;QAEf,IAAI,CAACW,IAAI,EAAE;UACT;QACF;QAEA,IAAIzB,EAAE,YAAY5H,OAAO,CAACsB,KAAK,EAAE;UAC/B,IAAI+H,IAAI,CAACC,IAAI,IAAI,IAAI,EAAE;YACrBZ,MAAM,CAACpD,CAAC,GAAGsC,EAAE,CAACtC,CAAC;YACfoD,MAAM,CAACnD,CAAC,GAAGqC,EAAE,CAACrC,CAAC;YACfqC,EAAE,CAACtC,CAAC,GAAG+D,IAAI,CAACC,IAAI;YAChB1B,EAAE,CAACrC,CAAC,GAAG8D,IAAI,CAACE,IAAI;UAClB;QACF,CAAC,MAAM;UACL,IAAIF,IAAI,CAACG,QAAQ,EAAE;YACjBd,MAAM,CAACE,KAAK,GAAG/I,MAAM,CAAC,CAAC,CAAC,EAAE+H,EAAE,CAACgB,KAAK,CAAC;YACnChB,EAAE,CAAC6B,QAAQ,CAACJ,IAAI,CAACG,QAAQ,CAAC;UAC5B;UAEA,IAAIH,IAAI,CAACK,MAAM,EAAE;YACf9B,EAAE,CAAC+B,QAAQ,CAAC,SAAS,EAAE,CAAC,CAAC;YACzBjB,MAAM,CAACO,KAAK,GAAG;cACbC,OAAO,EAAE;YACX,CAAC;UACH,CAAC,CAAC;UACF;UAAA,KACK,IAAItB,EAAE,CAACqB,KAAK,CAACC,OAAO,KAAK,CAAC,EAAE;YAC7BR,MAAM,CAACO,KAAK,GAAG;cACbC,OAAO,EAAE;YACX,CAAC;UACH;QACJ;QAEAX,aAAa,CAAClD,GAAG,CAACuC,EAAE,EAAEc,MAAM,EAAEL,QAAQ,EAAE,CAAC,EAAEC,MAAM,CAAC;MACpD,CAAC,CAAC;IACJ,CAAC,EAAE,IAAI,CAAC;IACR,IAAI,CAAC1F,MAAM,GAAG,WAAW;IACzB2F,aAAa,CAACqB,QAAQ,CAACnK,IAAI,CAAC,YAAY;MACtC,IAAI,CAACmD,MAAM,GAAG,OAAO;MACrBgC,YAAY,CAACG,aAAa,CAAC,CAAC;IAC9B,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC8E,KAAK,CAAC,CAAC;EACnB,CAAC;EAEDvH,WAAW,CAACS,SAAS,CAACiC,gBAAgB,GAAG,UAAU7B,GAAG,EAAE;IACtD,IAAI2G,UAAU,GAAG,IAAI,CAACC,WAAW,CAAC,CAAC;;IAEnC,IAAI,CAACD,UAAU,EAAE;MACfA,UAAU,GAAG,IAAI,CAACC,WAAW,GAAG,IAAIrJ,cAAc,CAACyC,GAAG,CAAC6G,KAAK,CAAC,CAAC,CAAC;MAC/DF,UAAU,CAACG,MAAM,CAAC,IAAI,CAAChH,WAAW,CAAC0B,GAAG,CAAC,MAAM,CAAC,CAAC;MAC/CmF,UAAU,CAACI,EAAE,CAAC,KAAK,EAAEzK,IAAI,CAAC,IAAI,CAAC0K,MAAM,EAAE,IAAI,CAAC,CAAC;MAC7CL,UAAU,CAACI,EAAE,CAAC,MAAM,EAAEzK,IAAI,CAAC,IAAI,CAAC2K,OAAO,EAAE,IAAI,CAAC,CAAC;IACjD;IAEA,IAAIC,IAAI,GAAG,IAAI1J,YAAY,CAAC,CAAC,EAAE,CAAC,EAAEwC,GAAG,CAACmH,QAAQ,CAAC,CAAC,EAAEnH,GAAG,CAACoH,SAAS,CAAC,CAAC,CAAC;IAClET,UAAU,CAACU,iBAAiB,CAAC,UAAUC,CAAC,EAAEnF,CAAC,EAAEC,CAAC,EAAE;MAC9C,OAAO8E,IAAI,CAACK,OAAO,CAACpF,CAAC,EAAEC,CAAC,CAAC;IAC3B,CAAC,CAAC;EACJ,CAAC;EAEDjD,WAAW,CAACS,SAAS,CAAC4H,gBAAgB,GAAG,YAAY;IACnD,IAAIb,UAAU,GAAG,IAAI,CAACC,WAAW;IAEjC,IAAID,UAAU,EAAE;MACdA,UAAU,CAACc,OAAO,CAAC,CAAC;MACpBd,UAAU,GAAG,IAAI;IACnB;EACF,CAAC;EAEDxH,WAAW,CAACS,SAAS,CAACoH,MAAM,GAAG,UAAUM,CAAC,EAAE;IAC1C,IAAI,IAAI,CAAC7H,MAAM,KAAK,WAAW,KAAKiI,IAAI,CAACC,GAAG,CAACL,CAAC,CAACM,EAAE,CAAC,GAAGvJ,cAAc,IAAIqJ,IAAI,CAACC,GAAG,CAACL,CAAC,CAACO,EAAE,CAAC,GAAGxJ,cAAc,CAAC,EAAE;MACvG;MACA,IAAI8E,IAAI,GAAG,IAAI,CAACrD,WAAW,CAACwC,OAAO,CAAC,CAAC,CAACC,IAAI,CAACY,IAAI;MAE/C,IAAI,CAACA,IAAI,EAAE;QACT;MACF;MAEA,IAAI2E,UAAU,GAAG3E,IAAI,CAAC4E,SAAS,CAAC,CAAC;MAEjC,IAAI,CAACD,UAAU,EAAE;QACf;MACF;MAEA,IAAI,CAAC9H,GAAG,CAACgI,cAAc,CAAC;QACtBxI,IAAI,EAAE,aAAa;QACnByI,IAAI,EAAE,IAAI,CAACC,GAAG;QACdC,QAAQ,EAAE,IAAI,CAACrI,WAAW,CAACsI,EAAE;QAC7BC,QAAQ,EAAE;UACRlG,CAAC,EAAE2F,UAAU,CAAC3F,CAAC,GAAGmF,CAAC,CAACM,EAAE;UACtBxF,CAAC,EAAE0F,UAAU,CAAC1F,CAAC,GAAGkF,CAAC,CAACO,EAAE;UACtBnC,KAAK,EAAEoC,UAAU,CAACpC,KAAK;UACvBE,MAAM,EAAEkC,UAAU,CAAClC;QACrB;MACF,CAAC,CAAC;IACJ;EACF,CAAC;EAEDzG,WAAW,CAACS,SAAS,CAACqH,OAAO,GAAG,UAAUK,CAAC,EAAE;IAC3C,IAAIgB,MAAM,GAAGhB,CAAC,CAACiB,OAAO;IACtB,IAAIC,MAAM,GAAGlB,CAAC,CAACmB,OAAO;IAEtB,IAAI,IAAI,CAAChJ,MAAM,KAAK,WAAW,EAAE;MAC/B;MACA,IAAI0D,IAAI,GAAG,IAAI,CAACrD,WAAW,CAACwC,OAAO,CAAC,CAAC,CAACC,IAAI,CAACY,IAAI;MAE/C,IAAI,CAACA,IAAI,EAAE;QACT;MACF;MAEA,IAAI2E,UAAU,GAAG3E,IAAI,CAAC4E,SAAS,CAAC,CAAC;MAEjC,IAAI,CAACD,UAAU,EAAE;QACf;MACF;MAEA,IAAIZ,IAAI,GAAG,IAAI1J,YAAY,CAACsK,UAAU,CAAC3F,CAAC,EAAE2F,UAAU,CAAC1F,CAAC,EAAE0F,UAAU,CAACpC,KAAK,EAAEoC,UAAU,CAAClC,MAAM,CAAC;MAC5F,IAAIjF,UAAU,GAAG,IAAI,CAACb,WAAW,CAACa,UAAU,CAAC,CAAC;;MAE9C2H,MAAM,IAAI3H,UAAU,CAACwB,CAAC;MACtBqG,MAAM,IAAI7H,UAAU,CAACyB,CAAC,CAAC,CAAC;;MAExB,IAAIsG,CAAC,GAAGjL,MAAM,CAACkL,MAAM,CAAC,CAAC;MACvBlL,MAAM,CAACmL,SAAS,CAACF,CAAC,EAAEA,CAAC,EAAE,CAAC,CAACJ,MAAM,EAAE,CAACE,MAAM,CAAC,CAAC;MAC1C/K,MAAM,CAACoL,KAAK,CAACH,CAAC,EAAEA,CAAC,EAAE,CAACpB,CAAC,CAACuB,KAAK,EAAEvB,CAAC,CAACuB,KAAK,CAAC,CAAC;MACtCpL,MAAM,CAACmL,SAAS,CAACF,CAAC,EAAEA,CAAC,EAAE,CAACJ,MAAM,EAAEE,MAAM,CAAC,CAAC;MACxCtB,IAAI,CAAC4B,cAAc,CAACJ,CAAC,CAAC;MACtB,IAAI,CAAC1I,GAAG,CAACgI,cAAc,CAAC;QACtBxI,IAAI,EAAE,eAAe;QACrByI,IAAI,EAAE,IAAI,CAACC,GAAG;QACdC,QAAQ,EAAE,IAAI,CAACrI,WAAW,CAACsI,EAAE;QAC7BC,QAAQ,EAAE;UACRlG,CAAC,EAAE+E,IAAI,CAAC/E,CAAC;UACTC,CAAC,EAAE8E,IAAI,CAAC9E,CAAC;UACTsD,KAAK,EAAEwB,IAAI,CAACxB,KAAK;UACjBE,MAAM,EAAEsB,IAAI,CAACtB;QACf;MACF,CAAC,CAAC;IACJ;EACF,CAAC;EAEDzG,WAAW,CAACS,SAAS,CAACoC,WAAW,GAAG,UAAUX,cAAc,EAAE;IAC5D,IAAIhC,KAAK,GAAG,IAAI;IAEhBgC,cAAc,CAAC0F,EAAE,CAAC,OAAO,EAAE,UAAUO,CAAC,EAAE;MACtC,IAAIjI,KAAK,CAACI,MAAM,KAAK,OAAO,EAAE;QAC5B;MACF;MAEA,IAAIsJ,SAAS,GAAG1J,KAAK,CAACS,WAAW,CAAC0B,GAAG,CAAC,WAAW,EAAE,IAAI,CAAC;MAExD,IAAI,CAACuH,SAAS,EAAE;QACd;MACF;MAEA,IAAIvI,UAAU,GAAGnB,KAAK,CAAC2J,UAAU,CAAC1B,CAAC,CAAC2B,OAAO,EAAE3B,CAAC,CAAC4B,OAAO,CAAC;MAEvD,IAAI,CAAC1I,UAAU,EAAE;QACf;MACF;MAEA,IAAIU,IAAI,GAAGV,UAAU,CAACU,IAAI;MAE1B,IAAIA,IAAI,CAAC6G,SAAS,CAAC,CAAC,CAACoB,UAAU,EAAE;QAC/B9J,KAAK,CAAC+J,WAAW,CAAC5I,UAAU,CAAC;MAC/B,CAAC,MAAM;QACL,IAAIuI,SAAS,KAAK,YAAY,EAAE;UAC9B1J,KAAK,CAACgK,WAAW,CAAC7I,UAAU,CAAC;QAC/B,CAAC,MAAM,IAAIuI,SAAS,KAAK,MAAM,EAAE;UAC/B,IAAIO,SAAS,GAAGpI,IAAI,CAACqI,QAAQ,CAACC,IAAI,CAACC,YAAY,CAACvI,IAAI,CAACwI,SAAS,CAAC;UAC/D,IAAIC,IAAI,GAAGL,SAAS,CAAC9H,GAAG,CAAC,MAAM,EAAE,IAAI,CAAC;UACtC,IAAIoI,UAAU,GAAGN,SAAS,CAAC9H,GAAG,CAAC,QAAQ,EAAE,IAAI,CAAC,IAAI,OAAO;UACzDmI,IAAI,IAAI3L,UAAU,CAAC2L,IAAI,EAAEC,UAAU,CAAC;QACtC;MACF;IACF,CAAC,EAAE,IAAI,CAAC;EACV,CAAC;EAEDzK,WAAW,CAACS,SAAS,CAACkC,iBAAiB,GAAG,UAAUhC,WAAW,EAAEE,GAAG,EAAEQ,UAAU,EAAE;IAChF,IAAInB,KAAK,GAAG,IAAI;IAEhB,IAAI,CAACmB,UAAU,EAAE;MACfA,UAAU,GAAGV,WAAW,CAAC0B,GAAG,CAAC,WAAW,EAAE,IAAI,CAAC,IAAI,IAAI,GAAG;QACxDN,IAAI,EAAEpB,WAAW,CAAC+J,WAAW,CAAC;MAChC,CAAC,CAAC;MACF;MACA;MAAA,EACE,IAAI,CAACb,UAAU,CAAChJ,GAAG,CAACmH,QAAQ,CAAC,CAAC,GAAG,CAAC,EAAEnH,GAAG,CAACoH,SAAS,CAAC,CAAC,GAAG,CAAC,CAAC;MAE1D,IAAI,CAAC5G,UAAU,EAAE;QACfA,UAAU,GAAG;UACXU,IAAI,EAAEpB,WAAW,CAACwC,OAAO,CAAC,CAAC,CAACC,IAAI,CAACY;QACnC,CAAC;MACH;IACF;IAEA,CAAC,IAAI,CAAC2G,WAAW,KAAK,IAAI,CAACA,WAAW,GAAG,IAAIxM,UAAU,CAAC,IAAI,CAAC2E,KAAK,CAAC,CAAC,EAAEpC,MAAM,CAACC,WAAW,EAAEE,GAAG,EAAEQ,UAAU,CAACU,IAAI,EAAE,UAAUA,IAAI,EAAE;MAC9H,IAAI7B,KAAK,CAACI,MAAM,KAAK,WAAW,EAAE;QAChCpC,MAAM,CAAC0M,aAAa,CAACjK,WAAW,CAAC+J,WAAW,CAAC,CAAC,EAAE3I,IAAI,CAAC,GAAG7B,KAAK,CAAC+J,WAAW,CAAC;UACxElI,IAAI,EAAEA;QACR,CAAC,CAAC,GAAG7B,KAAK,CAACgK,WAAW,CAAC;UACrBnI,IAAI,EAAEA;QACR,CAAC,CAAC;MACJ;IACF,CAAC,CAAC;EACJ,CAAC;EACD;AACF;AACA;;EAGE/B,WAAW,CAACS,SAAS,CAACmE,MAAM,GAAG,YAAY;IACzC,IAAI,CAACyD,gBAAgB,CAAC,CAAC;IAEvB,IAAI,CAACzF,eAAe,IAAI,IAAI,CAACA,eAAe,CAACiI,SAAS,CAAC,CAAC;IACxD,IAAI,CAACtK,QAAQ,GAAGC,aAAa,CAAC,CAAC;IAC/B,IAAI,CAACF,MAAM,GAAG,OAAO;IACrB,IAAI,CAACqK,WAAW,IAAI,IAAI,CAACA,WAAW,CAAC/F,MAAM,CAAC,CAAC;EAC/C,CAAC;EAED5E,WAAW,CAACS,SAAS,CAAC6H,OAAO,GAAG,YAAY;IAC1C,IAAI,CAACD,gBAAgB,CAAC,CAAC;EACzB,CAAC;EAEDrI,WAAW,CAACS,SAAS,CAACyJ,WAAW,GAAG,UAAU7I,UAAU,EAAE;IACxD,IAAI,CAACR,GAAG,CAACgI,cAAc,CAAC;MACtBxI,IAAI,EAAE,mBAAmB;MACzByI,IAAI,EAAE,IAAI,CAACC,GAAG;MACdC,QAAQ,EAAE,IAAI,CAACrI,WAAW,CAACsI,EAAE;MAC7B6B,UAAU,EAAEzJ,UAAU,CAACU;IACzB,CAAC,CAAC;EACJ,CAAC;EAED/B,WAAW,CAACS,SAAS,CAACwJ,WAAW,GAAG,UAAU5I,UAAU,EAAE;IACxD,IAAI,CAACR,GAAG,CAACgI,cAAc,CAAC;MACtBxI,IAAI,EAAE,mBAAmB;MACzByI,IAAI,EAAE,IAAI,CAACC,GAAG;MACdC,QAAQ,EAAE,IAAI,CAACrI,WAAW,CAACsI,EAAE;MAC7B6B,UAAU,EAAEzJ,UAAU,CAACU;IACzB,CAAC,CAAC;EACJ,CAAC;EACD;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;EAGE/B,WAAW,CAACS,SAAS,CAACoJ,UAAU,GAAG,UAAU7G,CAAC,EAAEC,CAAC,EAAE;IACjD,IAAI5B,UAAU;IACd,IAAI0J,QAAQ,GAAG,IAAI,CAACpK,WAAW,CAAC+J,WAAW,CAAC,CAAC;IAC7CK,QAAQ,CAACC,QAAQ,CAAC;MAChBC,IAAI,EAAE,cAAc;MACpBC,KAAK,EAAE;IACT,CAAC,EAAE,UAAUnJ,IAAI,EAAE;MACjB,IAAIoJ,IAAI,GAAG,IAAI,CAAC5K,QAAQ,CAAC6K,UAAU,CAACrJ,IAAI,CAACC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC;;MAGzD,IAAImJ,IAAI,EAAE;QACR,IAAIE,KAAK,GAAGF,IAAI,CAACG,qBAAqB,CAACtI,CAAC,EAAEC,CAAC,CAAC;QAC5C,IAAIqD,KAAK,GAAG6E,IAAI,CAAC7E,KAAK,CAAC,CAAC;;QAExB,IAAIA,KAAK,CAACtD,CAAC,IAAIqI,KAAK,CAAC,CAAC,CAAC,IAAIA,KAAK,CAAC,CAAC,CAAC,IAAI/E,KAAK,CAACtD,CAAC,GAAGsD,KAAK,CAACC,KAAK,IAAID,KAAK,CAACrD,CAAC,IAAIoI,KAAK,CAAC,CAAC,CAAC,IAAIA,KAAK,CAAC,CAAC,CAAC,IAAI/E,KAAK,CAACrD,CAAC,GAAGqD,KAAK,CAACG,MAAM,EAAE;UACzHpF,UAAU,GAAG;YACXU,IAAI,EAAEA,IAAI;YACV+H,OAAO,EAAEuB,KAAK,CAAC,CAAC,CAAC;YACjBtB,OAAO,EAAEsB,KAAK,CAAC,CAAC;UAClB,CAAC;QACH,CAAC,MAAM;UACL,OAAO,KAAK,CAAC,CAAC;QAChB;MACF;IACF,CAAC,EAAE,IAAI,CAAC;IACR,OAAOhK,UAAU;EACnB,CAAC;EAEDrB,WAAW,CAACK,IAAI,GAAG,SAAS;EAC5B,OAAOL,WAAW;AACpB,CAAC,CAACvB,SAAS,CAAC;AACZ;AACA;AACA;;AAGA,SAAS+B,aAAaA,CAAA,EAAG;EACvB,OAAO;IACLsB,SAAS,EAAE,EAAE;IACbsJ,UAAU,EAAE,EAAE;IACdG,OAAO,EAAE;EACX,CAAC;AACH;AACA;AACA;AACA;AACA;;AAGA,SAASzH,UAAUA,CAACnD,WAAW,EAAEgB,WAAW,EAAE4B,UAAU,EAAE3B,MAAM,EAAE0B,iBAAiB,EAAEE,gBAAgB,EAAEE,QAAQ,EAAEC,OAAO,EAAEC,WAAW,EAAEC,KAAK,EAAE;EAC5I;EACA,IAAI,CAACH,QAAQ,EAAE;IACb;IACA;IACA;IACA;EACF,CAAC,CAAC;EACF;;EAGA,IAAI8H,UAAU,GAAG9H,QAAQ,CAACkF,SAAS,CAAC,CAAC;EACrC,IAAIyB,IAAI,GAAG1J,WAAW,CAACwC,OAAO,CAAC,CAAC;EAChC,IAAIsI,SAAS,GAAG/H,QAAQ,CAACgI,QAAQ,CAAC,CAAC,CAAC,CAAC;EACrC;;EAEArB,IAAI,CAACsB,gBAAgB,CAACjI,QAAQ,CAAC6G,SAAS,EAAE,IAAI,CAAC;EAE/C,IAAI,CAACiB,UAAU,IAAI,CAACA,UAAU,CAACI,QAAQ,EAAE;IACvC;EACF;EAEA,IAAIC,SAAS,GAAGL,UAAU,CAACjF,KAAK;EAChC,IAAIuF,UAAU,GAAGN,UAAU,CAAC/E,MAAM;EAClC,IAAIsF,WAAW,GAAGP,UAAU,CAACO,WAAW;EACxC,IAAIC,aAAa,GAAGR,UAAU,CAAC7F,SAAS;EACxC,IAAIsG,YAAY,GAAGvI,QAAQ,CAAC1B,WAAW,CAAC,CAAC;EACzC,IAAIkK,WAAW,GAAGvI,OAAO,IAAIA,OAAO,CAAC3B,WAAW,CAAC,CAAC;EAClD,IAAImC,gBAAgB,GAAGT,QAAQ,CAACuB,YAAY;EAC5C,IAAIkH,WAAW,GAAGX,UAAU,CAACW,WAAW;EACxC,IAAIC,QAAQ,GAAGjI,gBAAgB,IAAIA,gBAAgB,CAACkI,MAAM;EAC1D,IAAIC,oBAAoB,GAAGb,SAAS,CAACC,QAAQ,CAAC,WAAW,CAAC;EAC1D,IAAIa,sBAAsB,GAAGd,SAAS,CAACC,QAAQ,CAAC,CAAC,UAAU,EAAE,WAAW,CAAC,CAAC;EAC1E,IAAIc,kBAAkB,GAAGf,SAAS,CAACC,QAAQ,CAAC,CAAC,MAAM,EAAE,WAAW,CAAC,CAAC;EAClE,IAAIe,oBAAoB,GAAGhB,SAAS,CAACC,QAAQ,CAAC,CAAC,QAAQ,EAAE,WAAW,CAAC,CAAC;EACtE,IAAIgB,YAAY,GAAGJ,oBAAoB,CAACjK,GAAG,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC,CAAC;EAClE;EACA;;EAEA,IAAIS,KAAK,GAAG6J,WAAW,CAAC,WAAW,EAAE3N,KAAK,CAAC;EAE3C,IAAI,CAAC8D,KAAK,EAAE;IACV;EACF;EAEAc,WAAW,CAACb,GAAG,CAACD,KAAK,CAAC,CAAC,CAAC;;EAExBA,KAAK,CAACE,CAAC,GAAGwI,UAAU,CAACxI,CAAC,IAAI,CAAC;EAC3BF,KAAK,CAACG,CAAC,GAAGuI,UAAU,CAACvI,CAAC,IAAI,CAAC;EAC3BH,KAAK,CAAC8J,UAAU,CAAC,CAAC;EAClB7M,KAAK,CAAC+C,KAAK,CAAC,CAAC0D,SAAS,GAAGqF,SAAS;EAClC9L,KAAK,CAAC+C,KAAK,CAAC,CAAC4D,UAAU,GAAGoF,UAAU;EAEpC,IAAIN,UAAU,CAACqB,eAAe,EAAE;IAC9B,OAAO/J,KAAK;EACd,CAAC,CAAC;;EAGF,IAAIgK,EAAE,GAAGH,WAAW,CAAC,YAAY,EAAE1N,IAAI,EAAE4E,KAAK,EAAEvE,KAAK,CAAC;EACtDwN,EAAE,IAAIC,gBAAgB,CAACjK,KAAK,EAAEgK,EAAE,EAAEV,QAAQ,IAAIZ,UAAU,CAACwB,gBAAgB,CAAC;EAC1E,IAAIC,aAAa,GAAGxB,SAAS,CAACC,QAAQ,CAAC,UAAU,CAAC;EAClD,IAAIwB,KAAK,GAAGD,aAAa,CAAC5K,GAAG,CAAC,OAAO,CAAC;EACtC,IAAI8K,SAAS,GAAGF,aAAa,CAAC5K,GAAG,CAAC,WAAW,CAAC;EAC9C,IAAI+K,UAAU,GAAGH,aAAa,CAAC5K,GAAG,CAAC,UAAU,CAAC;EAC9C,IAAIgL,cAAc,GAAGH,KAAK,KAAK,UAAU,GAAGxJ,QAAQ,CAAC4J,mBAAmB,CAAC,CAAC,GAAGJ,KAAK,KAAK,YAAY,GAAGxJ,QAAQ,CAAC6J,oBAAoB,CAAC,CAAC,GAAGL,KAAK,CAAC,CAAC;;EAE/I,IAAId,QAAQ,EAAE;IACZ;IACA;IACA;IACA,IAAIxO,oBAAoB,CAACkF,KAAK,CAAC,EAAE;MAC/BjF,uBAAuB,CAACiF,KAAK,EAAE,KAAK,CAAC;IACvC;IAEA,IAAIgK,EAAE,EAAE;MACNjP,uBAAuB,CAACiP,EAAE,EAAE,CAACM,UAAU,CAAC,CAAC,CAAC;;MAE1C/C,IAAI,CAACsB,gBAAgB,CAACjI,QAAQ,CAAC6G,SAAS,EAAEuC,EAAE,CAAC;MAC7C/O,gBAAgB,CAAC+O,EAAE,EAAEO,cAAc,EAAEF,SAAS,CAAC;IACjD;EACF,CAAC,MAAM;IACL,IAAI5B,OAAO,GAAGoB,WAAW,CAAC,SAAS,EAAE1N,IAAI,EAAE4E,KAAK,EAAEtE,UAAU,CAAC;IAC7DgM,OAAO,IAAIiC,aAAa,CAAC1K,KAAK,EAAEyI,OAAO,CAAC;IACxCuB,EAAE,CAACW,eAAe,GAAG,IAAI;IAEzB,IAAIX,EAAE,IAAIlP,oBAAoB,CAACkP,EAAE,CAAC,EAAE;MAClCjP,uBAAuB,CAACiP,EAAE,EAAE,KAAK,CAAC;IACpC;IAEAjP,uBAAuB,CAACiF,KAAK,EAAE,CAACsK,UAAU,CAAC,CAAC,CAAC;;IAE7C/C,IAAI,CAACsB,gBAAgB,CAACjI,QAAQ,CAAC6G,SAAS,EAAEzH,KAAK,CAAC;IAChD/E,gBAAgB,CAAC+E,KAAK,EAAEuK,cAAc,EAAEF,SAAS,CAAC;EACpD;EAEA,OAAOrK,KAAK,CAAC,CAAC;EACd;EACA;;EAEA,SAASiK,gBAAgBA,CAACjK,KAAK,EAAEgK,EAAE,EAAEY,aAAa,EAAE;IAClD,IAAIC,MAAM,GAAGhQ,SAAS,CAACmP,EAAE,CAAC,CAAC,CAAC;;IAE5Ba,MAAM,CAACpD,SAAS,GAAG7G,QAAQ,CAAC6G,SAAS;IACrCoD,MAAM,CAACC,WAAW,GAAGjN,WAAW,CAACiN,WAAW;IAC5Cd,EAAE,CAAC3F,QAAQ,CAAC;MACVnE,CAAC,EAAE,CAAC;MACJC,CAAC,EAAE,CAAC;MACJsD,KAAK,EAAEsF,SAAS;MAChBpF,MAAM,EAAEqF,UAAU;MAClB+B,CAAC,EAAEnB;IACL,CAAC,CAAC;IAEF,IAAIV,aAAa,EAAE;MACjB;MACA;MACA;MACA8B,gBAAgB,CAAChB,EAAE,CAAC;IACtB,CAAC,MAAM;MACLA,EAAE,CAACnH,SAAS,GAAG,KAAK;MACpB,IAAIgB,KAAK,GAAGjD,QAAQ,CAACqK,SAAS,CAAC,OAAO,CAAC;MACvC,IAAIC,iBAAiB,GAAGrH,KAAK,CAAC/G,MAAM;MACpC,IAAIqO,WAAW,GAAGxO,kBAAkB,CAAC6M,oBAAoB,CAAC;MAC1D2B,WAAW,CAACpO,IAAI,GAAGmO,iBAAiB;MACpC,IAAIE,aAAa,GAAG1O,iBAAiB,CAAC+M,sBAAsB,CAAC;MAC7D2B,aAAa,CAACrO,IAAI,GAAG0M,sBAAsB,CAAClK,GAAG,CAAC,aAAa,CAAC;MAC9D,IAAI8L,SAAS,GAAG3O,iBAAiB,CAACgN,kBAAkB,CAAC;MACrD2B,SAAS,CAACtO,IAAI,GAAG2M,kBAAkB,CAACnK,GAAG,CAAC,aAAa,CAAC;MACtD,IAAI+L,WAAW,GAAG5O,iBAAiB,CAACiN,oBAAoB,CAAC;MACzD2B,WAAW,CAACvO,IAAI,GAAG4M,oBAAoB,CAACpK,GAAG,CAAC,aAAa,CAAC;MAE1D,IAAIqL,aAAa,EAAE;QACjB,IAAIW,eAAe,GAAGxC,SAAS,GAAG,CAAC,GAAGE,WAAW;QACjDuC,WAAW;QAAE;QACbxB,EAAE,EAAEkB,iBAAiB,EAAErH,KAAK,CAACC,OAAO,EAAE;UACpC5D,CAAC,EAAE+I,WAAW;UACd9I,CAAC,EAAE,CAAC;UACJsD,KAAK,EAAE8H,eAAe;UACtB5H,MAAM,EAAE0F;QACV,CAAC,CAAC;MACJ,CAAC,CAAC;MAAA,KACG;QACDW,EAAE,CAACyB,iBAAiB,CAAC,CAAC;MACxB;MAEFzB,EAAE,CAACzF,QAAQ,CAAC4G,WAAW,CAAC;MACxBnB,EAAE,CAAC0B,WAAW,CAAC,UAAU,CAAC,CAAC7H,KAAK,GAAGuH,aAAa;MAChDpB,EAAE,CAAC0B,WAAW,CAAC,MAAM,CAAC,CAAC7H,KAAK,GAAGwH,SAAS;MACxCrB,EAAE,CAAC0B,WAAW,CAAC,QAAQ,CAAC,CAAC7H,KAAK,GAAGyH,WAAW;MAC5CtQ,oBAAoB,CAACgP,EAAE,CAAC;IAC1B;IAEAhK,KAAK,CAACC,GAAG,CAAC+J,EAAE,CAAC;EACf;EAEA,SAASU,aAAaA,CAAC1K,KAAK,EAAEyI,OAAO,EAAE;IACrC,IAAIoC,MAAM,GAAGhQ,SAAS,CAAC4N,OAAO,CAAC,CAAC,CAAC;;IAEjCoC,MAAM,CAACpD,SAAS,GAAG7G,QAAQ,CAAC6G,SAAS;IACrCoD,MAAM,CAACC,WAAW,GAAGjN,WAAW,CAACiN,WAAW;IAC5C,IAAIa,YAAY,GAAGlG,IAAI,CAACmG,GAAG,CAAC7C,SAAS,GAAG,CAAC,GAAGE,WAAW,EAAE,CAAC,CAAC;IAC3D,IAAI4C,aAAa,GAAGpG,IAAI,CAACmG,GAAG,CAAC5C,UAAU,GAAG,CAAC,GAAGC,WAAW,EAAE,CAAC,CAAC;IAC7DR,OAAO,CAACqD,OAAO,GAAG,IAAI;IACtBrD,OAAO,CAACpE,QAAQ,CAAC;MACfnE,CAAC,EAAE+I,WAAW;MACd9I,CAAC,EAAE8I,WAAW;MACdxF,KAAK,EAAEkI,YAAY;MACnBhI,MAAM,EAAEkI,aAAa;MACrBd,CAAC,EAAEnB;IACL,CAAC,CAAC;IAEF,IAAIV,aAAa,EAAE;MACjB;MACA;MACA;MACA8B,gBAAgB,CAACvC,OAAO,CAAC;IAC3B,CAAC,MAAM;MACLA,OAAO,CAAC5F,SAAS,GAAG,KAAK;MACzB,IAAIkJ,SAAS,GAAGnL,QAAQ,CAACqK,SAAS,CAAC,OAAO,CAAC;MAC3C,IAAIe,WAAW,GAAGD,SAAS,CAAChP,IAAI;MAChC,IAAIoO,WAAW,GAAGxO,kBAAkB,CAAC6M,oBAAoB,CAAC;MAC1D2B,WAAW,CAACpO,IAAI,GAAGiP,WAAW;MAC9Bb,WAAW,CAACc,KAAK,GAAGF,SAAS,CAACE,KAAK;MACnC,IAAIb,aAAa,GAAG1O,iBAAiB,CAAC+M,sBAAsB,CAAC;MAC7D,IAAI4B,SAAS,GAAG3O,iBAAiB,CAACgN,kBAAkB,CAAC;MACrD,IAAI4B,WAAW,GAAG5O,iBAAiB,CAACiN,oBAAoB,CAAC,CAAC,CAAC;;MAE3D6B,WAAW,CAAC/C,OAAO,EAAEuD,WAAW,EAAED,SAAS,CAACjI,OAAO,EAAE,IAAI,CAAC;MAC1D2E,OAAO,CAAClE,QAAQ,CAAC4G,WAAW,CAAC;MAC7B1C,OAAO,CAACiD,WAAW,CAAC,UAAU,CAAC,CAAC7H,KAAK,GAAGuH,aAAa;MACrD3C,OAAO,CAACiD,WAAW,CAAC,MAAM,CAAC,CAAC7H,KAAK,GAAGwH,SAAS;MAC7C5C,OAAO,CAACiD,WAAW,CAAC,QAAQ,CAAC,CAAC7H,KAAK,GAAGyH,WAAW;MACjDtQ,oBAAoB,CAACyN,OAAO,CAAC;IAC/B;IAEAzI,KAAK,CAACC,GAAG,CAACwI,OAAO,CAAC;EACpB;EAEA,SAASuC,gBAAgBA,CAACkB,OAAO,EAAE;IACjC;IACA;IACA,CAACA,OAAO,CAACrJ,SAAS,IAAInC,gBAAgB,CAAC+B,IAAI,CAACyJ,OAAO,CAAC;EACtD;EAEA,SAASV,WAAWA,CAACW,MAAM,EAAEH,WAAW,EAAEI,aAAa;EAAE;EACzDC,cAAc,EAAE;IACd,IAAIC,gBAAgB,GAAG3D,SAAS,CAACC,QAAQ,CAACyD,cAAc,GAAG/P,sBAAsB,GAAGD,iBAAiB,CAAC;IACtG,IAAIkQ,WAAW,GAAGzQ,mBAAmB,CAAC6M,SAAS,CAACpJ,GAAG,CAAC,MAAM,CAAC,EAAE,IAAI,CAAC;IAClE,IAAIiN,MAAM,GAAGF,gBAAgB,CAACG,UAAU,CAAC,MAAM,CAAC;IAChDzQ,aAAa,CAACmQ,MAAM,EAAElQ,oBAAoB,CAAC0M,SAAS,EAAE0D,cAAc,GAAG/P,sBAAsB,GAAGD,iBAAiB,CAAC,EAAE;MAClHkQ,WAAW,EAAEC,MAAM,GAAGD,WAAW,GAAG,IAAI;MACxCG,YAAY,EAAEV,WAAW;MACzBW,cAAc,EAAEP,aAAa;MAC7BQ,YAAY,EAAE/O,WAAW;MACzBgP,cAAc,EAAEjM,QAAQ,CAAC6G;IAC3B,CAAC,CAAC;IACF,IAAIqF,MAAM,GAAGX,MAAM,CAACY,cAAc,CAAC,CAAC;IAEpC,IAAI,CAACD,MAAM,EAAE;MACX;IACF;IAEA,IAAIE,SAAS,GAAGF,MAAM,CAACjJ,KAAK;IAC5B,IAAIoJ,WAAW,GAAGvS,iBAAiB,CAACsS,SAAS,CAACE,OAAO,IAAI,CAAC,CAAC;IAE3D,IAAIb,cAAc,EAAE;MAClBF,MAAM,CAACgB,aAAa,CAAC;QACnBC,UAAU,EAAEf;MACd,CAAC,CAAC;MACFS,MAAM,CAACO,kBAAkB,GAAG,IAAI;IAClC;IAEAP,MAAM,CAACQ,YAAY,GAAG,YAAY;MAChC,IAAI7J,KAAK,GAAGgC,IAAI,CAACmG,GAAG,CAAC,CAACS,cAAc,GAAGA,cAAc,CAAC5I,KAAK,GAAG0I,MAAM,CAAC3I,KAAK,CAACC,KAAK,IAAIwJ,WAAW,CAAC,CAAC,CAAC,GAAGA,WAAW,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;MACvH,IAAItJ,MAAM,GAAG8B,IAAI,CAACmG,GAAG,CAAC,CAACS,cAAc,GAAGA,cAAc,CAAC1I,MAAM,GAAGwI,MAAM,CAAC3I,KAAK,CAACG,MAAM,IAAIsJ,WAAW,CAAC,CAAC,CAAC,GAAGA,WAAW,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;MAE1H,IAAID,SAAS,CAACvJ,KAAK,KAAKA,KAAK,IAAIuJ,SAAS,CAACrJ,MAAM,KAAKA,MAAM,EAAE;QAC5DmJ,MAAM,CAACvI,QAAQ,CAAC;UACdd,KAAK,EAAEA,KAAK;UACZE,MAAM,EAAEA;QACV,CAAC,CAAC;MACJ;IACF,CAAC;IAEDqJ,SAAS,CAACO,eAAe,GAAG,CAAC;IAC7BP,SAAS,CAACQ,YAAY,GAAG,UAAU;IACnCC,gBAAgB,CAACT,SAAS,EAAEX,cAAc,EAAE3D,UAAU,CAAC;IACvD,IAAIgF,iBAAiB,GAAGZ,MAAM,CAACa,QAAQ,CAAC,UAAU,CAAC;IACnDF,gBAAgB,CAACC,iBAAiB,GAAGA,iBAAiB,CAAC7J,KAAK,GAAG,IAAI,EAAEwI,cAAc,EAAE3D,UAAU,CAAC;EAClG;EAEA,SAAS+E,gBAAgBA,CAAC5J,KAAK,EAAEwI,cAAc,EAAE3D,UAAU,EAAE;IAC3D,IAAIkF,IAAI,GAAG/J,KAAK,GAAGA,KAAK,CAAC+J,IAAI,GAAG,IAAI;IAEpC,IAAI,CAACvB,cAAc,IAAI3D,UAAU,CAACxB,UAAU,IAAI0G,IAAI,IAAI,IAAI,EAAE;MAC5D,IAAIC,QAAQ,GAAGhQ,WAAW,CAAC0B,GAAG,CAAC,eAAe,EAAE,IAAI,CAAC;MACrDsE,KAAK,CAAC+J,IAAI,GAAGC,QAAQ,GAAGA,QAAQ,GAAG,GAAG,GAAGD,IAAI,GAAGA,IAAI;IACtD;EACF;EAEA,SAAS/D,WAAWA,CAACvH,WAAW,EAAEwL,IAAI,EAAE/M,KAAK,EAAEgN,CAAC,EAAE;IAChD,IAAI7B,OAAO,GAAG9C,WAAW,IAAI,IAAI,IAAI3I,UAAU,CAAC6B,WAAW,CAAC,CAAC8G,WAAW,CAAC;IACzE,IAAI4E,KAAK,GAAGxN,iBAAiB,CAAC8B,WAAW,CAAC;IAE1C,IAAI4J,OAAO,EAAE;MACX;MACAzL,UAAU,CAAC6B,WAAW,CAAC,CAAC8G,WAAW,CAAC,GAAG,IAAI;MAC3C6E,0BAA0B,CAACD,KAAK,EAAE9B,OAAO,CAAC;IAC5C,CAAC,CAAC;IAAA,KACG,IAAI,CAAChD,aAAa,EAAE;MACrBgD,OAAO,GAAG,IAAI4B,IAAI,CAAC,CAAC;MAEpB,IAAI5B,OAAO,YAAYtQ,WAAW,EAAE;QAClCsQ,OAAO,CAACgC,EAAE,GAAGC,WAAW,CAACpN,KAAK,EAAEgN,CAAC,CAAC;MACpC;MAEAK,yBAAyB,CAACJ,KAAK,EAAE9B,OAAO,CAAC;IAC3C,CAAC,CAAC;;IAGJ,OAAOrN,WAAW,CAACyD,WAAW,CAAC,CAAC6G,YAAY,CAAC,GAAG+C,OAAO;EACzD;EAEA,SAAS+B,0BAA0BA,CAACD,KAAK,EAAE9B,OAAO,EAAE;IAClD,IAAImC,OAAO,GAAGL,KAAK,CAAC7E,YAAY,CAAC,GAAG,CAAC,CAAC;IAEtC,IAAI+C,OAAO,YAAYhQ,KAAK,EAAE;MAC5BmS,OAAO,CAACnK,IAAI,GAAGgI,OAAO,CAAChM,CAAC;MACxBmO,OAAO,CAAClK,IAAI,GAAG+H,OAAO,CAAC/L,CAAC;IAC1B,CAAC,MAAM;MACLkO,OAAO,CAACjK,QAAQ,GAAG3J,MAAM,CAAC,CAAC,CAAC,EAAEyR,OAAO,CAAC1I,KAAK,CAAC;IAC9C;EACF,CAAC,CAAC;EACF;;EAGA,SAAS4K,yBAAyBA,CAACJ,KAAK,EAAE9B,OAAO,EAAE;IACjD,IAAImC,OAAO,GAAGL,KAAK,CAAC7E,YAAY,CAAC,GAAG,CAAC,CAAC;IACtC,IAAImF,UAAU,GAAG1N,QAAQ,CAAC0N,UAAU;IACpC,IAAIC,OAAO,GAAGrC,OAAO,YAAYtR,OAAO,CAACsB,KAAK;IAE9C,IAAIoS,UAAU,KAAK,CAACxP,MAAM,IAAIA,MAAM,CAACK,SAAS,KAAK,WAAW,CAAC,EAAE;MAC/D,IAAIqP,UAAU,GAAG,CAAC;MAClB,IAAIC,UAAU,GAAG,CAAC,CAAC,CAAC;MACpB;;MAEA,IAAIC,WAAW,GAAGlO,iBAAiB,CAAC8H,UAAU,CAACgG,UAAU,CAACpP,WAAW,CAAC,CAAC,CAAC;MAExE,IAAI,CAACJ,MAAM,IAAI4P,WAAW,IAAIA,WAAW,CAACtK,QAAQ,EAAE;QAClDoK,UAAU,GAAGE,WAAW,CAACtK,QAAQ,CAACX,KAAK;QACvCgL,UAAU,GAAGC,WAAW,CAACtK,QAAQ,CAACT,MAAM;MAC1C,CAAC,CAAC;MACF;;MAGA,IAAI4K,OAAO,EAAE;QACXF,OAAO,CAACnK,IAAI,GAAG,CAAC;QAChBmK,OAAO,CAAClK,IAAI,GAAGsK,UAAU;MAC3B,CAAC,MAAM;QACLJ,OAAO,CAACjK,QAAQ,GAAG;UACjBlE,CAAC,EAAEsO,UAAU;UACbrO,CAAC,EAAEsO,UAAU;UACbhL,KAAK,EAAE,CAAC;UACRE,MAAM,EAAE;QACV,CAAC;MACH;IACF,CAAC,CAAC;;IAGF0K,OAAO,CAAC/J,MAAM,GAAG,CAACiK,OAAO;EAC3B;AACF,CAAC,CAAC;AACF;AACA;AACA;AACA;AACA;;AAGA,SAASJ,WAAWA,CAACpN,KAAK,EAAE4N,SAAS,EAAE;EACrC,OAAO5N,KAAK,GAAGxE,OAAO,GAAGoS,SAAS;AACpC;AAEA,eAAezR,WAAW"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}