{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { Point, Path, Polyline } from '../util/graphic.js';\nimport PathProxy from 'zrender/lib/core/PathProxy.js';\nimport { normalizeRadian } from 'zrender/lib/contain/util.js';\nimport { cubicProjectPoint, quadraticProjectPoint } from 'zrender/lib/core/curve.js';\nimport { defaults, retrieve2 } from 'zrender/lib/core/util.js';\nimport { invert } from 'zrender/lib/core/matrix.js';\nimport * as vector from 'zrender/lib/core/vector.js';\nimport { DISPLAY_STATES, SPECIAL_STATES } from '../util/states.js';\nvar PI2 = Math.PI * 2;\nvar CMD = PathProxy.CMD;\nvar DEFAULT_SEARCH_SPACE = ['top', 'right', 'bottom', 'left'];\nfunction getCandidateAnchor(pos, distance, rect, outPt, outDir) {\n  var width = rect.width;\n  var height = rect.height;\n  switch (pos) {\n    case 'top':\n      outPt.set(rect.x + width / 2, rect.y - distance);\n      outDir.set(0, -1);\n      break;\n    case 'bottom':\n      outPt.set(rect.x + width / 2, rect.y + height + distance);\n      outDir.set(0, 1);\n      break;\n    case 'left':\n      outPt.set(rect.x - distance, rect.y + height / 2);\n      outDir.set(-1, 0);\n      break;\n    case 'right':\n      outPt.set(rect.x + width + distance, rect.y + height / 2);\n      outDir.set(1, 0);\n      break;\n  }\n}\nfunction projectPointToArc(cx, cy, r, startAngle, endAngle, anticlockwise, x, y, out) {\n  x -= cx;\n  y -= cy;\n  var d = Math.sqrt(x * x + y * y);\n  x /= d;\n  y /= d; // Intersect point.\n\n  var ox = x * r + cx;\n  var oy = y * r + cy;\n  if (Math.abs(startAngle - endAngle) % PI2 < 1e-4) {\n    // Is a circle\n    out[0] = ox;\n    out[1] = oy;\n    return d - r;\n  }\n  if (anticlockwise) {\n    var tmp = startAngle;\n    startAngle = normalizeRadian(endAngle);\n    endAngle = normalizeRadian(tmp);\n  } else {\n    startAngle = normalizeRadian(startAngle);\n    endAngle = normalizeRadian(endAngle);\n  }\n  if (startAngle > endAngle) {\n    endAngle += PI2;\n  }\n  var angle = Math.atan2(y, x);\n  if (angle < 0) {\n    angle += PI2;\n  }\n  if (angle >= startAngle && angle <= endAngle || angle + PI2 >= startAngle && angle + PI2 <= endAngle) {\n    // Project point is on the arc.\n    out[0] = ox;\n    out[1] = oy;\n    return d - r;\n  }\n  var x1 = r * Math.cos(startAngle) + cx;\n  var y1 = r * Math.sin(startAngle) + cy;\n  var x2 = r * Math.cos(endAngle) + cx;\n  var y2 = r * Math.sin(endAngle) + cy;\n  var d1 = (x1 - x) * (x1 - x) + (y1 - y) * (y1 - y);\n  var d2 = (x2 - x) * (x2 - x) + (y2 - y) * (y2 - y);\n  if (d1 < d2) {\n    out[0] = x1;\n    out[1] = y1;\n    return Math.sqrt(d1);\n  } else {\n    out[0] = x2;\n    out[1] = y2;\n    return Math.sqrt(d2);\n  }\n}\nfunction projectPointToLine(x1, y1, x2, y2, x, y, out, limitToEnds) {\n  var dx = x - x1;\n  var dy = y - y1;\n  var dx1 = x2 - x1;\n  var dy1 = y2 - y1;\n  var lineLen = Math.sqrt(dx1 * dx1 + dy1 * dy1);\n  dx1 /= lineLen;\n  dy1 /= lineLen; // dot product\n\n  var projectedLen = dx * dx1 + dy * dy1;\n  var t = projectedLen / lineLen;\n  if (limitToEnds) {\n    t = Math.min(Math.max(t, 0), 1);\n  }\n  t *= lineLen;\n  var ox = out[0] = x1 + t * dx1;\n  var oy = out[1] = y1 + t * dy1;\n  return Math.sqrt((ox - x) * (ox - x) + (oy - y) * (oy - y));\n}\nfunction projectPointToRect(x1, y1, width, height, x, y, out) {\n  if (width < 0) {\n    x1 = x1 + width;\n    width = -width;\n  }\n  if (height < 0) {\n    y1 = y1 + height;\n    height = -height;\n  }\n  var x2 = x1 + width;\n  var y2 = y1 + height;\n  var ox = out[0] = Math.min(Math.max(x, x1), x2);\n  var oy = out[1] = Math.min(Math.max(y, y1), y2);\n  return Math.sqrt((ox - x) * (ox - x) + (oy - y) * (oy - y));\n}\nvar tmpPt = [];\nfunction nearestPointOnRect(pt, rect, out) {\n  var dist = projectPointToRect(rect.x, rect.y, rect.width, rect.height, pt.x, pt.y, tmpPt);\n  out.set(tmpPt[0], tmpPt[1]);\n  return dist;\n}\n/**\r\n * Calculate min distance corresponding point.\r\n * This method won't evaluate if point is in the path.\r\n */\n\nfunction nearestPointOnPath(pt, path, out) {\n  var xi = 0;\n  var yi = 0;\n  var x0 = 0;\n  var y0 = 0;\n  var x1;\n  var y1;\n  var minDist = Infinity;\n  var data = path.data;\n  var x = pt.x;\n  var y = pt.y;\n  for (var i = 0; i < data.length;) {\n    var cmd = data[i++];\n    if (i === 1) {\n      xi = data[i];\n      yi = data[i + 1];\n      x0 = xi;\n      y0 = yi;\n    }\n    var d = minDist;\n    switch (cmd) {\n      case CMD.M:\n        // moveTo 命令重新创建一个新的 subpath, 并且更新新的起点\n        // 在 closePath 的时候使用\n        x0 = data[i++];\n        y0 = data[i++];\n        xi = x0;\n        yi = y0;\n        break;\n      case CMD.L:\n        d = projectPointToLine(xi, yi, data[i], data[i + 1], x, y, tmpPt, true);\n        xi = data[i++];\n        yi = data[i++];\n        break;\n      case CMD.C:\n        d = cubicProjectPoint(xi, yi, data[i++], data[i++], data[i++], data[i++], data[i], data[i + 1], x, y, tmpPt);\n        xi = data[i++];\n        yi = data[i++];\n        break;\n      case CMD.Q:\n        d = quadraticProjectPoint(xi, yi, data[i++], data[i++], data[i], data[i + 1], x, y, tmpPt);\n        xi = data[i++];\n        yi = data[i++];\n        break;\n      case CMD.A:\n        // TODO Arc 判断的开销比较大\n        var cx = data[i++];\n        var cy = data[i++];\n        var rx = data[i++];\n        var ry = data[i++];\n        var theta = data[i++];\n        var dTheta = data[i++]; // TODO Arc 旋转\n\n        i += 1;\n        var anticlockwise = !!(1 - data[i++]);\n        x1 = Math.cos(theta) * rx + cx;\n        y1 = Math.sin(theta) * ry + cy; // 不是直接使用 arc 命令\n\n        if (i <= 1) {\n          // 第一个命令起点还未定义\n          x0 = x1;\n          y0 = y1;\n        } // zr 使用scale来模拟椭圆, 这里也对x做一定的缩放\n\n        var _x = (x - cx) * ry / rx + cx;\n        d = projectPointToArc(cx, cy, ry, theta, theta + dTheta, anticlockwise, _x, y, tmpPt);\n        xi = Math.cos(theta + dTheta) * rx + cx;\n        yi = Math.sin(theta + dTheta) * ry + cy;\n        break;\n      case CMD.R:\n        x0 = xi = data[i++];\n        y0 = yi = data[i++];\n        var width = data[i++];\n        var height = data[i++];\n        d = projectPointToRect(x0, y0, width, height, x, y, tmpPt);\n        break;\n      case CMD.Z:\n        d = projectPointToLine(xi, yi, x0, y0, x, y, tmpPt, true);\n        xi = x0;\n        yi = y0;\n        break;\n    }\n    if (d < minDist) {\n      minDist = d;\n      out.set(tmpPt[0], tmpPt[1]);\n    }\n  }\n  return minDist;\n} // Temporal variable for intermediate usage.\n\nvar pt0 = new Point();\nvar pt1 = new Point();\nvar pt2 = new Point();\nvar dir = new Point();\nvar dir2 = new Point();\n/**\r\n * Calculate a proper guide line based on the label position and graphic element definition\r\n * @param label\r\n * @param labelRect\r\n * @param target\r\n * @param targetRect\r\n */\n\nexport function updateLabelLinePoints(target, labelLineModel) {\n  if (!target) {\n    return;\n  }\n  var labelLine = target.getTextGuideLine();\n  var label = target.getTextContent(); // Needs to create text guide in each charts.\n\n  if (!(label && labelLine)) {\n    return;\n  }\n  var labelGuideConfig = target.textGuideLineConfig || {};\n  var points = [[0, 0], [0, 0], [0, 0]];\n  var searchSpace = labelGuideConfig.candidates || DEFAULT_SEARCH_SPACE;\n  var labelRect = label.getBoundingRect().clone();\n  labelRect.applyTransform(label.getComputedTransform());\n  var minDist = Infinity;\n  var anchorPoint = labelGuideConfig.anchor;\n  var targetTransform = target.getComputedTransform();\n  var targetInversedTransform = targetTransform && invert([], targetTransform);\n  var len = labelLineModel.get('length2') || 0;\n  if (anchorPoint) {\n    pt2.copy(anchorPoint);\n  }\n  for (var i = 0; i < searchSpace.length; i++) {\n    var candidate = searchSpace[i];\n    getCandidateAnchor(candidate, 0, labelRect, pt0, dir);\n    Point.scaleAndAdd(pt1, pt0, dir, len); // Transform to target coord space.\n\n    pt1.transform(targetInversedTransform); // Note: getBoundingRect will ensure the `path` being created.\n\n    var boundingRect = target.getBoundingRect();\n    var dist = anchorPoint ? anchorPoint.distance(pt1) : target instanceof Path ? nearestPointOnPath(pt1, target.path, pt2) : nearestPointOnRect(pt1, boundingRect, pt2); // TODO pt2 is in the path\n\n    if (dist < minDist) {\n      minDist = dist; // Transform back to global space.\n\n      pt1.transform(targetTransform);\n      pt2.transform(targetTransform);\n      pt2.toArray(points[0]);\n      pt1.toArray(points[1]);\n      pt0.toArray(points[2]);\n    }\n  }\n  limitTurnAngle(points, labelLineModel.get('minTurnAngle'));\n  labelLine.setShape({\n    points: points\n  });\n} // Temporal variable for the limitTurnAngle function\n\nvar tmpArr = [];\nvar tmpProjPoint = new Point();\n/**\r\n * Reduce the line segment attached to the label to limit the turn angle between two segments.\r\n * @param linePoints\r\n * @param minTurnAngle Radian of minimum turn angle. 0 - 180\r\n */\n\nexport function limitTurnAngle(linePoints, minTurnAngle) {\n  if (!(minTurnAngle <= 180 && minTurnAngle > 0)) {\n    return;\n  }\n  minTurnAngle = minTurnAngle / 180 * Math.PI; // The line points can be\n  //      /pt1----pt2 (label)\n  //     /\n  // pt0/\n\n  pt0.fromArray(linePoints[0]);\n  pt1.fromArray(linePoints[1]);\n  pt2.fromArray(linePoints[2]);\n  Point.sub(dir, pt0, pt1);\n  Point.sub(dir2, pt2, pt1);\n  var len1 = dir.len();\n  var len2 = dir2.len();\n  if (len1 < 1e-3 || len2 < 1e-3) {\n    return;\n  }\n  dir.scale(1 / len1);\n  dir2.scale(1 / len2);\n  var angleCos = dir.dot(dir2);\n  var minTurnAngleCos = Math.cos(minTurnAngle);\n  if (minTurnAngleCos < angleCos) {\n    // Smaller than minTurnAngle\n    // Calculate project point of pt0 on pt1-pt2\n    var d = projectPointToLine(pt1.x, pt1.y, pt2.x, pt2.y, pt0.x, pt0.y, tmpArr, false);\n    tmpProjPoint.fromArray(tmpArr); // Calculate new projected length with limited minTurnAngle and get the new connect point\n\n    tmpProjPoint.scaleAndAdd(dir2, d / Math.tan(Math.PI - minTurnAngle)); // Limit the new calculated connect point between pt1 and pt2.\n\n    var t = pt2.x !== pt1.x ? (tmpProjPoint.x - pt1.x) / (pt2.x - pt1.x) : (tmpProjPoint.y - pt1.y) / (pt2.y - pt1.y);\n    if (isNaN(t)) {\n      return;\n    }\n    if (t < 0) {\n      Point.copy(tmpProjPoint, pt1);\n    } else if (t > 1) {\n      Point.copy(tmpProjPoint, pt2);\n    }\n    tmpProjPoint.toArray(linePoints[1]);\n  }\n}\n/**\r\n * Limit the angle of line and the surface\r\n * @param maxSurfaceAngle Radian of minimum turn angle. 0 - 180. 0 is same direction to normal. 180 is opposite\r\n */\n\nexport function limitSurfaceAngle(linePoints, surfaceNormal, maxSurfaceAngle) {\n  if (!(maxSurfaceAngle <= 180 && maxSurfaceAngle > 0)) {\n    return;\n  }\n  maxSurfaceAngle = maxSurfaceAngle / 180 * Math.PI;\n  pt0.fromArray(linePoints[0]);\n  pt1.fromArray(linePoints[1]);\n  pt2.fromArray(linePoints[2]);\n  Point.sub(dir, pt1, pt0);\n  Point.sub(dir2, pt2, pt1);\n  var len1 = dir.len();\n  var len2 = dir2.len();\n  if (len1 < 1e-3 || len2 < 1e-3) {\n    return;\n  }\n  dir.scale(1 / len1);\n  dir2.scale(1 / len2);\n  var angleCos = dir.dot(surfaceNormal);\n  var maxSurfaceAngleCos = Math.cos(maxSurfaceAngle);\n  if (angleCos < maxSurfaceAngleCos) {\n    // Calculate project point of pt0 on pt1-pt2\n    var d = projectPointToLine(pt1.x, pt1.y, pt2.x, pt2.y, pt0.x, pt0.y, tmpArr, false);\n    tmpProjPoint.fromArray(tmpArr);\n    var HALF_PI = Math.PI / 2;\n    var angle2 = Math.acos(dir2.dot(surfaceNormal));\n    var newAngle = HALF_PI + angle2 - maxSurfaceAngle;\n    if (newAngle >= HALF_PI) {\n      // parallel\n      Point.copy(tmpProjPoint, pt2);\n    } else {\n      // Calculate new projected length with limited minTurnAngle and get the new connect point\n      tmpProjPoint.scaleAndAdd(dir2, d / Math.tan(Math.PI / 2 - newAngle)); // Limit the new calculated connect point between pt1 and pt2.\n\n      var t = pt2.x !== pt1.x ? (tmpProjPoint.x - pt1.x) / (pt2.x - pt1.x) : (tmpProjPoint.y - pt1.y) / (pt2.y - pt1.y);\n      if (isNaN(t)) {\n        return;\n      }\n      if (t < 0) {\n        Point.copy(tmpProjPoint, pt1);\n      } else if (t > 1) {\n        Point.copy(tmpProjPoint, pt2);\n      }\n    }\n    tmpProjPoint.toArray(linePoints[1]);\n  }\n}\nfunction setLabelLineState(labelLine, ignore, stateName, stateModel) {\n  var isNormal = stateName === 'normal';\n  var stateObj = isNormal ? labelLine : labelLine.ensureState(stateName); // Make sure display.\n\n  stateObj.ignore = ignore; // Set smooth\n\n  var smooth = stateModel.get('smooth');\n  if (smooth && smooth === true) {\n    smooth = 0.3;\n  }\n  stateObj.shape = stateObj.shape || {};\n  if (smooth > 0) {\n    stateObj.shape.smooth = smooth;\n  }\n  var styleObj = stateModel.getModel('lineStyle').getLineStyle();\n  isNormal ? labelLine.useStyle(styleObj) : stateObj.style = styleObj;\n}\nfunction buildLabelLinePath(path, shape) {\n  var smooth = shape.smooth;\n  var points = shape.points;\n  if (!points) {\n    return;\n  }\n  path.moveTo(points[0][0], points[0][1]);\n  if (smooth > 0 && points.length >= 3) {\n    var len1 = vector.dist(points[0], points[1]);\n    var len2 = vector.dist(points[1], points[2]);\n    if (!len1 || !len2) {\n      path.lineTo(points[1][0], points[1][1]);\n      path.lineTo(points[2][0], points[2][1]);\n      return;\n    }\n    var moveLen = Math.min(len1, len2) * smooth;\n    var midPoint0 = vector.lerp([], points[1], points[0], moveLen / len1);\n    var midPoint2 = vector.lerp([], points[1], points[2], moveLen / len2);\n    var midPoint1 = vector.lerp([], midPoint0, midPoint2, 0.5);\n    path.bezierCurveTo(midPoint0[0], midPoint0[1], midPoint0[0], midPoint0[1], midPoint1[0], midPoint1[1]);\n    path.bezierCurveTo(midPoint2[0], midPoint2[1], midPoint2[0], midPoint2[1], points[2][0], points[2][1]);\n  } else {\n    for (var i = 1; i < points.length; i++) {\n      path.lineTo(points[i][0], points[i][1]);\n    }\n  }\n}\n/**\r\n * Create a label line if necessary and set it's style.\r\n */\n\nexport function setLabelLineStyle(targetEl, statesModels, defaultStyle) {\n  var labelLine = targetEl.getTextGuideLine();\n  var label = targetEl.getTextContent();\n  if (!label) {\n    // Not show label line if there is no label.\n    if (labelLine) {\n      targetEl.removeTextGuideLine();\n    }\n    return;\n  }\n  var normalModel = statesModels.normal;\n  var showNormal = normalModel.get('show');\n  var labelIgnoreNormal = label.ignore;\n  for (var i = 0; i < DISPLAY_STATES.length; i++) {\n    var stateName = DISPLAY_STATES[i];\n    var stateModel = statesModels[stateName];\n    var isNormal = stateName === 'normal';\n    if (stateModel) {\n      var stateShow = stateModel.get('show');\n      var isLabelIgnored = isNormal ? labelIgnoreNormal : retrieve2(label.states[stateName] && label.states[stateName].ignore, labelIgnoreNormal);\n      if (isLabelIgnored // Not show when label is not shown in this state.\n      || !retrieve2(stateShow, showNormal) // Use normal state by default if not set.\n      ) {\n        var stateObj = isNormal ? labelLine : labelLine && labelLine.states[stateName];\n        if (stateObj) {\n          stateObj.ignore = true;\n        }\n        continue;\n      } // Create labelLine if not exists\n\n      if (!labelLine) {\n        labelLine = new Polyline();\n        targetEl.setTextGuideLine(labelLine); // Reset state of normal because it's new created.\n        // NOTE: NORMAL should always been the first!\n\n        if (!isNormal && (labelIgnoreNormal || !showNormal)) {\n          setLabelLineState(labelLine, true, 'normal', statesModels.normal);\n        } // Use same state proxy.\n\n        if (targetEl.stateProxy) {\n          labelLine.stateProxy = targetEl.stateProxy;\n        }\n      }\n      setLabelLineState(labelLine, false, stateName, stateModel);\n    }\n  }\n  if (labelLine) {\n    defaults(labelLine.style, defaultStyle); // Not fill.\n\n    labelLine.style.fill = null;\n    var showAbove = normalModel.get('showAbove');\n    var labelLineConfig = targetEl.textGuideLineConfig = targetEl.textGuideLineConfig || {};\n    labelLineConfig.showAbove = showAbove || false; // Custom the buildPath.\n\n    labelLine.buildPath = buildLabelLinePath;\n  }\n}\nexport function getLabelLineStatesModels(itemModel, labelLineName) {\n  labelLineName = labelLineName || 'labelLine';\n  var statesModels = {\n    normal: itemModel.getModel(labelLineName)\n  };\n  for (var i = 0; i < SPECIAL_STATES.length; i++) {\n    var stateName = SPECIAL_STATES[i];\n    statesModels[stateName] = itemModel.getModel([stateName, labelLineName]);\n  }\n  return statesModels;\n}", "map": {"version": 3, "names": ["Point", "Path", "Polyline", "PathProxy", "normalizeRadian", "cubicProjectPoint", "quadraticProjectPoint", "defaults", "retrieve2", "invert", "vector", "DISPLAY_STATES", "SPECIAL_STATES", "PI2", "Math", "PI", "CMD", "DEFAULT_SEARCH_SPACE", "getCandidateAnchor", "pos", "distance", "rect", "outPt", "outDir", "width", "height", "set", "x", "y", "projectPointToArc", "cx", "cy", "r", "startAngle", "endAngle", "anticlockwise", "out", "d", "sqrt", "ox", "oy", "abs", "tmp", "angle", "atan2", "x1", "cos", "y1", "sin", "x2", "y2", "d1", "d2", "projectPointToLine", "limitToEnds", "dx", "dy", "dx1", "dy1", "lineLen", "projectedLen", "t", "min", "max", "projectPointToRect", "tmpPt", "nearestPointOnRect", "pt", "dist", "nearestPointOnPath", "path", "xi", "yi", "x0", "y0", "minDist", "Infinity", "data", "i", "length", "cmd", "M", "L", "C", "Q", "A", "rx", "ry", "theta", "d<PERSON><PERSON><PERSON>", "_x", "R", "Z", "pt0", "pt1", "pt2", "dir", "dir2", "updateLabelLinePoints", "target", "labelLineModel", "labelLine", "getTextGuideLine", "label", "getTextContent", "labelGuideConfig", "textGuideLineConfig", "points", "searchSpace", "candidates", "labelRect", "getBoundingRect", "clone", "applyTransform", "getComputedTransform", "anchorPoint", "anchor", "targetTransform", "targetInversedTransform", "len", "get", "copy", "candidate", "scaleAndAdd", "transform", "boundingRect", "toArray", "limitTurnAngle", "setShape", "tmpArr", "tmpProjPoint", "linePoints", "minTurnAngle", "fromArray", "sub", "len1", "len2", "scale", "angleCos", "dot", "minTurnAngleCos", "tan", "isNaN", "limitSurfaceAngle", "surfaceNormal", "maxSurfaceAngle", "maxSurfaceAngleCos", "HALF_PI", "angle2", "acos", "newAngle", "setLabelLineState", "ignore", "stateName", "stateModel", "isNormal", "stateObj", "ensureState", "smooth", "shape", "styleObj", "getModel", "getLineStyle", "useStyle", "style", "buildLabelLinePath", "moveTo", "lineTo", "moveLen", "midPoint0", "lerp", "midPoint2", "midPoint1", "bezierCurveTo", "setLabelLineStyle", "targetEl", "statesModels", "defaultStyle", "removeTextGuideLine", "normalModel", "normal", "showNormal", "labelIgnoreNormal", "stateShow", "isLabelIgnored", "states", "setTextGuideLine", "stateProxy", "fill", "showAbove", "labelLineConfig", "buildPath", "getLabelLineStatesModels", "itemModel", "labelLineName"], "sources": ["C:/WolfSystemV2/WolfOldFrontEnd/node_modules/echarts/lib/label/labelGuideHelper.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { Point, Path, Polyline } from '../util/graphic.js';\nimport PathProxy from 'zrender/lib/core/PathProxy.js';\nimport { normalizeRadian } from 'zrender/lib/contain/util.js';\nimport { cubicProjectPoint, quadraticProjectPoint } from 'zrender/lib/core/curve.js';\nimport { defaults, retrieve2 } from 'zrender/lib/core/util.js';\nimport { invert } from 'zrender/lib/core/matrix.js';\nimport * as vector from 'zrender/lib/core/vector.js';\nimport { DISPLAY_STATES, SPECIAL_STATES } from '../util/states.js';\nvar PI2 = Math.PI * 2;\nvar CMD = PathProxy.CMD;\nvar DEFAULT_SEARCH_SPACE = ['top', 'right', 'bottom', 'left'];\n\nfunction getCandidateAnchor(pos, distance, rect, outPt, outDir) {\n  var width = rect.width;\n  var height = rect.height;\n\n  switch (pos) {\n    case 'top':\n      outPt.set(rect.x + width / 2, rect.y - distance);\n      outDir.set(0, -1);\n      break;\n\n    case 'bottom':\n      outPt.set(rect.x + width / 2, rect.y + height + distance);\n      outDir.set(0, 1);\n      break;\n\n    case 'left':\n      outPt.set(rect.x - distance, rect.y + height / 2);\n      outDir.set(-1, 0);\n      break;\n\n    case 'right':\n      outPt.set(rect.x + width + distance, rect.y + height / 2);\n      outDir.set(1, 0);\n      break;\n  }\n}\n\nfunction projectPointToArc(cx, cy, r, startAngle, endAngle, anticlockwise, x, y, out) {\n  x -= cx;\n  y -= cy;\n  var d = Math.sqrt(x * x + y * y);\n  x /= d;\n  y /= d; // Intersect point.\n\n  var ox = x * r + cx;\n  var oy = y * r + cy;\n\n  if (Math.abs(startAngle - endAngle) % PI2 < 1e-4) {\n    // Is a circle\n    out[0] = ox;\n    out[1] = oy;\n    return d - r;\n  }\n\n  if (anticlockwise) {\n    var tmp = startAngle;\n    startAngle = normalizeRadian(endAngle);\n    endAngle = normalizeRadian(tmp);\n  } else {\n    startAngle = normalizeRadian(startAngle);\n    endAngle = normalizeRadian(endAngle);\n  }\n\n  if (startAngle > endAngle) {\n    endAngle += PI2;\n  }\n\n  var angle = Math.atan2(y, x);\n\n  if (angle < 0) {\n    angle += PI2;\n  }\n\n  if (angle >= startAngle && angle <= endAngle || angle + PI2 >= startAngle && angle + PI2 <= endAngle) {\n    // Project point is on the arc.\n    out[0] = ox;\n    out[1] = oy;\n    return d - r;\n  }\n\n  var x1 = r * Math.cos(startAngle) + cx;\n  var y1 = r * Math.sin(startAngle) + cy;\n  var x2 = r * Math.cos(endAngle) + cx;\n  var y2 = r * Math.sin(endAngle) + cy;\n  var d1 = (x1 - x) * (x1 - x) + (y1 - y) * (y1 - y);\n  var d2 = (x2 - x) * (x2 - x) + (y2 - y) * (y2 - y);\n\n  if (d1 < d2) {\n    out[0] = x1;\n    out[1] = y1;\n    return Math.sqrt(d1);\n  } else {\n    out[0] = x2;\n    out[1] = y2;\n    return Math.sqrt(d2);\n  }\n}\n\nfunction projectPointToLine(x1, y1, x2, y2, x, y, out, limitToEnds) {\n  var dx = x - x1;\n  var dy = y - y1;\n  var dx1 = x2 - x1;\n  var dy1 = y2 - y1;\n  var lineLen = Math.sqrt(dx1 * dx1 + dy1 * dy1);\n  dx1 /= lineLen;\n  dy1 /= lineLen; // dot product\n\n  var projectedLen = dx * dx1 + dy * dy1;\n  var t = projectedLen / lineLen;\n\n  if (limitToEnds) {\n    t = Math.min(Math.max(t, 0), 1);\n  }\n\n  t *= lineLen;\n  var ox = out[0] = x1 + t * dx1;\n  var oy = out[1] = y1 + t * dy1;\n  return Math.sqrt((ox - x) * (ox - x) + (oy - y) * (oy - y));\n}\n\nfunction projectPointToRect(x1, y1, width, height, x, y, out) {\n  if (width < 0) {\n    x1 = x1 + width;\n    width = -width;\n  }\n\n  if (height < 0) {\n    y1 = y1 + height;\n    height = -height;\n  }\n\n  var x2 = x1 + width;\n  var y2 = y1 + height;\n  var ox = out[0] = Math.min(Math.max(x, x1), x2);\n  var oy = out[1] = Math.min(Math.max(y, y1), y2);\n  return Math.sqrt((ox - x) * (ox - x) + (oy - y) * (oy - y));\n}\n\nvar tmpPt = [];\n\nfunction nearestPointOnRect(pt, rect, out) {\n  var dist = projectPointToRect(rect.x, rect.y, rect.width, rect.height, pt.x, pt.y, tmpPt);\n  out.set(tmpPt[0], tmpPt[1]);\n  return dist;\n}\n/**\r\n * Calculate min distance corresponding point.\r\n * This method won't evaluate if point is in the path.\r\n */\n\n\nfunction nearestPointOnPath(pt, path, out) {\n  var xi = 0;\n  var yi = 0;\n  var x0 = 0;\n  var y0 = 0;\n  var x1;\n  var y1;\n  var minDist = Infinity;\n  var data = path.data;\n  var x = pt.x;\n  var y = pt.y;\n\n  for (var i = 0; i < data.length;) {\n    var cmd = data[i++];\n\n    if (i === 1) {\n      xi = data[i];\n      yi = data[i + 1];\n      x0 = xi;\n      y0 = yi;\n    }\n\n    var d = minDist;\n\n    switch (cmd) {\n      case CMD.M:\n        // moveTo 命令重新创建一个新的 subpath, 并且更新新的起点\n        // 在 closePath 的时候使用\n        x0 = data[i++];\n        y0 = data[i++];\n        xi = x0;\n        yi = y0;\n        break;\n\n      case CMD.L:\n        d = projectPointToLine(xi, yi, data[i], data[i + 1], x, y, tmpPt, true);\n        xi = data[i++];\n        yi = data[i++];\n        break;\n\n      case CMD.C:\n        d = cubicProjectPoint(xi, yi, data[i++], data[i++], data[i++], data[i++], data[i], data[i + 1], x, y, tmpPt);\n        xi = data[i++];\n        yi = data[i++];\n        break;\n\n      case CMD.Q:\n        d = quadraticProjectPoint(xi, yi, data[i++], data[i++], data[i], data[i + 1], x, y, tmpPt);\n        xi = data[i++];\n        yi = data[i++];\n        break;\n\n      case CMD.A:\n        // TODO Arc 判断的开销比较大\n        var cx = data[i++];\n        var cy = data[i++];\n        var rx = data[i++];\n        var ry = data[i++];\n        var theta = data[i++];\n        var dTheta = data[i++]; // TODO Arc 旋转\n\n        i += 1;\n        var anticlockwise = !!(1 - data[i++]);\n        x1 = Math.cos(theta) * rx + cx;\n        y1 = Math.sin(theta) * ry + cy; // 不是直接使用 arc 命令\n\n        if (i <= 1) {\n          // 第一个命令起点还未定义\n          x0 = x1;\n          y0 = y1;\n        } // zr 使用scale来模拟椭圆, 这里也对x做一定的缩放\n\n\n        var _x = (x - cx) * ry / rx + cx;\n\n        d = projectPointToArc(cx, cy, ry, theta, theta + dTheta, anticlockwise, _x, y, tmpPt);\n        xi = Math.cos(theta + dTheta) * rx + cx;\n        yi = Math.sin(theta + dTheta) * ry + cy;\n        break;\n\n      case CMD.R:\n        x0 = xi = data[i++];\n        y0 = yi = data[i++];\n        var width = data[i++];\n        var height = data[i++];\n        d = projectPointToRect(x0, y0, width, height, x, y, tmpPt);\n        break;\n\n      case CMD.Z:\n        d = projectPointToLine(xi, yi, x0, y0, x, y, tmpPt, true);\n        xi = x0;\n        yi = y0;\n        break;\n    }\n\n    if (d < minDist) {\n      minDist = d;\n      out.set(tmpPt[0], tmpPt[1]);\n    }\n  }\n\n  return minDist;\n} // Temporal variable for intermediate usage.\n\n\nvar pt0 = new Point();\nvar pt1 = new Point();\nvar pt2 = new Point();\nvar dir = new Point();\nvar dir2 = new Point();\n/**\r\n * Calculate a proper guide line based on the label position and graphic element definition\r\n * @param label\r\n * @param labelRect\r\n * @param target\r\n * @param targetRect\r\n */\n\nexport function updateLabelLinePoints(target, labelLineModel) {\n  if (!target) {\n    return;\n  }\n\n  var labelLine = target.getTextGuideLine();\n  var label = target.getTextContent(); // Needs to create text guide in each charts.\n\n  if (!(label && labelLine)) {\n    return;\n  }\n\n  var labelGuideConfig = target.textGuideLineConfig || {};\n  var points = [[0, 0], [0, 0], [0, 0]];\n  var searchSpace = labelGuideConfig.candidates || DEFAULT_SEARCH_SPACE;\n  var labelRect = label.getBoundingRect().clone();\n  labelRect.applyTransform(label.getComputedTransform());\n  var minDist = Infinity;\n  var anchorPoint = labelGuideConfig.anchor;\n  var targetTransform = target.getComputedTransform();\n  var targetInversedTransform = targetTransform && invert([], targetTransform);\n  var len = labelLineModel.get('length2') || 0;\n\n  if (anchorPoint) {\n    pt2.copy(anchorPoint);\n  }\n\n  for (var i = 0; i < searchSpace.length; i++) {\n    var candidate = searchSpace[i];\n    getCandidateAnchor(candidate, 0, labelRect, pt0, dir);\n    Point.scaleAndAdd(pt1, pt0, dir, len); // Transform to target coord space.\n\n    pt1.transform(targetInversedTransform); // Note: getBoundingRect will ensure the `path` being created.\n\n    var boundingRect = target.getBoundingRect();\n    var dist = anchorPoint ? anchorPoint.distance(pt1) : target instanceof Path ? nearestPointOnPath(pt1, target.path, pt2) : nearestPointOnRect(pt1, boundingRect, pt2); // TODO pt2 is in the path\n\n    if (dist < minDist) {\n      minDist = dist; // Transform back to global space.\n\n      pt1.transform(targetTransform);\n      pt2.transform(targetTransform);\n      pt2.toArray(points[0]);\n      pt1.toArray(points[1]);\n      pt0.toArray(points[2]);\n    }\n  }\n\n  limitTurnAngle(points, labelLineModel.get('minTurnAngle'));\n  labelLine.setShape({\n    points: points\n  });\n} // Temporal variable for the limitTurnAngle function\n\nvar tmpArr = [];\nvar tmpProjPoint = new Point();\n/**\r\n * Reduce the line segment attached to the label to limit the turn angle between two segments.\r\n * @param linePoints\r\n * @param minTurnAngle Radian of minimum turn angle. 0 - 180\r\n */\n\nexport function limitTurnAngle(linePoints, minTurnAngle) {\n  if (!(minTurnAngle <= 180 && minTurnAngle > 0)) {\n    return;\n  }\n\n  minTurnAngle = minTurnAngle / 180 * Math.PI; // The line points can be\n  //      /pt1----pt2 (label)\n  //     /\n  // pt0/\n\n  pt0.fromArray(linePoints[0]);\n  pt1.fromArray(linePoints[1]);\n  pt2.fromArray(linePoints[2]);\n  Point.sub(dir, pt0, pt1);\n  Point.sub(dir2, pt2, pt1);\n  var len1 = dir.len();\n  var len2 = dir2.len();\n\n  if (len1 < 1e-3 || len2 < 1e-3) {\n    return;\n  }\n\n  dir.scale(1 / len1);\n  dir2.scale(1 / len2);\n  var angleCos = dir.dot(dir2);\n  var minTurnAngleCos = Math.cos(minTurnAngle);\n\n  if (minTurnAngleCos < angleCos) {\n    // Smaller than minTurnAngle\n    // Calculate project point of pt0 on pt1-pt2\n    var d = projectPointToLine(pt1.x, pt1.y, pt2.x, pt2.y, pt0.x, pt0.y, tmpArr, false);\n    tmpProjPoint.fromArray(tmpArr); // Calculate new projected length with limited minTurnAngle and get the new connect point\n\n    tmpProjPoint.scaleAndAdd(dir2, d / Math.tan(Math.PI - minTurnAngle)); // Limit the new calculated connect point between pt1 and pt2.\n\n    var t = pt2.x !== pt1.x ? (tmpProjPoint.x - pt1.x) / (pt2.x - pt1.x) : (tmpProjPoint.y - pt1.y) / (pt2.y - pt1.y);\n\n    if (isNaN(t)) {\n      return;\n    }\n\n    if (t < 0) {\n      Point.copy(tmpProjPoint, pt1);\n    } else if (t > 1) {\n      Point.copy(tmpProjPoint, pt2);\n    }\n\n    tmpProjPoint.toArray(linePoints[1]);\n  }\n}\n/**\r\n * Limit the angle of line and the surface\r\n * @param maxSurfaceAngle Radian of minimum turn angle. 0 - 180. 0 is same direction to normal. 180 is opposite\r\n */\n\nexport function limitSurfaceAngle(linePoints, surfaceNormal, maxSurfaceAngle) {\n  if (!(maxSurfaceAngle <= 180 && maxSurfaceAngle > 0)) {\n    return;\n  }\n\n  maxSurfaceAngle = maxSurfaceAngle / 180 * Math.PI;\n  pt0.fromArray(linePoints[0]);\n  pt1.fromArray(linePoints[1]);\n  pt2.fromArray(linePoints[2]);\n  Point.sub(dir, pt1, pt0);\n  Point.sub(dir2, pt2, pt1);\n  var len1 = dir.len();\n  var len2 = dir2.len();\n\n  if (len1 < 1e-3 || len2 < 1e-3) {\n    return;\n  }\n\n  dir.scale(1 / len1);\n  dir2.scale(1 / len2);\n  var angleCos = dir.dot(surfaceNormal);\n  var maxSurfaceAngleCos = Math.cos(maxSurfaceAngle);\n\n  if (angleCos < maxSurfaceAngleCos) {\n    // Calculate project point of pt0 on pt1-pt2\n    var d = projectPointToLine(pt1.x, pt1.y, pt2.x, pt2.y, pt0.x, pt0.y, tmpArr, false);\n    tmpProjPoint.fromArray(tmpArr);\n    var HALF_PI = Math.PI / 2;\n    var angle2 = Math.acos(dir2.dot(surfaceNormal));\n    var newAngle = HALF_PI + angle2 - maxSurfaceAngle;\n\n    if (newAngle >= HALF_PI) {\n      // parallel\n      Point.copy(tmpProjPoint, pt2);\n    } else {\n      // Calculate new projected length with limited minTurnAngle and get the new connect point\n      tmpProjPoint.scaleAndAdd(dir2, d / Math.tan(Math.PI / 2 - newAngle)); // Limit the new calculated connect point between pt1 and pt2.\n\n      var t = pt2.x !== pt1.x ? (tmpProjPoint.x - pt1.x) / (pt2.x - pt1.x) : (tmpProjPoint.y - pt1.y) / (pt2.y - pt1.y);\n\n      if (isNaN(t)) {\n        return;\n      }\n\n      if (t < 0) {\n        Point.copy(tmpProjPoint, pt1);\n      } else if (t > 1) {\n        Point.copy(tmpProjPoint, pt2);\n      }\n    }\n\n    tmpProjPoint.toArray(linePoints[1]);\n  }\n}\n\nfunction setLabelLineState(labelLine, ignore, stateName, stateModel) {\n  var isNormal = stateName === 'normal';\n  var stateObj = isNormal ? labelLine : labelLine.ensureState(stateName); // Make sure display.\n\n  stateObj.ignore = ignore; // Set smooth\n\n  var smooth = stateModel.get('smooth');\n\n  if (smooth && smooth === true) {\n    smooth = 0.3;\n  }\n\n  stateObj.shape = stateObj.shape || {};\n\n  if (smooth > 0) {\n    stateObj.shape.smooth = smooth;\n  }\n\n  var styleObj = stateModel.getModel('lineStyle').getLineStyle();\n  isNormal ? labelLine.useStyle(styleObj) : stateObj.style = styleObj;\n}\n\nfunction buildLabelLinePath(path, shape) {\n  var smooth = shape.smooth;\n  var points = shape.points;\n\n  if (!points) {\n    return;\n  }\n\n  path.moveTo(points[0][0], points[0][1]);\n\n  if (smooth > 0 && points.length >= 3) {\n    var len1 = vector.dist(points[0], points[1]);\n    var len2 = vector.dist(points[1], points[2]);\n\n    if (!len1 || !len2) {\n      path.lineTo(points[1][0], points[1][1]);\n      path.lineTo(points[2][0], points[2][1]);\n      return;\n    }\n\n    var moveLen = Math.min(len1, len2) * smooth;\n    var midPoint0 = vector.lerp([], points[1], points[0], moveLen / len1);\n    var midPoint2 = vector.lerp([], points[1], points[2], moveLen / len2);\n    var midPoint1 = vector.lerp([], midPoint0, midPoint2, 0.5);\n    path.bezierCurveTo(midPoint0[0], midPoint0[1], midPoint0[0], midPoint0[1], midPoint1[0], midPoint1[1]);\n    path.bezierCurveTo(midPoint2[0], midPoint2[1], midPoint2[0], midPoint2[1], points[2][0], points[2][1]);\n  } else {\n    for (var i = 1; i < points.length; i++) {\n      path.lineTo(points[i][0], points[i][1]);\n    }\n  }\n}\n/**\r\n * Create a label line if necessary and set it's style.\r\n */\n\n\nexport function setLabelLineStyle(targetEl, statesModels, defaultStyle) {\n  var labelLine = targetEl.getTextGuideLine();\n  var label = targetEl.getTextContent();\n\n  if (!label) {\n    // Not show label line if there is no label.\n    if (labelLine) {\n      targetEl.removeTextGuideLine();\n    }\n\n    return;\n  }\n\n  var normalModel = statesModels.normal;\n  var showNormal = normalModel.get('show');\n  var labelIgnoreNormal = label.ignore;\n\n  for (var i = 0; i < DISPLAY_STATES.length; i++) {\n    var stateName = DISPLAY_STATES[i];\n    var stateModel = statesModels[stateName];\n    var isNormal = stateName === 'normal';\n\n    if (stateModel) {\n      var stateShow = stateModel.get('show');\n      var isLabelIgnored = isNormal ? labelIgnoreNormal : retrieve2(label.states[stateName] && label.states[stateName].ignore, labelIgnoreNormal);\n\n      if (isLabelIgnored // Not show when label is not shown in this state.\n      || !retrieve2(stateShow, showNormal) // Use normal state by default if not set.\n      ) {\n          var stateObj = isNormal ? labelLine : labelLine && labelLine.states[stateName];\n\n          if (stateObj) {\n            stateObj.ignore = true;\n          }\n\n          continue;\n        } // Create labelLine if not exists\n\n\n      if (!labelLine) {\n        labelLine = new Polyline();\n        targetEl.setTextGuideLine(labelLine); // Reset state of normal because it's new created.\n        // NOTE: NORMAL should always been the first!\n\n        if (!isNormal && (labelIgnoreNormal || !showNormal)) {\n          setLabelLineState(labelLine, true, 'normal', statesModels.normal);\n        } // Use same state proxy.\n\n\n        if (targetEl.stateProxy) {\n          labelLine.stateProxy = targetEl.stateProxy;\n        }\n      }\n\n      setLabelLineState(labelLine, false, stateName, stateModel);\n    }\n  }\n\n  if (labelLine) {\n    defaults(labelLine.style, defaultStyle); // Not fill.\n\n    labelLine.style.fill = null;\n    var showAbove = normalModel.get('showAbove');\n    var labelLineConfig = targetEl.textGuideLineConfig = targetEl.textGuideLineConfig || {};\n    labelLineConfig.showAbove = showAbove || false; // Custom the buildPath.\n\n    labelLine.buildPath = buildLabelLinePath;\n  }\n}\nexport function getLabelLineStatesModels(itemModel, labelLineName) {\n  labelLineName = labelLineName || 'labelLine';\n  var statesModels = {\n    normal: itemModel.getModel(labelLineName)\n  };\n\n  for (var i = 0; i < SPECIAL_STATES.length; i++) {\n    var stateName = SPECIAL_STATES[i];\n    statesModels[stateName] = itemModel.getModel([stateName, labelLineName]);\n  }\n\n  return statesModels;\n}"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,KAAK,EAAEC,IAAI,EAAEC,QAAQ,QAAQ,oBAAoB;AAC1D,OAAOC,SAAS,MAAM,+BAA+B;AACrD,SAASC,eAAe,QAAQ,6BAA6B;AAC7D,SAASC,iBAAiB,EAAEC,qBAAqB,QAAQ,2BAA2B;AACpF,SAASC,QAAQ,EAAEC,SAAS,QAAQ,0BAA0B;AAC9D,SAASC,MAAM,QAAQ,4BAA4B;AACnD,OAAO,KAAKC,MAAM,MAAM,4BAA4B;AACpD,SAASC,cAAc,EAAEC,cAAc,QAAQ,mBAAmB;AAClE,IAAIC,GAAG,GAAGC,IAAI,CAACC,EAAE,GAAG,CAAC;AACrB,IAAIC,GAAG,GAAGb,SAAS,CAACa,GAAG;AACvB,IAAIC,oBAAoB,GAAG,CAAC,KAAK,EAAE,OAAO,EAAE,QAAQ,EAAE,MAAM,CAAC;AAE7D,SAASC,kBAAkBA,CAACC,GAAG,EAAEC,QAAQ,EAAEC,IAAI,EAAEC,KAAK,EAAEC,MAAM,EAAE;EAC9D,IAAIC,KAAK,GAAGH,IAAI,CAACG,KAAK;EACtB,IAAIC,MAAM,GAAGJ,IAAI,CAACI,MAAM;EAExB,QAAQN,GAAG;IACT,KAAK,KAAK;MACRG,KAAK,CAACI,GAAG,CAACL,IAAI,CAACM,CAAC,GAAGH,KAAK,GAAG,CAAC,EAAEH,IAAI,CAACO,CAAC,GAAGR,QAAQ,CAAC;MAChDG,MAAM,CAACG,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;MACjB;IAEF,KAAK,QAAQ;MACXJ,KAAK,CAACI,GAAG,CAACL,IAAI,CAACM,CAAC,GAAGH,KAAK,GAAG,CAAC,EAAEH,IAAI,CAACO,CAAC,GAAGH,MAAM,GAAGL,QAAQ,CAAC;MACzDG,MAAM,CAACG,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC;MAChB;IAEF,KAAK,MAAM;MACTJ,KAAK,CAACI,GAAG,CAACL,IAAI,CAACM,CAAC,GAAGP,QAAQ,EAAEC,IAAI,CAACO,CAAC,GAAGH,MAAM,GAAG,CAAC,CAAC;MACjDF,MAAM,CAACG,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;MACjB;IAEF,KAAK,OAAO;MACVJ,KAAK,CAACI,GAAG,CAACL,IAAI,CAACM,CAAC,GAAGH,KAAK,GAAGJ,QAAQ,EAAEC,IAAI,CAACO,CAAC,GAAGH,MAAM,GAAG,CAAC,CAAC;MACzDF,MAAM,CAACG,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC;MAChB;EACJ;AACF;AAEA,SAASG,iBAAiBA,CAACC,EAAE,EAAEC,EAAE,EAAEC,CAAC,EAAEC,UAAU,EAAEC,QAAQ,EAAEC,aAAa,EAAER,CAAC,EAAEC,CAAC,EAAEQ,GAAG,EAAE;EACpFT,CAAC,IAAIG,EAAE;EACPF,CAAC,IAAIG,EAAE;EACP,IAAIM,CAAC,GAAGvB,IAAI,CAACwB,IAAI,CAACX,CAAC,GAAGA,CAAC,GAAGC,CAAC,GAAGA,CAAC,CAAC;EAChCD,CAAC,IAAIU,CAAC;EACNT,CAAC,IAAIS,CAAC,CAAC,CAAC;;EAER,IAAIE,EAAE,GAAGZ,CAAC,GAAGK,CAAC,GAAGF,EAAE;EACnB,IAAIU,EAAE,GAAGZ,CAAC,GAAGI,CAAC,GAAGD,EAAE;EAEnB,IAAIjB,IAAI,CAAC2B,GAAG,CAACR,UAAU,GAAGC,QAAQ,CAAC,GAAGrB,GAAG,GAAG,IAAI,EAAE;IAChD;IACAuB,GAAG,CAAC,CAAC,CAAC,GAAGG,EAAE;IACXH,GAAG,CAAC,CAAC,CAAC,GAAGI,EAAE;IACX,OAAOH,CAAC,GAAGL,CAAC;EACd;EAEA,IAAIG,aAAa,EAAE;IACjB,IAAIO,GAAG,GAAGT,UAAU;IACpBA,UAAU,GAAG7B,eAAe,CAAC8B,QAAQ,CAAC;IACtCA,QAAQ,GAAG9B,eAAe,CAACsC,GAAG,CAAC;EACjC,CAAC,MAAM;IACLT,UAAU,GAAG7B,eAAe,CAAC6B,UAAU,CAAC;IACxCC,QAAQ,GAAG9B,eAAe,CAAC8B,QAAQ,CAAC;EACtC;EAEA,IAAID,UAAU,GAAGC,QAAQ,EAAE;IACzBA,QAAQ,IAAIrB,GAAG;EACjB;EAEA,IAAI8B,KAAK,GAAG7B,IAAI,CAAC8B,KAAK,CAAChB,CAAC,EAAED,CAAC,CAAC;EAE5B,IAAIgB,KAAK,GAAG,CAAC,EAAE;IACbA,KAAK,IAAI9B,GAAG;EACd;EAEA,IAAI8B,KAAK,IAAIV,UAAU,IAAIU,KAAK,IAAIT,QAAQ,IAAIS,KAAK,GAAG9B,GAAG,IAAIoB,UAAU,IAAIU,KAAK,GAAG9B,GAAG,IAAIqB,QAAQ,EAAE;IACpG;IACAE,GAAG,CAAC,CAAC,CAAC,GAAGG,EAAE;IACXH,GAAG,CAAC,CAAC,CAAC,GAAGI,EAAE;IACX,OAAOH,CAAC,GAAGL,CAAC;EACd;EAEA,IAAIa,EAAE,GAAGb,CAAC,GAAGlB,IAAI,CAACgC,GAAG,CAACb,UAAU,CAAC,GAAGH,EAAE;EACtC,IAAIiB,EAAE,GAAGf,CAAC,GAAGlB,IAAI,CAACkC,GAAG,CAACf,UAAU,CAAC,GAAGF,EAAE;EACtC,IAAIkB,EAAE,GAAGjB,CAAC,GAAGlB,IAAI,CAACgC,GAAG,CAACZ,QAAQ,CAAC,GAAGJ,EAAE;EACpC,IAAIoB,EAAE,GAAGlB,CAAC,GAAGlB,IAAI,CAACkC,GAAG,CAACd,QAAQ,CAAC,GAAGH,EAAE;EACpC,IAAIoB,EAAE,GAAG,CAACN,EAAE,GAAGlB,CAAC,KAAKkB,EAAE,GAAGlB,CAAC,CAAC,GAAG,CAACoB,EAAE,GAAGnB,CAAC,KAAKmB,EAAE,GAAGnB,CAAC,CAAC;EAClD,IAAIwB,EAAE,GAAG,CAACH,EAAE,GAAGtB,CAAC,KAAKsB,EAAE,GAAGtB,CAAC,CAAC,GAAG,CAACuB,EAAE,GAAGtB,CAAC,KAAKsB,EAAE,GAAGtB,CAAC,CAAC;EAElD,IAAIuB,EAAE,GAAGC,EAAE,EAAE;IACXhB,GAAG,CAAC,CAAC,CAAC,GAAGS,EAAE;IACXT,GAAG,CAAC,CAAC,CAAC,GAAGW,EAAE;IACX,OAAOjC,IAAI,CAACwB,IAAI,CAACa,EAAE,CAAC;EACtB,CAAC,MAAM;IACLf,GAAG,CAAC,CAAC,CAAC,GAAGa,EAAE;IACXb,GAAG,CAAC,CAAC,CAAC,GAAGc,EAAE;IACX,OAAOpC,IAAI,CAACwB,IAAI,CAACc,EAAE,CAAC;EACtB;AACF;AAEA,SAASC,kBAAkBA,CAACR,EAAE,EAAEE,EAAE,EAAEE,EAAE,EAAEC,EAAE,EAAEvB,CAAC,EAAEC,CAAC,EAAEQ,GAAG,EAAEkB,WAAW,EAAE;EAClE,IAAIC,EAAE,GAAG5B,CAAC,GAAGkB,EAAE;EACf,IAAIW,EAAE,GAAG5B,CAAC,GAAGmB,EAAE;EACf,IAAIU,GAAG,GAAGR,EAAE,GAAGJ,EAAE;EACjB,IAAIa,GAAG,GAAGR,EAAE,GAAGH,EAAE;EACjB,IAAIY,OAAO,GAAG7C,IAAI,CAACwB,IAAI,CAACmB,GAAG,GAAGA,GAAG,GAAGC,GAAG,GAAGA,GAAG,CAAC;EAC9CD,GAAG,IAAIE,OAAO;EACdD,GAAG,IAAIC,OAAO,CAAC,CAAC;;EAEhB,IAAIC,YAAY,GAAGL,EAAE,GAAGE,GAAG,GAAGD,EAAE,GAAGE,GAAG;EACtC,IAAIG,CAAC,GAAGD,YAAY,GAAGD,OAAO;EAE9B,IAAIL,WAAW,EAAE;IACfO,CAAC,GAAG/C,IAAI,CAACgD,GAAG,CAAChD,IAAI,CAACiD,GAAG,CAACF,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;EACjC;EAEAA,CAAC,IAAIF,OAAO;EACZ,IAAIpB,EAAE,GAAGH,GAAG,CAAC,CAAC,CAAC,GAAGS,EAAE,GAAGgB,CAAC,GAAGJ,GAAG;EAC9B,IAAIjB,EAAE,GAAGJ,GAAG,CAAC,CAAC,CAAC,GAAGW,EAAE,GAAGc,CAAC,GAAGH,GAAG;EAC9B,OAAO5C,IAAI,CAACwB,IAAI,CAAC,CAACC,EAAE,GAAGZ,CAAC,KAAKY,EAAE,GAAGZ,CAAC,CAAC,GAAG,CAACa,EAAE,GAAGZ,CAAC,KAAKY,EAAE,GAAGZ,CAAC,CAAC,CAAC;AAC7D;AAEA,SAASoC,kBAAkBA,CAACnB,EAAE,EAAEE,EAAE,EAAEvB,KAAK,EAAEC,MAAM,EAAEE,CAAC,EAAEC,CAAC,EAAEQ,GAAG,EAAE;EAC5D,IAAIZ,KAAK,GAAG,CAAC,EAAE;IACbqB,EAAE,GAAGA,EAAE,GAAGrB,KAAK;IACfA,KAAK,GAAG,CAACA,KAAK;EAChB;EAEA,IAAIC,MAAM,GAAG,CAAC,EAAE;IACdsB,EAAE,GAAGA,EAAE,GAAGtB,MAAM;IAChBA,MAAM,GAAG,CAACA,MAAM;EAClB;EAEA,IAAIwB,EAAE,GAAGJ,EAAE,GAAGrB,KAAK;EACnB,IAAI0B,EAAE,GAAGH,EAAE,GAAGtB,MAAM;EACpB,IAAIc,EAAE,GAAGH,GAAG,CAAC,CAAC,CAAC,GAAGtB,IAAI,CAACgD,GAAG,CAAChD,IAAI,CAACiD,GAAG,CAACpC,CAAC,EAAEkB,EAAE,CAAC,EAAEI,EAAE,CAAC;EAC/C,IAAIT,EAAE,GAAGJ,GAAG,CAAC,CAAC,CAAC,GAAGtB,IAAI,CAACgD,GAAG,CAAChD,IAAI,CAACiD,GAAG,CAACnC,CAAC,EAAEmB,EAAE,CAAC,EAAEG,EAAE,CAAC;EAC/C,OAAOpC,IAAI,CAACwB,IAAI,CAAC,CAACC,EAAE,GAAGZ,CAAC,KAAKY,EAAE,GAAGZ,CAAC,CAAC,GAAG,CAACa,EAAE,GAAGZ,CAAC,KAAKY,EAAE,GAAGZ,CAAC,CAAC,CAAC;AAC7D;AAEA,IAAIqC,KAAK,GAAG,EAAE;AAEd,SAASC,kBAAkBA,CAACC,EAAE,EAAE9C,IAAI,EAAEe,GAAG,EAAE;EACzC,IAAIgC,IAAI,GAAGJ,kBAAkB,CAAC3C,IAAI,CAACM,CAAC,EAAEN,IAAI,CAACO,CAAC,EAAEP,IAAI,CAACG,KAAK,EAAEH,IAAI,CAACI,MAAM,EAAE0C,EAAE,CAACxC,CAAC,EAAEwC,EAAE,CAACvC,CAAC,EAAEqC,KAAK,CAAC;EACzF7B,GAAG,CAACV,GAAG,CAACuC,KAAK,CAAC,CAAC,CAAC,EAAEA,KAAK,CAAC,CAAC,CAAC,CAAC;EAC3B,OAAOG,IAAI;AACb;AACA;AACA;AACA;AACA;;AAGA,SAASC,kBAAkBA,CAACF,EAAE,EAAEG,IAAI,EAAElC,GAAG,EAAE;EACzC,IAAImC,EAAE,GAAG,CAAC;EACV,IAAIC,EAAE,GAAG,CAAC;EACV,IAAIC,EAAE,GAAG,CAAC;EACV,IAAIC,EAAE,GAAG,CAAC;EACV,IAAI7B,EAAE;EACN,IAAIE,EAAE;EACN,IAAI4B,OAAO,GAAGC,QAAQ;EACtB,IAAIC,IAAI,GAAGP,IAAI,CAACO,IAAI;EACpB,IAAIlD,CAAC,GAAGwC,EAAE,CAACxC,CAAC;EACZ,IAAIC,CAAC,GAAGuC,EAAE,CAACvC,CAAC;EAEZ,KAAK,IAAIkD,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,IAAI,CAACE,MAAM,GAAG;IAChC,IAAIC,GAAG,GAAGH,IAAI,CAACC,CAAC,EAAE,CAAC;IAEnB,IAAIA,CAAC,KAAK,CAAC,EAAE;MACXP,EAAE,GAAGM,IAAI,CAACC,CAAC,CAAC;MACZN,EAAE,GAAGK,IAAI,CAACC,CAAC,GAAG,CAAC,CAAC;MAChBL,EAAE,GAAGF,EAAE;MACPG,EAAE,GAAGF,EAAE;IACT;IAEA,IAAInC,CAAC,GAAGsC,OAAO;IAEf,QAAQK,GAAG;MACT,KAAKhE,GAAG,CAACiE,CAAC;QACR;QACA;QACAR,EAAE,GAAGI,IAAI,CAACC,CAAC,EAAE,CAAC;QACdJ,EAAE,GAAGG,IAAI,CAACC,CAAC,EAAE,CAAC;QACdP,EAAE,GAAGE,EAAE;QACPD,EAAE,GAAGE,EAAE;QACP;MAEF,KAAK1D,GAAG,CAACkE,CAAC;QACR7C,CAAC,GAAGgB,kBAAkB,CAACkB,EAAE,EAAEC,EAAE,EAAEK,IAAI,CAACC,CAAC,CAAC,EAAED,IAAI,CAACC,CAAC,GAAG,CAAC,CAAC,EAAEnD,CAAC,EAAEC,CAAC,EAAEqC,KAAK,EAAE,IAAI,CAAC;QACvEM,EAAE,GAAGM,IAAI,CAACC,CAAC,EAAE,CAAC;QACdN,EAAE,GAAGK,IAAI,CAACC,CAAC,EAAE,CAAC;QACd;MAEF,KAAK9D,GAAG,CAACmE,CAAC;QACR9C,CAAC,GAAGhC,iBAAiB,CAACkE,EAAE,EAAEC,EAAE,EAAEK,IAAI,CAACC,CAAC,EAAE,CAAC,EAAED,IAAI,CAACC,CAAC,EAAE,CAAC,EAAED,IAAI,CAACC,CAAC,EAAE,CAAC,EAAED,IAAI,CAACC,CAAC,EAAE,CAAC,EAAED,IAAI,CAACC,CAAC,CAAC,EAAED,IAAI,CAACC,CAAC,GAAG,CAAC,CAAC,EAAEnD,CAAC,EAAEC,CAAC,EAAEqC,KAAK,CAAC;QAC5GM,EAAE,GAAGM,IAAI,CAACC,CAAC,EAAE,CAAC;QACdN,EAAE,GAAGK,IAAI,CAACC,CAAC,EAAE,CAAC;QACd;MAEF,KAAK9D,GAAG,CAACoE,CAAC;QACR/C,CAAC,GAAG/B,qBAAqB,CAACiE,EAAE,EAAEC,EAAE,EAAEK,IAAI,CAACC,CAAC,EAAE,CAAC,EAAED,IAAI,CAACC,CAAC,EAAE,CAAC,EAAED,IAAI,CAACC,CAAC,CAAC,EAAED,IAAI,CAACC,CAAC,GAAG,CAAC,CAAC,EAAEnD,CAAC,EAAEC,CAAC,EAAEqC,KAAK,CAAC;QAC1FM,EAAE,GAAGM,IAAI,CAACC,CAAC,EAAE,CAAC;QACdN,EAAE,GAAGK,IAAI,CAACC,CAAC,EAAE,CAAC;QACd;MAEF,KAAK9D,GAAG,CAACqE,CAAC;QACR;QACA,IAAIvD,EAAE,GAAG+C,IAAI,CAACC,CAAC,EAAE,CAAC;QAClB,IAAI/C,EAAE,GAAG8C,IAAI,CAACC,CAAC,EAAE,CAAC;QAClB,IAAIQ,EAAE,GAAGT,IAAI,CAACC,CAAC,EAAE,CAAC;QAClB,IAAIS,EAAE,GAAGV,IAAI,CAACC,CAAC,EAAE,CAAC;QAClB,IAAIU,KAAK,GAAGX,IAAI,CAACC,CAAC,EAAE,CAAC;QACrB,IAAIW,MAAM,GAAGZ,IAAI,CAACC,CAAC,EAAE,CAAC,CAAC,CAAC;;QAExBA,CAAC,IAAI,CAAC;QACN,IAAI3C,aAAa,GAAG,CAAC,EAAE,CAAC,GAAG0C,IAAI,CAACC,CAAC,EAAE,CAAC,CAAC;QACrCjC,EAAE,GAAG/B,IAAI,CAACgC,GAAG,CAAC0C,KAAK,CAAC,GAAGF,EAAE,GAAGxD,EAAE;QAC9BiB,EAAE,GAAGjC,IAAI,CAACkC,GAAG,CAACwC,KAAK,CAAC,GAAGD,EAAE,GAAGxD,EAAE,CAAC,CAAC;;QAEhC,IAAI+C,CAAC,IAAI,CAAC,EAAE;UACV;UACAL,EAAE,GAAG5B,EAAE;UACP6B,EAAE,GAAG3B,EAAE;QACT,CAAC,CAAC;;QAGF,IAAI2C,EAAE,GAAG,CAAC/D,CAAC,GAAGG,EAAE,IAAIyD,EAAE,GAAGD,EAAE,GAAGxD,EAAE;QAEhCO,CAAC,GAAGR,iBAAiB,CAACC,EAAE,EAAEC,EAAE,EAAEwD,EAAE,EAAEC,KAAK,EAAEA,KAAK,GAAGC,MAAM,EAAEtD,aAAa,EAAEuD,EAAE,EAAE9D,CAAC,EAAEqC,KAAK,CAAC;QACrFM,EAAE,GAAGzD,IAAI,CAACgC,GAAG,CAAC0C,KAAK,GAAGC,MAAM,CAAC,GAAGH,EAAE,GAAGxD,EAAE;QACvC0C,EAAE,GAAG1D,IAAI,CAACkC,GAAG,CAACwC,KAAK,GAAGC,MAAM,CAAC,GAAGF,EAAE,GAAGxD,EAAE;QACvC;MAEF,KAAKf,GAAG,CAAC2E,CAAC;QACRlB,EAAE,GAAGF,EAAE,GAAGM,IAAI,CAACC,CAAC,EAAE,CAAC;QACnBJ,EAAE,GAAGF,EAAE,GAAGK,IAAI,CAACC,CAAC,EAAE,CAAC;QACnB,IAAItD,KAAK,GAAGqD,IAAI,CAACC,CAAC,EAAE,CAAC;QACrB,IAAIrD,MAAM,GAAGoD,IAAI,CAACC,CAAC,EAAE,CAAC;QACtBzC,CAAC,GAAG2B,kBAAkB,CAACS,EAAE,EAAEC,EAAE,EAAElD,KAAK,EAAEC,MAAM,EAAEE,CAAC,EAAEC,CAAC,EAAEqC,KAAK,CAAC;QAC1D;MAEF,KAAKjD,GAAG,CAAC4E,CAAC;QACRvD,CAAC,GAAGgB,kBAAkB,CAACkB,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAE/C,CAAC,EAAEC,CAAC,EAAEqC,KAAK,EAAE,IAAI,CAAC;QACzDM,EAAE,GAAGE,EAAE;QACPD,EAAE,GAAGE,EAAE;QACP;IACJ;IAEA,IAAIrC,CAAC,GAAGsC,OAAO,EAAE;MACfA,OAAO,GAAGtC,CAAC;MACXD,GAAG,CAACV,GAAG,CAACuC,KAAK,CAAC,CAAC,CAAC,EAAEA,KAAK,CAAC,CAAC,CAAC,CAAC;IAC7B;EACF;EAEA,OAAOU,OAAO;AAChB,CAAC,CAAC;;AAGF,IAAIkB,GAAG,GAAG,IAAI7F,KAAK,CAAC,CAAC;AACrB,IAAI8F,GAAG,GAAG,IAAI9F,KAAK,CAAC,CAAC;AACrB,IAAI+F,GAAG,GAAG,IAAI/F,KAAK,CAAC,CAAC;AACrB,IAAIgG,GAAG,GAAG,IAAIhG,KAAK,CAAC,CAAC;AACrB,IAAIiG,IAAI,GAAG,IAAIjG,KAAK,CAAC,CAAC;AACtB;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,OAAO,SAASkG,qBAAqBA,CAACC,MAAM,EAAEC,cAAc,EAAE;EAC5D,IAAI,CAACD,MAAM,EAAE;IACX;EACF;EAEA,IAAIE,SAAS,GAAGF,MAAM,CAACG,gBAAgB,CAAC,CAAC;EACzC,IAAIC,KAAK,GAAGJ,MAAM,CAACK,cAAc,CAAC,CAAC,CAAC,CAAC;;EAErC,IAAI,EAAED,KAAK,IAAIF,SAAS,CAAC,EAAE;IACzB;EACF;EAEA,IAAII,gBAAgB,GAAGN,MAAM,CAACO,mBAAmB,IAAI,CAAC,CAAC;EACvD,IAAIC,MAAM,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EACrC,IAAIC,WAAW,GAAGH,gBAAgB,CAACI,UAAU,IAAI5F,oBAAoB;EACrE,IAAI6F,SAAS,GAAGP,KAAK,CAACQ,eAAe,CAAC,CAAC,CAACC,KAAK,CAAC,CAAC;EAC/CF,SAAS,CAACG,cAAc,CAACV,KAAK,CAACW,oBAAoB,CAAC,CAAC,CAAC;EACtD,IAAIvC,OAAO,GAAGC,QAAQ;EACtB,IAAIuC,WAAW,GAAGV,gBAAgB,CAACW,MAAM;EACzC,IAAIC,eAAe,GAAGlB,MAAM,CAACe,oBAAoB,CAAC,CAAC;EACnD,IAAII,uBAAuB,GAAGD,eAAe,IAAI5G,MAAM,CAAC,EAAE,EAAE4G,eAAe,CAAC;EAC5E,IAAIE,GAAG,GAAGnB,cAAc,CAACoB,GAAG,CAAC,SAAS,CAAC,IAAI,CAAC;EAE5C,IAAIL,WAAW,EAAE;IACfpB,GAAG,CAAC0B,IAAI,CAACN,WAAW,CAAC;EACvB;EAEA,KAAK,IAAIrC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG8B,WAAW,CAAC7B,MAAM,EAAED,CAAC,EAAE,EAAE;IAC3C,IAAI4C,SAAS,GAAGd,WAAW,CAAC9B,CAAC,CAAC;IAC9B5D,kBAAkB,CAACwG,SAAS,EAAE,CAAC,EAAEZ,SAAS,EAAEjB,GAAG,EAAEG,GAAG,CAAC;IACrDhG,KAAK,CAAC2H,WAAW,CAAC7B,GAAG,EAAED,GAAG,EAAEG,GAAG,EAAEuB,GAAG,CAAC,CAAC,CAAC;;IAEvCzB,GAAG,CAAC8B,SAAS,CAACN,uBAAuB,CAAC,CAAC,CAAC;;IAExC,IAAIO,YAAY,GAAG1B,MAAM,CAACY,eAAe,CAAC,CAAC;IAC3C,IAAI3C,IAAI,GAAG+C,WAAW,GAAGA,WAAW,CAAC/F,QAAQ,CAAC0E,GAAG,CAAC,GAAGK,MAAM,YAAYlG,IAAI,GAAGoE,kBAAkB,CAACyB,GAAG,EAAEK,MAAM,CAAC7B,IAAI,EAAEyB,GAAG,CAAC,GAAG7B,kBAAkB,CAAC4B,GAAG,EAAE+B,YAAY,EAAE9B,GAAG,CAAC,CAAC,CAAC;;IAEtK,IAAI3B,IAAI,GAAGO,OAAO,EAAE;MAClBA,OAAO,GAAGP,IAAI,CAAC,CAAC;;MAEhB0B,GAAG,CAAC8B,SAAS,CAACP,eAAe,CAAC;MAC9BtB,GAAG,CAAC6B,SAAS,CAACP,eAAe,CAAC;MAC9BtB,GAAG,CAAC+B,OAAO,CAACnB,MAAM,CAAC,CAAC,CAAC,CAAC;MACtBb,GAAG,CAACgC,OAAO,CAACnB,MAAM,CAAC,CAAC,CAAC,CAAC;MACtBd,GAAG,CAACiC,OAAO,CAACnB,MAAM,CAAC,CAAC,CAAC,CAAC;IACxB;EACF;EAEAoB,cAAc,CAACpB,MAAM,EAAEP,cAAc,CAACoB,GAAG,CAAC,cAAc,CAAC,CAAC;EAC1DnB,SAAS,CAAC2B,QAAQ,CAAC;IACjBrB,MAAM,EAAEA;EACV,CAAC,CAAC;AACJ,CAAC,CAAC;;AAEF,IAAIsB,MAAM,GAAG,EAAE;AACf,IAAIC,YAAY,GAAG,IAAIlI,KAAK,CAAC,CAAC;AAC9B;AACA;AACA;AACA;AACA;;AAEA,OAAO,SAAS+H,cAAcA,CAACI,UAAU,EAAEC,YAAY,EAAE;EACvD,IAAI,EAAEA,YAAY,IAAI,GAAG,IAAIA,YAAY,GAAG,CAAC,CAAC,EAAE;IAC9C;EACF;EAEAA,YAAY,GAAGA,YAAY,GAAG,GAAG,GAAGtH,IAAI,CAACC,EAAE,CAAC,CAAC;EAC7C;EACA;EACA;;EAEA8E,GAAG,CAACwC,SAAS,CAACF,UAAU,CAAC,CAAC,CAAC,CAAC;EAC5BrC,GAAG,CAACuC,SAAS,CAACF,UAAU,CAAC,CAAC,CAAC,CAAC;EAC5BpC,GAAG,CAACsC,SAAS,CAACF,UAAU,CAAC,CAAC,CAAC,CAAC;EAC5BnI,KAAK,CAACsI,GAAG,CAACtC,GAAG,EAAEH,GAAG,EAAEC,GAAG,CAAC;EACxB9F,KAAK,CAACsI,GAAG,CAACrC,IAAI,EAAEF,GAAG,EAAED,GAAG,CAAC;EACzB,IAAIyC,IAAI,GAAGvC,GAAG,CAACuB,GAAG,CAAC,CAAC;EACpB,IAAIiB,IAAI,GAAGvC,IAAI,CAACsB,GAAG,CAAC,CAAC;EAErB,IAAIgB,IAAI,GAAG,IAAI,IAAIC,IAAI,GAAG,IAAI,EAAE;IAC9B;EACF;EAEAxC,GAAG,CAACyC,KAAK,CAAC,CAAC,GAAGF,IAAI,CAAC;EACnBtC,IAAI,CAACwC,KAAK,CAAC,CAAC,GAAGD,IAAI,CAAC;EACpB,IAAIE,QAAQ,GAAG1C,GAAG,CAAC2C,GAAG,CAAC1C,IAAI,CAAC;EAC5B,IAAI2C,eAAe,GAAG9H,IAAI,CAACgC,GAAG,CAACsF,YAAY,CAAC;EAE5C,IAAIQ,eAAe,GAAGF,QAAQ,EAAE;IAC9B;IACA;IACA,IAAIrG,CAAC,GAAGgB,kBAAkB,CAACyC,GAAG,CAACnE,CAAC,EAAEmE,GAAG,CAAClE,CAAC,EAAEmE,GAAG,CAACpE,CAAC,EAAEoE,GAAG,CAACnE,CAAC,EAAEiE,GAAG,CAAClE,CAAC,EAAEkE,GAAG,CAACjE,CAAC,EAAEqG,MAAM,EAAE,KAAK,CAAC;IACnFC,YAAY,CAACG,SAAS,CAACJ,MAAM,CAAC,CAAC,CAAC;;IAEhCC,YAAY,CAACP,WAAW,CAAC1B,IAAI,EAAE5D,CAAC,GAAGvB,IAAI,CAAC+H,GAAG,CAAC/H,IAAI,CAACC,EAAE,GAAGqH,YAAY,CAAC,CAAC,CAAC,CAAC;;IAEtE,IAAIvE,CAAC,GAAGkC,GAAG,CAACpE,CAAC,KAAKmE,GAAG,CAACnE,CAAC,GAAG,CAACuG,YAAY,CAACvG,CAAC,GAAGmE,GAAG,CAACnE,CAAC,KAAKoE,GAAG,CAACpE,CAAC,GAAGmE,GAAG,CAACnE,CAAC,CAAC,GAAG,CAACuG,YAAY,CAACtG,CAAC,GAAGkE,GAAG,CAAClE,CAAC,KAAKmE,GAAG,CAACnE,CAAC,GAAGkE,GAAG,CAAClE,CAAC,CAAC;IAEjH,IAAIkH,KAAK,CAACjF,CAAC,CAAC,EAAE;MACZ;IACF;IAEA,IAAIA,CAAC,GAAG,CAAC,EAAE;MACT7D,KAAK,CAACyH,IAAI,CAACS,YAAY,EAAEpC,GAAG,CAAC;IAC/B,CAAC,MAAM,IAAIjC,CAAC,GAAG,CAAC,EAAE;MAChB7D,KAAK,CAACyH,IAAI,CAACS,YAAY,EAAEnC,GAAG,CAAC;IAC/B;IAEAmC,YAAY,CAACJ,OAAO,CAACK,UAAU,CAAC,CAAC,CAAC,CAAC;EACrC;AACF;AACA;AACA;AACA;AACA;;AAEA,OAAO,SAASY,iBAAiBA,CAACZ,UAAU,EAAEa,aAAa,EAAEC,eAAe,EAAE;EAC5E,IAAI,EAAEA,eAAe,IAAI,GAAG,IAAIA,eAAe,GAAG,CAAC,CAAC,EAAE;IACpD;EACF;EAEAA,eAAe,GAAGA,eAAe,GAAG,GAAG,GAAGnI,IAAI,CAACC,EAAE;EACjD8E,GAAG,CAACwC,SAAS,CAACF,UAAU,CAAC,CAAC,CAAC,CAAC;EAC5BrC,GAAG,CAACuC,SAAS,CAACF,UAAU,CAAC,CAAC,CAAC,CAAC;EAC5BpC,GAAG,CAACsC,SAAS,CAACF,UAAU,CAAC,CAAC,CAAC,CAAC;EAC5BnI,KAAK,CAACsI,GAAG,CAACtC,GAAG,EAAEF,GAAG,EAAED,GAAG,CAAC;EACxB7F,KAAK,CAACsI,GAAG,CAACrC,IAAI,EAAEF,GAAG,EAAED,GAAG,CAAC;EACzB,IAAIyC,IAAI,GAAGvC,GAAG,CAACuB,GAAG,CAAC,CAAC;EACpB,IAAIiB,IAAI,GAAGvC,IAAI,CAACsB,GAAG,CAAC,CAAC;EAErB,IAAIgB,IAAI,GAAG,IAAI,IAAIC,IAAI,GAAG,IAAI,EAAE;IAC9B;EACF;EAEAxC,GAAG,CAACyC,KAAK,CAAC,CAAC,GAAGF,IAAI,CAAC;EACnBtC,IAAI,CAACwC,KAAK,CAAC,CAAC,GAAGD,IAAI,CAAC;EACpB,IAAIE,QAAQ,GAAG1C,GAAG,CAAC2C,GAAG,CAACK,aAAa,CAAC;EACrC,IAAIE,kBAAkB,GAAGpI,IAAI,CAACgC,GAAG,CAACmG,eAAe,CAAC;EAElD,IAAIP,QAAQ,GAAGQ,kBAAkB,EAAE;IACjC;IACA,IAAI7G,CAAC,GAAGgB,kBAAkB,CAACyC,GAAG,CAACnE,CAAC,EAAEmE,GAAG,CAAClE,CAAC,EAAEmE,GAAG,CAACpE,CAAC,EAAEoE,GAAG,CAACnE,CAAC,EAAEiE,GAAG,CAAClE,CAAC,EAAEkE,GAAG,CAACjE,CAAC,EAAEqG,MAAM,EAAE,KAAK,CAAC;IACnFC,YAAY,CAACG,SAAS,CAACJ,MAAM,CAAC;IAC9B,IAAIkB,OAAO,GAAGrI,IAAI,CAACC,EAAE,GAAG,CAAC;IACzB,IAAIqI,MAAM,GAAGtI,IAAI,CAACuI,IAAI,CAACpD,IAAI,CAAC0C,GAAG,CAACK,aAAa,CAAC,CAAC;IAC/C,IAAIM,QAAQ,GAAGH,OAAO,GAAGC,MAAM,GAAGH,eAAe;IAEjD,IAAIK,QAAQ,IAAIH,OAAO,EAAE;MACvB;MACAnJ,KAAK,CAACyH,IAAI,CAACS,YAAY,EAAEnC,GAAG,CAAC;IAC/B,CAAC,MAAM;MACL;MACAmC,YAAY,CAACP,WAAW,CAAC1B,IAAI,EAAE5D,CAAC,GAAGvB,IAAI,CAAC+H,GAAG,CAAC/H,IAAI,CAACC,EAAE,GAAG,CAAC,GAAGuI,QAAQ,CAAC,CAAC,CAAC,CAAC;;MAEtE,IAAIzF,CAAC,GAAGkC,GAAG,CAACpE,CAAC,KAAKmE,GAAG,CAACnE,CAAC,GAAG,CAACuG,YAAY,CAACvG,CAAC,GAAGmE,GAAG,CAACnE,CAAC,KAAKoE,GAAG,CAACpE,CAAC,GAAGmE,GAAG,CAACnE,CAAC,CAAC,GAAG,CAACuG,YAAY,CAACtG,CAAC,GAAGkE,GAAG,CAAClE,CAAC,KAAKmE,GAAG,CAACnE,CAAC,GAAGkE,GAAG,CAAClE,CAAC,CAAC;MAEjH,IAAIkH,KAAK,CAACjF,CAAC,CAAC,EAAE;QACZ;MACF;MAEA,IAAIA,CAAC,GAAG,CAAC,EAAE;QACT7D,KAAK,CAACyH,IAAI,CAACS,YAAY,EAAEpC,GAAG,CAAC;MAC/B,CAAC,MAAM,IAAIjC,CAAC,GAAG,CAAC,EAAE;QAChB7D,KAAK,CAACyH,IAAI,CAACS,YAAY,EAAEnC,GAAG,CAAC;MAC/B;IACF;IAEAmC,YAAY,CAACJ,OAAO,CAACK,UAAU,CAAC,CAAC,CAAC,CAAC;EACrC;AACF;AAEA,SAASoB,iBAAiBA,CAAClD,SAAS,EAAEmD,MAAM,EAAEC,SAAS,EAAEC,UAAU,EAAE;EACnE,IAAIC,QAAQ,GAAGF,SAAS,KAAK,QAAQ;EACrC,IAAIG,QAAQ,GAAGD,QAAQ,GAAGtD,SAAS,GAAGA,SAAS,CAACwD,WAAW,CAACJ,SAAS,CAAC,CAAC,CAAC;;EAExEG,QAAQ,CAACJ,MAAM,GAAGA,MAAM,CAAC,CAAC;;EAE1B,IAAIM,MAAM,GAAGJ,UAAU,CAAClC,GAAG,CAAC,QAAQ,CAAC;EAErC,IAAIsC,MAAM,IAAIA,MAAM,KAAK,IAAI,EAAE;IAC7BA,MAAM,GAAG,GAAG;EACd;EAEAF,QAAQ,CAACG,KAAK,GAAGH,QAAQ,CAACG,KAAK,IAAI,CAAC,CAAC;EAErC,IAAID,MAAM,GAAG,CAAC,EAAE;IACdF,QAAQ,CAACG,KAAK,CAACD,MAAM,GAAGA,MAAM;EAChC;EAEA,IAAIE,QAAQ,GAAGN,UAAU,CAACO,QAAQ,CAAC,WAAW,CAAC,CAACC,YAAY,CAAC,CAAC;EAC9DP,QAAQ,GAAGtD,SAAS,CAAC8D,QAAQ,CAACH,QAAQ,CAAC,GAAGJ,QAAQ,CAACQ,KAAK,GAAGJ,QAAQ;AACrE;AAEA,SAASK,kBAAkBA,CAAC/F,IAAI,EAAEyF,KAAK,EAAE;EACvC,IAAID,MAAM,GAAGC,KAAK,CAACD,MAAM;EACzB,IAAInD,MAAM,GAAGoD,KAAK,CAACpD,MAAM;EAEzB,IAAI,CAACA,MAAM,EAAE;IACX;EACF;EAEArC,IAAI,CAACgG,MAAM,CAAC3D,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAEA,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAEvC,IAAImD,MAAM,GAAG,CAAC,IAAInD,MAAM,CAAC5B,MAAM,IAAI,CAAC,EAAE;IACpC,IAAIwD,IAAI,GAAG7H,MAAM,CAAC0D,IAAI,CAACuC,MAAM,CAAC,CAAC,CAAC,EAAEA,MAAM,CAAC,CAAC,CAAC,CAAC;IAC5C,IAAI6B,IAAI,GAAG9H,MAAM,CAAC0D,IAAI,CAACuC,MAAM,CAAC,CAAC,CAAC,EAAEA,MAAM,CAAC,CAAC,CAAC,CAAC;IAE5C,IAAI,CAAC4B,IAAI,IAAI,CAACC,IAAI,EAAE;MAClBlE,IAAI,CAACiG,MAAM,CAAC5D,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAEA,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACvCrC,IAAI,CAACiG,MAAM,CAAC5D,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAEA,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACvC;IACF;IAEA,IAAI6D,OAAO,GAAG1J,IAAI,CAACgD,GAAG,CAACyE,IAAI,EAAEC,IAAI,CAAC,GAAGsB,MAAM;IAC3C,IAAIW,SAAS,GAAG/J,MAAM,CAACgK,IAAI,CAAC,EAAE,EAAE/D,MAAM,CAAC,CAAC,CAAC,EAAEA,MAAM,CAAC,CAAC,CAAC,EAAE6D,OAAO,GAAGjC,IAAI,CAAC;IACrE,IAAIoC,SAAS,GAAGjK,MAAM,CAACgK,IAAI,CAAC,EAAE,EAAE/D,MAAM,CAAC,CAAC,CAAC,EAAEA,MAAM,CAAC,CAAC,CAAC,EAAE6D,OAAO,GAAGhC,IAAI,CAAC;IACrE,IAAIoC,SAAS,GAAGlK,MAAM,CAACgK,IAAI,CAAC,EAAE,EAAED,SAAS,EAAEE,SAAS,EAAE,GAAG,CAAC;IAC1DrG,IAAI,CAACuG,aAAa,CAACJ,SAAS,CAAC,CAAC,CAAC,EAAEA,SAAS,CAAC,CAAC,CAAC,EAAEA,SAAS,CAAC,CAAC,CAAC,EAAEA,SAAS,CAAC,CAAC,CAAC,EAAEG,SAAS,CAAC,CAAC,CAAC,EAAEA,SAAS,CAAC,CAAC,CAAC,CAAC;IACtGtG,IAAI,CAACuG,aAAa,CAACF,SAAS,CAAC,CAAC,CAAC,EAAEA,SAAS,CAAC,CAAC,CAAC,EAAEA,SAAS,CAAC,CAAC,CAAC,EAAEA,SAAS,CAAC,CAAC,CAAC,EAAEhE,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAEA,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACxG,CAAC,MAAM;IACL,KAAK,IAAI7B,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG6B,MAAM,CAAC5B,MAAM,EAAED,CAAC,EAAE,EAAE;MACtCR,IAAI,CAACiG,MAAM,CAAC5D,MAAM,CAAC7B,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE6B,MAAM,CAAC7B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACzC;EACF;AACF;AACA;AACA;AACA;;AAGA,OAAO,SAASgG,iBAAiBA,CAACC,QAAQ,EAAEC,YAAY,EAAEC,YAAY,EAAE;EACtE,IAAI5E,SAAS,GAAG0E,QAAQ,CAACzE,gBAAgB,CAAC,CAAC;EAC3C,IAAIC,KAAK,GAAGwE,QAAQ,CAACvE,cAAc,CAAC,CAAC;EAErC,IAAI,CAACD,KAAK,EAAE;IACV;IACA,IAAIF,SAAS,EAAE;MACb0E,QAAQ,CAACG,mBAAmB,CAAC,CAAC;IAChC;IAEA;EACF;EAEA,IAAIC,WAAW,GAAGH,YAAY,CAACI,MAAM;EACrC,IAAIC,UAAU,GAAGF,WAAW,CAAC3D,GAAG,CAAC,MAAM,CAAC;EACxC,IAAI8D,iBAAiB,GAAG/E,KAAK,CAACiD,MAAM;EAEpC,KAAK,IAAI1E,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGnE,cAAc,CAACoE,MAAM,EAAED,CAAC,EAAE,EAAE;IAC9C,IAAI2E,SAAS,GAAG9I,cAAc,CAACmE,CAAC,CAAC;IACjC,IAAI4E,UAAU,GAAGsB,YAAY,CAACvB,SAAS,CAAC;IACxC,IAAIE,QAAQ,GAAGF,SAAS,KAAK,QAAQ;IAErC,IAAIC,UAAU,EAAE;MACd,IAAI6B,SAAS,GAAG7B,UAAU,CAAClC,GAAG,CAAC,MAAM,CAAC;MACtC,IAAIgE,cAAc,GAAG7B,QAAQ,GAAG2B,iBAAiB,GAAG9K,SAAS,CAAC+F,KAAK,CAACkF,MAAM,CAAChC,SAAS,CAAC,IAAIlD,KAAK,CAACkF,MAAM,CAAChC,SAAS,CAAC,CAACD,MAAM,EAAE8B,iBAAiB,CAAC;MAE3I,IAAIE,cAAc,CAAC;MAAA,GAChB,CAAChL,SAAS,CAAC+K,SAAS,EAAEF,UAAU,CAAC,CAAC;MAAA,EACnC;QACE,IAAIzB,QAAQ,GAAGD,QAAQ,GAAGtD,SAAS,GAAGA,SAAS,IAAIA,SAAS,CAACoF,MAAM,CAAChC,SAAS,CAAC;QAE9E,IAAIG,QAAQ,EAAE;UACZA,QAAQ,CAACJ,MAAM,GAAG,IAAI;QACxB;QAEA;MACF,CAAC,CAAC;;MAGJ,IAAI,CAACnD,SAAS,EAAE;QACdA,SAAS,GAAG,IAAInG,QAAQ,CAAC,CAAC;QAC1B6K,QAAQ,CAACW,gBAAgB,CAACrF,SAAS,CAAC,CAAC,CAAC;QACtC;;QAEA,IAAI,CAACsD,QAAQ,KAAK2B,iBAAiB,IAAI,CAACD,UAAU,CAAC,EAAE;UACnD9B,iBAAiB,CAAClD,SAAS,EAAE,IAAI,EAAE,QAAQ,EAAE2E,YAAY,CAACI,MAAM,CAAC;QACnE,CAAC,CAAC;;QAGF,IAAIL,QAAQ,CAACY,UAAU,EAAE;UACvBtF,SAAS,CAACsF,UAAU,GAAGZ,QAAQ,CAACY,UAAU;QAC5C;MACF;MAEApC,iBAAiB,CAAClD,SAAS,EAAE,KAAK,EAAEoD,SAAS,EAAEC,UAAU,CAAC;IAC5D;EACF;EAEA,IAAIrD,SAAS,EAAE;IACb9F,QAAQ,CAAC8F,SAAS,CAAC+D,KAAK,EAAEa,YAAY,CAAC,CAAC,CAAC;;IAEzC5E,SAAS,CAAC+D,KAAK,CAACwB,IAAI,GAAG,IAAI;IAC3B,IAAIC,SAAS,GAAGV,WAAW,CAAC3D,GAAG,CAAC,WAAW,CAAC;IAC5C,IAAIsE,eAAe,GAAGf,QAAQ,CAACrE,mBAAmB,GAAGqE,QAAQ,CAACrE,mBAAmB,IAAI,CAAC,CAAC;IACvFoF,eAAe,CAACD,SAAS,GAAGA,SAAS,IAAI,KAAK,CAAC,CAAC;;IAEhDxF,SAAS,CAAC0F,SAAS,GAAG1B,kBAAkB;EAC1C;AACF;AACA,OAAO,SAAS2B,wBAAwBA,CAACC,SAAS,EAAEC,aAAa,EAAE;EACjEA,aAAa,GAAGA,aAAa,IAAI,WAAW;EAC5C,IAAIlB,YAAY,GAAG;IACjBI,MAAM,EAAEa,SAAS,CAAChC,QAAQ,CAACiC,aAAa;EAC1C,CAAC;EAED,KAAK,IAAIpH,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGlE,cAAc,CAACmE,MAAM,EAAED,CAAC,EAAE,EAAE;IAC9C,IAAI2E,SAAS,GAAG7I,cAAc,CAACkE,CAAC,CAAC;IACjCkG,YAAY,CAACvB,SAAS,CAAC,GAAGwC,SAAS,CAAChC,QAAQ,CAAC,CAACR,SAAS,EAAEyC,aAAa,CAAC,CAAC;EAC1E;EAEA,OAAOlB,YAAY;AACrB"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}