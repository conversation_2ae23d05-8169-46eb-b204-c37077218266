{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\n\n/**\r\n * Parallel Coordinates\r\n * <https://en.wikipedia.org/wiki/Parallel_coordinates>\r\n */\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport * as matrix from 'zrender/lib/core/matrix.js';\nimport * as layoutUtil from '../../util/layout.js';\nimport * as axisHelper from '../../coord/axisHelper.js';\nimport ParallelAxis from './ParallelAxis.js';\nimport * as graphic from '../../util/graphic.js';\nimport * as numberUtil from '../../util/number.js';\nimport sliderMove from '../../component/helper/sliderMove.js';\nvar each = zrUtil.each;\nvar mathMin = Math.min;\nvar mathMax = Math.max;\nvar mathFloor = Math.floor;\nvar mathCeil = Math.ceil;\nvar round = numberUtil.round;\nvar PI = Math.PI;\nvar Parallel = /** @class */\nfunction () {\n  function Parallel(parallelModel, ecModel, api) {\n    this.type = 'parallel';\n    /**\r\n     * key: dimension\r\n     */\n\n    this._axesMap = zrUtil.createHashMap();\n    /**\r\n     * key: dimension\r\n     * value: {position: [], rotation, }\r\n     */\n\n    this._axesLayout = {};\n    this.dimensions = parallelModel.dimensions;\n    this._model = parallelModel;\n    this._init(parallelModel, ecModel, api);\n  }\n  Parallel.prototype._init = function (parallelModel, ecModel, api) {\n    var dimensions = parallelModel.dimensions;\n    var parallelAxisIndex = parallelModel.parallelAxisIndex;\n    each(dimensions, function (dim, idx) {\n      var axisIndex = parallelAxisIndex[idx];\n      var axisModel = ecModel.getComponent('parallelAxis', axisIndex);\n      var axis = this._axesMap.set(dim, new ParallelAxis(dim, axisHelper.createScaleByModel(axisModel), [0, 0], axisModel.get('type'), axisIndex));\n      var isCategory = axis.type === 'category';\n      axis.onBand = isCategory && axisModel.get('boundaryGap');\n      axis.inverse = axisModel.get('inverse'); // Injection\n\n      axisModel.axis = axis;\n      axis.model = axisModel;\n      axis.coordinateSystem = axisModel.coordinateSystem = this;\n    }, this);\n  };\n  /**\r\n   * Update axis scale after data processed\r\n   */\n\n  Parallel.prototype.update = function (ecModel, api) {\n    this._updateAxesFromSeries(this._model, ecModel);\n  };\n  Parallel.prototype.containPoint = function (point) {\n    var layoutInfo = this._makeLayoutInfo();\n    var axisBase = layoutInfo.axisBase;\n    var layoutBase = layoutInfo.layoutBase;\n    var pixelDimIndex = layoutInfo.pixelDimIndex;\n    var pAxis = point[1 - pixelDimIndex];\n    var pLayout = point[pixelDimIndex];\n    return pAxis >= axisBase && pAxis <= axisBase + layoutInfo.axisLength && pLayout >= layoutBase && pLayout <= layoutBase + layoutInfo.layoutLength;\n  };\n  Parallel.prototype.getModel = function () {\n    return this._model;\n  };\n  /**\r\n   * Update properties from series\r\n   */\n\n  Parallel.prototype._updateAxesFromSeries = function (parallelModel, ecModel) {\n    ecModel.eachSeries(function (seriesModel) {\n      if (!parallelModel.contains(seriesModel, ecModel)) {\n        return;\n      }\n      var data = seriesModel.getData();\n      each(this.dimensions, function (dim) {\n        var axis = this._axesMap.get(dim);\n        axis.scale.unionExtentFromData(data, data.mapDimension(dim));\n        axisHelper.niceScaleExtent(axis.scale, axis.model);\n      }, this);\n    }, this);\n  };\n  /**\r\n   * Resize the parallel coordinate system.\r\n   */\n\n  Parallel.prototype.resize = function (parallelModel, api) {\n    this._rect = layoutUtil.getLayoutRect(parallelModel.getBoxLayoutParams(), {\n      width: api.getWidth(),\n      height: api.getHeight()\n    });\n    this._layoutAxes();\n  };\n  Parallel.prototype.getRect = function () {\n    return this._rect;\n  };\n  Parallel.prototype._makeLayoutInfo = function () {\n    var parallelModel = this._model;\n    var rect = this._rect;\n    var xy = ['x', 'y'];\n    var wh = ['width', 'height'];\n    var layout = parallelModel.get('layout');\n    var pixelDimIndex = layout === 'horizontal' ? 0 : 1;\n    var layoutLength = rect[wh[pixelDimIndex]];\n    var layoutExtent = [0, layoutLength];\n    var axisCount = this.dimensions.length;\n    var axisExpandWidth = restrict(parallelModel.get('axisExpandWidth'), layoutExtent);\n    var axisExpandCount = restrict(parallelModel.get('axisExpandCount') || 0, [0, axisCount]);\n    var axisExpandable = parallelModel.get('axisExpandable') && axisCount > 3 && axisCount > axisExpandCount && axisExpandCount > 1 && axisExpandWidth > 0 && layoutLength > 0; // `axisExpandWindow` is According to the coordinates of [0, axisExpandLength],\n    // for sake of consider the case that axisCollapseWidth is 0 (when screen is narrow),\n    // where collapsed axes should be overlapped.\n\n    var axisExpandWindow = parallelModel.get('axisExpandWindow');\n    var winSize;\n    if (!axisExpandWindow) {\n      winSize = restrict(axisExpandWidth * (axisExpandCount - 1), layoutExtent);\n      var axisExpandCenter = parallelModel.get('axisExpandCenter') || mathFloor(axisCount / 2);\n      axisExpandWindow = [axisExpandWidth * axisExpandCenter - winSize / 2];\n      axisExpandWindow[1] = axisExpandWindow[0] + winSize;\n    } else {\n      winSize = restrict(axisExpandWindow[1] - axisExpandWindow[0], layoutExtent);\n      axisExpandWindow[1] = axisExpandWindow[0] + winSize;\n    }\n    var axisCollapseWidth = (layoutLength - winSize) / (axisCount - axisExpandCount); // Avoid axisCollapseWidth is too small.\n\n    axisCollapseWidth < 3 && (axisCollapseWidth = 0); // Find the first and last indices > ewin[0] and < ewin[1].\n\n    var winInnerIndices = [mathFloor(round(axisExpandWindow[0] / axisExpandWidth, 1)) + 1, mathCeil(round(axisExpandWindow[1] / axisExpandWidth, 1)) - 1]; // Pos in ec coordinates.\n\n    var axisExpandWindow0Pos = axisCollapseWidth / axisExpandWidth * axisExpandWindow[0];\n    return {\n      layout: layout,\n      pixelDimIndex: pixelDimIndex,\n      layoutBase: rect[xy[pixelDimIndex]],\n      layoutLength: layoutLength,\n      axisBase: rect[xy[1 - pixelDimIndex]],\n      axisLength: rect[wh[1 - pixelDimIndex]],\n      axisExpandable: axisExpandable,\n      axisExpandWidth: axisExpandWidth,\n      axisCollapseWidth: axisCollapseWidth,\n      axisExpandWindow: axisExpandWindow,\n      axisCount: axisCount,\n      winInnerIndices: winInnerIndices,\n      axisExpandWindow0Pos: axisExpandWindow0Pos\n    };\n  };\n  Parallel.prototype._layoutAxes = function () {\n    var rect = this._rect;\n    var axes = this._axesMap;\n    var dimensions = this.dimensions;\n    var layoutInfo = this._makeLayoutInfo();\n    var layout = layoutInfo.layout;\n    axes.each(function (axis) {\n      var axisExtent = [0, layoutInfo.axisLength];\n      var idx = axis.inverse ? 1 : 0;\n      axis.setExtent(axisExtent[idx], axisExtent[1 - idx]);\n    });\n    each(dimensions, function (dim, idx) {\n      var posInfo = (layoutInfo.axisExpandable ? layoutAxisWithExpand : layoutAxisWithoutExpand)(idx, layoutInfo);\n      var positionTable = {\n        horizontal: {\n          x: posInfo.position,\n          y: layoutInfo.axisLength\n        },\n        vertical: {\n          x: 0,\n          y: posInfo.position\n        }\n      };\n      var rotationTable = {\n        horizontal: PI / 2,\n        vertical: 0\n      };\n      var position = [positionTable[layout].x + rect.x, positionTable[layout].y + rect.y];\n      var rotation = rotationTable[layout];\n      var transform = matrix.create();\n      matrix.rotate(transform, transform, rotation);\n      matrix.translate(transform, transform, position); // TODO\n      // tick layout info\n      // TODO\n      // update dimensions info based on axis order.\n\n      this._axesLayout[dim] = {\n        position: position,\n        rotation: rotation,\n        transform: transform,\n        axisNameAvailableWidth: posInfo.axisNameAvailableWidth,\n        axisLabelShow: posInfo.axisLabelShow,\n        nameTruncateMaxWidth: posInfo.nameTruncateMaxWidth,\n        tickDirection: 1,\n        labelDirection: 1\n      };\n    }, this);\n  };\n  /**\r\n   * Get axis by dim.\r\n   */\n\n  Parallel.prototype.getAxis = function (dim) {\n    return this._axesMap.get(dim);\n  };\n  /**\r\n   * Convert a dim value of a single item of series data to Point.\r\n   */\n\n  Parallel.prototype.dataToPoint = function (value, dim) {\n    return this.axisCoordToPoint(this._axesMap.get(dim).dataToCoord(value), dim);\n  };\n  /**\r\n   * Travel data for one time, get activeState of each data item.\r\n   * @param start the start dataIndex that travel from.\r\n   * @param end the next dataIndex of the last dataIndex will be travel.\r\n   */\n\n  Parallel.prototype.eachActiveState = function (data, callback, start, end) {\n    start == null && (start = 0);\n    end == null && (end = data.count());\n    var axesMap = this._axesMap;\n    var dimensions = this.dimensions;\n    var dataDimensions = [];\n    var axisModels = [];\n    zrUtil.each(dimensions, function (axisDim) {\n      dataDimensions.push(data.mapDimension(axisDim));\n      axisModels.push(axesMap.get(axisDim).model);\n    });\n    var hasActiveSet = this.hasAxisBrushed();\n    for (var dataIndex = start; dataIndex < end; dataIndex++) {\n      var activeState = void 0;\n      if (!hasActiveSet) {\n        activeState = 'normal';\n      } else {\n        activeState = 'active';\n        var values = data.getValues(dataDimensions, dataIndex);\n        for (var j = 0, lenj = dimensions.length; j < lenj; j++) {\n          var state = axisModels[j].getActiveState(values[j]);\n          if (state === 'inactive') {\n            activeState = 'inactive';\n            break;\n          }\n        }\n      }\n      callback(activeState, dataIndex);\n    }\n  };\n  /**\r\n   * Whether has any activeSet.\r\n   */\n\n  Parallel.prototype.hasAxisBrushed = function () {\n    var dimensions = this.dimensions;\n    var axesMap = this._axesMap;\n    var hasActiveSet = false;\n    for (var j = 0, lenj = dimensions.length; j < lenj; j++) {\n      if (axesMap.get(dimensions[j]).model.getActiveState() !== 'normal') {\n        hasActiveSet = true;\n      }\n    }\n    return hasActiveSet;\n  };\n  /**\r\n   * Convert coords of each axis to Point.\r\n   *  Return point. For example: [10, 20]\r\n   */\n\n  Parallel.prototype.axisCoordToPoint = function (coord, dim) {\n    var axisLayout = this._axesLayout[dim];\n    return graphic.applyTransform([coord, 0], axisLayout.transform);\n  };\n  /**\r\n   * Get axis layout.\r\n   */\n\n  Parallel.prototype.getAxisLayout = function (dim) {\n    return zrUtil.clone(this._axesLayout[dim]);\n  };\n  /**\r\n   * @return {Object} {axisExpandWindow, delta, behavior: 'jump' | 'slide' | 'none'}.\r\n   */\n\n  Parallel.prototype.getSlidedAxisExpandWindow = function (point) {\n    var layoutInfo = this._makeLayoutInfo();\n    var pixelDimIndex = layoutInfo.pixelDimIndex;\n    var axisExpandWindow = layoutInfo.axisExpandWindow.slice();\n    var winSize = axisExpandWindow[1] - axisExpandWindow[0];\n    var extent = [0, layoutInfo.axisExpandWidth * (layoutInfo.axisCount - 1)]; // Out of the area of coordinate system.\n\n    if (!this.containPoint(point)) {\n      return {\n        behavior: 'none',\n        axisExpandWindow: axisExpandWindow\n      };\n    } // Convert the point from global to expand coordinates.\n\n    var pointCoord = point[pixelDimIndex] - layoutInfo.layoutBase - layoutInfo.axisExpandWindow0Pos; // For dragging operation convenience, the window should not be\n    // slided when mouse is the center area of the window.\n\n    var delta;\n    var behavior = 'slide';\n    var axisCollapseWidth = layoutInfo.axisCollapseWidth;\n    var triggerArea = this._model.get('axisExpandSlideTriggerArea'); // But consider touch device, jump is necessary.\n\n    var useJump = triggerArea[0] != null;\n    if (axisCollapseWidth) {\n      if (useJump && axisCollapseWidth && pointCoord < winSize * triggerArea[0]) {\n        behavior = 'jump';\n        delta = pointCoord - winSize * triggerArea[2];\n      } else if (useJump && axisCollapseWidth && pointCoord > winSize * (1 - triggerArea[0])) {\n        behavior = 'jump';\n        delta = pointCoord - winSize * (1 - triggerArea[2]);\n      } else {\n        (delta = pointCoord - winSize * triggerArea[1]) >= 0 && (delta = pointCoord - winSize * (1 - triggerArea[1])) <= 0 && (delta = 0);\n      }\n      delta *= layoutInfo.axisExpandWidth / axisCollapseWidth;\n      delta ? sliderMove(delta, axisExpandWindow, extent, 'all') // Avoid nonsense triger on mousemove.\n      : behavior = 'none';\n    } // When screen is too narrow, make it visible and slidable, although it is hard to interact.\n    else {\n      var winSize2 = axisExpandWindow[1] - axisExpandWindow[0];\n      var pos = extent[1] * pointCoord / winSize2;\n      axisExpandWindow = [mathMax(0, pos - winSize2 / 2)];\n      axisExpandWindow[1] = mathMin(extent[1], axisExpandWindow[0] + winSize2);\n      axisExpandWindow[0] = axisExpandWindow[1] - winSize2;\n    }\n    return {\n      axisExpandWindow: axisExpandWindow,\n      behavior: behavior\n    };\n  };\n  return Parallel;\n}();\nfunction restrict(len, extent) {\n  return mathMin(mathMax(len, extent[0]), extent[1]);\n}\nfunction layoutAxisWithoutExpand(axisIndex, layoutInfo) {\n  var step = layoutInfo.layoutLength / (layoutInfo.axisCount - 1);\n  return {\n    position: step * axisIndex,\n    axisNameAvailableWidth: step,\n    axisLabelShow: true\n  };\n}\nfunction layoutAxisWithExpand(axisIndex, layoutInfo) {\n  var layoutLength = layoutInfo.layoutLength;\n  var axisExpandWidth = layoutInfo.axisExpandWidth;\n  var axisCount = layoutInfo.axisCount;\n  var axisCollapseWidth = layoutInfo.axisCollapseWidth;\n  var winInnerIndices = layoutInfo.winInnerIndices;\n  var position;\n  var axisNameAvailableWidth = axisCollapseWidth;\n  var axisLabelShow = false;\n  var nameTruncateMaxWidth;\n  if (axisIndex < winInnerIndices[0]) {\n    position = axisIndex * axisCollapseWidth;\n    nameTruncateMaxWidth = axisCollapseWidth;\n  } else if (axisIndex <= winInnerIndices[1]) {\n    position = layoutInfo.axisExpandWindow0Pos + axisIndex * axisExpandWidth - layoutInfo.axisExpandWindow[0];\n    axisNameAvailableWidth = axisExpandWidth;\n    axisLabelShow = true;\n  } else {\n    position = layoutLength - (axisCount - 1 - axisIndex) * axisCollapseWidth;\n    nameTruncateMaxWidth = axisCollapseWidth;\n  }\n  return {\n    position: position,\n    axisNameAvailableWidth: axisNameAvailableWidth,\n    axisLabelShow: axisLabelShow,\n    nameTruncateMaxWidth: nameTruncateMaxWidth\n  };\n}\nexport default Parallel;", "map": {"version": 3, "names": ["zrUtil", "matrix", "layoutUtil", "axisHelper", "ParallelAxis", "graphic", "numberUtil", "slider<PERSON><PERSON>", "each", "mathMin", "Math", "min", "mathMax", "max", "mathFloor", "floor", "math<PERSON>eil", "ceil", "round", "PI", "<PERSON><PERSON><PERSON>", "parallelModel", "ecModel", "api", "type", "_axesMap", "createHashMap", "_axesLayout", "dimensions", "_model", "_init", "prototype", "parallelAxisIndex", "dim", "idx", "axisIndex", "axisModel", "getComponent", "axis", "set", "createScaleByModel", "get", "isCategory", "onBand", "inverse", "model", "coordinateSystem", "update", "_updateAxesFromSeries", "containPoint", "point", "layoutInfo", "_makeLayoutInfo", "axisBase", "layoutBase", "pixelDimIndex", "pAxis", "pLayout", "axisLength", "<PERSON><PERSON><PERSON><PERSON>", "getModel", "eachSeries", "seriesModel", "contains", "data", "getData", "scale", "unionExtentFromData", "mapDimension", "niceScaleExtent", "resize", "_rect", "getLayoutRect", "getBoxLayoutParams", "width", "getWidth", "height", "getHeight", "_layoutAxes", "getRect", "rect", "xy", "wh", "layout", "layoutExtent", "axisCount", "length", "axisExpandWidth", "restrict", "axisExpandCount", "axisExpandable", "axisExpandWindow", "winSize", "axisExpandCenter", "axisCollapseWidth", "winInnerIndices", "axisExpandWindow0Pos", "axes", "axisExtent", "setExtent", "posInfo", "layoutAxisWithExpand", "layoutAxisWithoutExpand", "positionTable", "horizontal", "x", "position", "y", "vertical", "rotationTable", "rotation", "transform", "create", "rotate", "translate", "axisNameAvailableWidth", "axisLabelShow", "nameTruncateMaxWidth", "tickDirection", "labelDirection", "getAxis", "dataToPoint", "value", "axisCoordToPoint", "dataToCoord", "eachActiveState", "callback", "start", "end", "count", "axesMap", "dataDimensions", "axisModels", "axisDim", "push", "hasActiveSet", "hasAxisBrushed", "dataIndex", "activeState", "values", "getV<PERSON>ues", "j", "lenj", "state", "getActiveState", "coord", "axisLayout", "applyTransform", "getAxisLayout", "clone", "getSlidedAxisExpandWindow", "slice", "extent", "behavior", "pointCoord", "delta", "triggerArea", "useJump", "winSize2", "pos", "len", "step"], "sources": ["C:/WolfSystemV2/WolfOldFrontEnd/node_modules/echarts/lib/coord/parallel/Parallel.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\n\n/**\r\n * Parallel Coordinates\r\n * <https://en.wikipedia.org/wiki/Parallel_coordinates>\r\n */\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport * as matrix from 'zrender/lib/core/matrix.js';\nimport * as layoutUtil from '../../util/layout.js';\nimport * as axisHelper from '../../coord/axisHelper.js';\nimport ParallelAxis from './ParallelAxis.js';\nimport * as graphic from '../../util/graphic.js';\nimport * as numberUtil from '../../util/number.js';\nimport sliderMove from '../../component/helper/sliderMove.js';\nvar each = zrUtil.each;\nvar mathMin = Math.min;\nvar mathMax = Math.max;\nvar mathFloor = Math.floor;\nvar mathCeil = Math.ceil;\nvar round = numberUtil.round;\nvar PI = Math.PI;\n\nvar Parallel =\n/** @class */\nfunction () {\n  function Parallel(parallelModel, ecModel, api) {\n    this.type = 'parallel';\n    /**\r\n     * key: dimension\r\n     */\n\n    this._axesMap = zrUtil.createHashMap();\n    /**\r\n     * key: dimension\r\n     * value: {position: [], rotation, }\r\n     */\n\n    this._axesLayout = {};\n    this.dimensions = parallelModel.dimensions;\n    this._model = parallelModel;\n\n    this._init(parallelModel, ecModel, api);\n  }\n\n  Parallel.prototype._init = function (parallelModel, ecModel, api) {\n    var dimensions = parallelModel.dimensions;\n    var parallelAxisIndex = parallelModel.parallelAxisIndex;\n    each(dimensions, function (dim, idx) {\n      var axisIndex = parallelAxisIndex[idx];\n      var axisModel = ecModel.getComponent('parallelAxis', axisIndex);\n\n      var axis = this._axesMap.set(dim, new ParallelAxis(dim, axisHelper.createScaleByModel(axisModel), [0, 0], axisModel.get('type'), axisIndex));\n\n      var isCategory = axis.type === 'category';\n      axis.onBand = isCategory && axisModel.get('boundaryGap');\n      axis.inverse = axisModel.get('inverse'); // Injection\n\n      axisModel.axis = axis;\n      axis.model = axisModel;\n      axis.coordinateSystem = axisModel.coordinateSystem = this;\n    }, this);\n  };\n  /**\r\n   * Update axis scale after data processed\r\n   */\n\n\n  Parallel.prototype.update = function (ecModel, api) {\n    this._updateAxesFromSeries(this._model, ecModel);\n  };\n\n  Parallel.prototype.containPoint = function (point) {\n    var layoutInfo = this._makeLayoutInfo();\n\n    var axisBase = layoutInfo.axisBase;\n    var layoutBase = layoutInfo.layoutBase;\n    var pixelDimIndex = layoutInfo.pixelDimIndex;\n    var pAxis = point[1 - pixelDimIndex];\n    var pLayout = point[pixelDimIndex];\n    return pAxis >= axisBase && pAxis <= axisBase + layoutInfo.axisLength && pLayout >= layoutBase && pLayout <= layoutBase + layoutInfo.layoutLength;\n  };\n\n  Parallel.prototype.getModel = function () {\n    return this._model;\n  };\n  /**\r\n   * Update properties from series\r\n   */\n\n\n  Parallel.prototype._updateAxesFromSeries = function (parallelModel, ecModel) {\n    ecModel.eachSeries(function (seriesModel) {\n      if (!parallelModel.contains(seriesModel, ecModel)) {\n        return;\n      }\n\n      var data = seriesModel.getData();\n      each(this.dimensions, function (dim) {\n        var axis = this._axesMap.get(dim);\n\n        axis.scale.unionExtentFromData(data, data.mapDimension(dim));\n        axisHelper.niceScaleExtent(axis.scale, axis.model);\n      }, this);\n    }, this);\n  };\n  /**\r\n   * Resize the parallel coordinate system.\r\n   */\n\n\n  Parallel.prototype.resize = function (parallelModel, api) {\n    this._rect = layoutUtil.getLayoutRect(parallelModel.getBoxLayoutParams(), {\n      width: api.getWidth(),\n      height: api.getHeight()\n    });\n\n    this._layoutAxes();\n  };\n\n  Parallel.prototype.getRect = function () {\n    return this._rect;\n  };\n\n  Parallel.prototype._makeLayoutInfo = function () {\n    var parallelModel = this._model;\n    var rect = this._rect;\n    var xy = ['x', 'y'];\n    var wh = ['width', 'height'];\n    var layout = parallelModel.get('layout');\n    var pixelDimIndex = layout === 'horizontal' ? 0 : 1;\n    var layoutLength = rect[wh[pixelDimIndex]];\n    var layoutExtent = [0, layoutLength];\n    var axisCount = this.dimensions.length;\n    var axisExpandWidth = restrict(parallelModel.get('axisExpandWidth'), layoutExtent);\n    var axisExpandCount = restrict(parallelModel.get('axisExpandCount') || 0, [0, axisCount]);\n    var axisExpandable = parallelModel.get('axisExpandable') && axisCount > 3 && axisCount > axisExpandCount && axisExpandCount > 1 && axisExpandWidth > 0 && layoutLength > 0; // `axisExpandWindow` is According to the coordinates of [0, axisExpandLength],\n    // for sake of consider the case that axisCollapseWidth is 0 (when screen is narrow),\n    // where collapsed axes should be overlapped.\n\n    var axisExpandWindow = parallelModel.get('axisExpandWindow');\n    var winSize;\n\n    if (!axisExpandWindow) {\n      winSize = restrict(axisExpandWidth * (axisExpandCount - 1), layoutExtent);\n      var axisExpandCenter = parallelModel.get('axisExpandCenter') || mathFloor(axisCount / 2);\n      axisExpandWindow = [axisExpandWidth * axisExpandCenter - winSize / 2];\n      axisExpandWindow[1] = axisExpandWindow[0] + winSize;\n    } else {\n      winSize = restrict(axisExpandWindow[1] - axisExpandWindow[0], layoutExtent);\n      axisExpandWindow[1] = axisExpandWindow[0] + winSize;\n    }\n\n    var axisCollapseWidth = (layoutLength - winSize) / (axisCount - axisExpandCount); // Avoid axisCollapseWidth is too small.\n\n    axisCollapseWidth < 3 && (axisCollapseWidth = 0); // Find the first and last indices > ewin[0] and < ewin[1].\n\n    var winInnerIndices = [mathFloor(round(axisExpandWindow[0] / axisExpandWidth, 1)) + 1, mathCeil(round(axisExpandWindow[1] / axisExpandWidth, 1)) - 1]; // Pos in ec coordinates.\n\n    var axisExpandWindow0Pos = axisCollapseWidth / axisExpandWidth * axisExpandWindow[0];\n    return {\n      layout: layout,\n      pixelDimIndex: pixelDimIndex,\n      layoutBase: rect[xy[pixelDimIndex]],\n      layoutLength: layoutLength,\n      axisBase: rect[xy[1 - pixelDimIndex]],\n      axisLength: rect[wh[1 - pixelDimIndex]],\n      axisExpandable: axisExpandable,\n      axisExpandWidth: axisExpandWidth,\n      axisCollapseWidth: axisCollapseWidth,\n      axisExpandWindow: axisExpandWindow,\n      axisCount: axisCount,\n      winInnerIndices: winInnerIndices,\n      axisExpandWindow0Pos: axisExpandWindow0Pos\n    };\n  };\n\n  Parallel.prototype._layoutAxes = function () {\n    var rect = this._rect;\n    var axes = this._axesMap;\n    var dimensions = this.dimensions;\n\n    var layoutInfo = this._makeLayoutInfo();\n\n    var layout = layoutInfo.layout;\n    axes.each(function (axis) {\n      var axisExtent = [0, layoutInfo.axisLength];\n      var idx = axis.inverse ? 1 : 0;\n      axis.setExtent(axisExtent[idx], axisExtent[1 - idx]);\n    });\n    each(dimensions, function (dim, idx) {\n      var posInfo = (layoutInfo.axisExpandable ? layoutAxisWithExpand : layoutAxisWithoutExpand)(idx, layoutInfo);\n      var positionTable = {\n        horizontal: {\n          x: posInfo.position,\n          y: layoutInfo.axisLength\n        },\n        vertical: {\n          x: 0,\n          y: posInfo.position\n        }\n      };\n      var rotationTable = {\n        horizontal: PI / 2,\n        vertical: 0\n      };\n      var position = [positionTable[layout].x + rect.x, positionTable[layout].y + rect.y];\n      var rotation = rotationTable[layout];\n      var transform = matrix.create();\n      matrix.rotate(transform, transform, rotation);\n      matrix.translate(transform, transform, position); // TODO\n      // tick layout info\n      // TODO\n      // update dimensions info based on axis order.\n\n      this._axesLayout[dim] = {\n        position: position,\n        rotation: rotation,\n        transform: transform,\n        axisNameAvailableWidth: posInfo.axisNameAvailableWidth,\n        axisLabelShow: posInfo.axisLabelShow,\n        nameTruncateMaxWidth: posInfo.nameTruncateMaxWidth,\n        tickDirection: 1,\n        labelDirection: 1\n      };\n    }, this);\n  };\n  /**\r\n   * Get axis by dim.\r\n   */\n\n\n  Parallel.prototype.getAxis = function (dim) {\n    return this._axesMap.get(dim);\n  };\n  /**\r\n   * Convert a dim value of a single item of series data to Point.\r\n   */\n\n\n  Parallel.prototype.dataToPoint = function (value, dim) {\n    return this.axisCoordToPoint(this._axesMap.get(dim).dataToCoord(value), dim);\n  };\n  /**\r\n   * Travel data for one time, get activeState of each data item.\r\n   * @param start the start dataIndex that travel from.\r\n   * @param end the next dataIndex of the last dataIndex will be travel.\r\n   */\n\n\n  Parallel.prototype.eachActiveState = function (data, callback, start, end) {\n    start == null && (start = 0);\n    end == null && (end = data.count());\n    var axesMap = this._axesMap;\n    var dimensions = this.dimensions;\n    var dataDimensions = [];\n    var axisModels = [];\n    zrUtil.each(dimensions, function (axisDim) {\n      dataDimensions.push(data.mapDimension(axisDim));\n      axisModels.push(axesMap.get(axisDim).model);\n    });\n    var hasActiveSet = this.hasAxisBrushed();\n\n    for (var dataIndex = start; dataIndex < end; dataIndex++) {\n      var activeState = void 0;\n\n      if (!hasActiveSet) {\n        activeState = 'normal';\n      } else {\n        activeState = 'active';\n        var values = data.getValues(dataDimensions, dataIndex);\n\n        for (var j = 0, lenj = dimensions.length; j < lenj; j++) {\n          var state = axisModels[j].getActiveState(values[j]);\n\n          if (state === 'inactive') {\n            activeState = 'inactive';\n            break;\n          }\n        }\n      }\n\n      callback(activeState, dataIndex);\n    }\n  };\n  /**\r\n   * Whether has any activeSet.\r\n   */\n\n\n  Parallel.prototype.hasAxisBrushed = function () {\n    var dimensions = this.dimensions;\n    var axesMap = this._axesMap;\n    var hasActiveSet = false;\n\n    for (var j = 0, lenj = dimensions.length; j < lenj; j++) {\n      if (axesMap.get(dimensions[j]).model.getActiveState() !== 'normal') {\n        hasActiveSet = true;\n      }\n    }\n\n    return hasActiveSet;\n  };\n  /**\r\n   * Convert coords of each axis to Point.\r\n   *  Return point. For example: [10, 20]\r\n   */\n\n\n  Parallel.prototype.axisCoordToPoint = function (coord, dim) {\n    var axisLayout = this._axesLayout[dim];\n    return graphic.applyTransform([coord, 0], axisLayout.transform);\n  };\n  /**\r\n   * Get axis layout.\r\n   */\n\n\n  Parallel.prototype.getAxisLayout = function (dim) {\n    return zrUtil.clone(this._axesLayout[dim]);\n  };\n  /**\r\n   * @return {Object} {axisExpandWindow, delta, behavior: 'jump' | 'slide' | 'none'}.\r\n   */\n\n\n  Parallel.prototype.getSlidedAxisExpandWindow = function (point) {\n    var layoutInfo = this._makeLayoutInfo();\n\n    var pixelDimIndex = layoutInfo.pixelDimIndex;\n    var axisExpandWindow = layoutInfo.axisExpandWindow.slice();\n    var winSize = axisExpandWindow[1] - axisExpandWindow[0];\n    var extent = [0, layoutInfo.axisExpandWidth * (layoutInfo.axisCount - 1)]; // Out of the area of coordinate system.\n\n    if (!this.containPoint(point)) {\n      return {\n        behavior: 'none',\n        axisExpandWindow: axisExpandWindow\n      };\n    } // Convert the point from global to expand coordinates.\n\n\n    var pointCoord = point[pixelDimIndex] - layoutInfo.layoutBase - layoutInfo.axisExpandWindow0Pos; // For dragging operation convenience, the window should not be\n    // slided when mouse is the center area of the window.\n\n    var delta;\n    var behavior = 'slide';\n    var axisCollapseWidth = layoutInfo.axisCollapseWidth;\n\n    var triggerArea = this._model.get('axisExpandSlideTriggerArea'); // But consider touch device, jump is necessary.\n\n\n    var useJump = triggerArea[0] != null;\n\n    if (axisCollapseWidth) {\n      if (useJump && axisCollapseWidth && pointCoord < winSize * triggerArea[0]) {\n        behavior = 'jump';\n        delta = pointCoord - winSize * triggerArea[2];\n      } else if (useJump && axisCollapseWidth && pointCoord > winSize * (1 - triggerArea[0])) {\n        behavior = 'jump';\n        delta = pointCoord - winSize * (1 - triggerArea[2]);\n      } else {\n        (delta = pointCoord - winSize * triggerArea[1]) >= 0 && (delta = pointCoord - winSize * (1 - triggerArea[1])) <= 0 && (delta = 0);\n      }\n\n      delta *= layoutInfo.axisExpandWidth / axisCollapseWidth;\n      delta ? sliderMove(delta, axisExpandWindow, extent, 'all') // Avoid nonsense triger on mousemove.\n      : behavior = 'none';\n    } // When screen is too narrow, make it visible and slidable, although it is hard to interact.\n    else {\n        var winSize2 = axisExpandWindow[1] - axisExpandWindow[0];\n        var pos = extent[1] * pointCoord / winSize2;\n        axisExpandWindow = [mathMax(0, pos - winSize2 / 2)];\n        axisExpandWindow[1] = mathMin(extent[1], axisExpandWindow[0] + winSize2);\n        axisExpandWindow[0] = axisExpandWindow[1] - winSize2;\n      }\n\n    return {\n      axisExpandWindow: axisExpandWindow,\n      behavior: behavior\n    };\n  };\n\n  return Parallel;\n}();\n\nfunction restrict(len, extent) {\n  return mathMin(mathMax(len, extent[0]), extent[1]);\n}\n\nfunction layoutAxisWithoutExpand(axisIndex, layoutInfo) {\n  var step = layoutInfo.layoutLength / (layoutInfo.axisCount - 1);\n  return {\n    position: step * axisIndex,\n    axisNameAvailableWidth: step,\n    axisLabelShow: true\n  };\n}\n\nfunction layoutAxisWithExpand(axisIndex, layoutInfo) {\n  var layoutLength = layoutInfo.layoutLength;\n  var axisExpandWidth = layoutInfo.axisExpandWidth;\n  var axisCount = layoutInfo.axisCount;\n  var axisCollapseWidth = layoutInfo.axisCollapseWidth;\n  var winInnerIndices = layoutInfo.winInnerIndices;\n  var position;\n  var axisNameAvailableWidth = axisCollapseWidth;\n  var axisLabelShow = false;\n  var nameTruncateMaxWidth;\n\n  if (axisIndex < winInnerIndices[0]) {\n    position = axisIndex * axisCollapseWidth;\n    nameTruncateMaxWidth = axisCollapseWidth;\n  } else if (axisIndex <= winInnerIndices[1]) {\n    position = layoutInfo.axisExpandWindow0Pos + axisIndex * axisExpandWidth - layoutInfo.axisExpandWindow[0];\n    axisNameAvailableWidth = axisExpandWidth;\n    axisLabelShow = true;\n  } else {\n    position = layoutLength - (axisCount - 1 - axisIndex) * axisCollapseWidth;\n    nameTruncateMaxWidth = axisCollapseWidth;\n  }\n\n  return {\n    position: position,\n    axisNameAvailableWidth: axisNameAvailableWidth,\n    axisLabelShow: axisLabelShow,\n    nameTruncateMaxWidth: nameTruncateMaxWidth\n  };\n}\n\nexport default Parallel;"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,OAAO,KAAKA,MAAM,MAAM,0BAA0B;AAClD,OAAO,KAAKC,MAAM,MAAM,4BAA4B;AACpD,OAAO,KAAKC,UAAU,MAAM,sBAAsB;AAClD,OAAO,KAAKC,UAAU,MAAM,2BAA2B;AACvD,OAAOC,YAAY,MAAM,mBAAmB;AAC5C,OAAO,KAAKC,OAAO,MAAM,uBAAuB;AAChD,OAAO,KAAKC,UAAU,MAAM,sBAAsB;AAClD,OAAOC,UAAU,MAAM,sCAAsC;AAC7D,IAAIC,IAAI,GAAGR,MAAM,CAACQ,IAAI;AACtB,IAAIC,OAAO,GAAGC,IAAI,CAACC,GAAG;AACtB,IAAIC,OAAO,GAAGF,IAAI,CAACG,GAAG;AACtB,IAAIC,SAAS,GAAGJ,IAAI,CAACK,KAAK;AAC1B,IAAIC,QAAQ,GAAGN,IAAI,CAACO,IAAI;AACxB,IAAIC,KAAK,GAAGZ,UAAU,CAACY,KAAK;AAC5B,IAAIC,EAAE,GAAGT,IAAI,CAACS,EAAE;AAEhB,IAAIC,QAAQ,GACZ;AACA,YAAY;EACV,SAASA,QAAQA,CAACC,aAAa,EAAEC,OAAO,EAAEC,GAAG,EAAE;IAC7C,IAAI,CAACC,IAAI,GAAG,UAAU;IACtB;AACJ;AACA;;IAEI,IAAI,CAACC,QAAQ,GAAGzB,MAAM,CAAC0B,aAAa,CAAC,CAAC;IACtC;AACJ;AACA;AACA;;IAEI,IAAI,CAACC,WAAW,GAAG,CAAC,CAAC;IACrB,IAAI,CAACC,UAAU,GAAGP,aAAa,CAACO,UAAU;IAC1C,IAAI,CAACC,MAAM,GAAGR,aAAa;IAE3B,IAAI,CAACS,KAAK,CAACT,aAAa,EAAEC,OAAO,EAAEC,GAAG,CAAC;EACzC;EAEAH,QAAQ,CAACW,SAAS,CAACD,KAAK,GAAG,UAAUT,aAAa,EAAEC,OAAO,EAAEC,GAAG,EAAE;IAChE,IAAIK,UAAU,GAAGP,aAAa,CAACO,UAAU;IACzC,IAAII,iBAAiB,GAAGX,aAAa,CAACW,iBAAiB;IACvDxB,IAAI,CAACoB,UAAU,EAAE,UAAUK,GAAG,EAAEC,GAAG,EAAE;MACnC,IAAIC,SAAS,GAAGH,iBAAiB,CAACE,GAAG,CAAC;MACtC,IAAIE,SAAS,GAAGd,OAAO,CAACe,YAAY,CAAC,cAAc,EAAEF,SAAS,CAAC;MAE/D,IAAIG,IAAI,GAAG,IAAI,CAACb,QAAQ,CAACc,GAAG,CAACN,GAAG,EAAE,IAAI7B,YAAY,CAAC6B,GAAG,EAAE9B,UAAU,CAACqC,kBAAkB,CAACJ,SAAS,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAEA,SAAS,CAACK,GAAG,CAAC,MAAM,CAAC,EAAEN,SAAS,CAAC,CAAC;MAE5I,IAAIO,UAAU,GAAGJ,IAAI,CAACd,IAAI,KAAK,UAAU;MACzCc,IAAI,CAACK,MAAM,GAAGD,UAAU,IAAIN,SAAS,CAACK,GAAG,CAAC,aAAa,CAAC;MACxDH,IAAI,CAACM,OAAO,GAAGR,SAAS,CAACK,GAAG,CAAC,SAAS,CAAC,CAAC,CAAC;;MAEzCL,SAAS,CAACE,IAAI,GAAGA,IAAI;MACrBA,IAAI,CAACO,KAAK,GAAGT,SAAS;MACtBE,IAAI,CAACQ,gBAAgB,GAAGV,SAAS,CAACU,gBAAgB,GAAG,IAAI;IAC3D,CAAC,EAAE,IAAI,CAAC;EACV,CAAC;EACD;AACF;AACA;;EAGE1B,QAAQ,CAACW,SAAS,CAACgB,MAAM,GAAG,UAAUzB,OAAO,EAAEC,GAAG,EAAE;IAClD,IAAI,CAACyB,qBAAqB,CAAC,IAAI,CAACnB,MAAM,EAAEP,OAAO,CAAC;EAClD,CAAC;EAEDF,QAAQ,CAACW,SAAS,CAACkB,YAAY,GAAG,UAAUC,KAAK,EAAE;IACjD,IAAIC,UAAU,GAAG,IAAI,CAACC,eAAe,CAAC,CAAC;IAEvC,IAAIC,QAAQ,GAAGF,UAAU,CAACE,QAAQ;IAClC,IAAIC,UAAU,GAAGH,UAAU,CAACG,UAAU;IACtC,IAAIC,aAAa,GAAGJ,UAAU,CAACI,aAAa;IAC5C,IAAIC,KAAK,GAAGN,KAAK,CAAC,CAAC,GAAGK,aAAa,CAAC;IACpC,IAAIE,OAAO,GAAGP,KAAK,CAACK,aAAa,CAAC;IAClC,OAAOC,KAAK,IAAIH,QAAQ,IAAIG,KAAK,IAAIH,QAAQ,GAAGF,UAAU,CAACO,UAAU,IAAID,OAAO,IAAIH,UAAU,IAAIG,OAAO,IAAIH,UAAU,GAAGH,UAAU,CAACQ,YAAY;EACnJ,CAAC;EAEDvC,QAAQ,CAACW,SAAS,CAAC6B,QAAQ,GAAG,YAAY;IACxC,OAAO,IAAI,CAAC/B,MAAM;EACpB,CAAC;EACD;AACF;AACA;;EAGET,QAAQ,CAACW,SAAS,CAACiB,qBAAqB,GAAG,UAAU3B,aAAa,EAAEC,OAAO,EAAE;IAC3EA,OAAO,CAACuC,UAAU,CAAC,UAAUC,WAAW,EAAE;MACxC,IAAI,CAACzC,aAAa,CAAC0C,QAAQ,CAACD,WAAW,EAAExC,OAAO,CAAC,EAAE;QACjD;MACF;MAEA,IAAI0C,IAAI,GAAGF,WAAW,CAACG,OAAO,CAAC,CAAC;MAChCzD,IAAI,CAAC,IAAI,CAACoB,UAAU,EAAE,UAAUK,GAAG,EAAE;QACnC,IAAIK,IAAI,GAAG,IAAI,CAACb,QAAQ,CAACgB,GAAG,CAACR,GAAG,CAAC;QAEjCK,IAAI,CAAC4B,KAAK,CAACC,mBAAmB,CAACH,IAAI,EAAEA,IAAI,CAACI,YAAY,CAACnC,GAAG,CAAC,CAAC;QAC5D9B,UAAU,CAACkE,eAAe,CAAC/B,IAAI,CAAC4B,KAAK,EAAE5B,IAAI,CAACO,KAAK,CAAC;MACpD,CAAC,EAAE,IAAI,CAAC;IACV,CAAC,EAAE,IAAI,CAAC;EACV,CAAC;EACD;AACF;AACA;;EAGEzB,QAAQ,CAACW,SAAS,CAACuC,MAAM,GAAG,UAAUjD,aAAa,EAAEE,GAAG,EAAE;IACxD,IAAI,CAACgD,KAAK,GAAGrE,UAAU,CAACsE,aAAa,CAACnD,aAAa,CAACoD,kBAAkB,CAAC,CAAC,EAAE;MACxEC,KAAK,EAAEnD,GAAG,CAACoD,QAAQ,CAAC,CAAC;MACrBC,MAAM,EAAErD,GAAG,CAACsD,SAAS,CAAC;IACxB,CAAC,CAAC;IAEF,IAAI,CAACC,WAAW,CAAC,CAAC;EACpB,CAAC;EAED1D,QAAQ,CAACW,SAAS,CAACgD,OAAO,GAAG,YAAY;IACvC,OAAO,IAAI,CAACR,KAAK;EACnB,CAAC;EAEDnD,QAAQ,CAACW,SAAS,CAACqB,eAAe,GAAG,YAAY;IAC/C,IAAI/B,aAAa,GAAG,IAAI,CAACQ,MAAM;IAC/B,IAAImD,IAAI,GAAG,IAAI,CAACT,KAAK;IACrB,IAAIU,EAAE,GAAG,CAAC,GAAG,EAAE,GAAG,CAAC;IACnB,IAAIC,EAAE,GAAG,CAAC,OAAO,EAAE,QAAQ,CAAC;IAC5B,IAAIC,MAAM,GAAG9D,aAAa,CAACoB,GAAG,CAAC,QAAQ,CAAC;IACxC,IAAIc,aAAa,GAAG4B,MAAM,KAAK,YAAY,GAAG,CAAC,GAAG,CAAC;IACnD,IAAIxB,YAAY,GAAGqB,IAAI,CAACE,EAAE,CAAC3B,aAAa,CAAC,CAAC;IAC1C,IAAI6B,YAAY,GAAG,CAAC,CAAC,EAAEzB,YAAY,CAAC;IACpC,IAAI0B,SAAS,GAAG,IAAI,CAACzD,UAAU,CAAC0D,MAAM;IACtC,IAAIC,eAAe,GAAGC,QAAQ,CAACnE,aAAa,CAACoB,GAAG,CAAC,iBAAiB,CAAC,EAAE2C,YAAY,CAAC;IAClF,IAAIK,eAAe,GAAGD,QAAQ,CAACnE,aAAa,CAACoB,GAAG,CAAC,iBAAiB,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,EAAE4C,SAAS,CAAC,CAAC;IACzF,IAAIK,cAAc,GAAGrE,aAAa,CAACoB,GAAG,CAAC,gBAAgB,CAAC,IAAI4C,SAAS,GAAG,CAAC,IAAIA,SAAS,GAAGI,eAAe,IAAIA,eAAe,GAAG,CAAC,IAAIF,eAAe,GAAG,CAAC,IAAI5B,YAAY,GAAG,CAAC,CAAC,CAAC;IAC5K;IACA;;IAEA,IAAIgC,gBAAgB,GAAGtE,aAAa,CAACoB,GAAG,CAAC,kBAAkB,CAAC;IAC5D,IAAImD,OAAO;IAEX,IAAI,CAACD,gBAAgB,EAAE;MACrBC,OAAO,GAAGJ,QAAQ,CAACD,eAAe,IAAIE,eAAe,GAAG,CAAC,CAAC,EAAEL,YAAY,CAAC;MACzE,IAAIS,gBAAgB,GAAGxE,aAAa,CAACoB,GAAG,CAAC,kBAAkB,CAAC,IAAI3B,SAAS,CAACuE,SAAS,GAAG,CAAC,CAAC;MACxFM,gBAAgB,GAAG,CAACJ,eAAe,GAAGM,gBAAgB,GAAGD,OAAO,GAAG,CAAC,CAAC;MACrED,gBAAgB,CAAC,CAAC,CAAC,GAAGA,gBAAgB,CAAC,CAAC,CAAC,GAAGC,OAAO;IACrD,CAAC,MAAM;MACLA,OAAO,GAAGJ,QAAQ,CAACG,gBAAgB,CAAC,CAAC,CAAC,GAAGA,gBAAgB,CAAC,CAAC,CAAC,EAAEP,YAAY,CAAC;MAC3EO,gBAAgB,CAAC,CAAC,CAAC,GAAGA,gBAAgB,CAAC,CAAC,CAAC,GAAGC,OAAO;IACrD;IAEA,IAAIE,iBAAiB,GAAG,CAACnC,YAAY,GAAGiC,OAAO,KAAKP,SAAS,GAAGI,eAAe,CAAC,CAAC,CAAC;;IAElFK,iBAAiB,GAAG,CAAC,KAAKA,iBAAiB,GAAG,CAAC,CAAC,CAAC,CAAC;;IAElD,IAAIC,eAAe,GAAG,CAACjF,SAAS,CAACI,KAAK,CAACyE,gBAAgB,CAAC,CAAC,CAAC,GAAGJ,eAAe,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,EAAEvE,QAAQ,CAACE,KAAK,CAACyE,gBAAgB,CAAC,CAAC,CAAC,GAAGJ,eAAe,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;;IAEvJ,IAAIS,oBAAoB,GAAGF,iBAAiB,GAAGP,eAAe,GAAGI,gBAAgB,CAAC,CAAC,CAAC;IACpF,OAAO;MACLR,MAAM,EAAEA,MAAM;MACd5B,aAAa,EAAEA,aAAa;MAC5BD,UAAU,EAAE0B,IAAI,CAACC,EAAE,CAAC1B,aAAa,CAAC,CAAC;MACnCI,YAAY,EAAEA,YAAY;MAC1BN,QAAQ,EAAE2B,IAAI,CAACC,EAAE,CAAC,CAAC,GAAG1B,aAAa,CAAC,CAAC;MACrCG,UAAU,EAAEsB,IAAI,CAACE,EAAE,CAAC,CAAC,GAAG3B,aAAa,CAAC,CAAC;MACvCmC,cAAc,EAAEA,cAAc;MAC9BH,eAAe,EAAEA,eAAe;MAChCO,iBAAiB,EAAEA,iBAAiB;MACpCH,gBAAgB,EAAEA,gBAAgB;MAClCN,SAAS,EAAEA,SAAS;MACpBU,eAAe,EAAEA,eAAe;MAChCC,oBAAoB,EAAEA;IACxB,CAAC;EACH,CAAC;EAED5E,QAAQ,CAACW,SAAS,CAAC+C,WAAW,GAAG,YAAY;IAC3C,IAAIE,IAAI,GAAG,IAAI,CAACT,KAAK;IACrB,IAAI0B,IAAI,GAAG,IAAI,CAACxE,QAAQ;IACxB,IAAIG,UAAU,GAAG,IAAI,CAACA,UAAU;IAEhC,IAAIuB,UAAU,GAAG,IAAI,CAACC,eAAe,CAAC,CAAC;IAEvC,IAAI+B,MAAM,GAAGhC,UAAU,CAACgC,MAAM;IAC9Bc,IAAI,CAACzF,IAAI,CAAC,UAAU8B,IAAI,EAAE;MACxB,IAAI4D,UAAU,GAAG,CAAC,CAAC,EAAE/C,UAAU,CAACO,UAAU,CAAC;MAC3C,IAAIxB,GAAG,GAAGI,IAAI,CAACM,OAAO,GAAG,CAAC,GAAG,CAAC;MAC9BN,IAAI,CAAC6D,SAAS,CAACD,UAAU,CAAChE,GAAG,CAAC,EAAEgE,UAAU,CAAC,CAAC,GAAGhE,GAAG,CAAC,CAAC;IACtD,CAAC,CAAC;IACF1B,IAAI,CAACoB,UAAU,EAAE,UAAUK,GAAG,EAAEC,GAAG,EAAE;MACnC,IAAIkE,OAAO,GAAG,CAACjD,UAAU,CAACuC,cAAc,GAAGW,oBAAoB,GAAGC,uBAAuB,EAAEpE,GAAG,EAAEiB,UAAU,CAAC;MAC3G,IAAIoD,aAAa,GAAG;QAClBC,UAAU,EAAE;UACVC,CAAC,EAAEL,OAAO,CAACM,QAAQ;UACnBC,CAAC,EAAExD,UAAU,CAACO;QAChB,CAAC;QACDkD,QAAQ,EAAE;UACRH,CAAC,EAAE,CAAC;UACJE,CAAC,EAAEP,OAAO,CAACM;QACb;MACF,CAAC;MACD,IAAIG,aAAa,GAAG;QAClBL,UAAU,EAAErF,EAAE,GAAG,CAAC;QAClByF,QAAQ,EAAE;MACZ,CAAC;MACD,IAAIF,QAAQ,GAAG,CAACH,aAAa,CAACpB,MAAM,CAAC,CAACsB,CAAC,GAAGzB,IAAI,CAACyB,CAAC,EAAEF,aAAa,CAACpB,MAAM,CAAC,CAACwB,CAAC,GAAG3B,IAAI,CAAC2B,CAAC,CAAC;MACnF,IAAIG,QAAQ,GAAGD,aAAa,CAAC1B,MAAM,CAAC;MACpC,IAAI4B,SAAS,GAAG9G,MAAM,CAAC+G,MAAM,CAAC,CAAC;MAC/B/G,MAAM,CAACgH,MAAM,CAACF,SAAS,EAAEA,SAAS,EAAED,QAAQ,CAAC;MAC7C7G,MAAM,CAACiH,SAAS,CAACH,SAAS,EAAEA,SAAS,EAAEL,QAAQ,CAAC,CAAC,CAAC;MAClD;MACA;MACA;;MAEA,IAAI,CAAC/E,WAAW,CAACM,GAAG,CAAC,GAAG;QACtByE,QAAQ,EAAEA,QAAQ;QAClBI,QAAQ,EAAEA,QAAQ;QAClBC,SAAS,EAAEA,SAAS;QACpBI,sBAAsB,EAAEf,OAAO,CAACe,sBAAsB;QACtDC,aAAa,EAAEhB,OAAO,CAACgB,aAAa;QACpCC,oBAAoB,EAAEjB,OAAO,CAACiB,oBAAoB;QAClDC,aAAa,EAAE,CAAC;QAChBC,cAAc,EAAE;MAClB,CAAC;IACH,CAAC,EAAE,IAAI,CAAC;EACV,CAAC;EACD;AACF;AACA;;EAGEnG,QAAQ,CAACW,SAAS,CAACyF,OAAO,GAAG,UAAUvF,GAAG,EAAE;IAC1C,OAAO,IAAI,CAACR,QAAQ,CAACgB,GAAG,CAACR,GAAG,CAAC;EAC/B,CAAC;EACD;AACF;AACA;;EAGEb,QAAQ,CAACW,SAAS,CAAC0F,WAAW,GAAG,UAAUC,KAAK,EAAEzF,GAAG,EAAE;IACrD,OAAO,IAAI,CAAC0F,gBAAgB,CAAC,IAAI,CAAClG,QAAQ,CAACgB,GAAG,CAACR,GAAG,CAAC,CAAC2F,WAAW,CAACF,KAAK,CAAC,EAAEzF,GAAG,CAAC;EAC9E,CAAC;EACD;AACF;AACA;AACA;AACA;;EAGEb,QAAQ,CAACW,SAAS,CAAC8F,eAAe,GAAG,UAAU7D,IAAI,EAAE8D,QAAQ,EAAEC,KAAK,EAAEC,GAAG,EAAE;IACzED,KAAK,IAAI,IAAI,KAAKA,KAAK,GAAG,CAAC,CAAC;IAC5BC,GAAG,IAAI,IAAI,KAAKA,GAAG,GAAGhE,IAAI,CAACiE,KAAK,CAAC,CAAC,CAAC;IACnC,IAAIC,OAAO,GAAG,IAAI,CAACzG,QAAQ;IAC3B,IAAIG,UAAU,GAAG,IAAI,CAACA,UAAU;IAChC,IAAIuG,cAAc,GAAG,EAAE;IACvB,IAAIC,UAAU,GAAG,EAAE;IACnBpI,MAAM,CAACQ,IAAI,CAACoB,UAAU,EAAE,UAAUyG,OAAO,EAAE;MACzCF,cAAc,CAACG,IAAI,CAACtE,IAAI,CAACI,YAAY,CAACiE,OAAO,CAAC,CAAC;MAC/CD,UAAU,CAACE,IAAI,CAACJ,OAAO,CAACzF,GAAG,CAAC4F,OAAO,CAAC,CAACxF,KAAK,CAAC;IAC7C,CAAC,CAAC;IACF,IAAI0F,YAAY,GAAG,IAAI,CAACC,cAAc,CAAC,CAAC;IAExC,KAAK,IAAIC,SAAS,GAAGV,KAAK,EAAEU,SAAS,GAAGT,GAAG,EAAES,SAAS,EAAE,EAAE;MACxD,IAAIC,WAAW,GAAG,KAAK,CAAC;MAExB,IAAI,CAACH,YAAY,EAAE;QACjBG,WAAW,GAAG,QAAQ;MACxB,CAAC,MAAM;QACLA,WAAW,GAAG,QAAQ;QACtB,IAAIC,MAAM,GAAG3E,IAAI,CAAC4E,SAAS,CAACT,cAAc,EAAEM,SAAS,CAAC;QAEtD,KAAK,IAAII,CAAC,GAAG,CAAC,EAAEC,IAAI,GAAGlH,UAAU,CAAC0D,MAAM,EAAEuD,CAAC,GAAGC,IAAI,EAAED,CAAC,EAAE,EAAE;UACvD,IAAIE,KAAK,GAAGX,UAAU,CAACS,CAAC,CAAC,CAACG,cAAc,CAACL,MAAM,CAACE,CAAC,CAAC,CAAC;UAEnD,IAAIE,KAAK,KAAK,UAAU,EAAE;YACxBL,WAAW,GAAG,UAAU;YACxB;UACF;QACF;MACF;MAEAZ,QAAQ,CAACY,WAAW,EAAED,SAAS,CAAC;IAClC;EACF,CAAC;EACD;AACF;AACA;;EAGErH,QAAQ,CAACW,SAAS,CAACyG,cAAc,GAAG,YAAY;IAC9C,IAAI5G,UAAU,GAAG,IAAI,CAACA,UAAU;IAChC,IAAIsG,OAAO,GAAG,IAAI,CAACzG,QAAQ;IAC3B,IAAI8G,YAAY,GAAG,KAAK;IAExB,KAAK,IAAIM,CAAC,GAAG,CAAC,EAAEC,IAAI,GAAGlH,UAAU,CAAC0D,MAAM,EAAEuD,CAAC,GAAGC,IAAI,EAAED,CAAC,EAAE,EAAE;MACvD,IAAIX,OAAO,CAACzF,GAAG,CAACb,UAAU,CAACiH,CAAC,CAAC,CAAC,CAAChG,KAAK,CAACmG,cAAc,CAAC,CAAC,KAAK,QAAQ,EAAE;QAClET,YAAY,GAAG,IAAI;MACrB;IACF;IAEA,OAAOA,YAAY;EACrB,CAAC;EACD;AACF;AACA;AACA;;EAGEnH,QAAQ,CAACW,SAAS,CAAC4F,gBAAgB,GAAG,UAAUsB,KAAK,EAAEhH,GAAG,EAAE;IAC1D,IAAIiH,UAAU,GAAG,IAAI,CAACvH,WAAW,CAACM,GAAG,CAAC;IACtC,OAAO5B,OAAO,CAAC8I,cAAc,CAAC,CAACF,KAAK,EAAE,CAAC,CAAC,EAAEC,UAAU,CAACnC,SAAS,CAAC;EACjE,CAAC;EACD;AACF;AACA;;EAGE3F,QAAQ,CAACW,SAAS,CAACqH,aAAa,GAAG,UAAUnH,GAAG,EAAE;IAChD,OAAOjC,MAAM,CAACqJ,KAAK,CAAC,IAAI,CAAC1H,WAAW,CAACM,GAAG,CAAC,CAAC;EAC5C,CAAC;EACD;AACF;AACA;;EAGEb,QAAQ,CAACW,SAAS,CAACuH,yBAAyB,GAAG,UAAUpG,KAAK,EAAE;IAC9D,IAAIC,UAAU,GAAG,IAAI,CAACC,eAAe,CAAC,CAAC;IAEvC,IAAIG,aAAa,GAAGJ,UAAU,CAACI,aAAa;IAC5C,IAAIoC,gBAAgB,GAAGxC,UAAU,CAACwC,gBAAgB,CAAC4D,KAAK,CAAC,CAAC;IAC1D,IAAI3D,OAAO,GAAGD,gBAAgB,CAAC,CAAC,CAAC,GAAGA,gBAAgB,CAAC,CAAC,CAAC;IACvD,IAAI6D,MAAM,GAAG,CAAC,CAAC,EAAErG,UAAU,CAACoC,eAAe,IAAIpC,UAAU,CAACkC,SAAS,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;;IAE3E,IAAI,CAAC,IAAI,CAACpC,YAAY,CAACC,KAAK,CAAC,EAAE;MAC7B,OAAO;QACLuG,QAAQ,EAAE,MAAM;QAChB9D,gBAAgB,EAAEA;MACpB,CAAC;IACH,CAAC,CAAC;;IAGF,IAAI+D,UAAU,GAAGxG,KAAK,CAACK,aAAa,CAAC,GAAGJ,UAAU,CAACG,UAAU,GAAGH,UAAU,CAAC6C,oBAAoB,CAAC,CAAC;IACjG;;IAEA,IAAI2D,KAAK;IACT,IAAIF,QAAQ,GAAG,OAAO;IACtB,IAAI3D,iBAAiB,GAAG3C,UAAU,CAAC2C,iBAAiB;IAEpD,IAAI8D,WAAW,GAAG,IAAI,CAAC/H,MAAM,CAACY,GAAG,CAAC,4BAA4B,CAAC,CAAC,CAAC;;IAGjE,IAAIoH,OAAO,GAAGD,WAAW,CAAC,CAAC,CAAC,IAAI,IAAI;IAEpC,IAAI9D,iBAAiB,EAAE;MACrB,IAAI+D,OAAO,IAAI/D,iBAAiB,IAAI4D,UAAU,GAAG9D,OAAO,GAAGgE,WAAW,CAAC,CAAC,CAAC,EAAE;QACzEH,QAAQ,GAAG,MAAM;QACjBE,KAAK,GAAGD,UAAU,GAAG9D,OAAO,GAAGgE,WAAW,CAAC,CAAC,CAAC;MAC/C,CAAC,MAAM,IAAIC,OAAO,IAAI/D,iBAAiB,IAAI4D,UAAU,GAAG9D,OAAO,IAAI,CAAC,GAAGgE,WAAW,CAAC,CAAC,CAAC,CAAC,EAAE;QACtFH,QAAQ,GAAG,MAAM;QACjBE,KAAK,GAAGD,UAAU,GAAG9D,OAAO,IAAI,CAAC,GAAGgE,WAAW,CAAC,CAAC,CAAC,CAAC;MACrD,CAAC,MAAM;QACL,CAACD,KAAK,GAAGD,UAAU,GAAG9D,OAAO,GAAGgE,WAAW,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,CAACD,KAAK,GAAGD,UAAU,GAAG9D,OAAO,IAAI,CAAC,GAAGgE,WAAW,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,KAAKD,KAAK,GAAG,CAAC,CAAC;MACnI;MAEAA,KAAK,IAAIxG,UAAU,CAACoC,eAAe,GAAGO,iBAAiB;MACvD6D,KAAK,GAAGpJ,UAAU,CAACoJ,KAAK,EAAEhE,gBAAgB,EAAE6D,MAAM,EAAE,KAAK,CAAC,CAAC;MAAA,EACzDC,QAAQ,GAAG,MAAM;IACrB,CAAC,CAAC;IAAA,KACG;MACD,IAAIK,QAAQ,GAAGnE,gBAAgB,CAAC,CAAC,CAAC,GAAGA,gBAAgB,CAAC,CAAC,CAAC;MACxD,IAAIoE,GAAG,GAAGP,MAAM,CAAC,CAAC,CAAC,GAAGE,UAAU,GAAGI,QAAQ;MAC3CnE,gBAAgB,GAAG,CAAC/E,OAAO,CAAC,CAAC,EAAEmJ,GAAG,GAAGD,QAAQ,GAAG,CAAC,CAAC,CAAC;MACnDnE,gBAAgB,CAAC,CAAC,CAAC,GAAGlF,OAAO,CAAC+I,MAAM,CAAC,CAAC,CAAC,EAAE7D,gBAAgB,CAAC,CAAC,CAAC,GAAGmE,QAAQ,CAAC;MACxEnE,gBAAgB,CAAC,CAAC,CAAC,GAAGA,gBAAgB,CAAC,CAAC,CAAC,GAAGmE,QAAQ;IACtD;IAEF,OAAO;MACLnE,gBAAgB,EAAEA,gBAAgB;MAClC8D,QAAQ,EAAEA;IACZ,CAAC;EACH,CAAC;EAED,OAAOrI,QAAQ;AACjB,CAAC,CAAC,CAAC;AAEH,SAASoE,QAAQA,CAACwE,GAAG,EAAER,MAAM,EAAE;EAC7B,OAAO/I,OAAO,CAACG,OAAO,CAACoJ,GAAG,EAAER,MAAM,CAAC,CAAC,CAAC,CAAC,EAAEA,MAAM,CAAC,CAAC,CAAC,CAAC;AACpD;AAEA,SAASlD,uBAAuBA,CAACnE,SAAS,EAAEgB,UAAU,EAAE;EACtD,IAAI8G,IAAI,GAAG9G,UAAU,CAACQ,YAAY,IAAIR,UAAU,CAACkC,SAAS,GAAG,CAAC,CAAC;EAC/D,OAAO;IACLqB,QAAQ,EAAEuD,IAAI,GAAG9H,SAAS;IAC1BgF,sBAAsB,EAAE8C,IAAI;IAC5B7C,aAAa,EAAE;EACjB,CAAC;AACH;AAEA,SAASf,oBAAoBA,CAAClE,SAAS,EAAEgB,UAAU,EAAE;EACnD,IAAIQ,YAAY,GAAGR,UAAU,CAACQ,YAAY;EAC1C,IAAI4B,eAAe,GAAGpC,UAAU,CAACoC,eAAe;EAChD,IAAIF,SAAS,GAAGlC,UAAU,CAACkC,SAAS;EACpC,IAAIS,iBAAiB,GAAG3C,UAAU,CAAC2C,iBAAiB;EACpD,IAAIC,eAAe,GAAG5C,UAAU,CAAC4C,eAAe;EAChD,IAAIW,QAAQ;EACZ,IAAIS,sBAAsB,GAAGrB,iBAAiB;EAC9C,IAAIsB,aAAa,GAAG,KAAK;EACzB,IAAIC,oBAAoB;EAExB,IAAIlF,SAAS,GAAG4D,eAAe,CAAC,CAAC,CAAC,EAAE;IAClCW,QAAQ,GAAGvE,SAAS,GAAG2D,iBAAiB;IACxCuB,oBAAoB,GAAGvB,iBAAiB;EAC1C,CAAC,MAAM,IAAI3D,SAAS,IAAI4D,eAAe,CAAC,CAAC,CAAC,EAAE;IAC1CW,QAAQ,GAAGvD,UAAU,CAAC6C,oBAAoB,GAAG7D,SAAS,GAAGoD,eAAe,GAAGpC,UAAU,CAACwC,gBAAgB,CAAC,CAAC,CAAC;IACzGwB,sBAAsB,GAAG5B,eAAe;IACxC6B,aAAa,GAAG,IAAI;EACtB,CAAC,MAAM;IACLV,QAAQ,GAAG/C,YAAY,GAAG,CAAC0B,SAAS,GAAG,CAAC,GAAGlD,SAAS,IAAI2D,iBAAiB;IACzEuB,oBAAoB,GAAGvB,iBAAiB;EAC1C;EAEA,OAAO;IACLY,QAAQ,EAAEA,QAAQ;IAClBS,sBAAsB,EAAEA,sBAAsB;IAC9CC,aAAa,EAAEA,aAAa;IAC5BC,oBAAoB,EAAEA;EACxB,CAAC;AACH;AAEA,eAAejG,QAAQ"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}