{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { setAsPrimitive, map, isTypedArray, assert, each, retrieve2 } from 'zrender/lib/core/util.js';\nimport { createSource, cloneSourceShallow } from '../Source.js';\nimport { SOURCE_FORMAT_TYPED_ARRAY, SOURCE_FORMAT_ORIGINAL } from '../../util/types.js';\nimport { querySeriesUpstreamDatasetModel, queryDatasetUpstreamDatasetModels } from './sourceHelper.js';\nimport { applyDataTransform } from './transform.js';\nimport DataStore from '../DataStore.js';\nimport { DefaultDataProvider } from './dataProvider.js';\n/**\r\n * [REQUIREMENT_MEMO]:\r\n * (0) `metaRawOption` means `dimensions`/`sourceHeader`/`seriesLayoutBy` in raw option.\r\n * (1) Keep support the feature: `metaRawOption` can be specified both on `series` and\r\n * `root-dataset`. Them on `series` has higher priority.\r\n * (2) Do not support to set `metaRawOption` on a `non-root-dataset`, because it might\r\n * confuse users: whether those props indicate how to visit the upstream source or visit\r\n * the transform result source, and some transforms has nothing to do with these props,\r\n * and some transforms might have multiple upstream.\r\n * (3) Transforms should specify `metaRawOption` in each output, just like they can be\r\n * declared in `root-dataset`.\r\n * (4) At present only support visit source in `SERIES_LAYOUT_BY_COLUMN` in transforms.\r\n * That is for reducing complexity in transforms.\r\n * PENDING: Whether to provide transposition transform?\r\n *\r\n * [IMPLEMENTAION_MEMO]:\r\n * \"sourceVisitConfig\" are calculated from `metaRawOption` and `data`.\r\n * They will not be calculated until `source` is about to be visited (to prevent from\r\n * duplicate calcuation). `source` is visited only in series and input to transforms.\r\n *\r\n * [DIMENSION_INHERIT_RULE]:\r\n * By default the dimensions are inherited from ancestors, unless a transform return\r\n * a new dimensions definition.\r\n * Consider the case:\r\n * ```js\r\n * dataset: [{\r\n *     source: [ ['Product', 'Sales', 'Prise'], ['Cookies', 321, 44.21], ...]\r\n * }, {\r\n *     transform: { type: 'filter', ... }\r\n * }]\r\n * dataset: [{\r\n *     dimension: ['Product', 'Sales', 'Prise'],\r\n *     source: [ ['Cookies', 321, 44.21], ...]\r\n * }, {\r\n *     transform: { type: 'filter', ... }\r\n * }]\r\n * ```\r\n * The two types of option should have the same behavior after transform.\r\n *\r\n *\r\n * [SCENARIO]:\r\n * (1) Provide source data directly:\r\n * ```js\r\n * series: {\r\n *     encode: {...},\r\n *     dimensions: [...]\r\n *     seriesLayoutBy: 'row',\r\n *     data: [[...]]\r\n * }\r\n * ```\r\n * (2) Series refer to dataset.\r\n * ```js\r\n * series: [{\r\n *     encode: {...}\r\n *     // Ignore datasetIndex means `datasetIndex: 0`\r\n *     // and the dimensions defination in dataset is used\r\n * }, {\r\n *     encode: {...},\r\n *     seriesLayoutBy: 'column',\r\n *     datasetIndex: 1\r\n * }]\r\n * ```\r\n * (3) dataset transform\r\n * ```js\r\n * dataset: [{\r\n *     source: [...]\r\n * }, {\r\n *     source: [...]\r\n * }, {\r\n *     // By default from 0.\r\n *     transform: { type: 'filter', config: {...} }\r\n * }, {\r\n *     // Piped.\r\n *     transform: [\r\n *         { type: 'filter', config: {...} },\r\n *         { type: 'sort', config: {...} }\r\n *     ]\r\n * }, {\r\n *     id: 'regressionData',\r\n *     fromDatasetIndex: 1,\r\n *     // Third-party transform\r\n *     transform: { type: 'ecStat:regression', config: {...} }\r\n * }, {\r\n *     // retrieve the extra result.\r\n *     id: 'regressionFormula',\r\n *     fromDatasetId: 'regressionData',\r\n *     fromTransformResult: 1\r\n * }]\r\n * ```\r\n */\n\nvar SourceManager = /** @class */\nfunction () {\n  function SourceManager(sourceHost) {\n    // Cached source. Do not repeat calculating if not dirty.\n    this._sourceList = [];\n    this._storeList = []; // version sign of each upstream source manager.\n\n    this._upstreamSignList = [];\n    this._versionSignBase = 0;\n    this._dirty = true;\n    this._sourceHost = sourceHost;\n  }\n  /**\r\n   * Mark dirty.\r\n   */\n\n  SourceManager.prototype.dirty = function () {\n    this._setLocalSource([], []);\n    this._storeList = [];\n    this._dirty = true;\n  };\n  SourceManager.prototype._setLocalSource = function (sourceList, upstreamSignList) {\n    this._sourceList = sourceList;\n    this._upstreamSignList = upstreamSignList;\n    this._versionSignBase++;\n    if (this._versionSignBase > 9e10) {\n      this._versionSignBase = 0;\n    }\n  };\n  /**\r\n   * For detecting whether the upstream source is dirty, so that\r\n   * the local cached source (in `_sourceList`) should be discarded.\r\n   */\n\n  SourceManager.prototype._getVersionSign = function () {\n    return this._sourceHost.uid + '_' + this._versionSignBase;\n  };\n  /**\r\n   * Always return a source instance. Otherwise throw error.\r\n   */\n\n  SourceManager.prototype.prepareSource = function () {\n    // For the case that call `setOption` multiple time but no data changed,\n    // cache the result source to prevent from repeating transform.\n    if (this._isDirty()) {\n      this._createSource();\n      this._dirty = false;\n    }\n  };\n  SourceManager.prototype._createSource = function () {\n    this._setLocalSource([], []);\n    var sourceHost = this._sourceHost;\n    var upSourceMgrList = this._getUpstreamSourceManagers();\n    var hasUpstream = !!upSourceMgrList.length;\n    var resultSourceList;\n    var upstreamSignList;\n    if (isSeries(sourceHost)) {\n      var seriesModel = sourceHost;\n      var data = void 0;\n      var sourceFormat = void 0;\n      var upSource = void 0; // Has upstream dataset\n\n      if (hasUpstream) {\n        var upSourceMgr = upSourceMgrList[0];\n        upSourceMgr.prepareSource();\n        upSource = upSourceMgr.getSource();\n        data = upSource.data;\n        sourceFormat = upSource.sourceFormat;\n        upstreamSignList = [upSourceMgr._getVersionSign()];\n      } // Series data is from own.\n      else {\n        data = seriesModel.get('data', true);\n        sourceFormat = isTypedArray(data) ? SOURCE_FORMAT_TYPED_ARRAY : SOURCE_FORMAT_ORIGINAL;\n        upstreamSignList = [];\n      } // See [REQUIREMENT_MEMO], merge settings on series and parent dataset if it is root.\n\n      var newMetaRawOption = this._getSourceMetaRawOption() || {};\n      var upMetaRawOption = upSource && upSource.metaRawOption || {};\n      var seriesLayoutBy = retrieve2(newMetaRawOption.seriesLayoutBy, upMetaRawOption.seriesLayoutBy) || null;\n      var sourceHeader = retrieve2(newMetaRawOption.sourceHeader, upMetaRawOption.sourceHeader); // Note here we should not use `upSource.dimensionsDefine`. Consider the case:\n      // `upSource.dimensionsDefine` is detected by `seriesLayoutBy: 'column'`,\n      // but series need `seriesLayoutBy: 'row'`.\n\n      var dimensions = retrieve2(newMetaRawOption.dimensions, upMetaRawOption.dimensions); // We share source with dataset as much as possible\n      // to avoid extra memory cost of high dimensional data.\n\n      var needsCreateSource = seriesLayoutBy !== upMetaRawOption.seriesLayoutBy || !!sourceHeader !== !!upMetaRawOption.sourceHeader || dimensions;\n      resultSourceList = needsCreateSource ? [createSource(data, {\n        seriesLayoutBy: seriesLayoutBy,\n        sourceHeader: sourceHeader,\n        dimensions: dimensions\n      }, sourceFormat)] : [];\n    } else {\n      var datasetModel = sourceHost; // Has upstream dataset.\n\n      if (hasUpstream) {\n        var result = this._applyTransform(upSourceMgrList);\n        resultSourceList = result.sourceList;\n        upstreamSignList = result.upstreamSignList;\n      } // Is root dataset.\n      else {\n        var sourceData = datasetModel.get('source', true);\n        resultSourceList = [createSource(sourceData, this._getSourceMetaRawOption(), null)];\n        upstreamSignList = [];\n      }\n    }\n    if (process.env.NODE_ENV !== 'production') {\n      assert(resultSourceList && upstreamSignList);\n    }\n    this._setLocalSource(resultSourceList, upstreamSignList);\n  };\n  SourceManager.prototype._applyTransform = function (upMgrList) {\n    var datasetModel = this._sourceHost;\n    var transformOption = datasetModel.get('transform', true);\n    var fromTransformResult = datasetModel.get('fromTransformResult', true);\n    if (process.env.NODE_ENV !== 'production') {\n      assert(fromTransformResult != null || transformOption != null);\n    }\n    if (fromTransformResult != null) {\n      var errMsg = '';\n      if (upMgrList.length !== 1) {\n        if (process.env.NODE_ENV !== 'production') {\n          errMsg = 'When using `fromTransformResult`, there should be only one upstream dataset';\n        }\n        doThrow(errMsg);\n      }\n    }\n    var sourceList;\n    var upSourceList = [];\n    var upstreamSignList = [];\n    each(upMgrList, function (upMgr) {\n      upMgr.prepareSource();\n      var upSource = upMgr.getSource(fromTransformResult || 0);\n      var errMsg = '';\n      if (fromTransformResult != null && !upSource) {\n        if (process.env.NODE_ENV !== 'production') {\n          errMsg = 'Can not retrieve result by `fromTransformResult`: ' + fromTransformResult;\n        }\n        doThrow(errMsg);\n      }\n      upSourceList.push(upSource);\n      upstreamSignList.push(upMgr._getVersionSign());\n    });\n    if (transformOption) {\n      sourceList = applyDataTransform(transformOption, upSourceList, {\n        datasetIndex: datasetModel.componentIndex\n      });\n    } else if (fromTransformResult != null) {\n      sourceList = [cloneSourceShallow(upSourceList[0])];\n    }\n    return {\n      sourceList: sourceList,\n      upstreamSignList: upstreamSignList\n    };\n  };\n  SourceManager.prototype._isDirty = function () {\n    if (this._dirty) {\n      return true;\n    } // All sourceList is from the some upstream.\n\n    var upSourceMgrList = this._getUpstreamSourceManagers();\n    for (var i = 0; i < upSourceMgrList.length; i++) {\n      var upSrcMgr = upSourceMgrList[i];\n      if (\n      // Consider the case that there is ancestor diry, call it recursively.\n      // The performance is probably not an issue because usually the chain is not long.\n      upSrcMgr._isDirty() || this._upstreamSignList[i] !== upSrcMgr._getVersionSign()) {\n        return true;\n      }\n    }\n  };\n  /**\r\n   * @param sourceIndex By default 0, means \"main source\".\r\n   *                    In most cases there is only one source.\r\n   */\n\n  SourceManager.prototype.getSource = function (sourceIndex) {\n    sourceIndex = sourceIndex || 0;\n    var source = this._sourceList[sourceIndex];\n    if (!source) {\n      // Series may share source instance with dataset.\n      var upSourceMgrList = this._getUpstreamSourceManagers();\n      return upSourceMgrList[0] && upSourceMgrList[0].getSource(sourceIndex);\n    }\n    return source;\n  };\n  /**\r\n   *\r\n   * Get a data store which can be shared across series.\r\n   * Only available for series.\r\n   *\r\n   * @param seriesDimRequest Dimensions that are generated in series.\r\n   *        Should have been sorted by `storeDimIndex` asc.\r\n   */\n\n  SourceManager.prototype.getSharedDataStore = function (seriesDimRequest) {\n    if (process.env.NODE_ENV !== 'production') {\n      assert(isSeries(this._sourceHost), 'Can only call getDataStore on series source manager.');\n    }\n    var schema = seriesDimRequest.makeStoreSchema();\n    return this._innerGetDataStore(schema.dimensions, seriesDimRequest.source, schema.hash);\n  };\n  SourceManager.prototype._innerGetDataStore = function (storeDims, seriesSource, sourceReadKey) {\n    // TODO Can use other sourceIndex?\n    var sourceIndex = 0;\n    var storeList = this._storeList;\n    var cachedStoreMap = storeList[sourceIndex];\n    if (!cachedStoreMap) {\n      cachedStoreMap = storeList[sourceIndex] = {};\n    }\n    var cachedStore = cachedStoreMap[sourceReadKey];\n    if (!cachedStore) {\n      var upSourceMgr = this._getUpstreamSourceManagers()[0];\n      if (isSeries(this._sourceHost) && upSourceMgr) {\n        cachedStore = upSourceMgr._innerGetDataStore(storeDims, seriesSource, sourceReadKey);\n      } else {\n        cachedStore = new DataStore(); // Always create store from source of series.\n\n        cachedStore.initData(new DefaultDataProvider(seriesSource, storeDims.length), storeDims);\n      }\n      cachedStoreMap[sourceReadKey] = cachedStore;\n    }\n    return cachedStore;\n  };\n  /**\r\n   * PENDING: Is it fast enough?\r\n   * If no upstream, return empty array.\r\n   */\n\n  SourceManager.prototype._getUpstreamSourceManagers = function () {\n    // Always get the relationship from the raw option.\n    // Do not cache the link of the dependency graph, so that\n    // there is no need to update them when change happens.\n    var sourceHost = this._sourceHost;\n    if (isSeries(sourceHost)) {\n      var datasetModel = querySeriesUpstreamDatasetModel(sourceHost);\n      return !datasetModel ? [] : [datasetModel.getSourceManager()];\n    } else {\n      return map(queryDatasetUpstreamDatasetModels(sourceHost), function (datasetModel) {\n        return datasetModel.getSourceManager();\n      });\n    }\n  };\n  SourceManager.prototype._getSourceMetaRawOption = function () {\n    var sourceHost = this._sourceHost;\n    var seriesLayoutBy;\n    var sourceHeader;\n    var dimensions;\n    if (isSeries(sourceHost)) {\n      seriesLayoutBy = sourceHost.get('seriesLayoutBy', true);\n      sourceHeader = sourceHost.get('sourceHeader', true);\n      dimensions = sourceHost.get('dimensions', true);\n    } // See [REQUIREMENT_MEMO], `non-root-dataset` do not support them.\n    else if (!this._getUpstreamSourceManagers().length) {\n      var model = sourceHost;\n      seriesLayoutBy = model.get('seriesLayoutBy', true);\n      sourceHeader = model.get('sourceHeader', true);\n      dimensions = model.get('dimensions', true);\n    }\n    return {\n      seriesLayoutBy: seriesLayoutBy,\n      sourceHeader: sourceHeader,\n      dimensions: dimensions\n    };\n  };\n  return SourceManager;\n}();\nexport { SourceManager }; // Call this method after `super.init` and `super.mergeOption` to\n// disable the transform merge, but do not disable transform clone from rawOption.\n\nexport function disableTransformOptionMerge(datasetModel) {\n  var transformOption = datasetModel.option.transform;\n  transformOption && setAsPrimitive(datasetModel.option.transform);\n}\nfunction isSeries(sourceHost) {\n  // Avoid circular dependency with Series.ts\n  return sourceHost.mainType === 'series';\n}\nfunction doThrow(errMsg) {\n  throw new Error(errMsg);\n}", "map": {"version": 3, "names": ["setAsPrimitive", "map", "isTypedArray", "assert", "each", "retrieve2", "createSource", "cloneSourceShallow", "SOURCE_FORMAT_TYPED_ARRAY", "SOURCE_FORMAT_ORIGINAL", "querySeriesUpstreamDatasetModel", "queryDatasetUpstreamDatasetModels", "applyDataTransform", "DataStore", "DefaultDataProvider", "SourceManager", "sourceHost", "_sourceList", "_storeList", "_upstreamSignList", "_versionSignBase", "_dirty", "_sourceHost", "prototype", "dirty", "_setLocalSource", "sourceList", "upstreamSignList", "_getVersionSign", "uid", "prepareSource", "_isDirty", "_createSource", "upSourceMgrList", "_getUpstreamSourceManagers", "hasUpstream", "length", "resultSourceList", "isSeries", "seriesModel", "data", "sourceFormat", "upSource", "upSourceMgr", "getSource", "get", "newMetaRawOption", "_getSourceMetaRawOption", "upMetaRawOption", "metaRawOption", "seriesLayoutBy", "sourceHeader", "dimensions", "needsCreateSource", "datasetModel", "result", "_applyTransform", "sourceData", "process", "env", "NODE_ENV", "upMgrList", "transformOption", "fromTransformResult", "errMsg", "doThrow", "upSourceList", "upMgr", "push", "datasetIndex", "componentIndex", "i", "upSrcMgr", "sourceIndex", "source", "getSharedDataStore", "seriesDimRequest", "schema", "makeStoreSchema", "_innerGetDataStore", "hash", "storeDims", "seriesSource", "sourceReadKey", "storeList", "cachedStoreMap", "cachedStore", "initData", "getSourceManager", "model", "disableTransformOptionMerge", "option", "transform", "mainType", "Error"], "sources": ["C:/WolfSystemV2/WolfOldFrontEnd/node_modules/echarts/lib/data/helper/sourceManager.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { setAsPrimitive, map, isTypedArray, assert, each, retrieve2 } from 'zrender/lib/core/util.js';\nimport { createSource, cloneSourceShallow } from '../Source.js';\nimport { SOURCE_FORMAT_TYPED_ARRAY, SOURCE_FORMAT_ORIGINAL } from '../../util/types.js';\nimport { querySeriesUpstreamDatasetModel, queryDatasetUpstreamDatasetModels } from './sourceHelper.js';\nimport { applyDataTransform } from './transform.js';\nimport DataStore from '../DataStore.js';\nimport { DefaultDataProvider } from './dataProvider.js';\n/**\r\n * [REQUIREMENT_MEMO]:\r\n * (0) `metaRawOption` means `dimensions`/`sourceHeader`/`seriesLayoutBy` in raw option.\r\n * (1) Keep support the feature: `metaRawOption` can be specified both on `series` and\r\n * `root-dataset`. Them on `series` has higher priority.\r\n * (2) Do not support to set `metaRawOption` on a `non-root-dataset`, because it might\r\n * confuse users: whether those props indicate how to visit the upstream source or visit\r\n * the transform result source, and some transforms has nothing to do with these props,\r\n * and some transforms might have multiple upstream.\r\n * (3) Transforms should specify `metaRawOption` in each output, just like they can be\r\n * declared in `root-dataset`.\r\n * (4) At present only support visit source in `SERIES_LAYOUT_BY_COLUMN` in transforms.\r\n * That is for reducing complexity in transforms.\r\n * PENDING: Whether to provide transposition transform?\r\n *\r\n * [IMPLEMENTAION_MEMO]:\r\n * \"sourceVisitConfig\" are calculated from `metaRawOption` and `data`.\r\n * They will not be calculated until `source` is about to be visited (to prevent from\r\n * duplicate calcuation). `source` is visited only in series and input to transforms.\r\n *\r\n * [DIMENSION_INHERIT_RULE]:\r\n * By default the dimensions are inherited from ancestors, unless a transform return\r\n * a new dimensions definition.\r\n * Consider the case:\r\n * ```js\r\n * dataset: [{\r\n *     source: [ ['Product', 'Sales', 'Prise'], ['Cookies', 321, 44.21], ...]\r\n * }, {\r\n *     transform: { type: 'filter', ... }\r\n * }]\r\n * dataset: [{\r\n *     dimension: ['Product', 'Sales', 'Prise'],\r\n *     source: [ ['Cookies', 321, 44.21], ...]\r\n * }, {\r\n *     transform: { type: 'filter', ... }\r\n * }]\r\n * ```\r\n * The two types of option should have the same behavior after transform.\r\n *\r\n *\r\n * [SCENARIO]:\r\n * (1) Provide source data directly:\r\n * ```js\r\n * series: {\r\n *     encode: {...},\r\n *     dimensions: [...]\r\n *     seriesLayoutBy: 'row',\r\n *     data: [[...]]\r\n * }\r\n * ```\r\n * (2) Series refer to dataset.\r\n * ```js\r\n * series: [{\r\n *     encode: {...}\r\n *     // Ignore datasetIndex means `datasetIndex: 0`\r\n *     // and the dimensions defination in dataset is used\r\n * }, {\r\n *     encode: {...},\r\n *     seriesLayoutBy: 'column',\r\n *     datasetIndex: 1\r\n * }]\r\n * ```\r\n * (3) dataset transform\r\n * ```js\r\n * dataset: [{\r\n *     source: [...]\r\n * }, {\r\n *     source: [...]\r\n * }, {\r\n *     // By default from 0.\r\n *     transform: { type: 'filter', config: {...} }\r\n * }, {\r\n *     // Piped.\r\n *     transform: [\r\n *         { type: 'filter', config: {...} },\r\n *         { type: 'sort', config: {...} }\r\n *     ]\r\n * }, {\r\n *     id: 'regressionData',\r\n *     fromDatasetIndex: 1,\r\n *     // Third-party transform\r\n *     transform: { type: 'ecStat:regression', config: {...} }\r\n * }, {\r\n *     // retrieve the extra result.\r\n *     id: 'regressionFormula',\r\n *     fromDatasetId: 'regressionData',\r\n *     fromTransformResult: 1\r\n * }]\r\n * ```\r\n */\n\nvar SourceManager =\n/** @class */\nfunction () {\n  function SourceManager(sourceHost) {\n    // Cached source. Do not repeat calculating if not dirty.\n    this._sourceList = [];\n    this._storeList = []; // version sign of each upstream source manager.\n\n    this._upstreamSignList = [];\n    this._versionSignBase = 0;\n    this._dirty = true;\n    this._sourceHost = sourceHost;\n  }\n  /**\r\n   * Mark dirty.\r\n   */\n\n\n  SourceManager.prototype.dirty = function () {\n    this._setLocalSource([], []);\n\n    this._storeList = [];\n    this._dirty = true;\n  };\n\n  SourceManager.prototype._setLocalSource = function (sourceList, upstreamSignList) {\n    this._sourceList = sourceList;\n    this._upstreamSignList = upstreamSignList;\n    this._versionSignBase++;\n\n    if (this._versionSignBase > 9e10) {\n      this._versionSignBase = 0;\n    }\n  };\n  /**\r\n   * For detecting whether the upstream source is dirty, so that\r\n   * the local cached source (in `_sourceList`) should be discarded.\r\n   */\n\n\n  SourceManager.prototype._getVersionSign = function () {\n    return this._sourceHost.uid + '_' + this._versionSignBase;\n  };\n  /**\r\n   * Always return a source instance. Otherwise throw error.\r\n   */\n\n\n  SourceManager.prototype.prepareSource = function () {\n    // For the case that call `setOption` multiple time but no data changed,\n    // cache the result source to prevent from repeating transform.\n    if (this._isDirty()) {\n      this._createSource();\n\n      this._dirty = false;\n    }\n  };\n\n  SourceManager.prototype._createSource = function () {\n    this._setLocalSource([], []);\n\n    var sourceHost = this._sourceHost;\n\n    var upSourceMgrList = this._getUpstreamSourceManagers();\n\n    var hasUpstream = !!upSourceMgrList.length;\n    var resultSourceList;\n    var upstreamSignList;\n\n    if (isSeries(sourceHost)) {\n      var seriesModel = sourceHost;\n      var data = void 0;\n      var sourceFormat = void 0;\n      var upSource = void 0; // Has upstream dataset\n\n      if (hasUpstream) {\n        var upSourceMgr = upSourceMgrList[0];\n        upSourceMgr.prepareSource();\n        upSource = upSourceMgr.getSource();\n        data = upSource.data;\n        sourceFormat = upSource.sourceFormat;\n        upstreamSignList = [upSourceMgr._getVersionSign()];\n      } // Series data is from own.\n      else {\n          data = seriesModel.get('data', true);\n          sourceFormat = isTypedArray(data) ? SOURCE_FORMAT_TYPED_ARRAY : SOURCE_FORMAT_ORIGINAL;\n          upstreamSignList = [];\n        } // See [REQUIREMENT_MEMO], merge settings on series and parent dataset if it is root.\n\n\n      var newMetaRawOption = this._getSourceMetaRawOption() || {};\n      var upMetaRawOption = upSource && upSource.metaRawOption || {};\n      var seriesLayoutBy = retrieve2(newMetaRawOption.seriesLayoutBy, upMetaRawOption.seriesLayoutBy) || null;\n      var sourceHeader = retrieve2(newMetaRawOption.sourceHeader, upMetaRawOption.sourceHeader); // Note here we should not use `upSource.dimensionsDefine`. Consider the case:\n      // `upSource.dimensionsDefine` is detected by `seriesLayoutBy: 'column'`,\n      // but series need `seriesLayoutBy: 'row'`.\n\n      var dimensions = retrieve2(newMetaRawOption.dimensions, upMetaRawOption.dimensions); // We share source with dataset as much as possible\n      // to avoid extra memory cost of high dimensional data.\n\n      var needsCreateSource = seriesLayoutBy !== upMetaRawOption.seriesLayoutBy || !!sourceHeader !== !!upMetaRawOption.sourceHeader || dimensions;\n      resultSourceList = needsCreateSource ? [createSource(data, {\n        seriesLayoutBy: seriesLayoutBy,\n        sourceHeader: sourceHeader,\n        dimensions: dimensions\n      }, sourceFormat)] : [];\n    } else {\n      var datasetModel = sourceHost; // Has upstream dataset.\n\n      if (hasUpstream) {\n        var result = this._applyTransform(upSourceMgrList);\n\n        resultSourceList = result.sourceList;\n        upstreamSignList = result.upstreamSignList;\n      } // Is root dataset.\n      else {\n          var sourceData = datasetModel.get('source', true);\n          resultSourceList = [createSource(sourceData, this._getSourceMetaRawOption(), null)];\n          upstreamSignList = [];\n        }\n    }\n\n    if (process.env.NODE_ENV !== 'production') {\n      assert(resultSourceList && upstreamSignList);\n    }\n\n    this._setLocalSource(resultSourceList, upstreamSignList);\n  };\n\n  SourceManager.prototype._applyTransform = function (upMgrList) {\n    var datasetModel = this._sourceHost;\n    var transformOption = datasetModel.get('transform', true);\n    var fromTransformResult = datasetModel.get('fromTransformResult', true);\n\n    if (process.env.NODE_ENV !== 'production') {\n      assert(fromTransformResult != null || transformOption != null);\n    }\n\n    if (fromTransformResult != null) {\n      var errMsg = '';\n\n      if (upMgrList.length !== 1) {\n        if (process.env.NODE_ENV !== 'production') {\n          errMsg = 'When using `fromTransformResult`, there should be only one upstream dataset';\n        }\n\n        doThrow(errMsg);\n      }\n    }\n\n    var sourceList;\n    var upSourceList = [];\n    var upstreamSignList = [];\n    each(upMgrList, function (upMgr) {\n      upMgr.prepareSource();\n      var upSource = upMgr.getSource(fromTransformResult || 0);\n      var errMsg = '';\n\n      if (fromTransformResult != null && !upSource) {\n        if (process.env.NODE_ENV !== 'production') {\n          errMsg = 'Can not retrieve result by `fromTransformResult`: ' + fromTransformResult;\n        }\n\n        doThrow(errMsg);\n      }\n\n      upSourceList.push(upSource);\n      upstreamSignList.push(upMgr._getVersionSign());\n    });\n\n    if (transformOption) {\n      sourceList = applyDataTransform(transformOption, upSourceList, {\n        datasetIndex: datasetModel.componentIndex\n      });\n    } else if (fromTransformResult != null) {\n      sourceList = [cloneSourceShallow(upSourceList[0])];\n    }\n\n    return {\n      sourceList: sourceList,\n      upstreamSignList: upstreamSignList\n    };\n  };\n\n  SourceManager.prototype._isDirty = function () {\n    if (this._dirty) {\n      return true;\n    } // All sourceList is from the some upstream.\n\n\n    var upSourceMgrList = this._getUpstreamSourceManagers();\n\n    for (var i = 0; i < upSourceMgrList.length; i++) {\n      var upSrcMgr = upSourceMgrList[i];\n\n      if ( // Consider the case that there is ancestor diry, call it recursively.\n      // The performance is probably not an issue because usually the chain is not long.\n      upSrcMgr._isDirty() || this._upstreamSignList[i] !== upSrcMgr._getVersionSign()) {\n        return true;\n      }\n    }\n  };\n  /**\r\n   * @param sourceIndex By default 0, means \"main source\".\r\n   *                    In most cases there is only one source.\r\n   */\n\n\n  SourceManager.prototype.getSource = function (sourceIndex) {\n    sourceIndex = sourceIndex || 0;\n    var source = this._sourceList[sourceIndex];\n\n    if (!source) {\n      // Series may share source instance with dataset.\n      var upSourceMgrList = this._getUpstreamSourceManagers();\n\n      return upSourceMgrList[0] && upSourceMgrList[0].getSource(sourceIndex);\n    }\n\n    return source;\n  };\n  /**\r\n   *\r\n   * Get a data store which can be shared across series.\r\n   * Only available for series.\r\n   *\r\n   * @param seriesDimRequest Dimensions that are generated in series.\r\n   *        Should have been sorted by `storeDimIndex` asc.\r\n   */\n\n\n  SourceManager.prototype.getSharedDataStore = function (seriesDimRequest) {\n    if (process.env.NODE_ENV !== 'production') {\n      assert(isSeries(this._sourceHost), 'Can only call getDataStore on series source manager.');\n    }\n\n    var schema = seriesDimRequest.makeStoreSchema();\n    return this._innerGetDataStore(schema.dimensions, seriesDimRequest.source, schema.hash);\n  };\n\n  SourceManager.prototype._innerGetDataStore = function (storeDims, seriesSource, sourceReadKey) {\n    // TODO Can use other sourceIndex?\n    var sourceIndex = 0;\n    var storeList = this._storeList;\n    var cachedStoreMap = storeList[sourceIndex];\n\n    if (!cachedStoreMap) {\n      cachedStoreMap = storeList[sourceIndex] = {};\n    }\n\n    var cachedStore = cachedStoreMap[sourceReadKey];\n\n    if (!cachedStore) {\n      var upSourceMgr = this._getUpstreamSourceManagers()[0];\n\n      if (isSeries(this._sourceHost) && upSourceMgr) {\n        cachedStore = upSourceMgr._innerGetDataStore(storeDims, seriesSource, sourceReadKey);\n      } else {\n        cachedStore = new DataStore(); // Always create store from source of series.\n\n        cachedStore.initData(new DefaultDataProvider(seriesSource, storeDims.length), storeDims);\n      }\n\n      cachedStoreMap[sourceReadKey] = cachedStore;\n    }\n\n    return cachedStore;\n  };\n  /**\r\n   * PENDING: Is it fast enough?\r\n   * If no upstream, return empty array.\r\n   */\n\n\n  SourceManager.prototype._getUpstreamSourceManagers = function () {\n    // Always get the relationship from the raw option.\n    // Do not cache the link of the dependency graph, so that\n    // there is no need to update them when change happens.\n    var sourceHost = this._sourceHost;\n\n    if (isSeries(sourceHost)) {\n      var datasetModel = querySeriesUpstreamDatasetModel(sourceHost);\n      return !datasetModel ? [] : [datasetModel.getSourceManager()];\n    } else {\n      return map(queryDatasetUpstreamDatasetModels(sourceHost), function (datasetModel) {\n        return datasetModel.getSourceManager();\n      });\n    }\n  };\n\n  SourceManager.prototype._getSourceMetaRawOption = function () {\n    var sourceHost = this._sourceHost;\n    var seriesLayoutBy;\n    var sourceHeader;\n    var dimensions;\n\n    if (isSeries(sourceHost)) {\n      seriesLayoutBy = sourceHost.get('seriesLayoutBy', true);\n      sourceHeader = sourceHost.get('sourceHeader', true);\n      dimensions = sourceHost.get('dimensions', true);\n    } // See [REQUIREMENT_MEMO], `non-root-dataset` do not support them.\n    else if (!this._getUpstreamSourceManagers().length) {\n        var model = sourceHost;\n        seriesLayoutBy = model.get('seriesLayoutBy', true);\n        sourceHeader = model.get('sourceHeader', true);\n        dimensions = model.get('dimensions', true);\n      }\n\n    return {\n      seriesLayoutBy: seriesLayoutBy,\n      sourceHeader: sourceHeader,\n      dimensions: dimensions\n    };\n  };\n\n  return SourceManager;\n}();\n\nexport { SourceManager }; // Call this method after `super.init` and `super.mergeOption` to\n// disable the transform merge, but do not disable transform clone from rawOption.\n\nexport function disableTransformOptionMerge(datasetModel) {\n  var transformOption = datasetModel.option.transform;\n  transformOption && setAsPrimitive(datasetModel.option.transform);\n}\n\nfunction isSeries(sourceHost) {\n  // Avoid circular dependency with Series.ts\n  return sourceHost.mainType === 'series';\n}\n\nfunction doThrow(errMsg) {\n  throw new Error(errMsg);\n}"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,cAAc,EAAEC,GAAG,EAAEC,YAAY,EAAEC,MAAM,EAAEC,IAAI,EAAEC,SAAS,QAAQ,0BAA0B;AACrG,SAASC,YAAY,EAAEC,kBAAkB,QAAQ,cAAc;AAC/D,SAASC,yBAAyB,EAAEC,sBAAsB,QAAQ,qBAAqB;AACvF,SAASC,+BAA+B,EAAEC,iCAAiC,QAAQ,mBAAmB;AACtG,SAASC,kBAAkB,QAAQ,gBAAgB;AACnD,OAAOC,SAAS,MAAM,iBAAiB;AACvC,SAASC,mBAAmB,QAAQ,mBAAmB;AACvD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,IAAIC,aAAa,GACjB;AACA,YAAY;EACV,SAASA,aAAaA,CAACC,UAAU,EAAE;IACjC;IACA,IAAI,CAACC,WAAW,GAAG,EAAE;IACrB,IAAI,CAACC,UAAU,GAAG,EAAE,CAAC,CAAC;;IAEtB,IAAI,CAACC,iBAAiB,GAAG,EAAE;IAC3B,IAAI,CAACC,gBAAgB,GAAG,CAAC;IACzB,IAAI,CAACC,MAAM,GAAG,IAAI;IAClB,IAAI,CAACC,WAAW,GAAGN,UAAU;EAC/B;EACA;AACF;AACA;;EAGED,aAAa,CAACQ,SAAS,CAACC,KAAK,GAAG,YAAY;IAC1C,IAAI,CAACC,eAAe,CAAC,EAAE,EAAE,EAAE,CAAC;IAE5B,IAAI,CAACP,UAAU,GAAG,EAAE;IACpB,IAAI,CAACG,MAAM,GAAG,IAAI;EACpB,CAAC;EAEDN,aAAa,CAACQ,SAAS,CAACE,eAAe,GAAG,UAAUC,UAAU,EAAEC,gBAAgB,EAAE;IAChF,IAAI,CAACV,WAAW,GAAGS,UAAU;IAC7B,IAAI,CAACP,iBAAiB,GAAGQ,gBAAgB;IACzC,IAAI,CAACP,gBAAgB,EAAE;IAEvB,IAAI,IAAI,CAACA,gBAAgB,GAAG,IAAI,EAAE;MAChC,IAAI,CAACA,gBAAgB,GAAG,CAAC;IAC3B;EACF,CAAC;EACD;AACF;AACA;AACA;;EAGEL,aAAa,CAACQ,SAAS,CAACK,eAAe,GAAG,YAAY;IACpD,OAAO,IAAI,CAACN,WAAW,CAACO,GAAG,GAAG,GAAG,GAAG,IAAI,CAACT,gBAAgB;EAC3D,CAAC;EACD;AACF;AACA;;EAGEL,aAAa,CAACQ,SAAS,CAACO,aAAa,GAAG,YAAY;IAClD;IACA;IACA,IAAI,IAAI,CAACC,QAAQ,CAAC,CAAC,EAAE;MACnB,IAAI,CAACC,aAAa,CAAC,CAAC;MAEpB,IAAI,CAACX,MAAM,GAAG,KAAK;IACrB;EACF,CAAC;EAEDN,aAAa,CAACQ,SAAS,CAACS,aAAa,GAAG,YAAY;IAClD,IAAI,CAACP,eAAe,CAAC,EAAE,EAAE,EAAE,CAAC;IAE5B,IAAIT,UAAU,GAAG,IAAI,CAACM,WAAW;IAEjC,IAAIW,eAAe,GAAG,IAAI,CAACC,0BAA0B,CAAC,CAAC;IAEvD,IAAIC,WAAW,GAAG,CAAC,CAACF,eAAe,CAACG,MAAM;IAC1C,IAAIC,gBAAgB;IACpB,IAAIV,gBAAgB;IAEpB,IAAIW,QAAQ,CAACtB,UAAU,CAAC,EAAE;MACxB,IAAIuB,WAAW,GAAGvB,UAAU;MAC5B,IAAIwB,IAAI,GAAG,KAAK,CAAC;MACjB,IAAIC,YAAY,GAAG,KAAK,CAAC;MACzB,IAAIC,QAAQ,GAAG,KAAK,CAAC,CAAC,CAAC;;MAEvB,IAAIP,WAAW,EAAE;QACf,IAAIQ,WAAW,GAAGV,eAAe,CAAC,CAAC,CAAC;QACpCU,WAAW,CAACb,aAAa,CAAC,CAAC;QAC3BY,QAAQ,GAAGC,WAAW,CAACC,SAAS,CAAC,CAAC;QAClCJ,IAAI,GAAGE,QAAQ,CAACF,IAAI;QACpBC,YAAY,GAAGC,QAAQ,CAACD,YAAY;QACpCd,gBAAgB,GAAG,CAACgB,WAAW,CAACf,eAAe,CAAC,CAAC,CAAC;MACpD,CAAC,CAAC;MAAA,KACG;QACDY,IAAI,GAAGD,WAAW,CAACM,GAAG,CAAC,MAAM,EAAE,IAAI,CAAC;QACpCJ,YAAY,GAAGvC,YAAY,CAACsC,IAAI,CAAC,GAAGhC,yBAAyB,GAAGC,sBAAsB;QACtFkB,gBAAgB,GAAG,EAAE;MACvB,CAAC,CAAC;;MAGJ,IAAImB,gBAAgB,GAAG,IAAI,CAACC,uBAAuB,CAAC,CAAC,IAAI,CAAC,CAAC;MAC3D,IAAIC,eAAe,GAAGN,QAAQ,IAAIA,QAAQ,CAACO,aAAa,IAAI,CAAC,CAAC;MAC9D,IAAIC,cAAc,GAAG7C,SAAS,CAACyC,gBAAgB,CAACI,cAAc,EAAEF,eAAe,CAACE,cAAc,CAAC,IAAI,IAAI;MACvG,IAAIC,YAAY,GAAG9C,SAAS,CAACyC,gBAAgB,CAACK,YAAY,EAAEH,eAAe,CAACG,YAAY,CAAC,CAAC,CAAC;MAC3F;MACA;;MAEA,IAAIC,UAAU,GAAG/C,SAAS,CAACyC,gBAAgB,CAACM,UAAU,EAAEJ,eAAe,CAACI,UAAU,CAAC,CAAC,CAAC;MACrF;;MAEA,IAAIC,iBAAiB,GAAGH,cAAc,KAAKF,eAAe,CAACE,cAAc,IAAI,CAAC,CAACC,YAAY,KAAK,CAAC,CAACH,eAAe,CAACG,YAAY,IAAIC,UAAU;MAC5If,gBAAgB,GAAGgB,iBAAiB,GAAG,CAAC/C,YAAY,CAACkC,IAAI,EAAE;QACzDU,cAAc,EAAEA,cAAc;QAC9BC,YAAY,EAAEA,YAAY;QAC1BC,UAAU,EAAEA;MACd,CAAC,EAAEX,YAAY,CAAC,CAAC,GAAG,EAAE;IACxB,CAAC,MAAM;MACL,IAAIa,YAAY,GAAGtC,UAAU,CAAC,CAAC;;MAE/B,IAAImB,WAAW,EAAE;QACf,IAAIoB,MAAM,GAAG,IAAI,CAACC,eAAe,CAACvB,eAAe,CAAC;QAElDI,gBAAgB,GAAGkB,MAAM,CAAC7B,UAAU;QACpCC,gBAAgB,GAAG4B,MAAM,CAAC5B,gBAAgB;MAC5C,CAAC,CAAC;MAAA,KACG;QACD,IAAI8B,UAAU,GAAGH,YAAY,CAACT,GAAG,CAAC,QAAQ,EAAE,IAAI,CAAC;QACjDR,gBAAgB,GAAG,CAAC/B,YAAY,CAACmD,UAAU,EAAE,IAAI,CAACV,uBAAuB,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC;QACnFpB,gBAAgB,GAAG,EAAE;MACvB;IACJ;IAEA,IAAI+B,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;MACzCzD,MAAM,CAACkC,gBAAgB,IAAIV,gBAAgB,CAAC;IAC9C;IAEA,IAAI,CAACF,eAAe,CAACY,gBAAgB,EAAEV,gBAAgB,CAAC;EAC1D,CAAC;EAEDZ,aAAa,CAACQ,SAAS,CAACiC,eAAe,GAAG,UAAUK,SAAS,EAAE;IAC7D,IAAIP,YAAY,GAAG,IAAI,CAAChC,WAAW;IACnC,IAAIwC,eAAe,GAAGR,YAAY,CAACT,GAAG,CAAC,WAAW,EAAE,IAAI,CAAC;IACzD,IAAIkB,mBAAmB,GAAGT,YAAY,CAACT,GAAG,CAAC,qBAAqB,EAAE,IAAI,CAAC;IAEvE,IAAIa,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;MACzCzD,MAAM,CAAC4D,mBAAmB,IAAI,IAAI,IAAID,eAAe,IAAI,IAAI,CAAC;IAChE;IAEA,IAAIC,mBAAmB,IAAI,IAAI,EAAE;MAC/B,IAAIC,MAAM,GAAG,EAAE;MAEf,IAAIH,SAAS,CAACzB,MAAM,KAAK,CAAC,EAAE;QAC1B,IAAIsB,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;UACzCI,MAAM,GAAG,6EAA6E;QACxF;QAEAC,OAAO,CAACD,MAAM,CAAC;MACjB;IACF;IAEA,IAAItC,UAAU;IACd,IAAIwC,YAAY,GAAG,EAAE;IACrB,IAAIvC,gBAAgB,GAAG,EAAE;IACzBvB,IAAI,CAACyD,SAAS,EAAE,UAAUM,KAAK,EAAE;MAC/BA,KAAK,CAACrC,aAAa,CAAC,CAAC;MACrB,IAAIY,QAAQ,GAAGyB,KAAK,CAACvB,SAAS,CAACmB,mBAAmB,IAAI,CAAC,CAAC;MACxD,IAAIC,MAAM,GAAG,EAAE;MAEf,IAAID,mBAAmB,IAAI,IAAI,IAAI,CAACrB,QAAQ,EAAE;QAC5C,IAAIgB,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;UACzCI,MAAM,GAAG,oDAAoD,GAAGD,mBAAmB;QACrF;QAEAE,OAAO,CAACD,MAAM,CAAC;MACjB;MAEAE,YAAY,CAACE,IAAI,CAAC1B,QAAQ,CAAC;MAC3Bf,gBAAgB,CAACyC,IAAI,CAACD,KAAK,CAACvC,eAAe,CAAC,CAAC,CAAC;IAChD,CAAC,CAAC;IAEF,IAAIkC,eAAe,EAAE;MACnBpC,UAAU,GAAGd,kBAAkB,CAACkD,eAAe,EAAEI,YAAY,EAAE;QAC7DG,YAAY,EAAEf,YAAY,CAACgB;MAC7B,CAAC,CAAC;IACJ,CAAC,MAAM,IAAIP,mBAAmB,IAAI,IAAI,EAAE;MACtCrC,UAAU,GAAG,CAACnB,kBAAkB,CAAC2D,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC;IACpD;IAEA,OAAO;MACLxC,UAAU,EAAEA,UAAU;MACtBC,gBAAgB,EAAEA;IACpB,CAAC;EACH,CAAC;EAEDZ,aAAa,CAACQ,SAAS,CAACQ,QAAQ,GAAG,YAAY;IAC7C,IAAI,IAAI,CAACV,MAAM,EAAE;MACf,OAAO,IAAI;IACb,CAAC,CAAC;;IAGF,IAAIY,eAAe,GAAG,IAAI,CAACC,0BAA0B,CAAC,CAAC;IAEvD,KAAK,IAAIqC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGtC,eAAe,CAACG,MAAM,EAAEmC,CAAC,EAAE,EAAE;MAC/C,IAAIC,QAAQ,GAAGvC,eAAe,CAACsC,CAAC,CAAC;MAEjC;MAAK;MACL;MACAC,QAAQ,CAACzC,QAAQ,CAAC,CAAC,IAAI,IAAI,CAACZ,iBAAiB,CAACoD,CAAC,CAAC,KAAKC,QAAQ,CAAC5C,eAAe,CAAC,CAAC,EAAE;QAC/E,OAAO,IAAI;MACb;IACF;EACF,CAAC;EACD;AACF;AACA;AACA;;EAGEb,aAAa,CAACQ,SAAS,CAACqB,SAAS,GAAG,UAAU6B,WAAW,EAAE;IACzDA,WAAW,GAAGA,WAAW,IAAI,CAAC;IAC9B,IAAIC,MAAM,GAAG,IAAI,CAACzD,WAAW,CAACwD,WAAW,CAAC;IAE1C,IAAI,CAACC,MAAM,EAAE;MACX;MACA,IAAIzC,eAAe,GAAG,IAAI,CAACC,0BAA0B,CAAC,CAAC;MAEvD,OAAOD,eAAe,CAAC,CAAC,CAAC,IAAIA,eAAe,CAAC,CAAC,CAAC,CAACW,SAAS,CAAC6B,WAAW,CAAC;IACxE;IAEA,OAAOC,MAAM;EACf,CAAC;EACD;AACF;AACA;AACA;AACA;AACA;AACA;AACA;;EAGE3D,aAAa,CAACQ,SAAS,CAACoD,kBAAkB,GAAG,UAAUC,gBAAgB,EAAE;IACvE,IAAIlB,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;MACzCzD,MAAM,CAACmC,QAAQ,CAAC,IAAI,CAAChB,WAAW,CAAC,EAAE,sDAAsD,CAAC;IAC5F;IAEA,IAAIuD,MAAM,GAAGD,gBAAgB,CAACE,eAAe,CAAC,CAAC;IAC/C,OAAO,IAAI,CAACC,kBAAkB,CAACF,MAAM,CAACzB,UAAU,EAAEwB,gBAAgB,CAACF,MAAM,EAAEG,MAAM,CAACG,IAAI,CAAC;EACzF,CAAC;EAEDjE,aAAa,CAACQ,SAAS,CAACwD,kBAAkB,GAAG,UAAUE,SAAS,EAAEC,YAAY,EAAEC,aAAa,EAAE;IAC7F;IACA,IAAIV,WAAW,GAAG,CAAC;IACnB,IAAIW,SAAS,GAAG,IAAI,CAAClE,UAAU;IAC/B,IAAImE,cAAc,GAAGD,SAAS,CAACX,WAAW,CAAC;IAE3C,IAAI,CAACY,cAAc,EAAE;MACnBA,cAAc,GAAGD,SAAS,CAACX,WAAW,CAAC,GAAG,CAAC,CAAC;IAC9C;IAEA,IAAIa,WAAW,GAAGD,cAAc,CAACF,aAAa,CAAC;IAE/C,IAAI,CAACG,WAAW,EAAE;MAChB,IAAI3C,WAAW,GAAG,IAAI,CAACT,0BAA0B,CAAC,CAAC,CAAC,CAAC,CAAC;MAEtD,IAAII,QAAQ,CAAC,IAAI,CAAChB,WAAW,CAAC,IAAIqB,WAAW,EAAE;QAC7C2C,WAAW,GAAG3C,WAAW,CAACoC,kBAAkB,CAACE,SAAS,EAAEC,YAAY,EAAEC,aAAa,CAAC;MACtF,CAAC,MAAM;QACLG,WAAW,GAAG,IAAIzE,SAAS,CAAC,CAAC,CAAC,CAAC;;QAE/ByE,WAAW,CAACC,QAAQ,CAAC,IAAIzE,mBAAmB,CAACoE,YAAY,EAAED,SAAS,CAAC7C,MAAM,CAAC,EAAE6C,SAAS,CAAC;MAC1F;MAEAI,cAAc,CAACF,aAAa,CAAC,GAAGG,WAAW;IAC7C;IAEA,OAAOA,WAAW;EACpB,CAAC;EACD;AACF;AACA;AACA;;EAGEvE,aAAa,CAACQ,SAAS,CAACW,0BAA0B,GAAG,YAAY;IAC/D;IACA;IACA;IACA,IAAIlB,UAAU,GAAG,IAAI,CAACM,WAAW;IAEjC,IAAIgB,QAAQ,CAACtB,UAAU,CAAC,EAAE;MACxB,IAAIsC,YAAY,GAAG5C,+BAA+B,CAACM,UAAU,CAAC;MAC9D,OAAO,CAACsC,YAAY,GAAG,EAAE,GAAG,CAACA,YAAY,CAACkC,gBAAgB,CAAC,CAAC,CAAC;IAC/D,CAAC,MAAM;MACL,OAAOvF,GAAG,CAACU,iCAAiC,CAACK,UAAU,CAAC,EAAE,UAAUsC,YAAY,EAAE;QAChF,OAAOA,YAAY,CAACkC,gBAAgB,CAAC,CAAC;MACxC,CAAC,CAAC;IACJ;EACF,CAAC;EAEDzE,aAAa,CAACQ,SAAS,CAACwB,uBAAuB,GAAG,YAAY;IAC5D,IAAI/B,UAAU,GAAG,IAAI,CAACM,WAAW;IACjC,IAAI4B,cAAc;IAClB,IAAIC,YAAY;IAChB,IAAIC,UAAU;IAEd,IAAId,QAAQ,CAACtB,UAAU,CAAC,EAAE;MACxBkC,cAAc,GAAGlC,UAAU,CAAC6B,GAAG,CAAC,gBAAgB,EAAE,IAAI,CAAC;MACvDM,YAAY,GAAGnC,UAAU,CAAC6B,GAAG,CAAC,cAAc,EAAE,IAAI,CAAC;MACnDO,UAAU,GAAGpC,UAAU,CAAC6B,GAAG,CAAC,YAAY,EAAE,IAAI,CAAC;IACjD,CAAC,CAAC;IAAA,KACG,IAAI,CAAC,IAAI,CAACX,0BAA0B,CAAC,CAAC,CAACE,MAAM,EAAE;MAChD,IAAIqD,KAAK,GAAGzE,UAAU;MACtBkC,cAAc,GAAGuC,KAAK,CAAC5C,GAAG,CAAC,gBAAgB,EAAE,IAAI,CAAC;MAClDM,YAAY,GAAGsC,KAAK,CAAC5C,GAAG,CAAC,cAAc,EAAE,IAAI,CAAC;MAC9CO,UAAU,GAAGqC,KAAK,CAAC5C,GAAG,CAAC,YAAY,EAAE,IAAI,CAAC;IAC5C;IAEF,OAAO;MACLK,cAAc,EAAEA,cAAc;MAC9BC,YAAY,EAAEA,YAAY;MAC1BC,UAAU,EAAEA;IACd,CAAC;EACH,CAAC;EAED,OAAOrC,aAAa;AACtB,CAAC,CAAC,CAAC;AAEH,SAASA,aAAa,GAAG,CAAC;AAC1B;;AAEA,OAAO,SAAS2E,2BAA2BA,CAACpC,YAAY,EAAE;EACxD,IAAIQ,eAAe,GAAGR,YAAY,CAACqC,MAAM,CAACC,SAAS;EACnD9B,eAAe,IAAI9D,cAAc,CAACsD,YAAY,CAACqC,MAAM,CAACC,SAAS,CAAC;AAClE;AAEA,SAAStD,QAAQA,CAACtB,UAAU,EAAE;EAC5B;EACA,OAAOA,UAAU,CAAC6E,QAAQ,KAAK,QAAQ;AACzC;AAEA,SAAS5B,OAAOA,CAACD,MAAM,EAAE;EACvB,MAAM,IAAI8B,KAAK,CAAC9B,MAAM,CAAC;AACzB"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}