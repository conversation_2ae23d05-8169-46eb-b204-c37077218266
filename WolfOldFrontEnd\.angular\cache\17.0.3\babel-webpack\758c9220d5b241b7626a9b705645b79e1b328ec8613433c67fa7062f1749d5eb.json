{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { isString, indexOf, each, bind, isArray, isDom } from 'zrender/lib/core/util.js';\nimport { normalizeEvent } from 'zrender/lib/core/event.js';\nimport { transformLocalCoord } from 'zrender/lib/core/dom.js';\nimport env from 'zrender/lib/core/env.js';\nimport { convertToColorString, toCamelCase, normalizeCssArray } from '../../util/format.js';\nimport { shouldTooltipConfine, toCSSVendorPrefix, getComputedStyle, TRANSFORM_VENDOR, TRANSITION_VENDOR } from './helper.js';\nimport { getPaddingFromTooltipModel } from './tooltipMarkup.js';\n/* global document, window */\n\nvar CSS_TRANSITION_VENDOR = toCSSVendorPrefix(TRANSITION_VENDOR, 'transition');\nvar CSS_TRANSFORM_VENDOR = toCSSVendorPrefix(TRANSFORM_VENDOR, 'transform'); // eslint-disable-next-line\n\nvar gCssText = \"position:absolute;display:block;border-style:solid;white-space:nowrap;z-index:9999999;\" + (env.transform3dSupported ? 'will-change:transform;' : '');\nfunction mirrorPos(pos) {\n  pos = pos === 'left' ? 'right' : pos === 'right' ? 'left' : pos === 'top' ? 'bottom' : 'top';\n  return pos;\n}\nfunction assembleArrow(tooltipModel, borderColor, arrowPosition) {\n  if (!isString(arrowPosition) || arrowPosition === 'inside') {\n    return '';\n  }\n  var backgroundColor = tooltipModel.get('backgroundColor');\n  var borderWidth = tooltipModel.get('borderWidth');\n  borderColor = convertToColorString(borderColor);\n  var arrowPos = mirrorPos(arrowPosition);\n  var arrowSize = Math.max(Math.round(borderWidth) * 1.5, 6);\n  var positionStyle = '';\n  var transformStyle = CSS_TRANSFORM_VENDOR + ':';\n  var rotateDeg;\n  if (indexOf(['left', 'right'], arrowPos) > -1) {\n    positionStyle += 'top:50%';\n    transformStyle += \"translateY(-50%) rotate(\" + (rotateDeg = arrowPos === 'left' ? -225 : -45) + \"deg)\";\n  } else {\n    positionStyle += 'left:50%';\n    transformStyle += \"translateX(-50%) rotate(\" + (rotateDeg = arrowPos === 'top' ? 225 : 45) + \"deg)\";\n  }\n  var rotateRadian = rotateDeg * Math.PI / 180;\n  var arrowWH = arrowSize + borderWidth;\n  var rotatedWH = arrowWH * Math.abs(Math.cos(rotateRadian)) + arrowWH * Math.abs(Math.sin(rotateRadian));\n  var arrowOffset = Math.round(((rotatedWH - Math.SQRT2 * borderWidth) / 2 + Math.SQRT2 * borderWidth - (rotatedWH - arrowWH) / 2) * 100) / 100;\n  positionStyle += \";\" + arrowPos + \":-\" + arrowOffset + \"px\";\n  var borderStyle = borderColor + \" solid \" + borderWidth + \"px;\";\n  var styleCss = [\"position:absolute;width:\" + arrowSize + \"px;height:\" + arrowSize + \"px;z-index:-1;\", positionStyle + \";\" + transformStyle + \";\", \"border-bottom:\" + borderStyle, \"border-right:\" + borderStyle, \"background-color:\" + backgroundColor + \";\"];\n  return \"<div style=\\\"\" + styleCss.join('') + \"\\\"></div>\";\n}\nfunction assembleTransition(duration, onlyFade) {\n  var transitionCurve = 'cubic-bezier(0.23,1,0.32,1)';\n  var transitionOption = \" \" + duration / 2 + \"s \" + transitionCurve;\n  var transitionText = \"opacity\" + transitionOption + \",visibility\" + transitionOption;\n  if (!onlyFade) {\n    transitionOption = \" \" + duration + \"s \" + transitionCurve;\n    transitionText += env.transformSupported ? \",\" + CSS_TRANSFORM_VENDOR + transitionOption : \",left\" + transitionOption + \",top\" + transitionOption;\n  }\n  return CSS_TRANSITION_VENDOR + ':' + transitionText;\n}\nfunction assembleTransform(x, y, toString) {\n  // If using float on style, the final width of the dom might\n  // keep changing slightly while mouse move. So `toFixed(0)` them.\n  var x0 = x.toFixed(0) + 'px';\n  var y0 = y.toFixed(0) + 'px'; // not support transform, use `left` and `top` instead.\n\n  if (!env.transformSupported) {\n    return toString ? \"top:\" + y0 + \";left:\" + x0 + \";\" : [['top', y0], ['left', x0]];\n  } // support transform\n\n  var is3d = env.transform3dSupported;\n  var translate = \"translate\" + (is3d ? '3d' : '') + \"(\" + x0 + \",\" + y0 + (is3d ? ',0' : '') + \")\";\n  return toString ? 'top:0;left:0;' + CSS_TRANSFORM_VENDOR + ':' + translate + ';' : [['top', 0], ['left', 0], [TRANSFORM_VENDOR, translate]];\n}\n/**\r\n * @param {Object} textStyle\r\n * @return {string}\r\n * @inner\r\n */\n\nfunction assembleFont(textStyleModel) {\n  var cssText = [];\n  var fontSize = textStyleModel.get('fontSize');\n  var color = textStyleModel.getTextColor();\n  color && cssText.push('color:' + color);\n  cssText.push('font:' + textStyleModel.getFont());\n  fontSize // @ts-ignore, leave it to the tooltip refactor.\n  && cssText.push('line-height:' + Math.round(fontSize * 3 / 2) + 'px');\n  var shadowColor = textStyleModel.get('textShadowColor');\n  var shadowBlur = textStyleModel.get('textShadowBlur') || 0;\n  var shadowOffsetX = textStyleModel.get('textShadowOffsetX') || 0;\n  var shadowOffsetY = textStyleModel.get('textShadowOffsetY') || 0;\n  shadowColor && shadowBlur && cssText.push('text-shadow:' + shadowOffsetX + 'px ' + shadowOffsetY + 'px ' + shadowBlur + 'px ' + shadowColor);\n  each(['decoration', 'align'], function (name) {\n    var val = textStyleModel.get(name);\n    val && cssText.push('text-' + name + ':' + val);\n  });\n  return cssText.join(';');\n}\nfunction assembleCssText(tooltipModel, enableTransition, onlyFade) {\n  var cssText = [];\n  var transitionDuration = tooltipModel.get('transitionDuration');\n  var backgroundColor = tooltipModel.get('backgroundColor');\n  var shadowBlur = tooltipModel.get('shadowBlur');\n  var shadowColor = tooltipModel.get('shadowColor');\n  var shadowOffsetX = tooltipModel.get('shadowOffsetX');\n  var shadowOffsetY = tooltipModel.get('shadowOffsetY');\n  var textStyleModel = tooltipModel.getModel('textStyle');\n  var padding = getPaddingFromTooltipModel(tooltipModel, 'html');\n  var boxShadow = shadowOffsetX + \"px \" + shadowOffsetY + \"px \" + shadowBlur + \"px \" + shadowColor;\n  cssText.push('box-shadow:' + boxShadow); // Animation transition. Do not animate when transitionDuration is 0.\n\n  enableTransition && transitionDuration && cssText.push(assembleTransition(transitionDuration, onlyFade));\n  if (backgroundColor) {\n    cssText.push('background-color:' + backgroundColor);\n  } // Border style\n\n  each(['width', 'color', 'radius'], function (name) {\n    var borderName = 'border-' + name;\n    var camelCase = toCamelCase(borderName);\n    var val = tooltipModel.get(camelCase);\n    val != null && cssText.push(borderName + ':' + val + (name === 'color' ? '' : 'px'));\n  }); // Text style\n\n  cssText.push(assembleFont(textStyleModel)); // Padding\n\n  if (padding != null) {\n    cssText.push('padding:' + normalizeCssArray(padding).join('px ') + 'px');\n  }\n  return cssText.join(';') + ';';\n} // If not able to make, do not modify the input `out`.\n\nfunction makeStyleCoord(out, zr, appendToBody, zrX, zrY) {\n  var zrPainter = zr && zr.painter;\n  if (appendToBody) {\n    var zrViewportRoot = zrPainter && zrPainter.getViewportRoot();\n    if (zrViewportRoot) {\n      // Some APPs might use scale on body, so we support CSS transform here.\n      transformLocalCoord(out, zrViewportRoot, document.body, zrX, zrY);\n    }\n  } else {\n    out[0] = zrX;\n    out[1] = zrY; // xy should be based on canvas root. But tooltipContent is\n    // the sibling of canvas root. So padding of ec container\n    // should be considered here.\n\n    var viewportRootOffset = zrPainter && zrPainter.getViewportRootOffset();\n    if (viewportRootOffset) {\n      out[0] += viewportRootOffset.offsetLeft;\n      out[1] += viewportRootOffset.offsetTop;\n    }\n  }\n  out[2] = out[0] / zr.getWidth();\n  out[3] = out[1] / zr.getHeight();\n}\nvar TooltipHTMLContent = /** @class */\nfunction () {\n  function TooltipHTMLContent(container, api, opt) {\n    this._show = false;\n    this._styleCoord = [0, 0, 0, 0];\n    this._enterable = true;\n    this._alwaysShowContent = false;\n    this._firstShow = true;\n    this._longHide = true;\n    if (env.wxa) {\n      return null;\n    }\n    var el = document.createElement('div'); // TODO: TYPE\n\n    el.domBelongToZr = true;\n    this.el = el;\n    var zr = this._zr = api.getZr();\n    var appendToBody = this._appendToBody = opt && opt.appendToBody;\n    makeStyleCoord(this._styleCoord, zr, appendToBody, api.getWidth() / 2, api.getHeight() / 2);\n    if (appendToBody) {\n      document.body.appendChild(el);\n    } else {\n      container.appendChild(el);\n    }\n    this._container = container; // FIXME\n    // Is it needed to trigger zr event manually if\n    // the browser do not support `pointer-events: none`.\n\n    var self = this;\n    el.onmouseenter = function () {\n      // clear the timeout in hideLater and keep showing tooltip\n      if (self._enterable) {\n        clearTimeout(self._hideTimeout);\n        self._show = true;\n      }\n      self._inContent = true;\n    };\n    el.onmousemove = function (e) {\n      e = e || window.event;\n      if (!self._enterable) {\n        // `pointer-events: none` is set to tooltip content div\n        // if `enterable` is set as `false`, and `el.onmousemove`\n        // can not be triggered. But in browser that do not\n        // support `pointer-events`, we need to do this:\n        // Try trigger zrender event to avoid mouse\n        // in and out shape too frequently\n        var handler = zr.handler;\n        var zrViewportRoot = zr.painter.getViewportRoot();\n        normalizeEvent(zrViewportRoot, e, true);\n        handler.dispatch('mousemove', e);\n      }\n    };\n    el.onmouseleave = function () {\n      // set `_inContent` to `false` before `hideLater`\n      self._inContent = false;\n      if (self._enterable) {\n        if (self._show) {\n          self.hideLater(self._hideDelay);\n        }\n      }\n    };\n  }\n  /**\r\n   * Update when tooltip is rendered\r\n   */\n\n  TooltipHTMLContent.prototype.update = function (tooltipModel) {\n    // FIXME\n    // Move this logic to ec main?\n    var container = this._container;\n    var position = getComputedStyle(container, 'position');\n    var domStyle = container.style;\n    if (domStyle.position !== 'absolute' && position !== 'absolute') {\n      domStyle.position = 'relative';\n    } // move tooltip if chart resized\n\n    var alwaysShowContent = tooltipModel.get('alwaysShowContent');\n    alwaysShowContent && this._moveIfResized(); // update alwaysShowContent\n\n    this._alwaysShowContent = alwaysShowContent; // update className\n\n    this.el.className = tooltipModel.get('className') || ''; // Hide the tooltip\n    // PENDING\n    // this.hide();\n  };\n\n  TooltipHTMLContent.prototype.show = function (tooltipModel, nearPointColor) {\n    clearTimeout(this._hideTimeout);\n    clearTimeout(this._longHideTimeout);\n    var el = this.el;\n    var style = el.style;\n    var styleCoord = this._styleCoord;\n    if (!el.innerHTML) {\n      style.display = 'none';\n    } else {\n      style.cssText = gCssText + assembleCssText(tooltipModel, !this._firstShow, this._longHide) // initial transform\n      + assembleTransform(styleCoord[0], styleCoord[1], true) + (\"border-color:\" + convertToColorString(nearPointColor) + \";\") + (tooltipModel.get('extraCssText') || '') // If mouse occasionally move over the tooltip, a mouseout event will be\n      // triggered by canvas, and cause some unexpectable result like dragging\n      // stop, \"unfocusAdjacency\". Here `pointer-events: none` is used to solve\n      // it. Although it is not supported by IE8~IE10, fortunately it is a rare\n      // scenario.\n      + (\";pointer-events:\" + (this._enterable ? 'auto' : 'none'));\n    }\n    this._show = true;\n    this._firstShow = false;\n    this._longHide = false;\n  };\n  TooltipHTMLContent.prototype.setContent = function (content, markers, tooltipModel, borderColor, arrowPosition) {\n    var el = this.el;\n    if (content == null) {\n      el.innerHTML = '';\n      return;\n    }\n    var arrow = '';\n    if (isString(arrowPosition) && tooltipModel.get('trigger') === 'item' && !shouldTooltipConfine(tooltipModel)) {\n      arrow = assembleArrow(tooltipModel, borderColor, arrowPosition);\n    }\n    if (isString(content)) {\n      el.innerHTML = content + arrow;\n    } else if (content) {\n      // Clear previous\n      el.innerHTML = '';\n      if (!isArray(content)) {\n        content = [content];\n      }\n      for (var i = 0; i < content.length; i++) {\n        if (isDom(content[i]) && content[i].parentNode !== el) {\n          el.appendChild(content[i]);\n        }\n      } // no arrow if empty\n\n      if (arrow && el.childNodes.length) {\n        // no need to create a new parent element, but it's not supported by IE 10 and older.\n        // const arrowEl = document.createRange().createContextualFragment(arrow);\n        var arrowEl = document.createElement('div');\n        arrowEl.innerHTML = arrow;\n        el.appendChild(arrowEl);\n      }\n    }\n  };\n  TooltipHTMLContent.prototype.setEnterable = function (enterable) {\n    this._enterable = enterable;\n  };\n  TooltipHTMLContent.prototype.getSize = function () {\n    var el = this.el;\n    return [el.offsetWidth, el.offsetHeight];\n  };\n  TooltipHTMLContent.prototype.moveTo = function (zrX, zrY) {\n    var styleCoord = this._styleCoord;\n    makeStyleCoord(styleCoord, this._zr, this._appendToBody, zrX, zrY);\n    if (styleCoord[0] != null && styleCoord[1] != null) {\n      var style_1 = this.el.style;\n      var transforms = assembleTransform(styleCoord[0], styleCoord[1]);\n      each(transforms, function (transform) {\n        style_1[transform[0]] = transform[1];\n      });\n    }\n  };\n  /**\r\n   * when `alwaysShowContent` is true,\r\n   * move the tooltip after chart resized\r\n   */\n\n  TooltipHTMLContent.prototype._moveIfResized = function () {\n    // The ratio of left to width\n    var ratioX = this._styleCoord[2]; // The ratio of top to height\n\n    var ratioY = this._styleCoord[3];\n    this.moveTo(ratioX * this._zr.getWidth(), ratioY * this._zr.getHeight());\n  };\n  TooltipHTMLContent.prototype.hide = function () {\n    var _this = this;\n    var style = this.el.style;\n    style.visibility = 'hidden';\n    style.opacity = '0';\n    env.transform3dSupported && (style.willChange = '');\n    this._show = false;\n    this._longHideTimeout = setTimeout(function () {\n      return _this._longHide = true;\n    }, 500);\n  };\n  TooltipHTMLContent.prototype.hideLater = function (time) {\n    if (this._show && !(this._inContent && this._enterable) && !this._alwaysShowContent) {\n      if (time) {\n        this._hideDelay = time; // Set show false to avoid invoke hideLater multiple times\n\n        this._show = false;\n        this._hideTimeout = setTimeout(bind(this.hide, this), time);\n      } else {\n        this.hide();\n      }\n    }\n  };\n  TooltipHTMLContent.prototype.isShow = function () {\n    return this._show;\n  };\n  TooltipHTMLContent.prototype.dispose = function () {\n    this.el.parentNode.removeChild(this.el);\n  };\n  return TooltipHTMLContent;\n}();\nexport default TooltipHTMLContent;", "map": {"version": 3, "names": ["isString", "indexOf", "each", "bind", "isArray", "isDom", "normalizeEvent", "transformLocalCoord", "env", "convertToColorString", "toCamelCase", "normalizeCssArray", "shouldTooltipConfine", "toCSSVendorPrefix", "getComputedStyle", "TRANSFORM_VENDOR", "TRANSITION_VENDOR", "getPaddingFromTooltipModel", "CSS_TRANSITION_VENDOR", "CSS_TRANSFORM_VENDOR", "gCssText", "transform3dSupported", "mirrorPos", "pos", "assembleArrow", "tooltipModel", "borderColor", "arrowPosition", "backgroundColor", "get", "borderWidth", "arrowPos", "arrowSize", "Math", "max", "round", "positionStyle", "transformStyle", "rotateDeg", "rotateRadian", "PI", "arrowWH", "rotatedWH", "abs", "cos", "sin", "arrowOffset", "SQRT2", "borderStyle", "styleCss", "join", "assembleTransition", "duration", "onlyFade", "transitionCurve", "transitionOption", "transitionText", "transformSupported", "assembleTransform", "x", "y", "toString", "x0", "toFixed", "y0", "is3d", "translate", "assembleFont", "textStyleModel", "cssText", "fontSize", "color", "getTextColor", "push", "getFont", "shadowColor", "<PERSON><PERSON><PERSON><PERSON>", "shadowOffsetX", "shadowOffsetY", "name", "val", "assembleCssText", "enableTransition", "transitionDuration", "getModel", "padding", "boxShadow", "borderName", "camelCase", "makeStyleCoord", "out", "zr", "appendToBody", "zrX", "zrY", "zrPainter", "painter", "zrViewportRoot", "getViewportRoot", "document", "body", "viewportRootOffset", "getViewportRootOffset", "offsetLeft", "offsetTop", "getWidth", "getHeight", "TooltipHTMLContent", "container", "api", "opt", "_show", "_styleCoord", "_enterable", "_alwaysShowContent", "_firstShow", "_longHide", "wxa", "el", "createElement", "domBelongToZr", "_zr", "getZr", "_appendToBody", "append<PERSON><PERSON><PERSON>", "_container", "self", "onmouseenter", "clearTimeout", "_hideTimeout", "_inContent", "<PERSON><PERSON><PERSON><PERSON>", "e", "window", "event", "handler", "dispatch", "onmouseleave", "hideLater", "_hideDelay", "prototype", "update", "position", "domStyle", "style", "alwaysS<PERSON><PERSON><PERSON>nt", "_moveIfResized", "className", "show", "nearPointColor", "_longHideTimeout", "styleCoord", "innerHTML", "display", "<PERSON><PERSON><PERSON><PERSON>", "content", "markers", "arrow", "i", "length", "parentNode", "childNodes", "arrowEl", "setEnterable", "enterable", "getSize", "offsetWidth", "offsetHeight", "moveTo", "style_1", "transforms", "transform", "ratioX", "ratioY", "hide", "_this", "visibility", "opacity", "<PERSON><PERSON><PERSON><PERSON>", "setTimeout", "time", "isShow", "dispose", "<PERSON><PERSON><PERSON><PERSON>"], "sources": ["C:/WolfSystemV2/WolfOldFrontEnd/node_modules/echarts/lib/component/tooltip/TooltipHTMLContent.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { isString, indexOf, each, bind, isArray, isDom } from 'zrender/lib/core/util.js';\nimport { normalizeEvent } from 'zrender/lib/core/event.js';\nimport { transformLocalCoord } from 'zrender/lib/core/dom.js';\nimport env from 'zrender/lib/core/env.js';\nimport { convertToColorString, toCamelCase, normalizeCssArray } from '../../util/format.js';\nimport { shouldTooltipConfine, toCSSVendorPrefix, getComputedStyle, TRANSFORM_VENDOR, TRANSITION_VENDOR } from './helper.js';\nimport { getPaddingFromTooltipModel } from './tooltipMarkup.js';\n/* global document, window */\n\nvar CSS_TRANSITION_VENDOR = toCSSVendorPrefix(TRANSITION_VENDOR, 'transition');\nvar CSS_TRANSFORM_VENDOR = toCSSVendorPrefix(TRANSFORM_VENDOR, 'transform'); // eslint-disable-next-line\n\nvar gCssText = \"position:absolute;display:block;border-style:solid;white-space:nowrap;z-index:9999999;\" + (env.transform3dSupported ? 'will-change:transform;' : '');\n\nfunction mirrorPos(pos) {\n  pos = pos === 'left' ? 'right' : pos === 'right' ? 'left' : pos === 'top' ? 'bottom' : 'top';\n  return pos;\n}\n\nfunction assembleArrow(tooltipModel, borderColor, arrowPosition) {\n  if (!isString(arrowPosition) || arrowPosition === 'inside') {\n    return '';\n  }\n\n  var backgroundColor = tooltipModel.get('backgroundColor');\n  var borderWidth = tooltipModel.get('borderWidth');\n  borderColor = convertToColorString(borderColor);\n  var arrowPos = mirrorPos(arrowPosition);\n  var arrowSize = Math.max(Math.round(borderWidth) * 1.5, 6);\n  var positionStyle = '';\n  var transformStyle = CSS_TRANSFORM_VENDOR + ':';\n  var rotateDeg;\n\n  if (indexOf(['left', 'right'], arrowPos) > -1) {\n    positionStyle += 'top:50%';\n    transformStyle += \"translateY(-50%) rotate(\" + (rotateDeg = arrowPos === 'left' ? -225 : -45) + \"deg)\";\n  } else {\n    positionStyle += 'left:50%';\n    transformStyle += \"translateX(-50%) rotate(\" + (rotateDeg = arrowPos === 'top' ? 225 : 45) + \"deg)\";\n  }\n\n  var rotateRadian = rotateDeg * Math.PI / 180;\n  var arrowWH = arrowSize + borderWidth;\n  var rotatedWH = arrowWH * Math.abs(Math.cos(rotateRadian)) + arrowWH * Math.abs(Math.sin(rotateRadian));\n  var arrowOffset = Math.round(((rotatedWH - Math.SQRT2 * borderWidth) / 2 + Math.SQRT2 * borderWidth - (rotatedWH - arrowWH) / 2) * 100) / 100;\n  positionStyle += \";\" + arrowPos + \":-\" + arrowOffset + \"px\";\n  var borderStyle = borderColor + \" solid \" + borderWidth + \"px;\";\n  var styleCss = [\"position:absolute;width:\" + arrowSize + \"px;height:\" + arrowSize + \"px;z-index:-1;\", positionStyle + \";\" + transformStyle + \";\", \"border-bottom:\" + borderStyle, \"border-right:\" + borderStyle, \"background-color:\" + backgroundColor + \";\"];\n  return \"<div style=\\\"\" + styleCss.join('') + \"\\\"></div>\";\n}\n\nfunction assembleTransition(duration, onlyFade) {\n  var transitionCurve = 'cubic-bezier(0.23,1,0.32,1)';\n  var transitionOption = \" \" + duration / 2 + \"s \" + transitionCurve;\n  var transitionText = \"opacity\" + transitionOption + \",visibility\" + transitionOption;\n\n  if (!onlyFade) {\n    transitionOption = \" \" + duration + \"s \" + transitionCurve;\n    transitionText += env.transformSupported ? \",\" + CSS_TRANSFORM_VENDOR + transitionOption : \",left\" + transitionOption + \",top\" + transitionOption;\n  }\n\n  return CSS_TRANSITION_VENDOR + ':' + transitionText;\n}\n\nfunction assembleTransform(x, y, toString) {\n  // If using float on style, the final width of the dom might\n  // keep changing slightly while mouse move. So `toFixed(0)` them.\n  var x0 = x.toFixed(0) + 'px';\n  var y0 = y.toFixed(0) + 'px'; // not support transform, use `left` and `top` instead.\n\n  if (!env.transformSupported) {\n    return toString ? \"top:\" + y0 + \";left:\" + x0 + \";\" : [['top', y0], ['left', x0]];\n  } // support transform\n\n\n  var is3d = env.transform3dSupported;\n  var translate = \"translate\" + (is3d ? '3d' : '') + \"(\" + x0 + \",\" + y0 + (is3d ? ',0' : '') + \")\";\n  return toString ? 'top:0;left:0;' + CSS_TRANSFORM_VENDOR + ':' + translate + ';' : [['top', 0], ['left', 0], [TRANSFORM_VENDOR, translate]];\n}\n/**\r\n * @param {Object} textStyle\r\n * @return {string}\r\n * @inner\r\n */\n\n\nfunction assembleFont(textStyleModel) {\n  var cssText = [];\n  var fontSize = textStyleModel.get('fontSize');\n  var color = textStyleModel.getTextColor();\n  color && cssText.push('color:' + color);\n  cssText.push('font:' + textStyleModel.getFont());\n  fontSize // @ts-ignore, leave it to the tooltip refactor.\n  && cssText.push('line-height:' + Math.round(fontSize * 3 / 2) + 'px');\n  var shadowColor = textStyleModel.get('textShadowColor');\n  var shadowBlur = textStyleModel.get('textShadowBlur') || 0;\n  var shadowOffsetX = textStyleModel.get('textShadowOffsetX') || 0;\n  var shadowOffsetY = textStyleModel.get('textShadowOffsetY') || 0;\n  shadowColor && shadowBlur && cssText.push('text-shadow:' + shadowOffsetX + 'px ' + shadowOffsetY + 'px ' + shadowBlur + 'px ' + shadowColor);\n  each(['decoration', 'align'], function (name) {\n    var val = textStyleModel.get(name);\n    val && cssText.push('text-' + name + ':' + val);\n  });\n  return cssText.join(';');\n}\n\nfunction assembleCssText(tooltipModel, enableTransition, onlyFade) {\n  var cssText = [];\n  var transitionDuration = tooltipModel.get('transitionDuration');\n  var backgroundColor = tooltipModel.get('backgroundColor');\n  var shadowBlur = tooltipModel.get('shadowBlur');\n  var shadowColor = tooltipModel.get('shadowColor');\n  var shadowOffsetX = tooltipModel.get('shadowOffsetX');\n  var shadowOffsetY = tooltipModel.get('shadowOffsetY');\n  var textStyleModel = tooltipModel.getModel('textStyle');\n  var padding = getPaddingFromTooltipModel(tooltipModel, 'html');\n  var boxShadow = shadowOffsetX + \"px \" + shadowOffsetY + \"px \" + shadowBlur + \"px \" + shadowColor;\n  cssText.push('box-shadow:' + boxShadow); // Animation transition. Do not animate when transitionDuration is 0.\n\n  enableTransition && transitionDuration && cssText.push(assembleTransition(transitionDuration, onlyFade));\n\n  if (backgroundColor) {\n    cssText.push('background-color:' + backgroundColor);\n  } // Border style\n\n\n  each(['width', 'color', 'radius'], function (name) {\n    var borderName = 'border-' + name;\n    var camelCase = toCamelCase(borderName);\n    var val = tooltipModel.get(camelCase);\n    val != null && cssText.push(borderName + ':' + val + (name === 'color' ? '' : 'px'));\n  }); // Text style\n\n  cssText.push(assembleFont(textStyleModel)); // Padding\n\n  if (padding != null) {\n    cssText.push('padding:' + normalizeCssArray(padding).join('px ') + 'px');\n  }\n\n  return cssText.join(';') + ';';\n} // If not able to make, do not modify the input `out`.\n\n\nfunction makeStyleCoord(out, zr, appendToBody, zrX, zrY) {\n  var zrPainter = zr && zr.painter;\n\n  if (appendToBody) {\n    var zrViewportRoot = zrPainter && zrPainter.getViewportRoot();\n\n    if (zrViewportRoot) {\n      // Some APPs might use scale on body, so we support CSS transform here.\n      transformLocalCoord(out, zrViewportRoot, document.body, zrX, zrY);\n    }\n  } else {\n    out[0] = zrX;\n    out[1] = zrY; // xy should be based on canvas root. But tooltipContent is\n    // the sibling of canvas root. So padding of ec container\n    // should be considered here.\n\n    var viewportRootOffset = zrPainter && zrPainter.getViewportRootOffset();\n\n    if (viewportRootOffset) {\n      out[0] += viewportRootOffset.offsetLeft;\n      out[1] += viewportRootOffset.offsetTop;\n    }\n  }\n\n  out[2] = out[0] / zr.getWidth();\n  out[3] = out[1] / zr.getHeight();\n}\n\nvar TooltipHTMLContent =\n/** @class */\nfunction () {\n  function TooltipHTMLContent(container, api, opt) {\n    this._show = false;\n    this._styleCoord = [0, 0, 0, 0];\n    this._enterable = true;\n    this._alwaysShowContent = false;\n    this._firstShow = true;\n    this._longHide = true;\n\n    if (env.wxa) {\n      return null;\n    }\n\n    var el = document.createElement('div'); // TODO: TYPE\n\n    el.domBelongToZr = true;\n    this.el = el;\n    var zr = this._zr = api.getZr();\n    var appendToBody = this._appendToBody = opt && opt.appendToBody;\n    makeStyleCoord(this._styleCoord, zr, appendToBody, api.getWidth() / 2, api.getHeight() / 2);\n\n    if (appendToBody) {\n      document.body.appendChild(el);\n    } else {\n      container.appendChild(el);\n    }\n\n    this._container = container; // FIXME\n    // Is it needed to trigger zr event manually if\n    // the browser do not support `pointer-events: none`.\n\n    var self = this;\n\n    el.onmouseenter = function () {\n      // clear the timeout in hideLater and keep showing tooltip\n      if (self._enterable) {\n        clearTimeout(self._hideTimeout);\n        self._show = true;\n      }\n\n      self._inContent = true;\n    };\n\n    el.onmousemove = function (e) {\n      e = e || window.event;\n\n      if (!self._enterable) {\n        // `pointer-events: none` is set to tooltip content div\n        // if `enterable` is set as `false`, and `el.onmousemove`\n        // can not be triggered. But in browser that do not\n        // support `pointer-events`, we need to do this:\n        // Try trigger zrender event to avoid mouse\n        // in and out shape too frequently\n        var handler = zr.handler;\n        var zrViewportRoot = zr.painter.getViewportRoot();\n        normalizeEvent(zrViewportRoot, e, true);\n        handler.dispatch('mousemove', e);\n      }\n    };\n\n    el.onmouseleave = function () {\n      // set `_inContent` to `false` before `hideLater`\n      self._inContent = false;\n\n      if (self._enterable) {\n        if (self._show) {\n          self.hideLater(self._hideDelay);\n        }\n      }\n    };\n  }\n  /**\r\n   * Update when tooltip is rendered\r\n   */\n\n\n  TooltipHTMLContent.prototype.update = function (tooltipModel) {\n    // FIXME\n    // Move this logic to ec main?\n    var container = this._container;\n    var position = getComputedStyle(container, 'position');\n    var domStyle = container.style;\n\n    if (domStyle.position !== 'absolute' && position !== 'absolute') {\n      domStyle.position = 'relative';\n    } // move tooltip if chart resized\n\n\n    var alwaysShowContent = tooltipModel.get('alwaysShowContent');\n    alwaysShowContent && this._moveIfResized(); // update alwaysShowContent\n\n    this._alwaysShowContent = alwaysShowContent; // update className\n\n    this.el.className = tooltipModel.get('className') || ''; // Hide the tooltip\n    // PENDING\n    // this.hide();\n  };\n\n  TooltipHTMLContent.prototype.show = function (tooltipModel, nearPointColor) {\n    clearTimeout(this._hideTimeout);\n    clearTimeout(this._longHideTimeout);\n    var el = this.el;\n    var style = el.style;\n    var styleCoord = this._styleCoord;\n\n    if (!el.innerHTML) {\n      style.display = 'none';\n    } else {\n      style.cssText = gCssText + assembleCssText(tooltipModel, !this._firstShow, this._longHide) // initial transform\n      + assembleTransform(styleCoord[0], styleCoord[1], true) + (\"border-color:\" + convertToColorString(nearPointColor) + \";\") + (tooltipModel.get('extraCssText') || '') // If mouse occasionally move over the tooltip, a mouseout event will be\n      // triggered by canvas, and cause some unexpectable result like dragging\n      // stop, \"unfocusAdjacency\". Here `pointer-events: none` is used to solve\n      // it. Although it is not supported by IE8~IE10, fortunately it is a rare\n      // scenario.\n      + (\";pointer-events:\" + (this._enterable ? 'auto' : 'none'));\n    }\n\n    this._show = true;\n    this._firstShow = false;\n    this._longHide = false;\n  };\n\n  TooltipHTMLContent.prototype.setContent = function (content, markers, tooltipModel, borderColor, arrowPosition) {\n    var el = this.el;\n\n    if (content == null) {\n      el.innerHTML = '';\n      return;\n    }\n\n    var arrow = '';\n\n    if (isString(arrowPosition) && tooltipModel.get('trigger') === 'item' && !shouldTooltipConfine(tooltipModel)) {\n      arrow = assembleArrow(tooltipModel, borderColor, arrowPosition);\n    }\n\n    if (isString(content)) {\n      el.innerHTML = content + arrow;\n    } else if (content) {\n      // Clear previous\n      el.innerHTML = '';\n\n      if (!isArray(content)) {\n        content = [content];\n      }\n\n      for (var i = 0; i < content.length; i++) {\n        if (isDom(content[i]) && content[i].parentNode !== el) {\n          el.appendChild(content[i]);\n        }\n      } // no arrow if empty\n\n\n      if (arrow && el.childNodes.length) {\n        // no need to create a new parent element, but it's not supported by IE 10 and older.\n        // const arrowEl = document.createRange().createContextualFragment(arrow);\n        var arrowEl = document.createElement('div');\n        arrowEl.innerHTML = arrow;\n        el.appendChild(arrowEl);\n      }\n    }\n  };\n\n  TooltipHTMLContent.prototype.setEnterable = function (enterable) {\n    this._enterable = enterable;\n  };\n\n  TooltipHTMLContent.prototype.getSize = function () {\n    var el = this.el;\n    return [el.offsetWidth, el.offsetHeight];\n  };\n\n  TooltipHTMLContent.prototype.moveTo = function (zrX, zrY) {\n    var styleCoord = this._styleCoord;\n    makeStyleCoord(styleCoord, this._zr, this._appendToBody, zrX, zrY);\n\n    if (styleCoord[0] != null && styleCoord[1] != null) {\n      var style_1 = this.el.style;\n      var transforms = assembleTransform(styleCoord[0], styleCoord[1]);\n      each(transforms, function (transform) {\n        style_1[transform[0]] = transform[1];\n      });\n    }\n  };\n  /**\r\n   * when `alwaysShowContent` is true,\r\n   * move the tooltip after chart resized\r\n   */\n\n\n  TooltipHTMLContent.prototype._moveIfResized = function () {\n    // The ratio of left to width\n    var ratioX = this._styleCoord[2]; // The ratio of top to height\n\n    var ratioY = this._styleCoord[3];\n    this.moveTo(ratioX * this._zr.getWidth(), ratioY * this._zr.getHeight());\n  };\n\n  TooltipHTMLContent.prototype.hide = function () {\n    var _this = this;\n\n    var style = this.el.style;\n    style.visibility = 'hidden';\n    style.opacity = '0';\n    env.transform3dSupported && (style.willChange = '');\n    this._show = false;\n    this._longHideTimeout = setTimeout(function () {\n      return _this._longHide = true;\n    }, 500);\n  };\n\n  TooltipHTMLContent.prototype.hideLater = function (time) {\n    if (this._show && !(this._inContent && this._enterable) && !this._alwaysShowContent) {\n      if (time) {\n        this._hideDelay = time; // Set show false to avoid invoke hideLater multiple times\n\n        this._show = false;\n        this._hideTimeout = setTimeout(bind(this.hide, this), time);\n      } else {\n        this.hide();\n      }\n    }\n  };\n\n  TooltipHTMLContent.prototype.isShow = function () {\n    return this._show;\n  };\n\n  TooltipHTMLContent.prototype.dispose = function () {\n    this.el.parentNode.removeChild(this.el);\n  };\n\n  return TooltipHTMLContent;\n}();\n\nexport default TooltipHTMLContent;"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,QAAQ,EAAEC,OAAO,EAAEC,IAAI,EAAEC,IAAI,EAAEC,OAAO,EAAEC,KAAK,QAAQ,0BAA0B;AACxF,SAASC,cAAc,QAAQ,2BAA2B;AAC1D,SAASC,mBAAmB,QAAQ,yBAAyB;AAC7D,OAAOC,GAAG,MAAM,yBAAyB;AACzC,SAASC,oBAAoB,EAAEC,WAAW,EAAEC,iBAAiB,QAAQ,sBAAsB;AAC3F,SAASC,oBAAoB,EAAEC,iBAAiB,EAAEC,gBAAgB,EAAEC,gBAAgB,EAAEC,iBAAiB,QAAQ,aAAa;AAC5H,SAASC,0BAA0B,QAAQ,oBAAoB;AAC/D;;AAEA,IAAIC,qBAAqB,GAAGL,iBAAiB,CAACG,iBAAiB,EAAE,YAAY,CAAC;AAC9E,IAAIG,oBAAoB,GAAGN,iBAAiB,CAACE,gBAAgB,EAAE,WAAW,CAAC,CAAC,CAAC;;AAE7E,IAAIK,QAAQ,GAAG,wFAAwF,IAAIZ,GAAG,CAACa,oBAAoB,GAAG,wBAAwB,GAAG,EAAE,CAAC;AAEpK,SAASC,SAASA,CAACC,GAAG,EAAE;EACtBA,GAAG,GAAGA,GAAG,KAAK,MAAM,GAAG,OAAO,GAAGA,GAAG,KAAK,OAAO,GAAG,MAAM,GAAGA,GAAG,KAAK,KAAK,GAAG,QAAQ,GAAG,KAAK;EAC5F,OAAOA,GAAG;AACZ;AAEA,SAASC,aAAaA,CAACC,YAAY,EAAEC,WAAW,EAAEC,aAAa,EAAE;EAC/D,IAAI,CAAC3B,QAAQ,CAAC2B,aAAa,CAAC,IAAIA,aAAa,KAAK,QAAQ,EAAE;IAC1D,OAAO,EAAE;EACX;EAEA,IAAIC,eAAe,GAAGH,YAAY,CAACI,GAAG,CAAC,iBAAiB,CAAC;EACzD,IAAIC,WAAW,GAAGL,YAAY,CAACI,GAAG,CAAC,aAAa,CAAC;EACjDH,WAAW,GAAGjB,oBAAoB,CAACiB,WAAW,CAAC;EAC/C,IAAIK,QAAQ,GAAGT,SAAS,CAACK,aAAa,CAAC;EACvC,IAAIK,SAAS,GAAGC,IAAI,CAACC,GAAG,CAACD,IAAI,CAACE,KAAK,CAACL,WAAW,CAAC,GAAG,GAAG,EAAE,CAAC,CAAC;EAC1D,IAAIM,aAAa,GAAG,EAAE;EACtB,IAAIC,cAAc,GAAGlB,oBAAoB,GAAG,GAAG;EAC/C,IAAImB,SAAS;EAEb,IAAIrC,OAAO,CAAC,CAAC,MAAM,EAAE,OAAO,CAAC,EAAE8B,QAAQ,CAAC,GAAG,CAAC,CAAC,EAAE;IAC7CK,aAAa,IAAI,SAAS;IAC1BC,cAAc,IAAI,0BAA0B,IAAIC,SAAS,GAAGP,QAAQ,KAAK,MAAM,GAAG,CAAC,GAAG,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM;EACxG,CAAC,MAAM;IACLK,aAAa,IAAI,UAAU;IAC3BC,cAAc,IAAI,0BAA0B,IAAIC,SAAS,GAAGP,QAAQ,KAAK,KAAK,GAAG,GAAG,GAAG,EAAE,CAAC,GAAG,MAAM;EACrG;EAEA,IAAIQ,YAAY,GAAGD,SAAS,GAAGL,IAAI,CAACO,EAAE,GAAG,GAAG;EAC5C,IAAIC,OAAO,GAAGT,SAAS,GAAGF,WAAW;EACrC,IAAIY,SAAS,GAAGD,OAAO,GAAGR,IAAI,CAACU,GAAG,CAACV,IAAI,CAACW,GAAG,CAACL,YAAY,CAAC,CAAC,GAAGE,OAAO,GAAGR,IAAI,CAACU,GAAG,CAACV,IAAI,CAACY,GAAG,CAACN,YAAY,CAAC,CAAC;EACvG,IAAIO,WAAW,GAAGb,IAAI,CAACE,KAAK,CAAC,CAAC,CAACO,SAAS,GAAGT,IAAI,CAACc,KAAK,GAAGjB,WAAW,IAAI,CAAC,GAAGG,IAAI,CAACc,KAAK,GAAGjB,WAAW,GAAG,CAACY,SAAS,GAAGD,OAAO,IAAI,CAAC,IAAI,GAAG,CAAC,GAAG,GAAG;EAC7IL,aAAa,IAAI,GAAG,GAAGL,QAAQ,GAAG,IAAI,GAAGe,WAAW,GAAG,IAAI;EAC3D,IAAIE,WAAW,GAAGtB,WAAW,GAAG,SAAS,GAAGI,WAAW,GAAG,KAAK;EAC/D,IAAImB,QAAQ,GAAG,CAAC,0BAA0B,GAAGjB,SAAS,GAAG,YAAY,GAAGA,SAAS,GAAG,gBAAgB,EAAEI,aAAa,GAAG,GAAG,GAAGC,cAAc,GAAG,GAAG,EAAE,gBAAgB,GAAGW,WAAW,EAAE,eAAe,GAAGA,WAAW,EAAE,mBAAmB,GAAGpB,eAAe,GAAG,GAAG,CAAC;EAC7P,OAAO,eAAe,GAAGqB,QAAQ,CAACC,IAAI,CAAC,EAAE,CAAC,GAAG,WAAW;AAC1D;AAEA,SAASC,kBAAkBA,CAACC,QAAQ,EAAEC,QAAQ,EAAE;EAC9C,IAAIC,eAAe,GAAG,6BAA6B;EACnD,IAAIC,gBAAgB,GAAG,GAAG,GAAGH,QAAQ,GAAG,CAAC,GAAG,IAAI,GAAGE,eAAe;EAClE,IAAIE,cAAc,GAAG,SAAS,GAAGD,gBAAgB,GAAG,aAAa,GAAGA,gBAAgB;EAEpF,IAAI,CAACF,QAAQ,EAAE;IACbE,gBAAgB,GAAG,GAAG,GAAGH,QAAQ,GAAG,IAAI,GAAGE,eAAe;IAC1DE,cAAc,IAAIhD,GAAG,CAACiD,kBAAkB,GAAG,GAAG,GAAGtC,oBAAoB,GAAGoC,gBAAgB,GAAG,OAAO,GAAGA,gBAAgB,GAAG,MAAM,GAAGA,gBAAgB;EACnJ;EAEA,OAAOrC,qBAAqB,GAAG,GAAG,GAAGsC,cAAc;AACrD;AAEA,SAASE,iBAAiBA,CAACC,CAAC,EAAEC,CAAC,EAAEC,QAAQ,EAAE;EACzC;EACA;EACA,IAAIC,EAAE,GAAGH,CAAC,CAACI,OAAO,CAAC,CAAC,CAAC,GAAG,IAAI;EAC5B,IAAIC,EAAE,GAAGJ,CAAC,CAACG,OAAO,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC;;EAE9B,IAAI,CAACvD,GAAG,CAACiD,kBAAkB,EAAE;IAC3B,OAAOI,QAAQ,GAAG,MAAM,GAAGG,EAAE,GAAG,QAAQ,GAAGF,EAAE,GAAG,GAAG,GAAG,CAAC,CAAC,KAAK,EAAEE,EAAE,CAAC,EAAE,CAAC,MAAM,EAAEF,EAAE,CAAC,CAAC;EACnF,CAAC,CAAC;;EAGF,IAAIG,IAAI,GAAGzD,GAAG,CAACa,oBAAoB;EACnC,IAAI6C,SAAS,GAAG,WAAW,IAAID,IAAI,GAAG,IAAI,GAAG,EAAE,CAAC,GAAG,GAAG,GAAGH,EAAE,GAAG,GAAG,GAAGE,EAAE,IAAIC,IAAI,GAAG,IAAI,GAAG,EAAE,CAAC,GAAG,GAAG;EACjG,OAAOJ,QAAQ,GAAG,eAAe,GAAG1C,oBAAoB,GAAG,GAAG,GAAG+C,SAAS,GAAG,GAAG,GAAG,CAAC,CAAC,KAAK,EAAE,CAAC,CAAC,EAAE,CAAC,MAAM,EAAE,CAAC,CAAC,EAAE,CAACnD,gBAAgB,EAAEmD,SAAS,CAAC,CAAC;AAC7I;AACA;AACA;AACA;AACA;AACA;;AAGA,SAASC,YAAYA,CAACC,cAAc,EAAE;EACpC,IAAIC,OAAO,GAAG,EAAE;EAChB,IAAIC,QAAQ,GAAGF,cAAc,CAACvC,GAAG,CAAC,UAAU,CAAC;EAC7C,IAAI0C,KAAK,GAAGH,cAAc,CAACI,YAAY,CAAC,CAAC;EACzCD,KAAK,IAAIF,OAAO,CAACI,IAAI,CAAC,QAAQ,GAAGF,KAAK,CAAC;EACvCF,OAAO,CAACI,IAAI,CAAC,OAAO,GAAGL,cAAc,CAACM,OAAO,CAAC,CAAC,CAAC;EAChDJ,QAAQ,CAAC;EAAA,GACND,OAAO,CAACI,IAAI,CAAC,cAAc,GAAGxC,IAAI,CAACE,KAAK,CAACmC,QAAQ,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC;EACrE,IAAIK,WAAW,GAAGP,cAAc,CAACvC,GAAG,CAAC,iBAAiB,CAAC;EACvD,IAAI+C,UAAU,GAAGR,cAAc,CAACvC,GAAG,CAAC,gBAAgB,CAAC,IAAI,CAAC;EAC1D,IAAIgD,aAAa,GAAGT,cAAc,CAACvC,GAAG,CAAC,mBAAmB,CAAC,IAAI,CAAC;EAChE,IAAIiD,aAAa,GAAGV,cAAc,CAACvC,GAAG,CAAC,mBAAmB,CAAC,IAAI,CAAC;EAChE8C,WAAW,IAAIC,UAAU,IAAIP,OAAO,CAACI,IAAI,CAAC,cAAc,GAAGI,aAAa,GAAG,KAAK,GAAGC,aAAa,GAAG,KAAK,GAAGF,UAAU,GAAG,KAAK,GAAGD,WAAW,CAAC;EAC5IzE,IAAI,CAAC,CAAC,YAAY,EAAE,OAAO,CAAC,EAAE,UAAU6E,IAAI,EAAE;IAC5C,IAAIC,GAAG,GAAGZ,cAAc,CAACvC,GAAG,CAACkD,IAAI,CAAC;IAClCC,GAAG,IAAIX,OAAO,CAACI,IAAI,CAAC,OAAO,GAAGM,IAAI,GAAG,GAAG,GAAGC,GAAG,CAAC;EACjD,CAAC,CAAC;EACF,OAAOX,OAAO,CAACnB,IAAI,CAAC,GAAG,CAAC;AAC1B;AAEA,SAAS+B,eAAeA,CAACxD,YAAY,EAAEyD,gBAAgB,EAAE7B,QAAQ,EAAE;EACjE,IAAIgB,OAAO,GAAG,EAAE;EAChB,IAAIc,kBAAkB,GAAG1D,YAAY,CAACI,GAAG,CAAC,oBAAoB,CAAC;EAC/D,IAAID,eAAe,GAAGH,YAAY,CAACI,GAAG,CAAC,iBAAiB,CAAC;EACzD,IAAI+C,UAAU,GAAGnD,YAAY,CAACI,GAAG,CAAC,YAAY,CAAC;EAC/C,IAAI8C,WAAW,GAAGlD,YAAY,CAACI,GAAG,CAAC,aAAa,CAAC;EACjD,IAAIgD,aAAa,GAAGpD,YAAY,CAACI,GAAG,CAAC,eAAe,CAAC;EACrD,IAAIiD,aAAa,GAAGrD,YAAY,CAACI,GAAG,CAAC,eAAe,CAAC;EACrD,IAAIuC,cAAc,GAAG3C,YAAY,CAAC2D,QAAQ,CAAC,WAAW,CAAC;EACvD,IAAIC,OAAO,GAAGpE,0BAA0B,CAACQ,YAAY,EAAE,MAAM,CAAC;EAC9D,IAAI6D,SAAS,GAAGT,aAAa,GAAG,KAAK,GAAGC,aAAa,GAAG,KAAK,GAAGF,UAAU,GAAG,KAAK,GAAGD,WAAW;EAChGN,OAAO,CAACI,IAAI,CAAC,aAAa,GAAGa,SAAS,CAAC,CAAC,CAAC;;EAEzCJ,gBAAgB,IAAIC,kBAAkB,IAAId,OAAO,CAACI,IAAI,CAACtB,kBAAkB,CAACgC,kBAAkB,EAAE9B,QAAQ,CAAC,CAAC;EAExG,IAAIzB,eAAe,EAAE;IACnByC,OAAO,CAACI,IAAI,CAAC,mBAAmB,GAAG7C,eAAe,CAAC;EACrD,CAAC,CAAC;;EAGF1B,IAAI,CAAC,CAAC,OAAO,EAAE,OAAO,EAAE,QAAQ,CAAC,EAAE,UAAU6E,IAAI,EAAE;IACjD,IAAIQ,UAAU,GAAG,SAAS,GAAGR,IAAI;IACjC,IAAIS,SAAS,GAAG9E,WAAW,CAAC6E,UAAU,CAAC;IACvC,IAAIP,GAAG,GAAGvD,YAAY,CAACI,GAAG,CAAC2D,SAAS,CAAC;IACrCR,GAAG,IAAI,IAAI,IAAIX,OAAO,CAACI,IAAI,CAACc,UAAU,GAAG,GAAG,GAAGP,GAAG,IAAID,IAAI,KAAK,OAAO,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC;EACtF,CAAC,CAAC,CAAC,CAAC;;EAEJV,OAAO,CAACI,IAAI,CAACN,YAAY,CAACC,cAAc,CAAC,CAAC,CAAC,CAAC;;EAE5C,IAAIiB,OAAO,IAAI,IAAI,EAAE;IACnBhB,OAAO,CAACI,IAAI,CAAC,UAAU,GAAG9D,iBAAiB,CAAC0E,OAAO,CAAC,CAACnC,IAAI,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC;EAC1E;EAEA,OAAOmB,OAAO,CAACnB,IAAI,CAAC,GAAG,CAAC,GAAG,GAAG;AAChC,CAAC,CAAC;;AAGF,SAASuC,cAAcA,CAACC,GAAG,EAAEC,EAAE,EAAEC,YAAY,EAAEC,GAAG,EAAEC,GAAG,EAAE;EACvD,IAAIC,SAAS,GAAGJ,EAAE,IAAIA,EAAE,CAACK,OAAO;EAEhC,IAAIJ,YAAY,EAAE;IAChB,IAAIK,cAAc,GAAGF,SAAS,IAAIA,SAAS,CAACG,eAAe,CAAC,CAAC;IAE7D,IAAID,cAAc,EAAE;MAClB;MACA1F,mBAAmB,CAACmF,GAAG,EAAEO,cAAc,EAAEE,QAAQ,CAACC,IAAI,EAAEP,GAAG,EAAEC,GAAG,CAAC;IACnE;EACF,CAAC,MAAM;IACLJ,GAAG,CAAC,CAAC,CAAC,GAAGG,GAAG;IACZH,GAAG,CAAC,CAAC,CAAC,GAAGI,GAAG,CAAC,CAAC;IACd;IACA;;IAEA,IAAIO,kBAAkB,GAAGN,SAAS,IAAIA,SAAS,CAACO,qBAAqB,CAAC,CAAC;IAEvE,IAAID,kBAAkB,EAAE;MACtBX,GAAG,CAAC,CAAC,CAAC,IAAIW,kBAAkB,CAACE,UAAU;MACvCb,GAAG,CAAC,CAAC,CAAC,IAAIW,kBAAkB,CAACG,SAAS;IACxC;EACF;EAEAd,GAAG,CAAC,CAAC,CAAC,GAAGA,GAAG,CAAC,CAAC,CAAC,GAAGC,EAAE,CAACc,QAAQ,CAAC,CAAC;EAC/Bf,GAAG,CAAC,CAAC,CAAC,GAAGA,GAAG,CAAC,CAAC,CAAC,GAAGC,EAAE,CAACe,SAAS,CAAC,CAAC;AAClC;AAEA,IAAIC,kBAAkB,GACtB;AACA,YAAY;EACV,SAASA,kBAAkBA,CAACC,SAAS,EAAEC,GAAG,EAAEC,GAAG,EAAE;IAC/C,IAAI,CAACC,KAAK,GAAG,KAAK;IAClB,IAAI,CAACC,WAAW,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IAC/B,IAAI,CAACC,UAAU,GAAG,IAAI;IACtB,IAAI,CAACC,kBAAkB,GAAG,KAAK;IAC/B,IAAI,CAACC,UAAU,GAAG,IAAI;IACtB,IAAI,CAACC,SAAS,GAAG,IAAI;IAErB,IAAI5G,GAAG,CAAC6G,GAAG,EAAE;MACX,OAAO,IAAI;IACb;IAEA,IAAIC,EAAE,GAAGnB,QAAQ,CAACoB,aAAa,CAAC,KAAK,CAAC,CAAC,CAAC;;IAExCD,EAAE,CAACE,aAAa,GAAG,IAAI;IACvB,IAAI,CAACF,EAAE,GAAGA,EAAE;IACZ,IAAI3B,EAAE,GAAG,IAAI,CAAC8B,GAAG,GAAGZ,GAAG,CAACa,KAAK,CAAC,CAAC;IAC/B,IAAI9B,YAAY,GAAG,IAAI,CAAC+B,aAAa,GAAGb,GAAG,IAAIA,GAAG,CAAClB,YAAY;IAC/DH,cAAc,CAAC,IAAI,CAACuB,WAAW,EAAErB,EAAE,EAAEC,YAAY,EAAEiB,GAAG,CAACJ,QAAQ,CAAC,CAAC,GAAG,CAAC,EAAEI,GAAG,CAACH,SAAS,CAAC,CAAC,GAAG,CAAC,CAAC;IAE3F,IAAId,YAAY,EAAE;MAChBO,QAAQ,CAACC,IAAI,CAACwB,WAAW,CAACN,EAAE,CAAC;IAC/B,CAAC,MAAM;MACLV,SAAS,CAACgB,WAAW,CAACN,EAAE,CAAC;IAC3B;IAEA,IAAI,CAACO,UAAU,GAAGjB,SAAS,CAAC,CAAC;IAC7B;IACA;;IAEA,IAAIkB,IAAI,GAAG,IAAI;IAEfR,EAAE,CAACS,YAAY,GAAG,YAAY;MAC5B;MACA,IAAID,IAAI,CAACb,UAAU,EAAE;QACnBe,YAAY,CAACF,IAAI,CAACG,YAAY,CAAC;QAC/BH,IAAI,CAACf,KAAK,GAAG,IAAI;MACnB;MAEAe,IAAI,CAACI,UAAU,GAAG,IAAI;IACxB,CAAC;IAEDZ,EAAE,CAACa,WAAW,GAAG,UAAUC,CAAC,EAAE;MAC5BA,CAAC,GAAGA,CAAC,IAAIC,MAAM,CAACC,KAAK;MAErB,IAAI,CAACR,IAAI,CAACb,UAAU,EAAE;QACpB;QACA;QACA;QACA;QACA;QACA;QACA,IAAIsB,OAAO,GAAG5C,EAAE,CAAC4C,OAAO;QACxB,IAAItC,cAAc,GAAGN,EAAE,CAACK,OAAO,CAACE,eAAe,CAAC,CAAC;QACjD5F,cAAc,CAAC2F,cAAc,EAAEmC,CAAC,EAAE,IAAI,CAAC;QACvCG,OAAO,CAACC,QAAQ,CAAC,WAAW,EAAEJ,CAAC,CAAC;MAClC;IACF,CAAC;IAEDd,EAAE,CAACmB,YAAY,GAAG,YAAY;MAC5B;MACAX,IAAI,CAACI,UAAU,GAAG,KAAK;MAEvB,IAAIJ,IAAI,CAACb,UAAU,EAAE;QACnB,IAAIa,IAAI,CAACf,KAAK,EAAE;UACde,IAAI,CAACY,SAAS,CAACZ,IAAI,CAACa,UAAU,CAAC;QACjC;MACF;IACF,CAAC;EACH;EACA;AACF;AACA;;EAGEhC,kBAAkB,CAACiC,SAAS,CAACC,MAAM,GAAG,UAAUpH,YAAY,EAAE;IAC5D;IACA;IACA,IAAImF,SAAS,GAAG,IAAI,CAACiB,UAAU;IAC/B,IAAIiB,QAAQ,GAAGhI,gBAAgB,CAAC8F,SAAS,EAAE,UAAU,CAAC;IACtD,IAAImC,QAAQ,GAAGnC,SAAS,CAACoC,KAAK;IAE9B,IAAID,QAAQ,CAACD,QAAQ,KAAK,UAAU,IAAIA,QAAQ,KAAK,UAAU,EAAE;MAC/DC,QAAQ,CAACD,QAAQ,GAAG,UAAU;IAChC,CAAC,CAAC;;IAGF,IAAIG,iBAAiB,GAAGxH,YAAY,CAACI,GAAG,CAAC,mBAAmB,CAAC;IAC7DoH,iBAAiB,IAAI,IAAI,CAACC,cAAc,CAAC,CAAC,CAAC,CAAC;;IAE5C,IAAI,CAAChC,kBAAkB,GAAG+B,iBAAiB,CAAC,CAAC;;IAE7C,IAAI,CAAC3B,EAAE,CAAC6B,SAAS,GAAG1H,YAAY,CAACI,GAAG,CAAC,WAAW,CAAC,IAAI,EAAE,CAAC,CAAC;IACzD;IACA;EACF,CAAC;;EAED8E,kBAAkB,CAACiC,SAAS,CAACQ,IAAI,GAAG,UAAU3H,YAAY,EAAE4H,cAAc,EAAE;IAC1ErB,YAAY,CAAC,IAAI,CAACC,YAAY,CAAC;IAC/BD,YAAY,CAAC,IAAI,CAACsB,gBAAgB,CAAC;IACnC,IAAIhC,EAAE,GAAG,IAAI,CAACA,EAAE;IAChB,IAAI0B,KAAK,GAAG1B,EAAE,CAAC0B,KAAK;IACpB,IAAIO,UAAU,GAAG,IAAI,CAACvC,WAAW;IAEjC,IAAI,CAACM,EAAE,CAACkC,SAAS,EAAE;MACjBR,KAAK,CAACS,OAAO,GAAG,MAAM;IACxB,CAAC,MAAM;MACLT,KAAK,CAAC3E,OAAO,GAAGjD,QAAQ,GAAG6D,eAAe,CAACxD,YAAY,EAAE,CAAC,IAAI,CAAC0F,UAAU,EAAE,IAAI,CAACC,SAAS,CAAC,CAAC;MAAA,EACzF1D,iBAAiB,CAAC6F,UAAU,CAAC,CAAC,CAAC,EAAEA,UAAU,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,IAAI,eAAe,GAAG9I,oBAAoB,CAAC4I,cAAc,CAAC,GAAG,GAAG,CAAC,IAAI5H,YAAY,CAACI,GAAG,CAAC,cAAc,CAAC,IAAI,EAAE,CAAC,CAAC;MACpK;MACA;MACA;MACA;MAAA,GACG,kBAAkB,IAAI,IAAI,CAACoF,UAAU,GAAG,MAAM,GAAG,MAAM,CAAC,CAAC;IAC9D;IAEA,IAAI,CAACF,KAAK,GAAG,IAAI;IACjB,IAAI,CAACI,UAAU,GAAG,KAAK;IACvB,IAAI,CAACC,SAAS,GAAG,KAAK;EACxB,CAAC;EAEDT,kBAAkB,CAACiC,SAAS,CAACc,UAAU,GAAG,UAAUC,OAAO,EAAEC,OAAO,EAAEnI,YAAY,EAAEC,WAAW,EAAEC,aAAa,EAAE;IAC9G,IAAI2F,EAAE,GAAG,IAAI,CAACA,EAAE;IAEhB,IAAIqC,OAAO,IAAI,IAAI,EAAE;MACnBrC,EAAE,CAACkC,SAAS,GAAG,EAAE;MACjB;IACF;IAEA,IAAIK,KAAK,GAAG,EAAE;IAEd,IAAI7J,QAAQ,CAAC2B,aAAa,CAAC,IAAIF,YAAY,CAACI,GAAG,CAAC,SAAS,CAAC,KAAK,MAAM,IAAI,CAACjB,oBAAoB,CAACa,YAAY,CAAC,EAAE;MAC5GoI,KAAK,GAAGrI,aAAa,CAACC,YAAY,EAAEC,WAAW,EAAEC,aAAa,CAAC;IACjE;IAEA,IAAI3B,QAAQ,CAAC2J,OAAO,CAAC,EAAE;MACrBrC,EAAE,CAACkC,SAAS,GAAGG,OAAO,GAAGE,KAAK;IAChC,CAAC,MAAM,IAAIF,OAAO,EAAE;MAClB;MACArC,EAAE,CAACkC,SAAS,GAAG,EAAE;MAEjB,IAAI,CAACpJ,OAAO,CAACuJ,OAAO,CAAC,EAAE;QACrBA,OAAO,GAAG,CAACA,OAAO,CAAC;MACrB;MAEA,KAAK,IAAIG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,OAAO,CAACI,MAAM,EAAED,CAAC,EAAE,EAAE;QACvC,IAAIzJ,KAAK,CAACsJ,OAAO,CAACG,CAAC,CAAC,CAAC,IAAIH,OAAO,CAACG,CAAC,CAAC,CAACE,UAAU,KAAK1C,EAAE,EAAE;UACrDA,EAAE,CAACM,WAAW,CAAC+B,OAAO,CAACG,CAAC,CAAC,CAAC;QAC5B;MACF,CAAC,CAAC;;MAGF,IAAID,KAAK,IAAIvC,EAAE,CAAC2C,UAAU,CAACF,MAAM,EAAE;QACjC;QACA;QACA,IAAIG,OAAO,GAAG/D,QAAQ,CAACoB,aAAa,CAAC,KAAK,CAAC;QAC3C2C,OAAO,CAACV,SAAS,GAAGK,KAAK;QACzBvC,EAAE,CAACM,WAAW,CAACsC,OAAO,CAAC;MACzB;IACF;EACF,CAAC;EAEDvD,kBAAkB,CAACiC,SAAS,CAACuB,YAAY,GAAG,UAAUC,SAAS,EAAE;IAC/D,IAAI,CAACnD,UAAU,GAAGmD,SAAS;EAC7B,CAAC;EAEDzD,kBAAkB,CAACiC,SAAS,CAACyB,OAAO,GAAG,YAAY;IACjD,IAAI/C,EAAE,GAAG,IAAI,CAACA,EAAE;IAChB,OAAO,CAACA,EAAE,CAACgD,WAAW,EAAEhD,EAAE,CAACiD,YAAY,CAAC;EAC1C,CAAC;EAED5D,kBAAkB,CAACiC,SAAS,CAAC4B,MAAM,GAAG,UAAU3E,GAAG,EAAEC,GAAG,EAAE;IACxD,IAAIyD,UAAU,GAAG,IAAI,CAACvC,WAAW;IACjCvB,cAAc,CAAC8D,UAAU,EAAE,IAAI,CAAC9B,GAAG,EAAE,IAAI,CAACE,aAAa,EAAE9B,GAAG,EAAEC,GAAG,CAAC;IAElE,IAAIyD,UAAU,CAAC,CAAC,CAAC,IAAI,IAAI,IAAIA,UAAU,CAAC,CAAC,CAAC,IAAI,IAAI,EAAE;MAClD,IAAIkB,OAAO,GAAG,IAAI,CAACnD,EAAE,CAAC0B,KAAK;MAC3B,IAAI0B,UAAU,GAAGhH,iBAAiB,CAAC6F,UAAU,CAAC,CAAC,CAAC,EAAEA,UAAU,CAAC,CAAC,CAAC,CAAC;MAChErJ,IAAI,CAACwK,UAAU,EAAE,UAAUC,SAAS,EAAE;QACpCF,OAAO,CAACE,SAAS,CAAC,CAAC,CAAC,CAAC,GAAGA,SAAS,CAAC,CAAC,CAAC;MACtC,CAAC,CAAC;IACJ;EACF,CAAC;EACD;AACF;AACA;AACA;;EAGEhE,kBAAkB,CAACiC,SAAS,CAACM,cAAc,GAAG,YAAY;IACxD;IACA,IAAI0B,MAAM,GAAG,IAAI,CAAC5D,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC;;IAElC,IAAI6D,MAAM,GAAG,IAAI,CAAC7D,WAAW,CAAC,CAAC,CAAC;IAChC,IAAI,CAACwD,MAAM,CAACI,MAAM,GAAG,IAAI,CAACnD,GAAG,CAAChB,QAAQ,CAAC,CAAC,EAAEoE,MAAM,GAAG,IAAI,CAACpD,GAAG,CAACf,SAAS,CAAC,CAAC,CAAC;EAC1E,CAAC;EAEDC,kBAAkB,CAACiC,SAAS,CAACkC,IAAI,GAAG,YAAY;IAC9C,IAAIC,KAAK,GAAG,IAAI;IAEhB,IAAI/B,KAAK,GAAG,IAAI,CAAC1B,EAAE,CAAC0B,KAAK;IACzBA,KAAK,CAACgC,UAAU,GAAG,QAAQ;IAC3BhC,KAAK,CAACiC,OAAO,GAAG,GAAG;IACnBzK,GAAG,CAACa,oBAAoB,KAAK2H,KAAK,CAACkC,UAAU,GAAG,EAAE,CAAC;IACnD,IAAI,CAACnE,KAAK,GAAG,KAAK;IAClB,IAAI,CAACuC,gBAAgB,GAAG6B,UAAU,CAAC,YAAY;MAC7C,OAAOJ,KAAK,CAAC3D,SAAS,GAAG,IAAI;IAC/B,CAAC,EAAE,GAAG,CAAC;EACT,CAAC;EAEDT,kBAAkB,CAACiC,SAAS,CAACF,SAAS,GAAG,UAAU0C,IAAI,EAAE;IACvD,IAAI,IAAI,CAACrE,KAAK,IAAI,EAAE,IAAI,CAACmB,UAAU,IAAI,IAAI,CAACjB,UAAU,CAAC,IAAI,CAAC,IAAI,CAACC,kBAAkB,EAAE;MACnF,IAAIkE,IAAI,EAAE;QACR,IAAI,CAACzC,UAAU,GAAGyC,IAAI,CAAC,CAAC;;QAExB,IAAI,CAACrE,KAAK,GAAG,KAAK;QAClB,IAAI,CAACkB,YAAY,GAAGkD,UAAU,CAAChL,IAAI,CAAC,IAAI,CAAC2K,IAAI,EAAE,IAAI,CAAC,EAAEM,IAAI,CAAC;MAC7D,CAAC,MAAM;QACL,IAAI,CAACN,IAAI,CAAC,CAAC;MACb;IACF;EACF,CAAC;EAEDnE,kBAAkB,CAACiC,SAAS,CAACyC,MAAM,GAAG,YAAY;IAChD,OAAO,IAAI,CAACtE,KAAK;EACnB,CAAC;EAEDJ,kBAAkB,CAACiC,SAAS,CAAC0C,OAAO,GAAG,YAAY;IACjD,IAAI,CAAChE,EAAE,CAAC0C,UAAU,CAACuB,WAAW,CAAC,IAAI,CAACjE,EAAE,CAAC;EACzC,CAAC;EAED,OAAOX,kBAAkB;AAC3B,CAAC,CAAC,CAAC;AAEH,eAAeA,kBAAkB"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}