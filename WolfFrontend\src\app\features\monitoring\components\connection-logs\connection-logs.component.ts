import { Component, Input, OnChanges, SimpleChanges, ElementRef, ViewChild, AfterViewInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ConnectionLog } from '../../monitoring.component';

@Component({
  selector: 'app-connection-logs',
  standalone: true,
  imports: [CommonModule],
  template: `
    <div class="logs-container">
      <div class="logs-header">
        <div class="logs-count">
          <span class="count">{{ logs.length }}</span>
          <span class="label">logs recentes</span>
        </div>
        <div class="auto-scroll-toggle" (click)="toggleAutoScroll()">
          <span class="toggle-icon" [class.active]="autoScroll">📜</span>
          <span class="toggle-text">Auto Scroll</span>
        </div>
      </div>
      
      <div class="logs-list" #logsList>
        <div 
          *ngFor="let log of logs; trackBy: trackByLog; let i = index" 
          class="log-entry"
          [class.success]="log.level === 'success'"
          [class.warning]="log.level === 'warning'"
          [class.error]="log.level === 'error'"
          [class.info]="log.level === 'info'"
          [class.repeated]="log.isRepeated"
          [class.new]="i === 0"
        >
          <div class="log-time">{{ log.timestamp }}</div>
          <div class="log-flag">{{ getCountryFlag(log.countryCode) }}</div>
          <div class="log-content">
            <div class="log-main">
              <span class="log-country">{{ log.country }}</span>
              <span class="log-ip">({{ log.ip }})</span>
              <span class="log-action">{{ getActionText(log) }}</span>
              <span class="log-page">{{ log.page }}</span>
            </div>
            <div class="log-details" *ngIf="log.accessCount > 1">
              <span class="access-count">{{ log.accessCount }}ª tentativa</span>
              <span class="repeated-badge" *ngIf="log.isRepeated">REPETIDO</span>
            </div>
          </div>
          <div class="log-indicator" [class]="log.level"></div>
        </div>
      </div>
      
      <div class="empty-state" *ngIf="logs.length === 0">
        <div class="empty-icon">📝</div>
        <div class="empty-text">Aguardando logs...</div>
      </div>
    </div>
  `,
  styleUrls: ['./connection-logs.component.scss']
})
export class ConnectionLogsComponent implements OnChanges, AfterViewInit {
  @Input() logs: ConnectionLog[] = [];
  @ViewChild('logsList') logsList!: ElementRef;
  
  autoScroll = true;
  private previousLogsLength = 0;

  ngAfterViewInit() {
    this.scrollToBottom();
  }

  ngOnChanges(changes: SimpleChanges) {
    if (changes['logs'] && this.logs.length > this.previousLogsLength) {
      this.previousLogsLength = this.logs.length;
      
      if (this.autoScroll) {
        setTimeout(() => this.scrollToBottom(), 100);
      }
    }
  }

  trackByLog(index: number, log: ConnectionLog): string {
    return `${log.timestamp}-${log.ip}-${log.accessCount}`;
  }

  getCountryFlag(countryCode: string): string {
    const flags: { [key: string]: string } = {
      'BR': '🇧🇷',
      'US': '🇺🇸', 
      'DE': '🇩🇪',
      'FR': '🇫🇷',
      'GB': '🇬🇧',
      'CN': '🇨🇳',
      'JP': '🇯🇵',
      'RU': '🇷🇺',
      'CA': '🇨🇦',
      'AU': '🇦🇺',
      'IT': '🇮🇹',
      'ES': '🇪🇸',
      'NL': '🇳🇱',
      'IN': '🇮🇳',
      'MX': '🇲🇽',
      'AR': '🇦🇷',
      'LO': '🏠'
    };
    return flags[countryCode] || '🌍';
  }

  getActionText(log: ConnectionLog): string {
    if (log.message.includes('Nova conexão')) {
      return 'conectou em';
    } else if (log.message.includes('encerrada')) {
      return 'desconectou de';
    } else if (log.message.includes('múltiplas')) {
      return 'múltiplas tentativas em';
    } else {
      return 'acessou';
    }
  }

  toggleAutoScroll() {
    this.autoScroll = !this.autoScroll;
    if (this.autoScroll) {
      this.scrollToBottom();
    }
  }

  private scrollToBottom() {
    if (this.logsList) {
      const element = this.logsList.nativeElement;
      element.scrollTop = element.scrollHeight;
    }
  }
}
