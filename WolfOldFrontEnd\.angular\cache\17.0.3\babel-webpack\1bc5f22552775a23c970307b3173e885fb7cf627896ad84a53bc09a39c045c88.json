{"ast": null, "code": "import { quadraticProjectPoint } from '../core/curve.js';\nexport function containStroke(x0, y0, x1, y1, x2, y2, lineWidth, x, y) {\n  if (lineWidth === 0) {\n    return false;\n  }\n  var _l = lineWidth;\n  if (y > y0 + _l && y > y1 + _l && y > y2 + _l || y < y0 - _l && y < y1 - _l && y < y2 - _l || x > x0 + _l && x > x1 + _l && x > x2 + _l || x < x0 - _l && x < x1 - _l && x < x2 - _l) {\n    return false;\n  }\n  var d = quadraticProjectPoint(x0, y0, x1, y1, x2, y2, x, y, null);\n  return d <= _l / 2;\n}", "map": {"version": 3, "names": ["quadraticProjectPoint", "containStroke", "x0", "y0", "x1", "y1", "x2", "y2", "lineWidth", "x", "y", "_l", "d"], "sources": ["C:/WolfSystemV2/WolfOldFrontEnd/node_modules/zrender/lib/contain/quadratic.js"], "sourcesContent": ["import { quadraticProjectPoint } from '../core/curve.js';\nexport function containStroke(x0, y0, x1, y1, x2, y2, lineWidth, x, y) {\n    if (lineWidth === 0) {\n        return false;\n    }\n    var _l = lineWidth;\n    if ((y > y0 + _l && y > y1 + _l && y > y2 + _l)\n        || (y < y0 - _l && y < y1 - _l && y < y2 - _l)\n        || (x > x0 + _l && x > x1 + _l && x > x2 + _l)\n        || (x < x0 - _l && x < x1 - _l && x < x2 - _l)) {\n        return false;\n    }\n    var d = quadraticProjectPoint(x0, y0, x1, y1, x2, y2, x, y, null);\n    return d <= _l / 2;\n}\n"], "mappings": "AAAA,SAASA,qBAAqB,QAAQ,kBAAkB;AACxD,OAAO,SAASC,aAAaA,CAACC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,SAAS,EAAEC,CAAC,EAAEC,CAAC,EAAE;EACnE,IAAIF,SAAS,KAAK,CAAC,EAAE;IACjB,OAAO,KAAK;EAChB;EACA,IAAIG,EAAE,GAAGH,SAAS;EAClB,IAAKE,CAAC,GAAGP,EAAE,GAAGQ,EAAE,IAAID,CAAC,GAAGL,EAAE,GAAGM,EAAE,IAAID,CAAC,GAAGH,EAAE,GAAGI,EAAE,IACtCD,CAAC,GAAGP,EAAE,GAAGQ,EAAE,IAAID,CAAC,GAAGL,EAAE,GAAGM,EAAE,IAAID,CAAC,GAAGH,EAAE,GAAGI,EAAG,IAC1CF,CAAC,GAAGP,EAAE,GAAGS,EAAE,IAAIF,CAAC,GAAGL,EAAE,GAAGO,EAAE,IAAIF,CAAC,GAAGH,EAAE,GAAGK,EAAG,IAC1CF,CAAC,GAAGP,EAAE,GAAGS,EAAE,IAAIF,CAAC,GAAGL,EAAE,GAAGO,EAAE,IAAIF,CAAC,GAAGH,EAAE,GAAGK,EAAG,EAAE;IAChD,OAAO,KAAK;EAChB;EACA,IAAIC,CAAC,GAAGZ,qBAAqB,CAACE,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEE,CAAC,EAAEC,CAAC,EAAE,IAAI,CAAC;EACjE,OAAOE,CAAC,IAAID,EAAE,GAAG,CAAC;AACtB"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}