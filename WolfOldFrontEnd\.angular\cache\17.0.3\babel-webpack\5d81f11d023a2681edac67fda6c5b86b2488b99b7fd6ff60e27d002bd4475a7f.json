{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport { parsePercent } from '../../util/number.js';\nvar each = zrUtil.each;\nexport default function boxplotLayout(ecModel) {\n  var groupResult = groupSeriesByAxis(ecModel);\n  each(groupResult, function (groupItem) {\n    var seriesModels = groupItem.seriesModels;\n    if (!seriesModels.length) {\n      return;\n    }\n    calculateBase(groupItem);\n    each(seriesModels, function (seriesModel, idx) {\n      layoutSingleSeries(seriesModel, groupItem.boxOffsetList[idx], groupItem.boxWidthList[idx]);\n    });\n  });\n}\n/**\r\n * Group series by axis.\r\n */\n\nfunction groupSeriesByAxis(ecModel) {\n  var result = [];\n  var axisList = [];\n  ecModel.eachSeriesByType('boxplot', function (seriesModel) {\n    var baseAxis = seriesModel.getBaseAxis();\n    var idx = zrUtil.indexOf(axisList, baseAxis);\n    if (idx < 0) {\n      idx = axisList.length;\n      axisList[idx] = baseAxis;\n      result[idx] = {\n        axis: baseAxis,\n        seriesModels: []\n      };\n    }\n    result[idx].seriesModels.push(seriesModel);\n  });\n  return result;\n}\n/**\r\n * Calculate offset and box width for each series.\r\n */\n\nfunction calculateBase(groupItem) {\n  var baseAxis = groupItem.axis;\n  var seriesModels = groupItem.seriesModels;\n  var seriesCount = seriesModels.length;\n  var boxWidthList = groupItem.boxWidthList = [];\n  var boxOffsetList = groupItem.boxOffsetList = [];\n  var boundList = [];\n  var bandWidth;\n  if (baseAxis.type === 'category') {\n    bandWidth = baseAxis.getBandWidth();\n  } else {\n    var maxDataCount_1 = 0;\n    each(seriesModels, function (seriesModel) {\n      maxDataCount_1 = Math.max(maxDataCount_1, seriesModel.getData().count());\n    });\n    var extent = baseAxis.getExtent();\n    bandWidth = Math.abs(extent[1] - extent[0]) / maxDataCount_1;\n  }\n  each(seriesModels, function (seriesModel) {\n    var boxWidthBound = seriesModel.get('boxWidth');\n    if (!zrUtil.isArray(boxWidthBound)) {\n      boxWidthBound = [boxWidthBound, boxWidthBound];\n    }\n    boundList.push([parsePercent(boxWidthBound[0], bandWidth) || 0, parsePercent(boxWidthBound[1], bandWidth) || 0]);\n  });\n  var availableWidth = bandWidth * 0.8 - 2;\n  var boxGap = availableWidth / seriesCount * 0.3;\n  var boxWidth = (availableWidth - boxGap * (seriesCount - 1)) / seriesCount;\n  var base = boxWidth / 2 - availableWidth / 2;\n  each(seriesModels, function (seriesModel, idx) {\n    boxOffsetList.push(base);\n    base += boxGap + boxWidth;\n    boxWidthList.push(Math.min(Math.max(boxWidth, boundList[idx][0]), boundList[idx][1]));\n  });\n}\n/**\r\n * Calculate points location for each series.\r\n */\n\nfunction layoutSingleSeries(seriesModel, offset, boxWidth) {\n  var coordSys = seriesModel.coordinateSystem;\n  var data = seriesModel.getData();\n  var halfWidth = boxWidth / 2;\n  var cDimIdx = seriesModel.get('layout') === 'horizontal' ? 0 : 1;\n  var vDimIdx = 1 - cDimIdx;\n  var coordDims = ['x', 'y'];\n  var cDim = data.mapDimension(coordDims[cDimIdx]);\n  var vDims = data.mapDimensionsAll(coordDims[vDimIdx]);\n  if (cDim == null || vDims.length < 5) {\n    return;\n  }\n  for (var dataIndex = 0; dataIndex < data.count(); dataIndex++) {\n    var axisDimVal = data.get(cDim, dataIndex);\n    var median = getPoint(axisDimVal, vDims[2], dataIndex);\n    var end1 = getPoint(axisDimVal, vDims[0], dataIndex);\n    var end2 = getPoint(axisDimVal, vDims[1], dataIndex);\n    var end4 = getPoint(axisDimVal, vDims[3], dataIndex);\n    var end5 = getPoint(axisDimVal, vDims[4], dataIndex);\n    var ends = [];\n    addBodyEnd(ends, end2, false);\n    addBodyEnd(ends, end4, true);\n    ends.push(end1, end2, end5, end4);\n    layEndLine(ends, end1);\n    layEndLine(ends, end5);\n    layEndLine(ends, median);\n    data.setItemLayout(dataIndex, {\n      initBaseline: median[vDimIdx],\n      ends: ends\n    });\n  }\n  function getPoint(axisDimVal, dim, dataIndex) {\n    var val = data.get(dim, dataIndex);\n    var p = [];\n    p[cDimIdx] = axisDimVal;\n    p[vDimIdx] = val;\n    var point;\n    if (isNaN(axisDimVal) || isNaN(val)) {\n      point = [NaN, NaN];\n    } else {\n      point = coordSys.dataToPoint(p);\n      point[cDimIdx] += offset;\n    }\n    return point;\n  }\n  function addBodyEnd(ends, point, start) {\n    var point1 = point.slice();\n    var point2 = point.slice();\n    point1[cDimIdx] += halfWidth;\n    point2[cDimIdx] -= halfWidth;\n    start ? ends.push(point1, point2) : ends.push(point2, point1);\n  }\n  function layEndLine(ends, endCenter) {\n    var from = endCenter.slice();\n    var to = endCenter.slice();\n    from[cDimIdx] -= halfWidth;\n    to[cDimIdx] += halfWidth;\n    ends.push(from, to);\n  }\n}", "map": {"version": 3, "names": ["zrUtil", "parsePercent", "each", "boxplotLayout", "ecModel", "groupResult", "groupSeriesByAxis", "groupItem", "seriesModels", "length", "calculateBase", "seriesModel", "idx", "layoutSingleSeries", "boxOffsetList", "boxWidthList", "result", "axisList", "eachSeriesByType", "baseAxis", "getBaseAxis", "indexOf", "axis", "push", "seriesCount", "boundList", "bandWidth", "type", "getBandWidth", "maxDataCount_1", "Math", "max", "getData", "count", "extent", "getExtent", "abs", "boxWidthBound", "get", "isArray", "availableWidth", "boxGap", "boxWidth", "base", "min", "offset", "coordSys", "coordinateSystem", "data", "halfWidth", "cDimIdx", "vDimIdx", "coordDims", "cDim", "mapDimension", "vDims", "mapDimensionsAll", "dataIndex", "axisDimVal", "median", "getPoint", "end1", "end2", "end4", "end5", "ends", "addBodyEnd", "layEndLine", "setItemLayout", "initBaseline", "dim", "val", "p", "point", "isNaN", "NaN", "dataToPoint", "start", "point1", "slice", "point2", "endCenter", "from", "to"], "sources": ["C:/WolfSystemV2/WolfOldFrontEnd/node_modules/echarts/lib/chart/boxplot/boxplotLayout.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport { parsePercent } from '../../util/number.js';\nvar each = zrUtil.each;\nexport default function boxplotLayout(ecModel) {\n  var groupResult = groupSeriesByAxis(ecModel);\n  each(groupResult, function (groupItem) {\n    var seriesModels = groupItem.seriesModels;\n\n    if (!seriesModels.length) {\n      return;\n    }\n\n    calculateBase(groupItem);\n    each(seriesModels, function (seriesModel, idx) {\n      layoutSingleSeries(seriesModel, groupItem.boxOffsetList[idx], groupItem.boxWidthList[idx]);\n    });\n  });\n}\n/**\r\n * Group series by axis.\r\n */\n\nfunction groupSeriesByAxis(ecModel) {\n  var result = [];\n  var axisList = [];\n  ecModel.eachSeriesByType('boxplot', function (seriesModel) {\n    var baseAxis = seriesModel.getBaseAxis();\n    var idx = zrUtil.indexOf(axisList, baseAxis);\n\n    if (idx < 0) {\n      idx = axisList.length;\n      axisList[idx] = baseAxis;\n      result[idx] = {\n        axis: baseAxis,\n        seriesModels: []\n      };\n    }\n\n    result[idx].seriesModels.push(seriesModel);\n  });\n  return result;\n}\n/**\r\n * Calculate offset and box width for each series.\r\n */\n\n\nfunction calculateBase(groupItem) {\n  var baseAxis = groupItem.axis;\n  var seriesModels = groupItem.seriesModels;\n  var seriesCount = seriesModels.length;\n  var boxWidthList = groupItem.boxWidthList = [];\n  var boxOffsetList = groupItem.boxOffsetList = [];\n  var boundList = [];\n  var bandWidth;\n\n  if (baseAxis.type === 'category') {\n    bandWidth = baseAxis.getBandWidth();\n  } else {\n    var maxDataCount_1 = 0;\n    each(seriesModels, function (seriesModel) {\n      maxDataCount_1 = Math.max(maxDataCount_1, seriesModel.getData().count());\n    });\n    var extent = baseAxis.getExtent();\n    bandWidth = Math.abs(extent[1] - extent[0]) / maxDataCount_1;\n  }\n\n  each(seriesModels, function (seriesModel) {\n    var boxWidthBound = seriesModel.get('boxWidth');\n\n    if (!zrUtil.isArray(boxWidthBound)) {\n      boxWidthBound = [boxWidthBound, boxWidthBound];\n    }\n\n    boundList.push([parsePercent(boxWidthBound[0], bandWidth) || 0, parsePercent(boxWidthBound[1], bandWidth) || 0]);\n  });\n  var availableWidth = bandWidth * 0.8 - 2;\n  var boxGap = availableWidth / seriesCount * 0.3;\n  var boxWidth = (availableWidth - boxGap * (seriesCount - 1)) / seriesCount;\n  var base = boxWidth / 2 - availableWidth / 2;\n  each(seriesModels, function (seriesModel, idx) {\n    boxOffsetList.push(base);\n    base += boxGap + boxWidth;\n    boxWidthList.push(Math.min(Math.max(boxWidth, boundList[idx][0]), boundList[idx][1]));\n  });\n}\n/**\r\n * Calculate points location for each series.\r\n */\n\n\nfunction layoutSingleSeries(seriesModel, offset, boxWidth) {\n  var coordSys = seriesModel.coordinateSystem;\n  var data = seriesModel.getData();\n  var halfWidth = boxWidth / 2;\n  var cDimIdx = seriesModel.get('layout') === 'horizontal' ? 0 : 1;\n  var vDimIdx = 1 - cDimIdx;\n  var coordDims = ['x', 'y'];\n  var cDim = data.mapDimension(coordDims[cDimIdx]);\n  var vDims = data.mapDimensionsAll(coordDims[vDimIdx]);\n\n  if (cDim == null || vDims.length < 5) {\n    return;\n  }\n\n  for (var dataIndex = 0; dataIndex < data.count(); dataIndex++) {\n    var axisDimVal = data.get(cDim, dataIndex);\n    var median = getPoint(axisDimVal, vDims[2], dataIndex);\n    var end1 = getPoint(axisDimVal, vDims[0], dataIndex);\n    var end2 = getPoint(axisDimVal, vDims[1], dataIndex);\n    var end4 = getPoint(axisDimVal, vDims[3], dataIndex);\n    var end5 = getPoint(axisDimVal, vDims[4], dataIndex);\n    var ends = [];\n    addBodyEnd(ends, end2, false);\n    addBodyEnd(ends, end4, true);\n    ends.push(end1, end2, end5, end4);\n    layEndLine(ends, end1);\n    layEndLine(ends, end5);\n    layEndLine(ends, median);\n    data.setItemLayout(dataIndex, {\n      initBaseline: median[vDimIdx],\n      ends: ends\n    });\n  }\n\n  function getPoint(axisDimVal, dim, dataIndex) {\n    var val = data.get(dim, dataIndex);\n    var p = [];\n    p[cDimIdx] = axisDimVal;\n    p[vDimIdx] = val;\n    var point;\n\n    if (isNaN(axisDimVal) || isNaN(val)) {\n      point = [NaN, NaN];\n    } else {\n      point = coordSys.dataToPoint(p);\n      point[cDimIdx] += offset;\n    }\n\n    return point;\n  }\n\n  function addBodyEnd(ends, point, start) {\n    var point1 = point.slice();\n    var point2 = point.slice();\n    point1[cDimIdx] += halfWidth;\n    point2[cDimIdx] -= halfWidth;\n    start ? ends.push(point1, point2) : ends.push(point2, point1);\n  }\n\n  function layEndLine(ends, endCenter) {\n    var from = endCenter.slice();\n    var to = endCenter.slice();\n    from[cDimIdx] -= halfWidth;\n    to[cDimIdx] += halfWidth;\n    ends.push(from, to);\n  }\n}"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,KAAKA,MAAM,MAAM,0BAA0B;AAClD,SAASC,YAAY,QAAQ,sBAAsB;AACnD,IAAIC,IAAI,GAAGF,MAAM,CAACE,IAAI;AACtB,eAAe,SAASC,aAAaA,CAACC,OAAO,EAAE;EAC7C,IAAIC,WAAW,GAAGC,iBAAiB,CAACF,OAAO,CAAC;EAC5CF,IAAI,CAACG,WAAW,EAAE,UAAUE,SAAS,EAAE;IACrC,IAAIC,YAAY,GAAGD,SAAS,CAACC,YAAY;IAEzC,IAAI,CAACA,YAAY,CAACC,MAAM,EAAE;MACxB;IACF;IAEAC,aAAa,CAACH,SAAS,CAAC;IACxBL,IAAI,CAACM,YAAY,EAAE,UAAUG,WAAW,EAAEC,GAAG,EAAE;MAC7CC,kBAAkB,CAACF,WAAW,EAAEJ,SAAS,CAACO,aAAa,CAACF,GAAG,CAAC,EAAEL,SAAS,CAACQ,YAAY,CAACH,GAAG,CAAC,CAAC;IAC5F,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ;AACA;AACA;AACA;;AAEA,SAASN,iBAAiBA,CAACF,OAAO,EAAE;EAClC,IAAIY,MAAM,GAAG,EAAE;EACf,IAAIC,QAAQ,GAAG,EAAE;EACjBb,OAAO,CAACc,gBAAgB,CAAC,SAAS,EAAE,UAAUP,WAAW,EAAE;IACzD,IAAIQ,QAAQ,GAAGR,WAAW,CAACS,WAAW,CAAC,CAAC;IACxC,IAAIR,GAAG,GAAGZ,MAAM,CAACqB,OAAO,CAACJ,QAAQ,EAAEE,QAAQ,CAAC;IAE5C,IAAIP,GAAG,GAAG,CAAC,EAAE;MACXA,GAAG,GAAGK,QAAQ,CAACR,MAAM;MACrBQ,QAAQ,CAACL,GAAG,CAAC,GAAGO,QAAQ;MACxBH,MAAM,CAACJ,GAAG,CAAC,GAAG;QACZU,IAAI,EAAEH,QAAQ;QACdX,YAAY,EAAE;MAChB,CAAC;IACH;IAEAQ,MAAM,CAACJ,GAAG,CAAC,CAACJ,YAAY,CAACe,IAAI,CAACZ,WAAW,CAAC;EAC5C,CAAC,CAAC;EACF,OAAOK,MAAM;AACf;AACA;AACA;AACA;;AAGA,SAASN,aAAaA,CAACH,SAAS,EAAE;EAChC,IAAIY,QAAQ,GAAGZ,SAAS,CAACe,IAAI;EAC7B,IAAId,YAAY,GAAGD,SAAS,CAACC,YAAY;EACzC,IAAIgB,WAAW,GAAGhB,YAAY,CAACC,MAAM;EACrC,IAAIM,YAAY,GAAGR,SAAS,CAACQ,YAAY,GAAG,EAAE;EAC9C,IAAID,aAAa,GAAGP,SAAS,CAACO,aAAa,GAAG,EAAE;EAChD,IAAIW,SAAS,GAAG,EAAE;EAClB,IAAIC,SAAS;EAEb,IAAIP,QAAQ,CAACQ,IAAI,KAAK,UAAU,EAAE;IAChCD,SAAS,GAAGP,QAAQ,CAACS,YAAY,CAAC,CAAC;EACrC,CAAC,MAAM;IACL,IAAIC,cAAc,GAAG,CAAC;IACtB3B,IAAI,CAACM,YAAY,EAAE,UAAUG,WAAW,EAAE;MACxCkB,cAAc,GAAGC,IAAI,CAACC,GAAG,CAACF,cAAc,EAAElB,WAAW,CAACqB,OAAO,CAAC,CAAC,CAACC,KAAK,CAAC,CAAC,CAAC;IAC1E,CAAC,CAAC;IACF,IAAIC,MAAM,GAAGf,QAAQ,CAACgB,SAAS,CAAC,CAAC;IACjCT,SAAS,GAAGI,IAAI,CAACM,GAAG,CAACF,MAAM,CAAC,CAAC,CAAC,GAAGA,MAAM,CAAC,CAAC,CAAC,CAAC,GAAGL,cAAc;EAC9D;EAEA3B,IAAI,CAACM,YAAY,EAAE,UAAUG,WAAW,EAAE;IACxC,IAAI0B,aAAa,GAAG1B,WAAW,CAAC2B,GAAG,CAAC,UAAU,CAAC;IAE/C,IAAI,CAACtC,MAAM,CAACuC,OAAO,CAACF,aAAa,CAAC,EAAE;MAClCA,aAAa,GAAG,CAACA,aAAa,EAAEA,aAAa,CAAC;IAChD;IAEAZ,SAAS,CAACF,IAAI,CAAC,CAACtB,YAAY,CAACoC,aAAa,CAAC,CAAC,CAAC,EAAEX,SAAS,CAAC,IAAI,CAAC,EAAEzB,YAAY,CAACoC,aAAa,CAAC,CAAC,CAAC,EAAEX,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC;EAClH,CAAC,CAAC;EACF,IAAIc,cAAc,GAAGd,SAAS,GAAG,GAAG,GAAG,CAAC;EACxC,IAAIe,MAAM,GAAGD,cAAc,GAAGhB,WAAW,GAAG,GAAG;EAC/C,IAAIkB,QAAQ,GAAG,CAACF,cAAc,GAAGC,MAAM,IAAIjB,WAAW,GAAG,CAAC,CAAC,IAAIA,WAAW;EAC1E,IAAImB,IAAI,GAAGD,QAAQ,GAAG,CAAC,GAAGF,cAAc,GAAG,CAAC;EAC5CtC,IAAI,CAACM,YAAY,EAAE,UAAUG,WAAW,EAAEC,GAAG,EAAE;IAC7CE,aAAa,CAACS,IAAI,CAACoB,IAAI,CAAC;IACxBA,IAAI,IAAIF,MAAM,GAAGC,QAAQ;IACzB3B,YAAY,CAACQ,IAAI,CAACO,IAAI,CAACc,GAAG,CAACd,IAAI,CAACC,GAAG,CAACW,QAAQ,EAAEjB,SAAS,CAACb,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,EAAEa,SAAS,CAACb,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACvF,CAAC,CAAC;AACJ;AACA;AACA;AACA;;AAGA,SAASC,kBAAkBA,CAACF,WAAW,EAAEkC,MAAM,EAAEH,QAAQ,EAAE;EACzD,IAAII,QAAQ,GAAGnC,WAAW,CAACoC,gBAAgB;EAC3C,IAAIC,IAAI,GAAGrC,WAAW,CAACqB,OAAO,CAAC,CAAC;EAChC,IAAIiB,SAAS,GAAGP,QAAQ,GAAG,CAAC;EAC5B,IAAIQ,OAAO,GAAGvC,WAAW,CAAC2B,GAAG,CAAC,QAAQ,CAAC,KAAK,YAAY,GAAG,CAAC,GAAG,CAAC;EAChE,IAAIa,OAAO,GAAG,CAAC,GAAGD,OAAO;EACzB,IAAIE,SAAS,GAAG,CAAC,GAAG,EAAE,GAAG,CAAC;EAC1B,IAAIC,IAAI,GAAGL,IAAI,CAACM,YAAY,CAACF,SAAS,CAACF,OAAO,CAAC,CAAC;EAChD,IAAIK,KAAK,GAAGP,IAAI,CAACQ,gBAAgB,CAACJ,SAAS,CAACD,OAAO,CAAC,CAAC;EAErD,IAAIE,IAAI,IAAI,IAAI,IAAIE,KAAK,CAAC9C,MAAM,GAAG,CAAC,EAAE;IACpC;EACF;EAEA,KAAK,IAAIgD,SAAS,GAAG,CAAC,EAAEA,SAAS,GAAGT,IAAI,CAACf,KAAK,CAAC,CAAC,EAAEwB,SAAS,EAAE,EAAE;IAC7D,IAAIC,UAAU,GAAGV,IAAI,CAACV,GAAG,CAACe,IAAI,EAAEI,SAAS,CAAC;IAC1C,IAAIE,MAAM,GAAGC,QAAQ,CAACF,UAAU,EAAEH,KAAK,CAAC,CAAC,CAAC,EAAEE,SAAS,CAAC;IACtD,IAAII,IAAI,GAAGD,QAAQ,CAACF,UAAU,EAAEH,KAAK,CAAC,CAAC,CAAC,EAAEE,SAAS,CAAC;IACpD,IAAIK,IAAI,GAAGF,QAAQ,CAACF,UAAU,EAAEH,KAAK,CAAC,CAAC,CAAC,EAAEE,SAAS,CAAC;IACpD,IAAIM,IAAI,GAAGH,QAAQ,CAACF,UAAU,EAAEH,KAAK,CAAC,CAAC,CAAC,EAAEE,SAAS,CAAC;IACpD,IAAIO,IAAI,GAAGJ,QAAQ,CAACF,UAAU,EAAEH,KAAK,CAAC,CAAC,CAAC,EAAEE,SAAS,CAAC;IACpD,IAAIQ,IAAI,GAAG,EAAE;IACbC,UAAU,CAACD,IAAI,EAAEH,IAAI,EAAE,KAAK,CAAC;IAC7BI,UAAU,CAACD,IAAI,EAAEF,IAAI,EAAE,IAAI,CAAC;IAC5BE,IAAI,CAAC1C,IAAI,CAACsC,IAAI,EAAEC,IAAI,EAAEE,IAAI,EAAED,IAAI,CAAC;IACjCI,UAAU,CAACF,IAAI,EAAEJ,IAAI,CAAC;IACtBM,UAAU,CAACF,IAAI,EAAED,IAAI,CAAC;IACtBG,UAAU,CAACF,IAAI,EAAEN,MAAM,CAAC;IACxBX,IAAI,CAACoB,aAAa,CAACX,SAAS,EAAE;MAC5BY,YAAY,EAAEV,MAAM,CAACR,OAAO,CAAC;MAC7Bc,IAAI,EAAEA;IACR,CAAC,CAAC;EACJ;EAEA,SAASL,QAAQA,CAACF,UAAU,EAAEY,GAAG,EAAEb,SAAS,EAAE;IAC5C,IAAIc,GAAG,GAAGvB,IAAI,CAACV,GAAG,CAACgC,GAAG,EAAEb,SAAS,CAAC;IAClC,IAAIe,CAAC,GAAG,EAAE;IACVA,CAAC,CAACtB,OAAO,CAAC,GAAGQ,UAAU;IACvBc,CAAC,CAACrB,OAAO,CAAC,GAAGoB,GAAG;IAChB,IAAIE,KAAK;IAET,IAAIC,KAAK,CAAChB,UAAU,CAAC,IAAIgB,KAAK,CAACH,GAAG,CAAC,EAAE;MACnCE,KAAK,GAAG,CAACE,GAAG,EAAEA,GAAG,CAAC;IACpB,CAAC,MAAM;MACLF,KAAK,GAAG3B,QAAQ,CAAC8B,WAAW,CAACJ,CAAC,CAAC;MAC/BC,KAAK,CAACvB,OAAO,CAAC,IAAIL,MAAM;IAC1B;IAEA,OAAO4B,KAAK;EACd;EAEA,SAASP,UAAUA,CAACD,IAAI,EAAEQ,KAAK,EAAEI,KAAK,EAAE;IACtC,IAAIC,MAAM,GAAGL,KAAK,CAACM,KAAK,CAAC,CAAC;IAC1B,IAAIC,MAAM,GAAGP,KAAK,CAACM,KAAK,CAAC,CAAC;IAC1BD,MAAM,CAAC5B,OAAO,CAAC,IAAID,SAAS;IAC5B+B,MAAM,CAAC9B,OAAO,CAAC,IAAID,SAAS;IAC5B4B,KAAK,GAAGZ,IAAI,CAAC1C,IAAI,CAACuD,MAAM,EAAEE,MAAM,CAAC,GAAGf,IAAI,CAAC1C,IAAI,CAACyD,MAAM,EAAEF,MAAM,CAAC;EAC/D;EAEA,SAASX,UAAUA,CAACF,IAAI,EAAEgB,SAAS,EAAE;IACnC,IAAIC,IAAI,GAAGD,SAAS,CAACF,KAAK,CAAC,CAAC;IAC5B,IAAII,EAAE,GAAGF,SAAS,CAACF,KAAK,CAAC,CAAC;IAC1BG,IAAI,CAAChC,OAAO,CAAC,IAAID,SAAS;IAC1BkC,EAAE,CAACjC,OAAO,CAAC,IAAID,SAAS;IACxBgB,IAAI,CAAC1C,IAAI,CAAC2D,IAAI,EAAEC,EAAE,CAAC;EACrB;AACF"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}