{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\n\n/**\r\n * This module exposes helper functions for developing extensions.\r\n */\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport createSeriesData from '../../chart/helper/createSeriesData.js'; // import createGraphFromNodeEdge from './chart/helper/createGraphFromNodeEdge.js';\n\nimport * as axisHelper from '../../coord/axisHelper.js';\nimport { AxisModelCommonMixin } from '../../coord/axisModelCommonMixin.js';\nimport Model from '../../model/Model.js';\nimport { getLayoutRect } from '../../util/layout.js';\nimport { enableDataStack, isDimensionStacked, getStackedDimension } from '../../data/helper/dataStackHelper.js';\nimport { getECData } from '../../util/innerStore.js';\nimport { createTextStyle as innerCreateTextStyle } from '../../label/labelStyle.js';\n/**\r\n * Create a multi dimension List structure from seriesModel.\r\n */\n\nexport function createList(seriesModel) {\n  return createSeriesData(null, seriesModel);\n} // export function createGraph(seriesModel) {\n//     let nodes = seriesModel.get('data');\n//     let links = seriesModel.get('links');\n//     return createGraphFromNodeEdge(nodes, links, seriesModel);\n// }\n\nexport { getLayoutRect };\nexport { createDimensions } from '../../data/helper/createDimensions.js';\nexport var dataStack = {\n  isDimensionStacked: isDimensionStacked,\n  enableDataStack: enableDataStack,\n  getStackedDimension: getStackedDimension\n};\n/**\r\n * Create a symbol element with given symbol configuration: shape, x, y, width, height, color\r\n * @param {string} symbolDesc\r\n * @param {number} x\r\n * @param {number} y\r\n * @param {number} w\r\n * @param {number} h\r\n * @param {string} color\r\n */\n\nexport { createSymbol } from '../../util/symbol.js';\n/**\r\n * Create scale\r\n * @param {Array.<number>} dataExtent\r\n * @param {Object|module:echarts/Model} option If `optoin.type`\r\n *        is secified, it can only be `'value'` currently.\r\n */\n\nexport function createScale(dataExtent, option) {\n  var axisModel = option;\n  if (!(option instanceof Model)) {\n    axisModel = new Model(option); // FIXME\n    // Currently AxisModelCommonMixin has nothing to do with the\n    // the requirements of `axisHelper.createScaleByModel`. For\n    // example the methods `getCategories` and `getOrdinalMeta`\n    // are required for `'category'` axis, and ecModel is required\n    // for `'time'` axis. But occasionally echarts-gl happened\n    // to only use `'value'` axis.\n    // zrUtil.mixin(axisModel, AxisModelCommonMixin);\n  }\n\n  var scale = axisHelper.createScaleByModel(axisModel);\n  scale.setExtent(dataExtent[0], dataExtent[1]);\n  axisHelper.niceScaleExtent(scale, axisModel);\n  return scale;\n}\n/**\r\n * Mixin common methods to axis model,\r\n *\r\n * Include methods\r\n * `getFormattedLabels() => Array.<string>`\r\n * `getCategories() => Array.<string>`\r\n * `getMin(origin: boolean) => number`\r\n * `getMax(origin: boolean) => number`\r\n * `getNeedCrossZero() => boolean`\r\n */\n\nexport function mixinAxisModelCommonMethods(Model) {\n  zrUtil.mixin(Model, AxisModelCommonMixin);\n}\nexport { getECData };\nexport { enableHoverEmphasis } from '../../util/states.js';\nexport function createTextStyle(textStyleModel, opts) {\n  opts = opts || {};\n  return innerCreateTextStyle(textStyleModel, null, null, opts.state !== 'normal');\n}", "map": {"version": 3, "names": ["zrUtil", "createSeriesData", "axisHelper", "AxisModelCommonMixin", "Model", "getLayoutRect", "enableDataStack", "isDimensionStacked", "getStackedDimension", "getECData", "createTextStyle", "innerCreateTextStyle", "createList", "seriesModel", "createDimensions", "dataStack", "createSymbol", "createScale", "dataExtent", "option", "axisModel", "scale", "createScaleByModel", "setExtent", "niceScaleExtent", "mixinAxisModelCommonMethods", "mixin", "enableHoverEmphasis", "textStyleModel", "opts", "state"], "sources": ["C:/WolfSystemV2/WolfOldFrontEnd/node_modules/echarts/lib/export/api/helper.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\n\n/**\r\n * This module exposes helper functions for developing extensions.\r\n */\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport createSeriesData from '../../chart/helper/createSeriesData.js'; // import createGraphFromNodeEdge from './chart/helper/createGraphFromNodeEdge.js';\n\nimport * as axisHelper from '../../coord/axisHelper.js';\nimport { AxisModelCommonMixin } from '../../coord/axisModelCommonMixin.js';\nimport Model from '../../model/Model.js';\nimport { getLayoutRect } from '../../util/layout.js';\nimport { enableDataStack, isDimensionStacked, getStackedDimension } from '../../data/helper/dataStackHelper.js';\nimport { getECData } from '../../util/innerStore.js';\nimport { createTextStyle as innerCreateTextStyle } from '../../label/labelStyle.js';\n/**\r\n * Create a multi dimension List structure from seriesModel.\r\n */\n\nexport function createList(seriesModel) {\n  return createSeriesData(null, seriesModel);\n} // export function createGraph(seriesModel) {\n//     let nodes = seriesModel.get('data');\n//     let links = seriesModel.get('links');\n//     return createGraphFromNodeEdge(nodes, links, seriesModel);\n// }\n\nexport { getLayoutRect };\nexport { createDimensions } from '../../data/helper/createDimensions.js';\nexport var dataStack = {\n  isDimensionStacked: isDimensionStacked,\n  enableDataStack: enableDataStack,\n  getStackedDimension: getStackedDimension\n};\n/**\r\n * Create a symbol element with given symbol configuration: shape, x, y, width, height, color\r\n * @param {string} symbolDesc\r\n * @param {number} x\r\n * @param {number} y\r\n * @param {number} w\r\n * @param {number} h\r\n * @param {string} color\r\n */\n\nexport { createSymbol } from '../../util/symbol.js';\n/**\r\n * Create scale\r\n * @param {Array.<number>} dataExtent\r\n * @param {Object|module:echarts/Model} option If `optoin.type`\r\n *        is secified, it can only be `'value'` currently.\r\n */\n\nexport function createScale(dataExtent, option) {\n  var axisModel = option;\n\n  if (!(option instanceof Model)) {\n    axisModel = new Model(option); // FIXME\n    // Currently AxisModelCommonMixin has nothing to do with the\n    // the requirements of `axisHelper.createScaleByModel`. For\n    // example the methods `getCategories` and `getOrdinalMeta`\n    // are required for `'category'` axis, and ecModel is required\n    // for `'time'` axis. But occasionally echarts-gl happened\n    // to only use `'value'` axis.\n    // zrUtil.mixin(axisModel, AxisModelCommonMixin);\n  }\n\n  var scale = axisHelper.createScaleByModel(axisModel);\n  scale.setExtent(dataExtent[0], dataExtent[1]);\n  axisHelper.niceScaleExtent(scale, axisModel);\n  return scale;\n}\n/**\r\n * Mixin common methods to axis model,\r\n *\r\n * Include methods\r\n * `getFormattedLabels() => Array.<string>`\r\n * `getCategories() => Array.<string>`\r\n * `getMin(origin: boolean) => number`\r\n * `getMax(origin: boolean) => number`\r\n * `getNeedCrossZero() => boolean`\r\n */\n\nexport function mixinAxisModelCommonMethods(Model) {\n  zrUtil.mixin(Model, AxisModelCommonMixin);\n}\nexport { getECData };\nexport { enableHoverEmphasis } from '../../util/states.js';\nexport function createTextStyle(textStyleModel, opts) {\n  opts = opts || {};\n  return innerCreateTextStyle(textStyleModel, null, null, opts.state !== 'normal');\n}"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,OAAO,KAAKA,MAAM,MAAM,0BAA0B;AAClD,OAAOC,gBAAgB,MAAM,wCAAwC,CAAC,CAAC;;AAEvE,OAAO,KAAKC,UAAU,MAAM,2BAA2B;AACvD,SAASC,oBAAoB,QAAQ,qCAAqC;AAC1E,OAAOC,KAAK,MAAM,sBAAsB;AACxC,SAASC,aAAa,QAAQ,sBAAsB;AACpD,SAASC,eAAe,EAAEC,kBAAkB,EAAEC,mBAAmB,QAAQ,sCAAsC;AAC/G,SAASC,SAAS,QAAQ,0BAA0B;AACpD,SAASC,eAAe,IAAIC,oBAAoB,QAAQ,2BAA2B;AACnF;AACA;AACA;;AAEA,OAAO,SAASC,UAAUA,CAACC,WAAW,EAAE;EACtC,OAAOZ,gBAAgB,CAAC,IAAI,EAAEY,WAAW,CAAC;AAC5C,CAAC,CAAC;AACF;AACA;AACA;AACA;;AAEA,SAASR,aAAa;AACtB,SAASS,gBAAgB,QAAQ,uCAAuC;AACxE,OAAO,IAAIC,SAAS,GAAG;EACrBR,kBAAkB,EAAEA,kBAAkB;EACtCD,eAAe,EAAEA,eAAe;EAChCE,mBAAmB,EAAEA;AACvB,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAASQ,YAAY,QAAQ,sBAAsB;AACnD;AACA;AACA;AACA;AACA;AACA;;AAEA,OAAO,SAASC,WAAWA,CAACC,UAAU,EAAEC,MAAM,EAAE;EAC9C,IAAIC,SAAS,GAAGD,MAAM;EAEtB,IAAI,EAAEA,MAAM,YAAYf,KAAK,CAAC,EAAE;IAC9BgB,SAAS,GAAG,IAAIhB,KAAK,CAACe,MAAM,CAAC,CAAC,CAAC;IAC/B;IACA;IACA;IACA;IACA;IACA;IACA;EACF;;EAEA,IAAIE,KAAK,GAAGnB,UAAU,CAACoB,kBAAkB,CAACF,SAAS,CAAC;EACpDC,KAAK,CAACE,SAAS,CAACL,UAAU,CAAC,CAAC,CAAC,EAAEA,UAAU,CAAC,CAAC,CAAC,CAAC;EAC7ChB,UAAU,CAACsB,eAAe,CAACH,KAAK,EAAED,SAAS,CAAC;EAC5C,OAAOC,KAAK;AACd;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,OAAO,SAASI,2BAA2BA,CAACrB,KAAK,EAAE;EACjDJ,MAAM,CAAC0B,KAAK,CAACtB,KAAK,EAAED,oBAAoB,CAAC;AAC3C;AACA,SAASM,SAAS;AAClB,SAASkB,mBAAmB,QAAQ,sBAAsB;AAC1D,OAAO,SAASjB,eAAeA,CAACkB,cAAc,EAAEC,IAAI,EAAE;EACpDA,IAAI,GAAGA,IAAI,IAAI,CAAC,CAAC;EACjB,OAAOlB,oBAAoB,CAACiB,cAAc,EAAE,IAAI,EAAE,IAAI,EAAEC,IAAI,CAACC,KAAK,KAAK,QAAQ,CAAC;AAClF"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}