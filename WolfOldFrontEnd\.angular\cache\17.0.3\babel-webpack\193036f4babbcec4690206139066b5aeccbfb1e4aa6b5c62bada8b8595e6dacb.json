{"ast": null, "code": "import { __extends } from \"tslib\";\nimport Displayable, { DEFAULT_COMMON_STYLE, DEFAULT_COMMON_ANIMATION_PROPS } from './Displayable.js';\nimport PathProxy from '../core/PathProxy.js';\nimport * as pathContain from '../contain/path.js';\nimport { defaults, keys, extend, clone, isString, createObject } from '../core/util.js';\nimport { lum } from '../tool/color.js';\nimport { DARK_LABEL_COLOR, LIGHT_LABEL_COLOR, DARK_MODE_THRESHOLD, LIGHTER_LABEL_COLOR } from '../config.js';\nimport { REDRAW_BIT, SHAPE_CHANGED_BIT, STYLE_CHANGED_BIT } from './constants.js';\nimport { TRANSFORMABLE_PROPS } from '../core/Transformable.js';\nexport var DEFAULT_PATH_STYLE = defaults({\n  fill: '#000',\n  stroke: null,\n  strokePercent: 1,\n  fillOpacity: 1,\n  strokeOpacity: 1,\n  lineDashOffset: 0,\n  lineWidth: 1,\n  lineCap: 'butt',\n  miterLimit: 10,\n  strokeNoScale: false,\n  strokeFirst: false\n}, DEFAULT_COMMON_STYLE);\nexport var DEFAULT_PATH_ANIMATION_PROPS = {\n  style: defaults({\n    fill: true,\n    stroke: true,\n    strokePercent: true,\n    fillOpacity: true,\n    strokeOpacity: true,\n    lineDashOffset: true,\n    lineWidth: true,\n    miterLimit: true\n  }, DEFAULT_COMMON_ANIMATION_PROPS.style)\n};\nvar pathCopyParams = TRANSFORMABLE_PROPS.concat(['invisible', 'culling', 'z', 'z2', 'zlevel', 'parent']);\nvar Path = function (_super) {\n  __extends(Path, _super);\n  function Path(opts) {\n    return _super.call(this, opts) || this;\n  }\n  Path.prototype.update = function () {\n    var _this = this;\n    _super.prototype.update.call(this);\n    var style = this.style;\n    if (style.decal) {\n      var decalEl = this._decalEl = this._decalEl || new Path();\n      if (decalEl.buildPath === Path.prototype.buildPath) {\n        decalEl.buildPath = function (ctx) {\n          _this.buildPath(ctx, _this.shape);\n        };\n      }\n      decalEl.silent = true;\n      var decalElStyle = decalEl.style;\n      for (var key in style) {\n        if (decalElStyle[key] !== style[key]) {\n          decalElStyle[key] = style[key];\n        }\n      }\n      decalElStyle.fill = style.fill ? style.decal : null;\n      decalElStyle.decal = null;\n      decalElStyle.shadowColor = null;\n      style.strokeFirst && (decalElStyle.stroke = null);\n      for (var i = 0; i < pathCopyParams.length; ++i) {\n        decalEl[pathCopyParams[i]] = this[pathCopyParams[i]];\n      }\n      decalEl.__dirty |= REDRAW_BIT;\n    } else if (this._decalEl) {\n      this._decalEl = null;\n    }\n  };\n  Path.prototype.getDecalElement = function () {\n    return this._decalEl;\n  };\n  Path.prototype._init = function (props) {\n    var keysArr = keys(props);\n    this.shape = this.getDefaultShape();\n    var defaultStyle = this.getDefaultStyle();\n    if (defaultStyle) {\n      this.useStyle(defaultStyle);\n    }\n    for (var i = 0; i < keysArr.length; i++) {\n      var key = keysArr[i];\n      var value = props[key];\n      if (key === 'style') {\n        if (!this.style) {\n          this.useStyle(value);\n        } else {\n          extend(this.style, value);\n        }\n      } else if (key === 'shape') {\n        extend(this.shape, value);\n      } else {\n        _super.prototype.attrKV.call(this, key, value);\n      }\n    }\n    if (!this.style) {\n      this.useStyle({});\n    }\n  };\n  Path.prototype.getDefaultStyle = function () {\n    return null;\n  };\n  Path.prototype.getDefaultShape = function () {\n    return {};\n  };\n  Path.prototype.canBeInsideText = function () {\n    return this.hasFill();\n  };\n  Path.prototype.getInsideTextFill = function () {\n    var pathFill = this.style.fill;\n    if (pathFill !== 'none') {\n      if (isString(pathFill)) {\n        var fillLum = lum(pathFill, 0);\n        if (fillLum > 0.5) {\n          return DARK_LABEL_COLOR;\n        } else if (fillLum > 0.2) {\n          return LIGHTER_LABEL_COLOR;\n        }\n        return LIGHT_LABEL_COLOR;\n      } else if (pathFill) {\n        return LIGHT_LABEL_COLOR;\n      }\n    }\n    return DARK_LABEL_COLOR;\n  };\n  Path.prototype.getInsideTextStroke = function (textFill) {\n    var pathFill = this.style.fill;\n    if (isString(pathFill)) {\n      var zr = this.__zr;\n      var isDarkMode = !!(zr && zr.isDarkMode());\n      var isDarkLabel = lum(textFill, 0) < DARK_MODE_THRESHOLD;\n      if (isDarkMode === isDarkLabel) {\n        return pathFill;\n      }\n    }\n  };\n  Path.prototype.buildPath = function (ctx, shapeCfg, inBatch) {};\n  Path.prototype.pathUpdated = function () {\n    this.__dirty &= ~SHAPE_CHANGED_BIT;\n  };\n  Path.prototype.getUpdatedPathProxy = function (inBatch) {\n    !this.path && this.createPathProxy();\n    this.path.beginPath();\n    this.buildPath(this.path, this.shape, inBatch);\n    return this.path;\n  };\n  Path.prototype.createPathProxy = function () {\n    this.path = new PathProxy(false);\n  };\n  Path.prototype.hasStroke = function () {\n    var style = this.style;\n    var stroke = style.stroke;\n    return !(stroke == null || stroke === 'none' || !(style.lineWidth > 0));\n  };\n  Path.prototype.hasFill = function () {\n    var style = this.style;\n    var fill = style.fill;\n    return fill != null && fill !== 'none';\n  };\n  Path.prototype.getBoundingRect = function () {\n    var rect = this._rect;\n    var style = this.style;\n    var needsUpdateRect = !rect;\n    if (needsUpdateRect) {\n      var firstInvoke = false;\n      if (!this.path) {\n        firstInvoke = true;\n        this.createPathProxy();\n      }\n      var path = this.path;\n      if (firstInvoke || this.__dirty & SHAPE_CHANGED_BIT) {\n        path.beginPath();\n        this.buildPath(path, this.shape, false);\n        this.pathUpdated();\n      }\n      rect = path.getBoundingRect();\n    }\n    this._rect = rect;\n    if (this.hasStroke() && this.path && this.path.len() > 0) {\n      var rectStroke = this._rectStroke || (this._rectStroke = rect.clone());\n      if (this.__dirty || needsUpdateRect) {\n        rectStroke.copy(rect);\n        var lineScale = style.strokeNoScale ? this.getLineScale() : 1;\n        var w = style.lineWidth;\n        if (!this.hasFill()) {\n          var strokeContainThreshold = this.strokeContainThreshold;\n          w = Math.max(w, strokeContainThreshold == null ? 4 : strokeContainThreshold);\n        }\n        if (lineScale > 1e-10) {\n          rectStroke.width += w / lineScale;\n          rectStroke.height += w / lineScale;\n          rectStroke.x -= w / lineScale / 2;\n          rectStroke.y -= w / lineScale / 2;\n        }\n      }\n      return rectStroke;\n    }\n    return rect;\n  };\n  Path.prototype.contain = function (x, y) {\n    var localPos = this.transformCoordToLocal(x, y);\n    var rect = this.getBoundingRect();\n    var style = this.style;\n    x = localPos[0];\n    y = localPos[1];\n    if (rect.contain(x, y)) {\n      var pathProxy = this.path;\n      if (this.hasStroke()) {\n        var lineWidth = style.lineWidth;\n        var lineScale = style.strokeNoScale ? this.getLineScale() : 1;\n        if (lineScale > 1e-10) {\n          if (!this.hasFill()) {\n            lineWidth = Math.max(lineWidth, this.strokeContainThreshold);\n          }\n          if (pathContain.containStroke(pathProxy, lineWidth / lineScale, x, y)) {\n            return true;\n          }\n        }\n      }\n      if (this.hasFill()) {\n        return pathContain.contain(pathProxy, x, y);\n      }\n    }\n    return false;\n  };\n  Path.prototype.dirtyShape = function () {\n    this.__dirty |= SHAPE_CHANGED_BIT;\n    if (this._rect) {\n      this._rect = null;\n    }\n    if (this._decalEl) {\n      this._decalEl.dirtyShape();\n    }\n    this.markRedraw();\n  };\n  Path.prototype.dirty = function () {\n    this.dirtyStyle();\n    this.dirtyShape();\n  };\n  Path.prototype.animateShape = function (loop) {\n    return this.animate('shape', loop);\n  };\n  Path.prototype.updateDuringAnimation = function (targetKey) {\n    if (targetKey === 'style') {\n      this.dirtyStyle();\n    } else if (targetKey === 'shape') {\n      this.dirtyShape();\n    } else {\n      this.markRedraw();\n    }\n  };\n  Path.prototype.attrKV = function (key, value) {\n    if (key === 'shape') {\n      this.setShape(value);\n    } else {\n      _super.prototype.attrKV.call(this, key, value);\n    }\n  };\n  Path.prototype.setShape = function (keyOrObj, value) {\n    var shape = this.shape;\n    if (!shape) {\n      shape = this.shape = {};\n    }\n    if (typeof keyOrObj === 'string') {\n      shape[keyOrObj] = value;\n    } else {\n      extend(shape, keyOrObj);\n    }\n    this.dirtyShape();\n    return this;\n  };\n  Path.prototype.shapeChanged = function () {\n    return !!(this.__dirty & SHAPE_CHANGED_BIT);\n  };\n  Path.prototype.createStyle = function (obj) {\n    return createObject(DEFAULT_PATH_STYLE, obj);\n  };\n  Path.prototype._innerSaveToNormal = function (toState) {\n    _super.prototype._innerSaveToNormal.call(this, toState);\n    var normalState = this._normalState;\n    if (toState.shape && !normalState.shape) {\n      normalState.shape = extend({}, this.shape);\n    }\n  };\n  Path.prototype._applyStateObj = function (stateName, state, normalState, keepCurrentStates, transition, animationCfg) {\n    _super.prototype._applyStateObj.call(this, stateName, state, normalState, keepCurrentStates, transition, animationCfg);\n    var needsRestoreToNormal = !(state && keepCurrentStates);\n    var targetShape;\n    if (state && state.shape) {\n      if (transition) {\n        if (keepCurrentStates) {\n          targetShape = state.shape;\n        } else {\n          targetShape = extend({}, normalState.shape);\n          extend(targetShape, state.shape);\n        }\n      } else {\n        targetShape = extend({}, keepCurrentStates ? this.shape : normalState.shape);\n        extend(targetShape, state.shape);\n      }\n    } else if (needsRestoreToNormal) {\n      targetShape = normalState.shape;\n    }\n    if (targetShape) {\n      if (transition) {\n        this.shape = extend({}, this.shape);\n        var targetShapePrimaryProps = {};\n        var shapeKeys = keys(targetShape);\n        for (var i = 0; i < shapeKeys.length; i++) {\n          var key = shapeKeys[i];\n          if (typeof targetShape[key] === 'object') {\n            this.shape[key] = targetShape[key];\n          } else {\n            targetShapePrimaryProps[key] = targetShape[key];\n          }\n        }\n        this._transitionState(stateName, {\n          shape: targetShapePrimaryProps\n        }, animationCfg);\n      } else {\n        this.shape = targetShape;\n        this.dirtyShape();\n      }\n    }\n  };\n  Path.prototype._mergeStates = function (states) {\n    var mergedState = _super.prototype._mergeStates.call(this, states);\n    var mergedShape;\n    for (var i = 0; i < states.length; i++) {\n      var state = states[i];\n      if (state.shape) {\n        mergedShape = mergedShape || {};\n        this._mergeStyle(mergedShape, state.shape);\n      }\n    }\n    if (mergedShape) {\n      mergedState.shape = mergedShape;\n    }\n    return mergedState;\n  };\n  Path.prototype.getAnimationStyleProps = function () {\n    return DEFAULT_PATH_ANIMATION_PROPS;\n  };\n  Path.prototype.isZeroArea = function () {\n    return false;\n  };\n  Path.extend = function (defaultProps) {\n    var Sub = function (_super) {\n      __extends(Sub, _super);\n      function Sub(opts) {\n        var _this = _super.call(this, opts) || this;\n        defaultProps.init && defaultProps.init.call(_this, opts);\n        return _this;\n      }\n      Sub.prototype.getDefaultStyle = function () {\n        return clone(defaultProps.style);\n      };\n      Sub.prototype.getDefaultShape = function () {\n        return clone(defaultProps.shape);\n      };\n      return Sub;\n    }(Path);\n    for (var key in defaultProps) {\n      if (typeof defaultProps[key] === 'function') {\n        Sub.prototype[key] = defaultProps[key];\n      }\n    }\n    return Sub;\n  };\n  Path.initDefaultProps = function () {\n    var pathProto = Path.prototype;\n    pathProto.type = 'path';\n    pathProto.strokeContainThreshold = 5;\n    pathProto.segmentIgnoreThreshold = 0;\n    pathProto.subPixelOptimize = false;\n    pathProto.autoBatch = false;\n    pathProto.__dirty = REDRAW_BIT | STYLE_CHANGED_BIT | SHAPE_CHANGED_BIT;\n  }();\n  return Path;\n}(Displayable);\nexport default Path;", "map": {"version": 3, "names": ["__extends", "Displayable", "DEFAULT_COMMON_STYLE", "DEFAULT_COMMON_ANIMATION_PROPS", "PathProxy", "pathContain", "defaults", "keys", "extend", "clone", "isString", "createObject", "lum", "DARK_LABEL_COLOR", "LIGHT_LABEL_COLOR", "DARK_MODE_THRESHOLD", "LIGHTER_LABEL_COLOR", "REDRAW_BIT", "SHAPE_CHANGED_BIT", "STYLE_CHANGED_BIT", "TRANSFORMABLE_PROPS", "DEFAULT_PATH_STYLE", "fill", "stroke", "strokePercent", "fillOpacity", "strokeOpacity", "lineDashOffset", "lineWidth", "lineCap", "miterLimit", "strokeNoScale", "<PERSON><PERSON><PERSON><PERSON>", "DEFAULT_PATH_ANIMATION_PROPS", "style", "pathCopyParams", "concat", "Path", "_super", "opts", "call", "prototype", "update", "_this", "decal", "decalEl", "_decalEl", "buildPath", "ctx", "shape", "silent", "decalElStyle", "key", "shadowColor", "i", "length", "__dirty", "getDecalElement", "_init", "props", "keysArr", "getDefaultShape", "defaultStyle", "getDefaultStyle", "useStyle", "value", "attrKV", "canBeInsideText", "hasFill", "getInsideTextFill", "pathFill", "fillLum", "getInsideTextStroke", "textFill", "zr", "__zr", "isDarkMode", "isDarkLabel", "shapeCfg", "inBatch", "pathUpdated", "getUpdatedPathProxy", "path", "createPathProxy", "beginPath", "hasStroke", "getBoundingRect", "rect", "_rect", "needsUpdateRect", "firstInvoke", "len", "rectStroke", "_rectStroke", "copy", "lineScale", "getLineScale", "w", "strokeContainThreshold", "Math", "max", "width", "height", "x", "y", "contain", "localPos", "transformCoordToLocal", "pathProxy", "containStroke", "dirtyShape", "mark<PERSON><PERSON><PERSON>", "dirty", "dirtyStyle", "animateShape", "loop", "animate", "updateDuringAnimation", "<PERSON><PERSON><PERSON>", "setShape", "key<PERSON>r<PERSON><PERSON><PERSON>", "shapeChanged", "createStyle", "obj", "_innerSaveToNormal", "toState", "normalState", "_normalState", "_applyStateObj", "stateName", "state", "keepCurrentStates", "transition", "animationCfg", "needsRestoreToNormal", "targetShape", "targetShapePrimaryProps", "shapeKeys", "_transitionState", "_mergeStates", "states", "mergedState", "mergedShape", "_mergeStyle", "getAnimationStyleProps", "isZeroArea", "defaultProps", "Sub", "init", "initDefaultProps", "pathProto", "type", "segmentIgnoreThreshold", "subPixelOptimize", "autoBatch"], "sources": ["C:/WolfSystemV2/WolfOldFrontEnd/node_modules/zrender/lib/graphic/Path.js"], "sourcesContent": ["import { __extends } from \"tslib\";\nimport Displayable, { DEFAULT_COMMON_STYLE, DEFAULT_COMMON_ANIMATION_PROPS } from './Displayable.js';\nimport PathProxy from '../core/PathProxy.js';\nimport * as pathContain from '../contain/path.js';\nimport { defaults, keys, extend, clone, isString, createObject } from '../core/util.js';\nimport { lum } from '../tool/color.js';\nimport { DARK_LABEL_COLOR, LIGHT_LABEL_COLOR, DARK_MODE_THRESHOLD, LIGHTER_LABEL_COLOR } from '../config.js';\nimport { REDRAW_BIT, SHAPE_CHANGED_BIT, STYLE_CHANGED_BIT } from './constants.js';\nimport { TRANSFORMABLE_PROPS } from '../core/Transformable.js';\nexport var DEFAULT_PATH_STYLE = defaults({\n    fill: '#000',\n    stroke: null,\n    strokePercent: 1,\n    fillOpacity: 1,\n    strokeOpacity: 1,\n    lineDashOffset: 0,\n    lineWidth: 1,\n    lineCap: 'butt',\n    miterLimit: 10,\n    strokeNoScale: false,\n    strokeFirst: false\n}, DEFAULT_COMMON_STYLE);\nexport var DEFAULT_PATH_ANIMATION_PROPS = {\n    style: defaults({\n        fill: true,\n        stroke: true,\n        strokePercent: true,\n        fillOpacity: true,\n        strokeOpacity: true,\n        lineDashOffset: true,\n        lineWidth: true,\n        miterLimit: true\n    }, DEFAULT_COMMON_ANIMATION_PROPS.style)\n};\nvar pathCopyParams = TRANSFORMABLE_PROPS.concat(['invisible',\n    'culling', 'z', 'z2', 'zlevel', 'parent'\n]);\nvar Path = (function (_super) {\n    __extends(Path, _super);\n    function Path(opts) {\n        return _super.call(this, opts) || this;\n    }\n    Path.prototype.update = function () {\n        var _this = this;\n        _super.prototype.update.call(this);\n        var style = this.style;\n        if (style.decal) {\n            var decalEl = this._decalEl = this._decalEl || new Path();\n            if (decalEl.buildPath === Path.prototype.buildPath) {\n                decalEl.buildPath = function (ctx) {\n                    _this.buildPath(ctx, _this.shape);\n                };\n            }\n            decalEl.silent = true;\n            var decalElStyle = decalEl.style;\n            for (var key in style) {\n                if (decalElStyle[key] !== style[key]) {\n                    decalElStyle[key] = style[key];\n                }\n            }\n            decalElStyle.fill = style.fill ? style.decal : null;\n            decalElStyle.decal = null;\n            decalElStyle.shadowColor = null;\n            style.strokeFirst && (decalElStyle.stroke = null);\n            for (var i = 0; i < pathCopyParams.length; ++i) {\n                decalEl[pathCopyParams[i]] = this[pathCopyParams[i]];\n            }\n            decalEl.__dirty |= REDRAW_BIT;\n        }\n        else if (this._decalEl) {\n            this._decalEl = null;\n        }\n    };\n    Path.prototype.getDecalElement = function () {\n        return this._decalEl;\n    };\n    Path.prototype._init = function (props) {\n        var keysArr = keys(props);\n        this.shape = this.getDefaultShape();\n        var defaultStyle = this.getDefaultStyle();\n        if (defaultStyle) {\n            this.useStyle(defaultStyle);\n        }\n        for (var i = 0; i < keysArr.length; i++) {\n            var key = keysArr[i];\n            var value = props[key];\n            if (key === 'style') {\n                if (!this.style) {\n                    this.useStyle(value);\n                }\n                else {\n                    extend(this.style, value);\n                }\n            }\n            else if (key === 'shape') {\n                extend(this.shape, value);\n            }\n            else {\n                _super.prototype.attrKV.call(this, key, value);\n            }\n        }\n        if (!this.style) {\n            this.useStyle({});\n        }\n    };\n    Path.prototype.getDefaultStyle = function () {\n        return null;\n    };\n    Path.prototype.getDefaultShape = function () {\n        return {};\n    };\n    Path.prototype.canBeInsideText = function () {\n        return this.hasFill();\n    };\n    Path.prototype.getInsideTextFill = function () {\n        var pathFill = this.style.fill;\n        if (pathFill !== 'none') {\n            if (isString(pathFill)) {\n                var fillLum = lum(pathFill, 0);\n                if (fillLum > 0.5) {\n                    return DARK_LABEL_COLOR;\n                }\n                else if (fillLum > 0.2) {\n                    return LIGHTER_LABEL_COLOR;\n                }\n                return LIGHT_LABEL_COLOR;\n            }\n            else if (pathFill) {\n                return LIGHT_LABEL_COLOR;\n            }\n        }\n        return DARK_LABEL_COLOR;\n    };\n    Path.prototype.getInsideTextStroke = function (textFill) {\n        var pathFill = this.style.fill;\n        if (isString(pathFill)) {\n            var zr = this.__zr;\n            var isDarkMode = !!(zr && zr.isDarkMode());\n            var isDarkLabel = lum(textFill, 0) < DARK_MODE_THRESHOLD;\n            if (isDarkMode === isDarkLabel) {\n                return pathFill;\n            }\n        }\n    };\n    Path.prototype.buildPath = function (ctx, shapeCfg, inBatch) { };\n    Path.prototype.pathUpdated = function () {\n        this.__dirty &= ~SHAPE_CHANGED_BIT;\n    };\n    Path.prototype.getUpdatedPathProxy = function (inBatch) {\n        !this.path && this.createPathProxy();\n        this.path.beginPath();\n        this.buildPath(this.path, this.shape, inBatch);\n        return this.path;\n    };\n    Path.prototype.createPathProxy = function () {\n        this.path = new PathProxy(false);\n    };\n    Path.prototype.hasStroke = function () {\n        var style = this.style;\n        var stroke = style.stroke;\n        return !(stroke == null || stroke === 'none' || !(style.lineWidth > 0));\n    };\n    Path.prototype.hasFill = function () {\n        var style = this.style;\n        var fill = style.fill;\n        return fill != null && fill !== 'none';\n    };\n    Path.prototype.getBoundingRect = function () {\n        var rect = this._rect;\n        var style = this.style;\n        var needsUpdateRect = !rect;\n        if (needsUpdateRect) {\n            var firstInvoke = false;\n            if (!this.path) {\n                firstInvoke = true;\n                this.createPathProxy();\n            }\n            var path = this.path;\n            if (firstInvoke || (this.__dirty & SHAPE_CHANGED_BIT)) {\n                path.beginPath();\n                this.buildPath(path, this.shape, false);\n                this.pathUpdated();\n            }\n            rect = path.getBoundingRect();\n        }\n        this._rect = rect;\n        if (this.hasStroke() && this.path && this.path.len() > 0) {\n            var rectStroke = this._rectStroke || (this._rectStroke = rect.clone());\n            if (this.__dirty || needsUpdateRect) {\n                rectStroke.copy(rect);\n                var lineScale = style.strokeNoScale ? this.getLineScale() : 1;\n                var w = style.lineWidth;\n                if (!this.hasFill()) {\n                    var strokeContainThreshold = this.strokeContainThreshold;\n                    w = Math.max(w, strokeContainThreshold == null ? 4 : strokeContainThreshold);\n                }\n                if (lineScale > 1e-10) {\n                    rectStroke.width += w / lineScale;\n                    rectStroke.height += w / lineScale;\n                    rectStroke.x -= w / lineScale / 2;\n                    rectStroke.y -= w / lineScale / 2;\n                }\n            }\n            return rectStroke;\n        }\n        return rect;\n    };\n    Path.prototype.contain = function (x, y) {\n        var localPos = this.transformCoordToLocal(x, y);\n        var rect = this.getBoundingRect();\n        var style = this.style;\n        x = localPos[0];\n        y = localPos[1];\n        if (rect.contain(x, y)) {\n            var pathProxy = this.path;\n            if (this.hasStroke()) {\n                var lineWidth = style.lineWidth;\n                var lineScale = style.strokeNoScale ? this.getLineScale() : 1;\n                if (lineScale > 1e-10) {\n                    if (!this.hasFill()) {\n                        lineWidth = Math.max(lineWidth, this.strokeContainThreshold);\n                    }\n                    if (pathContain.containStroke(pathProxy, lineWidth / lineScale, x, y)) {\n                        return true;\n                    }\n                }\n            }\n            if (this.hasFill()) {\n                return pathContain.contain(pathProxy, x, y);\n            }\n        }\n        return false;\n    };\n    Path.prototype.dirtyShape = function () {\n        this.__dirty |= SHAPE_CHANGED_BIT;\n        if (this._rect) {\n            this._rect = null;\n        }\n        if (this._decalEl) {\n            this._decalEl.dirtyShape();\n        }\n        this.markRedraw();\n    };\n    Path.prototype.dirty = function () {\n        this.dirtyStyle();\n        this.dirtyShape();\n    };\n    Path.prototype.animateShape = function (loop) {\n        return this.animate('shape', loop);\n    };\n    Path.prototype.updateDuringAnimation = function (targetKey) {\n        if (targetKey === 'style') {\n            this.dirtyStyle();\n        }\n        else if (targetKey === 'shape') {\n            this.dirtyShape();\n        }\n        else {\n            this.markRedraw();\n        }\n    };\n    Path.prototype.attrKV = function (key, value) {\n        if (key === 'shape') {\n            this.setShape(value);\n        }\n        else {\n            _super.prototype.attrKV.call(this, key, value);\n        }\n    };\n    Path.prototype.setShape = function (keyOrObj, value) {\n        var shape = this.shape;\n        if (!shape) {\n            shape = this.shape = {};\n        }\n        if (typeof keyOrObj === 'string') {\n            shape[keyOrObj] = value;\n        }\n        else {\n            extend(shape, keyOrObj);\n        }\n        this.dirtyShape();\n        return this;\n    };\n    Path.prototype.shapeChanged = function () {\n        return !!(this.__dirty & SHAPE_CHANGED_BIT);\n    };\n    Path.prototype.createStyle = function (obj) {\n        return createObject(DEFAULT_PATH_STYLE, obj);\n    };\n    Path.prototype._innerSaveToNormal = function (toState) {\n        _super.prototype._innerSaveToNormal.call(this, toState);\n        var normalState = this._normalState;\n        if (toState.shape && !normalState.shape) {\n            normalState.shape = extend({}, this.shape);\n        }\n    };\n    Path.prototype._applyStateObj = function (stateName, state, normalState, keepCurrentStates, transition, animationCfg) {\n        _super.prototype._applyStateObj.call(this, stateName, state, normalState, keepCurrentStates, transition, animationCfg);\n        var needsRestoreToNormal = !(state && keepCurrentStates);\n        var targetShape;\n        if (state && state.shape) {\n            if (transition) {\n                if (keepCurrentStates) {\n                    targetShape = state.shape;\n                }\n                else {\n                    targetShape = extend({}, normalState.shape);\n                    extend(targetShape, state.shape);\n                }\n            }\n            else {\n                targetShape = extend({}, keepCurrentStates ? this.shape : normalState.shape);\n                extend(targetShape, state.shape);\n            }\n        }\n        else if (needsRestoreToNormal) {\n            targetShape = normalState.shape;\n        }\n        if (targetShape) {\n            if (transition) {\n                this.shape = extend({}, this.shape);\n                var targetShapePrimaryProps = {};\n                var shapeKeys = keys(targetShape);\n                for (var i = 0; i < shapeKeys.length; i++) {\n                    var key = shapeKeys[i];\n                    if (typeof targetShape[key] === 'object') {\n                        this.shape[key] = targetShape[key];\n                    }\n                    else {\n                        targetShapePrimaryProps[key] = targetShape[key];\n                    }\n                }\n                this._transitionState(stateName, {\n                    shape: targetShapePrimaryProps\n                }, animationCfg);\n            }\n            else {\n                this.shape = targetShape;\n                this.dirtyShape();\n            }\n        }\n    };\n    Path.prototype._mergeStates = function (states) {\n        var mergedState = _super.prototype._mergeStates.call(this, states);\n        var mergedShape;\n        for (var i = 0; i < states.length; i++) {\n            var state = states[i];\n            if (state.shape) {\n                mergedShape = mergedShape || {};\n                this._mergeStyle(mergedShape, state.shape);\n            }\n        }\n        if (mergedShape) {\n            mergedState.shape = mergedShape;\n        }\n        return mergedState;\n    };\n    Path.prototype.getAnimationStyleProps = function () {\n        return DEFAULT_PATH_ANIMATION_PROPS;\n    };\n    Path.prototype.isZeroArea = function () {\n        return false;\n    };\n    Path.extend = function (defaultProps) {\n        var Sub = (function (_super) {\n            __extends(Sub, _super);\n            function Sub(opts) {\n                var _this = _super.call(this, opts) || this;\n                defaultProps.init && defaultProps.init.call(_this, opts);\n                return _this;\n            }\n            Sub.prototype.getDefaultStyle = function () {\n                return clone(defaultProps.style);\n            };\n            Sub.prototype.getDefaultShape = function () {\n                return clone(defaultProps.shape);\n            };\n            return Sub;\n        }(Path));\n        for (var key in defaultProps) {\n            if (typeof defaultProps[key] === 'function') {\n                Sub.prototype[key] = defaultProps[key];\n            }\n        }\n        return Sub;\n    };\n    Path.initDefaultProps = (function () {\n        var pathProto = Path.prototype;\n        pathProto.type = 'path';\n        pathProto.strokeContainThreshold = 5;\n        pathProto.segmentIgnoreThreshold = 0;\n        pathProto.subPixelOptimize = false;\n        pathProto.autoBatch = false;\n        pathProto.__dirty = REDRAW_BIT | STYLE_CHANGED_BIT | SHAPE_CHANGED_BIT;\n    })();\n    return Path;\n}(Displayable));\nexport default Path;\n"], "mappings": "AAAA,SAASA,SAAS,QAAQ,OAAO;AACjC,OAAOC,WAAW,IAAIC,oBAAoB,EAAEC,8BAA8B,QAAQ,kBAAkB;AACpG,OAAOC,SAAS,MAAM,sBAAsB;AAC5C,OAAO,KAAKC,WAAW,MAAM,oBAAoB;AACjD,SAASC,QAAQ,EAAEC,IAAI,EAAEC,MAAM,EAAEC,KAAK,EAAEC,QAAQ,EAAEC,YAAY,QAAQ,iBAAiB;AACvF,SAASC,GAAG,QAAQ,kBAAkB;AACtC,SAASC,gBAAgB,EAAEC,iBAAiB,EAAEC,mBAAmB,EAAEC,mBAAmB,QAAQ,cAAc;AAC5G,SAASC,UAAU,EAAEC,iBAAiB,EAAEC,iBAAiB,QAAQ,gBAAgB;AACjF,SAASC,mBAAmB,QAAQ,0BAA0B;AAC9D,OAAO,IAAIC,kBAAkB,GAAGf,QAAQ,CAAC;EACrCgB,IAAI,EAAE,MAAM;EACZC,MAAM,EAAE,IAAI;EACZC,aAAa,EAAE,CAAC;EAChBC,WAAW,EAAE,CAAC;EACdC,aAAa,EAAE,CAAC;EAChBC,cAAc,EAAE,CAAC;EACjBC,SAAS,EAAE,CAAC;EACZC,OAAO,EAAE,MAAM;EACfC,UAAU,EAAE,EAAE;EACdC,aAAa,EAAE,KAAK;EACpBC,WAAW,EAAE;AACjB,CAAC,EAAE9B,oBAAoB,CAAC;AACxB,OAAO,IAAI+B,4BAA4B,GAAG;EACtCC,KAAK,EAAE5B,QAAQ,CAAC;IACZgB,IAAI,EAAE,IAAI;IACVC,MAAM,EAAE,IAAI;IACZC,aAAa,EAAE,IAAI;IACnBC,WAAW,EAAE,IAAI;IACjBC,aAAa,EAAE,IAAI;IACnBC,cAAc,EAAE,IAAI;IACpBC,SAAS,EAAE,IAAI;IACfE,UAAU,EAAE;EAChB,CAAC,EAAE3B,8BAA8B,CAAC+B,KAAK;AAC3C,CAAC;AACD,IAAIC,cAAc,GAAGf,mBAAmB,CAACgB,MAAM,CAAC,CAAC,WAAW,EACxD,SAAS,EAAE,GAAG,EAAE,IAAI,EAAE,QAAQ,EAAE,QAAQ,CAC3C,CAAC;AACF,IAAIC,IAAI,GAAI,UAAUC,MAAM,EAAE;EAC1BtC,SAAS,CAACqC,IAAI,EAAEC,MAAM,CAAC;EACvB,SAASD,IAAIA,CAACE,IAAI,EAAE;IAChB,OAAOD,MAAM,CAACE,IAAI,CAAC,IAAI,EAAED,IAAI,CAAC,IAAI,IAAI;EAC1C;EACAF,IAAI,CAACI,SAAS,CAACC,MAAM,GAAG,YAAY;IAChC,IAAIC,KAAK,GAAG,IAAI;IAChBL,MAAM,CAACG,SAAS,CAACC,MAAM,CAACF,IAAI,CAAC,IAAI,CAAC;IAClC,IAAIN,KAAK,GAAG,IAAI,CAACA,KAAK;IACtB,IAAIA,KAAK,CAACU,KAAK,EAAE;MACb,IAAIC,OAAO,GAAG,IAAI,CAACC,QAAQ,GAAG,IAAI,CAACA,QAAQ,IAAI,IAAIT,IAAI,CAAC,CAAC;MACzD,IAAIQ,OAAO,CAACE,SAAS,KAAKV,IAAI,CAACI,SAAS,CAACM,SAAS,EAAE;QAChDF,OAAO,CAACE,SAAS,GAAG,UAAUC,GAAG,EAAE;UAC/BL,KAAK,CAACI,SAAS,CAACC,GAAG,EAAEL,KAAK,CAACM,KAAK,CAAC;QACrC,CAAC;MACL;MACAJ,OAAO,CAACK,MAAM,GAAG,IAAI;MACrB,IAAIC,YAAY,GAAGN,OAAO,CAACX,KAAK;MAChC,KAAK,IAAIkB,GAAG,IAAIlB,KAAK,EAAE;QACnB,IAAIiB,YAAY,CAACC,GAAG,CAAC,KAAKlB,KAAK,CAACkB,GAAG,CAAC,EAAE;UAClCD,YAAY,CAACC,GAAG,CAAC,GAAGlB,KAAK,CAACkB,GAAG,CAAC;QAClC;MACJ;MACAD,YAAY,CAAC7B,IAAI,GAAGY,KAAK,CAACZ,IAAI,GAAGY,KAAK,CAACU,KAAK,GAAG,IAAI;MACnDO,YAAY,CAACP,KAAK,GAAG,IAAI;MACzBO,YAAY,CAACE,WAAW,GAAG,IAAI;MAC/BnB,KAAK,CAACF,WAAW,KAAKmB,YAAY,CAAC5B,MAAM,GAAG,IAAI,CAAC;MACjD,KAAK,IAAI+B,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGnB,cAAc,CAACoB,MAAM,EAAE,EAAED,CAAC,EAAE;QAC5CT,OAAO,CAACV,cAAc,CAACmB,CAAC,CAAC,CAAC,GAAG,IAAI,CAACnB,cAAc,CAACmB,CAAC,CAAC,CAAC;MACxD;MACAT,OAAO,CAACW,OAAO,IAAIvC,UAAU;IACjC,CAAC,MACI,IAAI,IAAI,CAAC6B,QAAQ,EAAE;MACpB,IAAI,CAACA,QAAQ,GAAG,IAAI;IACxB;EACJ,CAAC;EACDT,IAAI,CAACI,SAAS,CAACgB,eAAe,GAAG,YAAY;IACzC,OAAO,IAAI,CAACX,QAAQ;EACxB,CAAC;EACDT,IAAI,CAACI,SAAS,CAACiB,KAAK,GAAG,UAAUC,KAAK,EAAE;IACpC,IAAIC,OAAO,GAAGrD,IAAI,CAACoD,KAAK,CAAC;IACzB,IAAI,CAACV,KAAK,GAAG,IAAI,CAACY,eAAe,CAAC,CAAC;IACnC,IAAIC,YAAY,GAAG,IAAI,CAACC,eAAe,CAAC,CAAC;IACzC,IAAID,YAAY,EAAE;MACd,IAAI,CAACE,QAAQ,CAACF,YAAY,CAAC;IAC/B;IACA,KAAK,IAAIR,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGM,OAAO,CAACL,MAAM,EAAED,CAAC,EAAE,EAAE;MACrC,IAAIF,GAAG,GAAGQ,OAAO,CAACN,CAAC,CAAC;MACpB,IAAIW,KAAK,GAAGN,KAAK,CAACP,GAAG,CAAC;MACtB,IAAIA,GAAG,KAAK,OAAO,EAAE;QACjB,IAAI,CAAC,IAAI,CAAClB,KAAK,EAAE;UACb,IAAI,CAAC8B,QAAQ,CAACC,KAAK,CAAC;QACxB,CAAC,MACI;UACDzD,MAAM,CAAC,IAAI,CAAC0B,KAAK,EAAE+B,KAAK,CAAC;QAC7B;MACJ,CAAC,MACI,IAAIb,GAAG,KAAK,OAAO,EAAE;QACtB5C,MAAM,CAAC,IAAI,CAACyC,KAAK,EAAEgB,KAAK,CAAC;MAC7B,CAAC,MACI;QACD3B,MAAM,CAACG,SAAS,CAACyB,MAAM,CAAC1B,IAAI,CAAC,IAAI,EAAEY,GAAG,EAAEa,KAAK,CAAC;MAClD;IACJ;IACA,IAAI,CAAC,IAAI,CAAC/B,KAAK,EAAE;MACb,IAAI,CAAC8B,QAAQ,CAAC,CAAC,CAAC,CAAC;IACrB;EACJ,CAAC;EACD3B,IAAI,CAACI,SAAS,CAACsB,eAAe,GAAG,YAAY;IACzC,OAAO,IAAI;EACf,CAAC;EACD1B,IAAI,CAACI,SAAS,CAACoB,eAAe,GAAG,YAAY;IACzC,OAAO,CAAC,CAAC;EACb,CAAC;EACDxB,IAAI,CAACI,SAAS,CAAC0B,eAAe,GAAG,YAAY;IACzC,OAAO,IAAI,CAACC,OAAO,CAAC,CAAC;EACzB,CAAC;EACD/B,IAAI,CAACI,SAAS,CAAC4B,iBAAiB,GAAG,YAAY;IAC3C,IAAIC,QAAQ,GAAG,IAAI,CAACpC,KAAK,CAACZ,IAAI;IAC9B,IAAIgD,QAAQ,KAAK,MAAM,EAAE;MACrB,IAAI5D,QAAQ,CAAC4D,QAAQ,CAAC,EAAE;QACpB,IAAIC,OAAO,GAAG3D,GAAG,CAAC0D,QAAQ,EAAE,CAAC,CAAC;QAC9B,IAAIC,OAAO,GAAG,GAAG,EAAE;UACf,OAAO1D,gBAAgB;QAC3B,CAAC,MACI,IAAI0D,OAAO,GAAG,GAAG,EAAE;UACpB,OAAOvD,mBAAmB;QAC9B;QACA,OAAOF,iBAAiB;MAC5B,CAAC,MACI,IAAIwD,QAAQ,EAAE;QACf,OAAOxD,iBAAiB;MAC5B;IACJ;IACA,OAAOD,gBAAgB;EAC3B,CAAC;EACDwB,IAAI,CAACI,SAAS,CAAC+B,mBAAmB,GAAG,UAAUC,QAAQ,EAAE;IACrD,IAAIH,QAAQ,GAAG,IAAI,CAACpC,KAAK,CAACZ,IAAI;IAC9B,IAAIZ,QAAQ,CAAC4D,QAAQ,CAAC,EAAE;MACpB,IAAII,EAAE,GAAG,IAAI,CAACC,IAAI;MAClB,IAAIC,UAAU,GAAG,CAAC,EAAEF,EAAE,IAAIA,EAAE,CAACE,UAAU,CAAC,CAAC,CAAC;MAC1C,IAAIC,WAAW,GAAGjE,GAAG,CAAC6D,QAAQ,EAAE,CAAC,CAAC,GAAG1D,mBAAmB;MACxD,IAAI6D,UAAU,KAAKC,WAAW,EAAE;QAC5B,OAAOP,QAAQ;MACnB;IACJ;EACJ,CAAC;EACDjC,IAAI,CAACI,SAAS,CAACM,SAAS,GAAG,UAAUC,GAAG,EAAE8B,QAAQ,EAAEC,OAAO,EAAE,CAAE,CAAC;EAChE1C,IAAI,CAACI,SAAS,CAACuC,WAAW,GAAG,YAAY;IACrC,IAAI,CAACxB,OAAO,IAAI,CAACtC,iBAAiB;EACtC,CAAC;EACDmB,IAAI,CAACI,SAAS,CAACwC,mBAAmB,GAAG,UAAUF,OAAO,EAAE;IACpD,CAAC,IAAI,CAACG,IAAI,IAAI,IAAI,CAACC,eAAe,CAAC,CAAC;IACpC,IAAI,CAACD,IAAI,CAACE,SAAS,CAAC,CAAC;IACrB,IAAI,CAACrC,SAAS,CAAC,IAAI,CAACmC,IAAI,EAAE,IAAI,CAACjC,KAAK,EAAE8B,OAAO,CAAC;IAC9C,OAAO,IAAI,CAACG,IAAI;EACpB,CAAC;EACD7C,IAAI,CAACI,SAAS,CAAC0C,eAAe,GAAG,YAAY;IACzC,IAAI,CAACD,IAAI,GAAG,IAAI9E,SAAS,CAAC,KAAK,CAAC;EACpC,CAAC;EACDiC,IAAI,CAACI,SAAS,CAAC4C,SAAS,GAAG,YAAY;IACnC,IAAInD,KAAK,GAAG,IAAI,CAACA,KAAK;IACtB,IAAIX,MAAM,GAAGW,KAAK,CAACX,MAAM;IACzB,OAAO,EAAEA,MAAM,IAAI,IAAI,IAAIA,MAAM,KAAK,MAAM,IAAI,EAAEW,KAAK,CAACN,SAAS,GAAG,CAAC,CAAC,CAAC;EAC3E,CAAC;EACDS,IAAI,CAACI,SAAS,CAAC2B,OAAO,GAAG,YAAY;IACjC,IAAIlC,KAAK,GAAG,IAAI,CAACA,KAAK;IACtB,IAAIZ,IAAI,GAAGY,KAAK,CAACZ,IAAI;IACrB,OAAOA,IAAI,IAAI,IAAI,IAAIA,IAAI,KAAK,MAAM;EAC1C,CAAC;EACDe,IAAI,CAACI,SAAS,CAAC6C,eAAe,GAAG,YAAY;IACzC,IAAIC,IAAI,GAAG,IAAI,CAACC,KAAK;IACrB,IAAItD,KAAK,GAAG,IAAI,CAACA,KAAK;IACtB,IAAIuD,eAAe,GAAG,CAACF,IAAI;IAC3B,IAAIE,eAAe,EAAE;MACjB,IAAIC,WAAW,GAAG,KAAK;MACvB,IAAI,CAAC,IAAI,CAACR,IAAI,EAAE;QACZQ,WAAW,GAAG,IAAI;QAClB,IAAI,CAACP,eAAe,CAAC,CAAC;MAC1B;MACA,IAAID,IAAI,GAAG,IAAI,CAACA,IAAI;MACpB,IAAIQ,WAAW,IAAK,IAAI,CAAClC,OAAO,GAAGtC,iBAAkB,EAAE;QACnDgE,IAAI,CAACE,SAAS,CAAC,CAAC;QAChB,IAAI,CAACrC,SAAS,CAACmC,IAAI,EAAE,IAAI,CAACjC,KAAK,EAAE,KAAK,CAAC;QACvC,IAAI,CAAC+B,WAAW,CAAC,CAAC;MACtB;MACAO,IAAI,GAAGL,IAAI,CAACI,eAAe,CAAC,CAAC;IACjC;IACA,IAAI,CAACE,KAAK,GAAGD,IAAI;IACjB,IAAI,IAAI,CAACF,SAAS,CAAC,CAAC,IAAI,IAAI,CAACH,IAAI,IAAI,IAAI,CAACA,IAAI,CAACS,GAAG,CAAC,CAAC,GAAG,CAAC,EAAE;MACtD,IAAIC,UAAU,GAAG,IAAI,CAACC,WAAW,KAAK,IAAI,CAACA,WAAW,GAAGN,IAAI,CAAC9E,KAAK,CAAC,CAAC,CAAC;MACtE,IAAI,IAAI,CAAC+C,OAAO,IAAIiC,eAAe,EAAE;QACjCG,UAAU,CAACE,IAAI,CAACP,IAAI,CAAC;QACrB,IAAIQ,SAAS,GAAG7D,KAAK,CAACH,aAAa,GAAG,IAAI,CAACiE,YAAY,CAAC,CAAC,GAAG,CAAC;QAC7D,IAAIC,CAAC,GAAG/D,KAAK,CAACN,SAAS;QACvB,IAAI,CAAC,IAAI,CAACwC,OAAO,CAAC,CAAC,EAAE;UACjB,IAAI8B,sBAAsB,GAAG,IAAI,CAACA,sBAAsB;UACxDD,CAAC,GAAGE,IAAI,CAACC,GAAG,CAACH,CAAC,EAAEC,sBAAsB,IAAI,IAAI,GAAG,CAAC,GAAGA,sBAAsB,CAAC;QAChF;QACA,IAAIH,SAAS,GAAG,KAAK,EAAE;UACnBH,UAAU,CAACS,KAAK,IAAIJ,CAAC,GAAGF,SAAS;UACjCH,UAAU,CAACU,MAAM,IAAIL,CAAC,GAAGF,SAAS;UAClCH,UAAU,CAACW,CAAC,IAAIN,CAAC,GAAGF,SAAS,GAAG,CAAC;UACjCH,UAAU,CAACY,CAAC,IAAIP,CAAC,GAAGF,SAAS,GAAG,CAAC;QACrC;MACJ;MACA,OAAOH,UAAU;IACrB;IACA,OAAOL,IAAI;EACf,CAAC;EACDlD,IAAI,CAACI,SAAS,CAACgE,OAAO,GAAG,UAAUF,CAAC,EAAEC,CAAC,EAAE;IACrC,IAAIE,QAAQ,GAAG,IAAI,CAACC,qBAAqB,CAACJ,CAAC,EAAEC,CAAC,CAAC;IAC/C,IAAIjB,IAAI,GAAG,IAAI,CAACD,eAAe,CAAC,CAAC;IACjC,IAAIpD,KAAK,GAAG,IAAI,CAACA,KAAK;IACtBqE,CAAC,GAAGG,QAAQ,CAAC,CAAC,CAAC;IACfF,CAAC,GAAGE,QAAQ,CAAC,CAAC,CAAC;IACf,IAAInB,IAAI,CAACkB,OAAO,CAACF,CAAC,EAAEC,CAAC,CAAC,EAAE;MACpB,IAAII,SAAS,GAAG,IAAI,CAAC1B,IAAI;MACzB,IAAI,IAAI,CAACG,SAAS,CAAC,CAAC,EAAE;QAClB,IAAIzD,SAAS,GAAGM,KAAK,CAACN,SAAS;QAC/B,IAAImE,SAAS,GAAG7D,KAAK,CAACH,aAAa,GAAG,IAAI,CAACiE,YAAY,CAAC,CAAC,GAAG,CAAC;QAC7D,IAAID,SAAS,GAAG,KAAK,EAAE;UACnB,IAAI,CAAC,IAAI,CAAC3B,OAAO,CAAC,CAAC,EAAE;YACjBxC,SAAS,GAAGuE,IAAI,CAACC,GAAG,CAACxE,SAAS,EAAE,IAAI,CAACsE,sBAAsB,CAAC;UAChE;UACA,IAAI7F,WAAW,CAACwG,aAAa,CAACD,SAAS,EAAEhF,SAAS,GAAGmE,SAAS,EAAEQ,CAAC,EAAEC,CAAC,CAAC,EAAE;YACnE,OAAO,IAAI;UACf;QACJ;MACJ;MACA,IAAI,IAAI,CAACpC,OAAO,CAAC,CAAC,EAAE;QAChB,OAAO/D,WAAW,CAACoG,OAAO,CAACG,SAAS,EAAEL,CAAC,EAAEC,CAAC,CAAC;MAC/C;IACJ;IACA,OAAO,KAAK;EAChB,CAAC;EACDnE,IAAI,CAACI,SAAS,CAACqE,UAAU,GAAG,YAAY;IACpC,IAAI,CAACtD,OAAO,IAAItC,iBAAiB;IACjC,IAAI,IAAI,CAACsE,KAAK,EAAE;MACZ,IAAI,CAACA,KAAK,GAAG,IAAI;IACrB;IACA,IAAI,IAAI,CAAC1C,QAAQ,EAAE;MACf,IAAI,CAACA,QAAQ,CAACgE,UAAU,CAAC,CAAC;IAC9B;IACA,IAAI,CAACC,UAAU,CAAC,CAAC;EACrB,CAAC;EACD1E,IAAI,CAACI,SAAS,CAACuE,KAAK,GAAG,YAAY;IAC/B,IAAI,CAACC,UAAU,CAAC,CAAC;IACjB,IAAI,CAACH,UAAU,CAAC,CAAC;EACrB,CAAC;EACDzE,IAAI,CAACI,SAAS,CAACyE,YAAY,GAAG,UAAUC,IAAI,EAAE;IAC1C,OAAO,IAAI,CAACC,OAAO,CAAC,OAAO,EAAED,IAAI,CAAC;EACtC,CAAC;EACD9E,IAAI,CAACI,SAAS,CAAC4E,qBAAqB,GAAG,UAAUC,SAAS,EAAE;IACxD,IAAIA,SAAS,KAAK,OAAO,EAAE;MACvB,IAAI,CAACL,UAAU,CAAC,CAAC;IACrB,CAAC,MACI,IAAIK,SAAS,KAAK,OAAO,EAAE;MAC5B,IAAI,CAACR,UAAU,CAAC,CAAC;IACrB,CAAC,MACI;MACD,IAAI,CAACC,UAAU,CAAC,CAAC;IACrB;EACJ,CAAC;EACD1E,IAAI,CAACI,SAAS,CAACyB,MAAM,GAAG,UAAUd,GAAG,EAAEa,KAAK,EAAE;IAC1C,IAAIb,GAAG,KAAK,OAAO,EAAE;MACjB,IAAI,CAACmE,QAAQ,CAACtD,KAAK,CAAC;IACxB,CAAC,MACI;MACD3B,MAAM,CAACG,SAAS,CAACyB,MAAM,CAAC1B,IAAI,CAAC,IAAI,EAAEY,GAAG,EAAEa,KAAK,CAAC;IAClD;EACJ,CAAC;EACD5B,IAAI,CAACI,SAAS,CAAC8E,QAAQ,GAAG,UAAUC,QAAQ,EAAEvD,KAAK,EAAE;IACjD,IAAIhB,KAAK,GAAG,IAAI,CAACA,KAAK;IACtB,IAAI,CAACA,KAAK,EAAE;MACRA,KAAK,GAAG,IAAI,CAACA,KAAK,GAAG,CAAC,CAAC;IAC3B;IACA,IAAI,OAAOuE,QAAQ,KAAK,QAAQ,EAAE;MAC9BvE,KAAK,CAACuE,QAAQ,CAAC,GAAGvD,KAAK;IAC3B,CAAC,MACI;MACDzD,MAAM,CAACyC,KAAK,EAAEuE,QAAQ,CAAC;IAC3B;IACA,IAAI,CAACV,UAAU,CAAC,CAAC;IACjB,OAAO,IAAI;EACf,CAAC;EACDzE,IAAI,CAACI,SAAS,CAACgF,YAAY,GAAG,YAAY;IACtC,OAAO,CAAC,EAAE,IAAI,CAACjE,OAAO,GAAGtC,iBAAiB,CAAC;EAC/C,CAAC;EACDmB,IAAI,CAACI,SAAS,CAACiF,WAAW,GAAG,UAAUC,GAAG,EAAE;IACxC,OAAOhH,YAAY,CAACU,kBAAkB,EAAEsG,GAAG,CAAC;EAChD,CAAC;EACDtF,IAAI,CAACI,SAAS,CAACmF,kBAAkB,GAAG,UAAUC,OAAO,EAAE;IACnDvF,MAAM,CAACG,SAAS,CAACmF,kBAAkB,CAACpF,IAAI,CAAC,IAAI,EAAEqF,OAAO,CAAC;IACvD,IAAIC,WAAW,GAAG,IAAI,CAACC,YAAY;IACnC,IAAIF,OAAO,CAAC5E,KAAK,IAAI,CAAC6E,WAAW,CAAC7E,KAAK,EAAE;MACrC6E,WAAW,CAAC7E,KAAK,GAAGzC,MAAM,CAAC,CAAC,CAAC,EAAE,IAAI,CAACyC,KAAK,CAAC;IAC9C;EACJ,CAAC;EACDZ,IAAI,CAACI,SAAS,CAACuF,cAAc,GAAG,UAAUC,SAAS,EAAEC,KAAK,EAAEJ,WAAW,EAAEK,iBAAiB,EAAEC,UAAU,EAAEC,YAAY,EAAE;IAClH/F,MAAM,CAACG,SAAS,CAACuF,cAAc,CAACxF,IAAI,CAAC,IAAI,EAAEyF,SAAS,EAAEC,KAAK,EAAEJ,WAAW,EAAEK,iBAAiB,EAAEC,UAAU,EAAEC,YAAY,CAAC;IACtH,IAAIC,oBAAoB,GAAG,EAAEJ,KAAK,IAAIC,iBAAiB,CAAC;IACxD,IAAII,WAAW;IACf,IAAIL,KAAK,IAAIA,KAAK,CAACjF,KAAK,EAAE;MACtB,IAAImF,UAAU,EAAE;QACZ,IAAID,iBAAiB,EAAE;UACnBI,WAAW,GAAGL,KAAK,CAACjF,KAAK;QAC7B,CAAC,MACI;UACDsF,WAAW,GAAG/H,MAAM,CAAC,CAAC,CAAC,EAAEsH,WAAW,CAAC7E,KAAK,CAAC;UAC3CzC,MAAM,CAAC+H,WAAW,EAAEL,KAAK,CAACjF,KAAK,CAAC;QACpC;MACJ,CAAC,MACI;QACDsF,WAAW,GAAG/H,MAAM,CAAC,CAAC,CAAC,EAAE2H,iBAAiB,GAAG,IAAI,CAAClF,KAAK,GAAG6E,WAAW,CAAC7E,KAAK,CAAC;QAC5EzC,MAAM,CAAC+H,WAAW,EAAEL,KAAK,CAACjF,KAAK,CAAC;MACpC;IACJ,CAAC,MACI,IAAIqF,oBAAoB,EAAE;MAC3BC,WAAW,GAAGT,WAAW,CAAC7E,KAAK;IACnC;IACA,IAAIsF,WAAW,EAAE;MACb,IAAIH,UAAU,EAAE;QACZ,IAAI,CAACnF,KAAK,GAAGzC,MAAM,CAAC,CAAC,CAAC,EAAE,IAAI,CAACyC,KAAK,CAAC;QACnC,IAAIuF,uBAAuB,GAAG,CAAC,CAAC;QAChC,IAAIC,SAAS,GAAGlI,IAAI,CAACgI,WAAW,CAAC;QACjC,KAAK,IAAIjF,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGmF,SAAS,CAAClF,MAAM,EAAED,CAAC,EAAE,EAAE;UACvC,IAAIF,GAAG,GAAGqF,SAAS,CAACnF,CAAC,CAAC;UACtB,IAAI,OAAOiF,WAAW,CAACnF,GAAG,CAAC,KAAK,QAAQ,EAAE;YACtC,IAAI,CAACH,KAAK,CAACG,GAAG,CAAC,GAAGmF,WAAW,CAACnF,GAAG,CAAC;UACtC,CAAC,MACI;YACDoF,uBAAuB,CAACpF,GAAG,CAAC,GAAGmF,WAAW,CAACnF,GAAG,CAAC;UACnD;QACJ;QACA,IAAI,CAACsF,gBAAgB,CAACT,SAAS,EAAE;UAC7BhF,KAAK,EAAEuF;QACX,CAAC,EAAEH,YAAY,CAAC;MACpB,CAAC,MACI;QACD,IAAI,CAACpF,KAAK,GAAGsF,WAAW;QACxB,IAAI,CAACzB,UAAU,CAAC,CAAC;MACrB;IACJ;EACJ,CAAC;EACDzE,IAAI,CAACI,SAAS,CAACkG,YAAY,GAAG,UAAUC,MAAM,EAAE;IAC5C,IAAIC,WAAW,GAAGvG,MAAM,CAACG,SAAS,CAACkG,YAAY,CAACnG,IAAI,CAAC,IAAI,EAAEoG,MAAM,CAAC;IAClE,IAAIE,WAAW;IACf,KAAK,IAAIxF,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGsF,MAAM,CAACrF,MAAM,EAAED,CAAC,EAAE,EAAE;MACpC,IAAI4E,KAAK,GAAGU,MAAM,CAACtF,CAAC,CAAC;MACrB,IAAI4E,KAAK,CAACjF,KAAK,EAAE;QACb6F,WAAW,GAAGA,WAAW,IAAI,CAAC,CAAC;QAC/B,IAAI,CAACC,WAAW,CAACD,WAAW,EAAEZ,KAAK,CAACjF,KAAK,CAAC;MAC9C;IACJ;IACA,IAAI6F,WAAW,EAAE;MACbD,WAAW,CAAC5F,KAAK,GAAG6F,WAAW;IACnC;IACA,OAAOD,WAAW;EACtB,CAAC;EACDxG,IAAI,CAACI,SAAS,CAACuG,sBAAsB,GAAG,YAAY;IAChD,OAAO/G,4BAA4B;EACvC,CAAC;EACDI,IAAI,CAACI,SAAS,CAACwG,UAAU,GAAG,YAAY;IACpC,OAAO,KAAK;EAChB,CAAC;EACD5G,IAAI,CAAC7B,MAAM,GAAG,UAAU0I,YAAY,EAAE;IAClC,IAAIC,GAAG,GAAI,UAAU7G,MAAM,EAAE;MACzBtC,SAAS,CAACmJ,GAAG,EAAE7G,MAAM,CAAC;MACtB,SAAS6G,GAAGA,CAAC5G,IAAI,EAAE;QACf,IAAII,KAAK,GAAGL,MAAM,CAACE,IAAI,CAAC,IAAI,EAAED,IAAI,CAAC,IAAI,IAAI;QAC3C2G,YAAY,CAACE,IAAI,IAAIF,YAAY,CAACE,IAAI,CAAC5G,IAAI,CAACG,KAAK,EAAEJ,IAAI,CAAC;QACxD,OAAOI,KAAK;MAChB;MACAwG,GAAG,CAAC1G,SAAS,CAACsB,eAAe,GAAG,YAAY;QACxC,OAAOtD,KAAK,CAACyI,YAAY,CAAChH,KAAK,CAAC;MACpC,CAAC;MACDiH,GAAG,CAAC1G,SAAS,CAACoB,eAAe,GAAG,YAAY;QACxC,OAAOpD,KAAK,CAACyI,YAAY,CAACjG,KAAK,CAAC;MACpC,CAAC;MACD,OAAOkG,GAAG;IACd,CAAC,CAAC9G,IAAI,CAAE;IACR,KAAK,IAAIe,GAAG,IAAI8F,YAAY,EAAE;MAC1B,IAAI,OAAOA,YAAY,CAAC9F,GAAG,CAAC,KAAK,UAAU,EAAE;QACzC+F,GAAG,CAAC1G,SAAS,CAACW,GAAG,CAAC,GAAG8F,YAAY,CAAC9F,GAAG,CAAC;MAC1C;IACJ;IACA,OAAO+F,GAAG;EACd,CAAC;EACD9G,IAAI,CAACgH,gBAAgB,GAAI,YAAY;IACjC,IAAIC,SAAS,GAAGjH,IAAI,CAACI,SAAS;IAC9B6G,SAAS,CAACC,IAAI,GAAG,MAAM;IACvBD,SAAS,CAACpD,sBAAsB,GAAG,CAAC;IACpCoD,SAAS,CAACE,sBAAsB,GAAG,CAAC;IACpCF,SAAS,CAACG,gBAAgB,GAAG,KAAK;IAClCH,SAAS,CAACI,SAAS,GAAG,KAAK;IAC3BJ,SAAS,CAAC9F,OAAO,GAAGvC,UAAU,GAAGE,iBAAiB,GAAGD,iBAAiB;EAC1E,CAAC,CAAE,CAAC;EACJ,OAAOmB,IAAI;AACf,CAAC,CAACpC,WAAW,CAAE;AACf,eAAeoC,IAAI"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}