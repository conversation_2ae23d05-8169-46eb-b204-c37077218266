{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport ComponentModel from '../../model/Component.js';\nvar ParallelModel = /** @class */\nfunction (_super) {\n  __extends(ParallelModel, _super);\n  function ParallelModel() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.type = ParallelModel.type;\n    return _this;\n  }\n  ParallelModel.prototype.init = function () {\n    _super.prototype.init.apply(this, arguments);\n    this.mergeOption({});\n  };\n  ParallelModel.prototype.mergeOption = function (newOption) {\n    var thisOption = this.option;\n    newOption && zrUtil.merge(thisOption, newOption, true);\n    this._initDimensions();\n  };\n  /**\r\n   * Whether series or axis is in this coordinate system.\r\n   */\n\n  ParallelModel.prototype.contains = function (model, ecModel) {\n    var parallelIndex = model.get('parallelIndex');\n    return parallelIndex != null && ecModel.getComponent('parallel', parallelIndex) === this;\n  };\n  ParallelModel.prototype.setAxisExpand = function (opt) {\n    zrUtil.each(['axisExpandable', 'axisExpandCenter', 'axisExpandCount', 'axisExpandWidth', 'axisExpandWindow'], function (name) {\n      if (opt.hasOwnProperty(name)) {\n        // @ts-ignore FIXME: why \"never\" inferred in this.option[name]?\n        this.option[name] = opt[name];\n      }\n    }, this);\n  };\n  ParallelModel.prototype._initDimensions = function () {\n    var dimensions = this.dimensions = [];\n    var parallelAxisIndex = this.parallelAxisIndex = [];\n    var axisModels = zrUtil.filter(this.ecModel.queryComponents({\n      mainType: 'parallelAxis'\n    }), function (axisModel) {\n      // Can not use this.contains here, because\n      // initialization has not been completed yet.\n      return (axisModel.get('parallelIndex') || 0) === this.componentIndex;\n    }, this);\n    zrUtil.each(axisModels, function (axisModel) {\n      dimensions.push('dim' + axisModel.get('dim'));\n      parallelAxisIndex.push(axisModel.componentIndex);\n    });\n  };\n  ParallelModel.type = 'parallel';\n  ParallelModel.dependencies = ['parallelAxis'];\n  ParallelModel.layoutMode = 'box';\n  ParallelModel.defaultOption = {\n    // zlevel: 0,\n    z: 0,\n    left: 80,\n    top: 60,\n    right: 80,\n    bottom: 60,\n    // width: {totalWidth} - left - right,\n    // height: {totalHeight} - top - bottom,\n    layout: 'horizontal',\n    // FIXME\n    // naming?\n    axisExpandable: false,\n    axisExpandCenter: null,\n    axisExpandCount: 0,\n    axisExpandWidth: 50,\n    axisExpandRate: 17,\n    axisExpandDebounce: 50,\n    // [out, in, jumpTarget]. In percentage. If use [null, 0.05], null means full.\n    // Do not doc to user until necessary.\n    axisExpandSlideTriggerArea: [-0.15, 0.05, 0.4],\n    axisExpandTriggerOn: 'click',\n    parallelAxisDefault: null\n  };\n  return ParallelModel;\n}(ComponentModel);\nexport default ParallelModel;", "map": {"version": 3, "names": ["__extends", "zrUtil", "ComponentModel", "ParallelModel", "_super", "_this", "apply", "arguments", "type", "prototype", "init", "mergeOption", "newOption", "thisOption", "option", "merge", "_initDimensions", "contains", "model", "ecModel", "parallelIndex", "get", "getComponent", "setAxisExpand", "opt", "each", "name", "hasOwnProperty", "dimensions", "parallelAxisIndex", "axisModels", "filter", "queryComponents", "mainType", "axisModel", "componentIndex", "push", "dependencies", "layoutMode", "defaultOption", "z", "left", "top", "right", "bottom", "layout", "axisExpandable", "axisExpandCenter", "axisExpandCount", "axisExpandWidth", "axisExpandRate", "axisExpandDebounce", "axisExpandSlideTriggerArea", "axisExpandTriggerOn", "parallelAxisDefault"], "sources": ["C:/WolfSystemV2/WolfOldFrontEnd/node_modules/echarts/lib/coord/parallel/ParallelModel.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport ComponentModel from '../../model/Component.js';\n\nvar ParallelModel =\n/** @class */\nfunction (_super) {\n  __extends(ParallelModel, _super);\n\n  function ParallelModel() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n\n    _this.type = ParallelModel.type;\n    return _this;\n  }\n\n  ParallelModel.prototype.init = function () {\n    _super.prototype.init.apply(this, arguments);\n\n    this.mergeOption({});\n  };\n\n  ParallelModel.prototype.mergeOption = function (newOption) {\n    var thisOption = this.option;\n    newOption && zrUtil.merge(thisOption, newOption, true);\n\n    this._initDimensions();\n  };\n  /**\r\n   * Whether series or axis is in this coordinate system.\r\n   */\n\n\n  ParallelModel.prototype.contains = function (model, ecModel) {\n    var parallelIndex = model.get('parallelIndex');\n    return parallelIndex != null && ecModel.getComponent('parallel', parallelIndex) === this;\n  };\n\n  ParallelModel.prototype.setAxisExpand = function (opt) {\n    zrUtil.each(['axisExpandable', 'axisExpandCenter', 'axisExpandCount', 'axisExpandWidth', 'axisExpandWindow'], function (name) {\n      if (opt.hasOwnProperty(name)) {\n        // @ts-ignore FIXME: why \"never\" inferred in this.option[name]?\n        this.option[name] = opt[name];\n      }\n    }, this);\n  };\n\n  ParallelModel.prototype._initDimensions = function () {\n    var dimensions = this.dimensions = [];\n    var parallelAxisIndex = this.parallelAxisIndex = [];\n    var axisModels = zrUtil.filter(this.ecModel.queryComponents({\n      mainType: 'parallelAxis'\n    }), function (axisModel) {\n      // Can not use this.contains here, because\n      // initialization has not been completed yet.\n      return (axisModel.get('parallelIndex') || 0) === this.componentIndex;\n    }, this);\n    zrUtil.each(axisModels, function (axisModel) {\n      dimensions.push('dim' + axisModel.get('dim'));\n      parallelAxisIndex.push(axisModel.componentIndex);\n    });\n  };\n\n  ParallelModel.type = 'parallel';\n  ParallelModel.dependencies = ['parallelAxis'];\n  ParallelModel.layoutMode = 'box';\n  ParallelModel.defaultOption = {\n    // zlevel: 0,\n    z: 0,\n    left: 80,\n    top: 60,\n    right: 80,\n    bottom: 60,\n    // width: {totalWidth} - left - right,\n    // height: {totalHeight} - top - bottom,\n    layout: 'horizontal',\n    // FIXME\n    // naming?\n    axisExpandable: false,\n    axisExpandCenter: null,\n    axisExpandCount: 0,\n    axisExpandWidth: 50,\n    axisExpandRate: 17,\n    axisExpandDebounce: 50,\n    // [out, in, jumpTarget]. In percentage. If use [null, 0.05], null means full.\n    // Do not doc to user until necessary.\n    axisExpandSlideTriggerArea: [-0.15, 0.05, 0.4],\n    axisExpandTriggerOn: 'click',\n    parallelAxisDefault: null\n  };\n  return ParallelModel;\n}(ComponentModel);\n\nexport default ParallelModel;"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,SAAS,QAAQ,OAAO;AACjC,OAAO,KAAKC,MAAM,MAAM,0BAA0B;AAClD,OAAOC,cAAc,MAAM,0BAA0B;AAErD,IAAIC,aAAa,GACjB;AACA,UAAUC,MAAM,EAAE;EAChBJ,SAAS,CAACG,aAAa,EAAEC,MAAM,CAAC;EAEhC,SAASD,aAAaA,CAAA,EAAG;IACvB,IAAIE,KAAK,GAAGD,MAAM,KAAK,IAAI,IAAIA,MAAM,CAACE,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC,IAAI,IAAI;IAEpEF,KAAK,CAACG,IAAI,GAAGL,aAAa,CAACK,IAAI;IAC/B,OAAOH,KAAK;EACd;EAEAF,aAAa,CAACM,SAAS,CAACC,IAAI,GAAG,YAAY;IACzCN,MAAM,CAACK,SAAS,CAACC,IAAI,CAACJ,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC;IAE5C,IAAI,CAACI,WAAW,CAAC,CAAC,CAAC,CAAC;EACtB,CAAC;EAEDR,aAAa,CAACM,SAAS,CAACE,WAAW,GAAG,UAAUC,SAAS,EAAE;IACzD,IAAIC,UAAU,GAAG,IAAI,CAACC,MAAM;IAC5BF,SAAS,IAAIX,MAAM,CAACc,KAAK,CAACF,UAAU,EAAED,SAAS,EAAE,IAAI,CAAC;IAEtD,IAAI,CAACI,eAAe,CAAC,CAAC;EACxB,CAAC;EACD;AACF;AACA;;EAGEb,aAAa,CAACM,SAAS,CAACQ,QAAQ,GAAG,UAAUC,KAAK,EAAEC,OAAO,EAAE;IAC3D,IAAIC,aAAa,GAAGF,KAAK,CAACG,GAAG,CAAC,eAAe,CAAC;IAC9C,OAAOD,aAAa,IAAI,IAAI,IAAID,OAAO,CAACG,YAAY,CAAC,UAAU,EAAEF,aAAa,CAAC,KAAK,IAAI;EAC1F,CAAC;EAEDjB,aAAa,CAACM,SAAS,CAACc,aAAa,GAAG,UAAUC,GAAG,EAAE;IACrDvB,MAAM,CAACwB,IAAI,CAAC,CAAC,gBAAgB,EAAE,kBAAkB,EAAE,iBAAiB,EAAE,iBAAiB,EAAE,kBAAkB,CAAC,EAAE,UAAUC,IAAI,EAAE;MAC5H,IAAIF,GAAG,CAACG,cAAc,CAACD,IAAI,CAAC,EAAE;QAC5B;QACA,IAAI,CAACZ,MAAM,CAACY,IAAI,CAAC,GAAGF,GAAG,CAACE,IAAI,CAAC;MAC/B;IACF,CAAC,EAAE,IAAI,CAAC;EACV,CAAC;EAEDvB,aAAa,CAACM,SAAS,CAACO,eAAe,GAAG,YAAY;IACpD,IAAIY,UAAU,GAAG,IAAI,CAACA,UAAU,GAAG,EAAE;IACrC,IAAIC,iBAAiB,GAAG,IAAI,CAACA,iBAAiB,GAAG,EAAE;IACnD,IAAIC,UAAU,GAAG7B,MAAM,CAAC8B,MAAM,CAAC,IAAI,CAACZ,OAAO,CAACa,eAAe,CAAC;MAC1DC,QAAQ,EAAE;IACZ,CAAC,CAAC,EAAE,UAAUC,SAAS,EAAE;MACvB;MACA;MACA,OAAO,CAACA,SAAS,CAACb,GAAG,CAAC,eAAe,CAAC,IAAI,CAAC,MAAM,IAAI,CAACc,cAAc;IACtE,CAAC,EAAE,IAAI,CAAC;IACRlC,MAAM,CAACwB,IAAI,CAACK,UAAU,EAAE,UAAUI,SAAS,EAAE;MAC3CN,UAAU,CAACQ,IAAI,CAAC,KAAK,GAAGF,SAAS,CAACb,GAAG,CAAC,KAAK,CAAC,CAAC;MAC7CQ,iBAAiB,CAACO,IAAI,CAACF,SAAS,CAACC,cAAc,CAAC;IAClD,CAAC,CAAC;EACJ,CAAC;EAEDhC,aAAa,CAACK,IAAI,GAAG,UAAU;EAC/BL,aAAa,CAACkC,YAAY,GAAG,CAAC,cAAc,CAAC;EAC7ClC,aAAa,CAACmC,UAAU,GAAG,KAAK;EAChCnC,aAAa,CAACoC,aAAa,GAAG;IAC5B;IACAC,CAAC,EAAE,CAAC;IACJC,IAAI,EAAE,EAAE;IACRC,GAAG,EAAE,EAAE;IACPC,KAAK,EAAE,EAAE;IACTC,MAAM,EAAE,EAAE;IACV;IACA;IACAC,MAAM,EAAE,YAAY;IACpB;IACA;IACAC,cAAc,EAAE,KAAK;IACrBC,gBAAgB,EAAE,IAAI;IACtBC,eAAe,EAAE,CAAC;IAClBC,eAAe,EAAE,EAAE;IACnBC,cAAc,EAAE,EAAE;IAClBC,kBAAkB,EAAE,EAAE;IACtB;IACA;IACAC,0BAA0B,EAAE,CAAC,CAAC,IAAI,EAAE,IAAI,EAAE,GAAG,CAAC;IAC9CC,mBAAmB,EAAE,OAAO;IAC5BC,mBAAmB,EAAE;EACvB,CAAC;EACD,OAAOnD,aAAa;AACtB,CAAC,CAACD,cAAc,CAAC;AAEjB,eAAeC,aAAa"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}