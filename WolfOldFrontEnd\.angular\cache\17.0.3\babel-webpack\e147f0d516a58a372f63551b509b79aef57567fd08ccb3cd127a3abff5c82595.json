{"ast": null, "code": "//! moment.js locale configuration\n//! locale : Sinhalese [si]\n//! author : <PERSON><PERSON> : https://github.com/sampathsris\n\n;\n(function (global, factory) {\n  typeof exports === 'object' && typeof module !== 'undefined' && typeof require === 'function' ? factory(require('../moment')) : typeof define === 'function' && define.amd ? define(['../moment'], factory) : factory(global.moment);\n})(this, function (moment) {\n  'use strict';\n\n  //! moment.js locale configuration\n\n  /*jshint -W100*/\n  var si = moment.defineLocale('si', {\n    months: 'ජනවාරි_පෙබරවාරි_මාර්තු_අප්‍රේල්_මැයි_ජූනි_ජූලි_අගෝස්තු_සැප්තැම්බර්_ඔක්තෝබර්_නොවැම්බර්_දෙසැම්බර්'.split('_'),\n    monthsShort: 'ජන_පෙබ_මාර්_අප්_මැයි_ජූනි_ජූලි_අගෝ_සැප්_ඔක්_නොවැ_දෙසැ'.split('_'),\n    weekdays: 'ඉරිදා_සඳුදා_අඟහරුවාදා_බදාදා_බ්‍රහස්පතින්දා_සිකුරාදා_සෙනසුරාදා'.split('_'),\n    weekdaysShort: 'ඉරි_සඳු_අඟ_බදා_බ්‍රහ_සිකු_සෙන'.split('_'),\n    weekdaysMin: 'ඉ_ස_අ_බ_බ්‍ර_සි_සෙ'.split('_'),\n    weekdaysParseExact: true,\n    longDateFormat: {\n      LT: 'a h:mm',\n      LTS: 'a h:mm:ss',\n      L: 'YYYY/MM/DD',\n      LL: 'YYYY MMMM D',\n      LLL: 'YYYY MMMM D, a h:mm',\n      LLLL: 'YYYY MMMM D [වැනි] dddd, a h:mm:ss'\n    },\n    calendar: {\n      sameDay: '[අද] LT[ට]',\n      nextDay: '[හෙට] LT[ට]',\n      nextWeek: 'dddd LT[ට]',\n      lastDay: '[ඊයේ] LT[ට]',\n      lastWeek: '[පසුගිය] dddd LT[ට]',\n      sameElse: 'L'\n    },\n    relativeTime: {\n      future: '%sකින්',\n      past: '%sකට පෙර',\n      s: 'තත්පර කිහිපය',\n      ss: 'තත්පර %d',\n      m: 'මිනිත්තුව',\n      mm: 'මිනිත්තු %d',\n      h: 'පැය',\n      hh: 'පැය %d',\n      d: 'දිනය',\n      dd: 'දින %d',\n      M: 'මාසය',\n      MM: 'මාස %d',\n      y: 'වසර',\n      yy: 'වසර %d'\n    },\n    dayOfMonthOrdinalParse: /\\d{1,2} වැනි/,\n    ordinal: function (number) {\n      return number + ' වැනි';\n    },\n    meridiemParse: /පෙර වරු|පස් වරු|පෙ.ව|ප.ව./,\n    isPM: function (input) {\n      return input === 'ප.ව.' || input === 'පස් වරු';\n    },\n    meridiem: function (hours, minutes, isLower) {\n      if (hours > 11) {\n        return isLower ? 'ප.ව.' : 'පස් වරු';\n      } else {\n        return isLower ? 'පෙ.ව.' : 'පෙර වරු';\n      }\n    }\n  });\n  return si;\n});", "map": {"version": 3, "names": ["global", "factory", "exports", "module", "require", "define", "amd", "moment", "si", "defineLocale", "months", "split", "monthsShort", "weekdays", "weekdaysShort", "weekdaysMin", "weekdaysParseExact", "longDateFormat", "LT", "LTS", "L", "LL", "LLL", "LLLL", "calendar", "sameDay", "nextDay", "nextWeek", "lastDay", "lastWeek", "same<PERSON><PERSON><PERSON>", "relativeTime", "future", "past", "s", "ss", "m", "mm", "h", "hh", "d", "dd", "M", "MM", "y", "yy", "dayOfMonthOrdinalParse", "ordinal", "number", "meridiemParse", "isPM", "input", "meridiem", "hours", "minutes", "isLower"], "sources": ["C:/WolfSystemV2/WolfOldFrontEnd/node_modules/moment/locale/si.js"], "sourcesContent": ["//! moment.js locale configuration\n//! locale : Sinhalese [si]\n//! author : <PERSON><PERSON> : https://github.com/sampathsris\n\n;(function (global, factory) {\n   typeof exports === 'object' && typeof module !== 'undefined'\n       && typeof require === 'function' ? factory(require('../moment')) :\n   typeof define === 'function' && define.amd ? define(['../moment'], factory) :\n   factory(global.moment)\n}(this, (function (moment) { 'use strict';\n\n    //! moment.js locale configuration\n\n    /*jshint -W100*/\n    var si = moment.defineLocale('si', {\n        months: 'ජනවාරි_පෙබරවාරි_මාර්තු_අප්‍රේල්_මැයි_ජූනි_ජූලි_අගෝස්තු_සැප්තැම්බර්_ඔක්තෝබර්_නොවැම්බර්_දෙසැම්බර්'.split(\n            '_'\n        ),\n        monthsShort: 'ජන_පෙබ_මාර්_අප්_මැයි_ජූනි_ජූලි_අගෝ_සැප්_ඔක්_නොවැ_දෙසැ'.split(\n            '_'\n        ),\n        weekdays:\n            'ඉරිදා_සඳුදා_අඟහරුවාදා_බදාදා_බ්‍රහස්පතින්දා_සිකුරාදා_සෙනසුරාදා'.split(\n                '_'\n            ),\n        weekdaysShort: 'ඉරි_සඳු_අඟ_බදා_බ්‍රහ_සිකු_සෙන'.split('_'),\n        weekdaysMin: 'ඉ_ස_අ_බ_බ්‍ර_සි_සෙ'.split('_'),\n        weekdaysParseExact: true,\n        longDateFormat: {\n            LT: 'a h:mm',\n            LTS: 'a h:mm:ss',\n            L: 'YYYY/MM/DD',\n            LL: 'YYYY MMMM D',\n            LLL: 'YYYY MMMM D, a h:mm',\n            LLLL: 'YYYY MMMM D [වැනි] dddd, a h:mm:ss',\n        },\n        calendar: {\n            sameDay: '[අද] LT[ට]',\n            nextDay: '[හෙට] LT[ට]',\n            nextWeek: 'dddd LT[ට]',\n            lastDay: '[ඊයේ] LT[ට]',\n            lastWeek: '[පසුගිය] dddd LT[ට]',\n            sameElse: 'L',\n        },\n        relativeTime: {\n            future: '%sකින්',\n            past: '%sකට පෙර',\n            s: 'තත්පර කිහිපය',\n            ss: 'තත්පර %d',\n            m: 'මිනිත්තුව',\n            mm: 'මිනිත්තු %d',\n            h: 'පැය',\n            hh: 'පැය %d',\n            d: 'දිනය',\n            dd: 'දින %d',\n            M: 'මාසය',\n            MM: 'මාස %d',\n            y: 'වසර',\n            yy: 'වසර %d',\n        },\n        dayOfMonthOrdinalParse: /\\d{1,2} වැනි/,\n        ordinal: function (number) {\n            return number + ' වැනි';\n        },\n        meridiemParse: /පෙර වරු|පස් වරු|පෙ.ව|ප.ව./,\n        isPM: function (input) {\n            return input === 'ප.ව.' || input === 'පස් වරු';\n        },\n        meridiem: function (hours, minutes, isLower) {\n            if (hours > 11) {\n                return isLower ? 'ප.ව.' : 'පස් වරු';\n            } else {\n                return isLower ? 'පෙ.ව.' : 'පෙර වරු';\n            }\n        },\n    });\n\n    return si;\n\n})));\n"], "mappings": "AAAA;AACA;AACA;;AAEA;AAAE,WAAUA,MAAM,EAAEC,OAAO,EAAE;EAC1B,OAAOC,OAAO,KAAK,QAAQ,IAAI,OAAOC,MAAM,KAAK,WAAW,IACrD,OAAOC,OAAO,KAAK,UAAU,GAAGH,OAAO,CAACG,OAAO,CAAC,WAAW,CAAC,CAAC,GACpE,OAAOC,MAAM,KAAK,UAAU,IAAIA,MAAM,CAACC,GAAG,GAAGD,MAAM,CAAC,CAAC,WAAW,CAAC,EAAEJ,OAAO,CAAC,GAC3EA,OAAO,CAACD,MAAM,CAACO,MAAM,CAAC;AACzB,CAAC,EAAC,IAAI,EAAG,UAAUA,MAAM,EAAE;EAAE,YAAY;;EAErC;;EAEA;EACA,IAAIC,EAAE,GAAGD,MAAM,CAACE,YAAY,CAAC,IAAI,EAAE;IAC/BC,MAAM,EAAE,iGAAiG,CAACC,KAAK,CAC3G,GACJ,CAAC;IACDC,WAAW,EAAE,uDAAuD,CAACD,KAAK,CACtE,GACJ,CAAC;IACDE,QAAQ,EACJ,+DAA+D,CAACF,KAAK,CACjE,GACJ,CAAC;IACLG,aAAa,EAAE,+BAA+B,CAACH,KAAK,CAAC,GAAG,CAAC;IACzDI,WAAW,EAAE,oBAAoB,CAACJ,KAAK,CAAC,GAAG,CAAC;IAC5CK,kBAAkB,EAAE,IAAI;IACxBC,cAAc,EAAE;MACZC,EAAE,EAAE,QAAQ;MACZC,GAAG,EAAE,WAAW;MAChBC,CAAC,EAAE,YAAY;MACfC,EAAE,EAAE,aAAa;MACjBC,GAAG,EAAE,qBAAqB;MAC1BC,IAAI,EAAE;IACV,CAAC;IACDC,QAAQ,EAAE;MACNC,OAAO,EAAE,YAAY;MACrBC,OAAO,EAAE,aAAa;MACtBC,QAAQ,EAAE,YAAY;MACtBC,OAAO,EAAE,aAAa;MACtBC,QAAQ,EAAE,qBAAqB;MAC/BC,QAAQ,EAAE;IACd,CAAC;IACDC,YAAY,EAAE;MACVC,MAAM,EAAE,QAAQ;MAChBC,IAAI,EAAE,UAAU;MAChBC,CAAC,EAAE,cAAc;MACjBC,EAAE,EAAE,UAAU;MACdC,CAAC,EAAE,WAAW;MACdC,EAAE,EAAE,aAAa;MACjBC,CAAC,EAAE,KAAK;MACRC,EAAE,EAAE,QAAQ;MACZC,CAAC,EAAE,MAAM;MACTC,EAAE,EAAE,QAAQ;MACZC,CAAC,EAAE,MAAM;MACTC,EAAE,EAAE,QAAQ;MACZC,CAAC,EAAE,KAAK;MACRC,EAAE,EAAE;IACR,CAAC;IACDC,sBAAsB,EAAE,cAAc;IACtCC,OAAO,EAAE,SAAAA,CAAUC,MAAM,EAAE;MACvB,OAAOA,MAAM,GAAG,OAAO;IAC3B,CAAC;IACDC,aAAa,EAAE,2BAA2B;IAC1CC,IAAI,EAAE,SAAAA,CAAUC,KAAK,EAAE;MACnB,OAAOA,KAAK,KAAK,MAAM,IAAIA,KAAK,KAAK,SAAS;IAClD,CAAC;IACDC,QAAQ,EAAE,SAAAA,CAAUC,KAAK,EAAEC,OAAO,EAAEC,OAAO,EAAE;MACzC,IAAIF,KAAK,GAAG,EAAE,EAAE;QACZ,OAAOE,OAAO,GAAG,MAAM,GAAG,SAAS;MACvC,CAAC,MAAM;QACH,OAAOA,OAAO,GAAG,OAAO,GAAG,SAAS;MACxC;IACJ;EACJ,CAAC,CAAC;EAEF,OAAO/C,EAAE;AAEb,CAAE,CAAC"}, "metadata": {}, "sourceType": "script", "externalDependencies": []}