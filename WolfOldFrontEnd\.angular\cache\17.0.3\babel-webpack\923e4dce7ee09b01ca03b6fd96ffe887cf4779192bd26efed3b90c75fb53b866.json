{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\n/**\r\n * Separate legend and scrollable legend to reduce package size.\r\n */\n\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport * as graphic from '../../util/graphic.js';\nimport * as layoutUtil from '../../util/layout.js';\nimport LegendView from './LegendView.js';\nvar Group = graphic.Group;\nvar WH = ['width', 'height'];\nvar XY = ['x', 'y'];\nvar ScrollableLegendView = /** @class */\nfunction (_super) {\n  __extends(ScrollableLegendView, _super);\n  function ScrollableLegendView() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.type = ScrollableLegendView.type;\n    _this.newlineDisabled = true;\n    _this._currentIndex = 0;\n    return _this;\n  }\n  ScrollableLegendView.prototype.init = function () {\n    _super.prototype.init.call(this);\n    this.group.add(this._containerGroup = new Group());\n    this._containerGroup.add(this.getContentGroup());\n    this.group.add(this._controllerGroup = new Group());\n  };\n  /**\r\n   * @override\r\n   */\n\n  ScrollableLegendView.prototype.resetInner = function () {\n    _super.prototype.resetInner.call(this);\n    this._controllerGroup.removeAll();\n    this._containerGroup.removeClipPath();\n    this._containerGroup.__rectSize = null;\n  };\n  /**\r\n   * @override\r\n   */\n\n  ScrollableLegendView.prototype.renderInner = function (itemAlign, legendModel, ecModel, api, selector, orient, selectorPosition) {\n    var self = this; // Render content items.\n\n    _super.prototype.renderInner.call(this, itemAlign, legendModel, ecModel, api, selector, orient, selectorPosition);\n    var controllerGroup = this._controllerGroup; // FIXME: support be 'auto' adapt to size number text length,\n    // e.g., '3/12345' should not overlap with the control arrow button.\n\n    var pageIconSize = legendModel.get('pageIconSize', true);\n    var pageIconSizeArr = zrUtil.isArray(pageIconSize) ? pageIconSize : [pageIconSize, pageIconSize];\n    createPageButton('pagePrev', 0);\n    var pageTextStyleModel = legendModel.getModel('pageTextStyle');\n    controllerGroup.add(new graphic.Text({\n      name: 'pageText',\n      style: {\n        // Placeholder to calculate a proper layout.\n        text: 'xx/xx',\n        fill: pageTextStyleModel.getTextColor(),\n        font: pageTextStyleModel.getFont(),\n        verticalAlign: 'middle',\n        align: 'center'\n      },\n      silent: true\n    }));\n    createPageButton('pageNext', 1);\n    function createPageButton(name, iconIdx) {\n      var pageDataIndexName = name + 'DataIndex';\n      var icon = graphic.createIcon(legendModel.get('pageIcons', true)[legendModel.getOrient().name][iconIdx], {\n        // Buttons will be created in each render, so we do not need\n        // to worry about avoiding using legendModel kept in scope.\n        onclick: zrUtil.bind(self._pageGo, self, pageDataIndexName, legendModel, api)\n      }, {\n        x: -pageIconSizeArr[0] / 2,\n        y: -pageIconSizeArr[1] / 2,\n        width: pageIconSizeArr[0],\n        height: pageIconSizeArr[1]\n      });\n      icon.name = name;\n      controllerGroup.add(icon);\n    }\n  };\n  /**\r\n   * @override\r\n   */\n\n  ScrollableLegendView.prototype.layoutInner = function (legendModel, itemAlign, maxSize, isFirstRender, selector, selectorPosition) {\n    var selectorGroup = this.getSelectorGroup();\n    var orientIdx = legendModel.getOrient().index;\n    var wh = WH[orientIdx];\n    var xy = XY[orientIdx];\n    var hw = WH[1 - orientIdx];\n    var yx = XY[1 - orientIdx];\n    selector && layoutUtil.box(\n    // Buttons in selectorGroup always layout horizontally\n    'horizontal', selectorGroup, legendModel.get('selectorItemGap', true));\n    var selectorButtonGap = legendModel.get('selectorButtonGap', true);\n    var selectorRect = selectorGroup.getBoundingRect();\n    var selectorPos = [-selectorRect.x, -selectorRect.y];\n    var processMaxSize = zrUtil.clone(maxSize);\n    selector && (processMaxSize[wh] = maxSize[wh] - selectorRect[wh] - selectorButtonGap);\n    var mainRect = this._layoutContentAndController(legendModel, isFirstRender, processMaxSize, orientIdx, wh, hw, yx, xy);\n    if (selector) {\n      if (selectorPosition === 'end') {\n        selectorPos[orientIdx] += mainRect[wh] + selectorButtonGap;\n      } else {\n        var offset = selectorRect[wh] + selectorButtonGap;\n        selectorPos[orientIdx] -= offset;\n        mainRect[xy] -= offset;\n      }\n      mainRect[wh] += selectorRect[wh] + selectorButtonGap;\n      selectorPos[1 - orientIdx] += mainRect[yx] + mainRect[hw] / 2 - selectorRect[hw] / 2;\n      mainRect[hw] = Math.max(mainRect[hw], selectorRect[hw]);\n      mainRect[yx] = Math.min(mainRect[yx], selectorRect[yx] + selectorPos[1 - orientIdx]);\n      selectorGroup.x = selectorPos[0];\n      selectorGroup.y = selectorPos[1];\n      selectorGroup.markRedraw();\n    }\n    return mainRect;\n  };\n  ScrollableLegendView.prototype._layoutContentAndController = function (legendModel, isFirstRender, maxSize, orientIdx, wh, hw, yx, xy) {\n    var contentGroup = this.getContentGroup();\n    var containerGroup = this._containerGroup;\n    var controllerGroup = this._controllerGroup; // Place items in contentGroup.\n\n    layoutUtil.box(legendModel.get('orient'), contentGroup, legendModel.get('itemGap'), !orientIdx ? null : maxSize.width, orientIdx ? null : maxSize.height);\n    layoutUtil.box(\n    // Buttons in controller are layout always horizontally.\n    'horizontal', controllerGroup, legendModel.get('pageButtonItemGap', true));\n    var contentRect = contentGroup.getBoundingRect();\n    var controllerRect = controllerGroup.getBoundingRect();\n    var showController = this._showController = contentRect[wh] > maxSize[wh]; // In case that the inner elements of contentGroup layout do not based on [0, 0]\n\n    var contentPos = [-contentRect.x, -contentRect.y]; // Remain contentPos when scroll animation perfroming.\n    // If first rendering, `contentGroup.position` is [0, 0], which\n    // does not make sense and may cause unexepcted animation if adopted.\n\n    if (!isFirstRender) {\n      contentPos[orientIdx] = contentGroup[xy];\n    } // Layout container group based on 0.\n\n    var containerPos = [0, 0];\n    var controllerPos = [-controllerRect.x, -controllerRect.y];\n    var pageButtonGap = zrUtil.retrieve2(legendModel.get('pageButtonGap', true), legendModel.get('itemGap', true)); // Place containerGroup and controllerGroup and contentGroup.\n\n    if (showController) {\n      var pageButtonPosition = legendModel.get('pageButtonPosition', true); // controller is on the right / bottom.\n\n      if (pageButtonPosition === 'end') {\n        controllerPos[orientIdx] += maxSize[wh] - controllerRect[wh];\n      } // controller is on the left / top.\n      else {\n        containerPos[orientIdx] += controllerRect[wh] + pageButtonGap;\n      }\n    } // Always align controller to content as 'middle'.\n\n    controllerPos[1 - orientIdx] += contentRect[hw] / 2 - controllerRect[hw] / 2;\n    contentGroup.setPosition(contentPos);\n    containerGroup.setPosition(containerPos);\n    controllerGroup.setPosition(controllerPos); // Calculate `mainRect` and set `clipPath`.\n    // mainRect should not be calculated by `this.group.getBoundingRect()`\n    // for sake of the overflow.\n\n    var mainRect = {\n      x: 0,\n      y: 0\n    }; // Consider content may be overflow (should be clipped).\n\n    mainRect[wh] = showController ? maxSize[wh] : contentRect[wh];\n    mainRect[hw] = Math.max(contentRect[hw], controllerRect[hw]); // `containerRect[yx] + containerPos[1 - orientIdx]` is 0.\n\n    mainRect[yx] = Math.min(0, controllerRect[yx] + controllerPos[1 - orientIdx]);\n    containerGroup.__rectSize = maxSize[wh];\n    if (showController) {\n      var clipShape = {\n        x: 0,\n        y: 0\n      };\n      clipShape[wh] = Math.max(maxSize[wh] - controllerRect[wh] - pageButtonGap, 0);\n      clipShape[hw] = mainRect[hw];\n      containerGroup.setClipPath(new graphic.Rect({\n        shape: clipShape\n      })); // Consider content may be larger than container, container rect\n      // can not be obtained from `containerGroup.getBoundingRect()`.\n\n      containerGroup.__rectSize = clipShape[wh];\n    } else {\n      // Do not remove or ignore controller. Keep them set as placeholders.\n      controllerGroup.eachChild(function (child) {\n        child.attr({\n          invisible: true,\n          silent: true\n        });\n      });\n    } // Content translate animation.\n\n    var pageInfo = this._getPageInfo(legendModel);\n    pageInfo.pageIndex != null && graphic.updateProps(contentGroup, {\n      x: pageInfo.contentPosition[0],\n      y: pageInfo.contentPosition[1]\n    },\n    // When switch from \"show controller\" to \"not show controller\", view should be\n    // updated immediately without animation, otherwise causes weird effect.\n    showController ? legendModel : null);\n    this._updatePageInfoView(legendModel, pageInfo);\n    return mainRect;\n  };\n  ScrollableLegendView.prototype._pageGo = function (to, legendModel, api) {\n    var scrollDataIndex = this._getPageInfo(legendModel)[to];\n    scrollDataIndex != null && api.dispatchAction({\n      type: 'legendScroll',\n      scrollDataIndex: scrollDataIndex,\n      legendId: legendModel.id\n    });\n  };\n  ScrollableLegendView.prototype._updatePageInfoView = function (legendModel, pageInfo) {\n    var controllerGroup = this._controllerGroup;\n    zrUtil.each(['pagePrev', 'pageNext'], function (name) {\n      var key = name + 'DataIndex';\n      var canJump = pageInfo[key] != null;\n      var icon = controllerGroup.childOfName(name);\n      if (icon) {\n        icon.setStyle('fill', canJump ? legendModel.get('pageIconColor', true) : legendModel.get('pageIconInactiveColor', true));\n        icon.cursor = canJump ? 'pointer' : 'default';\n      }\n    });\n    var pageText = controllerGroup.childOfName('pageText');\n    var pageFormatter = legendModel.get('pageFormatter');\n    var pageIndex = pageInfo.pageIndex;\n    var current = pageIndex != null ? pageIndex + 1 : 0;\n    var total = pageInfo.pageCount;\n    pageText && pageFormatter && pageText.setStyle('text', zrUtil.isString(pageFormatter) ? pageFormatter.replace('{current}', current == null ? '' : current + '').replace('{total}', total == null ? '' : total + '') : pageFormatter({\n      current: current,\n      total: total\n    }));\n  };\n  /**\r\n   *  contentPosition: Array.<number>, null when data item not found.\r\n   *  pageIndex: number, null when data item not found.\r\n   *  pageCount: number, always be a number, can be 0.\r\n   *  pagePrevDataIndex: number, null when no previous page.\r\n   *  pageNextDataIndex: number, null when no next page.\r\n   * }\r\n   */\n\n  ScrollableLegendView.prototype._getPageInfo = function (legendModel) {\n    var scrollDataIndex = legendModel.get('scrollDataIndex', true);\n    var contentGroup = this.getContentGroup();\n    var containerRectSize = this._containerGroup.__rectSize;\n    var orientIdx = legendModel.getOrient().index;\n    var wh = WH[orientIdx];\n    var xy = XY[orientIdx];\n    var targetItemIndex = this._findTargetItemIndex(scrollDataIndex);\n    var children = contentGroup.children();\n    var targetItem = children[targetItemIndex];\n    var itemCount = children.length;\n    var pCount = !itemCount ? 0 : 1;\n    var result = {\n      contentPosition: [contentGroup.x, contentGroup.y],\n      pageCount: pCount,\n      pageIndex: pCount - 1,\n      pagePrevDataIndex: null,\n      pageNextDataIndex: null\n    };\n    if (!targetItem) {\n      return result;\n    }\n    var targetItemInfo = getItemInfo(targetItem);\n    result.contentPosition[orientIdx] = -targetItemInfo.s; // Strategy:\n    // (1) Always align based on the left/top most item.\n    // (2) It is user-friendly that the last item shown in the\n    // current window is shown at the begining of next window.\n    // Otherwise if half of the last item is cut by the window,\n    // it will have no chance to display entirely.\n    // (3) Consider that item size probably be different, we\n    // have calculate pageIndex by size rather than item index,\n    // and we can not get page index directly by division.\n    // (4) The window is to narrow to contain more than\n    // one item, we should make sure that the page can be fliped.\n\n    for (var i = targetItemIndex + 1, winStartItemInfo = targetItemInfo, winEndItemInfo = targetItemInfo, currItemInfo = null; i <= itemCount; ++i) {\n      currItemInfo = getItemInfo(children[i]);\n      if (\n      // Half of the last item is out of the window.\n      !currItemInfo && winEndItemInfo.e > winStartItemInfo.s + containerRectSize ||\n      // If the current item does not intersect with the window, the new page\n      // can be started at the current item or the last item.\n      currItemInfo && !intersect(currItemInfo, winStartItemInfo.s)) {\n        if (winEndItemInfo.i > winStartItemInfo.i) {\n          winStartItemInfo = winEndItemInfo;\n        } else {\n          // e.g., when page size is smaller than item size.\n          winStartItemInfo = currItemInfo;\n        }\n        if (winStartItemInfo) {\n          if (result.pageNextDataIndex == null) {\n            result.pageNextDataIndex = winStartItemInfo.i;\n          }\n          ++result.pageCount;\n        }\n      }\n      winEndItemInfo = currItemInfo;\n    }\n    for (var i = targetItemIndex - 1, winStartItemInfo = targetItemInfo, winEndItemInfo = targetItemInfo, currItemInfo = null; i >= -1; --i) {\n      currItemInfo = getItemInfo(children[i]);\n      if (\n      // If the the end item does not intersect with the window started\n      // from the current item, a page can be settled.\n      (!currItemInfo || !intersect(winEndItemInfo, currItemInfo.s)) &&\n      // e.g., when page size is smaller than item size.\n      winStartItemInfo.i < winEndItemInfo.i) {\n        winEndItemInfo = winStartItemInfo;\n        if (result.pagePrevDataIndex == null) {\n          result.pagePrevDataIndex = winStartItemInfo.i;\n        }\n        ++result.pageCount;\n        ++result.pageIndex;\n      }\n      winStartItemInfo = currItemInfo;\n    }\n    return result;\n    function getItemInfo(el) {\n      if (el) {\n        var itemRect = el.getBoundingRect();\n        var start = itemRect[xy] + el[xy];\n        return {\n          s: start,\n          e: start + itemRect[wh],\n          i: el.__legendDataIndex\n        };\n      }\n    }\n    function intersect(itemInfo, winStart) {\n      return itemInfo.e >= winStart && itemInfo.s <= winStart + containerRectSize;\n    }\n  };\n  ScrollableLegendView.prototype._findTargetItemIndex = function (targetDataIndex) {\n    if (!this._showController) {\n      return 0;\n    }\n    var index;\n    var contentGroup = this.getContentGroup();\n    var defaultIndex;\n    contentGroup.eachChild(function (child, idx) {\n      var legendDataIdx = child.__legendDataIndex; // FIXME\n      // If the given targetDataIndex (from model) is illegal,\n      // we use defaultIndex. But the index on the legend model and\n      // action payload is still illegal. That case will not be\n      // changed until some scenario requires.\n\n      if (defaultIndex == null && legendDataIdx != null) {\n        defaultIndex = idx;\n      }\n      if (legendDataIdx === targetDataIndex) {\n        index = idx;\n      }\n    });\n    return index != null ? index : defaultIndex;\n  };\n  ScrollableLegendView.type = 'legend.scroll';\n  return ScrollableLegendView;\n}(LegendView);\nexport default ScrollableLegendView;", "map": {"version": 3, "names": ["__extends", "zrUtil", "graphic", "layoutUtil", "LegendView", "Group", "WH", "XY", "ScrollableLegendView", "_super", "_this", "apply", "arguments", "type", "newlineDisabled", "_currentIndex", "prototype", "init", "call", "group", "add", "_containerGroup", "getContentGroup", "_controllerGroup", "resetInner", "removeAll", "removeClip<PERSON>ath", "__rectSize", "renderInner", "itemAlign", "legend<PERSON><PERSON><PERSON>", "ecModel", "api", "selector", "orient", "selectorPosition", "self", "controllerGroup", "pageIconSize", "get", "pageIconSizeArr", "isArray", "createPageButton", "pageTextStyleModel", "getModel", "Text", "name", "style", "text", "fill", "getTextColor", "font", "getFont", "verticalAlign", "align", "silent", "iconIdx", "pageDataIndexName", "icon", "createIcon", "getOrient", "onclick", "bind", "_pageGo", "x", "y", "width", "height", "layoutInner", "maxSize", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "selectorGroup", "getSelectorGroup", "orientIdx", "index", "wh", "xy", "hw", "yx", "box", "selectorButtonGap", "selectorRect", "getBoundingRect", "selectorPos", "processMaxSize", "clone", "mainRect", "_layoutContentAndController", "offset", "Math", "max", "min", "mark<PERSON><PERSON><PERSON>", "contentGroup", "containerGroup", "contentRect", "controllerRect", "showController", "_showController", "contentPos", "containerPos", "controllerPos", "pageButtonGap", "retrieve2", "pageButtonPosition", "setPosition", "clipShape", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Rect", "shape", "<PERSON><PERSON><PERSON><PERSON>", "child", "attr", "invisible", "pageInfo", "_getPageInfo", "pageIndex", "updateProps", "contentPosition", "_updatePageInfoView", "to", "scrollDataIndex", "dispatchAction", "legendId", "id", "each", "key", "canJump", "childOfName", "setStyle", "cursor", "pageText", "pageFormatter", "current", "total", "pageCount", "isString", "replace", "containerRectSize", "targetItemIndex", "_findTargetItemIndex", "children", "targetItem", "itemCount", "length", "pCount", "result", "pagePrevDataIndex", "pageNextDataIndex", "targetItemInfo", "getItemInfo", "s", "i", "winStartItemInfo", "winEndItemInfo", "currItemInfo", "e", "intersect", "el", "itemRect", "start", "__legendDataIndex", "itemInfo", "winStart", "targetDataIndex", "defaultIndex", "idx", "legendDataIdx"], "sources": ["C:/WolfSystemV2/WolfOldFrontEnd/node_modules/echarts/lib/component/legend/ScrollableLegendView.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\n/**\r\n * Separate legend and scrollable legend to reduce package size.\r\n */\n\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport * as graphic from '../../util/graphic.js';\nimport * as layoutUtil from '../../util/layout.js';\nimport LegendView from './LegendView.js';\nvar Group = graphic.Group;\nvar WH = ['width', 'height'];\nvar XY = ['x', 'y'];\n\nvar ScrollableLegendView =\n/** @class */\nfunction (_super) {\n  __extends(ScrollableLegendView, _super);\n\n  function ScrollableLegendView() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n\n    _this.type = ScrollableLegendView.type;\n    _this.newlineDisabled = true;\n    _this._currentIndex = 0;\n    return _this;\n  }\n\n  ScrollableLegendView.prototype.init = function () {\n    _super.prototype.init.call(this);\n\n    this.group.add(this._containerGroup = new Group());\n\n    this._containerGroup.add(this.getContentGroup());\n\n    this.group.add(this._controllerGroup = new Group());\n  };\n  /**\r\n   * @override\r\n   */\n\n\n  ScrollableLegendView.prototype.resetInner = function () {\n    _super.prototype.resetInner.call(this);\n\n    this._controllerGroup.removeAll();\n\n    this._containerGroup.removeClipPath();\n\n    this._containerGroup.__rectSize = null;\n  };\n  /**\r\n   * @override\r\n   */\n\n\n  ScrollableLegendView.prototype.renderInner = function (itemAlign, legendModel, ecModel, api, selector, orient, selectorPosition) {\n    var self = this; // Render content items.\n\n    _super.prototype.renderInner.call(this, itemAlign, legendModel, ecModel, api, selector, orient, selectorPosition);\n\n    var controllerGroup = this._controllerGroup; // FIXME: support be 'auto' adapt to size number text length,\n    // e.g., '3/12345' should not overlap with the control arrow button.\n\n    var pageIconSize = legendModel.get('pageIconSize', true);\n    var pageIconSizeArr = zrUtil.isArray(pageIconSize) ? pageIconSize : [pageIconSize, pageIconSize];\n    createPageButton('pagePrev', 0);\n    var pageTextStyleModel = legendModel.getModel('pageTextStyle');\n    controllerGroup.add(new graphic.Text({\n      name: 'pageText',\n      style: {\n        // Placeholder to calculate a proper layout.\n        text: 'xx/xx',\n        fill: pageTextStyleModel.getTextColor(),\n        font: pageTextStyleModel.getFont(),\n        verticalAlign: 'middle',\n        align: 'center'\n      },\n      silent: true\n    }));\n    createPageButton('pageNext', 1);\n\n    function createPageButton(name, iconIdx) {\n      var pageDataIndexName = name + 'DataIndex';\n      var icon = graphic.createIcon(legendModel.get('pageIcons', true)[legendModel.getOrient().name][iconIdx], {\n        // Buttons will be created in each render, so we do not need\n        // to worry about avoiding using legendModel kept in scope.\n        onclick: zrUtil.bind(self._pageGo, self, pageDataIndexName, legendModel, api)\n      }, {\n        x: -pageIconSizeArr[0] / 2,\n        y: -pageIconSizeArr[1] / 2,\n        width: pageIconSizeArr[0],\n        height: pageIconSizeArr[1]\n      });\n      icon.name = name;\n      controllerGroup.add(icon);\n    }\n  };\n  /**\r\n   * @override\r\n   */\n\n\n  ScrollableLegendView.prototype.layoutInner = function (legendModel, itemAlign, maxSize, isFirstRender, selector, selectorPosition) {\n    var selectorGroup = this.getSelectorGroup();\n    var orientIdx = legendModel.getOrient().index;\n    var wh = WH[orientIdx];\n    var xy = XY[orientIdx];\n    var hw = WH[1 - orientIdx];\n    var yx = XY[1 - orientIdx];\n    selector && layoutUtil.box( // Buttons in selectorGroup always layout horizontally\n    'horizontal', selectorGroup, legendModel.get('selectorItemGap', true));\n    var selectorButtonGap = legendModel.get('selectorButtonGap', true);\n    var selectorRect = selectorGroup.getBoundingRect();\n    var selectorPos = [-selectorRect.x, -selectorRect.y];\n    var processMaxSize = zrUtil.clone(maxSize);\n    selector && (processMaxSize[wh] = maxSize[wh] - selectorRect[wh] - selectorButtonGap);\n\n    var mainRect = this._layoutContentAndController(legendModel, isFirstRender, processMaxSize, orientIdx, wh, hw, yx, xy);\n\n    if (selector) {\n      if (selectorPosition === 'end') {\n        selectorPos[orientIdx] += mainRect[wh] + selectorButtonGap;\n      } else {\n        var offset = selectorRect[wh] + selectorButtonGap;\n        selectorPos[orientIdx] -= offset;\n        mainRect[xy] -= offset;\n      }\n\n      mainRect[wh] += selectorRect[wh] + selectorButtonGap;\n      selectorPos[1 - orientIdx] += mainRect[yx] + mainRect[hw] / 2 - selectorRect[hw] / 2;\n      mainRect[hw] = Math.max(mainRect[hw], selectorRect[hw]);\n      mainRect[yx] = Math.min(mainRect[yx], selectorRect[yx] + selectorPos[1 - orientIdx]);\n      selectorGroup.x = selectorPos[0];\n      selectorGroup.y = selectorPos[1];\n      selectorGroup.markRedraw();\n    }\n\n    return mainRect;\n  };\n\n  ScrollableLegendView.prototype._layoutContentAndController = function (legendModel, isFirstRender, maxSize, orientIdx, wh, hw, yx, xy) {\n    var contentGroup = this.getContentGroup();\n    var containerGroup = this._containerGroup;\n    var controllerGroup = this._controllerGroup; // Place items in contentGroup.\n\n    layoutUtil.box(legendModel.get('orient'), contentGroup, legendModel.get('itemGap'), !orientIdx ? null : maxSize.width, orientIdx ? null : maxSize.height);\n    layoutUtil.box( // Buttons in controller are layout always horizontally.\n    'horizontal', controllerGroup, legendModel.get('pageButtonItemGap', true));\n    var contentRect = contentGroup.getBoundingRect();\n    var controllerRect = controllerGroup.getBoundingRect();\n    var showController = this._showController = contentRect[wh] > maxSize[wh]; // In case that the inner elements of contentGroup layout do not based on [0, 0]\n\n    var contentPos = [-contentRect.x, -contentRect.y]; // Remain contentPos when scroll animation perfroming.\n    // If first rendering, `contentGroup.position` is [0, 0], which\n    // does not make sense and may cause unexepcted animation if adopted.\n\n    if (!isFirstRender) {\n      contentPos[orientIdx] = contentGroup[xy];\n    } // Layout container group based on 0.\n\n\n    var containerPos = [0, 0];\n    var controllerPos = [-controllerRect.x, -controllerRect.y];\n    var pageButtonGap = zrUtil.retrieve2(legendModel.get('pageButtonGap', true), legendModel.get('itemGap', true)); // Place containerGroup and controllerGroup and contentGroup.\n\n    if (showController) {\n      var pageButtonPosition = legendModel.get('pageButtonPosition', true); // controller is on the right / bottom.\n\n      if (pageButtonPosition === 'end') {\n        controllerPos[orientIdx] += maxSize[wh] - controllerRect[wh];\n      } // controller is on the left / top.\n      else {\n          containerPos[orientIdx] += controllerRect[wh] + pageButtonGap;\n        }\n    } // Always align controller to content as 'middle'.\n\n\n    controllerPos[1 - orientIdx] += contentRect[hw] / 2 - controllerRect[hw] / 2;\n    contentGroup.setPosition(contentPos);\n    containerGroup.setPosition(containerPos);\n    controllerGroup.setPosition(controllerPos); // Calculate `mainRect` and set `clipPath`.\n    // mainRect should not be calculated by `this.group.getBoundingRect()`\n    // for sake of the overflow.\n\n    var mainRect = {\n      x: 0,\n      y: 0\n    }; // Consider content may be overflow (should be clipped).\n\n    mainRect[wh] = showController ? maxSize[wh] : contentRect[wh];\n    mainRect[hw] = Math.max(contentRect[hw], controllerRect[hw]); // `containerRect[yx] + containerPos[1 - orientIdx]` is 0.\n\n    mainRect[yx] = Math.min(0, controllerRect[yx] + controllerPos[1 - orientIdx]);\n    containerGroup.__rectSize = maxSize[wh];\n\n    if (showController) {\n      var clipShape = {\n        x: 0,\n        y: 0\n      };\n      clipShape[wh] = Math.max(maxSize[wh] - controllerRect[wh] - pageButtonGap, 0);\n      clipShape[hw] = mainRect[hw];\n      containerGroup.setClipPath(new graphic.Rect({\n        shape: clipShape\n      })); // Consider content may be larger than container, container rect\n      // can not be obtained from `containerGroup.getBoundingRect()`.\n\n      containerGroup.__rectSize = clipShape[wh];\n    } else {\n      // Do not remove or ignore controller. Keep them set as placeholders.\n      controllerGroup.eachChild(function (child) {\n        child.attr({\n          invisible: true,\n          silent: true\n        });\n      });\n    } // Content translate animation.\n\n\n    var pageInfo = this._getPageInfo(legendModel);\n\n    pageInfo.pageIndex != null && graphic.updateProps(contentGroup, {\n      x: pageInfo.contentPosition[0],\n      y: pageInfo.contentPosition[1]\n    }, // When switch from \"show controller\" to \"not show controller\", view should be\n    // updated immediately without animation, otherwise causes weird effect.\n    showController ? legendModel : null);\n\n    this._updatePageInfoView(legendModel, pageInfo);\n\n    return mainRect;\n  };\n\n  ScrollableLegendView.prototype._pageGo = function (to, legendModel, api) {\n    var scrollDataIndex = this._getPageInfo(legendModel)[to];\n\n    scrollDataIndex != null && api.dispatchAction({\n      type: 'legendScroll',\n      scrollDataIndex: scrollDataIndex,\n      legendId: legendModel.id\n    });\n  };\n\n  ScrollableLegendView.prototype._updatePageInfoView = function (legendModel, pageInfo) {\n    var controllerGroup = this._controllerGroup;\n    zrUtil.each(['pagePrev', 'pageNext'], function (name) {\n      var key = name + 'DataIndex';\n      var canJump = pageInfo[key] != null;\n      var icon = controllerGroup.childOfName(name);\n\n      if (icon) {\n        icon.setStyle('fill', canJump ? legendModel.get('pageIconColor', true) : legendModel.get('pageIconInactiveColor', true));\n        icon.cursor = canJump ? 'pointer' : 'default';\n      }\n    });\n    var pageText = controllerGroup.childOfName('pageText');\n    var pageFormatter = legendModel.get('pageFormatter');\n    var pageIndex = pageInfo.pageIndex;\n    var current = pageIndex != null ? pageIndex + 1 : 0;\n    var total = pageInfo.pageCount;\n    pageText && pageFormatter && pageText.setStyle('text', zrUtil.isString(pageFormatter) ? pageFormatter.replace('{current}', current == null ? '' : current + '').replace('{total}', total == null ? '' : total + '') : pageFormatter({\n      current: current,\n      total: total\n    }));\n  };\n  /**\r\n   *  contentPosition: Array.<number>, null when data item not found.\r\n   *  pageIndex: number, null when data item not found.\r\n   *  pageCount: number, always be a number, can be 0.\r\n   *  pagePrevDataIndex: number, null when no previous page.\r\n   *  pageNextDataIndex: number, null when no next page.\r\n   * }\r\n   */\n\n\n  ScrollableLegendView.prototype._getPageInfo = function (legendModel) {\n    var scrollDataIndex = legendModel.get('scrollDataIndex', true);\n    var contentGroup = this.getContentGroup();\n    var containerRectSize = this._containerGroup.__rectSize;\n    var orientIdx = legendModel.getOrient().index;\n    var wh = WH[orientIdx];\n    var xy = XY[orientIdx];\n\n    var targetItemIndex = this._findTargetItemIndex(scrollDataIndex);\n\n    var children = contentGroup.children();\n    var targetItem = children[targetItemIndex];\n    var itemCount = children.length;\n    var pCount = !itemCount ? 0 : 1;\n    var result = {\n      contentPosition: [contentGroup.x, contentGroup.y],\n      pageCount: pCount,\n      pageIndex: pCount - 1,\n      pagePrevDataIndex: null,\n      pageNextDataIndex: null\n    };\n\n    if (!targetItem) {\n      return result;\n    }\n\n    var targetItemInfo = getItemInfo(targetItem);\n    result.contentPosition[orientIdx] = -targetItemInfo.s; // Strategy:\n    // (1) Always align based on the left/top most item.\n    // (2) It is user-friendly that the last item shown in the\n    // current window is shown at the begining of next window.\n    // Otherwise if half of the last item is cut by the window,\n    // it will have no chance to display entirely.\n    // (3) Consider that item size probably be different, we\n    // have calculate pageIndex by size rather than item index,\n    // and we can not get page index directly by division.\n    // (4) The window is to narrow to contain more than\n    // one item, we should make sure that the page can be fliped.\n\n    for (var i = targetItemIndex + 1, winStartItemInfo = targetItemInfo, winEndItemInfo = targetItemInfo, currItemInfo = null; i <= itemCount; ++i) {\n      currItemInfo = getItemInfo(children[i]);\n\n      if ( // Half of the last item is out of the window.\n      !currItemInfo && winEndItemInfo.e > winStartItemInfo.s + containerRectSize || // If the current item does not intersect with the window, the new page\n      // can be started at the current item or the last item.\n      currItemInfo && !intersect(currItemInfo, winStartItemInfo.s)) {\n        if (winEndItemInfo.i > winStartItemInfo.i) {\n          winStartItemInfo = winEndItemInfo;\n        } else {\n          // e.g., when page size is smaller than item size.\n          winStartItemInfo = currItemInfo;\n        }\n\n        if (winStartItemInfo) {\n          if (result.pageNextDataIndex == null) {\n            result.pageNextDataIndex = winStartItemInfo.i;\n          }\n\n          ++result.pageCount;\n        }\n      }\n\n      winEndItemInfo = currItemInfo;\n    }\n\n    for (var i = targetItemIndex - 1, winStartItemInfo = targetItemInfo, winEndItemInfo = targetItemInfo, currItemInfo = null; i >= -1; --i) {\n      currItemInfo = getItemInfo(children[i]);\n\n      if ( // If the the end item does not intersect with the window started\n      // from the current item, a page can be settled.\n      (!currItemInfo || !intersect(winEndItemInfo, currItemInfo.s)) && // e.g., when page size is smaller than item size.\n      winStartItemInfo.i < winEndItemInfo.i) {\n        winEndItemInfo = winStartItemInfo;\n\n        if (result.pagePrevDataIndex == null) {\n          result.pagePrevDataIndex = winStartItemInfo.i;\n        }\n\n        ++result.pageCount;\n        ++result.pageIndex;\n      }\n\n      winStartItemInfo = currItemInfo;\n    }\n\n    return result;\n\n    function getItemInfo(el) {\n      if (el) {\n        var itemRect = el.getBoundingRect();\n        var start = itemRect[xy] + el[xy];\n        return {\n          s: start,\n          e: start + itemRect[wh],\n          i: el.__legendDataIndex\n        };\n      }\n    }\n\n    function intersect(itemInfo, winStart) {\n      return itemInfo.e >= winStart && itemInfo.s <= winStart + containerRectSize;\n    }\n  };\n\n  ScrollableLegendView.prototype._findTargetItemIndex = function (targetDataIndex) {\n    if (!this._showController) {\n      return 0;\n    }\n\n    var index;\n    var contentGroup = this.getContentGroup();\n    var defaultIndex;\n    contentGroup.eachChild(function (child, idx) {\n      var legendDataIdx = child.__legendDataIndex; // FIXME\n      // If the given targetDataIndex (from model) is illegal,\n      // we use defaultIndex. But the index on the legend model and\n      // action payload is still illegal. That case will not be\n      // changed until some scenario requires.\n\n      if (defaultIndex == null && legendDataIdx != null) {\n        defaultIndex = idx;\n      }\n\n      if (legendDataIdx === targetDataIndex) {\n        index = idx;\n      }\n    });\n    return index != null ? index : defaultIndex;\n  };\n\n  ScrollableLegendView.type = 'legend.scroll';\n  return ScrollableLegendView;\n}(LegendView);\n\nexport default ScrollableLegendView;"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,SAAS,QAAQ,OAAO;AACjC;AACA;AACA;;AAEA,OAAO,KAAKC,MAAM,MAAM,0BAA0B;AAClD,OAAO,KAAKC,OAAO,MAAM,uBAAuB;AAChD,OAAO,KAAKC,UAAU,MAAM,sBAAsB;AAClD,OAAOC,UAAU,MAAM,iBAAiB;AACxC,IAAIC,KAAK,GAAGH,OAAO,CAACG,KAAK;AACzB,IAAIC,EAAE,GAAG,CAAC,OAAO,EAAE,QAAQ,CAAC;AAC5B,IAAIC,EAAE,GAAG,CAAC,GAAG,EAAE,GAAG,CAAC;AAEnB,IAAIC,oBAAoB,GACxB;AACA,UAAUC,MAAM,EAAE;EAChBT,SAAS,CAACQ,oBAAoB,EAAEC,MAAM,CAAC;EAEvC,SAASD,oBAAoBA,CAAA,EAAG;IAC9B,IAAIE,KAAK,GAAGD,MAAM,KAAK,IAAI,IAAIA,MAAM,CAACE,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC,IAAI,IAAI;IAEpEF,KAAK,CAACG,IAAI,GAAGL,oBAAoB,CAACK,IAAI;IACtCH,KAAK,CAACI,eAAe,GAAG,IAAI;IAC5BJ,KAAK,CAACK,aAAa,GAAG,CAAC;IACvB,OAAOL,KAAK;EACd;EAEAF,oBAAoB,CAACQ,SAAS,CAACC,IAAI,GAAG,YAAY;IAChDR,MAAM,CAACO,SAAS,CAACC,IAAI,CAACC,IAAI,CAAC,IAAI,CAAC;IAEhC,IAAI,CAACC,KAAK,CAACC,GAAG,CAAC,IAAI,CAACC,eAAe,GAAG,IAAIhB,KAAK,CAAC,CAAC,CAAC;IAElD,IAAI,CAACgB,eAAe,CAACD,GAAG,CAAC,IAAI,CAACE,eAAe,CAAC,CAAC,CAAC;IAEhD,IAAI,CAACH,KAAK,CAACC,GAAG,CAAC,IAAI,CAACG,gBAAgB,GAAG,IAAIlB,KAAK,CAAC,CAAC,CAAC;EACrD,CAAC;EACD;AACF;AACA;;EAGEG,oBAAoB,CAACQ,SAAS,CAACQ,UAAU,GAAG,YAAY;IACtDf,MAAM,CAACO,SAAS,CAACQ,UAAU,CAACN,IAAI,CAAC,IAAI,CAAC;IAEtC,IAAI,CAACK,gBAAgB,CAACE,SAAS,CAAC,CAAC;IAEjC,IAAI,CAACJ,eAAe,CAACK,cAAc,CAAC,CAAC;IAErC,IAAI,CAACL,eAAe,CAACM,UAAU,GAAG,IAAI;EACxC,CAAC;EACD;AACF;AACA;;EAGEnB,oBAAoB,CAACQ,SAAS,CAACY,WAAW,GAAG,UAAUC,SAAS,EAAEC,WAAW,EAAEC,OAAO,EAAEC,GAAG,EAAEC,QAAQ,EAAEC,MAAM,EAAEC,gBAAgB,EAAE;IAC/H,IAAIC,IAAI,GAAG,IAAI,CAAC,CAAC;;IAEjB3B,MAAM,CAACO,SAAS,CAACY,WAAW,CAACV,IAAI,CAAC,IAAI,EAAEW,SAAS,EAAEC,WAAW,EAAEC,OAAO,EAAEC,GAAG,EAAEC,QAAQ,EAAEC,MAAM,EAAEC,gBAAgB,CAAC;IAEjH,IAAIE,eAAe,GAAG,IAAI,CAACd,gBAAgB,CAAC,CAAC;IAC7C;;IAEA,IAAIe,YAAY,GAAGR,WAAW,CAACS,GAAG,CAAC,cAAc,EAAE,IAAI,CAAC;IACxD,IAAIC,eAAe,GAAGvC,MAAM,CAACwC,OAAO,CAACH,YAAY,CAAC,GAAGA,YAAY,GAAG,CAACA,YAAY,EAAEA,YAAY,CAAC;IAChGI,gBAAgB,CAAC,UAAU,EAAE,CAAC,CAAC;IAC/B,IAAIC,kBAAkB,GAAGb,WAAW,CAACc,QAAQ,CAAC,eAAe,CAAC;IAC9DP,eAAe,CAACjB,GAAG,CAAC,IAAIlB,OAAO,CAAC2C,IAAI,CAAC;MACnCC,IAAI,EAAE,UAAU;MAChBC,KAAK,EAAE;QACL;QACAC,IAAI,EAAE,OAAO;QACbC,IAAI,EAAEN,kBAAkB,CAACO,YAAY,CAAC,CAAC;QACvCC,IAAI,EAAER,kBAAkB,CAACS,OAAO,CAAC,CAAC;QAClCC,aAAa,EAAE,QAAQ;QACvBC,KAAK,EAAE;MACT,CAAC;MACDC,MAAM,EAAE;IACV,CAAC,CAAC,CAAC;IACHb,gBAAgB,CAAC,UAAU,EAAE,CAAC,CAAC;IAE/B,SAASA,gBAAgBA,CAACI,IAAI,EAAEU,OAAO,EAAE;MACvC,IAAIC,iBAAiB,GAAGX,IAAI,GAAG,WAAW;MAC1C,IAAIY,IAAI,GAAGxD,OAAO,CAACyD,UAAU,CAAC7B,WAAW,CAACS,GAAG,CAAC,WAAW,EAAE,IAAI,CAAC,CAACT,WAAW,CAAC8B,SAAS,CAAC,CAAC,CAACd,IAAI,CAAC,CAACU,OAAO,CAAC,EAAE;QACvG;QACA;QACAK,OAAO,EAAE5D,MAAM,CAAC6D,IAAI,CAAC1B,IAAI,CAAC2B,OAAO,EAAE3B,IAAI,EAAEqB,iBAAiB,EAAE3B,WAAW,EAAEE,GAAG;MAC9E,CAAC,EAAE;QACDgC,CAAC,EAAE,CAACxB,eAAe,CAAC,CAAC,CAAC,GAAG,CAAC;QAC1ByB,CAAC,EAAE,CAACzB,eAAe,CAAC,CAAC,CAAC,GAAG,CAAC;QAC1B0B,KAAK,EAAE1B,eAAe,CAAC,CAAC,CAAC;QACzB2B,MAAM,EAAE3B,eAAe,CAAC,CAAC;MAC3B,CAAC,CAAC;MACFkB,IAAI,CAACZ,IAAI,GAAGA,IAAI;MAChBT,eAAe,CAACjB,GAAG,CAACsC,IAAI,CAAC;IAC3B;EACF,CAAC;EACD;AACF;AACA;;EAGElD,oBAAoB,CAACQ,SAAS,CAACoD,WAAW,GAAG,UAAUtC,WAAW,EAAED,SAAS,EAAEwC,OAAO,EAAEC,aAAa,EAAErC,QAAQ,EAAEE,gBAAgB,EAAE;IACjI,IAAIoC,aAAa,GAAG,IAAI,CAACC,gBAAgB,CAAC,CAAC;IAC3C,IAAIC,SAAS,GAAG3C,WAAW,CAAC8B,SAAS,CAAC,CAAC,CAACc,KAAK;IAC7C,IAAIC,EAAE,GAAGrE,EAAE,CAACmE,SAAS,CAAC;IACtB,IAAIG,EAAE,GAAGrE,EAAE,CAACkE,SAAS,CAAC;IACtB,IAAII,EAAE,GAAGvE,EAAE,CAAC,CAAC,GAAGmE,SAAS,CAAC;IAC1B,IAAIK,EAAE,GAAGvE,EAAE,CAAC,CAAC,GAAGkE,SAAS,CAAC;IAC1BxC,QAAQ,IAAI9B,UAAU,CAAC4E,GAAG;IAAE;IAC5B,YAAY,EAAER,aAAa,EAAEzC,WAAW,CAACS,GAAG,CAAC,iBAAiB,EAAE,IAAI,CAAC,CAAC;IACtE,IAAIyC,iBAAiB,GAAGlD,WAAW,CAACS,GAAG,CAAC,mBAAmB,EAAE,IAAI,CAAC;IAClE,IAAI0C,YAAY,GAAGV,aAAa,CAACW,eAAe,CAAC,CAAC;IAClD,IAAIC,WAAW,GAAG,CAAC,CAACF,YAAY,CAACjB,CAAC,EAAE,CAACiB,YAAY,CAAChB,CAAC,CAAC;IACpD,IAAImB,cAAc,GAAGnF,MAAM,CAACoF,KAAK,CAAChB,OAAO,CAAC;IAC1CpC,QAAQ,KAAKmD,cAAc,CAACT,EAAE,CAAC,GAAGN,OAAO,CAACM,EAAE,CAAC,GAAGM,YAAY,CAACN,EAAE,CAAC,GAAGK,iBAAiB,CAAC;IAErF,IAAIM,QAAQ,GAAG,IAAI,CAACC,2BAA2B,CAACzD,WAAW,EAAEwC,aAAa,EAAEc,cAAc,EAAEX,SAAS,EAAEE,EAAE,EAAEE,EAAE,EAAEC,EAAE,EAAEF,EAAE,CAAC;IAEtH,IAAI3C,QAAQ,EAAE;MACZ,IAAIE,gBAAgB,KAAK,KAAK,EAAE;QAC9BgD,WAAW,CAACV,SAAS,CAAC,IAAIa,QAAQ,CAACX,EAAE,CAAC,GAAGK,iBAAiB;MAC5D,CAAC,MAAM;QACL,IAAIQ,MAAM,GAAGP,YAAY,CAACN,EAAE,CAAC,GAAGK,iBAAiB;QACjDG,WAAW,CAACV,SAAS,CAAC,IAAIe,MAAM;QAChCF,QAAQ,CAACV,EAAE,CAAC,IAAIY,MAAM;MACxB;MAEAF,QAAQ,CAACX,EAAE,CAAC,IAAIM,YAAY,CAACN,EAAE,CAAC,GAAGK,iBAAiB;MACpDG,WAAW,CAAC,CAAC,GAAGV,SAAS,CAAC,IAAIa,QAAQ,CAACR,EAAE,CAAC,GAAGQ,QAAQ,CAACT,EAAE,CAAC,GAAG,CAAC,GAAGI,YAAY,CAACJ,EAAE,CAAC,GAAG,CAAC;MACpFS,QAAQ,CAACT,EAAE,CAAC,GAAGY,IAAI,CAACC,GAAG,CAACJ,QAAQ,CAACT,EAAE,CAAC,EAAEI,YAAY,CAACJ,EAAE,CAAC,CAAC;MACvDS,QAAQ,CAACR,EAAE,CAAC,GAAGW,IAAI,CAACE,GAAG,CAACL,QAAQ,CAACR,EAAE,CAAC,EAAEG,YAAY,CAACH,EAAE,CAAC,GAAGK,WAAW,CAAC,CAAC,GAAGV,SAAS,CAAC,CAAC;MACpFF,aAAa,CAACP,CAAC,GAAGmB,WAAW,CAAC,CAAC,CAAC;MAChCZ,aAAa,CAACN,CAAC,GAAGkB,WAAW,CAAC,CAAC,CAAC;MAChCZ,aAAa,CAACqB,UAAU,CAAC,CAAC;IAC5B;IAEA,OAAON,QAAQ;EACjB,CAAC;EAED9E,oBAAoB,CAACQ,SAAS,CAACuE,2BAA2B,GAAG,UAAUzD,WAAW,EAAEwC,aAAa,EAAED,OAAO,EAAEI,SAAS,EAAEE,EAAE,EAAEE,EAAE,EAAEC,EAAE,EAAEF,EAAE,EAAE;IACrI,IAAIiB,YAAY,GAAG,IAAI,CAACvE,eAAe,CAAC,CAAC;IACzC,IAAIwE,cAAc,GAAG,IAAI,CAACzE,eAAe;IACzC,IAAIgB,eAAe,GAAG,IAAI,CAACd,gBAAgB,CAAC,CAAC;;IAE7CpB,UAAU,CAAC4E,GAAG,CAACjD,WAAW,CAACS,GAAG,CAAC,QAAQ,CAAC,EAAEsD,YAAY,EAAE/D,WAAW,CAACS,GAAG,CAAC,SAAS,CAAC,EAAE,CAACkC,SAAS,GAAG,IAAI,GAAGJ,OAAO,CAACH,KAAK,EAAEO,SAAS,GAAG,IAAI,GAAGJ,OAAO,CAACF,MAAM,CAAC;IACzJhE,UAAU,CAAC4E,GAAG;IAAE;IAChB,YAAY,EAAE1C,eAAe,EAAEP,WAAW,CAACS,GAAG,CAAC,mBAAmB,EAAE,IAAI,CAAC,CAAC;IAC1E,IAAIwD,WAAW,GAAGF,YAAY,CAACX,eAAe,CAAC,CAAC;IAChD,IAAIc,cAAc,GAAG3D,eAAe,CAAC6C,eAAe,CAAC,CAAC;IACtD,IAAIe,cAAc,GAAG,IAAI,CAACC,eAAe,GAAGH,WAAW,CAACpB,EAAE,CAAC,GAAGN,OAAO,CAACM,EAAE,CAAC,CAAC,CAAC;;IAE3E,IAAIwB,UAAU,GAAG,CAAC,CAACJ,WAAW,CAAC/B,CAAC,EAAE,CAAC+B,WAAW,CAAC9B,CAAC,CAAC,CAAC,CAAC;IACnD;IACA;;IAEA,IAAI,CAACK,aAAa,EAAE;MAClB6B,UAAU,CAAC1B,SAAS,CAAC,GAAGoB,YAAY,CAACjB,EAAE,CAAC;IAC1C,CAAC,CAAC;;IAGF,IAAIwB,YAAY,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC;IACzB,IAAIC,aAAa,GAAG,CAAC,CAACL,cAAc,CAAChC,CAAC,EAAE,CAACgC,cAAc,CAAC/B,CAAC,CAAC;IAC1D,IAAIqC,aAAa,GAAGrG,MAAM,CAACsG,SAAS,CAACzE,WAAW,CAACS,GAAG,CAAC,eAAe,EAAE,IAAI,CAAC,EAAET,WAAW,CAACS,GAAG,CAAC,SAAS,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC;;IAEhH,IAAI0D,cAAc,EAAE;MAClB,IAAIO,kBAAkB,GAAG1E,WAAW,CAACS,GAAG,CAAC,oBAAoB,EAAE,IAAI,CAAC,CAAC,CAAC;;MAEtE,IAAIiE,kBAAkB,KAAK,KAAK,EAAE;QAChCH,aAAa,CAAC5B,SAAS,CAAC,IAAIJ,OAAO,CAACM,EAAE,CAAC,GAAGqB,cAAc,CAACrB,EAAE,CAAC;MAC9D,CAAC,CAAC;MAAA,KACG;QACDyB,YAAY,CAAC3B,SAAS,CAAC,IAAIuB,cAAc,CAACrB,EAAE,CAAC,GAAG2B,aAAa;MAC/D;IACJ,CAAC,CAAC;;IAGFD,aAAa,CAAC,CAAC,GAAG5B,SAAS,CAAC,IAAIsB,WAAW,CAAClB,EAAE,CAAC,GAAG,CAAC,GAAGmB,cAAc,CAACnB,EAAE,CAAC,GAAG,CAAC;IAC5EgB,YAAY,CAACY,WAAW,CAACN,UAAU,CAAC;IACpCL,cAAc,CAACW,WAAW,CAACL,YAAY,CAAC;IACxC/D,eAAe,CAACoE,WAAW,CAACJ,aAAa,CAAC,CAAC,CAAC;IAC5C;IACA;;IAEA,IAAIf,QAAQ,GAAG;MACbtB,CAAC,EAAE,CAAC;MACJC,CAAC,EAAE;IACL,CAAC,CAAC,CAAC;;IAEHqB,QAAQ,CAACX,EAAE,CAAC,GAAGsB,cAAc,GAAG5B,OAAO,CAACM,EAAE,CAAC,GAAGoB,WAAW,CAACpB,EAAE,CAAC;IAC7DW,QAAQ,CAACT,EAAE,CAAC,GAAGY,IAAI,CAACC,GAAG,CAACK,WAAW,CAAClB,EAAE,CAAC,EAAEmB,cAAc,CAACnB,EAAE,CAAC,CAAC,CAAC,CAAC;;IAE9DS,QAAQ,CAACR,EAAE,CAAC,GAAGW,IAAI,CAACE,GAAG,CAAC,CAAC,EAAEK,cAAc,CAAClB,EAAE,CAAC,GAAGuB,aAAa,CAAC,CAAC,GAAG5B,SAAS,CAAC,CAAC;IAC7EqB,cAAc,CAACnE,UAAU,GAAG0C,OAAO,CAACM,EAAE,CAAC;IAEvC,IAAIsB,cAAc,EAAE;MAClB,IAAIS,SAAS,GAAG;QACd1C,CAAC,EAAE,CAAC;QACJC,CAAC,EAAE;MACL,CAAC;MACDyC,SAAS,CAAC/B,EAAE,CAAC,GAAGc,IAAI,CAACC,GAAG,CAACrB,OAAO,CAACM,EAAE,CAAC,GAAGqB,cAAc,CAACrB,EAAE,CAAC,GAAG2B,aAAa,EAAE,CAAC,CAAC;MAC7EI,SAAS,CAAC7B,EAAE,CAAC,GAAGS,QAAQ,CAACT,EAAE,CAAC;MAC5BiB,cAAc,CAACa,WAAW,CAAC,IAAIzG,OAAO,CAAC0G,IAAI,CAAC;QAC1CC,KAAK,EAAEH;MACT,CAAC,CAAC,CAAC,CAAC,CAAC;MACL;;MAEAZ,cAAc,CAACnE,UAAU,GAAG+E,SAAS,CAAC/B,EAAE,CAAC;IAC3C,CAAC,MAAM;MACL;MACAtC,eAAe,CAACyE,SAAS,CAAC,UAAUC,KAAK,EAAE;QACzCA,KAAK,CAACC,IAAI,CAAC;UACTC,SAAS,EAAE,IAAI;UACf1D,MAAM,EAAE;QACV,CAAC,CAAC;MACJ,CAAC,CAAC;IACJ,CAAC,CAAC;;IAGF,IAAI2D,QAAQ,GAAG,IAAI,CAACC,YAAY,CAACrF,WAAW,CAAC;IAE7CoF,QAAQ,CAACE,SAAS,IAAI,IAAI,IAAIlH,OAAO,CAACmH,WAAW,CAACxB,YAAY,EAAE;MAC9D7B,CAAC,EAAEkD,QAAQ,CAACI,eAAe,CAAC,CAAC,CAAC;MAC9BrD,CAAC,EAAEiD,QAAQ,CAACI,eAAe,CAAC,CAAC;IAC/B,CAAC;IAAE;IACH;IACArB,cAAc,GAAGnE,WAAW,GAAG,IAAI,CAAC;IAEpC,IAAI,CAACyF,mBAAmB,CAACzF,WAAW,EAAEoF,QAAQ,CAAC;IAE/C,OAAO5B,QAAQ;EACjB,CAAC;EAED9E,oBAAoB,CAACQ,SAAS,CAAC+C,OAAO,GAAG,UAAUyD,EAAE,EAAE1F,WAAW,EAAEE,GAAG,EAAE;IACvE,IAAIyF,eAAe,GAAG,IAAI,CAACN,YAAY,CAACrF,WAAW,CAAC,CAAC0F,EAAE,CAAC;IAExDC,eAAe,IAAI,IAAI,IAAIzF,GAAG,CAAC0F,cAAc,CAAC;MAC5C7G,IAAI,EAAE,cAAc;MACpB4G,eAAe,EAAEA,eAAe;MAChCE,QAAQ,EAAE7F,WAAW,CAAC8F;IACxB,CAAC,CAAC;EACJ,CAAC;EAEDpH,oBAAoB,CAACQ,SAAS,CAACuG,mBAAmB,GAAG,UAAUzF,WAAW,EAAEoF,QAAQ,EAAE;IACpF,IAAI7E,eAAe,GAAG,IAAI,CAACd,gBAAgB;IAC3CtB,MAAM,CAAC4H,IAAI,CAAC,CAAC,UAAU,EAAE,UAAU,CAAC,EAAE,UAAU/E,IAAI,EAAE;MACpD,IAAIgF,GAAG,GAAGhF,IAAI,GAAG,WAAW;MAC5B,IAAIiF,OAAO,GAAGb,QAAQ,CAACY,GAAG,CAAC,IAAI,IAAI;MACnC,IAAIpE,IAAI,GAAGrB,eAAe,CAAC2F,WAAW,CAAClF,IAAI,CAAC;MAE5C,IAAIY,IAAI,EAAE;QACRA,IAAI,CAACuE,QAAQ,CAAC,MAAM,EAAEF,OAAO,GAAGjG,WAAW,CAACS,GAAG,CAAC,eAAe,EAAE,IAAI,CAAC,GAAGT,WAAW,CAACS,GAAG,CAAC,uBAAuB,EAAE,IAAI,CAAC,CAAC;QACxHmB,IAAI,CAACwE,MAAM,GAAGH,OAAO,GAAG,SAAS,GAAG,SAAS;MAC/C;IACF,CAAC,CAAC;IACF,IAAII,QAAQ,GAAG9F,eAAe,CAAC2F,WAAW,CAAC,UAAU,CAAC;IACtD,IAAII,aAAa,GAAGtG,WAAW,CAACS,GAAG,CAAC,eAAe,CAAC;IACpD,IAAI6E,SAAS,GAAGF,QAAQ,CAACE,SAAS;IAClC,IAAIiB,OAAO,GAAGjB,SAAS,IAAI,IAAI,GAAGA,SAAS,GAAG,CAAC,GAAG,CAAC;IACnD,IAAIkB,KAAK,GAAGpB,QAAQ,CAACqB,SAAS;IAC9BJ,QAAQ,IAAIC,aAAa,IAAID,QAAQ,CAACF,QAAQ,CAAC,MAAM,EAAEhI,MAAM,CAACuI,QAAQ,CAACJ,aAAa,CAAC,GAAGA,aAAa,CAACK,OAAO,CAAC,WAAW,EAAEJ,OAAO,IAAI,IAAI,GAAG,EAAE,GAAGA,OAAO,GAAG,EAAE,CAAC,CAACI,OAAO,CAAC,SAAS,EAAEH,KAAK,IAAI,IAAI,GAAG,EAAE,GAAGA,KAAK,GAAG,EAAE,CAAC,GAAGF,aAAa,CAAC;MAClOC,OAAO,EAAEA,OAAO;MAChBC,KAAK,EAAEA;IACT,CAAC,CAAC,CAAC;EACL,CAAC;EACD;AACF;AACA;AACA;AACA;AACA;AACA;AACA;;EAGE9H,oBAAoB,CAACQ,SAAS,CAACmG,YAAY,GAAG,UAAUrF,WAAW,EAAE;IACnE,IAAI2F,eAAe,GAAG3F,WAAW,CAACS,GAAG,CAAC,iBAAiB,EAAE,IAAI,CAAC;IAC9D,IAAIsD,YAAY,GAAG,IAAI,CAACvE,eAAe,CAAC,CAAC;IACzC,IAAIoH,iBAAiB,GAAG,IAAI,CAACrH,eAAe,CAACM,UAAU;IACvD,IAAI8C,SAAS,GAAG3C,WAAW,CAAC8B,SAAS,CAAC,CAAC,CAACc,KAAK;IAC7C,IAAIC,EAAE,GAAGrE,EAAE,CAACmE,SAAS,CAAC;IACtB,IAAIG,EAAE,GAAGrE,EAAE,CAACkE,SAAS,CAAC;IAEtB,IAAIkE,eAAe,GAAG,IAAI,CAACC,oBAAoB,CAACnB,eAAe,CAAC;IAEhE,IAAIoB,QAAQ,GAAGhD,YAAY,CAACgD,QAAQ,CAAC,CAAC;IACtC,IAAIC,UAAU,GAAGD,QAAQ,CAACF,eAAe,CAAC;IAC1C,IAAII,SAAS,GAAGF,QAAQ,CAACG,MAAM;IAC/B,IAAIC,MAAM,GAAG,CAACF,SAAS,GAAG,CAAC,GAAG,CAAC;IAC/B,IAAIG,MAAM,GAAG;MACX5B,eAAe,EAAE,CAACzB,YAAY,CAAC7B,CAAC,EAAE6B,YAAY,CAAC5B,CAAC,CAAC;MACjDsE,SAAS,EAAEU,MAAM;MACjB7B,SAAS,EAAE6B,MAAM,GAAG,CAAC;MACrBE,iBAAiB,EAAE,IAAI;MACvBC,iBAAiB,EAAE;IACrB,CAAC;IAED,IAAI,CAACN,UAAU,EAAE;MACf,OAAOI,MAAM;IACf;IAEA,IAAIG,cAAc,GAAGC,WAAW,CAACR,UAAU,CAAC;IAC5CI,MAAM,CAAC5B,eAAe,CAAC7C,SAAS,CAAC,GAAG,CAAC4E,cAAc,CAACE,CAAC,CAAC,CAAC;IACvD;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;;IAEA,KAAK,IAAIC,CAAC,GAAGb,eAAe,GAAG,CAAC,EAAEc,gBAAgB,GAAGJ,cAAc,EAAEK,cAAc,GAAGL,cAAc,EAAEM,YAAY,GAAG,IAAI,EAAEH,CAAC,IAAIT,SAAS,EAAE,EAAES,CAAC,EAAE;MAC9IG,YAAY,GAAGL,WAAW,CAACT,QAAQ,CAACW,CAAC,CAAC,CAAC;MAEvC;MAAK;MACL,CAACG,YAAY,IAAID,cAAc,CAACE,CAAC,GAAGH,gBAAgB,CAACF,CAAC,GAAGb,iBAAiB;MAAI;MAC9E;MACAiB,YAAY,IAAI,CAACE,SAAS,CAACF,YAAY,EAAEF,gBAAgB,CAACF,CAAC,CAAC,EAAE;QAC5D,IAAIG,cAAc,CAACF,CAAC,GAAGC,gBAAgB,CAACD,CAAC,EAAE;UACzCC,gBAAgB,GAAGC,cAAc;QACnC,CAAC,MAAM;UACL;UACAD,gBAAgB,GAAGE,YAAY;QACjC;QAEA,IAAIF,gBAAgB,EAAE;UACpB,IAAIP,MAAM,CAACE,iBAAiB,IAAI,IAAI,EAAE;YACpCF,MAAM,CAACE,iBAAiB,GAAGK,gBAAgB,CAACD,CAAC;UAC/C;UAEA,EAAEN,MAAM,CAACX,SAAS;QACpB;MACF;MAEAmB,cAAc,GAAGC,YAAY;IAC/B;IAEA,KAAK,IAAIH,CAAC,GAAGb,eAAe,GAAG,CAAC,EAAEc,gBAAgB,GAAGJ,cAAc,EAAEK,cAAc,GAAGL,cAAc,EAAEM,YAAY,GAAG,IAAI,EAAEH,CAAC,IAAI,CAAC,CAAC,EAAE,EAAEA,CAAC,EAAE;MACvIG,YAAY,GAAGL,WAAW,CAACT,QAAQ,CAACW,CAAC,CAAC,CAAC;MAEvC;MAAK;MACL;MACA,CAAC,CAACG,YAAY,IAAI,CAACE,SAAS,CAACH,cAAc,EAAEC,YAAY,CAACJ,CAAC,CAAC;MAAK;MACjEE,gBAAgB,CAACD,CAAC,GAAGE,cAAc,CAACF,CAAC,EAAE;QACrCE,cAAc,GAAGD,gBAAgB;QAEjC,IAAIP,MAAM,CAACC,iBAAiB,IAAI,IAAI,EAAE;UACpCD,MAAM,CAACC,iBAAiB,GAAGM,gBAAgB,CAACD,CAAC;QAC/C;QAEA,EAAEN,MAAM,CAACX,SAAS;QAClB,EAAEW,MAAM,CAAC9B,SAAS;MACpB;MAEAqC,gBAAgB,GAAGE,YAAY;IACjC;IAEA,OAAOT,MAAM;IAEb,SAASI,WAAWA,CAACQ,EAAE,EAAE;MACvB,IAAIA,EAAE,EAAE;QACN,IAAIC,QAAQ,GAAGD,EAAE,CAAC5E,eAAe,CAAC,CAAC;QACnC,IAAI8E,KAAK,GAAGD,QAAQ,CAACnF,EAAE,CAAC,GAAGkF,EAAE,CAAClF,EAAE,CAAC;QACjC,OAAO;UACL2E,CAAC,EAAES,KAAK;UACRJ,CAAC,EAAEI,KAAK,GAAGD,QAAQ,CAACpF,EAAE,CAAC;UACvB6E,CAAC,EAAEM,EAAE,CAACG;QACR,CAAC;MACH;IACF;IAEA,SAASJ,SAASA,CAACK,QAAQ,EAAEC,QAAQ,EAAE;MACrC,OAAOD,QAAQ,CAACN,CAAC,IAAIO,QAAQ,IAAID,QAAQ,CAACX,CAAC,IAAIY,QAAQ,GAAGzB,iBAAiB;IAC7E;EACF,CAAC;EAEDlI,oBAAoB,CAACQ,SAAS,CAAC4H,oBAAoB,GAAG,UAAUwB,eAAe,EAAE;IAC/E,IAAI,CAAC,IAAI,CAAClE,eAAe,EAAE;MACzB,OAAO,CAAC;IACV;IAEA,IAAIxB,KAAK;IACT,IAAImB,YAAY,GAAG,IAAI,CAACvE,eAAe,CAAC,CAAC;IACzC,IAAI+I,YAAY;IAChBxE,YAAY,CAACiB,SAAS,CAAC,UAAUC,KAAK,EAAEuD,GAAG,EAAE;MAC3C,IAAIC,aAAa,GAAGxD,KAAK,CAACkD,iBAAiB,CAAC,CAAC;MAC7C;MACA;MACA;MACA;;MAEA,IAAII,YAAY,IAAI,IAAI,IAAIE,aAAa,IAAI,IAAI,EAAE;QACjDF,YAAY,GAAGC,GAAG;MACpB;MAEA,IAAIC,aAAa,KAAKH,eAAe,EAAE;QACrC1F,KAAK,GAAG4F,GAAG;MACb;IACF,CAAC,CAAC;IACF,OAAO5F,KAAK,IAAI,IAAI,GAAGA,KAAK,GAAG2F,YAAY;EAC7C,CAAC;EAED7J,oBAAoB,CAACK,IAAI,GAAG,eAAe;EAC3C,OAAOL,oBAAoB;AAC7B,CAAC,CAACJ,UAAU,CAAC;AAEb,eAAeI,oBAAoB"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}