{"ast": null, "code": "import castPath from './_castPath.js';\nimport isArguments from './isArguments.js';\nimport isArray from './isArray.js';\nimport isIndex from './_isIndex.js';\nimport isLength from './isLength.js';\nimport toKey from './_toKey.js';\n\n/**\n * Checks if `path` exists on `object`.\n *\n * @private\n * @param {Object} object The object to query.\n * @param {Array|string} path The path to check.\n * @param {Function} hasFunc The function to check properties.\n * @returns {boolean} Returns `true` if `path` exists, else `false`.\n */\nfunction hasPath(object, path, hasFunc) {\n  path = castPath(path, object);\n  var index = -1,\n    length = path.length,\n    result = false;\n  while (++index < length) {\n    var key = toKey(path[index]);\n    if (!(result = object != null && hasFunc(object, key))) {\n      break;\n    }\n    object = object[key];\n  }\n  if (result || ++index != length) {\n    return result;\n  }\n  length = object == null ? 0 : object.length;\n  return !!length && isLength(length) && isIndex(key, length) && (isArray(object) || isArguments(object));\n}\nexport default hasPath;", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON>", "isArguments", "isArray", "isIndex", "<PERSON><PERSON><PERSON><PERSON>", "to<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "object", "path", "hasFunc", "index", "length", "result", "key"], "sources": ["C:/WolfSystemV2/WolfOldFrontEnd/node_modules/lodash-es/_hasPath.js"], "sourcesContent": ["import castPath from './_castPath.js';\nimport isArguments from './isArguments.js';\nimport isArray from './isArray.js';\nimport isIndex from './_isIndex.js';\nimport isLength from './isLength.js';\nimport toKey from './_toKey.js';\n\n/**\n * Checks if `path` exists on `object`.\n *\n * @private\n * @param {Object} object The object to query.\n * @param {Array|string} path The path to check.\n * @param {Function} hasFunc The function to check properties.\n * @returns {boolean} Returns `true` if `path` exists, else `false`.\n */\nfunction hasPath(object, path, hasFunc) {\n  path = castPath(path, object);\n\n  var index = -1,\n      length = path.length,\n      result = false;\n\n  while (++index < length) {\n    var key = toKey(path[index]);\n    if (!(result = object != null && hasFunc(object, key))) {\n      break;\n    }\n    object = object[key];\n  }\n  if (result || ++index != length) {\n    return result;\n  }\n  length = object == null ? 0 : object.length;\n  return !!length && isLength(length) && isIndex(key, length) &&\n    (isArray(object) || isArguments(object));\n}\n\nexport default hasPath;\n"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,gBAAgB;AACrC,OAAOC,WAAW,MAAM,kBAAkB;AAC1C,OAAOC,OAAO,MAAM,cAAc;AAClC,OAAOC,OAAO,MAAM,eAAe;AACnC,OAAOC,QAAQ,MAAM,eAAe;AACpC,OAAOC,KAAK,MAAM,aAAa;;AAE/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,OAAOA,CAACC,MAAM,EAAEC,IAAI,EAAEC,OAAO,EAAE;EACtCD,IAAI,GAAGR,QAAQ,CAACQ,IAAI,EAAED,MAAM,CAAC;EAE7B,IAAIG,KAAK,GAAG,CAAC,CAAC;IACVC,MAAM,GAAGH,IAAI,CAACG,MAAM;IACpBC,MAAM,GAAG,KAAK;EAElB,OAAO,EAAEF,KAAK,GAAGC,MAAM,EAAE;IACvB,IAAIE,GAAG,GAAGR,KAAK,CAACG,IAAI,CAACE,KAAK,CAAC,CAAC;IAC5B,IAAI,EAAEE,MAAM,GAAGL,MAAM,IAAI,IAAI,IAAIE,OAAO,CAACF,MAAM,EAAEM,GAAG,CAAC,CAAC,EAAE;MACtD;IACF;IACAN,MAAM,GAAGA,MAAM,CAACM,GAAG,CAAC;EACtB;EACA,IAAID,MAAM,IAAI,EAAEF,KAAK,IAAIC,MAAM,EAAE;IAC/B,OAAOC,MAAM;EACf;EACAD,MAAM,GAAGJ,MAAM,IAAI,IAAI,GAAG,CAAC,GAAGA,MAAM,CAACI,MAAM;EAC3C,OAAO,CAAC,CAACA,MAAM,IAAIP,QAAQ,CAACO,MAAM,CAAC,IAAIR,OAAO,CAACU,GAAG,EAAEF,MAAM,CAAC,KACxDT,OAAO,CAACK,MAAM,CAAC,IAAIN,WAAW,CAACM,MAAM,CAAC,CAAC;AAC5C;AAEA,eAAeD,OAAO"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}