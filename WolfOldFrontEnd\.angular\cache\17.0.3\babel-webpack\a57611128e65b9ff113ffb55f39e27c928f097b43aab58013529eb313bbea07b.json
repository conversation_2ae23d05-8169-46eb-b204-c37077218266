{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { each, map, isFunction, createHashMap, noop, assert } from 'zrender/lib/core/util.js';\nimport { createTask } from './task.js';\nimport { getUID } from '../util/component.js';\nimport GlobalModel from '../model/Global.js';\nimport ExtensionAPI from './ExtensionAPI.js';\nimport { normalizeToArray } from '../util/model.js';\n;\nvar Scheduler = /** @class */\nfunction () {\n  function Scheduler(ecInstance, api, dataProcessorHandlers, visualHandlers) {\n    // key: handlerUID\n    this._stageTaskMap = createHashMap();\n    this.ecInstance = ecInstance;\n    this.api = api; // Fix current processors in case that in some rear cases that\n    // processors might be registered after echarts instance created.\n    // Register processors incrementally for a echarts instance is\n    // not supported by this stream architecture.\n\n    dataProcessorHandlers = this._dataProcessorHandlers = dataProcessorHandlers.slice();\n    visualHandlers = this._visualHandlers = visualHandlers.slice();\n    this._allHandlers = dataProcessorHandlers.concat(visualHandlers);\n  }\n  Scheduler.prototype.restoreData = function (ecModel, payload) {\n    // TODO: Only restore needed series and components, but not all components.\n    // Currently `restoreData` of all of the series and component will be called.\n    // But some independent components like `title`, `legend`, `graphic`, `toolbox`,\n    // `tooltip`, `axisPointer`, etc, do not need series refresh when `setOption`,\n    // and some components like coordinate system, axes, dataZoom, visualMap only\n    // need their target series refresh.\n    // (1) If we are implementing this feature some day, we should consider these cases:\n    // if a data processor depends on a component (e.g., dataZoomProcessor depends\n    // on the settings of `dataZoom`), it should be re-performed if the component\n    // is modified by `setOption`.\n    // (2) If a processor depends on sevral series, speicified by its `getTargetSeries`,\n    // it should be re-performed when the result array of `getTargetSeries` changed.\n    // We use `dependencies` to cover these issues.\n    // (3) How to update target series when coordinate system related components modified.\n    // TODO: simply the dirty mechanism? Check whether only the case here can set tasks dirty,\n    // and this case all of the tasks will be set as dirty.\n    ecModel.restoreData(payload); // Theoretically an overall task not only depends on each of its target series, but also\n    // depends on all of the series.\n    // The overall task is not in pipeline, and `ecModel.restoreData` only set pipeline tasks\n    // dirty. If `getTargetSeries` of an overall task returns nothing, we should also ensure\n    // that the overall task is set as dirty and to be performed, otherwise it probably cause\n    // state chaos. So we have to set dirty of all of the overall tasks manually, otherwise it\n    // probably cause state chaos (consider `dataZoomProcessor`).\n\n    this._stageTaskMap.each(function (taskRecord) {\n      var overallTask = taskRecord.overallTask;\n      overallTask && overallTask.dirty();\n    });\n  }; // If seriesModel provided, incremental threshold is check by series data.\n\n  Scheduler.prototype.getPerformArgs = function (task, isBlock) {\n    // For overall task\n    if (!task.__pipeline) {\n      return;\n    }\n    var pipeline = this._pipelineMap.get(task.__pipeline.id);\n    var pCtx = pipeline.context;\n    var incremental = !isBlock && pipeline.progressiveEnabled && (!pCtx || pCtx.progressiveRender) && task.__idxInPipeline > pipeline.blockIndex;\n    var step = incremental ? pipeline.step : null;\n    var modDataCount = pCtx && pCtx.modDataCount;\n    var modBy = modDataCount != null ? Math.ceil(modDataCount / step) : null;\n    return {\n      step: step,\n      modBy: modBy,\n      modDataCount: modDataCount\n    };\n  };\n  Scheduler.prototype.getPipeline = function (pipelineId) {\n    return this._pipelineMap.get(pipelineId);\n  };\n  /**\r\n   * Current, progressive rendering starts from visual and layout.\r\n   * Always detect render mode in the same stage, avoiding that incorrect\r\n   * detection caused by data filtering.\r\n   * Caution:\r\n   * `updateStreamModes` use `seriesModel.getData()`.\r\n   */\n\n  Scheduler.prototype.updateStreamModes = function (seriesModel, view) {\n    var pipeline = this._pipelineMap.get(seriesModel.uid);\n    var data = seriesModel.getData();\n    var dataLen = data.count(); // `progressiveRender` means that can render progressively in each\n    // animation frame. Note that some types of series do not provide\n    // `view.incrementalPrepareRender` but support `chart.appendData`. We\n    // use the term `incremental` but not `progressive` to describe the\n    // case that `chart.appendData`.\n\n    var progressiveRender = pipeline.progressiveEnabled && view.incrementalPrepareRender && dataLen >= pipeline.threshold;\n    var large = seriesModel.get('large') && dataLen >= seriesModel.get('largeThreshold'); // TODO: modDataCount should not updated if `appendData`, otherwise cause whole repaint.\n    // see `test/candlestick-large3.html`\n\n    var modDataCount = seriesModel.get('progressiveChunkMode') === 'mod' ? dataLen : null;\n    seriesModel.pipelineContext = pipeline.context = {\n      progressiveRender: progressiveRender,\n      modDataCount: modDataCount,\n      large: large\n    };\n  };\n  Scheduler.prototype.restorePipelines = function (ecModel) {\n    var scheduler = this;\n    var pipelineMap = scheduler._pipelineMap = createHashMap();\n    ecModel.eachSeries(function (seriesModel) {\n      var progressive = seriesModel.getProgressive();\n      var pipelineId = seriesModel.uid;\n      pipelineMap.set(pipelineId, {\n        id: pipelineId,\n        head: null,\n        tail: null,\n        threshold: seriesModel.getProgressiveThreshold(),\n        progressiveEnabled: progressive && !(seriesModel.preventIncremental && seriesModel.preventIncremental()),\n        blockIndex: -1,\n        step: Math.round(progressive || 700),\n        count: 0\n      });\n      scheduler._pipe(seriesModel, seriesModel.dataTask);\n    });\n  };\n  Scheduler.prototype.prepareStageTasks = function () {\n    var stageTaskMap = this._stageTaskMap;\n    var ecModel = this.api.getModel();\n    var api = this.api;\n    each(this._allHandlers, function (handler) {\n      var record = stageTaskMap.get(handler.uid) || stageTaskMap.set(handler.uid, {});\n      var errMsg = '';\n      if (process.env.NODE_ENV !== 'production') {\n        // Currently do not need to support to sepecify them both.\n        errMsg = '\"reset\" and \"overallReset\" must not be both specified.';\n      }\n      assert(!(handler.reset && handler.overallReset), errMsg);\n      handler.reset && this._createSeriesStageTask(handler, record, ecModel, api);\n      handler.overallReset && this._createOverallStageTask(handler, record, ecModel, api);\n    }, this);\n  };\n  Scheduler.prototype.prepareView = function (view, model, ecModel, api) {\n    var renderTask = view.renderTask;\n    var context = renderTask.context;\n    context.model = model;\n    context.ecModel = ecModel;\n    context.api = api;\n    renderTask.__block = !view.incrementalPrepareRender;\n    this._pipe(model, renderTask);\n  };\n  Scheduler.prototype.performDataProcessorTasks = function (ecModel, payload) {\n    // If we do not use `block` here, it should be considered when to update modes.\n    this._performStageTasks(this._dataProcessorHandlers, ecModel, payload, {\n      block: true\n    });\n  };\n  Scheduler.prototype.performVisualTasks = function (ecModel, payload, opt) {\n    this._performStageTasks(this._visualHandlers, ecModel, payload, opt);\n  };\n  Scheduler.prototype._performStageTasks = function (stageHandlers, ecModel, payload, opt) {\n    opt = opt || {};\n    var unfinished = false;\n    var scheduler = this;\n    each(stageHandlers, function (stageHandler, idx) {\n      if (opt.visualType && opt.visualType !== stageHandler.visualType) {\n        return;\n      }\n      var stageHandlerRecord = scheduler._stageTaskMap.get(stageHandler.uid);\n      var seriesTaskMap = stageHandlerRecord.seriesTaskMap;\n      var overallTask = stageHandlerRecord.overallTask;\n      if (overallTask) {\n        var overallNeedDirty_1;\n        var agentStubMap = overallTask.agentStubMap;\n        agentStubMap.each(function (stub) {\n          if (needSetDirty(opt, stub)) {\n            stub.dirty();\n            overallNeedDirty_1 = true;\n          }\n        });\n        overallNeedDirty_1 && overallTask.dirty();\n        scheduler.updatePayload(overallTask, payload);\n        var performArgs_1 = scheduler.getPerformArgs(overallTask, opt.block); // Execute stubs firstly, which may set the overall task dirty,\n        // then execute the overall task. And stub will call seriesModel.setData,\n        // which ensures that in the overallTask seriesModel.getData() will not\n        // return incorrect data.\n\n        agentStubMap.each(function (stub) {\n          stub.perform(performArgs_1);\n        });\n        if (overallTask.perform(performArgs_1)) {\n          unfinished = true;\n        }\n      } else if (seriesTaskMap) {\n        seriesTaskMap.each(function (task, pipelineId) {\n          if (needSetDirty(opt, task)) {\n            task.dirty();\n          }\n          var performArgs = scheduler.getPerformArgs(task, opt.block); // FIXME\n          // if intending to declare `performRawSeries` in handlers, only\n          // stream-independent (specifically, data item independent) operations can be\n          // performed. Because if a series is filtered, most of the tasks will not\n          // be performed. A stream-dependent operation probably cause wrong biz logic.\n          // Perhaps we should not provide a separate callback for this case instead\n          // of providing the config `performRawSeries`. The stream-dependent operations\n          // and stream-independent operations should better not be mixed.\n\n          performArgs.skip = !stageHandler.performRawSeries && ecModel.isSeriesFiltered(task.context.model);\n          scheduler.updatePayload(task, payload);\n          if (task.perform(performArgs)) {\n            unfinished = true;\n          }\n        });\n      }\n    });\n    function needSetDirty(opt, task) {\n      return opt.setDirty && (!opt.dirtyMap || opt.dirtyMap.get(task.__pipeline.id));\n    }\n    this.unfinished = unfinished || this.unfinished;\n  };\n  Scheduler.prototype.performSeriesTasks = function (ecModel) {\n    var unfinished;\n    ecModel.eachSeries(function (seriesModel) {\n      // Progress to the end for dataInit and dataRestore.\n      unfinished = seriesModel.dataTask.perform() || unfinished;\n    });\n    this.unfinished = unfinished || this.unfinished;\n  };\n  Scheduler.prototype.plan = function () {\n    // Travel pipelines, check block.\n    this._pipelineMap.each(function (pipeline) {\n      var task = pipeline.tail;\n      do {\n        if (task.__block) {\n          pipeline.blockIndex = task.__idxInPipeline;\n          break;\n        }\n        task = task.getUpstream();\n      } while (task);\n    });\n  };\n  Scheduler.prototype.updatePayload = function (task, payload) {\n    payload !== 'remain' && (task.context.payload = payload);\n  };\n  Scheduler.prototype._createSeriesStageTask = function (stageHandler, stageHandlerRecord, ecModel, api) {\n    var scheduler = this;\n    var oldSeriesTaskMap = stageHandlerRecord.seriesTaskMap; // The count of stages are totally about only several dozen, so\n    // do not need to reuse the map.\n\n    var newSeriesTaskMap = stageHandlerRecord.seriesTaskMap = createHashMap();\n    var seriesType = stageHandler.seriesType;\n    var getTargetSeries = stageHandler.getTargetSeries; // If a stageHandler should cover all series, `createOnAllSeries` should be declared mandatorily,\n    // to avoid some typo or abuse. Otherwise if an extension do not specify a `seriesType`,\n    // it works but it may cause other irrelevant charts blocked.\n\n    if (stageHandler.createOnAllSeries) {\n      ecModel.eachRawSeries(create);\n    } else if (seriesType) {\n      ecModel.eachRawSeriesByType(seriesType, create);\n    } else if (getTargetSeries) {\n      getTargetSeries(ecModel, api).each(create);\n    }\n    function create(seriesModel) {\n      var pipelineId = seriesModel.uid; // Init tasks for each seriesModel only once.\n      // Reuse original task instance.\n\n      var task = newSeriesTaskMap.set(pipelineId, oldSeriesTaskMap && oldSeriesTaskMap.get(pipelineId) || createTask({\n        plan: seriesTaskPlan,\n        reset: seriesTaskReset,\n        count: seriesTaskCount\n      }));\n      task.context = {\n        model: seriesModel,\n        ecModel: ecModel,\n        api: api,\n        // PENDING: `useClearVisual` not used?\n        useClearVisual: stageHandler.isVisual && !stageHandler.isLayout,\n        plan: stageHandler.plan,\n        reset: stageHandler.reset,\n        scheduler: scheduler\n      };\n      scheduler._pipe(seriesModel, task);\n    }\n  };\n  Scheduler.prototype._createOverallStageTask = function (stageHandler, stageHandlerRecord, ecModel, api) {\n    var scheduler = this;\n    var overallTask = stageHandlerRecord.overallTask = stageHandlerRecord.overallTask // For overall task, the function only be called on reset stage.\n    || createTask({\n      reset: overallTaskReset\n    });\n    overallTask.context = {\n      ecModel: ecModel,\n      api: api,\n      overallReset: stageHandler.overallReset,\n      scheduler: scheduler\n    };\n    var oldAgentStubMap = overallTask.agentStubMap; // The count of stages are totally about only several dozen, so\n    // do not need to reuse the map.\n\n    var newAgentStubMap = overallTask.agentStubMap = createHashMap();\n    var seriesType = stageHandler.seriesType;\n    var getTargetSeries = stageHandler.getTargetSeries;\n    var overallProgress = true;\n    var shouldOverallTaskDirty = false; // FIXME:TS never used, so comment it\n    // let modifyOutputEnd = stageHandler.modifyOutputEnd;\n    // An overall task with seriesType detected or has `getTargetSeries`, we add\n    // stub in each pipelines, it will set the overall task dirty when the pipeline\n    // progress. Moreover, to avoid call the overall task each frame (too frequent),\n    // we set the pipeline block.\n\n    var errMsg = '';\n    if (process.env.NODE_ENV !== 'production') {\n      errMsg = '\"createOnAllSeries\" is not supported for \"overallReset\", ' + 'because it will block all streams.';\n    }\n    assert(!stageHandler.createOnAllSeries, errMsg);\n    if (seriesType) {\n      ecModel.eachRawSeriesByType(seriesType, createStub);\n    } else if (getTargetSeries) {\n      getTargetSeries(ecModel, api).each(createStub);\n    } // Otherwise, (usually it is legacy case), the overall task will only be\n    // executed when upstream is dirty. Otherwise the progressive rendering of all\n    // pipelines will be disabled unexpectedly. But it still needs stubs to receive\n    // dirty info from upstream.\n    else {\n      overallProgress = false;\n      each(ecModel.getSeries(), createStub);\n    }\n    function createStub(seriesModel) {\n      var pipelineId = seriesModel.uid;\n      var stub = newAgentStubMap.set(pipelineId, oldAgentStubMap && oldAgentStubMap.get(pipelineId) || (\n      // When the result of `getTargetSeries` changed, the overallTask\n      // should be set as dirty and re-performed.\n      shouldOverallTaskDirty = true, createTask({\n        reset: stubReset,\n        onDirty: stubOnDirty\n      })));\n      stub.context = {\n        model: seriesModel,\n        overallProgress: overallProgress // FIXME:TS never used, so comment it\n        // modifyOutputEnd: modifyOutputEnd\n      };\n\n      stub.agent = overallTask;\n      stub.__block = overallProgress;\n      scheduler._pipe(seriesModel, stub);\n    }\n    if (shouldOverallTaskDirty) {\n      overallTask.dirty();\n    }\n  };\n  Scheduler.prototype._pipe = function (seriesModel, task) {\n    var pipelineId = seriesModel.uid;\n    var pipeline = this._pipelineMap.get(pipelineId);\n    !pipeline.head && (pipeline.head = task);\n    pipeline.tail && pipeline.tail.pipe(task);\n    pipeline.tail = task;\n    task.__idxInPipeline = pipeline.count++;\n    task.__pipeline = pipeline;\n  };\n  Scheduler.wrapStageHandler = function (stageHandler, visualType) {\n    if (isFunction(stageHandler)) {\n      stageHandler = {\n        overallReset: stageHandler,\n        seriesType: detectSeriseType(stageHandler)\n      };\n    }\n    stageHandler.uid = getUID('stageHandler');\n    visualType && (stageHandler.visualType = visualType);\n    return stageHandler;\n  };\n  ;\n  return Scheduler;\n}();\nfunction overallTaskReset(context) {\n  context.overallReset(context.ecModel, context.api, context.payload);\n}\nfunction stubReset(context) {\n  return context.overallProgress && stubProgress;\n}\nfunction stubProgress() {\n  this.agent.dirty();\n  this.getDownstream().dirty();\n}\nfunction stubOnDirty() {\n  this.agent && this.agent.dirty();\n}\nfunction seriesTaskPlan(context) {\n  return context.plan ? context.plan(context.model, context.ecModel, context.api, context.payload) : null;\n}\nfunction seriesTaskReset(context) {\n  if (context.useClearVisual) {\n    context.data.clearAllVisual();\n  }\n  var resetDefines = context.resetDefines = normalizeToArray(context.reset(context.model, context.ecModel, context.api, context.payload));\n  return resetDefines.length > 1 ? map(resetDefines, function (v, idx) {\n    return makeSeriesTaskProgress(idx);\n  }) : singleSeriesTaskProgress;\n}\nvar singleSeriesTaskProgress = makeSeriesTaskProgress(0);\nfunction makeSeriesTaskProgress(resetDefineIdx) {\n  return function (params, context) {\n    var data = context.data;\n    var resetDefine = context.resetDefines[resetDefineIdx];\n    if (resetDefine && resetDefine.dataEach) {\n      for (var i = params.start; i < params.end; i++) {\n        resetDefine.dataEach(data, i);\n      }\n    } else if (resetDefine && resetDefine.progress) {\n      resetDefine.progress(params, data);\n    }\n  };\n}\nfunction seriesTaskCount(context) {\n  return context.data.count();\n}\n/**\r\n * Only some legacy stage handlers (usually in echarts extensions) are pure function.\r\n * To ensure that they can work normally, they should work in block mode, that is,\r\n * they should not be started util the previous tasks finished. So they cause the\r\n * progressive rendering disabled. We try to detect the series type, to narrow down\r\n * the block range to only the series type they concern, but not all series.\r\n */\n\nfunction detectSeriseType(legacyFunc) {\n  seriesType = null;\n  try {\n    // Assume there is no async when calling `eachSeriesByType`.\n    legacyFunc(ecModelMock, apiMock);\n  } catch (e) {}\n  return seriesType;\n}\nvar ecModelMock = {};\nvar apiMock = {};\nvar seriesType;\nmockMethods(ecModelMock, GlobalModel);\nmockMethods(apiMock, ExtensionAPI);\necModelMock.eachSeriesByType = ecModelMock.eachRawSeriesByType = function (type) {\n  seriesType = type;\n};\necModelMock.eachComponent = function (cond) {\n  if (cond.mainType === 'series' && cond.subType) {\n    seriesType = cond.subType;\n  }\n};\nfunction mockMethods(target, Clz) {\n  /* eslint-disable */\n  for (var name_1 in Clz.prototype) {\n    // Do not use hasOwnProperty\n    target[name_1] = noop;\n  }\n  /* eslint-enable */\n}\n\nexport default Scheduler;", "map": {"version": 3, "names": ["each", "map", "isFunction", "createHashMap", "noop", "assert", "createTask", "getUID", "GlobalModel", "ExtensionAPI", "normalizeToArray", "Scheduler", "ecInstance", "api", "dataProcessorHandlers", "visualHandlers", "_stageTaskMap", "_dataProcessorHandlers", "slice", "_visualHandlers", "_allHandlers", "concat", "prototype", "restoreData", "ecModel", "payload", "taskRecord", "overallTask", "dirty", "getPerformArgs", "task", "isBlock", "__pipeline", "pipeline", "_pipelineMap", "get", "id", "pCtx", "context", "incremental", "progressiveEnabled", "progressiveRender", "__idxInPipeline", "blockIndex", "step", "modDataCount", "modBy", "Math", "ceil", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pipelineId", "updateStreamModes", "seriesModel", "view", "uid", "data", "getData", "dataLen", "count", "incrementalPrepareRender", "threshold", "large", "pipelineContext", "restorePipelines", "scheduler", "pipelineMap", "eachSeries", "progressive", "getProgressive", "set", "head", "tail", "getProgressiveThreshold", "preventIncremental", "round", "_pipe", "dataTask", "prepareStageTasks", "stageTaskMap", "getModel", "handler", "record", "errMsg", "process", "env", "NODE_ENV", "reset", "<PERSON><PERSON><PERSON><PERSON>", "_createSeriesStageTask", "_createOverallStageTask", "<PERSON><PERSON><PERSON><PERSON>", "model", "renderTask", "__block", "performDataProcessorTasks", "_performStageTasks", "block", "performVisualTasks", "opt", "stageHandlers", "unfinished", "<PERSON><PERSON><PERSON><PERSON>", "idx", "visualType", "stageHandlerRecord", "seriesTaskMap", "overallNeedDirty_1", "agentStubMap", "stub", "needSetDirty", "updatePayload", "performArgs_1", "perform", "performArgs", "skip", "performRawSeries", "isSeriesFiltered", "set<PERSON>irty", "dirtyMap", "performSeriesTasks", "plan", "getUpstream", "oldSeriesTaskMap", "newSeriesTaskMap", "seriesType", "getTargetSeries", "createOnAllSeries", "eachRawSeries", "create", "eachRawSeriesByType", "seriesTaskPlan", "seriesTaskReset", "seriesTaskCount", "useClearVisual", "isVisual", "isLayout", "overallTaskReset", "oldAgentStubMap", "newAgentStubMap", "overallProgress", "shouldOverallTaskDirty", "createStub", "getSeries", "stubReset", "onDirty", "stub<PERSON>n<PERSON>irty", "agent", "pipe", "wrapStageHandler", "detectSeriseType", "stubProgress", "getDownstream", "clearAllVisual", "resetDefines", "length", "v", "makeSeriesTaskProgress", "singleSeriesTaskProgress", "resetDefineIdx", "params", "resetDefine", "dataEach", "i", "start", "end", "progress", "legacyFunc", "ecModelMock", "apiMock", "e", "mockMethods", "eachSeriesByType", "type", "eachComponent", "cond", "mainType", "subType", "target", "Clz", "name_1"], "sources": ["C:/WolfSystemV2/WolfOldFrontEnd/node_modules/echarts/lib/core/Scheduler.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { each, map, isFunction, createHashMap, noop, assert } from 'zrender/lib/core/util.js';\nimport { createTask } from './task.js';\nimport { getUID } from '../util/component.js';\nimport GlobalModel from '../model/Global.js';\nimport ExtensionAPI from './ExtensionAPI.js';\nimport { normalizeToArray } from '../util/model.js';\n;\n\nvar Scheduler =\n/** @class */\nfunction () {\n  function Scheduler(ecInstance, api, dataProcessorHandlers, visualHandlers) {\n    // key: handlerUID\n    this._stageTaskMap = createHashMap();\n    this.ecInstance = ecInstance;\n    this.api = api; // Fix current processors in case that in some rear cases that\n    // processors might be registered after echarts instance created.\n    // Register processors incrementally for a echarts instance is\n    // not supported by this stream architecture.\n\n    dataProcessorHandlers = this._dataProcessorHandlers = dataProcessorHandlers.slice();\n    visualHandlers = this._visualHandlers = visualHandlers.slice();\n    this._allHandlers = dataProcessorHandlers.concat(visualHandlers);\n  }\n\n  Scheduler.prototype.restoreData = function (ecModel, payload) {\n    // TODO: Only restore needed series and components, but not all components.\n    // Currently `restoreData` of all of the series and component will be called.\n    // But some independent components like `title`, `legend`, `graphic`, `toolbox`,\n    // `tooltip`, `axisPointer`, etc, do not need series refresh when `setOption`,\n    // and some components like coordinate system, axes, dataZoom, visualMap only\n    // need their target series refresh.\n    // (1) If we are implementing this feature some day, we should consider these cases:\n    // if a data processor depends on a component (e.g., dataZoomProcessor depends\n    // on the settings of `dataZoom`), it should be re-performed if the component\n    // is modified by `setOption`.\n    // (2) If a processor depends on sevral series, speicified by its `getTargetSeries`,\n    // it should be re-performed when the result array of `getTargetSeries` changed.\n    // We use `dependencies` to cover these issues.\n    // (3) How to update target series when coordinate system related components modified.\n    // TODO: simply the dirty mechanism? Check whether only the case here can set tasks dirty,\n    // and this case all of the tasks will be set as dirty.\n    ecModel.restoreData(payload); // Theoretically an overall task not only depends on each of its target series, but also\n    // depends on all of the series.\n    // The overall task is not in pipeline, and `ecModel.restoreData` only set pipeline tasks\n    // dirty. If `getTargetSeries` of an overall task returns nothing, we should also ensure\n    // that the overall task is set as dirty and to be performed, otherwise it probably cause\n    // state chaos. So we have to set dirty of all of the overall tasks manually, otherwise it\n    // probably cause state chaos (consider `dataZoomProcessor`).\n\n    this._stageTaskMap.each(function (taskRecord) {\n      var overallTask = taskRecord.overallTask;\n      overallTask && overallTask.dirty();\n    });\n  }; // If seriesModel provided, incremental threshold is check by series data.\n\n\n  Scheduler.prototype.getPerformArgs = function (task, isBlock) {\n    // For overall task\n    if (!task.__pipeline) {\n      return;\n    }\n\n    var pipeline = this._pipelineMap.get(task.__pipeline.id);\n\n    var pCtx = pipeline.context;\n    var incremental = !isBlock && pipeline.progressiveEnabled && (!pCtx || pCtx.progressiveRender) && task.__idxInPipeline > pipeline.blockIndex;\n    var step = incremental ? pipeline.step : null;\n    var modDataCount = pCtx && pCtx.modDataCount;\n    var modBy = modDataCount != null ? Math.ceil(modDataCount / step) : null;\n    return {\n      step: step,\n      modBy: modBy,\n      modDataCount: modDataCount\n    };\n  };\n\n  Scheduler.prototype.getPipeline = function (pipelineId) {\n    return this._pipelineMap.get(pipelineId);\n  };\n  /**\r\n   * Current, progressive rendering starts from visual and layout.\r\n   * Always detect render mode in the same stage, avoiding that incorrect\r\n   * detection caused by data filtering.\r\n   * Caution:\r\n   * `updateStreamModes` use `seriesModel.getData()`.\r\n   */\n\n\n  Scheduler.prototype.updateStreamModes = function (seriesModel, view) {\n    var pipeline = this._pipelineMap.get(seriesModel.uid);\n\n    var data = seriesModel.getData();\n    var dataLen = data.count(); // `progressiveRender` means that can render progressively in each\n    // animation frame. Note that some types of series do not provide\n    // `view.incrementalPrepareRender` but support `chart.appendData`. We\n    // use the term `incremental` but not `progressive` to describe the\n    // case that `chart.appendData`.\n\n    var progressiveRender = pipeline.progressiveEnabled && view.incrementalPrepareRender && dataLen >= pipeline.threshold;\n    var large = seriesModel.get('large') && dataLen >= seriesModel.get('largeThreshold'); // TODO: modDataCount should not updated if `appendData`, otherwise cause whole repaint.\n    // see `test/candlestick-large3.html`\n\n    var modDataCount = seriesModel.get('progressiveChunkMode') === 'mod' ? dataLen : null;\n    seriesModel.pipelineContext = pipeline.context = {\n      progressiveRender: progressiveRender,\n      modDataCount: modDataCount,\n      large: large\n    };\n  };\n\n  Scheduler.prototype.restorePipelines = function (ecModel) {\n    var scheduler = this;\n    var pipelineMap = scheduler._pipelineMap = createHashMap();\n    ecModel.eachSeries(function (seriesModel) {\n      var progressive = seriesModel.getProgressive();\n      var pipelineId = seriesModel.uid;\n      pipelineMap.set(pipelineId, {\n        id: pipelineId,\n        head: null,\n        tail: null,\n        threshold: seriesModel.getProgressiveThreshold(),\n        progressiveEnabled: progressive && !(seriesModel.preventIncremental && seriesModel.preventIncremental()),\n        blockIndex: -1,\n        step: Math.round(progressive || 700),\n        count: 0\n      });\n\n      scheduler._pipe(seriesModel, seriesModel.dataTask);\n    });\n  };\n\n  Scheduler.prototype.prepareStageTasks = function () {\n    var stageTaskMap = this._stageTaskMap;\n    var ecModel = this.api.getModel();\n    var api = this.api;\n    each(this._allHandlers, function (handler) {\n      var record = stageTaskMap.get(handler.uid) || stageTaskMap.set(handler.uid, {});\n      var errMsg = '';\n\n      if (process.env.NODE_ENV !== 'production') {\n        // Currently do not need to support to sepecify them both.\n        errMsg = '\"reset\" and \"overallReset\" must not be both specified.';\n      }\n\n      assert(!(handler.reset && handler.overallReset), errMsg);\n      handler.reset && this._createSeriesStageTask(handler, record, ecModel, api);\n      handler.overallReset && this._createOverallStageTask(handler, record, ecModel, api);\n    }, this);\n  };\n\n  Scheduler.prototype.prepareView = function (view, model, ecModel, api) {\n    var renderTask = view.renderTask;\n    var context = renderTask.context;\n    context.model = model;\n    context.ecModel = ecModel;\n    context.api = api;\n    renderTask.__block = !view.incrementalPrepareRender;\n\n    this._pipe(model, renderTask);\n  };\n\n  Scheduler.prototype.performDataProcessorTasks = function (ecModel, payload) {\n    // If we do not use `block` here, it should be considered when to update modes.\n    this._performStageTasks(this._dataProcessorHandlers, ecModel, payload, {\n      block: true\n    });\n  };\n\n  Scheduler.prototype.performVisualTasks = function (ecModel, payload, opt) {\n    this._performStageTasks(this._visualHandlers, ecModel, payload, opt);\n  };\n\n  Scheduler.prototype._performStageTasks = function (stageHandlers, ecModel, payload, opt) {\n    opt = opt || {};\n    var unfinished = false;\n    var scheduler = this;\n    each(stageHandlers, function (stageHandler, idx) {\n      if (opt.visualType && opt.visualType !== stageHandler.visualType) {\n        return;\n      }\n\n      var stageHandlerRecord = scheduler._stageTaskMap.get(stageHandler.uid);\n\n      var seriesTaskMap = stageHandlerRecord.seriesTaskMap;\n      var overallTask = stageHandlerRecord.overallTask;\n\n      if (overallTask) {\n        var overallNeedDirty_1;\n        var agentStubMap = overallTask.agentStubMap;\n        agentStubMap.each(function (stub) {\n          if (needSetDirty(opt, stub)) {\n            stub.dirty();\n            overallNeedDirty_1 = true;\n          }\n        });\n        overallNeedDirty_1 && overallTask.dirty();\n        scheduler.updatePayload(overallTask, payload);\n        var performArgs_1 = scheduler.getPerformArgs(overallTask, opt.block); // Execute stubs firstly, which may set the overall task dirty,\n        // then execute the overall task. And stub will call seriesModel.setData,\n        // which ensures that in the overallTask seriesModel.getData() will not\n        // return incorrect data.\n\n        agentStubMap.each(function (stub) {\n          stub.perform(performArgs_1);\n        });\n\n        if (overallTask.perform(performArgs_1)) {\n          unfinished = true;\n        }\n      } else if (seriesTaskMap) {\n        seriesTaskMap.each(function (task, pipelineId) {\n          if (needSetDirty(opt, task)) {\n            task.dirty();\n          }\n\n          var performArgs = scheduler.getPerformArgs(task, opt.block); // FIXME\n          // if intending to declare `performRawSeries` in handlers, only\n          // stream-independent (specifically, data item independent) operations can be\n          // performed. Because if a series is filtered, most of the tasks will not\n          // be performed. A stream-dependent operation probably cause wrong biz logic.\n          // Perhaps we should not provide a separate callback for this case instead\n          // of providing the config `performRawSeries`. The stream-dependent operations\n          // and stream-independent operations should better not be mixed.\n\n          performArgs.skip = !stageHandler.performRawSeries && ecModel.isSeriesFiltered(task.context.model);\n          scheduler.updatePayload(task, payload);\n\n          if (task.perform(performArgs)) {\n            unfinished = true;\n          }\n        });\n      }\n    });\n\n    function needSetDirty(opt, task) {\n      return opt.setDirty && (!opt.dirtyMap || opt.dirtyMap.get(task.__pipeline.id));\n    }\n\n    this.unfinished = unfinished || this.unfinished;\n  };\n\n  Scheduler.prototype.performSeriesTasks = function (ecModel) {\n    var unfinished;\n    ecModel.eachSeries(function (seriesModel) {\n      // Progress to the end for dataInit and dataRestore.\n      unfinished = seriesModel.dataTask.perform() || unfinished;\n    });\n    this.unfinished = unfinished || this.unfinished;\n  };\n\n  Scheduler.prototype.plan = function () {\n    // Travel pipelines, check block.\n    this._pipelineMap.each(function (pipeline) {\n      var task = pipeline.tail;\n\n      do {\n        if (task.__block) {\n          pipeline.blockIndex = task.__idxInPipeline;\n          break;\n        }\n\n        task = task.getUpstream();\n      } while (task);\n    });\n  };\n\n  Scheduler.prototype.updatePayload = function (task, payload) {\n    payload !== 'remain' && (task.context.payload = payload);\n  };\n\n  Scheduler.prototype._createSeriesStageTask = function (stageHandler, stageHandlerRecord, ecModel, api) {\n    var scheduler = this;\n    var oldSeriesTaskMap = stageHandlerRecord.seriesTaskMap; // The count of stages are totally about only several dozen, so\n    // do not need to reuse the map.\n\n    var newSeriesTaskMap = stageHandlerRecord.seriesTaskMap = createHashMap();\n    var seriesType = stageHandler.seriesType;\n    var getTargetSeries = stageHandler.getTargetSeries; // If a stageHandler should cover all series, `createOnAllSeries` should be declared mandatorily,\n    // to avoid some typo or abuse. Otherwise if an extension do not specify a `seriesType`,\n    // it works but it may cause other irrelevant charts blocked.\n\n    if (stageHandler.createOnAllSeries) {\n      ecModel.eachRawSeries(create);\n    } else if (seriesType) {\n      ecModel.eachRawSeriesByType(seriesType, create);\n    } else if (getTargetSeries) {\n      getTargetSeries(ecModel, api).each(create);\n    }\n\n    function create(seriesModel) {\n      var pipelineId = seriesModel.uid; // Init tasks for each seriesModel only once.\n      // Reuse original task instance.\n\n      var task = newSeriesTaskMap.set(pipelineId, oldSeriesTaskMap && oldSeriesTaskMap.get(pipelineId) || createTask({\n        plan: seriesTaskPlan,\n        reset: seriesTaskReset,\n        count: seriesTaskCount\n      }));\n      task.context = {\n        model: seriesModel,\n        ecModel: ecModel,\n        api: api,\n        // PENDING: `useClearVisual` not used?\n        useClearVisual: stageHandler.isVisual && !stageHandler.isLayout,\n        plan: stageHandler.plan,\n        reset: stageHandler.reset,\n        scheduler: scheduler\n      };\n\n      scheduler._pipe(seriesModel, task);\n    }\n  };\n\n  Scheduler.prototype._createOverallStageTask = function (stageHandler, stageHandlerRecord, ecModel, api) {\n    var scheduler = this;\n    var overallTask = stageHandlerRecord.overallTask = stageHandlerRecord.overallTask // For overall task, the function only be called on reset stage.\n    || createTask({\n      reset: overallTaskReset\n    });\n    overallTask.context = {\n      ecModel: ecModel,\n      api: api,\n      overallReset: stageHandler.overallReset,\n      scheduler: scheduler\n    };\n    var oldAgentStubMap = overallTask.agentStubMap; // The count of stages are totally about only several dozen, so\n    // do not need to reuse the map.\n\n    var newAgentStubMap = overallTask.agentStubMap = createHashMap();\n    var seriesType = stageHandler.seriesType;\n    var getTargetSeries = stageHandler.getTargetSeries;\n    var overallProgress = true;\n    var shouldOverallTaskDirty = false; // FIXME:TS never used, so comment it\n    // let modifyOutputEnd = stageHandler.modifyOutputEnd;\n    // An overall task with seriesType detected or has `getTargetSeries`, we add\n    // stub in each pipelines, it will set the overall task dirty when the pipeline\n    // progress. Moreover, to avoid call the overall task each frame (too frequent),\n    // we set the pipeline block.\n\n    var errMsg = '';\n\n    if (process.env.NODE_ENV !== 'production') {\n      errMsg = '\"createOnAllSeries\" is not supported for \"overallReset\", ' + 'because it will block all streams.';\n    }\n\n    assert(!stageHandler.createOnAllSeries, errMsg);\n\n    if (seriesType) {\n      ecModel.eachRawSeriesByType(seriesType, createStub);\n    } else if (getTargetSeries) {\n      getTargetSeries(ecModel, api).each(createStub);\n    } // Otherwise, (usually it is legacy case), the overall task will only be\n    // executed when upstream is dirty. Otherwise the progressive rendering of all\n    // pipelines will be disabled unexpectedly. But it still needs stubs to receive\n    // dirty info from upstream.\n    else {\n        overallProgress = false;\n        each(ecModel.getSeries(), createStub);\n      }\n\n    function createStub(seriesModel) {\n      var pipelineId = seriesModel.uid;\n      var stub = newAgentStubMap.set(pipelineId, oldAgentStubMap && oldAgentStubMap.get(pipelineId) || ( // When the result of `getTargetSeries` changed, the overallTask\n      // should be set as dirty and re-performed.\n      shouldOverallTaskDirty = true, createTask({\n        reset: stubReset,\n        onDirty: stubOnDirty\n      })));\n      stub.context = {\n        model: seriesModel,\n        overallProgress: overallProgress // FIXME:TS never used, so comment it\n        // modifyOutputEnd: modifyOutputEnd\n\n      };\n      stub.agent = overallTask;\n      stub.__block = overallProgress;\n\n      scheduler._pipe(seriesModel, stub);\n    }\n\n    if (shouldOverallTaskDirty) {\n      overallTask.dirty();\n    }\n  };\n\n  Scheduler.prototype._pipe = function (seriesModel, task) {\n    var pipelineId = seriesModel.uid;\n\n    var pipeline = this._pipelineMap.get(pipelineId);\n\n    !pipeline.head && (pipeline.head = task);\n    pipeline.tail && pipeline.tail.pipe(task);\n    pipeline.tail = task;\n    task.__idxInPipeline = pipeline.count++;\n    task.__pipeline = pipeline;\n  };\n\n  Scheduler.wrapStageHandler = function (stageHandler, visualType) {\n    if (isFunction(stageHandler)) {\n      stageHandler = {\n        overallReset: stageHandler,\n        seriesType: detectSeriseType(stageHandler)\n      };\n    }\n\n    stageHandler.uid = getUID('stageHandler');\n    visualType && (stageHandler.visualType = visualType);\n    return stageHandler;\n  };\n\n  ;\n  return Scheduler;\n}();\n\nfunction overallTaskReset(context) {\n  context.overallReset(context.ecModel, context.api, context.payload);\n}\n\nfunction stubReset(context) {\n  return context.overallProgress && stubProgress;\n}\n\nfunction stubProgress() {\n  this.agent.dirty();\n  this.getDownstream().dirty();\n}\n\nfunction stubOnDirty() {\n  this.agent && this.agent.dirty();\n}\n\nfunction seriesTaskPlan(context) {\n  return context.plan ? context.plan(context.model, context.ecModel, context.api, context.payload) : null;\n}\n\nfunction seriesTaskReset(context) {\n  if (context.useClearVisual) {\n    context.data.clearAllVisual();\n  }\n\n  var resetDefines = context.resetDefines = normalizeToArray(context.reset(context.model, context.ecModel, context.api, context.payload));\n  return resetDefines.length > 1 ? map(resetDefines, function (v, idx) {\n    return makeSeriesTaskProgress(idx);\n  }) : singleSeriesTaskProgress;\n}\n\nvar singleSeriesTaskProgress = makeSeriesTaskProgress(0);\n\nfunction makeSeriesTaskProgress(resetDefineIdx) {\n  return function (params, context) {\n    var data = context.data;\n    var resetDefine = context.resetDefines[resetDefineIdx];\n\n    if (resetDefine && resetDefine.dataEach) {\n      for (var i = params.start; i < params.end; i++) {\n        resetDefine.dataEach(data, i);\n      }\n    } else if (resetDefine && resetDefine.progress) {\n      resetDefine.progress(params, data);\n    }\n  };\n}\n\nfunction seriesTaskCount(context) {\n  return context.data.count();\n}\n/**\r\n * Only some legacy stage handlers (usually in echarts extensions) are pure function.\r\n * To ensure that they can work normally, they should work in block mode, that is,\r\n * they should not be started util the previous tasks finished. So they cause the\r\n * progressive rendering disabled. We try to detect the series type, to narrow down\r\n * the block range to only the series type they concern, but not all series.\r\n */\n\n\nfunction detectSeriseType(legacyFunc) {\n  seriesType = null;\n\n  try {\n    // Assume there is no async when calling `eachSeriesByType`.\n    legacyFunc(ecModelMock, apiMock);\n  } catch (e) {}\n\n  return seriesType;\n}\n\nvar ecModelMock = {};\nvar apiMock = {};\nvar seriesType;\nmockMethods(ecModelMock, GlobalModel);\nmockMethods(apiMock, ExtensionAPI);\n\necModelMock.eachSeriesByType = ecModelMock.eachRawSeriesByType = function (type) {\n  seriesType = type;\n};\n\necModelMock.eachComponent = function (cond) {\n  if (cond.mainType === 'series' && cond.subType) {\n    seriesType = cond.subType;\n  }\n};\n\nfunction mockMethods(target, Clz) {\n  /* eslint-disable */\n  for (var name_1 in Clz.prototype) {\n    // Do not use hasOwnProperty\n    target[name_1] = noop;\n  }\n  /* eslint-enable */\n\n}\n\nexport default Scheduler;"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,IAAI,EAAEC,GAAG,EAAEC,UAAU,EAAEC,aAAa,EAAEC,IAAI,EAAEC,MAAM,QAAQ,0BAA0B;AAC7F,SAASC,UAAU,QAAQ,WAAW;AACtC,SAASC,MAAM,QAAQ,sBAAsB;AAC7C,OAAOC,WAAW,MAAM,oBAAoB;AAC5C,OAAOC,YAAY,MAAM,mBAAmB;AAC5C,SAASC,gBAAgB,QAAQ,kBAAkB;AACnD;AAEA,IAAIC,SAAS,GACb;AACA,YAAY;EACV,SAASA,SAASA,CAACC,UAAU,EAAEC,GAAG,EAAEC,qBAAqB,EAAEC,cAAc,EAAE;IACzE;IACA,IAAI,CAACC,aAAa,GAAGb,aAAa,CAAC,CAAC;IACpC,IAAI,CAACS,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAACC,GAAG,GAAGA,GAAG,CAAC,CAAC;IAChB;IACA;IACA;;IAEAC,qBAAqB,GAAG,IAAI,CAACG,sBAAsB,GAAGH,qBAAqB,CAACI,KAAK,CAAC,CAAC;IACnFH,cAAc,GAAG,IAAI,CAACI,eAAe,GAAGJ,cAAc,CAACG,KAAK,CAAC,CAAC;IAC9D,IAAI,CAACE,YAAY,GAAGN,qBAAqB,CAACO,MAAM,CAACN,cAAc,CAAC;EAClE;EAEAJ,SAAS,CAACW,SAAS,CAACC,WAAW,GAAG,UAAUC,OAAO,EAAEC,OAAO,EAAE;IAC5D;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACAD,OAAO,CAACD,WAAW,CAACE,OAAO,CAAC,CAAC,CAAC;IAC9B;IACA;IACA;IACA;IACA;IACA;;IAEA,IAAI,CAACT,aAAa,CAAChB,IAAI,CAAC,UAAU0B,UAAU,EAAE;MAC5C,IAAIC,WAAW,GAAGD,UAAU,CAACC,WAAW;MACxCA,WAAW,IAAIA,WAAW,CAACC,KAAK,CAAC,CAAC;IACpC,CAAC,CAAC;EACJ,CAAC,CAAC,CAAC;;EAGHjB,SAAS,CAACW,SAAS,CAACO,cAAc,GAAG,UAAUC,IAAI,EAAEC,OAAO,EAAE;IAC5D;IACA,IAAI,CAACD,IAAI,CAACE,UAAU,EAAE;MACpB;IACF;IAEA,IAAIC,QAAQ,GAAG,IAAI,CAACC,YAAY,CAACC,GAAG,CAACL,IAAI,CAACE,UAAU,CAACI,EAAE,CAAC;IAExD,IAAIC,IAAI,GAAGJ,QAAQ,CAACK,OAAO;IAC3B,IAAIC,WAAW,GAAG,CAACR,OAAO,IAAIE,QAAQ,CAACO,kBAAkB,KAAK,CAACH,IAAI,IAAIA,IAAI,CAACI,iBAAiB,CAAC,IAAIX,IAAI,CAACY,eAAe,GAAGT,QAAQ,CAACU,UAAU;IAC5I,IAAIC,IAAI,GAAGL,WAAW,GAAGN,QAAQ,CAACW,IAAI,GAAG,IAAI;IAC7C,IAAIC,YAAY,GAAGR,IAAI,IAAIA,IAAI,CAACQ,YAAY;IAC5C,IAAIC,KAAK,GAAGD,YAAY,IAAI,IAAI,GAAGE,IAAI,CAACC,IAAI,CAACH,YAAY,GAAGD,IAAI,CAAC,GAAG,IAAI;IACxE,OAAO;MACLA,IAAI,EAAEA,IAAI;MACVE,KAAK,EAAEA,KAAK;MACZD,YAAY,EAAEA;IAChB,CAAC;EACH,CAAC;EAEDlC,SAAS,CAACW,SAAS,CAAC2B,WAAW,GAAG,UAAUC,UAAU,EAAE;IACtD,OAAO,IAAI,CAAChB,YAAY,CAACC,GAAG,CAACe,UAAU,CAAC;EAC1C,CAAC;EACD;AACF;AACA;AACA;AACA;AACA;AACA;;EAGEvC,SAAS,CAACW,SAAS,CAAC6B,iBAAiB,GAAG,UAAUC,WAAW,EAAEC,IAAI,EAAE;IACnE,IAAIpB,QAAQ,GAAG,IAAI,CAACC,YAAY,CAACC,GAAG,CAACiB,WAAW,CAACE,GAAG,CAAC;IAErD,IAAIC,IAAI,GAAGH,WAAW,CAACI,OAAO,CAAC,CAAC;IAChC,IAAIC,OAAO,GAAGF,IAAI,CAACG,KAAK,CAAC,CAAC,CAAC,CAAC;IAC5B;IACA;IACA;IACA;;IAEA,IAAIjB,iBAAiB,GAAGR,QAAQ,CAACO,kBAAkB,IAAIa,IAAI,CAACM,wBAAwB,IAAIF,OAAO,IAAIxB,QAAQ,CAAC2B,SAAS;IACrH,IAAIC,KAAK,GAAGT,WAAW,CAACjB,GAAG,CAAC,OAAO,CAAC,IAAIsB,OAAO,IAAIL,WAAW,CAACjB,GAAG,CAAC,gBAAgB,CAAC,CAAC,CAAC;IACtF;;IAEA,IAAIU,YAAY,GAAGO,WAAW,CAACjB,GAAG,CAAC,sBAAsB,CAAC,KAAK,KAAK,GAAGsB,OAAO,GAAG,IAAI;IACrFL,WAAW,CAACU,eAAe,GAAG7B,QAAQ,CAACK,OAAO,GAAG;MAC/CG,iBAAiB,EAAEA,iBAAiB;MACpCI,YAAY,EAAEA,YAAY;MAC1BgB,KAAK,EAAEA;IACT,CAAC;EACH,CAAC;EAEDlD,SAAS,CAACW,SAAS,CAACyC,gBAAgB,GAAG,UAAUvC,OAAO,EAAE;IACxD,IAAIwC,SAAS,GAAG,IAAI;IACpB,IAAIC,WAAW,GAAGD,SAAS,CAAC9B,YAAY,GAAG/B,aAAa,CAAC,CAAC;IAC1DqB,OAAO,CAAC0C,UAAU,CAAC,UAAUd,WAAW,EAAE;MACxC,IAAIe,WAAW,GAAGf,WAAW,CAACgB,cAAc,CAAC,CAAC;MAC9C,IAAIlB,UAAU,GAAGE,WAAW,CAACE,GAAG;MAChCW,WAAW,CAACI,GAAG,CAACnB,UAAU,EAAE;QAC1Bd,EAAE,EAAEc,UAAU;QACdoB,IAAI,EAAE,IAAI;QACVC,IAAI,EAAE,IAAI;QACVX,SAAS,EAAER,WAAW,CAACoB,uBAAuB,CAAC,CAAC;QAChDhC,kBAAkB,EAAE2B,WAAW,IAAI,EAAEf,WAAW,CAACqB,kBAAkB,IAAIrB,WAAW,CAACqB,kBAAkB,CAAC,CAAC,CAAC;QACxG9B,UAAU,EAAE,CAAC,CAAC;QACdC,IAAI,EAAEG,IAAI,CAAC2B,KAAK,CAACP,WAAW,IAAI,GAAG,CAAC;QACpCT,KAAK,EAAE;MACT,CAAC,CAAC;MAEFM,SAAS,CAACW,KAAK,CAACvB,WAAW,EAAEA,WAAW,CAACwB,QAAQ,CAAC;IACpD,CAAC,CAAC;EACJ,CAAC;EAEDjE,SAAS,CAACW,SAAS,CAACuD,iBAAiB,GAAG,YAAY;IAClD,IAAIC,YAAY,GAAG,IAAI,CAAC9D,aAAa;IACrC,IAAIQ,OAAO,GAAG,IAAI,CAACX,GAAG,CAACkE,QAAQ,CAAC,CAAC;IACjC,IAAIlE,GAAG,GAAG,IAAI,CAACA,GAAG;IAClBb,IAAI,CAAC,IAAI,CAACoB,YAAY,EAAE,UAAU4D,OAAO,EAAE;MACzC,IAAIC,MAAM,GAAGH,YAAY,CAAC3C,GAAG,CAAC6C,OAAO,CAAC1B,GAAG,CAAC,IAAIwB,YAAY,CAACT,GAAG,CAACW,OAAO,CAAC1B,GAAG,EAAE,CAAC,CAAC,CAAC;MAC/E,IAAI4B,MAAM,GAAG,EAAE;MAEf,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;QACzC;QACAH,MAAM,GAAG,wDAAwD;MACnE;MAEA7E,MAAM,CAAC,EAAE2E,OAAO,CAACM,KAAK,IAAIN,OAAO,CAACO,YAAY,CAAC,EAAEL,MAAM,CAAC;MACxDF,OAAO,CAACM,KAAK,IAAI,IAAI,CAACE,sBAAsB,CAACR,OAAO,EAAEC,MAAM,EAAEzD,OAAO,EAAEX,GAAG,CAAC;MAC3EmE,OAAO,CAACO,YAAY,IAAI,IAAI,CAACE,uBAAuB,CAACT,OAAO,EAAEC,MAAM,EAAEzD,OAAO,EAAEX,GAAG,CAAC;IACrF,CAAC,EAAE,IAAI,CAAC;EACV,CAAC;EAEDF,SAAS,CAACW,SAAS,CAACoE,WAAW,GAAG,UAAUrC,IAAI,EAAEsC,KAAK,EAAEnE,OAAO,EAAEX,GAAG,EAAE;IACrE,IAAI+E,UAAU,GAAGvC,IAAI,CAACuC,UAAU;IAChC,IAAItD,OAAO,GAAGsD,UAAU,CAACtD,OAAO;IAChCA,OAAO,CAACqD,KAAK,GAAGA,KAAK;IACrBrD,OAAO,CAACd,OAAO,GAAGA,OAAO;IACzBc,OAAO,CAACzB,GAAG,GAAGA,GAAG;IACjB+E,UAAU,CAACC,OAAO,GAAG,CAACxC,IAAI,CAACM,wBAAwB;IAEnD,IAAI,CAACgB,KAAK,CAACgB,KAAK,EAAEC,UAAU,CAAC;EAC/B,CAAC;EAEDjF,SAAS,CAACW,SAAS,CAACwE,yBAAyB,GAAG,UAAUtE,OAAO,EAAEC,OAAO,EAAE;IAC1E;IACA,IAAI,CAACsE,kBAAkB,CAAC,IAAI,CAAC9E,sBAAsB,EAAEO,OAAO,EAAEC,OAAO,EAAE;MACrEuE,KAAK,EAAE;IACT,CAAC,CAAC;EACJ,CAAC;EAEDrF,SAAS,CAACW,SAAS,CAAC2E,kBAAkB,GAAG,UAAUzE,OAAO,EAAEC,OAAO,EAAEyE,GAAG,EAAE;IACxE,IAAI,CAACH,kBAAkB,CAAC,IAAI,CAAC5E,eAAe,EAAEK,OAAO,EAAEC,OAAO,EAAEyE,GAAG,CAAC;EACtE,CAAC;EAEDvF,SAAS,CAACW,SAAS,CAACyE,kBAAkB,GAAG,UAAUI,aAAa,EAAE3E,OAAO,EAAEC,OAAO,EAAEyE,GAAG,EAAE;IACvFA,GAAG,GAAGA,GAAG,IAAI,CAAC,CAAC;IACf,IAAIE,UAAU,GAAG,KAAK;IACtB,IAAIpC,SAAS,GAAG,IAAI;IACpBhE,IAAI,CAACmG,aAAa,EAAE,UAAUE,YAAY,EAAEC,GAAG,EAAE;MAC/C,IAAIJ,GAAG,CAACK,UAAU,IAAIL,GAAG,CAACK,UAAU,KAAKF,YAAY,CAACE,UAAU,EAAE;QAChE;MACF;MAEA,IAAIC,kBAAkB,GAAGxC,SAAS,CAAChD,aAAa,CAACmB,GAAG,CAACkE,YAAY,CAAC/C,GAAG,CAAC;MAEtE,IAAImD,aAAa,GAAGD,kBAAkB,CAACC,aAAa;MACpD,IAAI9E,WAAW,GAAG6E,kBAAkB,CAAC7E,WAAW;MAEhD,IAAIA,WAAW,EAAE;QACf,IAAI+E,kBAAkB;QACtB,IAAIC,YAAY,GAAGhF,WAAW,CAACgF,YAAY;QAC3CA,YAAY,CAAC3G,IAAI,CAAC,UAAU4G,IAAI,EAAE;UAChC,IAAIC,YAAY,CAACX,GAAG,EAAEU,IAAI,CAAC,EAAE;YAC3BA,IAAI,CAAChF,KAAK,CAAC,CAAC;YACZ8E,kBAAkB,GAAG,IAAI;UAC3B;QACF,CAAC,CAAC;QACFA,kBAAkB,IAAI/E,WAAW,CAACC,KAAK,CAAC,CAAC;QACzCoC,SAAS,CAAC8C,aAAa,CAACnF,WAAW,EAAEF,OAAO,CAAC;QAC7C,IAAIsF,aAAa,GAAG/C,SAAS,CAACnC,cAAc,CAACF,WAAW,EAAEuE,GAAG,CAACF,KAAK,CAAC,CAAC,CAAC;QACtE;QACA;QACA;;QAEAW,YAAY,CAAC3G,IAAI,CAAC,UAAU4G,IAAI,EAAE;UAChCA,IAAI,CAACI,OAAO,CAACD,aAAa,CAAC;QAC7B,CAAC,CAAC;QAEF,IAAIpF,WAAW,CAACqF,OAAO,CAACD,aAAa,CAAC,EAAE;UACtCX,UAAU,GAAG,IAAI;QACnB;MACF,CAAC,MAAM,IAAIK,aAAa,EAAE;QACxBA,aAAa,CAACzG,IAAI,CAAC,UAAU8B,IAAI,EAAEoB,UAAU,EAAE;UAC7C,IAAI2D,YAAY,CAACX,GAAG,EAAEpE,IAAI,CAAC,EAAE;YAC3BA,IAAI,CAACF,KAAK,CAAC,CAAC;UACd;UAEA,IAAIqF,WAAW,GAAGjD,SAAS,CAACnC,cAAc,CAACC,IAAI,EAAEoE,GAAG,CAACF,KAAK,CAAC,CAAC,CAAC;UAC7D;UACA;UACA;UACA;UACA;UACA;UACA;;UAEAiB,WAAW,CAACC,IAAI,GAAG,CAACb,YAAY,CAACc,gBAAgB,IAAI3F,OAAO,CAAC4F,gBAAgB,CAACtF,IAAI,CAACQ,OAAO,CAACqD,KAAK,CAAC;UACjG3B,SAAS,CAAC8C,aAAa,CAAChF,IAAI,EAAEL,OAAO,CAAC;UAEtC,IAAIK,IAAI,CAACkF,OAAO,CAACC,WAAW,CAAC,EAAE;YAC7Bb,UAAU,GAAG,IAAI;UACnB;QACF,CAAC,CAAC;MACJ;IACF,CAAC,CAAC;IAEF,SAASS,YAAYA,CAACX,GAAG,EAAEpE,IAAI,EAAE;MAC/B,OAAOoE,GAAG,CAACmB,QAAQ,KAAK,CAACnB,GAAG,CAACoB,QAAQ,IAAIpB,GAAG,CAACoB,QAAQ,CAACnF,GAAG,CAACL,IAAI,CAACE,UAAU,CAACI,EAAE,CAAC,CAAC;IAChF;IAEA,IAAI,CAACgE,UAAU,GAAGA,UAAU,IAAI,IAAI,CAACA,UAAU;EACjD,CAAC;EAEDzF,SAAS,CAACW,SAAS,CAACiG,kBAAkB,GAAG,UAAU/F,OAAO,EAAE;IAC1D,IAAI4E,UAAU;IACd5E,OAAO,CAAC0C,UAAU,CAAC,UAAUd,WAAW,EAAE;MACxC;MACAgD,UAAU,GAAGhD,WAAW,CAACwB,QAAQ,CAACoC,OAAO,CAAC,CAAC,IAAIZ,UAAU;IAC3D,CAAC,CAAC;IACF,IAAI,CAACA,UAAU,GAAGA,UAAU,IAAI,IAAI,CAACA,UAAU;EACjD,CAAC;EAEDzF,SAAS,CAACW,SAAS,CAACkG,IAAI,GAAG,YAAY;IACrC;IACA,IAAI,CAACtF,YAAY,CAAClC,IAAI,CAAC,UAAUiC,QAAQ,EAAE;MACzC,IAAIH,IAAI,GAAGG,QAAQ,CAACsC,IAAI;MAExB,GAAG;QACD,IAAIzC,IAAI,CAAC+D,OAAO,EAAE;UAChB5D,QAAQ,CAACU,UAAU,GAAGb,IAAI,CAACY,eAAe;UAC1C;QACF;QAEAZ,IAAI,GAAGA,IAAI,CAAC2F,WAAW,CAAC,CAAC;MAC3B,CAAC,QAAQ3F,IAAI;IACf,CAAC,CAAC;EACJ,CAAC;EAEDnB,SAAS,CAACW,SAAS,CAACwF,aAAa,GAAG,UAAUhF,IAAI,EAAEL,OAAO,EAAE;IAC3DA,OAAO,KAAK,QAAQ,KAAKK,IAAI,CAACQ,OAAO,CAACb,OAAO,GAAGA,OAAO,CAAC;EAC1D,CAAC;EAEDd,SAAS,CAACW,SAAS,CAACkE,sBAAsB,GAAG,UAAUa,YAAY,EAAEG,kBAAkB,EAAEhF,OAAO,EAAEX,GAAG,EAAE;IACrG,IAAImD,SAAS,GAAG,IAAI;IACpB,IAAI0D,gBAAgB,GAAGlB,kBAAkB,CAACC,aAAa,CAAC,CAAC;IACzD;;IAEA,IAAIkB,gBAAgB,GAAGnB,kBAAkB,CAACC,aAAa,GAAGtG,aAAa,CAAC,CAAC;IACzE,IAAIyH,UAAU,GAAGvB,YAAY,CAACuB,UAAU;IACxC,IAAIC,eAAe,GAAGxB,YAAY,CAACwB,eAAe,CAAC,CAAC;IACpD;IACA;;IAEA,IAAIxB,YAAY,CAACyB,iBAAiB,EAAE;MAClCtG,OAAO,CAACuG,aAAa,CAACC,MAAM,CAAC;IAC/B,CAAC,MAAM,IAAIJ,UAAU,EAAE;MACrBpG,OAAO,CAACyG,mBAAmB,CAACL,UAAU,EAAEI,MAAM,CAAC;IACjD,CAAC,MAAM,IAAIH,eAAe,EAAE;MAC1BA,eAAe,CAACrG,OAAO,EAAEX,GAAG,CAAC,CAACb,IAAI,CAACgI,MAAM,CAAC;IAC5C;IAEA,SAASA,MAAMA,CAAC5E,WAAW,EAAE;MAC3B,IAAIF,UAAU,GAAGE,WAAW,CAACE,GAAG,CAAC,CAAC;MAClC;;MAEA,IAAIxB,IAAI,GAAG6F,gBAAgB,CAACtD,GAAG,CAACnB,UAAU,EAAEwE,gBAAgB,IAAIA,gBAAgB,CAACvF,GAAG,CAACe,UAAU,CAAC,IAAI5C,UAAU,CAAC;QAC7GkH,IAAI,EAAEU,cAAc;QACpB5C,KAAK,EAAE6C,eAAe;QACtBzE,KAAK,EAAE0E;MACT,CAAC,CAAC,CAAC;MACHtG,IAAI,CAACQ,OAAO,GAAG;QACbqD,KAAK,EAAEvC,WAAW;QAClB5B,OAAO,EAAEA,OAAO;QAChBX,GAAG,EAAEA,GAAG;QACR;QACAwH,cAAc,EAAEhC,YAAY,CAACiC,QAAQ,IAAI,CAACjC,YAAY,CAACkC,QAAQ;QAC/Df,IAAI,EAAEnB,YAAY,CAACmB,IAAI;QACvBlC,KAAK,EAAEe,YAAY,CAACf,KAAK;QACzBtB,SAAS,EAAEA;MACb,CAAC;MAEDA,SAAS,CAACW,KAAK,CAACvB,WAAW,EAAEtB,IAAI,CAAC;IACpC;EACF,CAAC;EAEDnB,SAAS,CAACW,SAAS,CAACmE,uBAAuB,GAAG,UAAUY,YAAY,EAAEG,kBAAkB,EAAEhF,OAAO,EAAEX,GAAG,EAAE;IACtG,IAAImD,SAAS,GAAG,IAAI;IACpB,IAAIrC,WAAW,GAAG6E,kBAAkB,CAAC7E,WAAW,GAAG6E,kBAAkB,CAAC7E,WAAW,CAAC;IAAA,GAC/ErB,UAAU,CAAC;MACZgF,KAAK,EAAEkD;IACT,CAAC,CAAC;IACF7G,WAAW,CAACW,OAAO,GAAG;MACpBd,OAAO,EAAEA,OAAO;MAChBX,GAAG,EAAEA,GAAG;MACR0E,YAAY,EAAEc,YAAY,CAACd,YAAY;MACvCvB,SAAS,EAAEA;IACb,CAAC;IACD,IAAIyE,eAAe,GAAG9G,WAAW,CAACgF,YAAY,CAAC,CAAC;IAChD;;IAEA,IAAI+B,eAAe,GAAG/G,WAAW,CAACgF,YAAY,GAAGxG,aAAa,CAAC,CAAC;IAChE,IAAIyH,UAAU,GAAGvB,YAAY,CAACuB,UAAU;IACxC,IAAIC,eAAe,GAAGxB,YAAY,CAACwB,eAAe;IAClD,IAAIc,eAAe,GAAG,IAAI;IAC1B,IAAIC,sBAAsB,GAAG,KAAK,CAAC,CAAC;IACpC;IACA;IACA;IACA;IACA;;IAEA,IAAI1D,MAAM,GAAG,EAAE;IAEf,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;MACzCH,MAAM,GAAG,2DAA2D,GAAG,oCAAoC;IAC7G;IAEA7E,MAAM,CAAC,CAACgG,YAAY,CAACyB,iBAAiB,EAAE5C,MAAM,CAAC;IAE/C,IAAI0C,UAAU,EAAE;MACdpG,OAAO,CAACyG,mBAAmB,CAACL,UAAU,EAAEiB,UAAU,CAAC;IACrD,CAAC,MAAM,IAAIhB,eAAe,EAAE;MAC1BA,eAAe,CAACrG,OAAO,EAAEX,GAAG,CAAC,CAACb,IAAI,CAAC6I,UAAU,CAAC;IAChD,CAAC,CAAC;IACF;IACA;IACA;IAAA,KACK;MACDF,eAAe,GAAG,KAAK;MACvB3I,IAAI,CAACwB,OAAO,CAACsH,SAAS,CAAC,CAAC,EAAED,UAAU,CAAC;IACvC;IAEF,SAASA,UAAUA,CAACzF,WAAW,EAAE;MAC/B,IAAIF,UAAU,GAAGE,WAAW,CAACE,GAAG;MAChC,IAAIsD,IAAI,GAAG8B,eAAe,CAACrE,GAAG,CAACnB,UAAU,EAAEuF,eAAe,IAAIA,eAAe,CAACtG,GAAG,CAACe,UAAU,CAAC;MAAM;MACnG;MACA0F,sBAAsB,GAAG,IAAI,EAAEtI,UAAU,CAAC;QACxCgF,KAAK,EAAEyD,SAAS;QAChBC,OAAO,EAAEC;MACX,CAAC,CAAC,CAAC,CAAC;MACJrC,IAAI,CAACtE,OAAO,GAAG;QACbqD,KAAK,EAAEvC,WAAW;QAClBuF,eAAe,EAAEA,eAAe,CAAC;QACjC;MAEF,CAAC;;MACD/B,IAAI,CAACsC,KAAK,GAAGvH,WAAW;MACxBiF,IAAI,CAACf,OAAO,GAAG8C,eAAe;MAE9B3E,SAAS,CAACW,KAAK,CAACvB,WAAW,EAAEwD,IAAI,CAAC;IACpC;IAEA,IAAIgC,sBAAsB,EAAE;MAC1BjH,WAAW,CAACC,KAAK,CAAC,CAAC;IACrB;EACF,CAAC;EAEDjB,SAAS,CAACW,SAAS,CAACqD,KAAK,GAAG,UAAUvB,WAAW,EAAEtB,IAAI,EAAE;IACvD,IAAIoB,UAAU,GAAGE,WAAW,CAACE,GAAG;IAEhC,IAAIrB,QAAQ,GAAG,IAAI,CAACC,YAAY,CAACC,GAAG,CAACe,UAAU,CAAC;IAEhD,CAACjB,QAAQ,CAACqC,IAAI,KAAKrC,QAAQ,CAACqC,IAAI,GAAGxC,IAAI,CAAC;IACxCG,QAAQ,CAACsC,IAAI,IAAItC,QAAQ,CAACsC,IAAI,CAAC4E,IAAI,CAACrH,IAAI,CAAC;IACzCG,QAAQ,CAACsC,IAAI,GAAGzC,IAAI;IACpBA,IAAI,CAACY,eAAe,GAAGT,QAAQ,CAACyB,KAAK,EAAE;IACvC5B,IAAI,CAACE,UAAU,GAAGC,QAAQ;EAC5B,CAAC;EAEDtB,SAAS,CAACyI,gBAAgB,GAAG,UAAU/C,YAAY,EAAEE,UAAU,EAAE;IAC/D,IAAIrG,UAAU,CAACmG,YAAY,CAAC,EAAE;MAC5BA,YAAY,GAAG;QACbd,YAAY,EAAEc,YAAY;QAC1BuB,UAAU,EAAEyB,gBAAgB,CAAChD,YAAY;MAC3C,CAAC;IACH;IAEAA,YAAY,CAAC/C,GAAG,GAAG/C,MAAM,CAAC,cAAc,CAAC;IACzCgG,UAAU,KAAKF,YAAY,CAACE,UAAU,GAAGA,UAAU,CAAC;IACpD,OAAOF,YAAY;EACrB,CAAC;EAED;EACA,OAAO1F,SAAS;AAClB,CAAC,CAAC,CAAC;AAEH,SAAS6H,gBAAgBA,CAAClG,OAAO,EAAE;EACjCA,OAAO,CAACiD,YAAY,CAACjD,OAAO,CAACd,OAAO,EAAEc,OAAO,CAACzB,GAAG,EAAEyB,OAAO,CAACb,OAAO,CAAC;AACrE;AAEA,SAASsH,SAASA,CAACzG,OAAO,EAAE;EAC1B,OAAOA,OAAO,CAACqG,eAAe,IAAIW,YAAY;AAChD;AAEA,SAASA,YAAYA,CAAA,EAAG;EACtB,IAAI,CAACJ,KAAK,CAACtH,KAAK,CAAC,CAAC;EAClB,IAAI,CAAC2H,aAAa,CAAC,CAAC,CAAC3H,KAAK,CAAC,CAAC;AAC9B;AAEA,SAASqH,WAAWA,CAAA,EAAG;EACrB,IAAI,CAACC,KAAK,IAAI,IAAI,CAACA,KAAK,CAACtH,KAAK,CAAC,CAAC;AAClC;AAEA,SAASsG,cAAcA,CAAC5F,OAAO,EAAE;EAC/B,OAAOA,OAAO,CAACkF,IAAI,GAAGlF,OAAO,CAACkF,IAAI,CAAClF,OAAO,CAACqD,KAAK,EAAErD,OAAO,CAACd,OAAO,EAAEc,OAAO,CAACzB,GAAG,EAAEyB,OAAO,CAACb,OAAO,CAAC,GAAG,IAAI;AACzG;AAEA,SAAS0G,eAAeA,CAAC7F,OAAO,EAAE;EAChC,IAAIA,OAAO,CAAC+F,cAAc,EAAE;IAC1B/F,OAAO,CAACiB,IAAI,CAACiG,cAAc,CAAC,CAAC;EAC/B;EAEA,IAAIC,YAAY,GAAGnH,OAAO,CAACmH,YAAY,GAAG/I,gBAAgB,CAAC4B,OAAO,CAACgD,KAAK,CAAChD,OAAO,CAACqD,KAAK,EAAErD,OAAO,CAACd,OAAO,EAAEc,OAAO,CAACzB,GAAG,EAAEyB,OAAO,CAACb,OAAO,CAAC,CAAC;EACvI,OAAOgI,YAAY,CAACC,MAAM,GAAG,CAAC,GAAGzJ,GAAG,CAACwJ,YAAY,EAAE,UAAUE,CAAC,EAAErD,GAAG,EAAE;IACnE,OAAOsD,sBAAsB,CAACtD,GAAG,CAAC;EACpC,CAAC,CAAC,GAAGuD,wBAAwB;AAC/B;AAEA,IAAIA,wBAAwB,GAAGD,sBAAsB,CAAC,CAAC,CAAC;AAExD,SAASA,sBAAsBA,CAACE,cAAc,EAAE;EAC9C,OAAO,UAAUC,MAAM,EAAEzH,OAAO,EAAE;IAChC,IAAIiB,IAAI,GAAGjB,OAAO,CAACiB,IAAI;IACvB,IAAIyG,WAAW,GAAG1H,OAAO,CAACmH,YAAY,CAACK,cAAc,CAAC;IAEtD,IAAIE,WAAW,IAAIA,WAAW,CAACC,QAAQ,EAAE;MACvC,KAAK,IAAIC,CAAC,GAAGH,MAAM,CAACI,KAAK,EAAED,CAAC,GAAGH,MAAM,CAACK,GAAG,EAAEF,CAAC,EAAE,EAAE;QAC9CF,WAAW,CAACC,QAAQ,CAAC1G,IAAI,EAAE2G,CAAC,CAAC;MAC/B;IACF,CAAC,MAAM,IAAIF,WAAW,IAAIA,WAAW,CAACK,QAAQ,EAAE;MAC9CL,WAAW,CAACK,QAAQ,CAACN,MAAM,EAAExG,IAAI,CAAC;IACpC;EACF,CAAC;AACH;AAEA,SAAS6E,eAAeA,CAAC9F,OAAO,EAAE;EAChC,OAAOA,OAAO,CAACiB,IAAI,CAACG,KAAK,CAAC,CAAC;AAC7B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA,SAAS2F,gBAAgBA,CAACiB,UAAU,EAAE;EACpC1C,UAAU,GAAG,IAAI;EAEjB,IAAI;IACF;IACA0C,UAAU,CAACC,WAAW,EAAEC,OAAO,CAAC;EAClC,CAAC,CAAC,OAAOC,CAAC,EAAE,CAAC;EAEb,OAAO7C,UAAU;AACnB;AAEA,IAAI2C,WAAW,GAAG,CAAC,CAAC;AACpB,IAAIC,OAAO,GAAG,CAAC,CAAC;AAChB,IAAI5C,UAAU;AACd8C,WAAW,CAACH,WAAW,EAAE/J,WAAW,CAAC;AACrCkK,WAAW,CAACF,OAAO,EAAE/J,YAAY,CAAC;AAElC8J,WAAW,CAACI,gBAAgB,GAAGJ,WAAW,CAACtC,mBAAmB,GAAG,UAAU2C,IAAI,EAAE;EAC/EhD,UAAU,GAAGgD,IAAI;AACnB,CAAC;AAEDL,WAAW,CAACM,aAAa,GAAG,UAAUC,IAAI,EAAE;EAC1C,IAAIA,IAAI,CAACC,QAAQ,KAAK,QAAQ,IAAID,IAAI,CAACE,OAAO,EAAE;IAC9CpD,UAAU,GAAGkD,IAAI,CAACE,OAAO;EAC3B;AACF,CAAC;AAED,SAASN,WAAWA,CAACO,MAAM,EAAEC,GAAG,EAAE;EAChC;EACA,KAAK,IAAIC,MAAM,IAAID,GAAG,CAAC5J,SAAS,EAAE;IAChC;IACA2J,MAAM,CAACE,MAAM,CAAC,GAAG/K,IAAI;EACvB;EACA;AAEF;;AAEA,eAAeO,SAAS"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}