{"ast": null, "code": "//! moment.js locale configuration\n//! locale : Burmese [my]\n//! author : <PERSON>quar team, mysquar.com\n//! author : <PERSON> : https://github.com/gholadr\n//! author : <PERSON> : https://github.com/thanyawzinmin\n\n;\n(function (global, factory) {\n  typeof exports === 'object' && typeof module !== 'undefined' && typeof require === 'function' ? factory(require('../moment')) : typeof define === 'function' && define.amd ? define(['../moment'], factory) : factory(global.moment);\n})(this, function (moment) {\n  'use strict';\n\n  //! moment.js locale configuration\n  var symbolMap = {\n      1: '၁',\n      2: '၂',\n      3: '၃',\n      4: '၄',\n      5: '၅',\n      6: '၆',\n      7: '၇',\n      8: '၈',\n      9: '၉',\n      0: '၀'\n    },\n    numberMap = {\n      '၁': '1',\n      '၂': '2',\n      '၃': '3',\n      '၄': '4',\n      '၅': '5',\n      '၆': '6',\n      '၇': '7',\n      '၈': '8',\n      '၉': '9',\n      '၀': '0'\n    };\n  var my = moment.defineLocale('my', {\n    months: 'ဇန်နဝါရီ_ဖေဖော်ဝါရီ_မတ်_ဧပြီ_မေ_ဇွန်_ဇူလိုင်_သြဂုတ်_စက်တင်ဘာ_အောက်တိုဘာ_နိုဝင်ဘာ_ဒီဇင်ဘာ'.split('_'),\n    monthsShort: 'ဇန်_ဖေ_မတ်_ပြီ_မေ_ဇွန်_လိုင်_သြ_စက်_အောက်_နို_ဒီ'.split('_'),\n    weekdays: 'တနင်္ဂနွေ_တနင်္လာ_အင်္ဂါ_ဗုဒ္ဓဟူး_ကြာသပတေး_သောကြာ_စနေ'.split('_'),\n    weekdaysShort: 'နွေ_လာ_ဂါ_ဟူး_ကြာ_သော_နေ'.split('_'),\n    weekdaysMin: 'နွေ_လာ_ဂါ_ဟူး_ကြာ_သော_နေ'.split('_'),\n    longDateFormat: {\n      LT: 'HH:mm',\n      LTS: 'HH:mm:ss',\n      L: 'DD/MM/YYYY',\n      LL: 'D MMMM YYYY',\n      LLL: 'D MMMM YYYY HH:mm',\n      LLLL: 'dddd D MMMM YYYY HH:mm'\n    },\n    calendar: {\n      sameDay: '[ယနေ.] LT [မှာ]',\n      nextDay: '[မနက်ဖြန်] LT [မှာ]',\n      nextWeek: 'dddd LT [မှာ]',\n      lastDay: '[မနေ.က] LT [မှာ]',\n      lastWeek: '[ပြီးခဲ့သော] dddd LT [မှာ]',\n      sameElse: 'L'\n    },\n    relativeTime: {\n      future: 'လာမည့် %s မှာ',\n      past: 'လွန်ခဲ့သော %s က',\n      s: 'စက္ကန်.အနည်းငယ်',\n      ss: '%d စက္ကန့်',\n      m: 'တစ်မိနစ်',\n      mm: '%d မိနစ်',\n      h: 'တစ်နာရီ',\n      hh: '%d နာရီ',\n      d: 'တစ်ရက်',\n      dd: '%d ရက်',\n      M: 'တစ်လ',\n      MM: '%d လ',\n      y: 'တစ်နှစ်',\n      yy: '%d နှစ်'\n    },\n    preparse: function (string) {\n      return string.replace(/[၁၂၃၄၅၆၇၈၉၀]/g, function (match) {\n        return numberMap[match];\n      });\n    },\n    postformat: function (string) {\n      return string.replace(/\\d/g, function (match) {\n        return symbolMap[match];\n      });\n    },\n    week: {\n      dow: 1,\n      // Monday is the first day of the week.\n      doy: 4 // The week that contains Jan 4th is the first week of the year.\n    }\n  });\n\n  return my;\n});", "map": {"version": 3, "names": ["global", "factory", "exports", "module", "require", "define", "amd", "moment", "symbolMap", "numberMap", "my", "defineLocale", "months", "split", "monthsShort", "weekdays", "weekdaysShort", "weekdaysMin", "longDateFormat", "LT", "LTS", "L", "LL", "LLL", "LLLL", "calendar", "sameDay", "nextDay", "nextWeek", "lastDay", "lastWeek", "same<PERSON><PERSON><PERSON>", "relativeTime", "future", "past", "s", "ss", "m", "mm", "h", "hh", "d", "dd", "M", "MM", "y", "yy", "preparse", "string", "replace", "match", "postformat", "week", "dow", "doy"], "sources": ["C:/WolfSystemV2/WolfOldFrontEnd/node_modules/moment/locale/my.js"], "sourcesContent": ["//! moment.js locale configuration\n//! locale : Burmese [my]\n//! author : <PERSON>quar team, mysquar.com\n//! author : <PERSON> : https://github.com/gholadr\n//! author : <PERSON> : https://github.com/thanyawzinmin\n\n;(function (global, factory) {\n   typeof exports === 'object' && typeof module !== 'undefined'\n       && typeof require === 'function' ? factory(require('../moment')) :\n   typeof define === 'function' && define.amd ? define(['../moment'], factory) :\n   factory(global.moment)\n}(this, (function (moment) { 'use strict';\n\n    //! moment.js locale configuration\n\n    var symbolMap = {\n            1: '၁',\n            2: '၂',\n            3: '၃',\n            4: '၄',\n            5: '၅',\n            6: '၆',\n            7: '၇',\n            8: '၈',\n            9: '၉',\n            0: '၀',\n        },\n        numberMap = {\n            '၁': '1',\n            '၂': '2',\n            '၃': '3',\n            '၄': '4',\n            '၅': '5',\n            '၆': '6',\n            '၇': '7',\n            '၈': '8',\n            '၉': '9',\n            '၀': '0',\n        };\n\n    var my = moment.defineLocale('my', {\n        months: 'ဇန်နဝါရီ_ဖေဖော်ဝါရီ_မတ်_ဧပြီ_မေ_ဇွန်_ဇူလိုင်_သြဂုတ်_စက်တင်ဘာ_အောက်တိုဘာ_နိုဝင်ဘာ_ဒီဇင်ဘာ'.split(\n            '_'\n        ),\n        monthsShort: 'ဇန်_ဖေ_မတ်_ပြီ_မေ_ဇွန်_လိုင်_သြ_စက်_အောက်_နို_ဒီ'.split('_'),\n        weekdays: 'တနင်္ဂနွေ_တနင်္လာ_အင်္ဂါ_ဗုဒ္ဓဟူး_ကြာသပတေး_သောကြာ_စနေ'.split(\n            '_'\n        ),\n        weekdaysShort: 'နွေ_လာ_ဂါ_ဟူး_ကြာ_သော_နေ'.split('_'),\n        weekdaysMin: 'နွေ_လာ_ဂါ_ဟူး_ကြာ_သော_နေ'.split('_'),\n\n        longDateFormat: {\n            LT: 'HH:mm',\n            LTS: 'HH:mm:ss',\n            L: 'DD/MM/YYYY',\n            LL: 'D MMMM YYYY',\n            LLL: 'D MMMM YYYY HH:mm',\n            LLLL: 'dddd D MMMM YYYY HH:mm',\n        },\n        calendar: {\n            sameDay: '[ယနေ.] LT [မှာ]',\n            nextDay: '[မနက်ဖြန်] LT [မှာ]',\n            nextWeek: 'dddd LT [မှာ]',\n            lastDay: '[မနေ.က] LT [မှာ]',\n            lastWeek: '[ပြီးခဲ့သော] dddd LT [မှာ]',\n            sameElse: 'L',\n        },\n        relativeTime: {\n            future: 'လာမည့် %s မှာ',\n            past: 'လွန်ခဲ့သော %s က',\n            s: 'စက္ကန်.အနည်းငယ်',\n            ss: '%d စက္ကန့်',\n            m: 'တစ်မိနစ်',\n            mm: '%d မိနစ်',\n            h: 'တစ်နာရီ',\n            hh: '%d နာရီ',\n            d: 'တစ်ရက်',\n            dd: '%d ရက်',\n            M: 'တစ်လ',\n            MM: '%d လ',\n            y: 'တစ်နှစ်',\n            yy: '%d နှစ်',\n        },\n        preparse: function (string) {\n            return string.replace(/[၁၂၃၄၅၆၇၈၉၀]/g, function (match) {\n                return numberMap[match];\n            });\n        },\n        postformat: function (string) {\n            return string.replace(/\\d/g, function (match) {\n                return symbolMap[match];\n            });\n        },\n        week: {\n            dow: 1, // Monday is the first day of the week.\n            doy: 4, // The week that contains Jan 4th is the first week of the year.\n        },\n    });\n\n    return my;\n\n})));\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;;AAEA;AAAE,WAAUA,MAAM,EAAEC,OAAO,EAAE;EAC1B,OAAOC,OAAO,KAAK,QAAQ,IAAI,OAAOC,MAAM,KAAK,WAAW,IACrD,OAAOC,OAAO,KAAK,UAAU,GAAGH,OAAO,CAACG,OAAO,CAAC,WAAW,CAAC,CAAC,GACpE,OAAOC,MAAM,KAAK,UAAU,IAAIA,MAAM,CAACC,GAAG,GAAGD,MAAM,CAAC,CAAC,WAAW,CAAC,EAAEJ,OAAO,CAAC,GAC3EA,OAAO,CAACD,MAAM,CAACO,MAAM,CAAC;AACzB,CAAC,EAAC,IAAI,EAAG,UAAUA,MAAM,EAAE;EAAE,YAAY;;EAErC;EAEA,IAAIC,SAAS,GAAG;MACR,CAAC,EAAE,GAAG;MACN,CAAC,EAAE,GAAG;MACN,CAAC,EAAE,GAAG;MACN,CAAC,EAAE,GAAG;MACN,CAAC,EAAE,GAAG;MACN,CAAC,EAAE,GAAG;MACN,CAAC,EAAE,GAAG;MACN,CAAC,EAAE,GAAG;MACN,CAAC,EAAE,GAAG;MACN,CAAC,EAAE;IACP,CAAC;IACDC,SAAS,GAAG;MACR,GAAG,EAAE,GAAG;MACR,GAAG,EAAE,GAAG;MACR,GAAG,EAAE,GAAG;MACR,GAAG,EAAE,GAAG;MACR,GAAG,EAAE,GAAG;MACR,GAAG,EAAE,GAAG;MACR,GAAG,EAAE,GAAG;MACR,GAAG,EAAE,GAAG;MACR,GAAG,EAAE,GAAG;MACR,GAAG,EAAE;IACT,CAAC;EAEL,IAAIC,EAAE,GAAGH,MAAM,CAACI,YAAY,CAAC,IAAI,EAAE;IAC/BC,MAAM,EAAE,0FAA0F,CAACC,KAAK,CACpG,GACJ,CAAC;IACDC,WAAW,EAAE,kDAAkD,CAACD,KAAK,CAAC,GAAG,CAAC;IAC1EE,QAAQ,EAAE,uDAAuD,CAACF,KAAK,CACnE,GACJ,CAAC;IACDG,aAAa,EAAE,0BAA0B,CAACH,KAAK,CAAC,GAAG,CAAC;IACpDI,WAAW,EAAE,0BAA0B,CAACJ,KAAK,CAAC,GAAG,CAAC;IAElDK,cAAc,EAAE;MACZC,EAAE,EAAE,OAAO;MACXC,GAAG,EAAE,UAAU;MACfC,CAAC,EAAE,YAAY;MACfC,EAAE,EAAE,aAAa;MACjBC,GAAG,EAAE,mBAAmB;MACxBC,IAAI,EAAE;IACV,CAAC;IACDC,QAAQ,EAAE;MACNC,OAAO,EAAE,iBAAiB;MAC1BC,OAAO,EAAE,qBAAqB;MAC9BC,QAAQ,EAAE,eAAe;MACzBC,OAAO,EAAE,kBAAkB;MAC3BC,QAAQ,EAAE,4BAA4B;MACtCC,QAAQ,EAAE;IACd,CAAC;IACDC,YAAY,EAAE;MACVC,MAAM,EAAE,eAAe;MACvBC,IAAI,EAAE,iBAAiB;MACvBC,CAAC,EAAE,iBAAiB;MACpBC,EAAE,EAAE,YAAY;MAChBC,CAAC,EAAE,UAAU;MACbC,EAAE,EAAE,UAAU;MACdC,CAAC,EAAE,SAAS;MACZC,EAAE,EAAE,SAAS;MACbC,CAAC,EAAE,QAAQ;MACXC,EAAE,EAAE,QAAQ;MACZC,CAAC,EAAE,MAAM;MACTC,EAAE,EAAE,MAAM;MACVC,CAAC,EAAE,SAAS;MACZC,EAAE,EAAE;IACR,CAAC;IACDC,QAAQ,EAAE,SAAAA,CAAUC,MAAM,EAAE;MACxB,OAAOA,MAAM,CAACC,OAAO,CAAC,eAAe,EAAE,UAAUC,KAAK,EAAE;QACpD,OAAOzC,SAAS,CAACyC,KAAK,CAAC;MAC3B,CAAC,CAAC;IACN,CAAC;IACDC,UAAU,EAAE,SAAAA,CAAUH,MAAM,EAAE;MAC1B,OAAOA,MAAM,CAACC,OAAO,CAAC,KAAK,EAAE,UAAUC,KAAK,EAAE;QAC1C,OAAO1C,SAAS,CAAC0C,KAAK,CAAC;MAC3B,CAAC,CAAC;IACN,CAAC;IACDE,IAAI,EAAE;MACFC,GAAG,EAAE,CAAC;MAAE;MACRC,GAAG,EAAE,CAAC,CAAE;IACZ;EACJ,CAAC,CAAC;;EAEF,OAAO5C,EAAE;AAEb,CAAE,CAAC"}, "metadata": {}, "sourceType": "script", "externalDependencies": []}