{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nfunction getCenterCoord(view, point) {\n  // Use projected coord as center because it's linear.\n  return view.pointToProjected ? view.pointToProjected(point) : view.pointToData(point);\n}\nexport function updateCenterAndZoom(view, payload, zoomLimit, api) {\n  var previousZoom = view.getZoom();\n  var center = view.getCenter();\n  var zoom = payload.zoom;\n  var point = view.projectedToPoint ? view.projectedToPoint(center) : view.dataToPoint(center);\n  if (payload.dx != null && payload.dy != null) {\n    point[0] -= payload.dx;\n    point[1] -= payload.dy;\n    view.setCenter(getCenterCoord(view, point), api);\n  }\n  if (zoom != null) {\n    if (zoomLimit) {\n      var zoomMin = zoomLimit.min || 0;\n      var zoomMax = zoomLimit.max || Infinity;\n      zoom = Math.max(Math.min(previousZoom * zoom, zoomMax), zoomMin) / previousZoom;\n    } // Zoom on given point(originX, originY)\n\n    view.scaleX *= zoom;\n    view.scaleY *= zoom;\n    var fixX = (payload.originX - view.x) * (zoom - 1);\n    var fixY = (payload.originY - view.y) * (zoom - 1);\n    view.x -= fixX;\n    view.y -= fixY;\n    view.updateTransform(); // Get the new center\n\n    view.setCenter(getCenterCoord(view, point), api);\n    view.setZoom(zoom * previousZoom);\n  }\n  return {\n    center: view.getCenter(),\n    zoom: view.getZoom()\n  };\n}", "map": {"version": 3, "names": ["getCenterCoord", "view", "point", "pointToProjected", "pointToData", "updateCenterAndZoom", "payload", "zoomLimit", "api", "previousZoom", "getZoom", "center", "getCenter", "zoom", "projectedToPoint", "dataToPoint", "dx", "dy", "setCenter", "zoomMin", "min", "zoomMax", "max", "Infinity", "Math", "scaleX", "scaleY", "fixX", "originX", "x", "fixY", "originY", "y", "updateTransform", "setZoom"], "sources": ["C:/WolfSystemV2/WolfOldFrontEnd/node_modules/echarts/lib/action/roamHelper.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nfunction getCenterCoord(view, point) {\n  // Use projected coord as center because it's linear.\n  return view.pointToProjected ? view.pointToProjected(point) : view.pointToData(point);\n}\n\nexport function updateCenterAndZoom(view, payload, zoomLimit, api) {\n  var previousZoom = view.getZoom();\n  var center = view.getCenter();\n  var zoom = payload.zoom;\n  var point = view.projectedToPoint ? view.projectedToPoint(center) : view.dataToPoint(center);\n\n  if (payload.dx != null && payload.dy != null) {\n    point[0] -= payload.dx;\n    point[1] -= payload.dy;\n    view.setCenter(getCenterCoord(view, point), api);\n  }\n\n  if (zoom != null) {\n    if (zoomLimit) {\n      var zoomMin = zoomLimit.min || 0;\n      var zoomMax = zoomLimit.max || Infinity;\n      zoom = Math.max(Math.min(previousZoom * zoom, zoomMax), zoomMin) / previousZoom;\n    } // Zoom on given point(originX, originY)\n\n\n    view.scaleX *= zoom;\n    view.scaleY *= zoom;\n    var fixX = (payload.originX - view.x) * (zoom - 1);\n    var fixY = (payload.originY - view.y) * (zoom - 1);\n    view.x -= fixX;\n    view.y -= fixY;\n    view.updateTransform(); // Get the new center\n\n    view.setCenter(getCenterCoord(view, point), api);\n    view.setZoom(zoom * previousZoom);\n  }\n\n  return {\n    center: view.getCenter(),\n    zoom: view.getZoom()\n  };\n}"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,cAAcA,CAACC,IAAI,EAAEC,KAAK,EAAE;EACnC;EACA,OAAOD,IAAI,CAACE,gBAAgB,GAAGF,IAAI,CAACE,gBAAgB,CAACD,KAAK,CAAC,GAAGD,IAAI,CAACG,WAAW,CAACF,KAAK,CAAC;AACvF;AAEA,OAAO,SAASG,mBAAmBA,CAACJ,IAAI,EAAEK,OAAO,EAAEC,SAAS,EAAEC,GAAG,EAAE;EACjE,IAAIC,YAAY,GAAGR,IAAI,CAACS,OAAO,CAAC,CAAC;EACjC,IAAIC,MAAM,GAAGV,IAAI,CAACW,SAAS,CAAC,CAAC;EAC7B,IAAIC,IAAI,GAAGP,OAAO,CAACO,IAAI;EACvB,IAAIX,KAAK,GAAGD,IAAI,CAACa,gBAAgB,GAAGb,IAAI,CAACa,gBAAgB,CAACH,MAAM,CAAC,GAAGV,IAAI,CAACc,WAAW,CAACJ,MAAM,CAAC;EAE5F,IAAIL,OAAO,CAACU,EAAE,IAAI,IAAI,IAAIV,OAAO,CAACW,EAAE,IAAI,IAAI,EAAE;IAC5Cf,KAAK,CAAC,CAAC,CAAC,IAAII,OAAO,CAACU,EAAE;IACtBd,KAAK,CAAC,CAAC,CAAC,IAAII,OAAO,CAACW,EAAE;IACtBhB,IAAI,CAACiB,SAAS,CAAClB,cAAc,CAACC,IAAI,EAAEC,KAAK,CAAC,EAAEM,GAAG,CAAC;EAClD;EAEA,IAAIK,IAAI,IAAI,IAAI,EAAE;IAChB,IAAIN,SAAS,EAAE;MACb,IAAIY,OAAO,GAAGZ,SAAS,CAACa,GAAG,IAAI,CAAC;MAChC,IAAIC,OAAO,GAAGd,SAAS,CAACe,GAAG,IAAIC,QAAQ;MACvCV,IAAI,GAAGW,IAAI,CAACF,GAAG,CAACE,IAAI,CAACJ,GAAG,CAACX,YAAY,GAAGI,IAAI,EAAEQ,OAAO,CAAC,EAAEF,OAAO,CAAC,GAAGV,YAAY;IACjF,CAAC,CAAC;;IAGFR,IAAI,CAACwB,MAAM,IAAIZ,IAAI;IACnBZ,IAAI,CAACyB,MAAM,IAAIb,IAAI;IACnB,IAAIc,IAAI,GAAG,CAACrB,OAAO,CAACsB,OAAO,GAAG3B,IAAI,CAAC4B,CAAC,KAAKhB,IAAI,GAAG,CAAC,CAAC;IAClD,IAAIiB,IAAI,GAAG,CAACxB,OAAO,CAACyB,OAAO,GAAG9B,IAAI,CAAC+B,CAAC,KAAKnB,IAAI,GAAG,CAAC,CAAC;IAClDZ,IAAI,CAAC4B,CAAC,IAAIF,IAAI;IACd1B,IAAI,CAAC+B,CAAC,IAAIF,IAAI;IACd7B,IAAI,CAACgC,eAAe,CAAC,CAAC,CAAC,CAAC;;IAExBhC,IAAI,CAACiB,SAAS,CAAClB,cAAc,CAACC,IAAI,EAAEC,KAAK,CAAC,EAAEM,GAAG,CAAC;IAChDP,IAAI,CAACiC,OAAO,CAACrB,IAAI,GAAGJ,YAAY,CAAC;EACnC;EAEA,OAAO;IACLE,MAAM,EAAEV,IAAI,CAACW,SAAS,CAAC,CAAC;IACxBC,IAAI,EAAEZ,IAAI,CAACS,OAAO,CAAC;EACrB,CAAC;AACH"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}