{"ast": null, "code": "import { __extends } from \"tslib\";\nimport { addEventListener, removeEventListener, normalizeEvent, getNativeEvent } from '../core/event.js';\nimport * as zrUtil from '../core/util.js';\nimport Eventful from '../core/Eventful.js';\nimport env from '../core/env.js';\nvar TOUCH_CLICK_DELAY = 300;\nvar globalEventSupported = env.domSupported;\nvar localNativeListenerNames = function () {\n  var mouseHandlerNames = ['click', 'dblclick', 'mousewheel', 'wheel', 'mouseout', 'mouseup', 'mousedown', 'mousemove', 'contextmenu'];\n  var touchHandlerNames = ['touchstart', 'touchend', 'touchmove'];\n  var pointerEventNameMap = {\n    pointerdown: 1,\n    pointerup: 1,\n    pointermove: 1,\n    pointerout: 1\n  };\n  var pointerHandlerNames = zrUtil.map(mouseHandlerNames, function (name) {\n    var nm = name.replace('mouse', 'pointer');\n    return pointerEventNameMap.hasOwnProperty(nm) ? nm : name;\n  });\n  return {\n    mouse: mouseHandlerNames,\n    touch: touchHandlerNames,\n    pointer: pointerHandlerNames\n  };\n}();\nvar globalNativeListenerNames = {\n  mouse: ['mousemove', 'mouseup'],\n  pointer: ['pointermove', 'pointerup']\n};\nvar wheelEventSupported = false;\nfunction isPointerFromTouch(event) {\n  var pointerType = event.pointerType;\n  return pointerType === 'pen' || pointerType === 'touch';\n}\nfunction setTouchTimer(scope) {\n  scope.touching = true;\n  if (scope.touchTimer != null) {\n    clearTimeout(scope.touchTimer);\n    scope.touchTimer = null;\n  }\n  scope.touchTimer = setTimeout(function () {\n    scope.touching = false;\n    scope.touchTimer = null;\n  }, 700);\n}\nfunction markTouch(event) {\n  event && (event.zrByTouch = true);\n}\nfunction normalizeGlobalEvent(instance, event) {\n  return normalizeEvent(instance.dom, new FakeGlobalEvent(instance, event), true);\n}\nfunction isLocalEl(instance, el) {\n  var elTmp = el;\n  var isLocal = false;\n  while (elTmp && elTmp.nodeType !== 9 && !(isLocal = elTmp.domBelongToZr || elTmp !== el && elTmp === instance.painterRoot)) {\n    elTmp = elTmp.parentNode;\n  }\n  return isLocal;\n}\nvar FakeGlobalEvent = function () {\n  function FakeGlobalEvent(instance, event) {\n    this.stopPropagation = zrUtil.noop;\n    this.stopImmediatePropagation = zrUtil.noop;\n    this.preventDefault = zrUtil.noop;\n    this.type = event.type;\n    this.target = this.currentTarget = instance.dom;\n    this.pointerType = event.pointerType;\n    this.clientX = event.clientX;\n    this.clientY = event.clientY;\n  }\n  return FakeGlobalEvent;\n}();\nvar localDOMHandlers = {\n  mousedown: function (event) {\n    event = normalizeEvent(this.dom, event);\n    this.__mayPointerCapture = [event.zrX, event.zrY];\n    this.trigger('mousedown', event);\n  },\n  mousemove: function (event) {\n    event = normalizeEvent(this.dom, event);\n    var downPoint = this.__mayPointerCapture;\n    if (downPoint && (event.zrX !== downPoint[0] || event.zrY !== downPoint[1])) {\n      this.__togglePointerCapture(true);\n    }\n    this.trigger('mousemove', event);\n  },\n  mouseup: function (event) {\n    event = normalizeEvent(this.dom, event);\n    this.__togglePointerCapture(false);\n    this.trigger('mouseup', event);\n  },\n  mouseout: function (event) {\n    event = normalizeEvent(this.dom, event);\n    var element = event.toElement || event.relatedTarget;\n    if (!isLocalEl(this, element)) {\n      if (this.__pointerCapturing) {\n        event.zrEventControl = 'no_globalout';\n      }\n      this.trigger('mouseout', event);\n    }\n  },\n  wheel: function (event) {\n    wheelEventSupported = true;\n    event = normalizeEvent(this.dom, event);\n    this.trigger('mousewheel', event);\n  },\n  mousewheel: function (event) {\n    if (wheelEventSupported) {\n      return;\n    }\n    event = normalizeEvent(this.dom, event);\n    this.trigger('mousewheel', event);\n  },\n  touchstart: function (event) {\n    event = normalizeEvent(this.dom, event);\n    markTouch(event);\n    this.__lastTouchMoment = new Date();\n    this.handler.processGesture(event, 'start');\n    localDOMHandlers.mousemove.call(this, event);\n    localDOMHandlers.mousedown.call(this, event);\n  },\n  touchmove: function (event) {\n    event = normalizeEvent(this.dom, event);\n    markTouch(event);\n    this.handler.processGesture(event, 'change');\n    localDOMHandlers.mousemove.call(this, event);\n  },\n  touchend: function (event) {\n    event = normalizeEvent(this.dom, event);\n    markTouch(event);\n    this.handler.processGesture(event, 'end');\n    localDOMHandlers.mouseup.call(this, event);\n    if (+new Date() - +this.__lastTouchMoment < TOUCH_CLICK_DELAY) {\n      localDOMHandlers.click.call(this, event);\n    }\n  },\n  pointerdown: function (event) {\n    localDOMHandlers.mousedown.call(this, event);\n  },\n  pointermove: function (event) {\n    if (!isPointerFromTouch(event)) {\n      localDOMHandlers.mousemove.call(this, event);\n    }\n  },\n  pointerup: function (event) {\n    localDOMHandlers.mouseup.call(this, event);\n  },\n  pointerout: function (event) {\n    if (!isPointerFromTouch(event)) {\n      localDOMHandlers.mouseout.call(this, event);\n    }\n  }\n};\nzrUtil.each(['click', 'dblclick', 'contextmenu'], function (name) {\n  localDOMHandlers[name] = function (event) {\n    event = normalizeEvent(this.dom, event);\n    this.trigger(name, event);\n  };\n});\nvar globalDOMHandlers = {\n  pointermove: function (event) {\n    if (!isPointerFromTouch(event)) {\n      globalDOMHandlers.mousemove.call(this, event);\n    }\n  },\n  pointerup: function (event) {\n    globalDOMHandlers.mouseup.call(this, event);\n  },\n  mousemove: function (event) {\n    this.trigger('mousemove', event);\n  },\n  mouseup: function (event) {\n    var pointerCaptureReleasing = this.__pointerCapturing;\n    this.__togglePointerCapture(false);\n    this.trigger('mouseup', event);\n    if (pointerCaptureReleasing) {\n      event.zrEventControl = 'only_globalout';\n      this.trigger('mouseout', event);\n    }\n  }\n};\nfunction mountLocalDOMEventListeners(instance, scope) {\n  var domHandlers = scope.domHandlers;\n  if (env.pointerEventsSupported) {\n    zrUtil.each(localNativeListenerNames.pointer, function (nativeEventName) {\n      mountSingleDOMEventListener(scope, nativeEventName, function (event) {\n        domHandlers[nativeEventName].call(instance, event);\n      });\n    });\n  } else {\n    if (env.touchEventsSupported) {\n      zrUtil.each(localNativeListenerNames.touch, function (nativeEventName) {\n        mountSingleDOMEventListener(scope, nativeEventName, function (event) {\n          domHandlers[nativeEventName].call(instance, event);\n          setTouchTimer(scope);\n        });\n      });\n    }\n    zrUtil.each(localNativeListenerNames.mouse, function (nativeEventName) {\n      mountSingleDOMEventListener(scope, nativeEventName, function (event) {\n        event = getNativeEvent(event);\n        if (!scope.touching) {\n          domHandlers[nativeEventName].call(instance, event);\n        }\n      });\n    });\n  }\n}\nfunction mountGlobalDOMEventListeners(instance, scope) {\n  if (env.pointerEventsSupported) {\n    zrUtil.each(globalNativeListenerNames.pointer, mount);\n  } else if (!env.touchEventsSupported) {\n    zrUtil.each(globalNativeListenerNames.mouse, mount);\n  }\n  function mount(nativeEventName) {\n    function nativeEventListener(event) {\n      event = getNativeEvent(event);\n      if (!isLocalEl(instance, event.target)) {\n        event = normalizeGlobalEvent(instance, event);\n        scope.domHandlers[nativeEventName].call(instance, event);\n      }\n    }\n    mountSingleDOMEventListener(scope, nativeEventName, nativeEventListener, {\n      capture: true\n    });\n  }\n}\nfunction mountSingleDOMEventListener(scope, nativeEventName, listener, opt) {\n  scope.mounted[nativeEventName] = listener;\n  scope.listenerOpts[nativeEventName] = opt;\n  addEventListener(scope.domTarget, nativeEventName, listener, opt);\n}\nfunction unmountDOMEventListeners(scope) {\n  var mounted = scope.mounted;\n  for (var nativeEventName in mounted) {\n    if (mounted.hasOwnProperty(nativeEventName)) {\n      removeEventListener(scope.domTarget, nativeEventName, mounted[nativeEventName], scope.listenerOpts[nativeEventName]);\n    }\n  }\n  scope.mounted = {};\n}\nvar DOMHandlerScope = function () {\n  function DOMHandlerScope(domTarget, domHandlers) {\n    this.mounted = {};\n    this.listenerOpts = {};\n    this.touching = false;\n    this.domTarget = domTarget;\n    this.domHandlers = domHandlers;\n  }\n  return DOMHandlerScope;\n}();\nvar HandlerDomProxy = function (_super) {\n  __extends(HandlerDomProxy, _super);\n  function HandlerDomProxy(dom, painterRoot) {\n    var _this = _super.call(this) || this;\n    _this.__pointerCapturing = false;\n    _this.dom = dom;\n    _this.painterRoot = painterRoot;\n    _this._localHandlerScope = new DOMHandlerScope(dom, localDOMHandlers);\n    if (globalEventSupported) {\n      _this._globalHandlerScope = new DOMHandlerScope(document, globalDOMHandlers);\n    }\n    mountLocalDOMEventListeners(_this, _this._localHandlerScope);\n    return _this;\n  }\n  HandlerDomProxy.prototype.dispose = function () {\n    unmountDOMEventListeners(this._localHandlerScope);\n    if (globalEventSupported) {\n      unmountDOMEventListeners(this._globalHandlerScope);\n    }\n  };\n  HandlerDomProxy.prototype.setCursor = function (cursorStyle) {\n    this.dom.style && (this.dom.style.cursor = cursorStyle || 'default');\n  };\n  HandlerDomProxy.prototype.__togglePointerCapture = function (isPointerCapturing) {\n    this.__mayPointerCapture = null;\n    if (globalEventSupported && +this.__pointerCapturing ^ +isPointerCapturing) {\n      this.__pointerCapturing = isPointerCapturing;\n      var globalHandlerScope = this._globalHandlerScope;\n      isPointerCapturing ? mountGlobalDOMEventListeners(this, globalHandlerScope) : unmountDOMEventListeners(globalHandlerScope);\n    }\n  };\n  return HandlerDomProxy;\n}(Eventful);\nexport default HandlerDomProxy;", "map": {"version": 3, "names": ["__extends", "addEventListener", "removeEventListener", "normalizeEvent", "getNativeEvent", "zrUtil", "Eventful", "env", "TOUCH_CLICK_DELAY", "globalEventSupported", "domSupported", "localNativeListenerNames", "mouseHandlerNames", "touchHandlerNames", "pointerEventNameMap", "pointerdown", "pointerup", "pointermove", "pointerout", "pointerHandlerNames", "map", "name", "nm", "replace", "hasOwnProperty", "mouse", "touch", "pointer", "globalNativeListenerNames", "wheelEventSupported", "isPointerFromTouch", "event", "pointerType", "setTouchTimer", "scope", "touching", "touchTimer", "clearTimeout", "setTimeout", "<PERSON><PERSON><PERSON><PERSON>", "zrByTouch", "normalizeGlobalEvent", "instance", "dom", "FakeGlobalEvent", "isLocalEl", "el", "elTmp", "isLocal", "nodeType", "domBelongToZr", "<PERSON><PERSON><PERSON>", "parentNode", "stopPropagation", "noop", "stopImmediatePropagation", "preventDefault", "type", "target", "currentTarget", "clientX", "clientY", "localDOMHandlers", "mousedown", "__mayPointerCapture", "zrX", "zrY", "trigger", "mousemove", "downPoint", "__togglePointerCapture", "mouseup", "mouseout", "element", "toElement", "relatedTarget", "__pointerCapturing", "zrEventControl", "wheel", "mousewheel", "touchstart", "__lastTouchMoment", "Date", "handler", "processGesture", "call", "touchmove", "touchend", "click", "each", "globalDOMHandlers", "pointerCaptureReleasing", "mountLocalDOMEventListeners", "domHandlers", "pointerEventsSupported", "nativeEventName", "mountSingleDOMEventListener", "touchEventsSupported", "mountGlobalDOMEventListeners", "mount", "nativeEventListener", "capture", "listener", "opt", "mounted", "listenerOpts", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "unmountDOMEventListeners", "DOMHandlerScope", "HandlerDomProxy", "_super", "_this", "_localHandlerScope", "_globalHandlerScope", "document", "prototype", "dispose", "setCursor", "cursorStyle", "style", "cursor", "isPointerCapturing", "globalHandlerScope"], "sources": ["C:/WolfSystemV2/WolfOldFrontEnd/node_modules/zrender/lib/dom/HandlerProxy.js"], "sourcesContent": ["import { __extends } from \"tslib\";\nimport { addEventListener, removeEventListener, normalizeEvent, getNativeEvent } from '../core/event.js';\nimport * as zrUtil from '../core/util.js';\nimport Eventful from '../core/Eventful.js';\nimport env from '../core/env.js';\nvar TOUCH_CLICK_DELAY = 300;\nvar globalEventSupported = env.domSupported;\nvar localNativeListenerNames = (function () {\n    var mouseHandlerNames = [\n        'click', 'dblclick', 'mousewheel', 'wheel', 'mouseout',\n        'mouseup', 'mousedown', 'mousemove', 'contextmenu'\n    ];\n    var touchHandlerNames = [\n        'touchstart', 'touchend', 'touchmove'\n    ];\n    var pointerEventNameMap = {\n        pointerdown: 1, pointerup: 1, pointermove: 1, pointerout: 1\n    };\n    var pointerHandlerNames = zrUtil.map(mouseHandlerNames, function (name) {\n        var nm = name.replace('mouse', 'pointer');\n        return pointerEventNameMap.hasOwnProperty(nm) ? nm : name;\n    });\n    return {\n        mouse: mouseHandlerNames,\n        touch: touchHandlerNames,\n        pointer: pointerHandlerNames\n    };\n})();\nvar globalNativeListenerNames = {\n    mouse: ['mousemove', 'mouseup'],\n    pointer: ['pointermove', 'pointerup']\n};\nvar wheelEventSupported = false;\nfunction isPointerFromTouch(event) {\n    var pointerType = event.pointerType;\n    return pointerType === 'pen' || pointerType === 'touch';\n}\nfunction setTouchTimer(scope) {\n    scope.touching = true;\n    if (scope.touchTimer != null) {\n        clearTimeout(scope.touchTimer);\n        scope.touchTimer = null;\n    }\n    scope.touchTimer = setTimeout(function () {\n        scope.touching = false;\n        scope.touchTimer = null;\n    }, 700);\n}\nfunction markTouch(event) {\n    event && (event.zrByTouch = true);\n}\nfunction normalizeGlobalEvent(instance, event) {\n    return normalizeEvent(instance.dom, new FakeGlobalEvent(instance, event), true);\n}\nfunction isLocalEl(instance, el) {\n    var elTmp = el;\n    var isLocal = false;\n    while (elTmp && elTmp.nodeType !== 9\n        && !(isLocal = elTmp.domBelongToZr\n            || (elTmp !== el && elTmp === instance.painterRoot))) {\n        elTmp = elTmp.parentNode;\n    }\n    return isLocal;\n}\nvar FakeGlobalEvent = (function () {\n    function FakeGlobalEvent(instance, event) {\n        this.stopPropagation = zrUtil.noop;\n        this.stopImmediatePropagation = zrUtil.noop;\n        this.preventDefault = zrUtil.noop;\n        this.type = event.type;\n        this.target = this.currentTarget = instance.dom;\n        this.pointerType = event.pointerType;\n        this.clientX = event.clientX;\n        this.clientY = event.clientY;\n    }\n    return FakeGlobalEvent;\n}());\nvar localDOMHandlers = {\n    mousedown: function (event) {\n        event = normalizeEvent(this.dom, event);\n        this.__mayPointerCapture = [event.zrX, event.zrY];\n        this.trigger('mousedown', event);\n    },\n    mousemove: function (event) {\n        event = normalizeEvent(this.dom, event);\n        var downPoint = this.__mayPointerCapture;\n        if (downPoint && (event.zrX !== downPoint[0] || event.zrY !== downPoint[1])) {\n            this.__togglePointerCapture(true);\n        }\n        this.trigger('mousemove', event);\n    },\n    mouseup: function (event) {\n        event = normalizeEvent(this.dom, event);\n        this.__togglePointerCapture(false);\n        this.trigger('mouseup', event);\n    },\n    mouseout: function (event) {\n        event = normalizeEvent(this.dom, event);\n        var element = event.toElement || event.relatedTarget;\n        if (!isLocalEl(this, element)) {\n            if (this.__pointerCapturing) {\n                event.zrEventControl = 'no_globalout';\n            }\n            this.trigger('mouseout', event);\n        }\n    },\n    wheel: function (event) {\n        wheelEventSupported = true;\n        event = normalizeEvent(this.dom, event);\n        this.trigger('mousewheel', event);\n    },\n    mousewheel: function (event) {\n        if (wheelEventSupported) {\n            return;\n        }\n        event = normalizeEvent(this.dom, event);\n        this.trigger('mousewheel', event);\n    },\n    touchstart: function (event) {\n        event = normalizeEvent(this.dom, event);\n        markTouch(event);\n        this.__lastTouchMoment = new Date();\n        this.handler.processGesture(event, 'start');\n        localDOMHandlers.mousemove.call(this, event);\n        localDOMHandlers.mousedown.call(this, event);\n    },\n    touchmove: function (event) {\n        event = normalizeEvent(this.dom, event);\n        markTouch(event);\n        this.handler.processGesture(event, 'change');\n        localDOMHandlers.mousemove.call(this, event);\n    },\n    touchend: function (event) {\n        event = normalizeEvent(this.dom, event);\n        markTouch(event);\n        this.handler.processGesture(event, 'end');\n        localDOMHandlers.mouseup.call(this, event);\n        if (+new Date() - (+this.__lastTouchMoment) < TOUCH_CLICK_DELAY) {\n            localDOMHandlers.click.call(this, event);\n        }\n    },\n    pointerdown: function (event) {\n        localDOMHandlers.mousedown.call(this, event);\n    },\n    pointermove: function (event) {\n        if (!isPointerFromTouch(event)) {\n            localDOMHandlers.mousemove.call(this, event);\n        }\n    },\n    pointerup: function (event) {\n        localDOMHandlers.mouseup.call(this, event);\n    },\n    pointerout: function (event) {\n        if (!isPointerFromTouch(event)) {\n            localDOMHandlers.mouseout.call(this, event);\n        }\n    }\n};\nzrUtil.each(['click', 'dblclick', 'contextmenu'], function (name) {\n    localDOMHandlers[name] = function (event) {\n        event = normalizeEvent(this.dom, event);\n        this.trigger(name, event);\n    };\n});\nvar globalDOMHandlers = {\n    pointermove: function (event) {\n        if (!isPointerFromTouch(event)) {\n            globalDOMHandlers.mousemove.call(this, event);\n        }\n    },\n    pointerup: function (event) {\n        globalDOMHandlers.mouseup.call(this, event);\n    },\n    mousemove: function (event) {\n        this.trigger('mousemove', event);\n    },\n    mouseup: function (event) {\n        var pointerCaptureReleasing = this.__pointerCapturing;\n        this.__togglePointerCapture(false);\n        this.trigger('mouseup', event);\n        if (pointerCaptureReleasing) {\n            event.zrEventControl = 'only_globalout';\n            this.trigger('mouseout', event);\n        }\n    }\n};\nfunction mountLocalDOMEventListeners(instance, scope) {\n    var domHandlers = scope.domHandlers;\n    if (env.pointerEventsSupported) {\n        zrUtil.each(localNativeListenerNames.pointer, function (nativeEventName) {\n            mountSingleDOMEventListener(scope, nativeEventName, function (event) {\n                domHandlers[nativeEventName].call(instance, event);\n            });\n        });\n    }\n    else {\n        if (env.touchEventsSupported) {\n            zrUtil.each(localNativeListenerNames.touch, function (nativeEventName) {\n                mountSingleDOMEventListener(scope, nativeEventName, function (event) {\n                    domHandlers[nativeEventName].call(instance, event);\n                    setTouchTimer(scope);\n                });\n            });\n        }\n        zrUtil.each(localNativeListenerNames.mouse, function (nativeEventName) {\n            mountSingleDOMEventListener(scope, nativeEventName, function (event) {\n                event = getNativeEvent(event);\n                if (!scope.touching) {\n                    domHandlers[nativeEventName].call(instance, event);\n                }\n            });\n        });\n    }\n}\nfunction mountGlobalDOMEventListeners(instance, scope) {\n    if (env.pointerEventsSupported) {\n        zrUtil.each(globalNativeListenerNames.pointer, mount);\n    }\n    else if (!env.touchEventsSupported) {\n        zrUtil.each(globalNativeListenerNames.mouse, mount);\n    }\n    function mount(nativeEventName) {\n        function nativeEventListener(event) {\n            event = getNativeEvent(event);\n            if (!isLocalEl(instance, event.target)) {\n                event = normalizeGlobalEvent(instance, event);\n                scope.domHandlers[nativeEventName].call(instance, event);\n            }\n        }\n        mountSingleDOMEventListener(scope, nativeEventName, nativeEventListener, { capture: true });\n    }\n}\nfunction mountSingleDOMEventListener(scope, nativeEventName, listener, opt) {\n    scope.mounted[nativeEventName] = listener;\n    scope.listenerOpts[nativeEventName] = opt;\n    addEventListener(scope.domTarget, nativeEventName, listener, opt);\n}\nfunction unmountDOMEventListeners(scope) {\n    var mounted = scope.mounted;\n    for (var nativeEventName in mounted) {\n        if (mounted.hasOwnProperty(nativeEventName)) {\n            removeEventListener(scope.domTarget, nativeEventName, mounted[nativeEventName], scope.listenerOpts[nativeEventName]);\n        }\n    }\n    scope.mounted = {};\n}\nvar DOMHandlerScope = (function () {\n    function DOMHandlerScope(domTarget, domHandlers) {\n        this.mounted = {};\n        this.listenerOpts = {};\n        this.touching = false;\n        this.domTarget = domTarget;\n        this.domHandlers = domHandlers;\n    }\n    return DOMHandlerScope;\n}());\nvar HandlerDomProxy = (function (_super) {\n    __extends(HandlerDomProxy, _super);\n    function HandlerDomProxy(dom, painterRoot) {\n        var _this = _super.call(this) || this;\n        _this.__pointerCapturing = false;\n        _this.dom = dom;\n        _this.painterRoot = painterRoot;\n        _this._localHandlerScope = new DOMHandlerScope(dom, localDOMHandlers);\n        if (globalEventSupported) {\n            _this._globalHandlerScope = new DOMHandlerScope(document, globalDOMHandlers);\n        }\n        mountLocalDOMEventListeners(_this, _this._localHandlerScope);\n        return _this;\n    }\n    HandlerDomProxy.prototype.dispose = function () {\n        unmountDOMEventListeners(this._localHandlerScope);\n        if (globalEventSupported) {\n            unmountDOMEventListeners(this._globalHandlerScope);\n        }\n    };\n    HandlerDomProxy.prototype.setCursor = function (cursorStyle) {\n        this.dom.style && (this.dom.style.cursor = cursorStyle || 'default');\n    };\n    HandlerDomProxy.prototype.__togglePointerCapture = function (isPointerCapturing) {\n        this.__mayPointerCapture = null;\n        if (globalEventSupported\n            && ((+this.__pointerCapturing) ^ (+isPointerCapturing))) {\n            this.__pointerCapturing = isPointerCapturing;\n            var globalHandlerScope = this._globalHandlerScope;\n            isPointerCapturing\n                ? mountGlobalDOMEventListeners(this, globalHandlerScope)\n                : unmountDOMEventListeners(globalHandlerScope);\n        }\n    };\n    return HandlerDomProxy;\n}(Eventful));\nexport default HandlerDomProxy;\n"], "mappings": "AAAA,SAASA,SAAS,QAAQ,OAAO;AACjC,SAASC,gBAAgB,EAAEC,mBAAmB,EAAEC,cAAc,EAAEC,cAAc,QAAQ,kBAAkB;AACxG,OAAO,KAAKC,MAAM,MAAM,iBAAiB;AACzC,OAAOC,QAAQ,MAAM,qBAAqB;AAC1C,OAAOC,GAAG,MAAM,gBAAgB;AAChC,IAAIC,iBAAiB,GAAG,GAAG;AAC3B,IAAIC,oBAAoB,GAAGF,GAAG,CAACG,YAAY;AAC3C,IAAIC,wBAAwB,GAAI,YAAY;EACxC,IAAIC,iBAAiB,GAAG,CACpB,OAAO,EAAE,UAAU,EAAE,YAAY,EAAE,OAAO,EAAE,UAAU,EACtD,SAAS,EAAE,WAAW,EAAE,WAAW,EAAE,aAAa,CACrD;EACD,IAAIC,iBAAiB,GAAG,CACpB,YAAY,EAAE,UAAU,EAAE,WAAW,CACxC;EACD,IAAIC,mBAAmB,GAAG;IACtBC,WAAW,EAAE,CAAC;IAAEC,SAAS,EAAE,CAAC;IAAEC,WAAW,EAAE,CAAC;IAAEC,UAAU,EAAE;EAC9D,CAAC;EACD,IAAIC,mBAAmB,GAAGd,MAAM,CAACe,GAAG,CAACR,iBAAiB,EAAE,UAAUS,IAAI,EAAE;IACpE,IAAIC,EAAE,GAAGD,IAAI,CAACE,OAAO,CAAC,OAAO,EAAE,SAAS,CAAC;IACzC,OAAOT,mBAAmB,CAACU,cAAc,CAACF,EAAE,CAAC,GAAGA,EAAE,GAAGD,IAAI;EAC7D,CAAC,CAAC;EACF,OAAO;IACHI,KAAK,EAAEb,iBAAiB;IACxBc,KAAK,EAAEb,iBAAiB;IACxBc,OAAO,EAAER;EACb,CAAC;AACL,CAAC,CAAE,CAAC;AACJ,IAAIS,yBAAyB,GAAG;EAC5BH,KAAK,EAAE,CAAC,WAAW,EAAE,SAAS,CAAC;EAC/BE,OAAO,EAAE,CAAC,aAAa,EAAE,WAAW;AACxC,CAAC;AACD,IAAIE,mBAAmB,GAAG,KAAK;AAC/B,SAASC,kBAAkBA,CAACC,KAAK,EAAE;EAC/B,IAAIC,WAAW,GAAGD,KAAK,CAACC,WAAW;EACnC,OAAOA,WAAW,KAAK,KAAK,IAAIA,WAAW,KAAK,OAAO;AAC3D;AACA,SAASC,aAAaA,CAACC,KAAK,EAAE;EAC1BA,KAAK,CAACC,QAAQ,GAAG,IAAI;EACrB,IAAID,KAAK,CAACE,UAAU,IAAI,IAAI,EAAE;IAC1BC,YAAY,CAACH,KAAK,CAACE,UAAU,CAAC;IAC9BF,KAAK,CAACE,UAAU,GAAG,IAAI;EAC3B;EACAF,KAAK,CAACE,UAAU,GAAGE,UAAU,CAAC,YAAY;IACtCJ,KAAK,CAACC,QAAQ,GAAG,KAAK;IACtBD,KAAK,CAACE,UAAU,GAAG,IAAI;EAC3B,CAAC,EAAE,GAAG,CAAC;AACX;AACA,SAASG,SAASA,CAACR,KAAK,EAAE;EACtBA,KAAK,KAAKA,KAAK,CAACS,SAAS,GAAG,IAAI,CAAC;AACrC;AACA,SAASC,oBAAoBA,CAACC,QAAQ,EAAEX,KAAK,EAAE;EAC3C,OAAO5B,cAAc,CAACuC,QAAQ,CAACC,GAAG,EAAE,IAAIC,eAAe,CAACF,QAAQ,EAAEX,KAAK,CAAC,EAAE,IAAI,CAAC;AACnF;AACA,SAASc,SAASA,CAACH,QAAQ,EAAEI,EAAE,EAAE;EAC7B,IAAIC,KAAK,GAAGD,EAAE;EACd,IAAIE,OAAO,GAAG,KAAK;EACnB,OAAOD,KAAK,IAAIA,KAAK,CAACE,QAAQ,KAAK,CAAC,IAC7B,EAAED,OAAO,GAAGD,KAAK,CAACG,aAAa,IAC1BH,KAAK,KAAKD,EAAE,IAAIC,KAAK,KAAKL,QAAQ,CAACS,WAAY,CAAC,EAAE;IAC1DJ,KAAK,GAAGA,KAAK,CAACK,UAAU;EAC5B;EACA,OAAOJ,OAAO;AAClB;AACA,IAAIJ,eAAe,GAAI,YAAY;EAC/B,SAASA,eAAeA,CAACF,QAAQ,EAAEX,KAAK,EAAE;IACtC,IAAI,CAACsB,eAAe,GAAGhD,MAAM,CAACiD,IAAI;IAClC,IAAI,CAACC,wBAAwB,GAAGlD,MAAM,CAACiD,IAAI;IAC3C,IAAI,CAACE,cAAc,GAAGnD,MAAM,CAACiD,IAAI;IACjC,IAAI,CAACG,IAAI,GAAG1B,KAAK,CAAC0B,IAAI;IACtB,IAAI,CAACC,MAAM,GAAG,IAAI,CAACC,aAAa,GAAGjB,QAAQ,CAACC,GAAG;IAC/C,IAAI,CAACX,WAAW,GAAGD,KAAK,CAACC,WAAW;IACpC,IAAI,CAAC4B,OAAO,GAAG7B,KAAK,CAAC6B,OAAO;IAC5B,IAAI,CAACC,OAAO,GAAG9B,KAAK,CAAC8B,OAAO;EAChC;EACA,OAAOjB,eAAe;AAC1B,CAAC,CAAC,CAAE;AACJ,IAAIkB,gBAAgB,GAAG;EACnBC,SAAS,EAAE,SAAAA,CAAUhC,KAAK,EAAE;IACxBA,KAAK,GAAG5B,cAAc,CAAC,IAAI,CAACwC,GAAG,EAAEZ,KAAK,CAAC;IACvC,IAAI,CAACiC,mBAAmB,GAAG,CAACjC,KAAK,CAACkC,GAAG,EAAElC,KAAK,CAACmC,GAAG,CAAC;IACjD,IAAI,CAACC,OAAO,CAAC,WAAW,EAAEpC,KAAK,CAAC;EACpC,CAAC;EACDqC,SAAS,EAAE,SAAAA,CAAUrC,KAAK,EAAE;IACxBA,KAAK,GAAG5B,cAAc,CAAC,IAAI,CAACwC,GAAG,EAAEZ,KAAK,CAAC;IACvC,IAAIsC,SAAS,GAAG,IAAI,CAACL,mBAAmB;IACxC,IAAIK,SAAS,KAAKtC,KAAK,CAACkC,GAAG,KAAKI,SAAS,CAAC,CAAC,CAAC,IAAItC,KAAK,CAACmC,GAAG,KAAKG,SAAS,CAAC,CAAC,CAAC,CAAC,EAAE;MACzE,IAAI,CAACC,sBAAsB,CAAC,IAAI,CAAC;IACrC;IACA,IAAI,CAACH,OAAO,CAAC,WAAW,EAAEpC,KAAK,CAAC;EACpC,CAAC;EACDwC,OAAO,EAAE,SAAAA,CAAUxC,KAAK,EAAE;IACtBA,KAAK,GAAG5B,cAAc,CAAC,IAAI,CAACwC,GAAG,EAAEZ,KAAK,CAAC;IACvC,IAAI,CAACuC,sBAAsB,CAAC,KAAK,CAAC;IAClC,IAAI,CAACH,OAAO,CAAC,SAAS,EAAEpC,KAAK,CAAC;EAClC,CAAC;EACDyC,QAAQ,EAAE,SAAAA,CAAUzC,KAAK,EAAE;IACvBA,KAAK,GAAG5B,cAAc,CAAC,IAAI,CAACwC,GAAG,EAAEZ,KAAK,CAAC;IACvC,IAAI0C,OAAO,GAAG1C,KAAK,CAAC2C,SAAS,IAAI3C,KAAK,CAAC4C,aAAa;IACpD,IAAI,CAAC9B,SAAS,CAAC,IAAI,EAAE4B,OAAO,CAAC,EAAE;MAC3B,IAAI,IAAI,CAACG,kBAAkB,EAAE;QACzB7C,KAAK,CAAC8C,cAAc,GAAG,cAAc;MACzC;MACA,IAAI,CAACV,OAAO,CAAC,UAAU,EAAEpC,KAAK,CAAC;IACnC;EACJ,CAAC;EACD+C,KAAK,EAAE,SAAAA,CAAU/C,KAAK,EAAE;IACpBF,mBAAmB,GAAG,IAAI;IAC1BE,KAAK,GAAG5B,cAAc,CAAC,IAAI,CAACwC,GAAG,EAAEZ,KAAK,CAAC;IACvC,IAAI,CAACoC,OAAO,CAAC,YAAY,EAAEpC,KAAK,CAAC;EACrC,CAAC;EACDgD,UAAU,EAAE,SAAAA,CAAUhD,KAAK,EAAE;IACzB,IAAIF,mBAAmB,EAAE;MACrB;IACJ;IACAE,KAAK,GAAG5B,cAAc,CAAC,IAAI,CAACwC,GAAG,EAAEZ,KAAK,CAAC;IACvC,IAAI,CAACoC,OAAO,CAAC,YAAY,EAAEpC,KAAK,CAAC;EACrC,CAAC;EACDiD,UAAU,EAAE,SAAAA,CAAUjD,KAAK,EAAE;IACzBA,KAAK,GAAG5B,cAAc,CAAC,IAAI,CAACwC,GAAG,EAAEZ,KAAK,CAAC;IACvCQ,SAAS,CAACR,KAAK,CAAC;IAChB,IAAI,CAACkD,iBAAiB,GAAG,IAAIC,IAAI,CAAC,CAAC;IACnC,IAAI,CAACC,OAAO,CAACC,cAAc,CAACrD,KAAK,EAAE,OAAO,CAAC;IAC3C+B,gBAAgB,CAACM,SAAS,CAACiB,IAAI,CAAC,IAAI,EAAEtD,KAAK,CAAC;IAC5C+B,gBAAgB,CAACC,SAAS,CAACsB,IAAI,CAAC,IAAI,EAAEtD,KAAK,CAAC;EAChD,CAAC;EACDuD,SAAS,EAAE,SAAAA,CAAUvD,KAAK,EAAE;IACxBA,KAAK,GAAG5B,cAAc,CAAC,IAAI,CAACwC,GAAG,EAAEZ,KAAK,CAAC;IACvCQ,SAAS,CAACR,KAAK,CAAC;IAChB,IAAI,CAACoD,OAAO,CAACC,cAAc,CAACrD,KAAK,EAAE,QAAQ,CAAC;IAC5C+B,gBAAgB,CAACM,SAAS,CAACiB,IAAI,CAAC,IAAI,EAAEtD,KAAK,CAAC;EAChD,CAAC;EACDwD,QAAQ,EAAE,SAAAA,CAAUxD,KAAK,EAAE;IACvBA,KAAK,GAAG5B,cAAc,CAAC,IAAI,CAACwC,GAAG,EAAEZ,KAAK,CAAC;IACvCQ,SAAS,CAACR,KAAK,CAAC;IAChB,IAAI,CAACoD,OAAO,CAACC,cAAc,CAACrD,KAAK,EAAE,KAAK,CAAC;IACzC+B,gBAAgB,CAACS,OAAO,CAACc,IAAI,CAAC,IAAI,EAAEtD,KAAK,CAAC;IAC1C,IAAI,CAAC,IAAImD,IAAI,CAAC,CAAC,GAAI,CAAC,IAAI,CAACD,iBAAkB,GAAGzE,iBAAiB,EAAE;MAC7DsD,gBAAgB,CAAC0B,KAAK,CAACH,IAAI,CAAC,IAAI,EAAEtD,KAAK,CAAC;IAC5C;EACJ,CAAC;EACDhB,WAAW,EAAE,SAAAA,CAAUgB,KAAK,EAAE;IAC1B+B,gBAAgB,CAACC,SAAS,CAACsB,IAAI,CAAC,IAAI,EAAEtD,KAAK,CAAC;EAChD,CAAC;EACDd,WAAW,EAAE,SAAAA,CAAUc,KAAK,EAAE;IAC1B,IAAI,CAACD,kBAAkB,CAACC,KAAK,CAAC,EAAE;MAC5B+B,gBAAgB,CAACM,SAAS,CAACiB,IAAI,CAAC,IAAI,EAAEtD,KAAK,CAAC;IAChD;EACJ,CAAC;EACDf,SAAS,EAAE,SAAAA,CAAUe,KAAK,EAAE;IACxB+B,gBAAgB,CAACS,OAAO,CAACc,IAAI,CAAC,IAAI,EAAEtD,KAAK,CAAC;EAC9C,CAAC;EACDb,UAAU,EAAE,SAAAA,CAAUa,KAAK,EAAE;IACzB,IAAI,CAACD,kBAAkB,CAACC,KAAK,CAAC,EAAE;MAC5B+B,gBAAgB,CAACU,QAAQ,CAACa,IAAI,CAAC,IAAI,EAAEtD,KAAK,CAAC;IAC/C;EACJ;AACJ,CAAC;AACD1B,MAAM,CAACoF,IAAI,CAAC,CAAC,OAAO,EAAE,UAAU,EAAE,aAAa,CAAC,EAAE,UAAUpE,IAAI,EAAE;EAC9DyC,gBAAgB,CAACzC,IAAI,CAAC,GAAG,UAAUU,KAAK,EAAE;IACtCA,KAAK,GAAG5B,cAAc,CAAC,IAAI,CAACwC,GAAG,EAAEZ,KAAK,CAAC;IACvC,IAAI,CAACoC,OAAO,CAAC9C,IAAI,EAAEU,KAAK,CAAC;EAC7B,CAAC;AACL,CAAC,CAAC;AACF,IAAI2D,iBAAiB,GAAG;EACpBzE,WAAW,EAAE,SAAAA,CAAUc,KAAK,EAAE;IAC1B,IAAI,CAACD,kBAAkB,CAACC,KAAK,CAAC,EAAE;MAC5B2D,iBAAiB,CAACtB,SAAS,CAACiB,IAAI,CAAC,IAAI,EAAEtD,KAAK,CAAC;IACjD;EACJ,CAAC;EACDf,SAAS,EAAE,SAAAA,CAAUe,KAAK,EAAE;IACxB2D,iBAAiB,CAACnB,OAAO,CAACc,IAAI,CAAC,IAAI,EAAEtD,KAAK,CAAC;EAC/C,CAAC;EACDqC,SAAS,EAAE,SAAAA,CAAUrC,KAAK,EAAE;IACxB,IAAI,CAACoC,OAAO,CAAC,WAAW,EAAEpC,KAAK,CAAC;EACpC,CAAC;EACDwC,OAAO,EAAE,SAAAA,CAAUxC,KAAK,EAAE;IACtB,IAAI4D,uBAAuB,GAAG,IAAI,CAACf,kBAAkB;IACrD,IAAI,CAACN,sBAAsB,CAAC,KAAK,CAAC;IAClC,IAAI,CAACH,OAAO,CAAC,SAAS,EAAEpC,KAAK,CAAC;IAC9B,IAAI4D,uBAAuB,EAAE;MACzB5D,KAAK,CAAC8C,cAAc,GAAG,gBAAgB;MACvC,IAAI,CAACV,OAAO,CAAC,UAAU,EAAEpC,KAAK,CAAC;IACnC;EACJ;AACJ,CAAC;AACD,SAAS6D,2BAA2BA,CAAClD,QAAQ,EAAER,KAAK,EAAE;EAClD,IAAI2D,WAAW,GAAG3D,KAAK,CAAC2D,WAAW;EACnC,IAAItF,GAAG,CAACuF,sBAAsB,EAAE;IAC5BzF,MAAM,CAACoF,IAAI,CAAC9E,wBAAwB,CAACgB,OAAO,EAAE,UAAUoE,eAAe,EAAE;MACrEC,2BAA2B,CAAC9D,KAAK,EAAE6D,eAAe,EAAE,UAAUhE,KAAK,EAAE;QACjE8D,WAAW,CAACE,eAAe,CAAC,CAACV,IAAI,CAAC3C,QAAQ,EAAEX,KAAK,CAAC;MACtD,CAAC,CAAC;IACN,CAAC,CAAC;EACN,CAAC,MACI;IACD,IAAIxB,GAAG,CAAC0F,oBAAoB,EAAE;MAC1B5F,MAAM,CAACoF,IAAI,CAAC9E,wBAAwB,CAACe,KAAK,EAAE,UAAUqE,eAAe,EAAE;QACnEC,2BAA2B,CAAC9D,KAAK,EAAE6D,eAAe,EAAE,UAAUhE,KAAK,EAAE;UACjE8D,WAAW,CAACE,eAAe,CAAC,CAACV,IAAI,CAAC3C,QAAQ,EAAEX,KAAK,CAAC;UAClDE,aAAa,CAACC,KAAK,CAAC;QACxB,CAAC,CAAC;MACN,CAAC,CAAC;IACN;IACA7B,MAAM,CAACoF,IAAI,CAAC9E,wBAAwB,CAACc,KAAK,EAAE,UAAUsE,eAAe,EAAE;MACnEC,2BAA2B,CAAC9D,KAAK,EAAE6D,eAAe,EAAE,UAAUhE,KAAK,EAAE;QACjEA,KAAK,GAAG3B,cAAc,CAAC2B,KAAK,CAAC;QAC7B,IAAI,CAACG,KAAK,CAACC,QAAQ,EAAE;UACjB0D,WAAW,CAACE,eAAe,CAAC,CAACV,IAAI,CAAC3C,QAAQ,EAAEX,KAAK,CAAC;QACtD;MACJ,CAAC,CAAC;IACN,CAAC,CAAC;EACN;AACJ;AACA,SAASmE,4BAA4BA,CAACxD,QAAQ,EAAER,KAAK,EAAE;EACnD,IAAI3B,GAAG,CAACuF,sBAAsB,EAAE;IAC5BzF,MAAM,CAACoF,IAAI,CAAC7D,yBAAyB,CAACD,OAAO,EAAEwE,KAAK,CAAC;EACzD,CAAC,MACI,IAAI,CAAC5F,GAAG,CAAC0F,oBAAoB,EAAE;IAChC5F,MAAM,CAACoF,IAAI,CAAC7D,yBAAyB,CAACH,KAAK,EAAE0E,KAAK,CAAC;EACvD;EACA,SAASA,KAAKA,CAACJ,eAAe,EAAE;IAC5B,SAASK,mBAAmBA,CAACrE,KAAK,EAAE;MAChCA,KAAK,GAAG3B,cAAc,CAAC2B,KAAK,CAAC;MAC7B,IAAI,CAACc,SAAS,CAACH,QAAQ,EAAEX,KAAK,CAAC2B,MAAM,CAAC,EAAE;QACpC3B,KAAK,GAAGU,oBAAoB,CAACC,QAAQ,EAAEX,KAAK,CAAC;QAC7CG,KAAK,CAAC2D,WAAW,CAACE,eAAe,CAAC,CAACV,IAAI,CAAC3C,QAAQ,EAAEX,KAAK,CAAC;MAC5D;IACJ;IACAiE,2BAA2B,CAAC9D,KAAK,EAAE6D,eAAe,EAAEK,mBAAmB,EAAE;MAAEC,OAAO,EAAE;IAAK,CAAC,CAAC;EAC/F;AACJ;AACA,SAASL,2BAA2BA,CAAC9D,KAAK,EAAE6D,eAAe,EAAEO,QAAQ,EAAEC,GAAG,EAAE;EACxErE,KAAK,CAACsE,OAAO,CAACT,eAAe,CAAC,GAAGO,QAAQ;EACzCpE,KAAK,CAACuE,YAAY,CAACV,eAAe,CAAC,GAAGQ,GAAG;EACzCtG,gBAAgB,CAACiC,KAAK,CAACwE,SAAS,EAAEX,eAAe,EAAEO,QAAQ,EAAEC,GAAG,CAAC;AACrE;AACA,SAASI,wBAAwBA,CAACzE,KAAK,EAAE;EACrC,IAAIsE,OAAO,GAAGtE,KAAK,CAACsE,OAAO;EAC3B,KAAK,IAAIT,eAAe,IAAIS,OAAO,EAAE;IACjC,IAAIA,OAAO,CAAChF,cAAc,CAACuE,eAAe,CAAC,EAAE;MACzC7F,mBAAmB,CAACgC,KAAK,CAACwE,SAAS,EAAEX,eAAe,EAAES,OAAO,CAACT,eAAe,CAAC,EAAE7D,KAAK,CAACuE,YAAY,CAACV,eAAe,CAAC,CAAC;IACxH;EACJ;EACA7D,KAAK,CAACsE,OAAO,GAAG,CAAC,CAAC;AACtB;AACA,IAAII,eAAe,GAAI,YAAY;EAC/B,SAASA,eAAeA,CAACF,SAAS,EAAEb,WAAW,EAAE;IAC7C,IAAI,CAACW,OAAO,GAAG,CAAC,CAAC;IACjB,IAAI,CAACC,YAAY,GAAG,CAAC,CAAC;IACtB,IAAI,CAACtE,QAAQ,GAAG,KAAK;IACrB,IAAI,CAACuE,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACb,WAAW,GAAGA,WAAW;EAClC;EACA,OAAOe,eAAe;AAC1B,CAAC,CAAC,CAAE;AACJ,IAAIC,eAAe,GAAI,UAAUC,MAAM,EAAE;EACrC9G,SAAS,CAAC6G,eAAe,EAAEC,MAAM,CAAC;EAClC,SAASD,eAAeA,CAAClE,GAAG,EAAEQ,WAAW,EAAE;IACvC,IAAI4D,KAAK,GAAGD,MAAM,CAACzB,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI;IACrC0B,KAAK,CAACnC,kBAAkB,GAAG,KAAK;IAChCmC,KAAK,CAACpE,GAAG,GAAGA,GAAG;IACfoE,KAAK,CAAC5D,WAAW,GAAGA,WAAW;IAC/B4D,KAAK,CAACC,kBAAkB,GAAG,IAAIJ,eAAe,CAACjE,GAAG,EAAEmB,gBAAgB,CAAC;IACrE,IAAIrD,oBAAoB,EAAE;MACtBsG,KAAK,CAACE,mBAAmB,GAAG,IAAIL,eAAe,CAACM,QAAQ,EAAExB,iBAAiB,CAAC;IAChF;IACAE,2BAA2B,CAACmB,KAAK,EAAEA,KAAK,CAACC,kBAAkB,CAAC;IAC5D,OAAOD,KAAK;EAChB;EACAF,eAAe,CAACM,SAAS,CAACC,OAAO,GAAG,YAAY;IAC5CT,wBAAwB,CAAC,IAAI,CAACK,kBAAkB,CAAC;IACjD,IAAIvG,oBAAoB,EAAE;MACtBkG,wBAAwB,CAAC,IAAI,CAACM,mBAAmB,CAAC;IACtD;EACJ,CAAC;EACDJ,eAAe,CAACM,SAAS,CAACE,SAAS,GAAG,UAAUC,WAAW,EAAE;IACzD,IAAI,CAAC3E,GAAG,CAAC4E,KAAK,KAAK,IAAI,CAAC5E,GAAG,CAAC4E,KAAK,CAACC,MAAM,GAAGF,WAAW,IAAI,SAAS,CAAC;EACxE,CAAC;EACDT,eAAe,CAACM,SAAS,CAAC7C,sBAAsB,GAAG,UAAUmD,kBAAkB,EAAE;IAC7E,IAAI,CAACzD,mBAAmB,GAAG,IAAI;IAC/B,IAAIvD,oBAAoB,IACf,CAAC,IAAI,CAACmE,kBAAkB,GAAK,CAAC6C,kBAAoB,EAAE;MACzD,IAAI,CAAC7C,kBAAkB,GAAG6C,kBAAkB;MAC5C,IAAIC,kBAAkB,GAAG,IAAI,CAACT,mBAAmB;MACjDQ,kBAAkB,GACZvB,4BAA4B,CAAC,IAAI,EAAEwB,kBAAkB,CAAC,GACtDf,wBAAwB,CAACe,kBAAkB,CAAC;IACtD;EACJ,CAAC;EACD,OAAOb,eAAe;AAC1B,CAAC,CAACvG,QAAQ,CAAE;AACZ,eAAeuG,eAAe"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}