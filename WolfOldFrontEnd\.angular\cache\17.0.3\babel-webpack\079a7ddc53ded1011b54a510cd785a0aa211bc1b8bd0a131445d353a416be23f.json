{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\"; // FIXME step not support polar\n\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport SymbolDraw from '../helper/SymbolDraw.js';\nimport SymbolClz from '../helper/Symbol.js';\nimport lineAnimationDiff from './lineAnimationDiff.js';\nimport * as graphic from '../../util/graphic.js';\nimport * as modelUtil from '../../util/model.js';\nimport { ECPolyline, ECPolygon } from './poly.js';\nimport ChartView from '../../view/Chart.js';\nimport { prepareDataCoordInfo, getStackedOnPoint } from './helper.js';\nimport { createGridClipPath, createPolarClipPath } from '../helper/createClipPathFromCoordSys.js';\nimport { isCoordinateSystemType } from '../../coord/CoordinateSystem.js';\nimport { setStatesStylesFromModel, setStatesFlag, toggleHoverEmphasis, SPECIAL_STATES } from '../../util/states.js';\nimport { setLabelStyle, getLabelStatesModels, labelInner } from '../../label/labelStyle.js';\nimport { getDefaultLabel, getDefaultInterpolatedLabel } from '../helper/labelHelper.js';\nimport { getECData } from '../../util/innerStore.js';\nimport { createFloat32Array } from '../../util/vendor.js';\nimport { convertToColorString } from '../../util/format.js';\nimport { lerp } from 'zrender/lib/tool/color.js';\nfunction isPointsSame(points1, points2) {\n  if (points1.length !== points2.length) {\n    return;\n  }\n  for (var i = 0; i < points1.length; i++) {\n    if (points1[i] !== points2[i]) {\n      return;\n    }\n  }\n  return true;\n}\nfunction bboxFromPoints(points) {\n  var minX = Infinity;\n  var minY = Infinity;\n  var maxX = -Infinity;\n  var maxY = -Infinity;\n  for (var i = 0; i < points.length;) {\n    var x = points[i++];\n    var y = points[i++];\n    if (!isNaN(x)) {\n      minX = Math.min(x, minX);\n      maxX = Math.max(x, maxX);\n    }\n    if (!isNaN(y)) {\n      minY = Math.min(y, minY);\n      maxY = Math.max(y, maxY);\n    }\n  }\n  return [[minX, minY], [maxX, maxY]];\n}\nfunction getBoundingDiff(points1, points2) {\n  var _a = bboxFromPoints(points1),\n    min1 = _a[0],\n    max1 = _a[1];\n  var _b = bboxFromPoints(points2),\n    min2 = _b[0],\n    max2 = _b[1]; // Get a max value from each corner of two boundings.\n\n  return Math.max(Math.abs(min1[0] - min2[0]), Math.abs(min1[1] - min2[1]), Math.abs(max1[0] - max2[0]), Math.abs(max1[1] - max2[1]));\n}\nfunction getSmooth(smooth) {\n  return zrUtil.isNumber(smooth) ? smooth : smooth ? 0.5 : 0;\n}\nfunction getStackedOnPoints(coordSys, data, dataCoordInfo) {\n  if (!dataCoordInfo.valueDim) {\n    return [];\n  }\n  var len = data.count();\n  var points = createFloat32Array(len * 2);\n  for (var idx = 0; idx < len; idx++) {\n    var pt = getStackedOnPoint(dataCoordInfo, coordSys, data, idx);\n    points[idx * 2] = pt[0];\n    points[idx * 2 + 1] = pt[1];\n  }\n  return points;\n}\nfunction turnPointsIntoStep(points, coordSys, stepTurnAt, connectNulls) {\n  var baseAxis = coordSys.getBaseAxis();\n  var baseIndex = baseAxis.dim === 'x' || baseAxis.dim === 'radius' ? 0 : 1;\n  var stepPoints = [];\n  var i = 0;\n  var stepPt = [];\n  var pt = [];\n  var nextPt = [];\n  var filteredPoints = [];\n  if (connectNulls) {\n    for (i = 0; i < points.length; i += 2) {\n      if (!isNaN(points[i]) && !isNaN(points[i + 1])) {\n        filteredPoints.push(points[i], points[i + 1]);\n      }\n    }\n    points = filteredPoints;\n  }\n  for (i = 0; i < points.length - 2; i += 2) {\n    nextPt[0] = points[i + 2];\n    nextPt[1] = points[i + 3];\n    pt[0] = points[i];\n    pt[1] = points[i + 1];\n    stepPoints.push(pt[0], pt[1]);\n    switch (stepTurnAt) {\n      case 'end':\n        stepPt[baseIndex] = nextPt[baseIndex];\n        stepPt[1 - baseIndex] = pt[1 - baseIndex];\n        stepPoints.push(stepPt[0], stepPt[1]);\n        break;\n      case 'middle':\n        var middle = (pt[baseIndex] + nextPt[baseIndex]) / 2;\n        var stepPt2 = [];\n        stepPt[baseIndex] = stepPt2[baseIndex] = middle;\n        stepPt[1 - baseIndex] = pt[1 - baseIndex];\n        stepPt2[1 - baseIndex] = nextPt[1 - baseIndex];\n        stepPoints.push(stepPt[0], stepPt[1]);\n        stepPoints.push(stepPt2[0], stepPt2[1]);\n        break;\n      default:\n        // default is start\n        stepPt[baseIndex] = pt[baseIndex];\n        stepPt[1 - baseIndex] = nextPt[1 - baseIndex];\n        stepPoints.push(stepPt[0], stepPt[1]);\n    }\n  } // Last points\n\n  stepPoints.push(points[i++], points[i++]);\n  return stepPoints;\n}\n/**\r\n * Clip color stops to edge. Avoid creating too large gradients.\r\n * Which may lead to blurry when GPU acceleration is enabled. See #15680\r\n *\r\n * The stops has been sorted from small to large.\r\n */\n\nfunction clipColorStops(colorStops, maxSize) {\n  var newColorStops = [];\n  var len = colorStops.length; // coord will always < 0 in prevOutOfRangeColorStop.\n\n  var prevOutOfRangeColorStop;\n  var prevInRangeColorStop;\n  function lerpStop(stop0, stop1, clippedCoord) {\n    var coord0 = stop0.coord;\n    var p = (clippedCoord - coord0) / (stop1.coord - coord0);\n    var color = lerp(p, [stop0.color, stop1.color]);\n    return {\n      coord: clippedCoord,\n      color: color\n    };\n  }\n  for (var i = 0; i < len; i++) {\n    var stop_1 = colorStops[i];\n    var coord = stop_1.coord;\n    if (coord < 0) {\n      prevOutOfRangeColorStop = stop_1;\n    } else if (coord > maxSize) {\n      if (prevInRangeColorStop) {\n        newColorStops.push(lerpStop(prevInRangeColorStop, stop_1, maxSize));\n      } else if (prevOutOfRangeColorStop) {\n        // If there are two stops and coord range is between these two stops\n        newColorStops.push(lerpStop(prevOutOfRangeColorStop, stop_1, 0), lerpStop(prevOutOfRangeColorStop, stop_1, maxSize));\n      } // All following stop will be out of range. So just ignore them.\n\n      break;\n    } else {\n      if (prevOutOfRangeColorStop) {\n        newColorStops.push(lerpStop(prevOutOfRangeColorStop, stop_1, 0)); // Reset\n\n        prevOutOfRangeColorStop = null;\n      }\n      newColorStops.push(stop_1);\n      prevInRangeColorStop = stop_1;\n    }\n  }\n  return newColorStops;\n}\nfunction getVisualGradient(data, coordSys, api) {\n  var visualMetaList = data.getVisual('visualMeta');\n  if (!visualMetaList || !visualMetaList.length || !data.count()) {\n    // When data.count() is 0, gradient range can not be calculated.\n    return;\n  }\n  if (coordSys.type !== 'cartesian2d') {\n    if (process.env.NODE_ENV !== 'production') {\n      console.warn('Visual map on line style is only supported on cartesian2d.');\n    }\n    return;\n  }\n  var coordDim;\n  var visualMeta;\n  for (var i = visualMetaList.length - 1; i >= 0; i--) {\n    var dimInfo = data.getDimensionInfo(visualMetaList[i].dimension);\n    coordDim = dimInfo && dimInfo.coordDim; // Can only be x or y\n\n    if (coordDim === 'x' || coordDim === 'y') {\n      visualMeta = visualMetaList[i];\n      break;\n    }\n  }\n  if (!visualMeta) {\n    if (process.env.NODE_ENV !== 'production') {\n      console.warn('Visual map on line style only support x or y dimension.');\n    }\n    return;\n  } // If the area to be rendered is bigger than area defined by LinearGradient,\n  // the canvas spec prescribes that the color of the first stop and the last\n  // stop should be used. But if two stops are added at offset 0, in effect\n  // browsers use the color of the second stop to render area outside\n  // LinearGradient. So we can only infinitesimally extend area defined in\n  // LinearGradient to render `outerColors`.\n\n  var axis = coordSys.getAxis(coordDim); // dataToCoord mapping may not be linear, but must be monotonic.\n\n  var colorStops = zrUtil.map(visualMeta.stops, function (stop) {\n    // offset will be calculated later.\n    return {\n      coord: axis.toGlobalCoord(axis.dataToCoord(stop.value)),\n      color: stop.color\n    };\n  });\n  var stopLen = colorStops.length;\n  var outerColors = visualMeta.outerColors.slice();\n  if (stopLen && colorStops[0].coord > colorStops[stopLen - 1].coord) {\n    colorStops.reverse();\n    outerColors.reverse();\n  }\n  var colorStopsInRange = clipColorStops(colorStops, coordDim === 'x' ? api.getWidth() : api.getHeight());\n  var inRangeStopLen = colorStopsInRange.length;\n  if (!inRangeStopLen && stopLen) {\n    // All stops are out of range. All will be the same color.\n    return colorStops[0].coord < 0 ? outerColors[1] ? outerColors[1] : colorStops[stopLen - 1].color : outerColors[0] ? outerColors[0] : colorStops[0].color;\n  }\n  var tinyExtent = 10; // Arbitrary value: 10px\n\n  var minCoord = colorStopsInRange[0].coord - tinyExtent;\n  var maxCoord = colorStopsInRange[inRangeStopLen - 1].coord + tinyExtent;\n  var coordSpan = maxCoord - minCoord;\n  if (coordSpan < 1e-3) {\n    return 'transparent';\n  }\n  zrUtil.each(colorStopsInRange, function (stop) {\n    stop.offset = (stop.coord - minCoord) / coordSpan;\n  });\n  colorStopsInRange.push({\n    // NOTE: inRangeStopLen may still be 0 if stoplen is zero.\n    offset: inRangeStopLen ? colorStopsInRange[inRangeStopLen - 1].offset : 0.5,\n    color: outerColors[1] || 'transparent'\n  });\n  colorStopsInRange.unshift({\n    offset: inRangeStopLen ? colorStopsInRange[0].offset : 0.5,\n    color: outerColors[0] || 'transparent'\n  });\n  var gradient = new graphic.LinearGradient(0, 0, 0, 0, colorStopsInRange, true);\n  gradient[coordDim] = minCoord;\n  gradient[coordDim + '2'] = maxCoord;\n  return gradient;\n}\nfunction getIsIgnoreFunc(seriesModel, data, coordSys) {\n  var showAllSymbol = seriesModel.get('showAllSymbol');\n  var isAuto = showAllSymbol === 'auto';\n  if (showAllSymbol && !isAuto) {\n    return;\n  }\n  var categoryAxis = coordSys.getAxesByScale('ordinal')[0];\n  if (!categoryAxis) {\n    return;\n  } // Note that category label interval strategy might bring some weird effect\n  // in some scenario: users may wonder why some of the symbols are not\n  // displayed. So we show all symbols as possible as we can.\n\n  if (isAuto // Simplify the logic, do not determine label overlap here.\n  && canShowAllSymbolForCategory(categoryAxis, data)) {\n    return;\n  } // Otherwise follow the label interval strategy on category axis.\n\n  var categoryDataDim = data.mapDimension(categoryAxis.dim);\n  var labelMap = {};\n  zrUtil.each(categoryAxis.getViewLabels(), function (labelItem) {\n    var ordinalNumber = categoryAxis.scale.getRawOrdinalNumber(labelItem.tickValue);\n    labelMap[ordinalNumber] = 1;\n  });\n  return function (dataIndex) {\n    return !labelMap.hasOwnProperty(data.get(categoryDataDim, dataIndex));\n  };\n}\nfunction canShowAllSymbolForCategory(categoryAxis, data) {\n  // In most cases, line is monotonous on category axis, and the label size\n  // is close with each other. So we check the symbol size and some of the\n  // label size alone with the category axis to estimate whether all symbol\n  // can be shown without overlap.\n  var axisExtent = categoryAxis.getExtent();\n  var availSize = Math.abs(axisExtent[1] - axisExtent[0]) / categoryAxis.scale.count();\n  isNaN(availSize) && (availSize = 0); // 0/0 is NaN.\n  // Sampling some points, max 5.\n\n  var dataLen = data.count();\n  var step = Math.max(1, Math.round(dataLen / 5));\n  for (var dataIndex = 0; dataIndex < dataLen; dataIndex += step) {\n    if (SymbolClz.getSymbolSize(data, dataIndex // Only for cartesian, where `isHorizontal` exists.\n    )[categoryAxis.isHorizontal() ? 1 : 0] // Empirical number\n    * 1.5 > availSize) {\n      return false;\n    }\n  }\n  return true;\n}\nfunction isPointNull(x, y) {\n  return isNaN(x) || isNaN(y);\n}\nfunction getLastIndexNotNull(points) {\n  var len = points.length / 2;\n  for (; len > 0; len--) {\n    if (!isPointNull(points[len * 2 - 2], points[len * 2 - 1])) {\n      break;\n    }\n  }\n  return len - 1;\n}\nfunction getPointAtIndex(points, idx) {\n  return [points[idx * 2], points[idx * 2 + 1]];\n}\nfunction getIndexRange(points, xOrY, dim) {\n  var len = points.length / 2;\n  var dimIdx = dim === 'x' ? 0 : 1;\n  var a;\n  var b;\n  var prevIndex = 0;\n  var nextIndex = -1;\n  for (var i = 0; i < len; i++) {\n    b = points[i * 2 + dimIdx];\n    if (isNaN(b) || isNaN(points[i * 2 + 1 - dimIdx])) {\n      continue;\n    }\n    if (i === 0) {\n      a = b;\n      continue;\n    }\n    if (a <= xOrY && b >= xOrY || a >= xOrY && b <= xOrY) {\n      nextIndex = i;\n      break;\n    }\n    prevIndex = i;\n    a = b;\n  }\n  return {\n    range: [prevIndex, nextIndex],\n    t: (xOrY - a) / (b - a)\n  };\n}\nfunction anyStateShowEndLabel(seriesModel) {\n  if (seriesModel.get(['endLabel', 'show'])) {\n    return true;\n  }\n  for (var i = 0; i < SPECIAL_STATES.length; i++) {\n    if (seriesModel.get([SPECIAL_STATES[i], 'endLabel', 'show'])) {\n      return true;\n    }\n  }\n  return false;\n}\nfunction createLineClipPath(lineView, coordSys, hasAnimation, seriesModel) {\n  if (isCoordinateSystemType(coordSys, 'cartesian2d')) {\n    var endLabelModel_1 = seriesModel.getModel('endLabel');\n    var valueAnimation_1 = endLabelModel_1.get('valueAnimation');\n    var data_1 = seriesModel.getData();\n    var labelAnimationRecord_1 = {\n      lastFrameIndex: 0\n    };\n    var during = anyStateShowEndLabel(seriesModel) ? function (percent, clipRect) {\n      lineView._endLabelOnDuring(percent, clipRect, data_1, labelAnimationRecord_1, valueAnimation_1, endLabelModel_1, coordSys);\n    } : null;\n    var isHorizontal = coordSys.getBaseAxis().isHorizontal();\n    var clipPath = createGridClipPath(coordSys, hasAnimation, seriesModel, function () {\n      var endLabel = lineView._endLabel;\n      if (endLabel && hasAnimation) {\n        if (labelAnimationRecord_1.originalX != null) {\n          endLabel.attr({\n            x: labelAnimationRecord_1.originalX,\n            y: labelAnimationRecord_1.originalY\n          });\n        }\n      }\n    }, during); // Expand clip shape to avoid clipping when line value exceeds axis\n\n    if (!seriesModel.get('clip', true)) {\n      var rectShape = clipPath.shape;\n      var expandSize = Math.max(rectShape.width, rectShape.height);\n      if (isHorizontal) {\n        rectShape.y -= expandSize;\n        rectShape.height += expandSize * 2;\n      } else {\n        rectShape.x -= expandSize;\n        rectShape.width += expandSize * 2;\n      }\n    } // Set to the final frame. To make sure label layout is right.\n\n    if (during) {\n      during(1, clipPath);\n    }\n    return clipPath;\n  } else {\n    if (process.env.NODE_ENV !== 'production') {\n      if (seriesModel.get(['endLabel', 'show'])) {\n        console.warn('endLabel is not supported for lines in polar systems.');\n      }\n    }\n    return createPolarClipPath(coordSys, hasAnimation, seriesModel);\n  }\n}\nfunction getEndLabelStateSpecified(endLabelModel, coordSys) {\n  var baseAxis = coordSys.getBaseAxis();\n  var isHorizontal = baseAxis.isHorizontal();\n  var isBaseInversed = baseAxis.inverse;\n  var align = isHorizontal ? isBaseInversed ? 'right' : 'left' : 'center';\n  var verticalAlign = isHorizontal ? 'middle' : isBaseInversed ? 'top' : 'bottom';\n  return {\n    normal: {\n      align: endLabelModel.get('align') || align,\n      verticalAlign: endLabelModel.get('verticalAlign') || verticalAlign\n    }\n  };\n}\nvar LineView = /** @class */\nfunction (_super) {\n  __extends(LineView, _super);\n  function LineView() {\n    return _super !== null && _super.apply(this, arguments) || this;\n  }\n  LineView.prototype.init = function () {\n    var lineGroup = new graphic.Group();\n    var symbolDraw = new SymbolDraw();\n    this.group.add(symbolDraw.group);\n    this._symbolDraw = symbolDraw;\n    this._lineGroup = lineGroup;\n  };\n  LineView.prototype.render = function (seriesModel, ecModel, api) {\n    var _this = this;\n    var coordSys = seriesModel.coordinateSystem;\n    var group = this.group;\n    var data = seriesModel.getData();\n    var lineStyleModel = seriesModel.getModel('lineStyle');\n    var areaStyleModel = seriesModel.getModel('areaStyle');\n    var points = data.getLayout('points') || [];\n    var isCoordSysPolar = coordSys.type === 'polar';\n    var prevCoordSys = this._coordSys;\n    var symbolDraw = this._symbolDraw;\n    var polyline = this._polyline;\n    var polygon = this._polygon;\n    var lineGroup = this._lineGroup;\n    var hasAnimation = !ecModel.ssr && seriesModel.isAnimationEnabled();\n    var isAreaChart = !areaStyleModel.isEmpty();\n    var valueOrigin = areaStyleModel.get('origin');\n    var dataCoordInfo = prepareDataCoordInfo(coordSys, data, valueOrigin);\n    var stackedOnPoints = isAreaChart && getStackedOnPoints(coordSys, data, dataCoordInfo);\n    var showSymbol = seriesModel.get('showSymbol');\n    var connectNulls = seriesModel.get('connectNulls');\n    var isIgnoreFunc = showSymbol && !isCoordSysPolar && getIsIgnoreFunc(seriesModel, data, coordSys); // Remove temporary symbols\n\n    var oldData = this._data;\n    oldData && oldData.eachItemGraphicEl(function (el, idx) {\n      if (el.__temp) {\n        group.remove(el);\n        oldData.setItemGraphicEl(idx, null);\n      }\n    }); // Remove previous created symbols if showSymbol changed to false\n\n    if (!showSymbol) {\n      symbolDraw.remove();\n    }\n    group.add(lineGroup); // FIXME step not support polar\n\n    var step = !isCoordSysPolar ? seriesModel.get('step') : false;\n    var clipShapeForSymbol;\n    if (coordSys && coordSys.getArea && seriesModel.get('clip', true)) {\n      clipShapeForSymbol = coordSys.getArea(); // Avoid float number rounding error for symbol on the edge of axis extent.\n      // See #7913 and `test/dataZoom-clip.html`.\n\n      if (clipShapeForSymbol.width != null) {\n        clipShapeForSymbol.x -= 0.1;\n        clipShapeForSymbol.y -= 0.1;\n        clipShapeForSymbol.width += 0.2;\n        clipShapeForSymbol.height += 0.2;\n      } else if (clipShapeForSymbol.r0) {\n        clipShapeForSymbol.r0 -= 0.5;\n        clipShapeForSymbol.r += 0.5;\n      }\n    }\n    this._clipShapeForSymbol = clipShapeForSymbol;\n    var visualColor = getVisualGradient(data, coordSys, api) || data.getVisual('style')[data.getVisual('drawType')]; // Initialization animation or coordinate system changed\n\n    if (!(polyline && prevCoordSys.type === coordSys.type && step === this._step)) {\n      showSymbol && symbolDraw.updateData(data, {\n        isIgnore: isIgnoreFunc,\n        clipShape: clipShapeForSymbol,\n        disableAnimation: true,\n        getSymbolPoint: function (idx) {\n          return [points[idx * 2], points[idx * 2 + 1]];\n        }\n      });\n      hasAnimation && this._initSymbolLabelAnimation(data, coordSys, clipShapeForSymbol);\n      if (step) {\n        // TODO If stacked series is not step\n        points = turnPointsIntoStep(points, coordSys, step, connectNulls);\n        if (stackedOnPoints) {\n          stackedOnPoints = turnPointsIntoStep(stackedOnPoints, coordSys, step, connectNulls);\n        }\n      }\n      polyline = this._newPolyline(points);\n      if (isAreaChart) {\n        polygon = this._newPolygon(points, stackedOnPoints);\n      } // If areaStyle is removed\n      else if (polygon) {\n        lineGroup.remove(polygon);\n        polygon = this._polygon = null;\n      } // NOTE: Must update _endLabel before setClipPath.\n\n      if (!isCoordSysPolar) {\n        this._initOrUpdateEndLabel(seriesModel, coordSys, convertToColorString(visualColor));\n      }\n      lineGroup.setClipPath(createLineClipPath(this, coordSys, true, seriesModel));\n    } else {\n      if (isAreaChart && !polygon) {\n        // If areaStyle is added\n        polygon = this._newPolygon(points, stackedOnPoints);\n      } else if (polygon && !isAreaChart) {\n        // If areaStyle is removed\n        lineGroup.remove(polygon);\n        polygon = this._polygon = null;\n      } // NOTE: Must update _endLabel before setClipPath.\n\n      if (!isCoordSysPolar) {\n        this._initOrUpdateEndLabel(seriesModel, coordSys, convertToColorString(visualColor));\n      } // Update clipPath\n\n      var oldClipPath = lineGroup.getClipPath();\n      if (oldClipPath) {\n        var newClipPath = createLineClipPath(this, coordSys, false, seriesModel);\n        graphic.initProps(oldClipPath, {\n          shape: newClipPath.shape\n        }, seriesModel);\n      } else {\n        lineGroup.setClipPath(createLineClipPath(this, coordSys, true, seriesModel));\n      } // Always update, or it is wrong in the case turning on legend\n      // because points are not changed.\n\n      showSymbol && symbolDraw.updateData(data, {\n        isIgnore: isIgnoreFunc,\n        clipShape: clipShapeForSymbol,\n        disableAnimation: true,\n        getSymbolPoint: function (idx) {\n          return [points[idx * 2], points[idx * 2 + 1]];\n        }\n      }); // In the case data zoom triggered refreshing frequently\n      // Data may not change if line has a category axis. So it should animate nothing.\n\n      if (!isPointsSame(this._stackedOnPoints, stackedOnPoints) || !isPointsSame(this._points, points)) {\n        if (hasAnimation) {\n          this._doUpdateAnimation(data, stackedOnPoints, coordSys, api, step, valueOrigin, connectNulls);\n        } else {\n          // Not do it in update with animation\n          if (step) {\n            // TODO If stacked series is not step\n            points = turnPointsIntoStep(points, coordSys, step, connectNulls);\n            if (stackedOnPoints) {\n              stackedOnPoints = turnPointsIntoStep(stackedOnPoints, coordSys, step, connectNulls);\n            }\n          }\n          polyline.setShape({\n            points: points\n          });\n          polygon && polygon.setShape({\n            points: points,\n            stackedOnPoints: stackedOnPoints\n          });\n        }\n      }\n    }\n    var emphasisModel = seriesModel.getModel('emphasis');\n    var focus = emphasisModel.get('focus');\n    var blurScope = emphasisModel.get('blurScope');\n    var emphasisDisabled = emphasisModel.get('disabled');\n    polyline.useStyle(zrUtil.defaults(\n    // Use color in lineStyle first\n    lineStyleModel.getLineStyle(), {\n      fill: 'none',\n      stroke: visualColor,\n      lineJoin: 'bevel'\n    }));\n    setStatesStylesFromModel(polyline, seriesModel, 'lineStyle');\n    if (polyline.style.lineWidth > 0 && seriesModel.get(['emphasis', 'lineStyle', 'width']) === 'bolder') {\n      var emphasisLineStyle = polyline.getState('emphasis').style;\n      emphasisLineStyle.lineWidth = +polyline.style.lineWidth + 1;\n    } // Needs seriesIndex for focus\n\n    getECData(polyline).seriesIndex = seriesModel.seriesIndex;\n    toggleHoverEmphasis(polyline, focus, blurScope, emphasisDisabled);\n    var smooth = getSmooth(seriesModel.get('smooth'));\n    var smoothMonotone = seriesModel.get('smoothMonotone');\n    polyline.setShape({\n      smooth: smooth,\n      smoothMonotone: smoothMonotone,\n      connectNulls: connectNulls\n    });\n    if (polygon) {\n      var stackedOnSeries = data.getCalculationInfo('stackedOnSeries');\n      var stackedOnSmooth = 0;\n      polygon.useStyle(zrUtil.defaults(areaStyleModel.getAreaStyle(), {\n        fill: visualColor,\n        opacity: 0.7,\n        lineJoin: 'bevel',\n        decal: data.getVisual('style').decal\n      }));\n      if (stackedOnSeries) {\n        stackedOnSmooth = getSmooth(stackedOnSeries.get('smooth'));\n      }\n      polygon.setShape({\n        smooth: smooth,\n        stackedOnSmooth: stackedOnSmooth,\n        smoothMonotone: smoothMonotone,\n        connectNulls: connectNulls\n      });\n      setStatesStylesFromModel(polygon, seriesModel, 'areaStyle'); // Needs seriesIndex for focus\n\n      getECData(polygon).seriesIndex = seriesModel.seriesIndex;\n      toggleHoverEmphasis(polygon, focus, blurScope, emphasisDisabled);\n    }\n    var changePolyState = function (toState) {\n      _this._changePolyState(toState);\n    };\n    data.eachItemGraphicEl(function (el) {\n      // Switch polyline / polygon state if element changed its state.\n      el && (el.onHoverStateChange = changePolyState);\n    });\n    this._polyline.onHoverStateChange = changePolyState;\n    this._data = data; // Save the coordinate system for transition animation when data changed\n\n    this._coordSys = coordSys;\n    this._stackedOnPoints = stackedOnPoints;\n    this._points = points;\n    this._step = step;\n    this._valueOrigin = valueOrigin;\n    if (seriesModel.get('triggerLineEvent')) {\n      this.packEventData(seriesModel, polyline);\n      polygon && this.packEventData(seriesModel, polygon);\n    }\n  };\n  LineView.prototype.packEventData = function (seriesModel, el) {\n    getECData(el).eventData = {\n      componentType: 'series',\n      componentSubType: 'line',\n      componentIndex: seriesModel.componentIndex,\n      seriesIndex: seriesModel.seriesIndex,\n      seriesName: seriesModel.name,\n      seriesType: 'line'\n    };\n  };\n  LineView.prototype.highlight = function (seriesModel, ecModel, api, payload) {\n    var data = seriesModel.getData();\n    var dataIndex = modelUtil.queryDataIndex(data, payload);\n    this._changePolyState('emphasis');\n    if (!(dataIndex instanceof Array) && dataIndex != null && dataIndex >= 0) {\n      var points = data.getLayout('points');\n      var symbol = data.getItemGraphicEl(dataIndex);\n      if (!symbol) {\n        // Create a temporary symbol if it is not exists\n        var x = points[dataIndex * 2];\n        var y = points[dataIndex * 2 + 1];\n        if (isNaN(x) || isNaN(y)) {\n          // Null data\n          return;\n        } // fix #11360: shouldn't draw symbol outside clipShapeForSymbol\n\n        if (this._clipShapeForSymbol && !this._clipShapeForSymbol.contain(x, y)) {\n          return;\n        }\n        var zlevel = seriesModel.get('zlevel') || 0;\n        var z = seriesModel.get('z') || 0;\n        symbol = new SymbolClz(data, dataIndex);\n        symbol.x = x;\n        symbol.y = y;\n        symbol.setZ(zlevel, z); // ensure label text of the temporary symbol is in front of line and area polygon\n\n        var symbolLabel = symbol.getSymbolPath().getTextContent();\n        if (symbolLabel) {\n          symbolLabel.zlevel = zlevel;\n          symbolLabel.z = z;\n          symbolLabel.z2 = this._polyline.z2 + 1;\n        }\n        symbol.__temp = true;\n        data.setItemGraphicEl(dataIndex, symbol); // Stop scale animation\n\n        symbol.stopSymbolAnimation(true);\n        this.group.add(symbol);\n      }\n      symbol.highlight();\n    } else {\n      // Highlight whole series\n      ChartView.prototype.highlight.call(this, seriesModel, ecModel, api, payload);\n    }\n  };\n  LineView.prototype.downplay = function (seriesModel, ecModel, api, payload) {\n    var data = seriesModel.getData();\n    var dataIndex = modelUtil.queryDataIndex(data, payload);\n    this._changePolyState('normal');\n    if (dataIndex != null && dataIndex >= 0) {\n      var symbol = data.getItemGraphicEl(dataIndex);\n      if (symbol) {\n        if (symbol.__temp) {\n          data.setItemGraphicEl(dataIndex, null);\n          this.group.remove(symbol);\n        } else {\n          symbol.downplay();\n        }\n      }\n    } else {\n      // FIXME\n      // can not downplay completely.\n      // Downplay whole series\n      ChartView.prototype.downplay.call(this, seriesModel, ecModel, api, payload);\n    }\n  };\n  LineView.prototype._changePolyState = function (toState) {\n    var polygon = this._polygon;\n    setStatesFlag(this._polyline, toState);\n    polygon && setStatesFlag(polygon, toState);\n  };\n  LineView.prototype._newPolyline = function (points) {\n    var polyline = this._polyline; // Remove previous created polyline\n\n    if (polyline) {\n      this._lineGroup.remove(polyline);\n    }\n    polyline = new ECPolyline({\n      shape: {\n        points: points\n      },\n      segmentIgnoreThreshold: 2,\n      z2: 10\n    });\n    this._lineGroup.add(polyline);\n    this._polyline = polyline;\n    return polyline;\n  };\n  LineView.prototype._newPolygon = function (points, stackedOnPoints) {\n    var polygon = this._polygon; // Remove previous created polygon\n\n    if (polygon) {\n      this._lineGroup.remove(polygon);\n    }\n    polygon = new ECPolygon({\n      shape: {\n        points: points,\n        stackedOnPoints: stackedOnPoints\n      },\n      segmentIgnoreThreshold: 2\n    });\n    this._lineGroup.add(polygon);\n    this._polygon = polygon;\n    return polygon;\n  };\n  LineView.prototype._initSymbolLabelAnimation = function (data, coordSys, clipShape) {\n    var isHorizontalOrRadial;\n    var isCoordSysPolar;\n    var baseAxis = coordSys.getBaseAxis();\n    var isAxisInverse = baseAxis.inverse;\n    if (coordSys.type === 'cartesian2d') {\n      isHorizontalOrRadial = baseAxis.isHorizontal();\n      isCoordSysPolar = false;\n    } else if (coordSys.type === 'polar') {\n      isHorizontalOrRadial = baseAxis.dim === 'angle';\n      isCoordSysPolar = true;\n    }\n    var seriesModel = data.hostModel;\n    var seriesDuration = seriesModel.get('animationDuration');\n    if (zrUtil.isFunction(seriesDuration)) {\n      seriesDuration = seriesDuration(null);\n    }\n    var seriesDelay = seriesModel.get('animationDelay') || 0;\n    var seriesDelayValue = zrUtil.isFunction(seriesDelay) ? seriesDelay(null) : seriesDelay;\n    data.eachItemGraphicEl(function (symbol, idx) {\n      var el = symbol;\n      if (el) {\n        var point = [symbol.x, symbol.y];\n        var start = void 0;\n        var end = void 0;\n        var current = void 0;\n        if (clipShape) {\n          if (isCoordSysPolar) {\n            var polarClip = clipShape;\n            var coord = coordSys.pointToCoord(point);\n            if (isHorizontalOrRadial) {\n              start = polarClip.startAngle;\n              end = polarClip.endAngle;\n              current = -coord[1] / 180 * Math.PI;\n            } else {\n              start = polarClip.r0;\n              end = polarClip.r;\n              current = coord[0];\n            }\n          } else {\n            var gridClip = clipShape;\n            if (isHorizontalOrRadial) {\n              start = gridClip.x;\n              end = gridClip.x + gridClip.width;\n              current = symbol.x;\n            } else {\n              start = gridClip.y + gridClip.height;\n              end = gridClip.y;\n              current = symbol.y;\n            }\n          }\n        }\n        var ratio = end === start ? 0 : (current - start) / (end - start);\n        if (isAxisInverse) {\n          ratio = 1 - ratio;\n        }\n        var delay = zrUtil.isFunction(seriesDelay) ? seriesDelay(idx) : seriesDuration * ratio + seriesDelayValue;\n        var symbolPath = el.getSymbolPath();\n        var text = symbolPath.getTextContent();\n        el.attr({\n          scaleX: 0,\n          scaleY: 0\n        });\n        el.animateTo({\n          scaleX: 1,\n          scaleY: 1\n        }, {\n          duration: 200,\n          setToFinal: true,\n          delay: delay\n        });\n        if (text) {\n          text.animateFrom({\n            style: {\n              opacity: 0\n            }\n          }, {\n            duration: 300,\n            delay: delay\n          });\n        }\n        symbolPath.disableLabelAnimation = true;\n      }\n    });\n  };\n  LineView.prototype._initOrUpdateEndLabel = function (seriesModel, coordSys, inheritColor) {\n    var endLabelModel = seriesModel.getModel('endLabel');\n    if (anyStateShowEndLabel(seriesModel)) {\n      var data_2 = seriesModel.getData();\n      var polyline = this._polyline; // series may be filtered.\n\n      var points = data_2.getLayout('points');\n      if (!points) {\n        polyline.removeTextContent();\n        this._endLabel = null;\n        return;\n      }\n      var endLabel = this._endLabel;\n      if (!endLabel) {\n        endLabel = this._endLabel = new graphic.Text({\n          z2: 200 // should be higher than item symbol\n        });\n\n        endLabel.ignoreClip = true;\n        polyline.setTextContent(this._endLabel);\n        polyline.disableLabelAnimation = true;\n      } // Find last non-NaN data to display data\n\n      var dataIndex = getLastIndexNotNull(points);\n      if (dataIndex >= 0) {\n        setLabelStyle(polyline, getLabelStatesModels(seriesModel, 'endLabel'), {\n          inheritColor: inheritColor,\n          labelFetcher: seriesModel,\n          labelDataIndex: dataIndex,\n          defaultText: function (dataIndex, opt, interpolatedValue) {\n            return interpolatedValue != null ? getDefaultInterpolatedLabel(data_2, interpolatedValue) : getDefaultLabel(data_2, dataIndex);\n          },\n          enableTextSetter: true\n        }, getEndLabelStateSpecified(endLabelModel, coordSys));\n        polyline.textConfig.position = null;\n      }\n    } else if (this._endLabel) {\n      this._polyline.removeTextContent();\n      this._endLabel = null;\n    }\n  };\n  LineView.prototype._endLabelOnDuring = function (percent, clipRect, data, animationRecord, valueAnimation, endLabelModel, coordSys) {\n    var endLabel = this._endLabel;\n    var polyline = this._polyline;\n    if (endLabel) {\n      // NOTE: Don't remove percent < 1. percent === 1 means the first frame during render.\n      // The label is not prepared at this time.\n      if (percent < 1 && animationRecord.originalX == null) {\n        animationRecord.originalX = endLabel.x;\n        animationRecord.originalY = endLabel.y;\n      }\n      var points = data.getLayout('points');\n      var seriesModel = data.hostModel;\n      var connectNulls = seriesModel.get('connectNulls');\n      var precision = endLabelModel.get('precision');\n      var distance = endLabelModel.get('distance') || 0;\n      var baseAxis = coordSys.getBaseAxis();\n      var isHorizontal = baseAxis.isHorizontal();\n      var isBaseInversed = baseAxis.inverse;\n      var clipShape = clipRect.shape;\n      var xOrY = isBaseInversed ? isHorizontal ? clipShape.x : clipShape.y + clipShape.height : isHorizontal ? clipShape.x + clipShape.width : clipShape.y;\n      var distanceX = (isHorizontal ? distance : 0) * (isBaseInversed ? -1 : 1);\n      var distanceY = (isHorizontal ? 0 : -distance) * (isBaseInversed ? -1 : 1);\n      var dim = isHorizontal ? 'x' : 'y';\n      var dataIndexRange = getIndexRange(points, xOrY, dim);\n      var indices = dataIndexRange.range;\n      var diff = indices[1] - indices[0];\n      var value = void 0;\n      if (diff >= 1) {\n        // diff > 1 && connectNulls, which is on the null data.\n        if (diff > 1 && !connectNulls) {\n          var pt = getPointAtIndex(points, indices[0]);\n          endLabel.attr({\n            x: pt[0] + distanceX,\n            y: pt[1] + distanceY\n          });\n          valueAnimation && (value = seriesModel.getRawValue(indices[0]));\n        } else {\n          var pt = polyline.getPointOn(xOrY, dim);\n          pt && endLabel.attr({\n            x: pt[0] + distanceX,\n            y: pt[1] + distanceY\n          });\n          var startValue = seriesModel.getRawValue(indices[0]);\n          var endValue = seriesModel.getRawValue(indices[1]);\n          valueAnimation && (value = modelUtil.interpolateRawValues(data, precision, startValue, endValue, dataIndexRange.t));\n        }\n        animationRecord.lastFrameIndex = indices[0];\n      } else {\n        // If diff <= 0, which is the range is not found(Include NaN)\n        // Choose the first point or last point.\n        var idx = percent === 1 || animationRecord.lastFrameIndex > 0 ? indices[0] : 0;\n        var pt = getPointAtIndex(points, idx);\n        valueAnimation && (value = seriesModel.getRawValue(idx));\n        endLabel.attr({\n          x: pt[0] + distanceX,\n          y: pt[1] + distanceY\n        });\n      }\n      if (valueAnimation) {\n        var inner = labelInner(endLabel);\n        if (typeof inner.setLabelText === 'function') {\n          inner.setLabelText(value);\n        }\n      }\n    }\n  };\n  /**\r\n   * @private\r\n   */\n  // FIXME Two value axis\n\n  LineView.prototype._doUpdateAnimation = function (data, stackedOnPoints, coordSys, api, step, valueOrigin, connectNulls) {\n    var polyline = this._polyline;\n    var polygon = this._polygon;\n    var seriesModel = data.hostModel;\n    var diff = lineAnimationDiff(this._data, data, this._stackedOnPoints, stackedOnPoints, this._coordSys, coordSys, this._valueOrigin, valueOrigin);\n    var current = diff.current;\n    var stackedOnCurrent = diff.stackedOnCurrent;\n    var next = diff.next;\n    var stackedOnNext = diff.stackedOnNext;\n    if (step) {\n      // TODO If stacked series is not step\n      current = turnPointsIntoStep(diff.current, coordSys, step, connectNulls);\n      stackedOnCurrent = turnPointsIntoStep(diff.stackedOnCurrent, coordSys, step, connectNulls);\n      next = turnPointsIntoStep(diff.next, coordSys, step, connectNulls);\n      stackedOnNext = turnPointsIntoStep(diff.stackedOnNext, coordSys, step, connectNulls);\n    } // Don't apply animation if diff is large.\n    // For better result and avoid memory explosion problems like\n    // https://github.com/apache/incubator-echarts/issues/12229\n\n    if (getBoundingDiff(current, next) > 3000 || polygon && getBoundingDiff(stackedOnCurrent, stackedOnNext) > 3000) {\n      polyline.stopAnimation();\n      polyline.setShape({\n        points: next\n      });\n      if (polygon) {\n        polygon.stopAnimation();\n        polygon.setShape({\n          points: next,\n          stackedOnPoints: stackedOnNext\n        });\n      }\n      return;\n    }\n    polyline.shape.__points = diff.current;\n    polyline.shape.points = current;\n    var target = {\n      shape: {\n        points: next\n      }\n    }; // Also animate the original points.\n    // If points reference is changed when turning into step line.\n\n    if (diff.current !== current) {\n      target.shape.__points = diff.next;\n    } // Stop previous animation.\n\n    polyline.stopAnimation();\n    graphic.updateProps(polyline, target, seriesModel);\n    if (polygon) {\n      polygon.setShape({\n        // Reuse the points with polyline.\n        points: current,\n        stackedOnPoints: stackedOnCurrent\n      });\n      polygon.stopAnimation();\n      graphic.updateProps(polygon, {\n        shape: {\n          stackedOnPoints: stackedOnNext\n        }\n      }, seriesModel); // If use attr directly in updateProps.\n\n      if (polyline.shape.points !== polygon.shape.points) {\n        polygon.shape.points = polyline.shape.points;\n      }\n    }\n    var updatedDataInfo = [];\n    var diffStatus = diff.status;\n    for (var i = 0; i < diffStatus.length; i++) {\n      var cmd = diffStatus[i].cmd;\n      if (cmd === '=') {\n        var el = data.getItemGraphicEl(diffStatus[i].idx1);\n        if (el) {\n          updatedDataInfo.push({\n            el: el,\n            ptIdx: i // Index of points\n          });\n        }\n      }\n    }\n\n    if (polyline.animators && polyline.animators.length) {\n      polyline.animators[0].during(function () {\n        polygon && polygon.dirtyShape();\n        var points = polyline.shape.__points;\n        for (var i = 0; i < updatedDataInfo.length; i++) {\n          var el = updatedDataInfo[i].el;\n          var offset = updatedDataInfo[i].ptIdx * 2;\n          el.x = points[offset];\n          el.y = points[offset + 1];\n          el.markRedraw();\n        }\n      });\n    }\n  };\n  LineView.prototype.remove = function (ecModel) {\n    var group = this.group;\n    var oldData = this._data;\n    this._lineGroup.removeAll();\n    this._symbolDraw.remove(true); // Remove temporary created elements when highlighting\n\n    oldData && oldData.eachItemGraphicEl(function (el, idx) {\n      if (el.__temp) {\n        group.remove(el);\n        oldData.setItemGraphicEl(idx, null);\n      }\n    });\n    this._polyline = this._polygon = this._coordSys = this._points = this._stackedOnPoints = this._endLabel = this._data = null;\n  };\n  LineView.type = 'line';\n  return LineView;\n}(ChartView);\nexport default LineView;", "map": {"version": 3, "names": ["__extends", "zrUtil", "SymbolDraw", "SymbolClz", "lineAnimationDiff", "graphic", "modelUtil", "ECPolyline", "ECPolygon", "ChartView", "prepareDataCoordInfo", "getStackedOnPoint", "createGridClipPath", "createPolarClipPath", "isCoordinateSystemType", "setStatesStylesFromModel", "setStatesFlag", "toggleHoverEmphasis", "SPECIAL_STATES", "setLabelStyle", "getLabelStatesModels", "labelInner", "getDefaultLabel", "getDefaultInterpolatedLabel", "getECData", "createFloat32Array", "convertToColorString", "lerp", "isPointsSame", "points1", "points2", "length", "i", "bboxFromPoints", "points", "minX", "Infinity", "minY", "maxX", "maxY", "x", "y", "isNaN", "Math", "min", "max", "getBoundingDiff", "_a", "min1", "max1", "_b", "min2", "max2", "abs", "getSmooth", "smooth", "isNumber", "getStackedOnPoints", "coordSys", "data", "dataCoordInfo", "valueDim", "len", "count", "idx", "pt", "turnPointsIntoStep", "stepTurnAt", "connectNulls", "baseAxis", "getBaseAxis", "baseIndex", "dim", "stepPoints", "stepPt", "nextPt", "filteredPoints", "push", "middle", "stepPt2", "clipColorStops", "colorStops", "maxSize", "newColorStops", "prevOutOfRangeColorStop", "prevInRangeColorStop", "lerpStop", "stop0", "stop1", "clippedCoord", "coord0", "coord", "p", "color", "stop_1", "getVisualGradient", "api", "visualMetaList", "getVisual", "type", "process", "env", "NODE_ENV", "console", "warn", "coordDim", "visualMeta", "dimInfo", "getDimensionInfo", "dimension", "axis", "getAxis", "map", "stops", "stop", "toGlobalCoord", "dataToCoord", "value", "stopLen", "outerColors", "slice", "reverse", "colorStopsInRange", "getWidth", "getHeight", "inRangeStopLen", "tinyExtent", "minCoord", "maxCoord", "coordSpan", "each", "offset", "unshift", "gradient", "LinearGradient", "getIsIgnoreFunc", "seriesModel", "showAllSymbol", "get", "isAuto", "categoryAxis", "getAxesByScale", "canShowAllSymbolForCategory", "categoryDataDim", "mapDimension", "labelMap", "getViewLabels", "labelItem", "ordinalNumber", "scale", "getRawOrdinalNumber", "tickValue", "dataIndex", "hasOwnProperty", "axisExtent", "getExtent", "availSize", "dataLen", "step", "round", "getSymbolSize", "isHorizontal", "isPointNull", "getLastIndexNotNull", "getPointAtIndex", "getIndexRange", "xOrY", "dimIdx", "a", "b", "prevIndex", "nextIndex", "range", "t", "anyStateShowEndLabel", "createLineClipPath", "lineView", "hasAnimation", "endLabelModel_1", "getModel", "valueAnimation_1", "data_1", "getData", "labelAnimationRecord_1", "lastFrameIndex", "during", "percent", "clipRect", "_endLabelOnDuring", "clipPath", "endLabel", "_end<PERSON>abel", "originalX", "attr", "originalY", "rectShape", "shape", "expandSize", "width", "height", "getEndLabelStateSpecified", "endLabelModel", "isBaseInversed", "inverse", "align", "verticalAlign", "normal", "LineView", "_super", "apply", "arguments", "prototype", "init", "lineGroup", "Group", "symbolDraw", "group", "add", "_symbolDraw", "_lineGroup", "render", "ecModel", "_this", "coordinateSystem", "lineStyleModel", "areaStyleModel", "getLayout", "isCoordSysPolar", "prevCoordSys", "_coordSys", "polyline", "_polyline", "polygon", "_polygon", "ssr", "isAnimationEnabled", "is<PERSON><PERSON><PERSON><PERSON>", "isEmpty", "valueOrigin", "stackedOnPoints", "showSymbol", "isIgnoreFunc", "oldData", "_data", "eachItemGraphicEl", "el", "__temp", "remove", "setItemGraphicEl", "clipShapeForSymbol", "getArea", "r0", "r", "_clipShapeForSymbol", "visualColor", "_step", "updateData", "isIgnore", "clipShape", "disableAnimation", "getSymbolPoint", "_initSymbolLabelAnimation", "_newPolyline", "_newPolygon", "_initOrUpdateEndLabel", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "old<PERSON><PERSON><PERSON><PERSON>", "getClipPath", "newClipPath", "initProps", "_stackedOnPoints", "_points", "_doUpdateAnimation", "setShape", "emphasisModel", "focus", "blurScope", "emphasisDisabled", "useStyle", "defaults", "getLineStyle", "fill", "stroke", "lineJoin", "style", "lineWidth", "emphasisLineStyle", "getState", "seriesIndex", "smoothMonotone", "stackedOnSeries", "getCalculationInfo", "stackedOnSmooth", "getAreaStyle", "opacity", "decal", "changePolyState", "toState", "_changePolyState", "onHoverStateChange", "_valueO<PERSON>in", "packEventData", "eventData", "componentType", "componentSubType", "componentIndex", "seriesName", "name", "seriesType", "highlight", "payload", "queryDataIndex", "Array", "symbol", "getItemGraphicEl", "contain", "zlevel", "z", "setZ", "symbol<PERSON><PERSON><PERSON>", "getSymbolPath", "getTextContent", "z2", "stopSymbolAnimation", "call", "downplay", "segmentIgnoreThreshold", "isHorizontalOrRadial", "isAxisInverse", "hostModel", "seriesDuration", "isFunction", "seriesDelay", "seriesDelayValue", "point", "start", "end", "current", "polarClip", "pointToCoord", "startAngle", "endAngle", "PI", "gridClip", "ratio", "delay", "symbolPath", "text", "scaleX", "scaleY", "animateTo", "duration", "setToFinal", "animateFrom", "disableLabelAnimation", "inheritColor", "data_2", "removeTextContent", "Text", "ignoreClip", "setTextContent", "labelFetcher", "labelDataIndex", "defaultText", "opt", "interpolatedV<PERSON>ue", "enableTextSetter", "textConfig", "position", "animationRecord", "valueAnimation", "precision", "distance", "distanceX", "distanceY", "dataIndexRange", "indices", "diff", "getRawValue", "getPointOn", "startValue", "endValue", "interpolateRawValues", "inner", "setLabelText", "stackedOnCurrent", "next", "stackedOnNext", "stopAnimation", "__points", "target", "updateProps", "updatedDataInfo", "diffStatus", "status", "cmd", "idx1", "ptIdx", "animators", "dirtyShape", "mark<PERSON><PERSON><PERSON>", "removeAll"], "sources": ["C:/WolfSystemV2/WolfOldFrontEnd/node_modules/echarts/lib/chart/line/LineView.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\"; // FIXME step not support polar\n\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport SymbolDraw from '../helper/SymbolDraw.js';\nimport SymbolClz from '../helper/Symbol.js';\nimport lineAnimationDiff from './lineAnimationDiff.js';\nimport * as graphic from '../../util/graphic.js';\nimport * as modelUtil from '../../util/model.js';\nimport { ECPolyline, ECPolygon } from './poly.js';\nimport ChartView from '../../view/Chart.js';\nimport { prepareDataCoordInfo, getStackedOnPoint } from './helper.js';\nimport { createGridClipPath, createPolarClipPath } from '../helper/createClipPathFromCoordSys.js';\nimport { isCoordinateSystemType } from '../../coord/CoordinateSystem.js';\nimport { setStatesStylesFromModel, setStatesFlag, toggleHoverEmphasis, SPECIAL_STATES } from '../../util/states.js';\nimport { setLabelStyle, getLabelStatesModels, labelInner } from '../../label/labelStyle.js';\nimport { getDefaultLabel, getDefaultInterpolatedLabel } from '../helper/labelHelper.js';\nimport { getECData } from '../../util/innerStore.js';\nimport { createFloat32Array } from '../../util/vendor.js';\nimport { convertToColorString } from '../../util/format.js';\nimport { lerp } from 'zrender/lib/tool/color.js';\n\nfunction isPointsSame(points1, points2) {\n  if (points1.length !== points2.length) {\n    return;\n  }\n\n  for (var i = 0; i < points1.length; i++) {\n    if (points1[i] !== points2[i]) {\n      return;\n    }\n  }\n\n  return true;\n}\n\nfunction bboxFromPoints(points) {\n  var minX = Infinity;\n  var minY = Infinity;\n  var maxX = -Infinity;\n  var maxY = -Infinity;\n\n  for (var i = 0; i < points.length;) {\n    var x = points[i++];\n    var y = points[i++];\n\n    if (!isNaN(x)) {\n      minX = Math.min(x, minX);\n      maxX = Math.max(x, maxX);\n    }\n\n    if (!isNaN(y)) {\n      minY = Math.min(y, minY);\n      maxY = Math.max(y, maxY);\n    }\n  }\n\n  return [[minX, minY], [maxX, maxY]];\n}\n\nfunction getBoundingDiff(points1, points2) {\n  var _a = bboxFromPoints(points1),\n      min1 = _a[0],\n      max1 = _a[1];\n\n  var _b = bboxFromPoints(points2),\n      min2 = _b[0],\n      max2 = _b[1]; // Get a max value from each corner of two boundings.\n\n\n  return Math.max(Math.abs(min1[0] - min2[0]), Math.abs(min1[1] - min2[1]), Math.abs(max1[0] - max2[0]), Math.abs(max1[1] - max2[1]));\n}\n\nfunction getSmooth(smooth) {\n  return zrUtil.isNumber(smooth) ? smooth : smooth ? 0.5 : 0;\n}\n\nfunction getStackedOnPoints(coordSys, data, dataCoordInfo) {\n  if (!dataCoordInfo.valueDim) {\n    return [];\n  }\n\n  var len = data.count();\n  var points = createFloat32Array(len * 2);\n\n  for (var idx = 0; idx < len; idx++) {\n    var pt = getStackedOnPoint(dataCoordInfo, coordSys, data, idx);\n    points[idx * 2] = pt[0];\n    points[idx * 2 + 1] = pt[1];\n  }\n\n  return points;\n}\n\nfunction turnPointsIntoStep(points, coordSys, stepTurnAt, connectNulls) {\n  var baseAxis = coordSys.getBaseAxis();\n  var baseIndex = baseAxis.dim === 'x' || baseAxis.dim === 'radius' ? 0 : 1;\n  var stepPoints = [];\n  var i = 0;\n  var stepPt = [];\n  var pt = [];\n  var nextPt = [];\n  var filteredPoints = [];\n\n  if (connectNulls) {\n    for (i = 0; i < points.length; i += 2) {\n      if (!isNaN(points[i]) && !isNaN(points[i + 1])) {\n        filteredPoints.push(points[i], points[i + 1]);\n      }\n    }\n\n    points = filteredPoints;\n  }\n\n  for (i = 0; i < points.length - 2; i += 2) {\n    nextPt[0] = points[i + 2];\n    nextPt[1] = points[i + 3];\n    pt[0] = points[i];\n    pt[1] = points[i + 1];\n    stepPoints.push(pt[0], pt[1]);\n\n    switch (stepTurnAt) {\n      case 'end':\n        stepPt[baseIndex] = nextPt[baseIndex];\n        stepPt[1 - baseIndex] = pt[1 - baseIndex];\n        stepPoints.push(stepPt[0], stepPt[1]);\n        break;\n\n      case 'middle':\n        var middle = (pt[baseIndex] + nextPt[baseIndex]) / 2;\n        var stepPt2 = [];\n        stepPt[baseIndex] = stepPt2[baseIndex] = middle;\n        stepPt[1 - baseIndex] = pt[1 - baseIndex];\n        stepPt2[1 - baseIndex] = nextPt[1 - baseIndex];\n        stepPoints.push(stepPt[0], stepPt[1]);\n        stepPoints.push(stepPt2[0], stepPt2[1]);\n        break;\n\n      default:\n        // default is start\n        stepPt[baseIndex] = pt[baseIndex];\n        stepPt[1 - baseIndex] = nextPt[1 - baseIndex];\n        stepPoints.push(stepPt[0], stepPt[1]);\n    }\n  } // Last points\n\n\n  stepPoints.push(points[i++], points[i++]);\n  return stepPoints;\n}\n/**\r\n * Clip color stops to edge. Avoid creating too large gradients.\r\n * Which may lead to blurry when GPU acceleration is enabled. See #15680\r\n *\r\n * The stops has been sorted from small to large.\r\n */\n\n\nfunction clipColorStops(colorStops, maxSize) {\n  var newColorStops = [];\n  var len = colorStops.length; // coord will always < 0 in prevOutOfRangeColorStop.\n\n  var prevOutOfRangeColorStop;\n  var prevInRangeColorStop;\n\n  function lerpStop(stop0, stop1, clippedCoord) {\n    var coord0 = stop0.coord;\n    var p = (clippedCoord - coord0) / (stop1.coord - coord0);\n    var color = lerp(p, [stop0.color, stop1.color]);\n    return {\n      coord: clippedCoord,\n      color: color\n    };\n  }\n\n  for (var i = 0; i < len; i++) {\n    var stop_1 = colorStops[i];\n    var coord = stop_1.coord;\n\n    if (coord < 0) {\n      prevOutOfRangeColorStop = stop_1;\n    } else if (coord > maxSize) {\n      if (prevInRangeColorStop) {\n        newColorStops.push(lerpStop(prevInRangeColorStop, stop_1, maxSize));\n      } else if (prevOutOfRangeColorStop) {\n        // If there are two stops and coord range is between these two stops\n        newColorStops.push(lerpStop(prevOutOfRangeColorStop, stop_1, 0), lerpStop(prevOutOfRangeColorStop, stop_1, maxSize));\n      } // All following stop will be out of range. So just ignore them.\n\n\n      break;\n    } else {\n      if (prevOutOfRangeColorStop) {\n        newColorStops.push(lerpStop(prevOutOfRangeColorStop, stop_1, 0)); // Reset\n\n        prevOutOfRangeColorStop = null;\n      }\n\n      newColorStops.push(stop_1);\n      prevInRangeColorStop = stop_1;\n    }\n  }\n\n  return newColorStops;\n}\n\nfunction getVisualGradient(data, coordSys, api) {\n  var visualMetaList = data.getVisual('visualMeta');\n\n  if (!visualMetaList || !visualMetaList.length || !data.count()) {\n    // When data.count() is 0, gradient range can not be calculated.\n    return;\n  }\n\n  if (coordSys.type !== 'cartesian2d') {\n    if (process.env.NODE_ENV !== 'production') {\n      console.warn('Visual map on line style is only supported on cartesian2d.');\n    }\n\n    return;\n  }\n\n  var coordDim;\n  var visualMeta;\n\n  for (var i = visualMetaList.length - 1; i >= 0; i--) {\n    var dimInfo = data.getDimensionInfo(visualMetaList[i].dimension);\n    coordDim = dimInfo && dimInfo.coordDim; // Can only be x or y\n\n    if (coordDim === 'x' || coordDim === 'y') {\n      visualMeta = visualMetaList[i];\n      break;\n    }\n  }\n\n  if (!visualMeta) {\n    if (process.env.NODE_ENV !== 'production') {\n      console.warn('Visual map on line style only support x or y dimension.');\n    }\n\n    return;\n  } // If the area to be rendered is bigger than area defined by LinearGradient,\n  // the canvas spec prescribes that the color of the first stop and the last\n  // stop should be used. But if two stops are added at offset 0, in effect\n  // browsers use the color of the second stop to render area outside\n  // LinearGradient. So we can only infinitesimally extend area defined in\n  // LinearGradient to render `outerColors`.\n\n\n  var axis = coordSys.getAxis(coordDim); // dataToCoord mapping may not be linear, but must be monotonic.\n\n  var colorStops = zrUtil.map(visualMeta.stops, function (stop) {\n    // offset will be calculated later.\n    return {\n      coord: axis.toGlobalCoord(axis.dataToCoord(stop.value)),\n      color: stop.color\n    };\n  });\n  var stopLen = colorStops.length;\n  var outerColors = visualMeta.outerColors.slice();\n\n  if (stopLen && colorStops[0].coord > colorStops[stopLen - 1].coord) {\n    colorStops.reverse();\n    outerColors.reverse();\n  }\n\n  var colorStopsInRange = clipColorStops(colorStops, coordDim === 'x' ? api.getWidth() : api.getHeight());\n  var inRangeStopLen = colorStopsInRange.length;\n\n  if (!inRangeStopLen && stopLen) {\n    // All stops are out of range. All will be the same color.\n    return colorStops[0].coord < 0 ? outerColors[1] ? outerColors[1] : colorStops[stopLen - 1].color : outerColors[0] ? outerColors[0] : colorStops[0].color;\n  }\n\n  var tinyExtent = 10; // Arbitrary value: 10px\n\n  var minCoord = colorStopsInRange[0].coord - tinyExtent;\n  var maxCoord = colorStopsInRange[inRangeStopLen - 1].coord + tinyExtent;\n  var coordSpan = maxCoord - minCoord;\n\n  if (coordSpan < 1e-3) {\n    return 'transparent';\n  }\n\n  zrUtil.each(colorStopsInRange, function (stop) {\n    stop.offset = (stop.coord - minCoord) / coordSpan;\n  });\n  colorStopsInRange.push({\n    // NOTE: inRangeStopLen may still be 0 if stoplen is zero.\n    offset: inRangeStopLen ? colorStopsInRange[inRangeStopLen - 1].offset : 0.5,\n    color: outerColors[1] || 'transparent'\n  });\n  colorStopsInRange.unshift({\n    offset: inRangeStopLen ? colorStopsInRange[0].offset : 0.5,\n    color: outerColors[0] || 'transparent'\n  });\n  var gradient = new graphic.LinearGradient(0, 0, 0, 0, colorStopsInRange, true);\n  gradient[coordDim] = minCoord;\n  gradient[coordDim + '2'] = maxCoord;\n  return gradient;\n}\n\nfunction getIsIgnoreFunc(seriesModel, data, coordSys) {\n  var showAllSymbol = seriesModel.get('showAllSymbol');\n  var isAuto = showAllSymbol === 'auto';\n\n  if (showAllSymbol && !isAuto) {\n    return;\n  }\n\n  var categoryAxis = coordSys.getAxesByScale('ordinal')[0];\n\n  if (!categoryAxis) {\n    return;\n  } // Note that category label interval strategy might bring some weird effect\n  // in some scenario: users may wonder why some of the symbols are not\n  // displayed. So we show all symbols as possible as we can.\n\n\n  if (isAuto // Simplify the logic, do not determine label overlap here.\n  && canShowAllSymbolForCategory(categoryAxis, data)) {\n    return;\n  } // Otherwise follow the label interval strategy on category axis.\n\n\n  var categoryDataDim = data.mapDimension(categoryAxis.dim);\n  var labelMap = {};\n  zrUtil.each(categoryAxis.getViewLabels(), function (labelItem) {\n    var ordinalNumber = categoryAxis.scale.getRawOrdinalNumber(labelItem.tickValue);\n    labelMap[ordinalNumber] = 1;\n  });\n  return function (dataIndex) {\n    return !labelMap.hasOwnProperty(data.get(categoryDataDim, dataIndex));\n  };\n}\n\nfunction canShowAllSymbolForCategory(categoryAxis, data) {\n  // In most cases, line is monotonous on category axis, and the label size\n  // is close with each other. So we check the symbol size and some of the\n  // label size alone with the category axis to estimate whether all symbol\n  // can be shown without overlap.\n  var axisExtent = categoryAxis.getExtent();\n  var availSize = Math.abs(axisExtent[1] - axisExtent[0]) / categoryAxis.scale.count();\n  isNaN(availSize) && (availSize = 0); // 0/0 is NaN.\n  // Sampling some points, max 5.\n\n  var dataLen = data.count();\n  var step = Math.max(1, Math.round(dataLen / 5));\n\n  for (var dataIndex = 0; dataIndex < dataLen; dataIndex += step) {\n    if (SymbolClz.getSymbolSize(data, dataIndex // Only for cartesian, where `isHorizontal` exists.\n    )[categoryAxis.isHorizontal() ? 1 : 0] // Empirical number\n    * 1.5 > availSize) {\n      return false;\n    }\n  }\n\n  return true;\n}\n\nfunction isPointNull(x, y) {\n  return isNaN(x) || isNaN(y);\n}\n\nfunction getLastIndexNotNull(points) {\n  var len = points.length / 2;\n\n  for (; len > 0; len--) {\n    if (!isPointNull(points[len * 2 - 2], points[len * 2 - 1])) {\n      break;\n    }\n  }\n\n  return len - 1;\n}\n\nfunction getPointAtIndex(points, idx) {\n  return [points[idx * 2], points[idx * 2 + 1]];\n}\n\nfunction getIndexRange(points, xOrY, dim) {\n  var len = points.length / 2;\n  var dimIdx = dim === 'x' ? 0 : 1;\n  var a;\n  var b;\n  var prevIndex = 0;\n  var nextIndex = -1;\n\n  for (var i = 0; i < len; i++) {\n    b = points[i * 2 + dimIdx];\n\n    if (isNaN(b) || isNaN(points[i * 2 + 1 - dimIdx])) {\n      continue;\n    }\n\n    if (i === 0) {\n      a = b;\n      continue;\n    }\n\n    if (a <= xOrY && b >= xOrY || a >= xOrY && b <= xOrY) {\n      nextIndex = i;\n      break;\n    }\n\n    prevIndex = i;\n    a = b;\n  }\n\n  return {\n    range: [prevIndex, nextIndex],\n    t: (xOrY - a) / (b - a)\n  };\n}\n\nfunction anyStateShowEndLabel(seriesModel) {\n  if (seriesModel.get(['endLabel', 'show'])) {\n    return true;\n  }\n\n  for (var i = 0; i < SPECIAL_STATES.length; i++) {\n    if (seriesModel.get([SPECIAL_STATES[i], 'endLabel', 'show'])) {\n      return true;\n    }\n  }\n\n  return false;\n}\n\nfunction createLineClipPath(lineView, coordSys, hasAnimation, seriesModel) {\n  if (isCoordinateSystemType(coordSys, 'cartesian2d')) {\n    var endLabelModel_1 = seriesModel.getModel('endLabel');\n    var valueAnimation_1 = endLabelModel_1.get('valueAnimation');\n    var data_1 = seriesModel.getData();\n    var labelAnimationRecord_1 = {\n      lastFrameIndex: 0\n    };\n    var during = anyStateShowEndLabel(seriesModel) ? function (percent, clipRect) {\n      lineView._endLabelOnDuring(percent, clipRect, data_1, labelAnimationRecord_1, valueAnimation_1, endLabelModel_1, coordSys);\n    } : null;\n    var isHorizontal = coordSys.getBaseAxis().isHorizontal();\n    var clipPath = createGridClipPath(coordSys, hasAnimation, seriesModel, function () {\n      var endLabel = lineView._endLabel;\n\n      if (endLabel && hasAnimation) {\n        if (labelAnimationRecord_1.originalX != null) {\n          endLabel.attr({\n            x: labelAnimationRecord_1.originalX,\n            y: labelAnimationRecord_1.originalY\n          });\n        }\n      }\n    }, during); // Expand clip shape to avoid clipping when line value exceeds axis\n\n    if (!seriesModel.get('clip', true)) {\n      var rectShape = clipPath.shape;\n      var expandSize = Math.max(rectShape.width, rectShape.height);\n\n      if (isHorizontal) {\n        rectShape.y -= expandSize;\n        rectShape.height += expandSize * 2;\n      } else {\n        rectShape.x -= expandSize;\n        rectShape.width += expandSize * 2;\n      }\n    } // Set to the final frame. To make sure label layout is right.\n\n\n    if (during) {\n      during(1, clipPath);\n    }\n\n    return clipPath;\n  } else {\n    if (process.env.NODE_ENV !== 'production') {\n      if (seriesModel.get(['endLabel', 'show'])) {\n        console.warn('endLabel is not supported for lines in polar systems.');\n      }\n    }\n\n    return createPolarClipPath(coordSys, hasAnimation, seriesModel);\n  }\n}\n\nfunction getEndLabelStateSpecified(endLabelModel, coordSys) {\n  var baseAxis = coordSys.getBaseAxis();\n  var isHorizontal = baseAxis.isHorizontal();\n  var isBaseInversed = baseAxis.inverse;\n  var align = isHorizontal ? isBaseInversed ? 'right' : 'left' : 'center';\n  var verticalAlign = isHorizontal ? 'middle' : isBaseInversed ? 'top' : 'bottom';\n  return {\n    normal: {\n      align: endLabelModel.get('align') || align,\n      verticalAlign: endLabelModel.get('verticalAlign') || verticalAlign\n    }\n  };\n}\n\nvar LineView =\n/** @class */\nfunction (_super) {\n  __extends(LineView, _super);\n\n  function LineView() {\n    return _super !== null && _super.apply(this, arguments) || this;\n  }\n\n  LineView.prototype.init = function () {\n    var lineGroup = new graphic.Group();\n    var symbolDraw = new SymbolDraw();\n    this.group.add(symbolDraw.group);\n    this._symbolDraw = symbolDraw;\n    this._lineGroup = lineGroup;\n  };\n\n  LineView.prototype.render = function (seriesModel, ecModel, api) {\n    var _this = this;\n\n    var coordSys = seriesModel.coordinateSystem;\n    var group = this.group;\n    var data = seriesModel.getData();\n    var lineStyleModel = seriesModel.getModel('lineStyle');\n    var areaStyleModel = seriesModel.getModel('areaStyle');\n    var points = data.getLayout('points') || [];\n    var isCoordSysPolar = coordSys.type === 'polar';\n    var prevCoordSys = this._coordSys;\n    var symbolDraw = this._symbolDraw;\n    var polyline = this._polyline;\n    var polygon = this._polygon;\n    var lineGroup = this._lineGroup;\n    var hasAnimation = !ecModel.ssr && seriesModel.isAnimationEnabled();\n    var isAreaChart = !areaStyleModel.isEmpty();\n    var valueOrigin = areaStyleModel.get('origin');\n    var dataCoordInfo = prepareDataCoordInfo(coordSys, data, valueOrigin);\n    var stackedOnPoints = isAreaChart && getStackedOnPoints(coordSys, data, dataCoordInfo);\n    var showSymbol = seriesModel.get('showSymbol');\n    var connectNulls = seriesModel.get('connectNulls');\n    var isIgnoreFunc = showSymbol && !isCoordSysPolar && getIsIgnoreFunc(seriesModel, data, coordSys); // Remove temporary symbols\n\n    var oldData = this._data;\n    oldData && oldData.eachItemGraphicEl(function (el, idx) {\n      if (el.__temp) {\n        group.remove(el);\n        oldData.setItemGraphicEl(idx, null);\n      }\n    }); // Remove previous created symbols if showSymbol changed to false\n\n    if (!showSymbol) {\n      symbolDraw.remove();\n    }\n\n    group.add(lineGroup); // FIXME step not support polar\n\n    var step = !isCoordSysPolar ? seriesModel.get('step') : false;\n    var clipShapeForSymbol;\n\n    if (coordSys && coordSys.getArea && seriesModel.get('clip', true)) {\n      clipShapeForSymbol = coordSys.getArea(); // Avoid float number rounding error for symbol on the edge of axis extent.\n      // See #7913 and `test/dataZoom-clip.html`.\n\n      if (clipShapeForSymbol.width != null) {\n        clipShapeForSymbol.x -= 0.1;\n        clipShapeForSymbol.y -= 0.1;\n        clipShapeForSymbol.width += 0.2;\n        clipShapeForSymbol.height += 0.2;\n      } else if (clipShapeForSymbol.r0) {\n        clipShapeForSymbol.r0 -= 0.5;\n        clipShapeForSymbol.r += 0.5;\n      }\n    }\n\n    this._clipShapeForSymbol = clipShapeForSymbol;\n    var visualColor = getVisualGradient(data, coordSys, api) || data.getVisual('style')[data.getVisual('drawType')]; // Initialization animation or coordinate system changed\n\n    if (!(polyline && prevCoordSys.type === coordSys.type && step === this._step)) {\n      showSymbol && symbolDraw.updateData(data, {\n        isIgnore: isIgnoreFunc,\n        clipShape: clipShapeForSymbol,\n        disableAnimation: true,\n        getSymbolPoint: function (idx) {\n          return [points[idx * 2], points[idx * 2 + 1]];\n        }\n      });\n      hasAnimation && this._initSymbolLabelAnimation(data, coordSys, clipShapeForSymbol);\n\n      if (step) {\n        // TODO If stacked series is not step\n        points = turnPointsIntoStep(points, coordSys, step, connectNulls);\n\n        if (stackedOnPoints) {\n          stackedOnPoints = turnPointsIntoStep(stackedOnPoints, coordSys, step, connectNulls);\n        }\n      }\n\n      polyline = this._newPolyline(points);\n\n      if (isAreaChart) {\n        polygon = this._newPolygon(points, stackedOnPoints);\n      } // If areaStyle is removed\n      else if (polygon) {\n          lineGroup.remove(polygon);\n          polygon = this._polygon = null;\n        } // NOTE: Must update _endLabel before setClipPath.\n\n\n      if (!isCoordSysPolar) {\n        this._initOrUpdateEndLabel(seriesModel, coordSys, convertToColorString(visualColor));\n      }\n\n      lineGroup.setClipPath(createLineClipPath(this, coordSys, true, seriesModel));\n    } else {\n      if (isAreaChart && !polygon) {\n        // If areaStyle is added\n        polygon = this._newPolygon(points, stackedOnPoints);\n      } else if (polygon && !isAreaChart) {\n        // If areaStyle is removed\n        lineGroup.remove(polygon);\n        polygon = this._polygon = null;\n      } // NOTE: Must update _endLabel before setClipPath.\n\n\n      if (!isCoordSysPolar) {\n        this._initOrUpdateEndLabel(seriesModel, coordSys, convertToColorString(visualColor));\n      } // Update clipPath\n\n\n      var oldClipPath = lineGroup.getClipPath();\n\n      if (oldClipPath) {\n        var newClipPath = createLineClipPath(this, coordSys, false, seriesModel);\n        graphic.initProps(oldClipPath, {\n          shape: newClipPath.shape\n        }, seriesModel);\n      } else {\n        lineGroup.setClipPath(createLineClipPath(this, coordSys, true, seriesModel));\n      } // Always update, or it is wrong in the case turning on legend\n      // because points are not changed.\n\n\n      showSymbol && symbolDraw.updateData(data, {\n        isIgnore: isIgnoreFunc,\n        clipShape: clipShapeForSymbol,\n        disableAnimation: true,\n        getSymbolPoint: function (idx) {\n          return [points[idx * 2], points[idx * 2 + 1]];\n        }\n      }); // In the case data zoom triggered refreshing frequently\n      // Data may not change if line has a category axis. So it should animate nothing.\n\n      if (!isPointsSame(this._stackedOnPoints, stackedOnPoints) || !isPointsSame(this._points, points)) {\n        if (hasAnimation) {\n          this._doUpdateAnimation(data, stackedOnPoints, coordSys, api, step, valueOrigin, connectNulls);\n        } else {\n          // Not do it in update with animation\n          if (step) {\n            // TODO If stacked series is not step\n            points = turnPointsIntoStep(points, coordSys, step, connectNulls);\n\n            if (stackedOnPoints) {\n              stackedOnPoints = turnPointsIntoStep(stackedOnPoints, coordSys, step, connectNulls);\n            }\n          }\n\n          polyline.setShape({\n            points: points\n          });\n          polygon && polygon.setShape({\n            points: points,\n            stackedOnPoints: stackedOnPoints\n          });\n        }\n      }\n    }\n\n    var emphasisModel = seriesModel.getModel('emphasis');\n    var focus = emphasisModel.get('focus');\n    var blurScope = emphasisModel.get('blurScope');\n    var emphasisDisabled = emphasisModel.get('disabled');\n    polyline.useStyle(zrUtil.defaults( // Use color in lineStyle first\n    lineStyleModel.getLineStyle(), {\n      fill: 'none',\n      stroke: visualColor,\n      lineJoin: 'bevel'\n    }));\n    setStatesStylesFromModel(polyline, seriesModel, 'lineStyle');\n\n    if (polyline.style.lineWidth > 0 && seriesModel.get(['emphasis', 'lineStyle', 'width']) === 'bolder') {\n      var emphasisLineStyle = polyline.getState('emphasis').style;\n      emphasisLineStyle.lineWidth = +polyline.style.lineWidth + 1;\n    } // Needs seriesIndex for focus\n\n\n    getECData(polyline).seriesIndex = seriesModel.seriesIndex;\n    toggleHoverEmphasis(polyline, focus, blurScope, emphasisDisabled);\n    var smooth = getSmooth(seriesModel.get('smooth'));\n    var smoothMonotone = seriesModel.get('smoothMonotone');\n    polyline.setShape({\n      smooth: smooth,\n      smoothMonotone: smoothMonotone,\n      connectNulls: connectNulls\n    });\n\n    if (polygon) {\n      var stackedOnSeries = data.getCalculationInfo('stackedOnSeries');\n      var stackedOnSmooth = 0;\n      polygon.useStyle(zrUtil.defaults(areaStyleModel.getAreaStyle(), {\n        fill: visualColor,\n        opacity: 0.7,\n        lineJoin: 'bevel',\n        decal: data.getVisual('style').decal\n      }));\n\n      if (stackedOnSeries) {\n        stackedOnSmooth = getSmooth(stackedOnSeries.get('smooth'));\n      }\n\n      polygon.setShape({\n        smooth: smooth,\n        stackedOnSmooth: stackedOnSmooth,\n        smoothMonotone: smoothMonotone,\n        connectNulls: connectNulls\n      });\n      setStatesStylesFromModel(polygon, seriesModel, 'areaStyle'); // Needs seriesIndex for focus\n\n      getECData(polygon).seriesIndex = seriesModel.seriesIndex;\n      toggleHoverEmphasis(polygon, focus, blurScope, emphasisDisabled);\n    }\n\n    var changePolyState = function (toState) {\n      _this._changePolyState(toState);\n    };\n\n    data.eachItemGraphicEl(function (el) {\n      // Switch polyline / polygon state if element changed its state.\n      el && (el.onHoverStateChange = changePolyState);\n    });\n    this._polyline.onHoverStateChange = changePolyState;\n    this._data = data; // Save the coordinate system for transition animation when data changed\n\n    this._coordSys = coordSys;\n    this._stackedOnPoints = stackedOnPoints;\n    this._points = points;\n    this._step = step;\n    this._valueOrigin = valueOrigin;\n\n    if (seriesModel.get('triggerLineEvent')) {\n      this.packEventData(seriesModel, polyline);\n      polygon && this.packEventData(seriesModel, polygon);\n    }\n  };\n\n  LineView.prototype.packEventData = function (seriesModel, el) {\n    getECData(el).eventData = {\n      componentType: 'series',\n      componentSubType: 'line',\n      componentIndex: seriesModel.componentIndex,\n      seriesIndex: seriesModel.seriesIndex,\n      seriesName: seriesModel.name,\n      seriesType: 'line'\n    };\n  };\n\n  LineView.prototype.highlight = function (seriesModel, ecModel, api, payload) {\n    var data = seriesModel.getData();\n    var dataIndex = modelUtil.queryDataIndex(data, payload);\n\n    this._changePolyState('emphasis');\n\n    if (!(dataIndex instanceof Array) && dataIndex != null && dataIndex >= 0) {\n      var points = data.getLayout('points');\n      var symbol = data.getItemGraphicEl(dataIndex);\n\n      if (!symbol) {\n        // Create a temporary symbol if it is not exists\n        var x = points[dataIndex * 2];\n        var y = points[dataIndex * 2 + 1];\n\n        if (isNaN(x) || isNaN(y)) {\n          // Null data\n          return;\n        } // fix #11360: shouldn't draw symbol outside clipShapeForSymbol\n\n\n        if (this._clipShapeForSymbol && !this._clipShapeForSymbol.contain(x, y)) {\n          return;\n        }\n\n        var zlevel = seriesModel.get('zlevel') || 0;\n        var z = seriesModel.get('z') || 0;\n        symbol = new SymbolClz(data, dataIndex);\n        symbol.x = x;\n        symbol.y = y;\n        symbol.setZ(zlevel, z); // ensure label text of the temporary symbol is in front of line and area polygon\n\n        var symbolLabel = symbol.getSymbolPath().getTextContent();\n\n        if (symbolLabel) {\n          symbolLabel.zlevel = zlevel;\n          symbolLabel.z = z;\n          symbolLabel.z2 = this._polyline.z2 + 1;\n        }\n\n        symbol.__temp = true;\n        data.setItemGraphicEl(dataIndex, symbol); // Stop scale animation\n\n        symbol.stopSymbolAnimation(true);\n        this.group.add(symbol);\n      }\n\n      symbol.highlight();\n    } else {\n      // Highlight whole series\n      ChartView.prototype.highlight.call(this, seriesModel, ecModel, api, payload);\n    }\n  };\n\n  LineView.prototype.downplay = function (seriesModel, ecModel, api, payload) {\n    var data = seriesModel.getData();\n    var dataIndex = modelUtil.queryDataIndex(data, payload);\n\n    this._changePolyState('normal');\n\n    if (dataIndex != null && dataIndex >= 0) {\n      var symbol = data.getItemGraphicEl(dataIndex);\n\n      if (symbol) {\n        if (symbol.__temp) {\n          data.setItemGraphicEl(dataIndex, null);\n          this.group.remove(symbol);\n        } else {\n          symbol.downplay();\n        }\n      }\n    } else {\n      // FIXME\n      // can not downplay completely.\n      // Downplay whole series\n      ChartView.prototype.downplay.call(this, seriesModel, ecModel, api, payload);\n    }\n  };\n\n  LineView.prototype._changePolyState = function (toState) {\n    var polygon = this._polygon;\n    setStatesFlag(this._polyline, toState);\n    polygon && setStatesFlag(polygon, toState);\n  };\n\n  LineView.prototype._newPolyline = function (points) {\n    var polyline = this._polyline; // Remove previous created polyline\n\n    if (polyline) {\n      this._lineGroup.remove(polyline);\n    }\n\n    polyline = new ECPolyline({\n      shape: {\n        points: points\n      },\n      segmentIgnoreThreshold: 2,\n      z2: 10\n    });\n\n    this._lineGroup.add(polyline);\n\n    this._polyline = polyline;\n    return polyline;\n  };\n\n  LineView.prototype._newPolygon = function (points, stackedOnPoints) {\n    var polygon = this._polygon; // Remove previous created polygon\n\n    if (polygon) {\n      this._lineGroup.remove(polygon);\n    }\n\n    polygon = new ECPolygon({\n      shape: {\n        points: points,\n        stackedOnPoints: stackedOnPoints\n      },\n      segmentIgnoreThreshold: 2\n    });\n\n    this._lineGroup.add(polygon);\n\n    this._polygon = polygon;\n    return polygon;\n  };\n\n  LineView.prototype._initSymbolLabelAnimation = function (data, coordSys, clipShape) {\n    var isHorizontalOrRadial;\n    var isCoordSysPolar;\n    var baseAxis = coordSys.getBaseAxis();\n    var isAxisInverse = baseAxis.inverse;\n\n    if (coordSys.type === 'cartesian2d') {\n      isHorizontalOrRadial = baseAxis.isHorizontal();\n      isCoordSysPolar = false;\n    } else if (coordSys.type === 'polar') {\n      isHorizontalOrRadial = baseAxis.dim === 'angle';\n      isCoordSysPolar = true;\n    }\n\n    var seriesModel = data.hostModel;\n    var seriesDuration = seriesModel.get('animationDuration');\n\n    if (zrUtil.isFunction(seriesDuration)) {\n      seriesDuration = seriesDuration(null);\n    }\n\n    var seriesDelay = seriesModel.get('animationDelay') || 0;\n    var seriesDelayValue = zrUtil.isFunction(seriesDelay) ? seriesDelay(null) : seriesDelay;\n    data.eachItemGraphicEl(function (symbol, idx) {\n      var el = symbol;\n\n      if (el) {\n        var point = [symbol.x, symbol.y];\n        var start = void 0;\n        var end = void 0;\n        var current = void 0;\n\n        if (clipShape) {\n          if (isCoordSysPolar) {\n            var polarClip = clipShape;\n            var coord = coordSys.pointToCoord(point);\n\n            if (isHorizontalOrRadial) {\n              start = polarClip.startAngle;\n              end = polarClip.endAngle;\n              current = -coord[1] / 180 * Math.PI;\n            } else {\n              start = polarClip.r0;\n              end = polarClip.r;\n              current = coord[0];\n            }\n          } else {\n            var gridClip = clipShape;\n\n            if (isHorizontalOrRadial) {\n              start = gridClip.x;\n              end = gridClip.x + gridClip.width;\n              current = symbol.x;\n            } else {\n              start = gridClip.y + gridClip.height;\n              end = gridClip.y;\n              current = symbol.y;\n            }\n          }\n        }\n\n        var ratio = end === start ? 0 : (current - start) / (end - start);\n\n        if (isAxisInverse) {\n          ratio = 1 - ratio;\n        }\n\n        var delay = zrUtil.isFunction(seriesDelay) ? seriesDelay(idx) : seriesDuration * ratio + seriesDelayValue;\n        var symbolPath = el.getSymbolPath();\n        var text = symbolPath.getTextContent();\n        el.attr({\n          scaleX: 0,\n          scaleY: 0\n        });\n        el.animateTo({\n          scaleX: 1,\n          scaleY: 1\n        }, {\n          duration: 200,\n          setToFinal: true,\n          delay: delay\n        });\n\n        if (text) {\n          text.animateFrom({\n            style: {\n              opacity: 0\n            }\n          }, {\n            duration: 300,\n            delay: delay\n          });\n        }\n\n        symbolPath.disableLabelAnimation = true;\n      }\n    });\n  };\n\n  LineView.prototype._initOrUpdateEndLabel = function (seriesModel, coordSys, inheritColor) {\n    var endLabelModel = seriesModel.getModel('endLabel');\n\n    if (anyStateShowEndLabel(seriesModel)) {\n      var data_2 = seriesModel.getData();\n      var polyline = this._polyline; // series may be filtered.\n\n      var points = data_2.getLayout('points');\n\n      if (!points) {\n        polyline.removeTextContent();\n        this._endLabel = null;\n        return;\n      }\n\n      var endLabel = this._endLabel;\n\n      if (!endLabel) {\n        endLabel = this._endLabel = new graphic.Text({\n          z2: 200 // should be higher than item symbol\n\n        });\n        endLabel.ignoreClip = true;\n        polyline.setTextContent(this._endLabel);\n        polyline.disableLabelAnimation = true;\n      } // Find last non-NaN data to display data\n\n\n      var dataIndex = getLastIndexNotNull(points);\n\n      if (dataIndex >= 0) {\n        setLabelStyle(polyline, getLabelStatesModels(seriesModel, 'endLabel'), {\n          inheritColor: inheritColor,\n          labelFetcher: seriesModel,\n          labelDataIndex: dataIndex,\n          defaultText: function (dataIndex, opt, interpolatedValue) {\n            return interpolatedValue != null ? getDefaultInterpolatedLabel(data_2, interpolatedValue) : getDefaultLabel(data_2, dataIndex);\n          },\n          enableTextSetter: true\n        }, getEndLabelStateSpecified(endLabelModel, coordSys));\n        polyline.textConfig.position = null;\n      }\n    } else if (this._endLabel) {\n      this._polyline.removeTextContent();\n\n      this._endLabel = null;\n    }\n  };\n\n  LineView.prototype._endLabelOnDuring = function (percent, clipRect, data, animationRecord, valueAnimation, endLabelModel, coordSys) {\n    var endLabel = this._endLabel;\n    var polyline = this._polyline;\n\n    if (endLabel) {\n      // NOTE: Don't remove percent < 1. percent === 1 means the first frame during render.\n      // The label is not prepared at this time.\n      if (percent < 1 && animationRecord.originalX == null) {\n        animationRecord.originalX = endLabel.x;\n        animationRecord.originalY = endLabel.y;\n      }\n\n      var points = data.getLayout('points');\n      var seriesModel = data.hostModel;\n      var connectNulls = seriesModel.get('connectNulls');\n      var precision = endLabelModel.get('precision');\n      var distance = endLabelModel.get('distance') || 0;\n      var baseAxis = coordSys.getBaseAxis();\n      var isHorizontal = baseAxis.isHorizontal();\n      var isBaseInversed = baseAxis.inverse;\n      var clipShape = clipRect.shape;\n      var xOrY = isBaseInversed ? isHorizontal ? clipShape.x : clipShape.y + clipShape.height : isHorizontal ? clipShape.x + clipShape.width : clipShape.y;\n      var distanceX = (isHorizontal ? distance : 0) * (isBaseInversed ? -1 : 1);\n      var distanceY = (isHorizontal ? 0 : -distance) * (isBaseInversed ? -1 : 1);\n      var dim = isHorizontal ? 'x' : 'y';\n      var dataIndexRange = getIndexRange(points, xOrY, dim);\n      var indices = dataIndexRange.range;\n      var diff = indices[1] - indices[0];\n      var value = void 0;\n\n      if (diff >= 1) {\n        // diff > 1 && connectNulls, which is on the null data.\n        if (diff > 1 && !connectNulls) {\n          var pt = getPointAtIndex(points, indices[0]);\n          endLabel.attr({\n            x: pt[0] + distanceX,\n            y: pt[1] + distanceY\n          });\n          valueAnimation && (value = seriesModel.getRawValue(indices[0]));\n        } else {\n          var pt = polyline.getPointOn(xOrY, dim);\n          pt && endLabel.attr({\n            x: pt[0] + distanceX,\n            y: pt[1] + distanceY\n          });\n          var startValue = seriesModel.getRawValue(indices[0]);\n          var endValue = seriesModel.getRawValue(indices[1]);\n          valueAnimation && (value = modelUtil.interpolateRawValues(data, precision, startValue, endValue, dataIndexRange.t));\n        }\n\n        animationRecord.lastFrameIndex = indices[0];\n      } else {\n        // If diff <= 0, which is the range is not found(Include NaN)\n        // Choose the first point or last point.\n        var idx = percent === 1 || animationRecord.lastFrameIndex > 0 ? indices[0] : 0;\n        var pt = getPointAtIndex(points, idx);\n        valueAnimation && (value = seriesModel.getRawValue(idx));\n        endLabel.attr({\n          x: pt[0] + distanceX,\n          y: pt[1] + distanceY\n        });\n      }\n\n      if (valueAnimation) {\n        var inner = labelInner(endLabel);\n\n        if (typeof inner.setLabelText === 'function') {\n          inner.setLabelText(value);\n        }\n      }\n    }\n  };\n  /**\r\n   * @private\r\n   */\n  // FIXME Two value axis\n\n\n  LineView.prototype._doUpdateAnimation = function (data, stackedOnPoints, coordSys, api, step, valueOrigin, connectNulls) {\n    var polyline = this._polyline;\n    var polygon = this._polygon;\n    var seriesModel = data.hostModel;\n    var diff = lineAnimationDiff(this._data, data, this._stackedOnPoints, stackedOnPoints, this._coordSys, coordSys, this._valueOrigin, valueOrigin);\n    var current = diff.current;\n    var stackedOnCurrent = diff.stackedOnCurrent;\n    var next = diff.next;\n    var stackedOnNext = diff.stackedOnNext;\n\n    if (step) {\n      // TODO If stacked series is not step\n      current = turnPointsIntoStep(diff.current, coordSys, step, connectNulls);\n      stackedOnCurrent = turnPointsIntoStep(diff.stackedOnCurrent, coordSys, step, connectNulls);\n      next = turnPointsIntoStep(diff.next, coordSys, step, connectNulls);\n      stackedOnNext = turnPointsIntoStep(diff.stackedOnNext, coordSys, step, connectNulls);\n    } // Don't apply animation if diff is large.\n    // For better result and avoid memory explosion problems like\n    // https://github.com/apache/incubator-echarts/issues/12229\n\n\n    if (getBoundingDiff(current, next) > 3000 || polygon && getBoundingDiff(stackedOnCurrent, stackedOnNext) > 3000) {\n      polyline.stopAnimation();\n      polyline.setShape({\n        points: next\n      });\n\n      if (polygon) {\n        polygon.stopAnimation();\n        polygon.setShape({\n          points: next,\n          stackedOnPoints: stackedOnNext\n        });\n      }\n\n      return;\n    }\n\n    polyline.shape.__points = diff.current;\n    polyline.shape.points = current;\n    var target = {\n      shape: {\n        points: next\n      }\n    }; // Also animate the original points.\n    // If points reference is changed when turning into step line.\n\n    if (diff.current !== current) {\n      target.shape.__points = diff.next;\n    } // Stop previous animation.\n\n\n    polyline.stopAnimation();\n    graphic.updateProps(polyline, target, seriesModel);\n\n    if (polygon) {\n      polygon.setShape({\n        // Reuse the points with polyline.\n        points: current,\n        stackedOnPoints: stackedOnCurrent\n      });\n      polygon.stopAnimation();\n      graphic.updateProps(polygon, {\n        shape: {\n          stackedOnPoints: stackedOnNext\n        }\n      }, seriesModel); // If use attr directly in updateProps.\n\n      if (polyline.shape.points !== polygon.shape.points) {\n        polygon.shape.points = polyline.shape.points;\n      }\n    }\n\n    var updatedDataInfo = [];\n    var diffStatus = diff.status;\n\n    for (var i = 0; i < diffStatus.length; i++) {\n      var cmd = diffStatus[i].cmd;\n\n      if (cmd === '=') {\n        var el = data.getItemGraphicEl(diffStatus[i].idx1);\n\n        if (el) {\n          updatedDataInfo.push({\n            el: el,\n            ptIdx: i // Index of points\n\n          });\n        }\n      }\n    }\n\n    if (polyline.animators && polyline.animators.length) {\n      polyline.animators[0].during(function () {\n        polygon && polygon.dirtyShape();\n        var points = polyline.shape.__points;\n\n        for (var i = 0; i < updatedDataInfo.length; i++) {\n          var el = updatedDataInfo[i].el;\n          var offset = updatedDataInfo[i].ptIdx * 2;\n          el.x = points[offset];\n          el.y = points[offset + 1];\n          el.markRedraw();\n        }\n      });\n    }\n  };\n\n  LineView.prototype.remove = function (ecModel) {\n    var group = this.group;\n    var oldData = this._data;\n\n    this._lineGroup.removeAll();\n\n    this._symbolDraw.remove(true); // Remove temporary created elements when highlighting\n\n\n    oldData && oldData.eachItemGraphicEl(function (el, idx) {\n      if (el.__temp) {\n        group.remove(el);\n        oldData.setItemGraphicEl(idx, null);\n      }\n    });\n    this._polyline = this._polygon = this._coordSys = this._points = this._stackedOnPoints = this._endLabel = this._data = null;\n  };\n\n  LineView.type = 'line';\n  return LineView;\n}(ChartView);\n\nexport default LineView;"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,SAAS,QAAQ,OAAO,CAAC,CAAC;;AAEnC,OAAO,KAAKC,MAAM,MAAM,0BAA0B;AAClD,OAAOC,UAAU,MAAM,yBAAyB;AAChD,OAAOC,SAAS,MAAM,qBAAqB;AAC3C,OAAOC,iBAAiB,MAAM,wBAAwB;AACtD,OAAO,KAAKC,OAAO,MAAM,uBAAuB;AAChD,OAAO,KAAKC,SAAS,MAAM,qBAAqB;AAChD,SAASC,UAAU,EAAEC,SAAS,QAAQ,WAAW;AACjD,OAAOC,SAAS,MAAM,qBAAqB;AAC3C,SAASC,oBAAoB,EAAEC,iBAAiB,QAAQ,aAAa;AACrE,SAASC,kBAAkB,EAAEC,mBAAmB,QAAQ,yCAAyC;AACjG,SAASC,sBAAsB,QAAQ,iCAAiC;AACxE,SAASC,wBAAwB,EAAEC,aAAa,EAAEC,mBAAmB,EAAEC,cAAc,QAAQ,sBAAsB;AACnH,SAASC,aAAa,EAAEC,oBAAoB,EAAEC,UAAU,QAAQ,2BAA2B;AAC3F,SAASC,eAAe,EAAEC,2BAA2B,QAAQ,0BAA0B;AACvF,SAASC,SAAS,QAAQ,0BAA0B;AACpD,SAASC,kBAAkB,QAAQ,sBAAsB;AACzD,SAASC,oBAAoB,QAAQ,sBAAsB;AAC3D,SAASC,IAAI,QAAQ,2BAA2B;AAEhD,SAASC,YAAYA,CAACC,OAAO,EAAEC,OAAO,EAAE;EACtC,IAAID,OAAO,CAACE,MAAM,KAAKD,OAAO,CAACC,MAAM,EAAE;IACrC;EACF;EAEA,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,OAAO,CAACE,MAAM,EAAEC,CAAC,EAAE,EAAE;IACvC,IAAIH,OAAO,CAACG,CAAC,CAAC,KAAKF,OAAO,CAACE,CAAC,CAAC,EAAE;MAC7B;IACF;EACF;EAEA,OAAO,IAAI;AACb;AAEA,SAASC,cAAcA,CAACC,MAAM,EAAE;EAC9B,IAAIC,IAAI,GAAGC,QAAQ;EACnB,IAAIC,IAAI,GAAGD,QAAQ;EACnB,IAAIE,IAAI,GAAG,CAACF,QAAQ;EACpB,IAAIG,IAAI,GAAG,CAACH,QAAQ;EAEpB,KAAK,IAAIJ,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGE,MAAM,CAACH,MAAM,GAAG;IAClC,IAAIS,CAAC,GAAGN,MAAM,CAACF,CAAC,EAAE,CAAC;IACnB,IAAIS,CAAC,GAAGP,MAAM,CAACF,CAAC,EAAE,CAAC;IAEnB,IAAI,CAACU,KAAK,CAACF,CAAC,CAAC,EAAE;MACbL,IAAI,GAAGQ,IAAI,CAACC,GAAG,CAACJ,CAAC,EAAEL,IAAI,CAAC;MACxBG,IAAI,GAAGK,IAAI,CAACE,GAAG,CAACL,CAAC,EAAEF,IAAI,CAAC;IAC1B;IAEA,IAAI,CAACI,KAAK,CAACD,CAAC,CAAC,EAAE;MACbJ,IAAI,GAAGM,IAAI,CAACC,GAAG,CAACH,CAAC,EAAEJ,IAAI,CAAC;MACxBE,IAAI,GAAGI,IAAI,CAACE,GAAG,CAACJ,CAAC,EAAEF,IAAI,CAAC;IAC1B;EACF;EAEA,OAAO,CAAC,CAACJ,IAAI,EAAEE,IAAI,CAAC,EAAE,CAACC,IAAI,EAAEC,IAAI,CAAC,CAAC;AACrC;AAEA,SAASO,eAAeA,CAACjB,OAAO,EAAEC,OAAO,EAAE;EACzC,IAAIiB,EAAE,GAAGd,cAAc,CAACJ,OAAO,CAAC;IAC5BmB,IAAI,GAAGD,EAAE,CAAC,CAAC,CAAC;IACZE,IAAI,GAAGF,EAAE,CAAC,CAAC,CAAC;EAEhB,IAAIG,EAAE,GAAGjB,cAAc,CAACH,OAAO,CAAC;IAC5BqB,IAAI,GAAGD,EAAE,CAAC,CAAC,CAAC;IACZE,IAAI,GAAGF,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;;EAGlB,OAAOP,IAAI,CAACE,GAAG,CAACF,IAAI,CAACU,GAAG,CAACL,IAAI,CAAC,CAAC,CAAC,GAAGG,IAAI,CAAC,CAAC,CAAC,CAAC,EAAER,IAAI,CAACU,GAAG,CAACL,IAAI,CAAC,CAAC,CAAC,GAAGG,IAAI,CAAC,CAAC,CAAC,CAAC,EAAER,IAAI,CAACU,GAAG,CAACJ,IAAI,CAAC,CAAC,CAAC,GAAGG,IAAI,CAAC,CAAC,CAAC,CAAC,EAAET,IAAI,CAACU,GAAG,CAACJ,IAAI,CAAC,CAAC,CAAC,GAAGG,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;AACrI;AAEA,SAASE,SAASA,CAACC,MAAM,EAAE;EACzB,OAAOtD,MAAM,CAACuD,QAAQ,CAACD,MAAM,CAAC,GAAGA,MAAM,GAAGA,MAAM,GAAG,GAAG,GAAG,CAAC;AAC5D;AAEA,SAASE,kBAAkBA,CAACC,QAAQ,EAAEC,IAAI,EAAEC,aAAa,EAAE;EACzD,IAAI,CAACA,aAAa,CAACC,QAAQ,EAAE;IAC3B,OAAO,EAAE;EACX;EAEA,IAAIC,GAAG,GAAGH,IAAI,CAACI,KAAK,CAAC,CAAC;EACtB,IAAI7B,MAAM,GAAGT,kBAAkB,CAACqC,GAAG,GAAG,CAAC,CAAC;EAExC,KAAK,IAAIE,GAAG,GAAG,CAAC,EAAEA,GAAG,GAAGF,GAAG,EAAEE,GAAG,EAAE,EAAE;IAClC,IAAIC,EAAE,GAAGtD,iBAAiB,CAACiD,aAAa,EAAEF,QAAQ,EAAEC,IAAI,EAAEK,GAAG,CAAC;IAC9D9B,MAAM,CAAC8B,GAAG,GAAG,CAAC,CAAC,GAAGC,EAAE,CAAC,CAAC,CAAC;IACvB/B,MAAM,CAAC8B,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC,GAAGC,EAAE,CAAC,CAAC,CAAC;EAC7B;EAEA,OAAO/B,MAAM;AACf;AAEA,SAASgC,kBAAkBA,CAAChC,MAAM,EAAEwB,QAAQ,EAAES,UAAU,EAAEC,YAAY,EAAE;EACtE,IAAIC,QAAQ,GAAGX,QAAQ,CAACY,WAAW,CAAC,CAAC;EACrC,IAAIC,SAAS,GAAGF,QAAQ,CAACG,GAAG,KAAK,GAAG,IAAIH,QAAQ,CAACG,GAAG,KAAK,QAAQ,GAAG,CAAC,GAAG,CAAC;EACzE,IAAIC,UAAU,GAAG,EAAE;EACnB,IAAIzC,CAAC,GAAG,CAAC;EACT,IAAI0C,MAAM,GAAG,EAAE;EACf,IAAIT,EAAE,GAAG,EAAE;EACX,IAAIU,MAAM,GAAG,EAAE;EACf,IAAIC,cAAc,GAAG,EAAE;EAEvB,IAAIR,YAAY,EAAE;IAChB,KAAKpC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGE,MAAM,CAACH,MAAM,EAAEC,CAAC,IAAI,CAAC,EAAE;MACrC,IAAI,CAACU,KAAK,CAACR,MAAM,CAACF,CAAC,CAAC,CAAC,IAAI,CAACU,KAAK,CAACR,MAAM,CAACF,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE;QAC9C4C,cAAc,CAACC,IAAI,CAAC3C,MAAM,CAACF,CAAC,CAAC,EAAEE,MAAM,CAACF,CAAC,GAAG,CAAC,CAAC,CAAC;MAC/C;IACF;IAEAE,MAAM,GAAG0C,cAAc;EACzB;EAEA,KAAK5C,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGE,MAAM,CAACH,MAAM,GAAG,CAAC,EAAEC,CAAC,IAAI,CAAC,EAAE;IACzC2C,MAAM,CAAC,CAAC,CAAC,GAAGzC,MAAM,CAACF,CAAC,GAAG,CAAC,CAAC;IACzB2C,MAAM,CAAC,CAAC,CAAC,GAAGzC,MAAM,CAACF,CAAC,GAAG,CAAC,CAAC;IACzBiC,EAAE,CAAC,CAAC,CAAC,GAAG/B,MAAM,CAACF,CAAC,CAAC;IACjBiC,EAAE,CAAC,CAAC,CAAC,GAAG/B,MAAM,CAACF,CAAC,GAAG,CAAC,CAAC;IACrByC,UAAU,CAACI,IAAI,CAACZ,EAAE,CAAC,CAAC,CAAC,EAAEA,EAAE,CAAC,CAAC,CAAC,CAAC;IAE7B,QAAQE,UAAU;MAChB,KAAK,KAAK;QACRO,MAAM,CAACH,SAAS,CAAC,GAAGI,MAAM,CAACJ,SAAS,CAAC;QACrCG,MAAM,CAAC,CAAC,GAAGH,SAAS,CAAC,GAAGN,EAAE,CAAC,CAAC,GAAGM,SAAS,CAAC;QACzCE,UAAU,CAACI,IAAI,CAACH,MAAM,CAAC,CAAC,CAAC,EAAEA,MAAM,CAAC,CAAC,CAAC,CAAC;QACrC;MAEF,KAAK,QAAQ;QACX,IAAII,MAAM,GAAG,CAACb,EAAE,CAACM,SAAS,CAAC,GAAGI,MAAM,CAACJ,SAAS,CAAC,IAAI,CAAC;QACpD,IAAIQ,OAAO,GAAG,EAAE;QAChBL,MAAM,CAACH,SAAS,CAAC,GAAGQ,OAAO,CAACR,SAAS,CAAC,GAAGO,MAAM;QAC/CJ,MAAM,CAAC,CAAC,GAAGH,SAAS,CAAC,GAAGN,EAAE,CAAC,CAAC,GAAGM,SAAS,CAAC;QACzCQ,OAAO,CAAC,CAAC,GAAGR,SAAS,CAAC,GAAGI,MAAM,CAAC,CAAC,GAAGJ,SAAS,CAAC;QAC9CE,UAAU,CAACI,IAAI,CAACH,MAAM,CAAC,CAAC,CAAC,EAAEA,MAAM,CAAC,CAAC,CAAC,CAAC;QACrCD,UAAU,CAACI,IAAI,CAACE,OAAO,CAAC,CAAC,CAAC,EAAEA,OAAO,CAAC,CAAC,CAAC,CAAC;QACvC;MAEF;QACE;QACAL,MAAM,CAACH,SAAS,CAAC,GAAGN,EAAE,CAACM,SAAS,CAAC;QACjCG,MAAM,CAAC,CAAC,GAAGH,SAAS,CAAC,GAAGI,MAAM,CAAC,CAAC,GAAGJ,SAAS,CAAC;QAC7CE,UAAU,CAACI,IAAI,CAACH,MAAM,CAAC,CAAC,CAAC,EAAEA,MAAM,CAAC,CAAC,CAAC,CAAC;IACzC;EACF,CAAC,CAAC;;EAGFD,UAAU,CAACI,IAAI,CAAC3C,MAAM,CAACF,CAAC,EAAE,CAAC,EAAEE,MAAM,CAACF,CAAC,EAAE,CAAC,CAAC;EACzC,OAAOyC,UAAU;AACnB;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA,SAASO,cAAcA,CAACC,UAAU,EAAEC,OAAO,EAAE;EAC3C,IAAIC,aAAa,GAAG,EAAE;EACtB,IAAIrB,GAAG,GAAGmB,UAAU,CAAClD,MAAM,CAAC,CAAC;;EAE7B,IAAIqD,uBAAuB;EAC3B,IAAIC,oBAAoB;EAExB,SAASC,QAAQA,CAACC,KAAK,EAAEC,KAAK,EAAEC,YAAY,EAAE;IAC5C,IAAIC,MAAM,GAAGH,KAAK,CAACI,KAAK;IACxB,IAAIC,CAAC,GAAG,CAACH,YAAY,GAAGC,MAAM,KAAKF,KAAK,CAACG,KAAK,GAAGD,MAAM,CAAC;IACxD,IAAIG,KAAK,GAAGlE,IAAI,CAACiE,CAAC,EAAE,CAACL,KAAK,CAACM,KAAK,EAAEL,KAAK,CAACK,KAAK,CAAC,CAAC;IAC/C,OAAO;MACLF,KAAK,EAAEF,YAAY;MACnBI,KAAK,EAAEA;IACT,CAAC;EACH;EAEA,KAAK,IAAI7D,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG8B,GAAG,EAAE9B,CAAC,EAAE,EAAE;IAC5B,IAAI8D,MAAM,GAAGb,UAAU,CAACjD,CAAC,CAAC;IAC1B,IAAI2D,KAAK,GAAGG,MAAM,CAACH,KAAK;IAExB,IAAIA,KAAK,GAAG,CAAC,EAAE;MACbP,uBAAuB,GAAGU,MAAM;IAClC,CAAC,MAAM,IAAIH,KAAK,GAAGT,OAAO,EAAE;MAC1B,IAAIG,oBAAoB,EAAE;QACxBF,aAAa,CAACN,IAAI,CAACS,QAAQ,CAACD,oBAAoB,EAAES,MAAM,EAAEZ,OAAO,CAAC,CAAC;MACrE,CAAC,MAAM,IAAIE,uBAAuB,EAAE;QAClC;QACAD,aAAa,CAACN,IAAI,CAACS,QAAQ,CAACF,uBAAuB,EAAEU,MAAM,EAAE,CAAC,CAAC,EAAER,QAAQ,CAACF,uBAAuB,EAAEU,MAAM,EAAEZ,OAAO,CAAC,CAAC;MACtH,CAAC,CAAC;;MAGF;IACF,CAAC,MAAM;MACL,IAAIE,uBAAuB,EAAE;QAC3BD,aAAa,CAACN,IAAI,CAACS,QAAQ,CAACF,uBAAuB,EAAEU,MAAM,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;;QAElEV,uBAAuB,GAAG,IAAI;MAChC;MAEAD,aAAa,CAACN,IAAI,CAACiB,MAAM,CAAC;MAC1BT,oBAAoB,GAAGS,MAAM;IAC/B;EACF;EAEA,OAAOX,aAAa;AACtB;AAEA,SAASY,iBAAiBA,CAACpC,IAAI,EAAED,QAAQ,EAAEsC,GAAG,EAAE;EAC9C,IAAIC,cAAc,GAAGtC,IAAI,CAACuC,SAAS,CAAC,YAAY,CAAC;EAEjD,IAAI,CAACD,cAAc,IAAI,CAACA,cAAc,CAAClE,MAAM,IAAI,CAAC4B,IAAI,CAACI,KAAK,CAAC,CAAC,EAAE;IAC9D;IACA;EACF;EAEA,IAAIL,QAAQ,CAACyC,IAAI,KAAK,aAAa,EAAE;IACnC,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;MACzCC,OAAO,CAACC,IAAI,CAAC,4DAA4D,CAAC;IAC5E;IAEA;EACF;EAEA,IAAIC,QAAQ;EACZ,IAAIC,UAAU;EAEd,KAAK,IAAI1E,CAAC,GAAGiE,cAAc,CAAClE,MAAM,GAAG,CAAC,EAAEC,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;IACnD,IAAI2E,OAAO,GAAGhD,IAAI,CAACiD,gBAAgB,CAACX,cAAc,CAACjE,CAAC,CAAC,CAAC6E,SAAS,CAAC;IAChEJ,QAAQ,GAAGE,OAAO,IAAIA,OAAO,CAACF,QAAQ,CAAC,CAAC;;IAExC,IAAIA,QAAQ,KAAK,GAAG,IAAIA,QAAQ,KAAK,GAAG,EAAE;MACxCC,UAAU,GAAGT,cAAc,CAACjE,CAAC,CAAC;MAC9B;IACF;EACF;EAEA,IAAI,CAAC0E,UAAU,EAAE;IACf,IAAIN,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;MACzCC,OAAO,CAACC,IAAI,CAAC,yDAAyD,CAAC;IACzE;IAEA;EACF,CAAC,CAAC;EACF;EACA;EACA;EACA;EACA;;EAGA,IAAIM,IAAI,GAAGpD,QAAQ,CAACqD,OAAO,CAACN,QAAQ,CAAC,CAAC,CAAC;;EAEvC,IAAIxB,UAAU,GAAGhF,MAAM,CAAC+G,GAAG,CAACN,UAAU,CAACO,KAAK,EAAE,UAAUC,IAAI,EAAE;IAC5D;IACA,OAAO;MACLvB,KAAK,EAAEmB,IAAI,CAACK,aAAa,CAACL,IAAI,CAACM,WAAW,CAACF,IAAI,CAACG,KAAK,CAAC,CAAC;MACvDxB,KAAK,EAAEqB,IAAI,CAACrB;IACd,CAAC;EACH,CAAC,CAAC;EACF,IAAIyB,OAAO,GAAGrC,UAAU,CAAClD,MAAM;EAC/B,IAAIwF,WAAW,GAAGb,UAAU,CAACa,WAAW,CAACC,KAAK,CAAC,CAAC;EAEhD,IAAIF,OAAO,IAAIrC,UAAU,CAAC,CAAC,CAAC,CAACU,KAAK,GAAGV,UAAU,CAACqC,OAAO,GAAG,CAAC,CAAC,CAAC3B,KAAK,EAAE;IAClEV,UAAU,CAACwC,OAAO,CAAC,CAAC;IACpBF,WAAW,CAACE,OAAO,CAAC,CAAC;EACvB;EAEA,IAAIC,iBAAiB,GAAG1C,cAAc,CAACC,UAAU,EAAEwB,QAAQ,KAAK,GAAG,GAAGT,GAAG,CAAC2B,QAAQ,CAAC,CAAC,GAAG3B,GAAG,CAAC4B,SAAS,CAAC,CAAC,CAAC;EACvG,IAAIC,cAAc,GAAGH,iBAAiB,CAAC3F,MAAM;EAE7C,IAAI,CAAC8F,cAAc,IAAIP,OAAO,EAAE;IAC9B;IACA,OAAOrC,UAAU,CAAC,CAAC,CAAC,CAACU,KAAK,GAAG,CAAC,GAAG4B,WAAW,CAAC,CAAC,CAAC,GAAGA,WAAW,CAAC,CAAC,CAAC,GAAGtC,UAAU,CAACqC,OAAO,GAAG,CAAC,CAAC,CAACzB,KAAK,GAAG0B,WAAW,CAAC,CAAC,CAAC,GAAGA,WAAW,CAAC,CAAC,CAAC,GAAGtC,UAAU,CAAC,CAAC,CAAC,CAACY,KAAK;EAC1J;EAEA,IAAIiC,UAAU,GAAG,EAAE,CAAC,CAAC;;EAErB,IAAIC,QAAQ,GAAGL,iBAAiB,CAAC,CAAC,CAAC,CAAC/B,KAAK,GAAGmC,UAAU;EACtD,IAAIE,QAAQ,GAAGN,iBAAiB,CAACG,cAAc,GAAG,CAAC,CAAC,CAAClC,KAAK,GAAGmC,UAAU;EACvE,IAAIG,SAAS,GAAGD,QAAQ,GAAGD,QAAQ;EAEnC,IAAIE,SAAS,GAAG,IAAI,EAAE;IACpB,OAAO,aAAa;EACtB;EAEAhI,MAAM,CAACiI,IAAI,CAACR,iBAAiB,EAAE,UAAUR,IAAI,EAAE;IAC7CA,IAAI,CAACiB,MAAM,GAAG,CAACjB,IAAI,CAACvB,KAAK,GAAGoC,QAAQ,IAAIE,SAAS;EACnD,CAAC,CAAC;EACFP,iBAAiB,CAAC7C,IAAI,CAAC;IACrB;IACAsD,MAAM,EAAEN,cAAc,GAAGH,iBAAiB,CAACG,cAAc,GAAG,CAAC,CAAC,CAACM,MAAM,GAAG,GAAG;IAC3EtC,KAAK,EAAE0B,WAAW,CAAC,CAAC,CAAC,IAAI;EAC3B,CAAC,CAAC;EACFG,iBAAiB,CAACU,OAAO,CAAC;IACxBD,MAAM,EAAEN,cAAc,GAAGH,iBAAiB,CAAC,CAAC,CAAC,CAACS,MAAM,GAAG,GAAG;IAC1DtC,KAAK,EAAE0B,WAAW,CAAC,CAAC,CAAC,IAAI;EAC3B,CAAC,CAAC;EACF,IAAIc,QAAQ,GAAG,IAAIhI,OAAO,CAACiI,cAAc,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAEZ,iBAAiB,EAAE,IAAI,CAAC;EAC9EW,QAAQ,CAAC5B,QAAQ,CAAC,GAAGsB,QAAQ;EAC7BM,QAAQ,CAAC5B,QAAQ,GAAG,GAAG,CAAC,GAAGuB,QAAQ;EACnC,OAAOK,QAAQ;AACjB;AAEA,SAASE,eAAeA,CAACC,WAAW,EAAE7E,IAAI,EAAED,QAAQ,EAAE;EACpD,IAAI+E,aAAa,GAAGD,WAAW,CAACE,GAAG,CAAC,eAAe,CAAC;EACpD,IAAIC,MAAM,GAAGF,aAAa,KAAK,MAAM;EAErC,IAAIA,aAAa,IAAI,CAACE,MAAM,EAAE;IAC5B;EACF;EAEA,IAAIC,YAAY,GAAGlF,QAAQ,CAACmF,cAAc,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;EAExD,IAAI,CAACD,YAAY,EAAE;IACjB;EACF,CAAC,CAAC;EACF;EACA;;EAGA,IAAID,MAAM,CAAC;EAAA,GACRG,2BAA2B,CAACF,YAAY,EAAEjF,IAAI,CAAC,EAAE;IAClD;EACF,CAAC,CAAC;;EAGF,IAAIoF,eAAe,GAAGpF,IAAI,CAACqF,YAAY,CAACJ,YAAY,CAACpE,GAAG,CAAC;EACzD,IAAIyE,QAAQ,GAAG,CAAC,CAAC;EACjBhJ,MAAM,CAACiI,IAAI,CAACU,YAAY,CAACM,aAAa,CAAC,CAAC,EAAE,UAAUC,SAAS,EAAE;IAC7D,IAAIC,aAAa,GAAGR,YAAY,CAACS,KAAK,CAACC,mBAAmB,CAACH,SAAS,CAACI,SAAS,CAAC;IAC/EN,QAAQ,CAACG,aAAa,CAAC,GAAG,CAAC;EAC7B,CAAC,CAAC;EACF,OAAO,UAAUI,SAAS,EAAE;IAC1B,OAAO,CAACP,QAAQ,CAACQ,cAAc,CAAC9F,IAAI,CAAC+E,GAAG,CAACK,eAAe,EAAES,SAAS,CAAC,CAAC;EACvE,CAAC;AACH;AAEA,SAASV,2BAA2BA,CAACF,YAAY,EAAEjF,IAAI,EAAE;EACvD;EACA;EACA;EACA;EACA,IAAI+F,UAAU,GAAGd,YAAY,CAACe,SAAS,CAAC,CAAC;EACzC,IAAIC,SAAS,GAAGjH,IAAI,CAACU,GAAG,CAACqG,UAAU,CAAC,CAAC,CAAC,GAAGA,UAAU,CAAC,CAAC,CAAC,CAAC,GAAGd,YAAY,CAACS,KAAK,CAACtF,KAAK,CAAC,CAAC;EACpFrB,KAAK,CAACkH,SAAS,CAAC,KAAKA,SAAS,GAAG,CAAC,CAAC,CAAC,CAAC;EACrC;;EAEA,IAAIC,OAAO,GAAGlG,IAAI,CAACI,KAAK,CAAC,CAAC;EAC1B,IAAI+F,IAAI,GAAGnH,IAAI,CAACE,GAAG,CAAC,CAAC,EAAEF,IAAI,CAACoH,KAAK,CAACF,OAAO,GAAG,CAAC,CAAC,CAAC;EAE/C,KAAK,IAAIL,SAAS,GAAG,CAAC,EAAEA,SAAS,GAAGK,OAAO,EAAEL,SAAS,IAAIM,IAAI,EAAE;IAC9D,IAAI3J,SAAS,CAAC6J,aAAa,CAACrG,IAAI,EAAE6F,SAAS,CAAC;IAC5C,CAAC,CAACZ,YAAY,CAACqB,YAAY,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;IAAA,EACrC,GAAG,GAAGL,SAAS,EAAE;MACjB,OAAO,KAAK;IACd;EACF;EAEA,OAAO,IAAI;AACb;AAEA,SAASM,WAAWA,CAAC1H,CAAC,EAAEC,CAAC,EAAE;EACzB,OAAOC,KAAK,CAACF,CAAC,CAAC,IAAIE,KAAK,CAACD,CAAC,CAAC;AAC7B;AAEA,SAAS0H,mBAAmBA,CAACjI,MAAM,EAAE;EACnC,IAAI4B,GAAG,GAAG5B,MAAM,CAACH,MAAM,GAAG,CAAC;EAE3B,OAAO+B,GAAG,GAAG,CAAC,EAAEA,GAAG,EAAE,EAAE;IACrB,IAAI,CAACoG,WAAW,CAAChI,MAAM,CAAC4B,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC,EAAE5B,MAAM,CAAC4B,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE;MAC1D;IACF;EACF;EAEA,OAAOA,GAAG,GAAG,CAAC;AAChB;AAEA,SAASsG,eAAeA,CAAClI,MAAM,EAAE8B,GAAG,EAAE;EACpC,OAAO,CAAC9B,MAAM,CAAC8B,GAAG,GAAG,CAAC,CAAC,EAAE9B,MAAM,CAAC8B,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;AAC/C;AAEA,SAASqG,aAAaA,CAACnI,MAAM,EAAEoI,IAAI,EAAE9F,GAAG,EAAE;EACxC,IAAIV,GAAG,GAAG5B,MAAM,CAACH,MAAM,GAAG,CAAC;EAC3B,IAAIwI,MAAM,GAAG/F,GAAG,KAAK,GAAG,GAAG,CAAC,GAAG,CAAC;EAChC,IAAIgG,CAAC;EACL,IAAIC,CAAC;EACL,IAAIC,SAAS,GAAG,CAAC;EACjB,IAAIC,SAAS,GAAG,CAAC,CAAC;EAElB,KAAK,IAAI3I,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG8B,GAAG,EAAE9B,CAAC,EAAE,EAAE;IAC5ByI,CAAC,GAAGvI,MAAM,CAACF,CAAC,GAAG,CAAC,GAAGuI,MAAM,CAAC;IAE1B,IAAI7H,KAAK,CAAC+H,CAAC,CAAC,IAAI/H,KAAK,CAACR,MAAM,CAACF,CAAC,GAAG,CAAC,GAAG,CAAC,GAAGuI,MAAM,CAAC,CAAC,EAAE;MACjD;IACF;IAEA,IAAIvI,CAAC,KAAK,CAAC,EAAE;MACXwI,CAAC,GAAGC,CAAC;MACL;IACF;IAEA,IAAID,CAAC,IAAIF,IAAI,IAAIG,CAAC,IAAIH,IAAI,IAAIE,CAAC,IAAIF,IAAI,IAAIG,CAAC,IAAIH,IAAI,EAAE;MACpDK,SAAS,GAAG3I,CAAC;MACb;IACF;IAEA0I,SAAS,GAAG1I,CAAC;IACbwI,CAAC,GAAGC,CAAC;EACP;EAEA,OAAO;IACLG,KAAK,EAAE,CAACF,SAAS,EAAEC,SAAS,CAAC;IAC7BE,CAAC,EAAE,CAACP,IAAI,GAAGE,CAAC,KAAKC,CAAC,GAAGD,CAAC;EACxB,CAAC;AACH;AAEA,SAASM,oBAAoBA,CAACtC,WAAW,EAAE;EACzC,IAAIA,WAAW,CAACE,GAAG,CAAC,CAAC,UAAU,EAAE,MAAM,CAAC,CAAC,EAAE;IACzC,OAAO,IAAI;EACb;EAEA,KAAK,IAAI1G,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGd,cAAc,CAACa,MAAM,EAAEC,CAAC,EAAE,EAAE;IAC9C,IAAIwG,WAAW,CAACE,GAAG,CAAC,CAACxH,cAAc,CAACc,CAAC,CAAC,EAAE,UAAU,EAAE,MAAM,CAAC,CAAC,EAAE;MAC5D,OAAO,IAAI;IACb;EACF;EAEA,OAAO,KAAK;AACd;AAEA,SAAS+I,kBAAkBA,CAACC,QAAQ,EAAEtH,QAAQ,EAAEuH,YAAY,EAAEzC,WAAW,EAAE;EACzE,IAAI1H,sBAAsB,CAAC4C,QAAQ,EAAE,aAAa,CAAC,EAAE;IACnD,IAAIwH,eAAe,GAAG1C,WAAW,CAAC2C,QAAQ,CAAC,UAAU,CAAC;IACtD,IAAIC,gBAAgB,GAAGF,eAAe,CAACxC,GAAG,CAAC,gBAAgB,CAAC;IAC5D,IAAI2C,MAAM,GAAG7C,WAAW,CAAC8C,OAAO,CAAC,CAAC;IAClC,IAAIC,sBAAsB,GAAG;MAC3BC,cAAc,EAAE;IAClB,CAAC;IACD,IAAIC,MAAM,GAAGX,oBAAoB,CAACtC,WAAW,CAAC,GAAG,UAAUkD,OAAO,EAAEC,QAAQ,EAAE;MAC5EX,QAAQ,CAACY,iBAAiB,CAACF,OAAO,EAAEC,QAAQ,EAAEN,MAAM,EAAEE,sBAAsB,EAAEH,gBAAgB,EAAEF,eAAe,EAAExH,QAAQ,CAAC;IAC5H,CAAC,GAAG,IAAI;IACR,IAAIuG,YAAY,GAAGvG,QAAQ,CAACY,WAAW,CAAC,CAAC,CAAC2F,YAAY,CAAC,CAAC;IACxD,IAAI4B,QAAQ,GAAGjL,kBAAkB,CAAC8C,QAAQ,EAAEuH,YAAY,EAAEzC,WAAW,EAAE,YAAY;MACjF,IAAIsD,QAAQ,GAAGd,QAAQ,CAACe,SAAS;MAEjC,IAAID,QAAQ,IAAIb,YAAY,EAAE;QAC5B,IAAIM,sBAAsB,CAACS,SAAS,IAAI,IAAI,EAAE;UAC5CF,QAAQ,CAACG,IAAI,CAAC;YACZzJ,CAAC,EAAE+I,sBAAsB,CAACS,SAAS;YACnCvJ,CAAC,EAAE8I,sBAAsB,CAACW;UAC5B,CAAC,CAAC;QACJ;MACF;IACF,CAAC,EAAET,MAAM,CAAC,CAAC,CAAC;;IAEZ,IAAI,CAACjD,WAAW,CAACE,GAAG,CAAC,MAAM,EAAE,IAAI,CAAC,EAAE;MAClC,IAAIyD,SAAS,GAAGN,QAAQ,CAACO,KAAK;MAC9B,IAAIC,UAAU,GAAG1J,IAAI,CAACE,GAAG,CAACsJ,SAAS,CAACG,KAAK,EAAEH,SAAS,CAACI,MAAM,CAAC;MAE5D,IAAItC,YAAY,EAAE;QAChBkC,SAAS,CAAC1J,CAAC,IAAI4J,UAAU;QACzBF,SAAS,CAACI,MAAM,IAAIF,UAAU,GAAG,CAAC;MACpC,CAAC,MAAM;QACLF,SAAS,CAAC3J,CAAC,IAAI6J,UAAU;QACzBF,SAAS,CAACG,KAAK,IAAID,UAAU,GAAG,CAAC;MACnC;IACF,CAAC,CAAC;;IAGF,IAAIZ,MAAM,EAAE;MACVA,MAAM,CAAC,CAAC,EAAEI,QAAQ,CAAC;IACrB;IAEA,OAAOA,QAAQ;EACjB,CAAC,MAAM;IACL,IAAIzF,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;MACzC,IAAIkC,WAAW,CAACE,GAAG,CAAC,CAAC,UAAU,EAAE,MAAM,CAAC,CAAC,EAAE;QACzCnC,OAAO,CAACC,IAAI,CAAC,uDAAuD,CAAC;MACvE;IACF;IAEA,OAAO3F,mBAAmB,CAAC6C,QAAQ,EAAEuH,YAAY,EAAEzC,WAAW,CAAC;EACjE;AACF;AAEA,SAASgE,yBAAyBA,CAACC,aAAa,EAAE/I,QAAQ,EAAE;EAC1D,IAAIW,QAAQ,GAAGX,QAAQ,CAACY,WAAW,CAAC,CAAC;EACrC,IAAI2F,YAAY,GAAG5F,QAAQ,CAAC4F,YAAY,CAAC,CAAC;EAC1C,IAAIyC,cAAc,GAAGrI,QAAQ,CAACsI,OAAO;EACrC,IAAIC,KAAK,GAAG3C,YAAY,GAAGyC,cAAc,GAAG,OAAO,GAAG,MAAM,GAAG,QAAQ;EACvE,IAAIG,aAAa,GAAG5C,YAAY,GAAG,QAAQ,GAAGyC,cAAc,GAAG,KAAK,GAAG,QAAQ;EAC/E,OAAO;IACLI,MAAM,EAAE;MACNF,KAAK,EAAEH,aAAa,CAAC/D,GAAG,CAAC,OAAO,CAAC,IAAIkE,KAAK;MAC1CC,aAAa,EAAEJ,aAAa,CAAC/D,GAAG,CAAC,eAAe,CAAC,IAAImE;IACvD;EACF,CAAC;AACH;AAEA,IAAIE,QAAQ,GACZ;AACA,UAAUC,MAAM,EAAE;EAChBhN,SAAS,CAAC+M,QAAQ,EAAEC,MAAM,CAAC;EAE3B,SAASD,QAAQA,CAAA,EAAG;IAClB,OAAOC,MAAM,KAAK,IAAI,IAAIA,MAAM,CAACC,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC,IAAI,IAAI;EACjE;EAEAH,QAAQ,CAACI,SAAS,CAACC,IAAI,GAAG,YAAY;IACpC,IAAIC,SAAS,GAAG,IAAIhN,OAAO,CAACiN,KAAK,CAAC,CAAC;IACnC,IAAIC,UAAU,GAAG,IAAIrN,UAAU,CAAC,CAAC;IACjC,IAAI,CAACsN,KAAK,CAACC,GAAG,CAACF,UAAU,CAACC,KAAK,CAAC;IAChC,IAAI,CAACE,WAAW,GAAGH,UAAU;IAC7B,IAAI,CAACI,UAAU,GAAGN,SAAS;EAC7B,CAAC;EAEDN,QAAQ,CAACI,SAAS,CAACS,MAAM,GAAG,UAAUpF,WAAW,EAAEqF,OAAO,EAAE7H,GAAG,EAAE;IAC/D,IAAI8H,KAAK,GAAG,IAAI;IAEhB,IAAIpK,QAAQ,GAAG8E,WAAW,CAACuF,gBAAgB;IAC3C,IAAIP,KAAK,GAAG,IAAI,CAACA,KAAK;IACtB,IAAI7J,IAAI,GAAG6E,WAAW,CAAC8C,OAAO,CAAC,CAAC;IAChC,IAAI0C,cAAc,GAAGxF,WAAW,CAAC2C,QAAQ,CAAC,WAAW,CAAC;IACtD,IAAI8C,cAAc,GAAGzF,WAAW,CAAC2C,QAAQ,CAAC,WAAW,CAAC;IACtD,IAAIjJ,MAAM,GAAGyB,IAAI,CAACuK,SAAS,CAAC,QAAQ,CAAC,IAAI,EAAE;IAC3C,IAAIC,eAAe,GAAGzK,QAAQ,CAACyC,IAAI,KAAK,OAAO;IAC/C,IAAIiI,YAAY,GAAG,IAAI,CAACC,SAAS;IACjC,IAAId,UAAU,GAAG,IAAI,CAACG,WAAW;IACjC,IAAIY,QAAQ,GAAG,IAAI,CAACC,SAAS;IAC7B,IAAIC,OAAO,GAAG,IAAI,CAACC,QAAQ;IAC3B,IAAIpB,SAAS,GAAG,IAAI,CAACM,UAAU;IAC/B,IAAI1C,YAAY,GAAG,CAAC4C,OAAO,CAACa,GAAG,IAAIlG,WAAW,CAACmG,kBAAkB,CAAC,CAAC;IACnE,IAAIC,WAAW,GAAG,CAACX,cAAc,CAACY,OAAO,CAAC,CAAC;IAC3C,IAAIC,WAAW,GAAGb,cAAc,CAACvF,GAAG,CAAC,QAAQ,CAAC;IAC9C,IAAI9E,aAAa,GAAGlD,oBAAoB,CAACgD,QAAQ,EAAEC,IAAI,EAAEmL,WAAW,CAAC;IACrE,IAAIC,eAAe,GAAGH,WAAW,IAAInL,kBAAkB,CAACC,QAAQ,EAAEC,IAAI,EAAEC,aAAa,CAAC;IACtF,IAAIoL,UAAU,GAAGxG,WAAW,CAACE,GAAG,CAAC,YAAY,CAAC;IAC9C,IAAItE,YAAY,GAAGoE,WAAW,CAACE,GAAG,CAAC,cAAc,CAAC;IAClD,IAAIuG,YAAY,GAAGD,UAAU,IAAI,CAACb,eAAe,IAAI5F,eAAe,CAACC,WAAW,EAAE7E,IAAI,EAAED,QAAQ,CAAC,CAAC,CAAC;;IAEnG,IAAIwL,OAAO,GAAG,IAAI,CAACC,KAAK;IACxBD,OAAO,IAAIA,OAAO,CAACE,iBAAiB,CAAC,UAAUC,EAAE,EAAErL,GAAG,EAAE;MACtD,IAAIqL,EAAE,CAACC,MAAM,EAAE;QACb9B,KAAK,CAAC+B,MAAM,CAACF,EAAE,CAAC;QAChBH,OAAO,CAACM,gBAAgB,CAACxL,GAAG,EAAE,IAAI,CAAC;MACrC;IACF,CAAC,CAAC,CAAC,CAAC;;IAEJ,IAAI,CAACgL,UAAU,EAAE;MACfzB,UAAU,CAACgC,MAAM,CAAC,CAAC;IACrB;IAEA/B,KAAK,CAACC,GAAG,CAACJ,SAAS,CAAC,CAAC,CAAC;;IAEtB,IAAIvD,IAAI,GAAG,CAACqE,eAAe,GAAG3F,WAAW,CAACE,GAAG,CAAC,MAAM,CAAC,GAAG,KAAK;IAC7D,IAAI+G,kBAAkB;IAEtB,IAAI/L,QAAQ,IAAIA,QAAQ,CAACgM,OAAO,IAAIlH,WAAW,CAACE,GAAG,CAAC,MAAM,EAAE,IAAI,CAAC,EAAE;MACjE+G,kBAAkB,GAAG/L,QAAQ,CAACgM,OAAO,CAAC,CAAC,CAAC,CAAC;MACzC;;MAEA,IAAID,kBAAkB,CAACnD,KAAK,IAAI,IAAI,EAAE;QACpCmD,kBAAkB,CAACjN,CAAC,IAAI,GAAG;QAC3BiN,kBAAkB,CAAChN,CAAC,IAAI,GAAG;QAC3BgN,kBAAkB,CAACnD,KAAK,IAAI,GAAG;QAC/BmD,kBAAkB,CAAClD,MAAM,IAAI,GAAG;MAClC,CAAC,MAAM,IAAIkD,kBAAkB,CAACE,EAAE,EAAE;QAChCF,kBAAkB,CAACE,EAAE,IAAI,GAAG;QAC5BF,kBAAkB,CAACG,CAAC,IAAI,GAAG;MAC7B;IACF;IAEA,IAAI,CAACC,mBAAmB,GAAGJ,kBAAkB;IAC7C,IAAIK,WAAW,GAAG/J,iBAAiB,CAACpC,IAAI,EAAED,QAAQ,EAAEsC,GAAG,CAAC,IAAIrC,IAAI,CAACuC,SAAS,CAAC,OAAO,CAAC,CAACvC,IAAI,CAACuC,SAAS,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;;IAEjH,IAAI,EAAEoI,QAAQ,IAAIF,YAAY,CAACjI,IAAI,KAAKzC,QAAQ,CAACyC,IAAI,IAAI2D,IAAI,KAAK,IAAI,CAACiG,KAAK,CAAC,EAAE;MAC7Ef,UAAU,IAAIzB,UAAU,CAACyC,UAAU,CAACrM,IAAI,EAAE;QACxCsM,QAAQ,EAAEhB,YAAY;QACtBiB,SAAS,EAAET,kBAAkB;QAC7BU,gBAAgB,EAAE,IAAI;QACtBC,cAAc,EAAE,SAAAA,CAAUpM,GAAG,EAAE;UAC7B,OAAO,CAAC9B,MAAM,CAAC8B,GAAG,GAAG,CAAC,CAAC,EAAE9B,MAAM,CAAC8B,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;QAC/C;MACF,CAAC,CAAC;MACFiH,YAAY,IAAI,IAAI,CAACoF,yBAAyB,CAAC1M,IAAI,EAAED,QAAQ,EAAE+L,kBAAkB,CAAC;MAElF,IAAI3F,IAAI,EAAE;QACR;QACA5H,MAAM,GAAGgC,kBAAkB,CAAChC,MAAM,EAAEwB,QAAQ,EAAEoG,IAAI,EAAE1F,YAAY,CAAC;QAEjE,IAAI2K,eAAe,EAAE;UACnBA,eAAe,GAAG7K,kBAAkB,CAAC6K,eAAe,EAAErL,QAAQ,EAAEoG,IAAI,EAAE1F,YAAY,CAAC;QACrF;MACF;MAEAkK,QAAQ,GAAG,IAAI,CAACgC,YAAY,CAACpO,MAAM,CAAC;MAEpC,IAAI0M,WAAW,EAAE;QACfJ,OAAO,GAAG,IAAI,CAAC+B,WAAW,CAACrO,MAAM,EAAE6M,eAAe,CAAC;MACrD,CAAC,CAAC;MAAA,KACG,IAAIP,OAAO,EAAE;QACdnB,SAAS,CAACkC,MAAM,CAACf,OAAO,CAAC;QACzBA,OAAO,GAAG,IAAI,CAACC,QAAQ,GAAG,IAAI;MAChC,CAAC,CAAC;;MAGJ,IAAI,CAACN,eAAe,EAAE;QACpB,IAAI,CAACqC,qBAAqB,CAAChI,WAAW,EAAE9E,QAAQ,EAAEhC,oBAAoB,CAACoO,WAAW,CAAC,CAAC;MACtF;MAEAzC,SAAS,CAACoD,WAAW,CAAC1F,kBAAkB,CAAC,IAAI,EAAErH,QAAQ,EAAE,IAAI,EAAE8E,WAAW,CAAC,CAAC;IAC9E,CAAC,MAAM;MACL,IAAIoG,WAAW,IAAI,CAACJ,OAAO,EAAE;QAC3B;QACAA,OAAO,GAAG,IAAI,CAAC+B,WAAW,CAACrO,MAAM,EAAE6M,eAAe,CAAC;MACrD,CAAC,MAAM,IAAIP,OAAO,IAAI,CAACI,WAAW,EAAE;QAClC;QACAvB,SAAS,CAACkC,MAAM,CAACf,OAAO,CAAC;QACzBA,OAAO,GAAG,IAAI,CAACC,QAAQ,GAAG,IAAI;MAChC,CAAC,CAAC;;MAGF,IAAI,CAACN,eAAe,EAAE;QACpB,IAAI,CAACqC,qBAAqB,CAAChI,WAAW,EAAE9E,QAAQ,EAAEhC,oBAAoB,CAACoO,WAAW,CAAC,CAAC;MACtF,CAAC,CAAC;;MAGF,IAAIY,WAAW,GAAGrD,SAAS,CAACsD,WAAW,CAAC,CAAC;MAEzC,IAAID,WAAW,EAAE;QACf,IAAIE,WAAW,GAAG7F,kBAAkB,CAAC,IAAI,EAAErH,QAAQ,EAAE,KAAK,EAAE8E,WAAW,CAAC;QACxEnI,OAAO,CAACwQ,SAAS,CAACH,WAAW,EAAE;UAC7BtE,KAAK,EAAEwE,WAAW,CAACxE;QACrB,CAAC,EAAE5D,WAAW,CAAC;MACjB,CAAC,MAAM;QACL6E,SAAS,CAACoD,WAAW,CAAC1F,kBAAkB,CAAC,IAAI,EAAErH,QAAQ,EAAE,IAAI,EAAE8E,WAAW,CAAC,CAAC;MAC9E,CAAC,CAAC;MACF;;MAGAwG,UAAU,IAAIzB,UAAU,CAACyC,UAAU,CAACrM,IAAI,EAAE;QACxCsM,QAAQ,EAAEhB,YAAY;QACtBiB,SAAS,EAAET,kBAAkB;QAC7BU,gBAAgB,EAAE,IAAI;QACtBC,cAAc,EAAE,SAAAA,CAAUpM,GAAG,EAAE;UAC7B,OAAO,CAAC9B,MAAM,CAAC8B,GAAG,GAAG,CAAC,CAAC,EAAE9B,MAAM,CAAC8B,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;QAC/C;MACF,CAAC,CAAC,CAAC,CAAC;MACJ;;MAEA,IAAI,CAACpC,YAAY,CAAC,IAAI,CAACkP,gBAAgB,EAAE/B,eAAe,CAAC,IAAI,CAACnN,YAAY,CAAC,IAAI,CAACmP,OAAO,EAAE7O,MAAM,CAAC,EAAE;QAChG,IAAI+I,YAAY,EAAE;UAChB,IAAI,CAAC+F,kBAAkB,CAACrN,IAAI,EAAEoL,eAAe,EAAErL,QAAQ,EAAEsC,GAAG,EAAE8D,IAAI,EAAEgF,WAAW,EAAE1K,YAAY,CAAC;QAChG,CAAC,MAAM;UACL;UACA,IAAI0F,IAAI,EAAE;YACR;YACA5H,MAAM,GAAGgC,kBAAkB,CAAChC,MAAM,EAAEwB,QAAQ,EAAEoG,IAAI,EAAE1F,YAAY,CAAC;YAEjE,IAAI2K,eAAe,EAAE;cACnBA,eAAe,GAAG7K,kBAAkB,CAAC6K,eAAe,EAAErL,QAAQ,EAAEoG,IAAI,EAAE1F,YAAY,CAAC;YACrF;UACF;UAEAkK,QAAQ,CAAC2C,QAAQ,CAAC;YAChB/O,MAAM,EAAEA;UACV,CAAC,CAAC;UACFsM,OAAO,IAAIA,OAAO,CAACyC,QAAQ,CAAC;YAC1B/O,MAAM,EAAEA,MAAM;YACd6M,eAAe,EAAEA;UACnB,CAAC,CAAC;QACJ;MACF;IACF;IAEA,IAAImC,aAAa,GAAG1I,WAAW,CAAC2C,QAAQ,CAAC,UAAU,CAAC;IACpD,IAAIgG,KAAK,GAAGD,aAAa,CAACxI,GAAG,CAAC,OAAO,CAAC;IACtC,IAAI0I,SAAS,GAAGF,aAAa,CAACxI,GAAG,CAAC,WAAW,CAAC;IAC9C,IAAI2I,gBAAgB,GAAGH,aAAa,CAACxI,GAAG,CAAC,UAAU,CAAC;IACpD4F,QAAQ,CAACgD,QAAQ,CAACrR,MAAM,CAACsR,QAAQ;IAAE;IACnCvD,cAAc,CAACwD,YAAY,CAAC,CAAC,EAAE;MAC7BC,IAAI,EAAE,MAAM;MACZC,MAAM,EAAE5B,WAAW;MACnB6B,QAAQ,EAAE;IACZ,CAAC,CAAC,CAAC;IACH5Q,wBAAwB,CAACuN,QAAQ,EAAE9F,WAAW,EAAE,WAAW,CAAC;IAE5D,IAAI8F,QAAQ,CAACsD,KAAK,CAACC,SAAS,GAAG,CAAC,IAAIrJ,WAAW,CAACE,GAAG,CAAC,CAAC,UAAU,EAAE,WAAW,EAAE,OAAO,CAAC,CAAC,KAAK,QAAQ,EAAE;MACpG,IAAIoJ,iBAAiB,GAAGxD,QAAQ,CAACyD,QAAQ,CAAC,UAAU,CAAC,CAACH,KAAK;MAC3DE,iBAAiB,CAACD,SAAS,GAAG,CAACvD,QAAQ,CAACsD,KAAK,CAACC,SAAS,GAAG,CAAC;IAC7D,CAAC,CAAC;;IAGFrQ,SAAS,CAAC8M,QAAQ,CAAC,CAAC0D,WAAW,GAAGxJ,WAAW,CAACwJ,WAAW;IACzD/Q,mBAAmB,CAACqN,QAAQ,EAAE6C,KAAK,EAAEC,SAAS,EAAEC,gBAAgB,CAAC;IACjE,IAAI9N,MAAM,GAAGD,SAAS,CAACkF,WAAW,CAACE,GAAG,CAAC,QAAQ,CAAC,CAAC;IACjD,IAAIuJ,cAAc,GAAGzJ,WAAW,CAACE,GAAG,CAAC,gBAAgB,CAAC;IACtD4F,QAAQ,CAAC2C,QAAQ,CAAC;MAChB1N,MAAM,EAAEA,MAAM;MACd0O,cAAc,EAAEA,cAAc;MAC9B7N,YAAY,EAAEA;IAChB,CAAC,CAAC;IAEF,IAAIoK,OAAO,EAAE;MACX,IAAI0D,eAAe,GAAGvO,IAAI,CAACwO,kBAAkB,CAAC,iBAAiB,CAAC;MAChE,IAAIC,eAAe,GAAG,CAAC;MACvB5D,OAAO,CAAC8C,QAAQ,CAACrR,MAAM,CAACsR,QAAQ,CAACtD,cAAc,CAACoE,YAAY,CAAC,CAAC,EAAE;QAC9DZ,IAAI,EAAE3B,WAAW;QACjBwC,OAAO,EAAE,GAAG;QACZX,QAAQ,EAAE,OAAO;QACjBY,KAAK,EAAE5O,IAAI,CAACuC,SAAS,CAAC,OAAO,CAAC,CAACqM;MACjC,CAAC,CAAC,CAAC;MAEH,IAAIL,eAAe,EAAE;QACnBE,eAAe,GAAG9O,SAAS,CAAC4O,eAAe,CAACxJ,GAAG,CAAC,QAAQ,CAAC,CAAC;MAC5D;MAEA8F,OAAO,CAACyC,QAAQ,CAAC;QACf1N,MAAM,EAAEA,MAAM;QACd6O,eAAe,EAAEA,eAAe;QAChCH,cAAc,EAAEA,cAAc;QAC9B7N,YAAY,EAAEA;MAChB,CAAC,CAAC;MACFrD,wBAAwB,CAACyN,OAAO,EAAEhG,WAAW,EAAE,WAAW,CAAC,CAAC,CAAC;;MAE7DhH,SAAS,CAACgN,OAAO,CAAC,CAACwD,WAAW,GAAGxJ,WAAW,CAACwJ,WAAW;MACxD/Q,mBAAmB,CAACuN,OAAO,EAAE2C,KAAK,EAAEC,SAAS,EAAEC,gBAAgB,CAAC;IAClE;IAEA,IAAImB,eAAe,GAAG,SAAAA,CAAUC,OAAO,EAAE;MACvC3E,KAAK,CAAC4E,gBAAgB,CAACD,OAAO,CAAC;IACjC,CAAC;IAED9O,IAAI,CAACyL,iBAAiB,CAAC,UAAUC,EAAE,EAAE;MACnC;MACAA,EAAE,KAAKA,EAAE,CAACsD,kBAAkB,GAAGH,eAAe,CAAC;IACjD,CAAC,CAAC;IACF,IAAI,CAACjE,SAAS,CAACoE,kBAAkB,GAAGH,eAAe;IACnD,IAAI,CAACrD,KAAK,GAAGxL,IAAI,CAAC,CAAC;;IAEnB,IAAI,CAAC0K,SAAS,GAAG3K,QAAQ;IACzB,IAAI,CAACoN,gBAAgB,GAAG/B,eAAe;IACvC,IAAI,CAACgC,OAAO,GAAG7O,MAAM;IACrB,IAAI,CAAC6N,KAAK,GAAGjG,IAAI;IACjB,IAAI,CAAC8I,YAAY,GAAG9D,WAAW;IAE/B,IAAItG,WAAW,CAACE,GAAG,CAAC,kBAAkB,CAAC,EAAE;MACvC,IAAI,CAACmK,aAAa,CAACrK,WAAW,EAAE8F,QAAQ,CAAC;MACzCE,OAAO,IAAI,IAAI,CAACqE,aAAa,CAACrK,WAAW,EAAEgG,OAAO,CAAC;IACrD;EACF,CAAC;EAEDzB,QAAQ,CAACI,SAAS,CAAC0F,aAAa,GAAG,UAAUrK,WAAW,EAAE6G,EAAE,EAAE;IAC5D7N,SAAS,CAAC6N,EAAE,CAAC,CAACyD,SAAS,GAAG;MACxBC,aAAa,EAAE,QAAQ;MACvBC,gBAAgB,EAAE,MAAM;MACxBC,cAAc,EAAEzK,WAAW,CAACyK,cAAc;MAC1CjB,WAAW,EAAExJ,WAAW,CAACwJ,WAAW;MACpCkB,UAAU,EAAE1K,WAAW,CAAC2K,IAAI;MAC5BC,UAAU,EAAE;IACd,CAAC;EACH,CAAC;EAEDrG,QAAQ,CAACI,SAAS,CAACkG,SAAS,GAAG,UAAU7K,WAAW,EAAEqF,OAAO,EAAE7H,GAAG,EAAEsN,OAAO,EAAE;IAC3E,IAAI3P,IAAI,GAAG6E,WAAW,CAAC8C,OAAO,CAAC,CAAC;IAChC,IAAI9B,SAAS,GAAGlJ,SAAS,CAACiT,cAAc,CAAC5P,IAAI,EAAE2P,OAAO,CAAC;IAEvD,IAAI,CAACZ,gBAAgB,CAAC,UAAU,CAAC;IAEjC,IAAI,EAAElJ,SAAS,YAAYgK,KAAK,CAAC,IAAIhK,SAAS,IAAI,IAAI,IAAIA,SAAS,IAAI,CAAC,EAAE;MACxE,IAAItH,MAAM,GAAGyB,IAAI,CAACuK,SAAS,CAAC,QAAQ,CAAC;MACrC,IAAIuF,MAAM,GAAG9P,IAAI,CAAC+P,gBAAgB,CAAClK,SAAS,CAAC;MAE7C,IAAI,CAACiK,MAAM,EAAE;QACX;QACA,IAAIjR,CAAC,GAAGN,MAAM,CAACsH,SAAS,GAAG,CAAC,CAAC;QAC7B,IAAI/G,CAAC,GAAGP,MAAM,CAACsH,SAAS,GAAG,CAAC,GAAG,CAAC,CAAC;QAEjC,IAAI9G,KAAK,CAACF,CAAC,CAAC,IAAIE,KAAK,CAACD,CAAC,CAAC,EAAE;UACxB;UACA;QACF,CAAC,CAAC;;QAGF,IAAI,IAAI,CAACoN,mBAAmB,IAAI,CAAC,IAAI,CAACA,mBAAmB,CAAC8D,OAAO,CAACnR,CAAC,EAAEC,CAAC,CAAC,EAAE;UACvE;QACF;QAEA,IAAImR,MAAM,GAAGpL,WAAW,CAACE,GAAG,CAAC,QAAQ,CAAC,IAAI,CAAC;QAC3C,IAAImL,CAAC,GAAGrL,WAAW,CAACE,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC;QACjC+K,MAAM,GAAG,IAAItT,SAAS,CAACwD,IAAI,EAAE6F,SAAS,CAAC;QACvCiK,MAAM,CAACjR,CAAC,GAAGA,CAAC;QACZiR,MAAM,CAAChR,CAAC,GAAGA,CAAC;QACZgR,MAAM,CAACK,IAAI,CAACF,MAAM,EAAEC,CAAC,CAAC,CAAC,CAAC;;QAExB,IAAIE,WAAW,GAAGN,MAAM,CAACO,aAAa,CAAC,CAAC,CAACC,cAAc,CAAC,CAAC;QAEzD,IAAIF,WAAW,EAAE;UACfA,WAAW,CAACH,MAAM,GAAGA,MAAM;UAC3BG,WAAW,CAACF,CAAC,GAAGA,CAAC;UACjBE,WAAW,CAACG,EAAE,GAAG,IAAI,CAAC3F,SAAS,CAAC2F,EAAE,GAAG,CAAC;QACxC;QAEAT,MAAM,CAACnE,MAAM,GAAG,IAAI;QACpB3L,IAAI,CAAC6L,gBAAgB,CAAChG,SAAS,EAAEiK,MAAM,CAAC,CAAC,CAAC;;QAE1CA,MAAM,CAACU,mBAAmB,CAAC,IAAI,CAAC;QAChC,IAAI,CAAC3G,KAAK,CAACC,GAAG,CAACgG,MAAM,CAAC;MACxB;MAEAA,MAAM,CAACJ,SAAS,CAAC,CAAC;IACpB,CAAC,MAAM;MACL;MACA5S,SAAS,CAAC0M,SAAS,CAACkG,SAAS,CAACe,IAAI,CAAC,IAAI,EAAE5L,WAAW,EAAEqF,OAAO,EAAE7H,GAAG,EAAEsN,OAAO,CAAC;IAC9E;EACF,CAAC;EAEDvG,QAAQ,CAACI,SAAS,CAACkH,QAAQ,GAAG,UAAU7L,WAAW,EAAEqF,OAAO,EAAE7H,GAAG,EAAEsN,OAAO,EAAE;IAC1E,IAAI3P,IAAI,GAAG6E,WAAW,CAAC8C,OAAO,CAAC,CAAC;IAChC,IAAI9B,SAAS,GAAGlJ,SAAS,CAACiT,cAAc,CAAC5P,IAAI,EAAE2P,OAAO,CAAC;IAEvD,IAAI,CAACZ,gBAAgB,CAAC,QAAQ,CAAC;IAE/B,IAAIlJ,SAAS,IAAI,IAAI,IAAIA,SAAS,IAAI,CAAC,EAAE;MACvC,IAAIiK,MAAM,GAAG9P,IAAI,CAAC+P,gBAAgB,CAAClK,SAAS,CAAC;MAE7C,IAAIiK,MAAM,EAAE;QACV,IAAIA,MAAM,CAACnE,MAAM,EAAE;UACjB3L,IAAI,CAAC6L,gBAAgB,CAAChG,SAAS,EAAE,IAAI,CAAC;UACtC,IAAI,CAACgE,KAAK,CAAC+B,MAAM,CAACkE,MAAM,CAAC;QAC3B,CAAC,MAAM;UACLA,MAAM,CAACY,QAAQ,CAAC,CAAC;QACnB;MACF;IACF,CAAC,MAAM;MACL;MACA;MACA;MACA5T,SAAS,CAAC0M,SAAS,CAACkH,QAAQ,CAACD,IAAI,CAAC,IAAI,EAAE5L,WAAW,EAAEqF,OAAO,EAAE7H,GAAG,EAAEsN,OAAO,CAAC;IAC7E;EACF,CAAC;EAEDvG,QAAQ,CAACI,SAAS,CAACuF,gBAAgB,GAAG,UAAUD,OAAO,EAAE;IACvD,IAAIjE,OAAO,GAAG,IAAI,CAACC,QAAQ;IAC3BzN,aAAa,CAAC,IAAI,CAACuN,SAAS,EAAEkE,OAAO,CAAC;IACtCjE,OAAO,IAAIxN,aAAa,CAACwN,OAAO,EAAEiE,OAAO,CAAC;EAC5C,CAAC;EAED1F,QAAQ,CAACI,SAAS,CAACmD,YAAY,GAAG,UAAUpO,MAAM,EAAE;IAClD,IAAIoM,QAAQ,GAAG,IAAI,CAACC,SAAS,CAAC,CAAC;;IAE/B,IAAID,QAAQ,EAAE;MACZ,IAAI,CAACX,UAAU,CAAC4B,MAAM,CAACjB,QAAQ,CAAC;IAClC;IAEAA,QAAQ,GAAG,IAAI/N,UAAU,CAAC;MACxB6L,KAAK,EAAE;QACLlK,MAAM,EAAEA;MACV,CAAC;MACDoS,sBAAsB,EAAE,CAAC;MACzBJ,EAAE,EAAE;IACN,CAAC,CAAC;IAEF,IAAI,CAACvG,UAAU,CAACF,GAAG,CAACa,QAAQ,CAAC;IAE7B,IAAI,CAACC,SAAS,GAAGD,QAAQ;IACzB,OAAOA,QAAQ;EACjB,CAAC;EAEDvB,QAAQ,CAACI,SAAS,CAACoD,WAAW,GAAG,UAAUrO,MAAM,EAAE6M,eAAe,EAAE;IAClE,IAAIP,OAAO,GAAG,IAAI,CAACC,QAAQ,CAAC,CAAC;;IAE7B,IAAID,OAAO,EAAE;MACX,IAAI,CAACb,UAAU,CAAC4B,MAAM,CAACf,OAAO,CAAC;IACjC;IAEAA,OAAO,GAAG,IAAIhO,SAAS,CAAC;MACtB4L,KAAK,EAAE;QACLlK,MAAM,EAAEA,MAAM;QACd6M,eAAe,EAAEA;MACnB,CAAC;MACDuF,sBAAsB,EAAE;IAC1B,CAAC,CAAC;IAEF,IAAI,CAAC3G,UAAU,CAACF,GAAG,CAACe,OAAO,CAAC;IAE5B,IAAI,CAACC,QAAQ,GAAGD,OAAO;IACvB,OAAOA,OAAO;EAChB,CAAC;EAEDzB,QAAQ,CAACI,SAAS,CAACkD,yBAAyB,GAAG,UAAU1M,IAAI,EAAED,QAAQ,EAAEwM,SAAS,EAAE;IAClF,IAAIqE,oBAAoB;IACxB,IAAIpG,eAAe;IACnB,IAAI9J,QAAQ,GAAGX,QAAQ,CAACY,WAAW,CAAC,CAAC;IACrC,IAAIkQ,aAAa,GAAGnQ,QAAQ,CAACsI,OAAO;IAEpC,IAAIjJ,QAAQ,CAACyC,IAAI,KAAK,aAAa,EAAE;MACnCoO,oBAAoB,GAAGlQ,QAAQ,CAAC4F,YAAY,CAAC,CAAC;MAC9CkE,eAAe,GAAG,KAAK;IACzB,CAAC,MAAM,IAAIzK,QAAQ,CAACyC,IAAI,KAAK,OAAO,EAAE;MACpCoO,oBAAoB,GAAGlQ,QAAQ,CAACG,GAAG,KAAK,OAAO;MAC/C2J,eAAe,GAAG,IAAI;IACxB;IAEA,IAAI3F,WAAW,GAAG7E,IAAI,CAAC8Q,SAAS;IAChC,IAAIC,cAAc,GAAGlM,WAAW,CAACE,GAAG,CAAC,mBAAmB,CAAC;IAEzD,IAAIzI,MAAM,CAAC0U,UAAU,CAACD,cAAc,CAAC,EAAE;MACrCA,cAAc,GAAGA,cAAc,CAAC,IAAI,CAAC;IACvC;IAEA,IAAIE,WAAW,GAAGpM,WAAW,CAACE,GAAG,CAAC,gBAAgB,CAAC,IAAI,CAAC;IACxD,IAAImM,gBAAgB,GAAG5U,MAAM,CAAC0U,UAAU,CAACC,WAAW,CAAC,GAAGA,WAAW,CAAC,IAAI,CAAC,GAAGA,WAAW;IACvFjR,IAAI,CAACyL,iBAAiB,CAAC,UAAUqE,MAAM,EAAEzP,GAAG,EAAE;MAC5C,IAAIqL,EAAE,GAAGoE,MAAM;MAEf,IAAIpE,EAAE,EAAE;QACN,IAAIyF,KAAK,GAAG,CAACrB,MAAM,CAACjR,CAAC,EAAEiR,MAAM,CAAChR,CAAC,CAAC;QAChC,IAAIsS,KAAK,GAAG,KAAK,CAAC;QAClB,IAAIC,GAAG,GAAG,KAAK,CAAC;QAChB,IAAIC,OAAO,GAAG,KAAK,CAAC;QAEpB,IAAI/E,SAAS,EAAE;UACb,IAAI/B,eAAe,EAAE;YACnB,IAAI+G,SAAS,GAAGhF,SAAS;YACzB,IAAIvK,KAAK,GAAGjC,QAAQ,CAACyR,YAAY,CAACL,KAAK,CAAC;YAExC,IAAIP,oBAAoB,EAAE;cACxBQ,KAAK,GAAGG,SAAS,CAACE,UAAU;cAC5BJ,GAAG,GAAGE,SAAS,CAACG,QAAQ;cACxBJ,OAAO,GAAG,CAACtP,KAAK,CAAC,CAAC,CAAC,GAAG,GAAG,GAAGhD,IAAI,CAAC2S,EAAE;YACrC,CAAC,MAAM;cACLP,KAAK,GAAGG,SAAS,CAACvF,EAAE;cACpBqF,GAAG,GAAGE,SAAS,CAACtF,CAAC;cACjBqF,OAAO,GAAGtP,KAAK,CAAC,CAAC,CAAC;YACpB;UACF,CAAC,MAAM;YACL,IAAI4P,QAAQ,GAAGrF,SAAS;YAExB,IAAIqE,oBAAoB,EAAE;cACxBQ,KAAK,GAAGQ,QAAQ,CAAC/S,CAAC;cAClBwS,GAAG,GAAGO,QAAQ,CAAC/S,CAAC,GAAG+S,QAAQ,CAACjJ,KAAK;cACjC2I,OAAO,GAAGxB,MAAM,CAACjR,CAAC;YACpB,CAAC,MAAM;cACLuS,KAAK,GAAGQ,QAAQ,CAAC9S,CAAC,GAAG8S,QAAQ,CAAChJ,MAAM;cACpCyI,GAAG,GAAGO,QAAQ,CAAC9S,CAAC;cAChBwS,OAAO,GAAGxB,MAAM,CAAChR,CAAC;YACpB;UACF;QACF;QAEA,IAAI+S,KAAK,GAAGR,GAAG,KAAKD,KAAK,GAAG,CAAC,GAAG,CAACE,OAAO,GAAGF,KAAK,KAAKC,GAAG,GAAGD,KAAK,CAAC;QAEjE,IAAIP,aAAa,EAAE;UACjBgB,KAAK,GAAG,CAAC,GAAGA,KAAK;QACnB;QAEA,IAAIC,KAAK,GAAGxV,MAAM,CAAC0U,UAAU,CAACC,WAAW,CAAC,GAAGA,WAAW,CAAC5Q,GAAG,CAAC,GAAG0Q,cAAc,GAAGc,KAAK,GAAGX,gBAAgB;QACzG,IAAIa,UAAU,GAAGrG,EAAE,CAAC2E,aAAa,CAAC,CAAC;QACnC,IAAI2B,IAAI,GAAGD,UAAU,CAACzB,cAAc,CAAC,CAAC;QACtC5E,EAAE,CAACpD,IAAI,CAAC;UACN2J,MAAM,EAAE,CAAC;UACTC,MAAM,EAAE;QACV,CAAC,CAAC;QACFxG,EAAE,CAACyG,SAAS,CAAC;UACXF,MAAM,EAAE,CAAC;UACTC,MAAM,EAAE;QACV,CAAC,EAAE;UACDE,QAAQ,EAAE,GAAG;UACbC,UAAU,EAAE,IAAI;UAChBP,KAAK,EAAEA;QACT,CAAC,CAAC;QAEF,IAAIE,IAAI,EAAE;UACRA,IAAI,CAACM,WAAW,CAAC;YACfrE,KAAK,EAAE;cACLU,OAAO,EAAE;YACX;UACF,CAAC,EAAE;YACDyD,QAAQ,EAAE,GAAG;YACbN,KAAK,EAAEA;UACT,CAAC,CAAC;QACJ;QAEAC,UAAU,CAACQ,qBAAqB,GAAG,IAAI;MACzC;IACF,CAAC,CAAC;EACJ,CAAC;EAEDnJ,QAAQ,CAACI,SAAS,CAACqD,qBAAqB,GAAG,UAAUhI,WAAW,EAAE9E,QAAQ,EAAEyS,YAAY,EAAE;IACxF,IAAI1J,aAAa,GAAGjE,WAAW,CAAC2C,QAAQ,CAAC,UAAU,CAAC;IAEpD,IAAIL,oBAAoB,CAACtC,WAAW,CAAC,EAAE;MACrC,IAAI4N,MAAM,GAAG5N,WAAW,CAAC8C,OAAO,CAAC,CAAC;MAClC,IAAIgD,QAAQ,GAAG,IAAI,CAACC,SAAS,CAAC,CAAC;;MAE/B,IAAIrM,MAAM,GAAGkU,MAAM,CAAClI,SAAS,CAAC,QAAQ,CAAC;MAEvC,IAAI,CAAChM,MAAM,EAAE;QACXoM,QAAQ,CAAC+H,iBAAiB,CAAC,CAAC;QAC5B,IAAI,CAACtK,SAAS,GAAG,IAAI;QACrB;MACF;MAEA,IAAID,QAAQ,GAAG,IAAI,CAACC,SAAS;MAE7B,IAAI,CAACD,QAAQ,EAAE;QACbA,QAAQ,GAAG,IAAI,CAACC,SAAS,GAAG,IAAI1L,OAAO,CAACiW,IAAI,CAAC;UAC3CpC,EAAE,EAAE,GAAG,CAAC;QAEV,CAAC,CAAC;;QACFpI,QAAQ,CAACyK,UAAU,GAAG,IAAI;QAC1BjI,QAAQ,CAACkI,cAAc,CAAC,IAAI,CAACzK,SAAS,CAAC;QACvCuC,QAAQ,CAAC4H,qBAAqB,GAAG,IAAI;MACvC,CAAC,CAAC;;MAGF,IAAI1M,SAAS,GAAGW,mBAAmB,CAACjI,MAAM,CAAC;MAE3C,IAAIsH,SAAS,IAAI,CAAC,EAAE;QAClBrI,aAAa,CAACmN,QAAQ,EAAElN,oBAAoB,CAACoH,WAAW,EAAE,UAAU,CAAC,EAAE;UACrE2N,YAAY,EAAEA,YAAY;UAC1BM,YAAY,EAAEjO,WAAW;UACzBkO,cAAc,EAAElN,SAAS;UACzBmN,WAAW,EAAE,SAAAA,CAAUnN,SAAS,EAAEoN,GAAG,EAAEC,iBAAiB,EAAE;YACxD,OAAOA,iBAAiB,IAAI,IAAI,GAAGtV,2BAA2B,CAAC6U,MAAM,EAAES,iBAAiB,CAAC,GAAGvV,eAAe,CAAC8U,MAAM,EAAE5M,SAAS,CAAC;UAChI,CAAC;UACDsN,gBAAgB,EAAE;QACpB,CAAC,EAAEtK,yBAAyB,CAACC,aAAa,EAAE/I,QAAQ,CAAC,CAAC;QACtD4K,QAAQ,CAACyI,UAAU,CAACC,QAAQ,GAAG,IAAI;MACrC;IACF,CAAC,MAAM,IAAI,IAAI,CAACjL,SAAS,EAAE;MACzB,IAAI,CAACwC,SAAS,CAAC8H,iBAAiB,CAAC,CAAC;MAElC,IAAI,CAACtK,SAAS,GAAG,IAAI;IACvB;EACF,CAAC;EAEDgB,QAAQ,CAACI,SAAS,CAACvB,iBAAiB,GAAG,UAAUF,OAAO,EAAEC,QAAQ,EAAEhI,IAAI,EAAEsT,eAAe,EAAEC,cAAc,EAAEzK,aAAa,EAAE/I,QAAQ,EAAE;IAClI,IAAIoI,QAAQ,GAAG,IAAI,CAACC,SAAS;IAC7B,IAAIuC,QAAQ,GAAG,IAAI,CAACC,SAAS;IAE7B,IAAIzC,QAAQ,EAAE;MACZ;MACA;MACA,IAAIJ,OAAO,GAAG,CAAC,IAAIuL,eAAe,CAACjL,SAAS,IAAI,IAAI,EAAE;QACpDiL,eAAe,CAACjL,SAAS,GAAGF,QAAQ,CAACtJ,CAAC;QACtCyU,eAAe,CAAC/K,SAAS,GAAGJ,QAAQ,CAACrJ,CAAC;MACxC;MAEA,IAAIP,MAAM,GAAGyB,IAAI,CAACuK,SAAS,CAAC,QAAQ,CAAC;MACrC,IAAI1F,WAAW,GAAG7E,IAAI,CAAC8Q,SAAS;MAChC,IAAIrQ,YAAY,GAAGoE,WAAW,CAACE,GAAG,CAAC,cAAc,CAAC;MAClD,IAAIyO,SAAS,GAAG1K,aAAa,CAAC/D,GAAG,CAAC,WAAW,CAAC;MAC9C,IAAI0O,QAAQ,GAAG3K,aAAa,CAAC/D,GAAG,CAAC,UAAU,CAAC,IAAI,CAAC;MACjD,IAAIrE,QAAQ,GAAGX,QAAQ,CAACY,WAAW,CAAC,CAAC;MACrC,IAAI2F,YAAY,GAAG5F,QAAQ,CAAC4F,YAAY,CAAC,CAAC;MAC1C,IAAIyC,cAAc,GAAGrI,QAAQ,CAACsI,OAAO;MACrC,IAAIuD,SAAS,GAAGvE,QAAQ,CAACS,KAAK;MAC9B,IAAI9B,IAAI,GAAGoC,cAAc,GAAGzC,YAAY,GAAGiG,SAAS,CAAC1N,CAAC,GAAG0N,SAAS,CAACzN,CAAC,GAAGyN,SAAS,CAAC3D,MAAM,GAAGtC,YAAY,GAAGiG,SAAS,CAAC1N,CAAC,GAAG0N,SAAS,CAAC5D,KAAK,GAAG4D,SAAS,CAACzN,CAAC;MACpJ,IAAI4U,SAAS,GAAG,CAACpN,YAAY,GAAGmN,QAAQ,GAAG,CAAC,KAAK1K,cAAc,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;MACzE,IAAI4K,SAAS,GAAG,CAACrN,YAAY,GAAG,CAAC,GAAG,CAACmN,QAAQ,KAAK1K,cAAc,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;MAC1E,IAAIlI,GAAG,GAAGyF,YAAY,GAAG,GAAG,GAAG,GAAG;MAClC,IAAIsN,cAAc,GAAGlN,aAAa,CAACnI,MAAM,EAAEoI,IAAI,EAAE9F,GAAG,CAAC;MACrD,IAAIgT,OAAO,GAAGD,cAAc,CAAC3M,KAAK;MAClC,IAAI6M,IAAI,GAAGD,OAAO,CAAC,CAAC,CAAC,GAAGA,OAAO,CAAC,CAAC,CAAC;MAClC,IAAInQ,KAAK,GAAG,KAAK,CAAC;MAElB,IAAIoQ,IAAI,IAAI,CAAC,EAAE;QACb;QACA,IAAIA,IAAI,GAAG,CAAC,IAAI,CAACrT,YAAY,EAAE;UAC7B,IAAIH,EAAE,GAAGmG,eAAe,CAAClI,MAAM,EAAEsV,OAAO,CAAC,CAAC,CAAC,CAAC;UAC5C1L,QAAQ,CAACG,IAAI,CAAC;YACZzJ,CAAC,EAAEyB,EAAE,CAAC,CAAC,CAAC,GAAGoT,SAAS;YACpB5U,CAAC,EAAEwB,EAAE,CAAC,CAAC,CAAC,GAAGqT;UACb,CAAC,CAAC;UACFJ,cAAc,KAAK7P,KAAK,GAAGmB,WAAW,CAACkP,WAAW,CAACF,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;QACjE,CAAC,MAAM;UACL,IAAIvT,EAAE,GAAGqK,QAAQ,CAACqJ,UAAU,CAACrN,IAAI,EAAE9F,GAAG,CAAC;UACvCP,EAAE,IAAI6H,QAAQ,CAACG,IAAI,CAAC;YAClBzJ,CAAC,EAAEyB,EAAE,CAAC,CAAC,CAAC,GAAGoT,SAAS;YACpB5U,CAAC,EAAEwB,EAAE,CAAC,CAAC,CAAC,GAAGqT;UACb,CAAC,CAAC;UACF,IAAIM,UAAU,GAAGpP,WAAW,CAACkP,WAAW,CAACF,OAAO,CAAC,CAAC,CAAC,CAAC;UACpD,IAAIK,QAAQ,GAAGrP,WAAW,CAACkP,WAAW,CAACF,OAAO,CAAC,CAAC,CAAC,CAAC;UAClDN,cAAc,KAAK7P,KAAK,GAAG/G,SAAS,CAACwX,oBAAoB,CAACnU,IAAI,EAAEwT,SAAS,EAAES,UAAU,EAAEC,QAAQ,EAAEN,cAAc,CAAC1M,CAAC,CAAC,CAAC;QACrH;QAEAoM,eAAe,CAACzL,cAAc,GAAGgM,OAAO,CAAC,CAAC,CAAC;MAC7C,CAAC,MAAM;QACL;QACA;QACA,IAAIxT,GAAG,GAAG0H,OAAO,KAAK,CAAC,IAAIuL,eAAe,CAACzL,cAAc,GAAG,CAAC,GAAGgM,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC;QAC9E,IAAIvT,EAAE,GAAGmG,eAAe,CAAClI,MAAM,EAAE8B,GAAG,CAAC;QACrCkT,cAAc,KAAK7P,KAAK,GAAGmB,WAAW,CAACkP,WAAW,CAAC1T,GAAG,CAAC,CAAC;QACxD8H,QAAQ,CAACG,IAAI,CAAC;UACZzJ,CAAC,EAAEyB,EAAE,CAAC,CAAC,CAAC,GAAGoT,SAAS;UACpB5U,CAAC,EAAEwB,EAAE,CAAC,CAAC,CAAC,GAAGqT;QACb,CAAC,CAAC;MACJ;MAEA,IAAIJ,cAAc,EAAE;QAClB,IAAIa,KAAK,GAAG1W,UAAU,CAACyK,QAAQ,CAAC;QAEhC,IAAI,OAAOiM,KAAK,CAACC,YAAY,KAAK,UAAU,EAAE;UAC5CD,KAAK,CAACC,YAAY,CAAC3Q,KAAK,CAAC;QAC3B;MACF;IACF;EACF,CAAC;EACD;AACF;AACA;EACE;;EAGA0F,QAAQ,CAACI,SAAS,CAAC6D,kBAAkB,GAAG,UAAUrN,IAAI,EAAEoL,eAAe,EAAErL,QAAQ,EAAEsC,GAAG,EAAE8D,IAAI,EAAEgF,WAAW,EAAE1K,YAAY,EAAE;IACvH,IAAIkK,QAAQ,GAAG,IAAI,CAACC,SAAS;IAC7B,IAAIC,OAAO,GAAG,IAAI,CAACC,QAAQ;IAC3B,IAAIjG,WAAW,GAAG7E,IAAI,CAAC8Q,SAAS;IAChC,IAAIgD,IAAI,GAAGrX,iBAAiB,CAAC,IAAI,CAAC+O,KAAK,EAAExL,IAAI,EAAE,IAAI,CAACmN,gBAAgB,EAAE/B,eAAe,EAAE,IAAI,CAACV,SAAS,EAAE3K,QAAQ,EAAE,IAAI,CAACkP,YAAY,EAAE9D,WAAW,CAAC;IAChJ,IAAImG,OAAO,GAAGwC,IAAI,CAACxC,OAAO;IAC1B,IAAIgD,gBAAgB,GAAGR,IAAI,CAACQ,gBAAgB;IAC5C,IAAIC,IAAI,GAAGT,IAAI,CAACS,IAAI;IACpB,IAAIC,aAAa,GAAGV,IAAI,CAACU,aAAa;IAEtC,IAAIrO,IAAI,EAAE;MACR;MACAmL,OAAO,GAAG/Q,kBAAkB,CAACuT,IAAI,CAACxC,OAAO,EAAEvR,QAAQ,EAAEoG,IAAI,EAAE1F,YAAY,CAAC;MACxE6T,gBAAgB,GAAG/T,kBAAkB,CAACuT,IAAI,CAACQ,gBAAgB,EAAEvU,QAAQ,EAAEoG,IAAI,EAAE1F,YAAY,CAAC;MAC1F8T,IAAI,GAAGhU,kBAAkB,CAACuT,IAAI,CAACS,IAAI,EAAExU,QAAQ,EAAEoG,IAAI,EAAE1F,YAAY,CAAC;MAClE+T,aAAa,GAAGjU,kBAAkB,CAACuT,IAAI,CAACU,aAAa,EAAEzU,QAAQ,EAAEoG,IAAI,EAAE1F,YAAY,CAAC;IACtF,CAAC,CAAC;IACF;IACA;;IAGA,IAAItB,eAAe,CAACmS,OAAO,EAAEiD,IAAI,CAAC,GAAG,IAAI,IAAI1J,OAAO,IAAI1L,eAAe,CAACmV,gBAAgB,EAAEE,aAAa,CAAC,GAAG,IAAI,EAAE;MAC/G7J,QAAQ,CAAC8J,aAAa,CAAC,CAAC;MACxB9J,QAAQ,CAAC2C,QAAQ,CAAC;QAChB/O,MAAM,EAAEgW;MACV,CAAC,CAAC;MAEF,IAAI1J,OAAO,EAAE;QACXA,OAAO,CAAC4J,aAAa,CAAC,CAAC;QACvB5J,OAAO,CAACyC,QAAQ,CAAC;UACf/O,MAAM,EAAEgW,IAAI;UACZnJ,eAAe,EAAEoJ;QACnB,CAAC,CAAC;MACJ;MAEA;IACF;IAEA7J,QAAQ,CAAClC,KAAK,CAACiM,QAAQ,GAAGZ,IAAI,CAACxC,OAAO;IACtC3G,QAAQ,CAAClC,KAAK,CAAClK,MAAM,GAAG+S,OAAO;IAC/B,IAAIqD,MAAM,GAAG;MACXlM,KAAK,EAAE;QACLlK,MAAM,EAAEgW;MACV;IACF,CAAC,CAAC,CAAC;IACH;;IAEA,IAAIT,IAAI,CAACxC,OAAO,KAAKA,OAAO,EAAE;MAC5BqD,MAAM,CAAClM,KAAK,CAACiM,QAAQ,GAAGZ,IAAI,CAACS,IAAI;IACnC,CAAC,CAAC;;IAGF5J,QAAQ,CAAC8J,aAAa,CAAC,CAAC;IACxB/X,OAAO,CAACkY,WAAW,CAACjK,QAAQ,EAAEgK,MAAM,EAAE9P,WAAW,CAAC;IAElD,IAAIgG,OAAO,EAAE;MACXA,OAAO,CAACyC,QAAQ,CAAC;QACf;QACA/O,MAAM,EAAE+S,OAAO;QACflG,eAAe,EAAEkJ;MACnB,CAAC,CAAC;MACFzJ,OAAO,CAAC4J,aAAa,CAAC,CAAC;MACvB/X,OAAO,CAACkY,WAAW,CAAC/J,OAAO,EAAE;QAC3BpC,KAAK,EAAE;UACL2C,eAAe,EAAEoJ;QACnB;MACF,CAAC,EAAE3P,WAAW,CAAC,CAAC,CAAC;;MAEjB,IAAI8F,QAAQ,CAAClC,KAAK,CAAClK,MAAM,KAAKsM,OAAO,CAACpC,KAAK,CAAClK,MAAM,EAAE;QAClDsM,OAAO,CAACpC,KAAK,CAAClK,MAAM,GAAGoM,QAAQ,CAAClC,KAAK,CAAClK,MAAM;MAC9C;IACF;IAEA,IAAIsW,eAAe,GAAG,EAAE;IACxB,IAAIC,UAAU,GAAGhB,IAAI,CAACiB,MAAM;IAE5B,KAAK,IAAI1W,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGyW,UAAU,CAAC1W,MAAM,EAAEC,CAAC,EAAE,EAAE;MAC1C,IAAI2W,GAAG,GAAGF,UAAU,CAACzW,CAAC,CAAC,CAAC2W,GAAG;MAE3B,IAAIA,GAAG,KAAK,GAAG,EAAE;QACf,IAAItJ,EAAE,GAAG1L,IAAI,CAAC+P,gBAAgB,CAAC+E,UAAU,CAACzW,CAAC,CAAC,CAAC4W,IAAI,CAAC;QAElD,IAAIvJ,EAAE,EAAE;UACNmJ,eAAe,CAAC3T,IAAI,CAAC;YACnBwK,EAAE,EAAEA,EAAE;YACNwJ,KAAK,EAAE7W,CAAC,CAAC;UAEX,CAAC,CAAC;QACJ;MACF;IACF;;IAEA,IAAIsM,QAAQ,CAACwK,SAAS,IAAIxK,QAAQ,CAACwK,SAAS,CAAC/W,MAAM,EAAE;MACnDuM,QAAQ,CAACwK,SAAS,CAAC,CAAC,CAAC,CAACrN,MAAM,CAAC,YAAY;QACvC+C,OAAO,IAAIA,OAAO,CAACuK,UAAU,CAAC,CAAC;QAC/B,IAAI7W,MAAM,GAAGoM,QAAQ,CAAClC,KAAK,CAACiM,QAAQ;QAEpC,KAAK,IAAIrW,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGwW,eAAe,CAACzW,MAAM,EAAEC,CAAC,EAAE,EAAE;UAC/C,IAAIqN,EAAE,GAAGmJ,eAAe,CAACxW,CAAC,CAAC,CAACqN,EAAE;UAC9B,IAAIlH,MAAM,GAAGqQ,eAAe,CAACxW,CAAC,CAAC,CAAC6W,KAAK,GAAG,CAAC;UACzCxJ,EAAE,CAAC7M,CAAC,GAAGN,MAAM,CAACiG,MAAM,CAAC;UACrBkH,EAAE,CAAC5M,CAAC,GAAGP,MAAM,CAACiG,MAAM,GAAG,CAAC,CAAC;UACzBkH,EAAE,CAAC2J,UAAU,CAAC,CAAC;QACjB;MACF,CAAC,CAAC;IACJ;EACF,CAAC;EAEDjM,QAAQ,CAACI,SAAS,CAACoC,MAAM,GAAG,UAAU1B,OAAO,EAAE;IAC7C,IAAIL,KAAK,GAAG,IAAI,CAACA,KAAK;IACtB,IAAI0B,OAAO,GAAG,IAAI,CAACC,KAAK;IAExB,IAAI,CAACxB,UAAU,CAACsL,SAAS,CAAC,CAAC;IAE3B,IAAI,CAACvL,WAAW,CAAC6B,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC;;IAG/BL,OAAO,IAAIA,OAAO,CAACE,iBAAiB,CAAC,UAAUC,EAAE,EAAErL,GAAG,EAAE;MACtD,IAAIqL,EAAE,CAACC,MAAM,EAAE;QACb9B,KAAK,CAAC+B,MAAM,CAACF,EAAE,CAAC;QAChBH,OAAO,CAACM,gBAAgB,CAACxL,GAAG,EAAE,IAAI,CAAC;MACrC;IACF,CAAC,CAAC;IACF,IAAI,CAACuK,SAAS,GAAG,IAAI,CAACE,QAAQ,GAAG,IAAI,CAACJ,SAAS,GAAG,IAAI,CAAC0C,OAAO,GAAG,IAAI,CAACD,gBAAgB,GAAG,IAAI,CAAC/E,SAAS,GAAG,IAAI,CAACoD,KAAK,GAAG,IAAI;EAC7H,CAAC;EAEDpC,QAAQ,CAAC5G,IAAI,GAAG,MAAM;EACtB,OAAO4G,QAAQ;AACjB,CAAC,CAACtM,SAAS,CAAC;AAEZ,eAAesM,QAAQ"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}