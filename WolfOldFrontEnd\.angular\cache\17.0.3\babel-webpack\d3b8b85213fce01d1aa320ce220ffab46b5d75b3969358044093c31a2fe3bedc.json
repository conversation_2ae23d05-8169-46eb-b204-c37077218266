{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport SeriesData from '../../data/SeriesData.js';\nimport Graph from '../../data/Graph.js';\nimport linkSeriesData from '../../data/helper/linkSeriesData.js';\nimport prepareSeriesDataSchema from '../../data/helper/createDimensions.js';\nimport CoordinateSystem from '../../core/CoordinateSystem.js';\nimport createSeriesData from './createSeriesData.js';\nimport { convertOptionIdName } from '../../util/model.js';\nexport default function createGraphFromNodeEdge(nodes, edges, seriesModel, directed, beforeLink) {\n  // ??? TODO\n  // support dataset?\n  var graph = new Graph(directed);\n  for (var i = 0; i < nodes.length; i++) {\n    graph.addNode(zrUtil.retrieve(\n    // Id, name, dataIndex\n    nodes[i].id, nodes[i].name, i), i);\n  }\n  var linkNameList = [];\n  var validEdges = [];\n  var linkCount = 0;\n  for (var i = 0; i < edges.length; i++) {\n    var link = edges[i];\n    var source = link.source;\n    var target = link.target; // addEdge may fail when source or target not exists\n\n    if (graph.addEdge(source, target, linkCount)) {\n      validEdges.push(link);\n      linkNameList.push(zrUtil.retrieve(convertOptionIdName(link.id, null), source + ' > ' + target));\n      linkCount++;\n    }\n  }\n  var coordSys = seriesModel.get('coordinateSystem');\n  var nodeData;\n  if (coordSys === 'cartesian2d' || coordSys === 'polar') {\n    nodeData = createSeriesData(nodes, seriesModel);\n  } else {\n    var coordSysCtor = CoordinateSystem.get(coordSys);\n    var coordDimensions = coordSysCtor ? coordSysCtor.dimensions || [] : []; // FIXME: Some geo do not need `value` dimenson, whereas `calendar` needs\n    // `value` dimension, but graph need `value` dimension. It's better to\n    // uniform this behavior.\n\n    if (zrUtil.indexOf(coordDimensions, 'value') < 0) {\n      coordDimensions.concat(['value']);\n    }\n    var dimensions = prepareSeriesDataSchema(nodes, {\n      coordDimensions: coordDimensions,\n      encodeDefine: seriesModel.getEncode()\n    }).dimensions;\n    nodeData = new SeriesData(dimensions, seriesModel);\n    nodeData.initData(nodes);\n  }\n  var edgeData = new SeriesData(['value'], seriesModel);\n  edgeData.initData(validEdges, linkNameList);\n  beforeLink && beforeLink(nodeData, edgeData);\n  linkSeriesData({\n    mainData: nodeData,\n    struct: graph,\n    structAttr: 'graph',\n    datas: {\n      node: nodeData,\n      edge: edgeData\n    },\n    datasAttr: {\n      node: 'data',\n      edge: 'edgeData'\n    }\n  }); // Update dataIndex of nodes and edges because invalid edge may be removed\n\n  graph.update();\n  return graph;\n}", "map": {"version": 3, "names": ["zrUtil", "SeriesData", "Graph", "linkSeriesData", "prepareSeriesDataSchema", "CoordinateSystem", "createSeriesData", "convertOptionIdName", "createGraphFromNodeEdge", "nodes", "edges", "seriesModel", "directed", "beforeLink", "graph", "i", "length", "addNode", "retrieve", "id", "name", "linkNameList", "validEdges", "linkCount", "link", "source", "target", "addEdge", "push", "coordSys", "get", "nodeData", "coordSysCtor", "coordDimensions", "dimensions", "indexOf", "concat", "encodeDefine", "getEncode", "initData", "edgeData", "mainData", "struct", "structAttr", "datas", "node", "edge", "datasAttr", "update"], "sources": ["C:/WolfSystemV2/WolfOldFrontEnd/node_modules/echarts/lib/chart/helper/createGraphFromNodeEdge.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport SeriesData from '../../data/SeriesData.js';\nimport Graph from '../../data/Graph.js';\nimport linkSeriesData from '../../data/helper/linkSeriesData.js';\nimport prepareSeriesDataSchema from '../../data/helper/createDimensions.js';\nimport CoordinateSystem from '../../core/CoordinateSystem.js';\nimport createSeriesData from './createSeriesData.js';\nimport { convertOptionIdName } from '../../util/model.js';\nexport default function createGraphFromNodeEdge(nodes, edges, seriesModel, directed, beforeLink) {\n  // ??? TODO\n  // support dataset?\n  var graph = new Graph(directed);\n\n  for (var i = 0; i < nodes.length; i++) {\n    graph.addNode(zrUtil.retrieve( // Id, name, dataIndex\n    nodes[i].id, nodes[i].name, i), i);\n  }\n\n  var linkNameList = [];\n  var validEdges = [];\n  var linkCount = 0;\n\n  for (var i = 0; i < edges.length; i++) {\n    var link = edges[i];\n    var source = link.source;\n    var target = link.target; // addEdge may fail when source or target not exists\n\n    if (graph.addEdge(source, target, linkCount)) {\n      validEdges.push(link);\n      linkNameList.push(zrUtil.retrieve(convertOptionIdName(link.id, null), source + ' > ' + target));\n      linkCount++;\n    }\n  }\n\n  var coordSys = seriesModel.get('coordinateSystem');\n  var nodeData;\n\n  if (coordSys === 'cartesian2d' || coordSys === 'polar') {\n    nodeData = createSeriesData(nodes, seriesModel);\n  } else {\n    var coordSysCtor = CoordinateSystem.get(coordSys);\n    var coordDimensions = coordSysCtor ? coordSysCtor.dimensions || [] : []; // FIXME: Some geo do not need `value` dimenson, whereas `calendar` needs\n    // `value` dimension, but graph need `value` dimension. It's better to\n    // uniform this behavior.\n\n    if (zrUtil.indexOf(coordDimensions, 'value') < 0) {\n      coordDimensions.concat(['value']);\n    }\n\n    var dimensions = prepareSeriesDataSchema(nodes, {\n      coordDimensions: coordDimensions,\n      encodeDefine: seriesModel.getEncode()\n    }).dimensions;\n    nodeData = new SeriesData(dimensions, seriesModel);\n    nodeData.initData(nodes);\n  }\n\n  var edgeData = new SeriesData(['value'], seriesModel);\n  edgeData.initData(validEdges, linkNameList);\n  beforeLink && beforeLink(nodeData, edgeData);\n  linkSeriesData({\n    mainData: nodeData,\n    struct: graph,\n    structAttr: 'graph',\n    datas: {\n      node: nodeData,\n      edge: edgeData\n    },\n    datasAttr: {\n      node: 'data',\n      edge: 'edgeData'\n    }\n  }); // Update dataIndex of nodes and edges because invalid edge may be removed\n\n  graph.update();\n  return graph;\n}"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,KAAKA,MAAM,MAAM,0BAA0B;AAClD,OAAOC,UAAU,MAAM,0BAA0B;AACjD,OAAOC,KAAK,MAAM,qBAAqB;AACvC,OAAOC,cAAc,MAAM,qCAAqC;AAChE,OAAOC,uBAAuB,MAAM,uCAAuC;AAC3E,OAAOC,gBAAgB,MAAM,gCAAgC;AAC7D,OAAOC,gBAAgB,MAAM,uBAAuB;AACpD,SAASC,mBAAmB,QAAQ,qBAAqB;AACzD,eAAe,SAASC,uBAAuBA,CAACC,KAAK,EAAEC,KAAK,EAAEC,WAAW,EAAEC,QAAQ,EAAEC,UAAU,EAAE;EAC/F;EACA;EACA,IAAIC,KAAK,GAAG,IAAIZ,KAAK,CAACU,QAAQ,CAAC;EAE/B,KAAK,IAAIG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGN,KAAK,CAACO,MAAM,EAAED,CAAC,EAAE,EAAE;IACrCD,KAAK,CAACG,OAAO,CAACjB,MAAM,CAACkB,QAAQ;IAAE;IAC/BT,KAAK,CAACM,CAAC,CAAC,CAACI,EAAE,EAAEV,KAAK,CAACM,CAAC,CAAC,CAACK,IAAI,EAAEL,CAAC,CAAC,EAAEA,CAAC,CAAC;EACpC;EAEA,IAAIM,YAAY,GAAG,EAAE;EACrB,IAAIC,UAAU,GAAG,EAAE;EACnB,IAAIC,SAAS,GAAG,CAAC;EAEjB,KAAK,IAAIR,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGL,KAAK,CAACM,MAAM,EAAED,CAAC,EAAE,EAAE;IACrC,IAAIS,IAAI,GAAGd,KAAK,CAACK,CAAC,CAAC;IACnB,IAAIU,MAAM,GAAGD,IAAI,CAACC,MAAM;IACxB,IAAIC,MAAM,GAAGF,IAAI,CAACE,MAAM,CAAC,CAAC;;IAE1B,IAAIZ,KAAK,CAACa,OAAO,CAACF,MAAM,EAAEC,MAAM,EAAEH,SAAS,CAAC,EAAE;MAC5CD,UAAU,CAACM,IAAI,CAACJ,IAAI,CAAC;MACrBH,YAAY,CAACO,IAAI,CAAC5B,MAAM,CAACkB,QAAQ,CAACX,mBAAmB,CAACiB,IAAI,CAACL,EAAE,EAAE,IAAI,CAAC,EAAEM,MAAM,GAAG,KAAK,GAAGC,MAAM,CAAC,CAAC;MAC/FH,SAAS,EAAE;IACb;EACF;EAEA,IAAIM,QAAQ,GAAGlB,WAAW,CAACmB,GAAG,CAAC,kBAAkB,CAAC;EAClD,IAAIC,QAAQ;EAEZ,IAAIF,QAAQ,KAAK,aAAa,IAAIA,QAAQ,KAAK,OAAO,EAAE;IACtDE,QAAQ,GAAGzB,gBAAgB,CAACG,KAAK,EAAEE,WAAW,CAAC;EACjD,CAAC,MAAM;IACL,IAAIqB,YAAY,GAAG3B,gBAAgB,CAACyB,GAAG,CAACD,QAAQ,CAAC;IACjD,IAAII,eAAe,GAAGD,YAAY,GAAGA,YAAY,CAACE,UAAU,IAAI,EAAE,GAAG,EAAE,CAAC,CAAC;IACzE;IACA;;IAEA,IAAIlC,MAAM,CAACmC,OAAO,CAACF,eAAe,EAAE,OAAO,CAAC,GAAG,CAAC,EAAE;MAChDA,eAAe,CAACG,MAAM,CAAC,CAAC,OAAO,CAAC,CAAC;IACnC;IAEA,IAAIF,UAAU,GAAG9B,uBAAuB,CAACK,KAAK,EAAE;MAC9CwB,eAAe,EAAEA,eAAe;MAChCI,YAAY,EAAE1B,WAAW,CAAC2B,SAAS,CAAC;IACtC,CAAC,CAAC,CAACJ,UAAU;IACbH,QAAQ,GAAG,IAAI9B,UAAU,CAACiC,UAAU,EAAEvB,WAAW,CAAC;IAClDoB,QAAQ,CAACQ,QAAQ,CAAC9B,KAAK,CAAC;EAC1B;EAEA,IAAI+B,QAAQ,GAAG,IAAIvC,UAAU,CAAC,CAAC,OAAO,CAAC,EAAEU,WAAW,CAAC;EACrD6B,QAAQ,CAACD,QAAQ,CAACjB,UAAU,EAAED,YAAY,CAAC;EAC3CR,UAAU,IAAIA,UAAU,CAACkB,QAAQ,EAAES,QAAQ,CAAC;EAC5CrC,cAAc,CAAC;IACbsC,QAAQ,EAAEV,QAAQ;IAClBW,MAAM,EAAE5B,KAAK;IACb6B,UAAU,EAAE,OAAO;IACnBC,KAAK,EAAE;MACLC,IAAI,EAAEd,QAAQ;MACde,IAAI,EAAEN;IACR,CAAC;IACDO,SAAS,EAAE;MACTF,IAAI,EAAE,MAAM;MACZC,IAAI,EAAE;IACR;EACF,CAAC,CAAC,CAAC,CAAC;;EAEJhC,KAAK,CAACkC,MAAM,CAAC,CAAC;EACd,OAAOlC,KAAK;AACd"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}