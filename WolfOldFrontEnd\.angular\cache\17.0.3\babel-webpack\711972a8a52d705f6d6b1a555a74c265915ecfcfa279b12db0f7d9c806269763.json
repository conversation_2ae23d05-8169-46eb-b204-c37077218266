{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport * as zrUtil from 'zrender/lib/core/util.js';\nfunction dataToCoordSize(dataSize, dataItem) {\n  // dataItem is necessary in log axis.\n  dataItem = dataItem || [0, 0];\n  return zrUtil.map(['x', 'y'], function (dim, dimIdx) {\n    var axis = this.getAxis(dim);\n    var val = dataItem[dimIdx];\n    var halfSize = dataSize[dimIdx] / 2;\n    return axis.type === 'category' ? axis.getBandWidth() : Math.abs(axis.dataToCoord(val - halfSize) - axis.dataToCoord(val + halfSize));\n  }, this);\n}\nexport default function cartesianPrepareCustom(coordSys) {\n  var rect = coordSys.master.getRect();\n  return {\n    coordSys: {\n      // The name exposed to user is always 'cartesian2d' but not 'grid'.\n      type: 'cartesian2d',\n      x: rect.x,\n      y: rect.y,\n      width: rect.width,\n      height: rect.height\n    },\n    api: {\n      coord: function (data) {\n        // do not provide \"out\" param\n        return coordSys.dataToPoint(data);\n      },\n      size: zrUtil.bind(dataToCoordSize, coordSys)\n    }\n  };\n}", "map": {"version": 3, "names": ["zrUtil", "dataToCoordSize", "dataSize", "dataItem", "map", "dim", "dimIdx", "axis", "getAxis", "val", "halfSize", "type", "getBandWidth", "Math", "abs", "dataToCoord", "cartesianPrepareCustom", "coordSys", "rect", "master", "getRect", "x", "y", "width", "height", "api", "coord", "data", "dataToPoint", "size", "bind"], "sources": ["C:/WolfSystemV2/WolfOldFrontEnd/node_modules/echarts/lib/coord/cartesian/prepareCustom.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport * as zrUtil from 'zrender/lib/core/util.js';\n\nfunction dataToCoordSize(dataSize, dataItem) {\n  // dataItem is necessary in log axis.\n  dataItem = dataItem || [0, 0];\n  return zrUtil.map(['x', 'y'], function (dim, dimIdx) {\n    var axis = this.getAxis(dim);\n    var val = dataItem[dimIdx];\n    var halfSize = dataSize[dimIdx] / 2;\n    return axis.type === 'category' ? axis.getBandWidth() : Math.abs(axis.dataToCoord(val - halfSize) - axis.dataToCoord(val + halfSize));\n  }, this);\n}\n\nexport default function cartesianPrepareCustom(coordSys) {\n  var rect = coordSys.master.getRect();\n  return {\n    coordSys: {\n      // The name exposed to user is always 'cartesian2d' but not 'grid'.\n      type: 'cartesian2d',\n      x: rect.x,\n      y: rect.y,\n      width: rect.width,\n      height: rect.height\n    },\n    api: {\n      coord: function (data) {\n        // do not provide \"out\" param\n        return coordSys.dataToPoint(data);\n      },\n      size: zrUtil.bind(dataToCoordSize, coordSys)\n    }\n  };\n}"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,KAAKA,MAAM,MAAM,0BAA0B;AAElD,SAASC,eAAeA,CAACC,QAAQ,EAAEC,QAAQ,EAAE;EAC3C;EACAA,QAAQ,GAAGA,QAAQ,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC;EAC7B,OAAOH,MAAM,CAACI,GAAG,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC,EAAE,UAAUC,GAAG,EAAEC,MAAM,EAAE;IACnD,IAAIC,IAAI,GAAG,IAAI,CAACC,OAAO,CAACH,GAAG,CAAC;IAC5B,IAAII,GAAG,GAAGN,QAAQ,CAACG,MAAM,CAAC;IAC1B,IAAII,QAAQ,GAAGR,QAAQ,CAACI,MAAM,CAAC,GAAG,CAAC;IACnC,OAAOC,IAAI,CAACI,IAAI,KAAK,UAAU,GAAGJ,IAAI,CAACK,YAAY,CAAC,CAAC,GAAGC,IAAI,CAACC,GAAG,CAACP,IAAI,CAACQ,WAAW,CAACN,GAAG,GAAGC,QAAQ,CAAC,GAAGH,IAAI,CAACQ,WAAW,CAACN,GAAG,GAAGC,QAAQ,CAAC,CAAC;EACvI,CAAC,EAAE,IAAI,CAAC;AACV;AAEA,eAAe,SAASM,sBAAsBA,CAACC,QAAQ,EAAE;EACvD,IAAIC,IAAI,GAAGD,QAAQ,CAACE,MAAM,CAACC,OAAO,CAAC,CAAC;EACpC,OAAO;IACLH,QAAQ,EAAE;MACR;MACAN,IAAI,EAAE,aAAa;MACnBU,CAAC,EAAEH,IAAI,CAACG,CAAC;MACTC,CAAC,EAAEJ,IAAI,CAACI,CAAC;MACTC,KAAK,EAAEL,IAAI,CAACK,KAAK;MACjBC,MAAM,EAAEN,IAAI,CAACM;IACf,CAAC;IACDC,GAAG,EAAE;MACHC,KAAK,EAAE,SAAAA,CAAUC,IAAI,EAAE;QACrB;QACA,OAAOV,QAAQ,CAACW,WAAW,CAACD,IAAI,CAAC;MACnC,CAAC;MACDE,IAAI,EAAE7B,MAAM,CAAC8B,IAAI,CAAC7B,eAAe,EAAEgB,QAAQ;IAC7C;EACF,CAAC;AACH"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}