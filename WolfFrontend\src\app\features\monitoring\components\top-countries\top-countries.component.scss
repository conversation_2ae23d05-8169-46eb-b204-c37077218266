.top-countries-container {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.countries-list {
  display: flex;
  flex-direction: column;
  gap: 15px;
  flex: 1;
}

.country-item {
  display: grid;
  grid-template-columns: 30px 40px 1fr 60px;
  gap: 10px;
  align-items: center;
  padding: 12px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 10px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;

  &:hover {
    background: rgba(255, 255, 255, 0.15);
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
  }

  &.first {
    border-color: #ffd700;
    box-shadow: 0 0 15px rgba(255, 215, 0, 0.3);
    
    .country-rank {
      background: linear-gradient(135deg, #ffd700, #ffed4e);
      color: #333;
    }
  }

  &.second {
    border-color: #c0c0c0;
    box-shadow: 0 0 10px rgba(192, 192, 192, 0.3);
    
    .country-rank {
      background: linear-gradient(135deg, #c0c0c0, #e8e8e8);
      color: #333;
    }
  }

  &.third {
    border-color: #cd7f32;
    box-shadow: 0 0 10px rgba(205, 127, 50, 0.3);
    
    .country-rank {
      background: linear-gradient(135deg, #cd7f32, #daa520);
      color: #fff;
    }
  }
}

.country-rank {
  width: 25px;
  height: 25px;
  border-radius: 50%;
  background: linear-gradient(135deg, #4ecdc4, #44a08d);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  font-size: 0.9rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

.country-flag {
  font-size: 1.8rem;
  text-align: center;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));
}

.country-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
  min-width: 0;
}

.country-name {
  font-weight: 600;
  font-size: 0.95rem;
  color: #fff;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.country-stats {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.connections {
  font-size: 0.8rem;
  color: #4ecdc4;
  font-weight: 500;
}

.percentage {
  font-size: 0.75rem;
  color: #ff6b6b;
  font-weight: bold;
}

.country-bar {
  width: 50px;
  height: 6px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 3px;
  overflow: hidden;
  position: relative;
}

.bar-fill {
  height: 100%;
  border-radius: 3px;
  transition: width 0.8s ease;
  position: relative;
  
  &.high {
    background: linear-gradient(90deg, #ff6b6b, #ff8e8e);
    box-shadow: 0 0 8px rgba(255, 107, 107, 0.5);
  }
  
  &.medium {
    background: linear-gradient(90deg, #ffa726, #ffcc02);
    box-shadow: 0 0 8px rgba(255, 167, 38, 0.5);
  }
  
  &.low {
    background: linear-gradient(90deg, #4ecdc4, #44a08d);
    box-shadow: 0 0 8px rgba(78, 205, 196, 0.5);
  }

  &::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    animation: shimmer 2s infinite;
  }
}

@keyframes shimmer {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: rgba(255, 255, 255, 0.6);
  text-align: center;
}

.empty-icon {
  font-size: 3rem;
  margin-bottom: 10px;
  opacity: 0.7;
}

.empty-text {
  font-size: 1rem;
  font-weight: 500;
}

/* Animação de entrada */
.country-item {
  animation: slideInLeft 0.5s ease forwards;
}

.country-item:nth-child(1) { animation-delay: 0.1s; }
.country-item:nth-child(2) { animation-delay: 0.2s; }
.country-item:nth-child(3) { animation-delay: 0.3s; }

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Responsividade */
@media (max-width: 768px) {
  .country-item {
    grid-template-columns: 25px 35px 1fr 50px;
    gap: 8px;
    padding: 10px;
  }
  
  .country-flag {
    font-size: 1.5rem;
  }
  
  .country-name {
    font-size: 0.85rem;
  }
  
  .connections {
    font-size: 0.75rem;
  }
  
  .percentage {
    font-size: 0.7rem;
  }
}
