{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport * as graphic from '../../util/graphic.js';\nimport { getECData } from '../../util/innerStore.js';\nimport * as layout from '../../util/layout.js';\nimport { wrapTreePathInfo } from '../helper/treeHelper.js';\nimport { curry, defaults } from 'zrender/lib/core/util.js';\nimport { convertOptionIdName } from '../../util/model.js';\nimport { toggleHoverEmphasis, Z2_EMPHASIS_LIFT } from '../../util/states.js';\nimport { createTextStyle } from '../../label/labelStyle.js';\nvar TEXT_PADDING = 8;\nvar ITEM_GAP = 8;\nvar ARRAY_LENGTH = 5;\nvar Breadcrumb = /** @class */\nfunction () {\n  function Breadcrumb(containerGroup) {\n    this.group = new graphic.Group();\n    containerGroup.add(this.group);\n  }\n  Breadcrumb.prototype.render = function (seriesModel, api, targetNode, onSelect) {\n    var model = seriesModel.getModel('breadcrumb');\n    var thisGroup = this.group;\n    thisGroup.removeAll();\n    if (!model.get('show') || !targetNode) {\n      return;\n    }\n    var normalStyleModel = model.getModel('itemStyle');\n    var emphasisModel = model.getModel('emphasis');\n    var textStyleModel = normalStyleModel.getModel('textStyle');\n    var emphasisTextStyleModel = emphasisModel.getModel(['itemStyle', 'textStyle']);\n    var layoutParam = {\n      pos: {\n        left: model.get('left'),\n        right: model.get('right'),\n        top: model.get('top'),\n        bottom: model.get('bottom')\n      },\n      box: {\n        width: api.getWidth(),\n        height: api.getHeight()\n      },\n      emptyItemWidth: model.get('emptyItemWidth'),\n      totalWidth: 0,\n      renderList: []\n    };\n    this._prepare(targetNode, layoutParam, textStyleModel);\n    this._renderContent(seriesModel, layoutParam, normalStyleModel, emphasisModel, textStyleModel, emphasisTextStyleModel, onSelect);\n    layout.positionElement(thisGroup, layoutParam.pos, layoutParam.box);\n  };\n  /**\r\n   * Prepare render list and total width\r\n   * @private\r\n   */\n\n  Breadcrumb.prototype._prepare = function (targetNode, layoutParam, textStyleModel) {\n    for (var node = targetNode; node; node = node.parentNode) {\n      var text = convertOptionIdName(node.getModel().get('name'), '');\n      var textRect = textStyleModel.getTextRect(text);\n      var itemWidth = Math.max(textRect.width + TEXT_PADDING * 2, layoutParam.emptyItemWidth);\n      layoutParam.totalWidth += itemWidth + ITEM_GAP;\n      layoutParam.renderList.push({\n        node: node,\n        text: text,\n        width: itemWidth\n      });\n    }\n  };\n  /**\r\n   * @private\r\n   */\n\n  Breadcrumb.prototype._renderContent = function (seriesModel, layoutParam, normalStyleModel, emphasisModel, textStyleModel, emphasisTextStyleModel, onSelect) {\n    // Start rendering.\n    var lastX = 0;\n    var emptyItemWidth = layoutParam.emptyItemWidth;\n    var height = seriesModel.get(['breadcrumb', 'height']);\n    var availableSize = layout.getAvailableSize(layoutParam.pos, layoutParam.box);\n    var totalWidth = layoutParam.totalWidth;\n    var renderList = layoutParam.renderList;\n    var emphasisItemStyle = emphasisModel.getModel('itemStyle').getItemStyle();\n    for (var i = renderList.length - 1; i >= 0; i--) {\n      var item = renderList[i];\n      var itemNode = item.node;\n      var itemWidth = item.width;\n      var text = item.text; // Hdie text and shorten width if necessary.\n\n      if (totalWidth > availableSize.width) {\n        totalWidth -= itemWidth - emptyItemWidth;\n        itemWidth = emptyItemWidth;\n        text = null;\n      }\n      var el = new graphic.Polygon({\n        shape: {\n          points: makeItemPoints(lastX, 0, itemWidth, height, i === renderList.length - 1, i === 0)\n        },\n        style: defaults(normalStyleModel.getItemStyle(), {\n          lineJoin: 'bevel'\n        }),\n        textContent: new graphic.Text({\n          style: createTextStyle(textStyleModel, {\n            text: text\n          })\n        }),\n        textConfig: {\n          position: 'inside'\n        },\n        z2: Z2_EMPHASIS_LIFT * 1e4,\n        onclick: curry(onSelect, itemNode)\n      });\n      el.disableLabelAnimation = true;\n      el.getTextContent().ensureState('emphasis').style = createTextStyle(emphasisTextStyleModel, {\n        text: text\n      });\n      el.ensureState('emphasis').style = emphasisItemStyle;\n      toggleHoverEmphasis(el, emphasisModel.get('focus'), emphasisModel.get('blurScope'), emphasisModel.get('disabled'));\n      this.group.add(el);\n      packEventData(el, seriesModel, itemNode);\n      lastX += itemWidth + ITEM_GAP;\n    }\n  };\n  Breadcrumb.prototype.remove = function () {\n    this.group.removeAll();\n  };\n  return Breadcrumb;\n}();\nfunction makeItemPoints(x, y, itemWidth, itemHeight, head, tail) {\n  var points = [[head ? x : x - ARRAY_LENGTH, y], [x + itemWidth, y], [x + itemWidth, y + itemHeight], [head ? x : x - ARRAY_LENGTH, y + itemHeight]];\n  !tail && points.splice(2, 0, [x + itemWidth + ARRAY_LENGTH, y + itemHeight / 2]);\n  !head && points.push([x, y + itemHeight / 2]);\n  return points;\n} // Package custom mouse event.\n\nfunction packEventData(el, seriesModel, itemNode) {\n  getECData(el).eventData = {\n    componentType: 'series',\n    componentSubType: 'treemap',\n    componentIndex: seriesModel.componentIndex,\n    seriesIndex: seriesModel.seriesIndex,\n    seriesName: seriesModel.name,\n    seriesType: 'treemap',\n    selfType: 'breadcrumb',\n    nodeData: {\n      dataIndex: itemNode && itemNode.dataIndex,\n      name: itemNode && itemNode.name\n    },\n    treePathInfo: itemNode && wrapTreePathInfo(itemNode, seriesModel)\n  };\n}\nexport default Breadcrumb;", "map": {"version": 3, "names": ["graphic", "getECData", "layout", "wrapTreePathInfo", "curry", "defaults", "convertOptionIdName", "toggleHoverEmphasis", "Z2_EMPHASIS_LIFT", "createTextStyle", "TEXT_PADDING", "ITEM_GAP", "ARRAY_LENGTH", "Breadcrumb", "containerGroup", "group", "Group", "add", "prototype", "render", "seriesModel", "api", "targetNode", "onSelect", "model", "getModel", "thisGroup", "removeAll", "get", "normalStyleModel", "emphasisModel", "textStyleModel", "emphasisTextStyleModel", "layoutPara<PERSON>", "pos", "left", "right", "top", "bottom", "box", "width", "getWidth", "height", "getHeight", "emptyItemWidth", "totalWidth", "renderList", "_prepare", "_renderContent", "positionElement", "node", "parentNode", "text", "textRect", "getTextRect", "itemWidth", "Math", "max", "push", "lastX", "availableSize", "getAvailableSize", "emphasisItemStyle", "getItemStyle", "i", "length", "item", "itemNode", "el", "Polygon", "shape", "points", "makeItemPoints", "style", "lineJoin", "textContent", "Text", "textConfig", "position", "z2", "onclick", "disableLabelAnimation", "getTextContent", "ensureState", "packEventData", "remove", "x", "y", "itemHeight", "head", "tail", "splice", "eventData", "componentType", "componentSubType", "componentIndex", "seriesIndex", "seriesName", "name", "seriesType", "selfType", "nodeData", "dataIndex", "treePathInfo"], "sources": ["C:/WolfSystemV2/WolfOldFrontEnd/node_modules/echarts/lib/chart/treemap/Breadcrumb.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport * as graphic from '../../util/graphic.js';\nimport { getECData } from '../../util/innerStore.js';\nimport * as layout from '../../util/layout.js';\nimport { wrapTreePathInfo } from '../helper/treeHelper.js';\nimport { curry, defaults } from 'zrender/lib/core/util.js';\nimport { convertOptionIdName } from '../../util/model.js';\nimport { toggleHoverEmphasis, Z2_EMPHASIS_LIFT } from '../../util/states.js';\nimport { createTextStyle } from '../../label/labelStyle.js';\nvar TEXT_PADDING = 8;\nvar ITEM_GAP = 8;\nvar ARRAY_LENGTH = 5;\n\nvar Breadcrumb =\n/** @class */\nfunction () {\n  function Breadcrumb(containerGroup) {\n    this.group = new graphic.Group();\n    containerGroup.add(this.group);\n  }\n\n  Breadcrumb.prototype.render = function (seriesModel, api, targetNode, onSelect) {\n    var model = seriesModel.getModel('breadcrumb');\n    var thisGroup = this.group;\n    thisGroup.removeAll();\n\n    if (!model.get('show') || !targetNode) {\n      return;\n    }\n\n    var normalStyleModel = model.getModel('itemStyle');\n    var emphasisModel = model.getModel('emphasis');\n    var textStyleModel = normalStyleModel.getModel('textStyle');\n    var emphasisTextStyleModel = emphasisModel.getModel(['itemStyle', 'textStyle']);\n    var layoutParam = {\n      pos: {\n        left: model.get('left'),\n        right: model.get('right'),\n        top: model.get('top'),\n        bottom: model.get('bottom')\n      },\n      box: {\n        width: api.getWidth(),\n        height: api.getHeight()\n      },\n      emptyItemWidth: model.get('emptyItemWidth'),\n      totalWidth: 0,\n      renderList: []\n    };\n\n    this._prepare(targetNode, layoutParam, textStyleModel);\n\n    this._renderContent(seriesModel, layoutParam, normalStyleModel, emphasisModel, textStyleModel, emphasisTextStyleModel, onSelect);\n\n    layout.positionElement(thisGroup, layoutParam.pos, layoutParam.box);\n  };\n  /**\r\n   * Prepare render list and total width\r\n   * @private\r\n   */\n\n\n  Breadcrumb.prototype._prepare = function (targetNode, layoutParam, textStyleModel) {\n    for (var node = targetNode; node; node = node.parentNode) {\n      var text = convertOptionIdName(node.getModel().get('name'), '');\n      var textRect = textStyleModel.getTextRect(text);\n      var itemWidth = Math.max(textRect.width + TEXT_PADDING * 2, layoutParam.emptyItemWidth);\n      layoutParam.totalWidth += itemWidth + ITEM_GAP;\n      layoutParam.renderList.push({\n        node: node,\n        text: text,\n        width: itemWidth\n      });\n    }\n  };\n  /**\r\n   * @private\r\n   */\n\n\n  Breadcrumb.prototype._renderContent = function (seriesModel, layoutParam, normalStyleModel, emphasisModel, textStyleModel, emphasisTextStyleModel, onSelect) {\n    // Start rendering.\n    var lastX = 0;\n    var emptyItemWidth = layoutParam.emptyItemWidth;\n    var height = seriesModel.get(['breadcrumb', 'height']);\n    var availableSize = layout.getAvailableSize(layoutParam.pos, layoutParam.box);\n    var totalWidth = layoutParam.totalWidth;\n    var renderList = layoutParam.renderList;\n    var emphasisItemStyle = emphasisModel.getModel('itemStyle').getItemStyle();\n\n    for (var i = renderList.length - 1; i >= 0; i--) {\n      var item = renderList[i];\n      var itemNode = item.node;\n      var itemWidth = item.width;\n      var text = item.text; // Hdie text and shorten width if necessary.\n\n      if (totalWidth > availableSize.width) {\n        totalWidth -= itemWidth - emptyItemWidth;\n        itemWidth = emptyItemWidth;\n        text = null;\n      }\n\n      var el = new graphic.Polygon({\n        shape: {\n          points: makeItemPoints(lastX, 0, itemWidth, height, i === renderList.length - 1, i === 0)\n        },\n        style: defaults(normalStyleModel.getItemStyle(), {\n          lineJoin: 'bevel'\n        }),\n        textContent: new graphic.Text({\n          style: createTextStyle(textStyleModel, {\n            text: text\n          })\n        }),\n        textConfig: {\n          position: 'inside'\n        },\n        z2: Z2_EMPHASIS_LIFT * 1e4,\n        onclick: curry(onSelect, itemNode)\n      });\n      el.disableLabelAnimation = true;\n      el.getTextContent().ensureState('emphasis').style = createTextStyle(emphasisTextStyleModel, {\n        text: text\n      });\n      el.ensureState('emphasis').style = emphasisItemStyle;\n      toggleHoverEmphasis(el, emphasisModel.get('focus'), emphasisModel.get('blurScope'), emphasisModel.get('disabled'));\n      this.group.add(el);\n      packEventData(el, seriesModel, itemNode);\n      lastX += itemWidth + ITEM_GAP;\n    }\n  };\n\n  Breadcrumb.prototype.remove = function () {\n    this.group.removeAll();\n  };\n\n  return Breadcrumb;\n}();\n\nfunction makeItemPoints(x, y, itemWidth, itemHeight, head, tail) {\n  var points = [[head ? x : x - ARRAY_LENGTH, y], [x + itemWidth, y], [x + itemWidth, y + itemHeight], [head ? x : x - ARRAY_LENGTH, y + itemHeight]];\n  !tail && points.splice(2, 0, [x + itemWidth + ARRAY_LENGTH, y + itemHeight / 2]);\n  !head && points.push([x, y + itemHeight / 2]);\n  return points;\n} // Package custom mouse event.\n\n\nfunction packEventData(el, seriesModel, itemNode) {\n  getECData(el).eventData = {\n    componentType: 'series',\n    componentSubType: 'treemap',\n    componentIndex: seriesModel.componentIndex,\n    seriesIndex: seriesModel.seriesIndex,\n    seriesName: seriesModel.name,\n    seriesType: 'treemap',\n    selfType: 'breadcrumb',\n    nodeData: {\n      dataIndex: itemNode && itemNode.dataIndex,\n      name: itemNode && itemNode.name\n    },\n    treePathInfo: itemNode && wrapTreePathInfo(itemNode, seriesModel)\n  };\n}\n\nexport default Breadcrumb;"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,KAAKA,OAAO,MAAM,uBAAuB;AAChD,SAASC,SAAS,QAAQ,0BAA0B;AACpD,OAAO,KAAKC,MAAM,MAAM,sBAAsB;AAC9C,SAASC,gBAAgB,QAAQ,yBAAyB;AAC1D,SAASC,KAAK,EAAEC,QAAQ,QAAQ,0BAA0B;AAC1D,SAASC,mBAAmB,QAAQ,qBAAqB;AACzD,SAASC,mBAAmB,EAAEC,gBAAgB,QAAQ,sBAAsB;AAC5E,SAASC,eAAe,QAAQ,2BAA2B;AAC3D,IAAIC,YAAY,GAAG,CAAC;AACpB,IAAIC,QAAQ,GAAG,CAAC;AAChB,IAAIC,YAAY,GAAG,CAAC;AAEpB,IAAIC,UAAU,GACd;AACA,YAAY;EACV,SAASA,UAAUA,CAACC,cAAc,EAAE;IAClC,IAAI,CAACC,KAAK,GAAG,IAAIf,OAAO,CAACgB,KAAK,CAAC,CAAC;IAChCF,cAAc,CAACG,GAAG,CAAC,IAAI,CAACF,KAAK,CAAC;EAChC;EAEAF,UAAU,CAACK,SAAS,CAACC,MAAM,GAAG,UAAUC,WAAW,EAAEC,GAAG,EAAEC,UAAU,EAAEC,QAAQ,EAAE;IAC9E,IAAIC,KAAK,GAAGJ,WAAW,CAACK,QAAQ,CAAC,YAAY,CAAC;IAC9C,IAAIC,SAAS,GAAG,IAAI,CAACX,KAAK;IAC1BW,SAAS,CAACC,SAAS,CAAC,CAAC;IAErB,IAAI,CAACH,KAAK,CAACI,GAAG,CAAC,MAAM,CAAC,IAAI,CAACN,UAAU,EAAE;MACrC;IACF;IAEA,IAAIO,gBAAgB,GAAGL,KAAK,CAACC,QAAQ,CAAC,WAAW,CAAC;IAClD,IAAIK,aAAa,GAAGN,KAAK,CAACC,QAAQ,CAAC,UAAU,CAAC;IAC9C,IAAIM,cAAc,GAAGF,gBAAgB,CAACJ,QAAQ,CAAC,WAAW,CAAC;IAC3D,IAAIO,sBAAsB,GAAGF,aAAa,CAACL,QAAQ,CAAC,CAAC,WAAW,EAAE,WAAW,CAAC,CAAC;IAC/E,IAAIQ,WAAW,GAAG;MAChBC,GAAG,EAAE;QACHC,IAAI,EAAEX,KAAK,CAACI,GAAG,CAAC,MAAM,CAAC;QACvBQ,KAAK,EAAEZ,KAAK,CAACI,GAAG,CAAC,OAAO,CAAC;QACzBS,GAAG,EAAEb,KAAK,CAACI,GAAG,CAAC,KAAK,CAAC;QACrBU,MAAM,EAAEd,KAAK,CAACI,GAAG,CAAC,QAAQ;MAC5B,CAAC;MACDW,GAAG,EAAE;QACHC,KAAK,EAAEnB,GAAG,CAACoB,QAAQ,CAAC,CAAC;QACrBC,MAAM,EAAErB,GAAG,CAACsB,SAAS,CAAC;MACxB,CAAC;MACDC,cAAc,EAAEpB,KAAK,CAACI,GAAG,CAAC,gBAAgB,CAAC;MAC3CiB,UAAU,EAAE,CAAC;MACbC,UAAU,EAAE;IACd,CAAC;IAED,IAAI,CAACC,QAAQ,CAACzB,UAAU,EAAEW,WAAW,EAAEF,cAAc,CAAC;IAEtD,IAAI,CAACiB,cAAc,CAAC5B,WAAW,EAAEa,WAAW,EAAEJ,gBAAgB,EAAEC,aAAa,EAAEC,cAAc,EAAEC,sBAAsB,EAAET,QAAQ,CAAC;IAEhIrB,MAAM,CAAC+C,eAAe,CAACvB,SAAS,EAAEO,WAAW,CAACC,GAAG,EAAED,WAAW,CAACM,GAAG,CAAC;EACrE,CAAC;EACD;AACF;AACA;AACA;;EAGE1B,UAAU,CAACK,SAAS,CAAC6B,QAAQ,GAAG,UAAUzB,UAAU,EAAEW,WAAW,EAAEF,cAAc,EAAE;IACjF,KAAK,IAAImB,IAAI,GAAG5B,UAAU,EAAE4B,IAAI,EAAEA,IAAI,GAAGA,IAAI,CAACC,UAAU,EAAE;MACxD,IAAIC,IAAI,GAAG9C,mBAAmB,CAAC4C,IAAI,CAACzB,QAAQ,CAAC,CAAC,CAACG,GAAG,CAAC,MAAM,CAAC,EAAE,EAAE,CAAC;MAC/D,IAAIyB,QAAQ,GAAGtB,cAAc,CAACuB,WAAW,CAACF,IAAI,CAAC;MAC/C,IAAIG,SAAS,GAAGC,IAAI,CAACC,GAAG,CAACJ,QAAQ,CAACb,KAAK,GAAG9B,YAAY,GAAG,CAAC,EAAEuB,WAAW,CAACW,cAAc,CAAC;MACvFX,WAAW,CAACY,UAAU,IAAIU,SAAS,GAAG5C,QAAQ;MAC9CsB,WAAW,CAACa,UAAU,CAACY,IAAI,CAAC;QAC1BR,IAAI,EAAEA,IAAI;QACVE,IAAI,EAAEA,IAAI;QACVZ,KAAK,EAAEe;MACT,CAAC,CAAC;IACJ;EACF,CAAC;EACD;AACF;AACA;;EAGE1C,UAAU,CAACK,SAAS,CAAC8B,cAAc,GAAG,UAAU5B,WAAW,EAAEa,WAAW,EAAEJ,gBAAgB,EAAEC,aAAa,EAAEC,cAAc,EAAEC,sBAAsB,EAAET,QAAQ,EAAE;IAC3J;IACA,IAAIoC,KAAK,GAAG,CAAC;IACb,IAAIf,cAAc,GAAGX,WAAW,CAACW,cAAc;IAC/C,IAAIF,MAAM,GAAGtB,WAAW,CAACQ,GAAG,CAAC,CAAC,YAAY,EAAE,QAAQ,CAAC,CAAC;IACtD,IAAIgC,aAAa,GAAG1D,MAAM,CAAC2D,gBAAgB,CAAC5B,WAAW,CAACC,GAAG,EAAED,WAAW,CAACM,GAAG,CAAC;IAC7E,IAAIM,UAAU,GAAGZ,WAAW,CAACY,UAAU;IACvC,IAAIC,UAAU,GAAGb,WAAW,CAACa,UAAU;IACvC,IAAIgB,iBAAiB,GAAGhC,aAAa,CAACL,QAAQ,CAAC,WAAW,CAAC,CAACsC,YAAY,CAAC,CAAC;IAE1E,KAAK,IAAIC,CAAC,GAAGlB,UAAU,CAACmB,MAAM,GAAG,CAAC,EAAED,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;MAC/C,IAAIE,IAAI,GAAGpB,UAAU,CAACkB,CAAC,CAAC;MACxB,IAAIG,QAAQ,GAAGD,IAAI,CAAChB,IAAI;MACxB,IAAIK,SAAS,GAAGW,IAAI,CAAC1B,KAAK;MAC1B,IAAIY,IAAI,GAAGc,IAAI,CAACd,IAAI,CAAC,CAAC;;MAEtB,IAAIP,UAAU,GAAGe,aAAa,CAACpB,KAAK,EAAE;QACpCK,UAAU,IAAIU,SAAS,GAAGX,cAAc;QACxCW,SAAS,GAAGX,cAAc;QAC1BQ,IAAI,GAAG,IAAI;MACb;MAEA,IAAIgB,EAAE,GAAG,IAAIpE,OAAO,CAACqE,OAAO,CAAC;QAC3BC,KAAK,EAAE;UACLC,MAAM,EAAEC,cAAc,CAACb,KAAK,EAAE,CAAC,EAAEJ,SAAS,EAAEb,MAAM,EAAEsB,CAAC,KAAKlB,UAAU,CAACmB,MAAM,GAAG,CAAC,EAAED,CAAC,KAAK,CAAC;QAC1F,CAAC;QACDS,KAAK,EAAEpE,QAAQ,CAACwB,gBAAgB,CAACkC,YAAY,CAAC,CAAC,EAAE;UAC/CW,QAAQ,EAAE;QACZ,CAAC,CAAC;QACFC,WAAW,EAAE,IAAI3E,OAAO,CAAC4E,IAAI,CAAC;UAC5BH,KAAK,EAAEhE,eAAe,CAACsB,cAAc,EAAE;YACrCqB,IAAI,EAAEA;UACR,CAAC;QACH,CAAC,CAAC;QACFyB,UAAU,EAAE;UACVC,QAAQ,EAAE;QACZ,CAAC;QACDC,EAAE,EAAEvE,gBAAgB,GAAG,GAAG;QAC1BwE,OAAO,EAAE5E,KAAK,CAACmB,QAAQ,EAAE4C,QAAQ;MACnC,CAAC,CAAC;MACFC,EAAE,CAACa,qBAAqB,GAAG,IAAI;MAC/Bb,EAAE,CAACc,cAAc,CAAC,CAAC,CAACC,WAAW,CAAC,UAAU,CAAC,CAACV,KAAK,GAAGhE,eAAe,CAACuB,sBAAsB,EAAE;QAC1FoB,IAAI,EAAEA;MACR,CAAC,CAAC;MACFgB,EAAE,CAACe,WAAW,CAAC,UAAU,CAAC,CAACV,KAAK,GAAGX,iBAAiB;MACpDvD,mBAAmB,CAAC6D,EAAE,EAAEtC,aAAa,CAACF,GAAG,CAAC,OAAO,CAAC,EAAEE,aAAa,CAACF,GAAG,CAAC,WAAW,CAAC,EAAEE,aAAa,CAACF,GAAG,CAAC,UAAU,CAAC,CAAC;MAClH,IAAI,CAACb,KAAK,CAACE,GAAG,CAACmD,EAAE,CAAC;MAClBgB,aAAa,CAAChB,EAAE,EAAEhD,WAAW,EAAE+C,QAAQ,CAAC;MACxCR,KAAK,IAAIJ,SAAS,GAAG5C,QAAQ;IAC/B;EACF,CAAC;EAEDE,UAAU,CAACK,SAAS,CAACmE,MAAM,GAAG,YAAY;IACxC,IAAI,CAACtE,KAAK,CAACY,SAAS,CAAC,CAAC;EACxB,CAAC;EAED,OAAOd,UAAU;AACnB,CAAC,CAAC,CAAC;AAEH,SAAS2D,cAAcA,CAACc,CAAC,EAAEC,CAAC,EAAEhC,SAAS,EAAEiC,UAAU,EAAEC,IAAI,EAAEC,IAAI,EAAE;EAC/D,IAAInB,MAAM,GAAG,CAAC,CAACkB,IAAI,GAAGH,CAAC,GAAGA,CAAC,GAAG1E,YAAY,EAAE2E,CAAC,CAAC,EAAE,CAACD,CAAC,GAAG/B,SAAS,EAAEgC,CAAC,CAAC,EAAE,CAACD,CAAC,GAAG/B,SAAS,EAAEgC,CAAC,GAAGC,UAAU,CAAC,EAAE,CAACC,IAAI,GAAGH,CAAC,GAAGA,CAAC,GAAG1E,YAAY,EAAE2E,CAAC,GAAGC,UAAU,CAAC,CAAC;EACnJ,CAACE,IAAI,IAAInB,MAAM,CAACoB,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAACL,CAAC,GAAG/B,SAAS,GAAG3C,YAAY,EAAE2E,CAAC,GAAGC,UAAU,GAAG,CAAC,CAAC,CAAC;EAChF,CAACC,IAAI,IAAIlB,MAAM,CAACb,IAAI,CAAC,CAAC4B,CAAC,EAAEC,CAAC,GAAGC,UAAU,GAAG,CAAC,CAAC,CAAC;EAC7C,OAAOjB,MAAM;AACf,CAAC,CAAC;;AAGF,SAASa,aAAaA,CAAChB,EAAE,EAAEhD,WAAW,EAAE+C,QAAQ,EAAE;EAChDlE,SAAS,CAACmE,EAAE,CAAC,CAACwB,SAAS,GAAG;IACxBC,aAAa,EAAE,QAAQ;IACvBC,gBAAgB,EAAE,SAAS;IAC3BC,cAAc,EAAE3E,WAAW,CAAC2E,cAAc;IAC1CC,WAAW,EAAE5E,WAAW,CAAC4E,WAAW;IACpCC,UAAU,EAAE7E,WAAW,CAAC8E,IAAI;IAC5BC,UAAU,EAAE,SAAS;IACrBC,QAAQ,EAAE,YAAY;IACtBC,QAAQ,EAAE;MACRC,SAAS,EAAEnC,QAAQ,IAAIA,QAAQ,CAACmC,SAAS;MACzCJ,IAAI,EAAE/B,QAAQ,IAAIA,QAAQ,CAAC+B;IAC7B,CAAC;IACDK,YAAY,EAAEpC,QAAQ,IAAIhE,gBAAgB,CAACgE,QAAQ,EAAE/C,WAAW;EAClE,CAAC;AACH;AAEA,eAAeP,UAAU"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}