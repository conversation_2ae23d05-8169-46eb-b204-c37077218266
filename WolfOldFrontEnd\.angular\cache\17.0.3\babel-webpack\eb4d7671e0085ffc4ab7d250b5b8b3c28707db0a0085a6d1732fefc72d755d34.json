{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { parsePercent, linearMap } from '../../util/number.js';\nimport * as layout from '../../util/layout.js';\nimport * as zrUtil from 'zrender/lib/core/util.js';\nvar PI2 = Math.PI * 2;\nvar RADIAN = Math.PI / 180;\nfunction getViewRect(seriesModel, api) {\n  return layout.getLayoutRect(seriesModel.getBoxLayoutParams(), {\n    width: api.getWidth(),\n    height: api.getHeight()\n  });\n}\nexport function getBasicPieLayout(seriesModel, api) {\n  var viewRect = getViewRect(seriesModel, api); // center can be string or number when coordinateSystem is specified\n\n  var center = seriesModel.get('center');\n  var radius = seriesModel.get('radius');\n  if (!zrUtil.isArray(radius)) {\n    radius = [0, radius];\n  }\n  var width = parsePercent(viewRect.width, api.getWidth());\n  var height = parsePercent(viewRect.height, api.getHeight());\n  var size = Math.min(width, height);\n  var r0 = parsePercent(radius[0], size / 2);\n  var r = parsePercent(radius[1], size / 2);\n  var cx;\n  var cy;\n  var coordSys = seriesModel.coordinateSystem;\n  if (coordSys) {\n    // percentage is not allowed when coordinate system is specified\n    var point = coordSys.dataToPoint(center);\n    cx = point[0] || 0;\n    cy = point[1] || 0;\n  } else {\n    if (!zrUtil.isArray(center)) {\n      center = [center, center];\n    }\n    cx = parsePercent(center[0], width) + viewRect.x;\n    cy = parsePercent(center[1], height) + viewRect.y;\n  }\n  return {\n    cx: cx,\n    cy: cy,\n    r0: r0,\n    r: r\n  };\n}\nexport default function pieLayout(seriesType, ecModel, api) {\n  ecModel.eachSeriesByType(seriesType, function (seriesModel) {\n    var data = seriesModel.getData();\n    var valueDim = data.mapDimension('value');\n    var viewRect = getViewRect(seriesModel, api);\n    var _a = getBasicPieLayout(seriesModel, api),\n      cx = _a.cx,\n      cy = _a.cy,\n      r = _a.r,\n      r0 = _a.r0;\n    var startAngle = -seriesModel.get('startAngle') * RADIAN;\n    var minAngle = seriesModel.get('minAngle') * RADIAN;\n    var validDataCount = 0;\n    data.each(valueDim, function (value) {\n      !isNaN(value) && validDataCount++;\n    });\n    var sum = data.getSum(valueDim); // Sum may be 0\n\n    var unitRadian = Math.PI / (sum || validDataCount) * 2;\n    var clockwise = seriesModel.get('clockwise');\n    var roseType = seriesModel.get('roseType');\n    var stillShowZeroSum = seriesModel.get('stillShowZeroSum'); // [0...max]\n\n    var extent = data.getDataExtent(valueDim);\n    extent[0] = 0; // In the case some sector angle is smaller than minAngle\n\n    var restAngle = PI2;\n    var valueSumLargerThanMinAngle = 0;\n    var currentAngle = startAngle;\n    var dir = clockwise ? 1 : -1;\n    data.setLayout({\n      viewRect: viewRect,\n      r: r\n    });\n    data.each(valueDim, function (value, idx) {\n      var angle;\n      if (isNaN(value)) {\n        data.setItemLayout(idx, {\n          angle: NaN,\n          startAngle: NaN,\n          endAngle: NaN,\n          clockwise: clockwise,\n          cx: cx,\n          cy: cy,\n          r0: r0,\n          r: roseType ? NaN : r\n        });\n        return;\n      } // FIXME 兼容 2.0 但是 roseType 是 area 的时候才是这样？\n\n      if (roseType !== 'area') {\n        angle = sum === 0 && stillShowZeroSum ? unitRadian : value * unitRadian;\n      } else {\n        angle = PI2 / validDataCount;\n      }\n      if (angle < minAngle) {\n        angle = minAngle;\n        restAngle -= minAngle;\n      } else {\n        valueSumLargerThanMinAngle += value;\n      }\n      var endAngle = currentAngle + dir * angle;\n      data.setItemLayout(idx, {\n        angle: angle,\n        startAngle: currentAngle,\n        endAngle: endAngle,\n        clockwise: clockwise,\n        cx: cx,\n        cy: cy,\n        r0: r0,\n        r: roseType ? linearMap(value, extent, [r0, r]) : r\n      });\n      currentAngle = endAngle;\n    }); // Some sector is constrained by minAngle\n    // Rest sectors needs recalculate angle\n\n    if (restAngle < PI2 && validDataCount) {\n      // Average the angle if rest angle is not enough after all angles is\n      // Constrained by minAngle\n      if (restAngle <= 1e-3) {\n        var angle_1 = PI2 / validDataCount;\n        data.each(valueDim, function (value, idx) {\n          if (!isNaN(value)) {\n            var layout_1 = data.getItemLayout(idx);\n            layout_1.angle = angle_1;\n            layout_1.startAngle = startAngle + dir * idx * angle_1;\n            layout_1.endAngle = startAngle + dir * (idx + 1) * angle_1;\n          }\n        });\n      } else {\n        unitRadian = restAngle / valueSumLargerThanMinAngle;\n        currentAngle = startAngle;\n        data.each(valueDim, function (value, idx) {\n          if (!isNaN(value)) {\n            var layout_2 = data.getItemLayout(idx);\n            var angle = layout_2.angle === minAngle ? minAngle : value * unitRadian;\n            layout_2.startAngle = currentAngle;\n            layout_2.endAngle = currentAngle + dir * angle;\n            currentAngle += dir * angle;\n          }\n        });\n      }\n    }\n  });\n}", "map": {"version": 3, "names": ["parsePercent", "linearMap", "layout", "zrUtil", "PI2", "Math", "PI", "RADIAN", "getViewRect", "seriesModel", "api", "getLayoutRect", "getBoxLayoutParams", "width", "getWidth", "height", "getHeight", "getBasicPieLayout", "viewRect", "center", "get", "radius", "isArray", "size", "min", "r0", "r", "cx", "cy", "coordSys", "coordinateSystem", "point", "dataToPoint", "x", "y", "pieLayout", "seriesType", "ecModel", "eachSeriesByType", "data", "getData", "valueDim", "mapDimension", "_a", "startAngle", "minAngle", "validDataCount", "each", "value", "isNaN", "sum", "getSum", "unitRadian", "clockwise", "roseType", "stillShowZeroSum", "extent", "getDataExtent", "restAngle", "valueSumLargerThanMinAngle", "currentAngle", "dir", "setLayout", "idx", "angle", "setItemLayout", "NaN", "endAngle", "angle_1", "layout_1", "getItemLayout", "layout_2"], "sources": ["C:/WolfSystemV2/WolfOldFrontEnd/node_modules/echarts/lib/chart/pie/pieLayout.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { parsePercent, linearMap } from '../../util/number.js';\nimport * as layout from '../../util/layout.js';\nimport * as zrUtil from 'zrender/lib/core/util.js';\nvar PI2 = Math.PI * 2;\nvar RADIAN = Math.PI / 180;\n\nfunction getViewRect(seriesModel, api) {\n  return layout.getLayoutRect(seriesModel.getBoxLayoutParams(), {\n    width: api.getWidth(),\n    height: api.getHeight()\n  });\n}\n\nexport function getBasicPieLayout(seriesModel, api) {\n  var viewRect = getViewRect(seriesModel, api); // center can be string or number when coordinateSystem is specified\n\n  var center = seriesModel.get('center');\n  var radius = seriesModel.get('radius');\n\n  if (!zrUtil.isArray(radius)) {\n    radius = [0, radius];\n  }\n\n  var width = parsePercent(viewRect.width, api.getWidth());\n  var height = parsePercent(viewRect.height, api.getHeight());\n  var size = Math.min(width, height);\n  var r0 = parsePercent(radius[0], size / 2);\n  var r = parsePercent(radius[1], size / 2);\n  var cx;\n  var cy;\n  var coordSys = seriesModel.coordinateSystem;\n\n  if (coordSys) {\n    // percentage is not allowed when coordinate system is specified\n    var point = coordSys.dataToPoint(center);\n    cx = point[0] || 0;\n    cy = point[1] || 0;\n  } else {\n    if (!zrUtil.isArray(center)) {\n      center = [center, center];\n    }\n\n    cx = parsePercent(center[0], width) + viewRect.x;\n    cy = parsePercent(center[1], height) + viewRect.y;\n  }\n\n  return {\n    cx: cx,\n    cy: cy,\n    r0: r0,\n    r: r\n  };\n}\nexport default function pieLayout(seriesType, ecModel, api) {\n  ecModel.eachSeriesByType(seriesType, function (seriesModel) {\n    var data = seriesModel.getData();\n    var valueDim = data.mapDimension('value');\n    var viewRect = getViewRect(seriesModel, api);\n\n    var _a = getBasicPieLayout(seriesModel, api),\n        cx = _a.cx,\n        cy = _a.cy,\n        r = _a.r,\n        r0 = _a.r0;\n\n    var startAngle = -seriesModel.get('startAngle') * RADIAN;\n    var minAngle = seriesModel.get('minAngle') * RADIAN;\n    var validDataCount = 0;\n    data.each(valueDim, function (value) {\n      !isNaN(value) && validDataCount++;\n    });\n    var sum = data.getSum(valueDim); // Sum may be 0\n\n    var unitRadian = Math.PI / (sum || validDataCount) * 2;\n    var clockwise = seriesModel.get('clockwise');\n    var roseType = seriesModel.get('roseType');\n    var stillShowZeroSum = seriesModel.get('stillShowZeroSum'); // [0...max]\n\n    var extent = data.getDataExtent(valueDim);\n    extent[0] = 0; // In the case some sector angle is smaller than minAngle\n\n    var restAngle = PI2;\n    var valueSumLargerThanMinAngle = 0;\n    var currentAngle = startAngle;\n    var dir = clockwise ? 1 : -1;\n    data.setLayout({\n      viewRect: viewRect,\n      r: r\n    });\n    data.each(valueDim, function (value, idx) {\n      var angle;\n\n      if (isNaN(value)) {\n        data.setItemLayout(idx, {\n          angle: NaN,\n          startAngle: NaN,\n          endAngle: NaN,\n          clockwise: clockwise,\n          cx: cx,\n          cy: cy,\n          r0: r0,\n          r: roseType ? NaN : r\n        });\n        return;\n      } // FIXME 兼容 2.0 但是 roseType 是 area 的时候才是这样？\n\n\n      if (roseType !== 'area') {\n        angle = sum === 0 && stillShowZeroSum ? unitRadian : value * unitRadian;\n      } else {\n        angle = PI2 / validDataCount;\n      }\n\n      if (angle < minAngle) {\n        angle = minAngle;\n        restAngle -= minAngle;\n      } else {\n        valueSumLargerThanMinAngle += value;\n      }\n\n      var endAngle = currentAngle + dir * angle;\n      data.setItemLayout(idx, {\n        angle: angle,\n        startAngle: currentAngle,\n        endAngle: endAngle,\n        clockwise: clockwise,\n        cx: cx,\n        cy: cy,\n        r0: r0,\n        r: roseType ? linearMap(value, extent, [r0, r]) : r\n      });\n      currentAngle = endAngle;\n    }); // Some sector is constrained by minAngle\n    // Rest sectors needs recalculate angle\n\n    if (restAngle < PI2 && validDataCount) {\n      // Average the angle if rest angle is not enough after all angles is\n      // Constrained by minAngle\n      if (restAngle <= 1e-3) {\n        var angle_1 = PI2 / validDataCount;\n        data.each(valueDim, function (value, idx) {\n          if (!isNaN(value)) {\n            var layout_1 = data.getItemLayout(idx);\n            layout_1.angle = angle_1;\n            layout_1.startAngle = startAngle + dir * idx * angle_1;\n            layout_1.endAngle = startAngle + dir * (idx + 1) * angle_1;\n          }\n        });\n      } else {\n        unitRadian = restAngle / valueSumLargerThanMinAngle;\n        currentAngle = startAngle;\n        data.each(valueDim, function (value, idx) {\n          if (!isNaN(value)) {\n            var layout_2 = data.getItemLayout(idx);\n            var angle = layout_2.angle === minAngle ? minAngle : value * unitRadian;\n            layout_2.startAngle = currentAngle;\n            layout_2.endAngle = currentAngle + dir * angle;\n            currentAngle += dir * angle;\n          }\n        });\n      }\n    }\n  });\n}"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,YAAY,EAAEC,SAAS,QAAQ,sBAAsB;AAC9D,OAAO,KAAKC,MAAM,MAAM,sBAAsB;AAC9C,OAAO,KAAKC,MAAM,MAAM,0BAA0B;AAClD,IAAIC,GAAG,GAAGC,IAAI,CAACC,EAAE,GAAG,CAAC;AACrB,IAAIC,MAAM,GAAGF,IAAI,CAACC,EAAE,GAAG,GAAG;AAE1B,SAASE,WAAWA,CAACC,WAAW,EAAEC,GAAG,EAAE;EACrC,OAAOR,MAAM,CAACS,aAAa,CAACF,WAAW,CAACG,kBAAkB,CAAC,CAAC,EAAE;IAC5DC,KAAK,EAAEH,GAAG,CAACI,QAAQ,CAAC,CAAC;IACrBC,MAAM,EAAEL,GAAG,CAACM,SAAS,CAAC;EACxB,CAAC,CAAC;AACJ;AAEA,OAAO,SAASC,iBAAiBA,CAACR,WAAW,EAAEC,GAAG,EAAE;EAClD,IAAIQ,QAAQ,GAAGV,WAAW,CAACC,WAAW,EAAEC,GAAG,CAAC,CAAC,CAAC;;EAE9C,IAAIS,MAAM,GAAGV,WAAW,CAACW,GAAG,CAAC,QAAQ,CAAC;EACtC,IAAIC,MAAM,GAAGZ,WAAW,CAACW,GAAG,CAAC,QAAQ,CAAC;EAEtC,IAAI,CAACjB,MAAM,CAACmB,OAAO,CAACD,MAAM,CAAC,EAAE;IAC3BA,MAAM,GAAG,CAAC,CAAC,EAAEA,MAAM,CAAC;EACtB;EAEA,IAAIR,KAAK,GAAGb,YAAY,CAACkB,QAAQ,CAACL,KAAK,EAAEH,GAAG,CAACI,QAAQ,CAAC,CAAC,CAAC;EACxD,IAAIC,MAAM,GAAGf,YAAY,CAACkB,QAAQ,CAACH,MAAM,EAAEL,GAAG,CAACM,SAAS,CAAC,CAAC,CAAC;EAC3D,IAAIO,IAAI,GAAGlB,IAAI,CAACmB,GAAG,CAACX,KAAK,EAAEE,MAAM,CAAC;EAClC,IAAIU,EAAE,GAAGzB,YAAY,CAACqB,MAAM,CAAC,CAAC,CAAC,EAAEE,IAAI,GAAG,CAAC,CAAC;EAC1C,IAAIG,CAAC,GAAG1B,YAAY,CAACqB,MAAM,CAAC,CAAC,CAAC,EAAEE,IAAI,GAAG,CAAC,CAAC;EACzC,IAAII,EAAE;EACN,IAAIC,EAAE;EACN,IAAIC,QAAQ,GAAGpB,WAAW,CAACqB,gBAAgB;EAE3C,IAAID,QAAQ,EAAE;IACZ;IACA,IAAIE,KAAK,GAAGF,QAAQ,CAACG,WAAW,CAACb,MAAM,CAAC;IACxCQ,EAAE,GAAGI,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC;IAClBH,EAAE,GAAGG,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC;EACpB,CAAC,MAAM;IACL,IAAI,CAAC5B,MAAM,CAACmB,OAAO,CAACH,MAAM,CAAC,EAAE;MAC3BA,MAAM,GAAG,CAACA,MAAM,EAAEA,MAAM,CAAC;IAC3B;IAEAQ,EAAE,GAAG3B,YAAY,CAACmB,MAAM,CAAC,CAAC,CAAC,EAAEN,KAAK,CAAC,GAAGK,QAAQ,CAACe,CAAC;IAChDL,EAAE,GAAG5B,YAAY,CAACmB,MAAM,CAAC,CAAC,CAAC,EAAEJ,MAAM,CAAC,GAAGG,QAAQ,CAACgB,CAAC;EACnD;EAEA,OAAO;IACLP,EAAE,EAAEA,EAAE;IACNC,EAAE,EAAEA,EAAE;IACNH,EAAE,EAAEA,EAAE;IACNC,CAAC,EAAEA;EACL,CAAC;AACH;AACA,eAAe,SAASS,SAASA,CAACC,UAAU,EAAEC,OAAO,EAAE3B,GAAG,EAAE;EAC1D2B,OAAO,CAACC,gBAAgB,CAACF,UAAU,EAAE,UAAU3B,WAAW,EAAE;IAC1D,IAAI8B,IAAI,GAAG9B,WAAW,CAAC+B,OAAO,CAAC,CAAC;IAChC,IAAIC,QAAQ,GAAGF,IAAI,CAACG,YAAY,CAAC,OAAO,CAAC;IACzC,IAAIxB,QAAQ,GAAGV,WAAW,CAACC,WAAW,EAAEC,GAAG,CAAC;IAE5C,IAAIiC,EAAE,GAAG1B,iBAAiB,CAACR,WAAW,EAAEC,GAAG,CAAC;MACxCiB,EAAE,GAAGgB,EAAE,CAAChB,EAAE;MACVC,EAAE,GAAGe,EAAE,CAACf,EAAE;MACVF,CAAC,GAAGiB,EAAE,CAACjB,CAAC;MACRD,EAAE,GAAGkB,EAAE,CAAClB,EAAE;IAEd,IAAImB,UAAU,GAAG,CAACnC,WAAW,CAACW,GAAG,CAAC,YAAY,CAAC,GAAGb,MAAM;IACxD,IAAIsC,QAAQ,GAAGpC,WAAW,CAACW,GAAG,CAAC,UAAU,CAAC,GAAGb,MAAM;IACnD,IAAIuC,cAAc,GAAG,CAAC;IACtBP,IAAI,CAACQ,IAAI,CAACN,QAAQ,EAAE,UAAUO,KAAK,EAAE;MACnC,CAACC,KAAK,CAACD,KAAK,CAAC,IAAIF,cAAc,EAAE;IACnC,CAAC,CAAC;IACF,IAAII,GAAG,GAAGX,IAAI,CAACY,MAAM,CAACV,QAAQ,CAAC,CAAC,CAAC;;IAEjC,IAAIW,UAAU,GAAG/C,IAAI,CAACC,EAAE,IAAI4C,GAAG,IAAIJ,cAAc,CAAC,GAAG,CAAC;IACtD,IAAIO,SAAS,GAAG5C,WAAW,CAACW,GAAG,CAAC,WAAW,CAAC;IAC5C,IAAIkC,QAAQ,GAAG7C,WAAW,CAACW,GAAG,CAAC,UAAU,CAAC;IAC1C,IAAImC,gBAAgB,GAAG9C,WAAW,CAACW,GAAG,CAAC,kBAAkB,CAAC,CAAC,CAAC;;IAE5D,IAAIoC,MAAM,GAAGjB,IAAI,CAACkB,aAAa,CAAChB,QAAQ,CAAC;IACzCe,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;;IAEf,IAAIE,SAAS,GAAGtD,GAAG;IACnB,IAAIuD,0BAA0B,GAAG,CAAC;IAClC,IAAIC,YAAY,GAAGhB,UAAU;IAC7B,IAAIiB,GAAG,GAAGR,SAAS,GAAG,CAAC,GAAG,CAAC,CAAC;IAC5Bd,IAAI,CAACuB,SAAS,CAAC;MACb5C,QAAQ,EAAEA,QAAQ;MAClBQ,CAAC,EAAEA;IACL,CAAC,CAAC;IACFa,IAAI,CAACQ,IAAI,CAACN,QAAQ,EAAE,UAAUO,KAAK,EAAEe,GAAG,EAAE;MACxC,IAAIC,KAAK;MAET,IAAIf,KAAK,CAACD,KAAK,CAAC,EAAE;QAChBT,IAAI,CAAC0B,aAAa,CAACF,GAAG,EAAE;UACtBC,KAAK,EAAEE,GAAG;UACVtB,UAAU,EAAEsB,GAAG;UACfC,QAAQ,EAAED,GAAG;UACbb,SAAS,EAAEA,SAAS;UACpB1B,EAAE,EAAEA,EAAE;UACNC,EAAE,EAAEA,EAAE;UACNH,EAAE,EAAEA,EAAE;UACNC,CAAC,EAAE4B,QAAQ,GAAGY,GAAG,GAAGxC;QACtB,CAAC,CAAC;QACF;MACF,CAAC,CAAC;;MAGF,IAAI4B,QAAQ,KAAK,MAAM,EAAE;QACvBU,KAAK,GAAGd,GAAG,KAAK,CAAC,IAAIK,gBAAgB,GAAGH,UAAU,GAAGJ,KAAK,GAAGI,UAAU;MACzE,CAAC,MAAM;QACLY,KAAK,GAAG5D,GAAG,GAAG0C,cAAc;MAC9B;MAEA,IAAIkB,KAAK,GAAGnB,QAAQ,EAAE;QACpBmB,KAAK,GAAGnB,QAAQ;QAChBa,SAAS,IAAIb,QAAQ;MACvB,CAAC,MAAM;QACLc,0BAA0B,IAAIX,KAAK;MACrC;MAEA,IAAImB,QAAQ,GAAGP,YAAY,GAAGC,GAAG,GAAGG,KAAK;MACzCzB,IAAI,CAAC0B,aAAa,CAACF,GAAG,EAAE;QACtBC,KAAK,EAAEA,KAAK;QACZpB,UAAU,EAAEgB,YAAY;QACxBO,QAAQ,EAAEA,QAAQ;QAClBd,SAAS,EAAEA,SAAS;QACpB1B,EAAE,EAAEA,EAAE;QACNC,EAAE,EAAEA,EAAE;QACNH,EAAE,EAAEA,EAAE;QACNC,CAAC,EAAE4B,QAAQ,GAAGrD,SAAS,CAAC+C,KAAK,EAAEQ,MAAM,EAAE,CAAC/B,EAAE,EAAEC,CAAC,CAAC,CAAC,GAAGA;MACpD,CAAC,CAAC;MACFkC,YAAY,GAAGO,QAAQ;IACzB,CAAC,CAAC,CAAC,CAAC;IACJ;;IAEA,IAAIT,SAAS,GAAGtD,GAAG,IAAI0C,cAAc,EAAE;MACrC;MACA;MACA,IAAIY,SAAS,IAAI,IAAI,EAAE;QACrB,IAAIU,OAAO,GAAGhE,GAAG,GAAG0C,cAAc;QAClCP,IAAI,CAACQ,IAAI,CAACN,QAAQ,EAAE,UAAUO,KAAK,EAAEe,GAAG,EAAE;UACxC,IAAI,CAACd,KAAK,CAACD,KAAK,CAAC,EAAE;YACjB,IAAIqB,QAAQ,GAAG9B,IAAI,CAAC+B,aAAa,CAACP,GAAG,CAAC;YACtCM,QAAQ,CAACL,KAAK,GAAGI,OAAO;YACxBC,QAAQ,CAACzB,UAAU,GAAGA,UAAU,GAAGiB,GAAG,GAAGE,GAAG,GAAGK,OAAO;YACtDC,QAAQ,CAACF,QAAQ,GAAGvB,UAAU,GAAGiB,GAAG,IAAIE,GAAG,GAAG,CAAC,CAAC,GAAGK,OAAO;UAC5D;QACF,CAAC,CAAC;MACJ,CAAC,MAAM;QACLhB,UAAU,GAAGM,SAAS,GAAGC,0BAA0B;QACnDC,YAAY,GAAGhB,UAAU;QACzBL,IAAI,CAACQ,IAAI,CAACN,QAAQ,EAAE,UAAUO,KAAK,EAAEe,GAAG,EAAE;UACxC,IAAI,CAACd,KAAK,CAACD,KAAK,CAAC,EAAE;YACjB,IAAIuB,QAAQ,GAAGhC,IAAI,CAAC+B,aAAa,CAACP,GAAG,CAAC;YACtC,IAAIC,KAAK,GAAGO,QAAQ,CAACP,KAAK,KAAKnB,QAAQ,GAAGA,QAAQ,GAAGG,KAAK,GAAGI,UAAU;YACvEmB,QAAQ,CAAC3B,UAAU,GAAGgB,YAAY;YAClCW,QAAQ,CAACJ,QAAQ,GAAGP,YAAY,GAAGC,GAAG,GAAGG,KAAK;YAC9CJ,YAAY,IAAIC,GAAG,GAAGG,KAAK;UAC7B;QACF,CAAC,CAAC;MACJ;IACF;EACF,CAAC,CAAC;AACJ"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}