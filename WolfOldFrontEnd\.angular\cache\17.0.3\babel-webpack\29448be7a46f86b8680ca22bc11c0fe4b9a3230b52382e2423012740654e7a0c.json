{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\nimport { isString, extend, map, isFunction } from 'zrender/lib/core/util.js';\nimport * as graphic from '../../util/graphic.js';\nimport { createTextStyle } from '../../label/labelStyle.js';\nimport { formatTplSimple } from '../../util/format.js';\nimport { parsePercent } from '../../util/number.js';\nimport ComponentView from '../../view/Component.js';\nimport { getLocaleModel } from '../../core/locale.js';\nvar CalendarView = /** @class */\nfunction (_super) {\n  __extends(CalendarView, _super);\n  function CalendarView() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.type = CalendarView.type;\n    return _this;\n  }\n  CalendarView.prototype.render = function (calendarModel, ecModel, api) {\n    var group = this.group;\n    group.removeAll();\n    var coordSys = calendarModel.coordinateSystem; // range info\n\n    var rangeData = coordSys.getRangeInfo();\n    var orient = coordSys.getOrient(); // locale\n\n    var localeModel = ecModel.getLocaleModel();\n    this._renderDayRect(calendarModel, rangeData, group); // _renderLines must be called prior to following function\n\n    this._renderLines(calendarModel, rangeData, orient, group);\n    this._renderYearText(calendarModel, rangeData, orient, group);\n    this._renderMonthText(calendarModel, localeModel, orient, group);\n    this._renderWeekText(calendarModel, localeModel, rangeData, orient, group);\n  }; // render day rect\n\n  CalendarView.prototype._renderDayRect = function (calendarModel, rangeData, group) {\n    var coordSys = calendarModel.coordinateSystem;\n    var itemRectStyleModel = calendarModel.getModel('itemStyle').getItemStyle();\n    var sw = coordSys.getCellWidth();\n    var sh = coordSys.getCellHeight();\n    for (var i = rangeData.start.time; i <= rangeData.end.time; i = coordSys.getNextNDay(i, 1).time) {\n      var point = coordSys.dataToRect([i], false).tl; // every rect\n\n      var rect = new graphic.Rect({\n        shape: {\n          x: point[0],\n          y: point[1],\n          width: sw,\n          height: sh\n        },\n        cursor: 'default',\n        style: itemRectStyleModel\n      });\n      group.add(rect);\n    }\n  }; // render separate line\n\n  CalendarView.prototype._renderLines = function (calendarModel, rangeData, orient, group) {\n    var self = this;\n    var coordSys = calendarModel.coordinateSystem;\n    var lineStyleModel = calendarModel.getModel(['splitLine', 'lineStyle']).getLineStyle();\n    var show = calendarModel.get(['splitLine', 'show']);\n    var lineWidth = lineStyleModel.lineWidth;\n    this._tlpoints = [];\n    this._blpoints = [];\n    this._firstDayOfMonth = [];\n    this._firstDayPoints = [];\n    var firstDay = rangeData.start;\n    for (var i = 0; firstDay.time <= rangeData.end.time; i++) {\n      addPoints(firstDay.formatedDate);\n      if (i === 0) {\n        firstDay = coordSys.getDateInfo(rangeData.start.y + '-' + rangeData.start.m);\n      }\n      var date = firstDay.date;\n      date.setMonth(date.getMonth() + 1);\n      firstDay = coordSys.getDateInfo(date);\n    }\n    addPoints(coordSys.getNextNDay(rangeData.end.time, 1).formatedDate);\n    function addPoints(date) {\n      self._firstDayOfMonth.push(coordSys.getDateInfo(date));\n      self._firstDayPoints.push(coordSys.dataToRect([date], false).tl);\n      var points = self._getLinePointsOfOneWeek(calendarModel, date, orient);\n      self._tlpoints.push(points[0]);\n      self._blpoints.push(points[points.length - 1]);\n      show && self._drawSplitline(points, lineStyleModel, group);\n    } // render top/left line\n\n    show && this._drawSplitline(self._getEdgesPoints(self._tlpoints, lineWidth, orient), lineStyleModel, group); // render bottom/right line\n\n    show && this._drawSplitline(self._getEdgesPoints(self._blpoints, lineWidth, orient), lineStyleModel, group);\n  }; // get points at both ends\n\n  CalendarView.prototype._getEdgesPoints = function (points, lineWidth, orient) {\n    var rs = [points[0].slice(), points[points.length - 1].slice()];\n    var idx = orient === 'horizontal' ? 0 : 1; // both ends of the line are extend half lineWidth\n\n    rs[0][idx] = rs[0][idx] - lineWidth / 2;\n    rs[1][idx] = rs[1][idx] + lineWidth / 2;\n    return rs;\n  }; // render split line\n\n  CalendarView.prototype._drawSplitline = function (points, lineStyle, group) {\n    var poyline = new graphic.Polyline({\n      z2: 20,\n      shape: {\n        points: points\n      },\n      style: lineStyle\n    });\n    group.add(poyline);\n  }; // render month line of one week points\n\n  CalendarView.prototype._getLinePointsOfOneWeek = function (calendarModel, date, orient) {\n    var coordSys = calendarModel.coordinateSystem;\n    var parsedDate = coordSys.getDateInfo(date);\n    var points = [];\n    for (var i = 0; i < 7; i++) {\n      var tmpD = coordSys.getNextNDay(parsedDate.time, i);\n      var point = coordSys.dataToRect([tmpD.time], false);\n      points[2 * tmpD.day] = point.tl;\n      points[2 * tmpD.day + 1] = point[orient === 'horizontal' ? 'bl' : 'tr'];\n    }\n    return points;\n  };\n  CalendarView.prototype._formatterLabel = function (formatter, params) {\n    if (isString(formatter) && formatter) {\n      return formatTplSimple(formatter, params);\n    }\n    if (isFunction(formatter)) {\n      return formatter(params);\n    }\n    return params.nameMap;\n  };\n  CalendarView.prototype._yearTextPositionControl = function (textEl, point, orient, position, margin) {\n    var x = point[0];\n    var y = point[1];\n    var aligns = ['center', 'bottom'];\n    if (position === 'bottom') {\n      y += margin;\n      aligns = ['center', 'top'];\n    } else if (position === 'left') {\n      x -= margin;\n    } else if (position === 'right') {\n      x += margin;\n      aligns = ['center', 'top'];\n    } else {\n      // top\n      y -= margin;\n    }\n    var rotate = 0;\n    if (position === 'left' || position === 'right') {\n      rotate = Math.PI / 2;\n    }\n    return {\n      rotation: rotate,\n      x: x,\n      y: y,\n      style: {\n        align: aligns[0],\n        verticalAlign: aligns[1]\n      }\n    };\n  }; // render year\n\n  CalendarView.prototype._renderYearText = function (calendarModel, rangeData, orient, group) {\n    var yearLabel = calendarModel.getModel('yearLabel');\n    if (!yearLabel.get('show')) {\n      return;\n    }\n    var margin = yearLabel.get('margin');\n    var pos = yearLabel.get('position');\n    if (!pos) {\n      pos = orient !== 'horizontal' ? 'top' : 'left';\n    }\n    var points = [this._tlpoints[this._tlpoints.length - 1], this._blpoints[0]];\n    var xc = (points[0][0] + points[1][0]) / 2;\n    var yc = (points[0][1] + points[1][1]) / 2;\n    var idx = orient === 'horizontal' ? 0 : 1;\n    var posPoints = {\n      top: [xc, points[idx][1]],\n      bottom: [xc, points[1 - idx][1]],\n      left: [points[1 - idx][0], yc],\n      right: [points[idx][0], yc]\n    };\n    var name = rangeData.start.y;\n    if (+rangeData.end.y > +rangeData.start.y) {\n      name = name + '-' + rangeData.end.y;\n    }\n    var formatter = yearLabel.get('formatter');\n    var params = {\n      start: rangeData.start.y,\n      end: rangeData.end.y,\n      nameMap: name\n    };\n    var content = this._formatterLabel(formatter, params);\n    var yearText = new graphic.Text({\n      z2: 30,\n      style: createTextStyle(yearLabel, {\n        text: content\n      })\n    });\n    yearText.attr(this._yearTextPositionControl(yearText, posPoints[pos], orient, pos, margin));\n    group.add(yearText);\n  };\n  CalendarView.prototype._monthTextPositionControl = function (point, isCenter, orient, position, margin) {\n    var align = 'left';\n    var vAlign = 'top';\n    var x = point[0];\n    var y = point[1];\n    if (orient === 'horizontal') {\n      y = y + margin;\n      if (isCenter) {\n        align = 'center';\n      }\n      if (position === 'start') {\n        vAlign = 'bottom';\n      }\n    } else {\n      x = x + margin;\n      if (isCenter) {\n        vAlign = 'middle';\n      }\n      if (position === 'start') {\n        align = 'right';\n      }\n    }\n    return {\n      x: x,\n      y: y,\n      align: align,\n      verticalAlign: vAlign\n    };\n  }; // render month and year text\n\n  CalendarView.prototype._renderMonthText = function (calendarModel, localeModel, orient, group) {\n    var monthLabel = calendarModel.getModel('monthLabel');\n    if (!monthLabel.get('show')) {\n      return;\n    }\n    var nameMap = monthLabel.get('nameMap');\n    var margin = monthLabel.get('margin');\n    var pos = monthLabel.get('position');\n    var align = monthLabel.get('align');\n    var termPoints = [this._tlpoints, this._blpoints];\n    if (!nameMap || isString(nameMap)) {\n      if (nameMap) {\n        // case-sensitive\n        localeModel = getLocaleModel(nameMap) || localeModel;\n      } // PENDING\n      // for ZH locale, original form is `一月` but current form is `1月`\n\n      nameMap = localeModel.get(['time', 'monthAbbr']) || [];\n    }\n    var idx = pos === 'start' ? 0 : 1;\n    var axis = orient === 'horizontal' ? 0 : 1;\n    margin = pos === 'start' ? -margin : margin;\n    var isCenter = align === 'center';\n    for (var i = 0; i < termPoints[idx].length - 1; i++) {\n      var tmp = termPoints[idx][i].slice();\n      var firstDay = this._firstDayOfMonth[i];\n      if (isCenter) {\n        var firstDayPoints = this._firstDayPoints[i];\n        tmp[axis] = (firstDayPoints[axis] + termPoints[0][i + 1][axis]) / 2;\n      }\n      var formatter = monthLabel.get('formatter');\n      var name_1 = nameMap[+firstDay.m - 1];\n      var params = {\n        yyyy: firstDay.y,\n        yy: (firstDay.y + '').slice(2),\n        MM: firstDay.m,\n        M: +firstDay.m,\n        nameMap: name_1\n      };\n      var content = this._formatterLabel(formatter, params);\n      var monthText = new graphic.Text({\n        z2: 30,\n        style: extend(createTextStyle(monthLabel, {\n          text: content\n        }), this._monthTextPositionControl(tmp, isCenter, orient, pos, margin))\n      });\n      group.add(monthText);\n    }\n  };\n  CalendarView.prototype._weekTextPositionControl = function (point, orient, position, margin, cellSize) {\n    var align = 'center';\n    var vAlign = 'middle';\n    var x = point[0];\n    var y = point[1];\n    var isStart = position === 'start';\n    if (orient === 'horizontal') {\n      x = x + margin + (isStart ? 1 : -1) * cellSize[0] / 2;\n      align = isStart ? 'right' : 'left';\n    } else {\n      y = y + margin + (isStart ? 1 : -1) * cellSize[1] / 2;\n      vAlign = isStart ? 'bottom' : 'top';\n    }\n    return {\n      x: x,\n      y: y,\n      align: align,\n      verticalAlign: vAlign\n    };\n  }; // render weeks\n\n  CalendarView.prototype._renderWeekText = function (calendarModel, localeModel, rangeData, orient, group) {\n    var dayLabel = calendarModel.getModel('dayLabel');\n    if (!dayLabel.get('show')) {\n      return;\n    }\n    var coordSys = calendarModel.coordinateSystem;\n    var pos = dayLabel.get('position');\n    var nameMap = dayLabel.get('nameMap');\n    var margin = dayLabel.get('margin');\n    var firstDayOfWeek = coordSys.getFirstDayOfWeek();\n    if (!nameMap || isString(nameMap)) {\n      if (nameMap) {\n        // case-sensitive\n        localeModel = getLocaleModel(nameMap) || localeModel;\n      } // Use the first letter of `dayOfWeekAbbr` if `dayOfWeekShort` doesn't exist in the locale file\n\n      var dayOfWeekShort = localeModel.get(['time', 'dayOfWeekShort']);\n      nameMap = dayOfWeekShort || map(localeModel.get(['time', 'dayOfWeekAbbr']), function (val) {\n        return val[0];\n      });\n    }\n    var start = coordSys.getNextNDay(rangeData.end.time, 7 - rangeData.lweek).time;\n    var cellSize = [coordSys.getCellWidth(), coordSys.getCellHeight()];\n    margin = parsePercent(margin, Math.min(cellSize[1], cellSize[0]));\n    if (pos === 'start') {\n      start = coordSys.getNextNDay(rangeData.start.time, -(7 + rangeData.fweek)).time;\n      margin = -margin;\n    }\n    for (var i = 0; i < 7; i++) {\n      var tmpD = coordSys.getNextNDay(start, i);\n      var point = coordSys.dataToRect([tmpD.time], false).center;\n      var day = i;\n      day = Math.abs((i + firstDayOfWeek) % 7);\n      var weekText = new graphic.Text({\n        z2: 30,\n        style: extend(createTextStyle(dayLabel, {\n          text: nameMap[day]\n        }), this._weekTextPositionControl(point, orient, pos, margin, cellSize))\n      });\n      group.add(weekText);\n    }\n  };\n  CalendarView.type = 'calendar';\n  return CalendarView;\n}(ComponentView);\nexport default CalendarView;", "map": {"version": 3, "names": ["__extends", "isString", "extend", "map", "isFunction", "graphic", "createTextStyle", "formatTplSimple", "parsePercent", "ComponentView", "getLocaleModel", "CalendarView", "_super", "_this", "apply", "arguments", "type", "prototype", "render", "calendarModel", "ecModel", "api", "group", "removeAll", "coordSys", "coordinateSystem", "rangeData", "getRangeInfo", "orient", "getOrient", "localeModel", "_renderDayRect", "_renderLines", "_renderYearText", "_renderMonthText", "_renderWeekText", "itemRectStyleModel", "getModel", "getItemStyle", "sw", "get<PERSON>ell<PERSON><PERSON>th", "sh", "getCellHeight", "i", "start", "time", "end", "getNextNDay", "point", "dataToRect", "tl", "rect", "Rect", "shape", "x", "y", "width", "height", "cursor", "style", "add", "self", "lineStyleModel", "getLineStyle", "show", "get", "lineWidth", "_tlpoints", "_blpoints", "_firstDayOfMonth", "_firstDayPoints", "firstDay", "addPoints", "formatedDate", "getDateInfo", "m", "date", "setMonth", "getMonth", "push", "points", "_getLinePointsOfOneWeek", "length", "_drawSplitline", "_getEdgesPoints", "rs", "slice", "idx", "lineStyle", "poyline", "Polyline", "z2", "parsedDate", "tmpD", "day", "_formatterLabel", "formatter", "params", "nameMap", "_yearTextPositionControl", "textEl", "position", "margin", "aligns", "rotate", "Math", "PI", "rotation", "align", "verticalAlign", "<PERSON><PERSON><PERSON><PERSON>", "pos", "xc", "yc", "posPoints", "top", "bottom", "left", "right", "name", "content", "yearText", "Text", "text", "attr", "_monthTextPositionControl", "isCenter", "vAlign", "<PERSON><PERSON><PERSON><PERSON>", "termPoints", "axis", "tmp", "firstDayPoints", "name_1", "yyyy", "yy", "MM", "M", "monthText", "_weekTextPositionControl", "cellSize", "isStart", "<PERSON><PERSON><PERSON><PERSON>", "firstDayOfWeek", "getFirstDayOfWeek", "dayOfWeekShort", "val", "lweek", "min", "fweek", "center", "abs", "weekText"], "sources": ["C:/WolfSystemV2/WolfOldFrontEnd/node_modules/echarts/lib/component/calendar/CalendarView.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\nimport { isString, extend, map, isFunction } from 'zrender/lib/core/util.js';\nimport * as graphic from '../../util/graphic.js';\nimport { createTextStyle } from '../../label/labelStyle.js';\nimport { formatTplSimple } from '../../util/format.js';\nimport { parsePercent } from '../../util/number.js';\nimport ComponentView from '../../view/Component.js';\nimport { getLocaleModel } from '../../core/locale.js';\n\nvar CalendarView =\n/** @class */\nfunction (_super) {\n  __extends(CalendarView, _super);\n\n  function CalendarView() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n\n    _this.type = CalendarView.type;\n    return _this;\n  }\n\n  CalendarView.prototype.render = function (calendarModel, ecModel, api) {\n    var group = this.group;\n    group.removeAll();\n    var coordSys = calendarModel.coordinateSystem; // range info\n\n    var rangeData = coordSys.getRangeInfo();\n    var orient = coordSys.getOrient(); // locale\n\n    var localeModel = ecModel.getLocaleModel();\n\n    this._renderDayRect(calendarModel, rangeData, group); // _renderLines must be called prior to following function\n\n\n    this._renderLines(calendarModel, rangeData, orient, group);\n\n    this._renderYearText(calendarModel, rangeData, orient, group);\n\n    this._renderMonthText(calendarModel, localeModel, orient, group);\n\n    this._renderWeekText(calendarModel, localeModel, rangeData, orient, group);\n  }; // render day rect\n\n\n  CalendarView.prototype._renderDayRect = function (calendarModel, rangeData, group) {\n    var coordSys = calendarModel.coordinateSystem;\n    var itemRectStyleModel = calendarModel.getModel('itemStyle').getItemStyle();\n    var sw = coordSys.getCellWidth();\n    var sh = coordSys.getCellHeight();\n\n    for (var i = rangeData.start.time; i <= rangeData.end.time; i = coordSys.getNextNDay(i, 1).time) {\n      var point = coordSys.dataToRect([i], false).tl; // every rect\n\n      var rect = new graphic.Rect({\n        shape: {\n          x: point[0],\n          y: point[1],\n          width: sw,\n          height: sh\n        },\n        cursor: 'default',\n        style: itemRectStyleModel\n      });\n      group.add(rect);\n    }\n  }; // render separate line\n\n\n  CalendarView.prototype._renderLines = function (calendarModel, rangeData, orient, group) {\n    var self = this;\n    var coordSys = calendarModel.coordinateSystem;\n    var lineStyleModel = calendarModel.getModel(['splitLine', 'lineStyle']).getLineStyle();\n    var show = calendarModel.get(['splitLine', 'show']);\n    var lineWidth = lineStyleModel.lineWidth;\n    this._tlpoints = [];\n    this._blpoints = [];\n    this._firstDayOfMonth = [];\n    this._firstDayPoints = [];\n    var firstDay = rangeData.start;\n\n    for (var i = 0; firstDay.time <= rangeData.end.time; i++) {\n      addPoints(firstDay.formatedDate);\n\n      if (i === 0) {\n        firstDay = coordSys.getDateInfo(rangeData.start.y + '-' + rangeData.start.m);\n      }\n\n      var date = firstDay.date;\n      date.setMonth(date.getMonth() + 1);\n      firstDay = coordSys.getDateInfo(date);\n    }\n\n    addPoints(coordSys.getNextNDay(rangeData.end.time, 1).formatedDate);\n\n    function addPoints(date) {\n      self._firstDayOfMonth.push(coordSys.getDateInfo(date));\n\n      self._firstDayPoints.push(coordSys.dataToRect([date], false).tl);\n\n      var points = self._getLinePointsOfOneWeek(calendarModel, date, orient);\n\n      self._tlpoints.push(points[0]);\n\n      self._blpoints.push(points[points.length - 1]);\n\n      show && self._drawSplitline(points, lineStyleModel, group);\n    } // render top/left line\n\n\n    show && this._drawSplitline(self._getEdgesPoints(self._tlpoints, lineWidth, orient), lineStyleModel, group); // render bottom/right line\n\n    show && this._drawSplitline(self._getEdgesPoints(self._blpoints, lineWidth, orient), lineStyleModel, group);\n  }; // get points at both ends\n\n\n  CalendarView.prototype._getEdgesPoints = function (points, lineWidth, orient) {\n    var rs = [points[0].slice(), points[points.length - 1].slice()];\n    var idx = orient === 'horizontal' ? 0 : 1; // both ends of the line are extend half lineWidth\n\n    rs[0][idx] = rs[0][idx] - lineWidth / 2;\n    rs[1][idx] = rs[1][idx] + lineWidth / 2;\n    return rs;\n  }; // render split line\n\n\n  CalendarView.prototype._drawSplitline = function (points, lineStyle, group) {\n    var poyline = new graphic.Polyline({\n      z2: 20,\n      shape: {\n        points: points\n      },\n      style: lineStyle\n    });\n    group.add(poyline);\n  }; // render month line of one week points\n\n\n  CalendarView.prototype._getLinePointsOfOneWeek = function (calendarModel, date, orient) {\n    var coordSys = calendarModel.coordinateSystem;\n    var parsedDate = coordSys.getDateInfo(date);\n    var points = [];\n\n    for (var i = 0; i < 7; i++) {\n      var tmpD = coordSys.getNextNDay(parsedDate.time, i);\n      var point = coordSys.dataToRect([tmpD.time], false);\n      points[2 * tmpD.day] = point.tl;\n      points[2 * tmpD.day + 1] = point[orient === 'horizontal' ? 'bl' : 'tr'];\n    }\n\n    return points;\n  };\n\n  CalendarView.prototype._formatterLabel = function (formatter, params) {\n    if (isString(formatter) && formatter) {\n      return formatTplSimple(formatter, params);\n    }\n\n    if (isFunction(formatter)) {\n      return formatter(params);\n    }\n\n    return params.nameMap;\n  };\n\n  CalendarView.prototype._yearTextPositionControl = function (textEl, point, orient, position, margin) {\n    var x = point[0];\n    var y = point[1];\n    var aligns = ['center', 'bottom'];\n\n    if (position === 'bottom') {\n      y += margin;\n      aligns = ['center', 'top'];\n    } else if (position === 'left') {\n      x -= margin;\n    } else if (position === 'right') {\n      x += margin;\n      aligns = ['center', 'top'];\n    } else {\n      // top\n      y -= margin;\n    }\n\n    var rotate = 0;\n\n    if (position === 'left' || position === 'right') {\n      rotate = Math.PI / 2;\n    }\n\n    return {\n      rotation: rotate,\n      x: x,\n      y: y,\n      style: {\n        align: aligns[0],\n        verticalAlign: aligns[1]\n      }\n    };\n  }; // render year\n\n\n  CalendarView.prototype._renderYearText = function (calendarModel, rangeData, orient, group) {\n    var yearLabel = calendarModel.getModel('yearLabel');\n\n    if (!yearLabel.get('show')) {\n      return;\n    }\n\n    var margin = yearLabel.get('margin');\n    var pos = yearLabel.get('position');\n\n    if (!pos) {\n      pos = orient !== 'horizontal' ? 'top' : 'left';\n    }\n\n    var points = [this._tlpoints[this._tlpoints.length - 1], this._blpoints[0]];\n    var xc = (points[0][0] + points[1][0]) / 2;\n    var yc = (points[0][1] + points[1][1]) / 2;\n    var idx = orient === 'horizontal' ? 0 : 1;\n    var posPoints = {\n      top: [xc, points[idx][1]],\n      bottom: [xc, points[1 - idx][1]],\n      left: [points[1 - idx][0], yc],\n      right: [points[idx][0], yc]\n    };\n    var name = rangeData.start.y;\n\n    if (+rangeData.end.y > +rangeData.start.y) {\n      name = name + '-' + rangeData.end.y;\n    }\n\n    var formatter = yearLabel.get('formatter');\n    var params = {\n      start: rangeData.start.y,\n      end: rangeData.end.y,\n      nameMap: name\n    };\n\n    var content = this._formatterLabel(formatter, params);\n\n    var yearText = new graphic.Text({\n      z2: 30,\n      style: createTextStyle(yearLabel, {\n        text: content\n      })\n    });\n    yearText.attr(this._yearTextPositionControl(yearText, posPoints[pos], orient, pos, margin));\n    group.add(yearText);\n  };\n\n  CalendarView.prototype._monthTextPositionControl = function (point, isCenter, orient, position, margin) {\n    var align = 'left';\n    var vAlign = 'top';\n    var x = point[0];\n    var y = point[1];\n\n    if (orient === 'horizontal') {\n      y = y + margin;\n\n      if (isCenter) {\n        align = 'center';\n      }\n\n      if (position === 'start') {\n        vAlign = 'bottom';\n      }\n    } else {\n      x = x + margin;\n\n      if (isCenter) {\n        vAlign = 'middle';\n      }\n\n      if (position === 'start') {\n        align = 'right';\n      }\n    }\n\n    return {\n      x: x,\n      y: y,\n      align: align,\n      verticalAlign: vAlign\n    };\n  }; // render month and year text\n\n\n  CalendarView.prototype._renderMonthText = function (calendarModel, localeModel, orient, group) {\n    var monthLabel = calendarModel.getModel('monthLabel');\n\n    if (!monthLabel.get('show')) {\n      return;\n    }\n\n    var nameMap = monthLabel.get('nameMap');\n    var margin = monthLabel.get('margin');\n    var pos = monthLabel.get('position');\n    var align = monthLabel.get('align');\n    var termPoints = [this._tlpoints, this._blpoints];\n\n    if (!nameMap || isString(nameMap)) {\n      if (nameMap) {\n        // case-sensitive\n        localeModel = getLocaleModel(nameMap) || localeModel;\n      } // PENDING\n      // for ZH locale, original form is `一月` but current form is `1月`\n\n\n      nameMap = localeModel.get(['time', 'monthAbbr']) || [];\n    }\n\n    var idx = pos === 'start' ? 0 : 1;\n    var axis = orient === 'horizontal' ? 0 : 1;\n    margin = pos === 'start' ? -margin : margin;\n    var isCenter = align === 'center';\n\n    for (var i = 0; i < termPoints[idx].length - 1; i++) {\n      var tmp = termPoints[idx][i].slice();\n      var firstDay = this._firstDayOfMonth[i];\n\n      if (isCenter) {\n        var firstDayPoints = this._firstDayPoints[i];\n        tmp[axis] = (firstDayPoints[axis] + termPoints[0][i + 1][axis]) / 2;\n      }\n\n      var formatter = monthLabel.get('formatter');\n      var name_1 = nameMap[+firstDay.m - 1];\n      var params = {\n        yyyy: firstDay.y,\n        yy: (firstDay.y + '').slice(2),\n        MM: firstDay.m,\n        M: +firstDay.m,\n        nameMap: name_1\n      };\n\n      var content = this._formatterLabel(formatter, params);\n\n      var monthText = new graphic.Text({\n        z2: 30,\n        style: extend(createTextStyle(monthLabel, {\n          text: content\n        }), this._monthTextPositionControl(tmp, isCenter, orient, pos, margin))\n      });\n      group.add(monthText);\n    }\n  };\n\n  CalendarView.prototype._weekTextPositionControl = function (point, orient, position, margin, cellSize) {\n    var align = 'center';\n    var vAlign = 'middle';\n    var x = point[0];\n    var y = point[1];\n    var isStart = position === 'start';\n\n    if (orient === 'horizontal') {\n      x = x + margin + (isStart ? 1 : -1) * cellSize[0] / 2;\n      align = isStart ? 'right' : 'left';\n    } else {\n      y = y + margin + (isStart ? 1 : -1) * cellSize[1] / 2;\n      vAlign = isStart ? 'bottom' : 'top';\n    }\n\n    return {\n      x: x,\n      y: y,\n      align: align,\n      verticalAlign: vAlign\n    };\n  }; // render weeks\n\n\n  CalendarView.prototype._renderWeekText = function (calendarModel, localeModel, rangeData, orient, group) {\n    var dayLabel = calendarModel.getModel('dayLabel');\n\n    if (!dayLabel.get('show')) {\n      return;\n    }\n\n    var coordSys = calendarModel.coordinateSystem;\n    var pos = dayLabel.get('position');\n    var nameMap = dayLabel.get('nameMap');\n    var margin = dayLabel.get('margin');\n    var firstDayOfWeek = coordSys.getFirstDayOfWeek();\n\n    if (!nameMap || isString(nameMap)) {\n      if (nameMap) {\n        // case-sensitive\n        localeModel = getLocaleModel(nameMap) || localeModel;\n      } // Use the first letter of `dayOfWeekAbbr` if `dayOfWeekShort` doesn't exist in the locale file\n\n\n      var dayOfWeekShort = localeModel.get(['time', 'dayOfWeekShort']);\n      nameMap = dayOfWeekShort || map(localeModel.get(['time', 'dayOfWeekAbbr']), function (val) {\n        return val[0];\n      });\n    }\n\n    var start = coordSys.getNextNDay(rangeData.end.time, 7 - rangeData.lweek).time;\n    var cellSize = [coordSys.getCellWidth(), coordSys.getCellHeight()];\n    margin = parsePercent(margin, Math.min(cellSize[1], cellSize[0]));\n\n    if (pos === 'start') {\n      start = coordSys.getNextNDay(rangeData.start.time, -(7 + rangeData.fweek)).time;\n      margin = -margin;\n    }\n\n    for (var i = 0; i < 7; i++) {\n      var tmpD = coordSys.getNextNDay(start, i);\n      var point = coordSys.dataToRect([tmpD.time], false).center;\n      var day = i;\n      day = Math.abs((i + firstDayOfWeek) % 7);\n      var weekText = new graphic.Text({\n        z2: 30,\n        style: extend(createTextStyle(dayLabel, {\n          text: nameMap[day]\n        }), this._weekTextPositionControl(point, orient, pos, margin, cellSize))\n      });\n      group.add(weekText);\n    }\n  };\n\n  CalendarView.type = 'calendar';\n  return CalendarView;\n}(ComponentView);\n\nexport default CalendarView;"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,SAAS,QAAQ,OAAO;AACjC,SAASC,QAAQ,EAAEC,MAAM,EAAEC,GAAG,EAAEC,UAAU,QAAQ,0BAA0B;AAC5E,OAAO,KAAKC,OAAO,MAAM,uBAAuB;AAChD,SAASC,eAAe,QAAQ,2BAA2B;AAC3D,SAASC,eAAe,QAAQ,sBAAsB;AACtD,SAASC,YAAY,QAAQ,sBAAsB;AACnD,OAAOC,aAAa,MAAM,yBAAyB;AACnD,SAASC,cAAc,QAAQ,sBAAsB;AAErD,IAAIC,YAAY,GAChB;AACA,UAAUC,MAAM,EAAE;EAChBZ,SAAS,CAACW,YAAY,EAAEC,MAAM,CAAC;EAE/B,SAASD,YAAYA,CAAA,EAAG;IACtB,IAAIE,KAAK,GAAGD,MAAM,KAAK,IAAI,IAAIA,MAAM,CAACE,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC,IAAI,IAAI;IAEpEF,KAAK,CAACG,IAAI,GAAGL,YAAY,CAACK,IAAI;IAC9B,OAAOH,KAAK;EACd;EAEAF,YAAY,CAACM,SAAS,CAACC,MAAM,GAAG,UAAUC,aAAa,EAAEC,OAAO,EAAEC,GAAG,EAAE;IACrE,IAAIC,KAAK,GAAG,IAAI,CAACA,KAAK;IACtBA,KAAK,CAACC,SAAS,CAAC,CAAC;IACjB,IAAIC,QAAQ,GAAGL,aAAa,CAACM,gBAAgB,CAAC,CAAC;;IAE/C,IAAIC,SAAS,GAAGF,QAAQ,CAACG,YAAY,CAAC,CAAC;IACvC,IAAIC,MAAM,GAAGJ,QAAQ,CAACK,SAAS,CAAC,CAAC,CAAC,CAAC;;IAEnC,IAAIC,WAAW,GAAGV,OAAO,CAACV,cAAc,CAAC,CAAC;IAE1C,IAAI,CAACqB,cAAc,CAACZ,aAAa,EAAEO,SAAS,EAAEJ,KAAK,CAAC,CAAC,CAAC;;IAGtD,IAAI,CAACU,YAAY,CAACb,aAAa,EAAEO,SAAS,EAAEE,MAAM,EAAEN,KAAK,CAAC;IAE1D,IAAI,CAACW,eAAe,CAACd,aAAa,EAAEO,SAAS,EAAEE,MAAM,EAAEN,KAAK,CAAC;IAE7D,IAAI,CAACY,gBAAgB,CAACf,aAAa,EAAEW,WAAW,EAAEF,MAAM,EAAEN,KAAK,CAAC;IAEhE,IAAI,CAACa,eAAe,CAAChB,aAAa,EAAEW,WAAW,EAAEJ,SAAS,EAAEE,MAAM,EAAEN,KAAK,CAAC;EAC5E,CAAC,CAAC,CAAC;;EAGHX,YAAY,CAACM,SAAS,CAACc,cAAc,GAAG,UAAUZ,aAAa,EAAEO,SAAS,EAAEJ,KAAK,EAAE;IACjF,IAAIE,QAAQ,GAAGL,aAAa,CAACM,gBAAgB;IAC7C,IAAIW,kBAAkB,GAAGjB,aAAa,CAACkB,QAAQ,CAAC,WAAW,CAAC,CAACC,YAAY,CAAC,CAAC;IAC3E,IAAIC,EAAE,GAAGf,QAAQ,CAACgB,YAAY,CAAC,CAAC;IAChC,IAAIC,EAAE,GAAGjB,QAAQ,CAACkB,aAAa,CAAC,CAAC;IAEjC,KAAK,IAAIC,CAAC,GAAGjB,SAAS,CAACkB,KAAK,CAACC,IAAI,EAAEF,CAAC,IAAIjB,SAAS,CAACoB,GAAG,CAACD,IAAI,EAAEF,CAAC,GAAGnB,QAAQ,CAACuB,WAAW,CAACJ,CAAC,EAAE,CAAC,CAAC,CAACE,IAAI,EAAE;MAC/F,IAAIG,KAAK,GAAGxB,QAAQ,CAACyB,UAAU,CAAC,CAACN,CAAC,CAAC,EAAE,KAAK,CAAC,CAACO,EAAE,CAAC,CAAC;;MAEhD,IAAIC,IAAI,GAAG,IAAI9C,OAAO,CAAC+C,IAAI,CAAC;QAC1BC,KAAK,EAAE;UACLC,CAAC,EAAEN,KAAK,CAAC,CAAC,CAAC;UACXO,CAAC,EAAEP,KAAK,CAAC,CAAC,CAAC;UACXQ,KAAK,EAAEjB,EAAE;UACTkB,MAAM,EAAEhB;QACV,CAAC;QACDiB,MAAM,EAAE,SAAS;QACjBC,KAAK,EAAEvB;MACT,CAAC,CAAC;MACFd,KAAK,CAACsC,GAAG,CAACT,IAAI,CAAC;IACjB;EACF,CAAC,CAAC,CAAC;;EAGHxC,YAAY,CAACM,SAAS,CAACe,YAAY,GAAG,UAAUb,aAAa,EAAEO,SAAS,EAAEE,MAAM,EAAEN,KAAK,EAAE;IACvF,IAAIuC,IAAI,GAAG,IAAI;IACf,IAAIrC,QAAQ,GAAGL,aAAa,CAACM,gBAAgB;IAC7C,IAAIqC,cAAc,GAAG3C,aAAa,CAACkB,QAAQ,CAAC,CAAC,WAAW,EAAE,WAAW,CAAC,CAAC,CAAC0B,YAAY,CAAC,CAAC;IACtF,IAAIC,IAAI,GAAG7C,aAAa,CAAC8C,GAAG,CAAC,CAAC,WAAW,EAAE,MAAM,CAAC,CAAC;IACnD,IAAIC,SAAS,GAAGJ,cAAc,CAACI,SAAS;IACxC,IAAI,CAACC,SAAS,GAAG,EAAE;IACnB,IAAI,CAACC,SAAS,GAAG,EAAE;IACnB,IAAI,CAACC,gBAAgB,GAAG,EAAE;IAC1B,IAAI,CAACC,eAAe,GAAG,EAAE;IACzB,IAAIC,QAAQ,GAAG7C,SAAS,CAACkB,KAAK;IAE9B,KAAK,IAAID,CAAC,GAAG,CAAC,EAAE4B,QAAQ,CAAC1B,IAAI,IAAInB,SAAS,CAACoB,GAAG,CAACD,IAAI,EAAEF,CAAC,EAAE,EAAE;MACxD6B,SAAS,CAACD,QAAQ,CAACE,YAAY,CAAC;MAEhC,IAAI9B,CAAC,KAAK,CAAC,EAAE;QACX4B,QAAQ,GAAG/C,QAAQ,CAACkD,WAAW,CAAChD,SAAS,CAACkB,KAAK,CAACW,CAAC,GAAG,GAAG,GAAG7B,SAAS,CAACkB,KAAK,CAAC+B,CAAC,CAAC;MAC9E;MAEA,IAAIC,IAAI,GAAGL,QAAQ,CAACK,IAAI;MACxBA,IAAI,CAACC,QAAQ,CAACD,IAAI,CAACE,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC;MAClCP,QAAQ,GAAG/C,QAAQ,CAACkD,WAAW,CAACE,IAAI,CAAC;IACvC;IAEAJ,SAAS,CAAChD,QAAQ,CAACuB,WAAW,CAACrB,SAAS,CAACoB,GAAG,CAACD,IAAI,EAAE,CAAC,CAAC,CAAC4B,YAAY,CAAC;IAEnE,SAASD,SAASA,CAACI,IAAI,EAAE;MACvBf,IAAI,CAACQ,gBAAgB,CAACU,IAAI,CAACvD,QAAQ,CAACkD,WAAW,CAACE,IAAI,CAAC,CAAC;MAEtDf,IAAI,CAACS,eAAe,CAACS,IAAI,CAACvD,QAAQ,CAACyB,UAAU,CAAC,CAAC2B,IAAI,CAAC,EAAE,KAAK,CAAC,CAAC1B,EAAE,CAAC;MAEhE,IAAI8B,MAAM,GAAGnB,IAAI,CAACoB,uBAAuB,CAAC9D,aAAa,EAAEyD,IAAI,EAAEhD,MAAM,CAAC;MAEtEiC,IAAI,CAACM,SAAS,CAACY,IAAI,CAACC,MAAM,CAAC,CAAC,CAAC,CAAC;MAE9BnB,IAAI,CAACO,SAAS,CAACW,IAAI,CAACC,MAAM,CAACA,MAAM,CAACE,MAAM,GAAG,CAAC,CAAC,CAAC;MAE9ClB,IAAI,IAAIH,IAAI,CAACsB,cAAc,CAACH,MAAM,EAAElB,cAAc,EAAExC,KAAK,CAAC;IAC5D,CAAC,CAAC;;IAGF0C,IAAI,IAAI,IAAI,CAACmB,cAAc,CAACtB,IAAI,CAACuB,eAAe,CAACvB,IAAI,CAACM,SAAS,EAAED,SAAS,EAAEtC,MAAM,CAAC,EAAEkC,cAAc,EAAExC,KAAK,CAAC,CAAC,CAAC;;IAE7G0C,IAAI,IAAI,IAAI,CAACmB,cAAc,CAACtB,IAAI,CAACuB,eAAe,CAACvB,IAAI,CAACO,SAAS,EAAEF,SAAS,EAAEtC,MAAM,CAAC,EAAEkC,cAAc,EAAExC,KAAK,CAAC;EAC7G,CAAC,CAAC,CAAC;;EAGHX,YAAY,CAACM,SAAS,CAACmE,eAAe,GAAG,UAAUJ,MAAM,EAAEd,SAAS,EAAEtC,MAAM,EAAE;IAC5E,IAAIyD,EAAE,GAAG,CAACL,MAAM,CAAC,CAAC,CAAC,CAACM,KAAK,CAAC,CAAC,EAAEN,MAAM,CAACA,MAAM,CAACE,MAAM,GAAG,CAAC,CAAC,CAACI,KAAK,CAAC,CAAC,CAAC;IAC/D,IAAIC,GAAG,GAAG3D,MAAM,KAAK,YAAY,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;;IAE3CyD,EAAE,CAAC,CAAC,CAAC,CAACE,GAAG,CAAC,GAAGF,EAAE,CAAC,CAAC,CAAC,CAACE,GAAG,CAAC,GAAGrB,SAAS,GAAG,CAAC;IACvCmB,EAAE,CAAC,CAAC,CAAC,CAACE,GAAG,CAAC,GAAGF,EAAE,CAAC,CAAC,CAAC,CAACE,GAAG,CAAC,GAAGrB,SAAS,GAAG,CAAC;IACvC,OAAOmB,EAAE;EACX,CAAC,CAAC,CAAC;;EAGH1E,YAAY,CAACM,SAAS,CAACkE,cAAc,GAAG,UAAUH,MAAM,EAAEQ,SAAS,EAAElE,KAAK,EAAE;IAC1E,IAAImE,OAAO,GAAG,IAAIpF,OAAO,CAACqF,QAAQ,CAAC;MACjCC,EAAE,EAAE,EAAE;MACNtC,KAAK,EAAE;QACL2B,MAAM,EAAEA;MACV,CAAC;MACDrB,KAAK,EAAE6B;IACT,CAAC,CAAC;IACFlE,KAAK,CAACsC,GAAG,CAAC6B,OAAO,CAAC;EACpB,CAAC,CAAC,CAAC;;EAGH9E,YAAY,CAACM,SAAS,CAACgE,uBAAuB,GAAG,UAAU9D,aAAa,EAAEyD,IAAI,EAAEhD,MAAM,EAAE;IACtF,IAAIJ,QAAQ,GAAGL,aAAa,CAACM,gBAAgB;IAC7C,IAAImE,UAAU,GAAGpE,QAAQ,CAACkD,WAAW,CAACE,IAAI,CAAC;IAC3C,IAAII,MAAM,GAAG,EAAE;IAEf,KAAK,IAAIrC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC,EAAEA,CAAC,EAAE,EAAE;MAC1B,IAAIkD,IAAI,GAAGrE,QAAQ,CAACuB,WAAW,CAAC6C,UAAU,CAAC/C,IAAI,EAAEF,CAAC,CAAC;MACnD,IAAIK,KAAK,GAAGxB,QAAQ,CAACyB,UAAU,CAAC,CAAC4C,IAAI,CAAChD,IAAI,CAAC,EAAE,KAAK,CAAC;MACnDmC,MAAM,CAAC,CAAC,GAAGa,IAAI,CAACC,GAAG,CAAC,GAAG9C,KAAK,CAACE,EAAE;MAC/B8B,MAAM,CAAC,CAAC,GAAGa,IAAI,CAACC,GAAG,GAAG,CAAC,CAAC,GAAG9C,KAAK,CAACpB,MAAM,KAAK,YAAY,GAAG,IAAI,GAAG,IAAI,CAAC;IACzE;IAEA,OAAOoD,MAAM;EACf,CAAC;EAEDrE,YAAY,CAACM,SAAS,CAAC8E,eAAe,GAAG,UAAUC,SAAS,EAAEC,MAAM,EAAE;IACpE,IAAIhG,QAAQ,CAAC+F,SAAS,CAAC,IAAIA,SAAS,EAAE;MACpC,OAAOzF,eAAe,CAACyF,SAAS,EAAEC,MAAM,CAAC;IAC3C;IAEA,IAAI7F,UAAU,CAAC4F,SAAS,CAAC,EAAE;MACzB,OAAOA,SAAS,CAACC,MAAM,CAAC;IAC1B;IAEA,OAAOA,MAAM,CAACC,OAAO;EACvB,CAAC;EAEDvF,YAAY,CAACM,SAAS,CAACkF,wBAAwB,GAAG,UAAUC,MAAM,EAAEpD,KAAK,EAAEpB,MAAM,EAAEyE,QAAQ,EAAEC,MAAM,EAAE;IACnG,IAAIhD,CAAC,GAAGN,KAAK,CAAC,CAAC,CAAC;IAChB,IAAIO,CAAC,GAAGP,KAAK,CAAC,CAAC,CAAC;IAChB,IAAIuD,MAAM,GAAG,CAAC,QAAQ,EAAE,QAAQ,CAAC;IAEjC,IAAIF,QAAQ,KAAK,QAAQ,EAAE;MACzB9C,CAAC,IAAI+C,MAAM;MACXC,MAAM,GAAG,CAAC,QAAQ,EAAE,KAAK,CAAC;IAC5B,CAAC,MAAM,IAAIF,QAAQ,KAAK,MAAM,EAAE;MAC9B/C,CAAC,IAAIgD,MAAM;IACb,CAAC,MAAM,IAAID,QAAQ,KAAK,OAAO,EAAE;MAC/B/C,CAAC,IAAIgD,MAAM;MACXC,MAAM,GAAG,CAAC,QAAQ,EAAE,KAAK,CAAC;IAC5B,CAAC,MAAM;MACL;MACAhD,CAAC,IAAI+C,MAAM;IACb;IAEA,IAAIE,MAAM,GAAG,CAAC;IAEd,IAAIH,QAAQ,KAAK,MAAM,IAAIA,QAAQ,KAAK,OAAO,EAAE;MAC/CG,MAAM,GAAGC,IAAI,CAACC,EAAE,GAAG,CAAC;IACtB;IAEA,OAAO;MACLC,QAAQ,EAAEH,MAAM;MAChBlD,CAAC,EAAEA,CAAC;MACJC,CAAC,EAAEA,CAAC;MACJI,KAAK,EAAE;QACLiD,KAAK,EAAEL,MAAM,CAAC,CAAC,CAAC;QAChBM,aAAa,EAAEN,MAAM,CAAC,CAAC;MACzB;IACF,CAAC;EACH,CAAC,CAAC,CAAC;;EAGH5F,YAAY,CAACM,SAAS,CAACgB,eAAe,GAAG,UAAUd,aAAa,EAAEO,SAAS,EAAEE,MAAM,EAAEN,KAAK,EAAE;IAC1F,IAAIwF,SAAS,GAAG3F,aAAa,CAACkB,QAAQ,CAAC,WAAW,CAAC;IAEnD,IAAI,CAACyE,SAAS,CAAC7C,GAAG,CAAC,MAAM,CAAC,EAAE;MAC1B;IACF;IAEA,IAAIqC,MAAM,GAAGQ,SAAS,CAAC7C,GAAG,CAAC,QAAQ,CAAC;IACpC,IAAI8C,GAAG,GAAGD,SAAS,CAAC7C,GAAG,CAAC,UAAU,CAAC;IAEnC,IAAI,CAAC8C,GAAG,EAAE;MACRA,GAAG,GAAGnF,MAAM,KAAK,YAAY,GAAG,KAAK,GAAG,MAAM;IAChD;IAEA,IAAIoD,MAAM,GAAG,CAAC,IAAI,CAACb,SAAS,CAAC,IAAI,CAACA,SAAS,CAACe,MAAM,GAAG,CAAC,CAAC,EAAE,IAAI,CAACd,SAAS,CAAC,CAAC,CAAC,CAAC;IAC3E,IAAI4C,EAAE,GAAG,CAAChC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGA,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;IAC1C,IAAIiC,EAAE,GAAG,CAACjC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGA,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;IAC1C,IAAIO,GAAG,GAAG3D,MAAM,KAAK,YAAY,GAAG,CAAC,GAAG,CAAC;IACzC,IAAIsF,SAAS,GAAG;MACdC,GAAG,EAAE,CAACH,EAAE,EAAEhC,MAAM,CAACO,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;MACzB6B,MAAM,EAAE,CAACJ,EAAE,EAAEhC,MAAM,CAAC,CAAC,GAAGO,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;MAChC8B,IAAI,EAAE,CAACrC,MAAM,CAAC,CAAC,GAAGO,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE0B,EAAE,CAAC;MAC9BK,KAAK,EAAE,CAACtC,MAAM,CAACO,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE0B,EAAE;IAC5B,CAAC;IACD,IAAIM,IAAI,GAAG7F,SAAS,CAACkB,KAAK,CAACW,CAAC;IAE5B,IAAI,CAAC7B,SAAS,CAACoB,GAAG,CAACS,CAAC,GAAG,CAAC7B,SAAS,CAACkB,KAAK,CAACW,CAAC,EAAE;MACzCgE,IAAI,GAAGA,IAAI,GAAG,GAAG,GAAG7F,SAAS,CAACoB,GAAG,CAACS,CAAC;IACrC;IAEA,IAAIyC,SAAS,GAAGc,SAAS,CAAC7C,GAAG,CAAC,WAAW,CAAC;IAC1C,IAAIgC,MAAM,GAAG;MACXrD,KAAK,EAAElB,SAAS,CAACkB,KAAK,CAACW,CAAC;MACxBT,GAAG,EAAEpB,SAAS,CAACoB,GAAG,CAACS,CAAC;MACpB2C,OAAO,EAAEqB;IACX,CAAC;IAED,IAAIC,OAAO,GAAG,IAAI,CAACzB,eAAe,CAACC,SAAS,EAAEC,MAAM,CAAC;IAErD,IAAIwB,QAAQ,GAAG,IAAIpH,OAAO,CAACqH,IAAI,CAAC;MAC9B/B,EAAE,EAAE,EAAE;MACNhC,KAAK,EAAErD,eAAe,CAACwG,SAAS,EAAE;QAChCa,IAAI,EAAEH;MACR,CAAC;IACH,CAAC,CAAC;IACFC,QAAQ,CAACG,IAAI,CAAC,IAAI,CAACzB,wBAAwB,CAACsB,QAAQ,EAAEP,SAAS,CAACH,GAAG,CAAC,EAAEnF,MAAM,EAAEmF,GAAG,EAAET,MAAM,CAAC,CAAC;IAC3FhF,KAAK,CAACsC,GAAG,CAAC6D,QAAQ,CAAC;EACrB,CAAC;EAED9G,YAAY,CAACM,SAAS,CAAC4G,yBAAyB,GAAG,UAAU7E,KAAK,EAAE8E,QAAQ,EAAElG,MAAM,EAAEyE,QAAQ,EAAEC,MAAM,EAAE;IACtG,IAAIM,KAAK,GAAG,MAAM;IAClB,IAAImB,MAAM,GAAG,KAAK;IAClB,IAAIzE,CAAC,GAAGN,KAAK,CAAC,CAAC,CAAC;IAChB,IAAIO,CAAC,GAAGP,KAAK,CAAC,CAAC,CAAC;IAEhB,IAAIpB,MAAM,KAAK,YAAY,EAAE;MAC3B2B,CAAC,GAAGA,CAAC,GAAG+C,MAAM;MAEd,IAAIwB,QAAQ,EAAE;QACZlB,KAAK,GAAG,QAAQ;MAClB;MAEA,IAAIP,QAAQ,KAAK,OAAO,EAAE;QACxB0B,MAAM,GAAG,QAAQ;MACnB;IACF,CAAC,MAAM;MACLzE,CAAC,GAAGA,CAAC,GAAGgD,MAAM;MAEd,IAAIwB,QAAQ,EAAE;QACZC,MAAM,GAAG,QAAQ;MACnB;MAEA,IAAI1B,QAAQ,KAAK,OAAO,EAAE;QACxBO,KAAK,GAAG,OAAO;MACjB;IACF;IAEA,OAAO;MACLtD,CAAC,EAAEA,CAAC;MACJC,CAAC,EAAEA,CAAC;MACJqD,KAAK,EAAEA,KAAK;MACZC,aAAa,EAAEkB;IACjB,CAAC;EACH,CAAC,CAAC,CAAC;;EAGHpH,YAAY,CAACM,SAAS,CAACiB,gBAAgB,GAAG,UAAUf,aAAa,EAAEW,WAAW,EAAEF,MAAM,EAAEN,KAAK,EAAE;IAC7F,IAAI0G,UAAU,GAAG7G,aAAa,CAACkB,QAAQ,CAAC,YAAY,CAAC;IAErD,IAAI,CAAC2F,UAAU,CAAC/D,GAAG,CAAC,MAAM,CAAC,EAAE;MAC3B;IACF;IAEA,IAAIiC,OAAO,GAAG8B,UAAU,CAAC/D,GAAG,CAAC,SAAS,CAAC;IACvC,IAAIqC,MAAM,GAAG0B,UAAU,CAAC/D,GAAG,CAAC,QAAQ,CAAC;IACrC,IAAI8C,GAAG,GAAGiB,UAAU,CAAC/D,GAAG,CAAC,UAAU,CAAC;IACpC,IAAI2C,KAAK,GAAGoB,UAAU,CAAC/D,GAAG,CAAC,OAAO,CAAC;IACnC,IAAIgE,UAAU,GAAG,CAAC,IAAI,CAAC9D,SAAS,EAAE,IAAI,CAACC,SAAS,CAAC;IAEjD,IAAI,CAAC8B,OAAO,IAAIjG,QAAQ,CAACiG,OAAO,CAAC,EAAE;MACjC,IAAIA,OAAO,EAAE;QACX;QACApE,WAAW,GAAGpB,cAAc,CAACwF,OAAO,CAAC,IAAIpE,WAAW;MACtD,CAAC,CAAC;MACF;;MAGAoE,OAAO,GAAGpE,WAAW,CAACmC,GAAG,CAAC,CAAC,MAAM,EAAE,WAAW,CAAC,CAAC,IAAI,EAAE;IACxD;IAEA,IAAIsB,GAAG,GAAGwB,GAAG,KAAK,OAAO,GAAG,CAAC,GAAG,CAAC;IACjC,IAAImB,IAAI,GAAGtG,MAAM,KAAK,YAAY,GAAG,CAAC,GAAG,CAAC;IAC1C0E,MAAM,GAAGS,GAAG,KAAK,OAAO,GAAG,CAACT,MAAM,GAAGA,MAAM;IAC3C,IAAIwB,QAAQ,GAAGlB,KAAK,KAAK,QAAQ;IAEjC,KAAK,IAAIjE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGsF,UAAU,CAAC1C,GAAG,CAAC,CAACL,MAAM,GAAG,CAAC,EAAEvC,CAAC,EAAE,EAAE;MACnD,IAAIwF,GAAG,GAAGF,UAAU,CAAC1C,GAAG,CAAC,CAAC5C,CAAC,CAAC,CAAC2C,KAAK,CAAC,CAAC;MACpC,IAAIf,QAAQ,GAAG,IAAI,CAACF,gBAAgB,CAAC1B,CAAC,CAAC;MAEvC,IAAImF,QAAQ,EAAE;QACZ,IAAIM,cAAc,GAAG,IAAI,CAAC9D,eAAe,CAAC3B,CAAC,CAAC;QAC5CwF,GAAG,CAACD,IAAI,CAAC,GAAG,CAACE,cAAc,CAACF,IAAI,CAAC,GAAGD,UAAU,CAAC,CAAC,CAAC,CAACtF,CAAC,GAAG,CAAC,CAAC,CAACuF,IAAI,CAAC,IAAI,CAAC;MACrE;MAEA,IAAIlC,SAAS,GAAGgC,UAAU,CAAC/D,GAAG,CAAC,WAAW,CAAC;MAC3C,IAAIoE,MAAM,GAAGnC,OAAO,CAAC,CAAC3B,QAAQ,CAACI,CAAC,GAAG,CAAC,CAAC;MACrC,IAAIsB,MAAM,GAAG;QACXqC,IAAI,EAAE/D,QAAQ,CAAChB,CAAC;QAChBgF,EAAE,EAAE,CAAChE,QAAQ,CAAChB,CAAC,GAAG,EAAE,EAAE+B,KAAK,CAAC,CAAC,CAAC;QAC9BkD,EAAE,EAAEjE,QAAQ,CAACI,CAAC;QACd8D,CAAC,EAAE,CAAClE,QAAQ,CAACI,CAAC;QACduB,OAAO,EAAEmC;MACX,CAAC;MAED,IAAIb,OAAO,GAAG,IAAI,CAACzB,eAAe,CAACC,SAAS,EAAEC,MAAM,CAAC;MAErD,IAAIyC,SAAS,GAAG,IAAIrI,OAAO,CAACqH,IAAI,CAAC;QAC/B/B,EAAE,EAAE,EAAE;QACNhC,KAAK,EAAEzD,MAAM,CAACI,eAAe,CAAC0H,UAAU,EAAE;UACxCL,IAAI,EAAEH;QACR,CAAC,CAAC,EAAE,IAAI,CAACK,yBAAyB,CAACM,GAAG,EAAEL,QAAQ,EAAElG,MAAM,EAAEmF,GAAG,EAAET,MAAM,CAAC;MACxE,CAAC,CAAC;MACFhF,KAAK,CAACsC,GAAG,CAAC8E,SAAS,CAAC;IACtB;EACF,CAAC;EAED/H,YAAY,CAACM,SAAS,CAAC0H,wBAAwB,GAAG,UAAU3F,KAAK,EAAEpB,MAAM,EAAEyE,QAAQ,EAAEC,MAAM,EAAEsC,QAAQ,EAAE;IACrG,IAAIhC,KAAK,GAAG,QAAQ;IACpB,IAAImB,MAAM,GAAG,QAAQ;IACrB,IAAIzE,CAAC,GAAGN,KAAK,CAAC,CAAC,CAAC;IAChB,IAAIO,CAAC,GAAGP,KAAK,CAAC,CAAC,CAAC;IAChB,IAAI6F,OAAO,GAAGxC,QAAQ,KAAK,OAAO;IAElC,IAAIzE,MAAM,KAAK,YAAY,EAAE;MAC3B0B,CAAC,GAAGA,CAAC,GAAGgD,MAAM,GAAG,CAACuC,OAAO,GAAG,CAAC,GAAG,CAAC,CAAC,IAAID,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC;MACrDhC,KAAK,GAAGiC,OAAO,GAAG,OAAO,GAAG,MAAM;IACpC,CAAC,MAAM;MACLtF,CAAC,GAAGA,CAAC,GAAG+C,MAAM,GAAG,CAACuC,OAAO,GAAG,CAAC,GAAG,CAAC,CAAC,IAAID,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC;MACrDb,MAAM,GAAGc,OAAO,GAAG,QAAQ,GAAG,KAAK;IACrC;IAEA,OAAO;MACLvF,CAAC,EAAEA,CAAC;MACJC,CAAC,EAAEA,CAAC;MACJqD,KAAK,EAAEA,KAAK;MACZC,aAAa,EAAEkB;IACjB,CAAC;EACH,CAAC,CAAC,CAAC;;EAGHpH,YAAY,CAACM,SAAS,CAACkB,eAAe,GAAG,UAAUhB,aAAa,EAAEW,WAAW,EAAEJ,SAAS,EAAEE,MAAM,EAAEN,KAAK,EAAE;IACvG,IAAIwH,QAAQ,GAAG3H,aAAa,CAACkB,QAAQ,CAAC,UAAU,CAAC;IAEjD,IAAI,CAACyG,QAAQ,CAAC7E,GAAG,CAAC,MAAM,CAAC,EAAE;MACzB;IACF;IAEA,IAAIzC,QAAQ,GAAGL,aAAa,CAACM,gBAAgB;IAC7C,IAAIsF,GAAG,GAAG+B,QAAQ,CAAC7E,GAAG,CAAC,UAAU,CAAC;IAClC,IAAIiC,OAAO,GAAG4C,QAAQ,CAAC7E,GAAG,CAAC,SAAS,CAAC;IACrC,IAAIqC,MAAM,GAAGwC,QAAQ,CAAC7E,GAAG,CAAC,QAAQ,CAAC;IACnC,IAAI8E,cAAc,GAAGvH,QAAQ,CAACwH,iBAAiB,CAAC,CAAC;IAEjD,IAAI,CAAC9C,OAAO,IAAIjG,QAAQ,CAACiG,OAAO,CAAC,EAAE;MACjC,IAAIA,OAAO,EAAE;QACX;QACApE,WAAW,GAAGpB,cAAc,CAACwF,OAAO,CAAC,IAAIpE,WAAW;MACtD,CAAC,CAAC;;MAGF,IAAImH,cAAc,GAAGnH,WAAW,CAACmC,GAAG,CAAC,CAAC,MAAM,EAAE,gBAAgB,CAAC,CAAC;MAChEiC,OAAO,GAAG+C,cAAc,IAAI9I,GAAG,CAAC2B,WAAW,CAACmC,GAAG,CAAC,CAAC,MAAM,EAAE,eAAe,CAAC,CAAC,EAAE,UAAUiF,GAAG,EAAE;QACzF,OAAOA,GAAG,CAAC,CAAC,CAAC;MACf,CAAC,CAAC;IACJ;IAEA,IAAItG,KAAK,GAAGpB,QAAQ,CAACuB,WAAW,CAACrB,SAAS,CAACoB,GAAG,CAACD,IAAI,EAAE,CAAC,GAAGnB,SAAS,CAACyH,KAAK,CAAC,CAACtG,IAAI;IAC9E,IAAI+F,QAAQ,GAAG,CAACpH,QAAQ,CAACgB,YAAY,CAAC,CAAC,EAAEhB,QAAQ,CAACkB,aAAa,CAAC,CAAC,CAAC;IAClE4D,MAAM,GAAG9F,YAAY,CAAC8F,MAAM,EAAEG,IAAI,CAAC2C,GAAG,CAACR,QAAQ,CAAC,CAAC,CAAC,EAAEA,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;IAEjE,IAAI7B,GAAG,KAAK,OAAO,EAAE;MACnBnE,KAAK,GAAGpB,QAAQ,CAACuB,WAAW,CAACrB,SAAS,CAACkB,KAAK,CAACC,IAAI,EAAE,EAAE,CAAC,GAAGnB,SAAS,CAAC2H,KAAK,CAAC,CAAC,CAACxG,IAAI;MAC/EyD,MAAM,GAAG,CAACA,MAAM;IAClB;IAEA,KAAK,IAAI3D,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC,EAAEA,CAAC,EAAE,EAAE;MAC1B,IAAIkD,IAAI,GAAGrE,QAAQ,CAACuB,WAAW,CAACH,KAAK,EAAED,CAAC,CAAC;MACzC,IAAIK,KAAK,GAAGxB,QAAQ,CAACyB,UAAU,CAAC,CAAC4C,IAAI,CAAChD,IAAI,CAAC,EAAE,KAAK,CAAC,CAACyG,MAAM;MAC1D,IAAIxD,GAAG,GAAGnD,CAAC;MACXmD,GAAG,GAAGW,IAAI,CAAC8C,GAAG,CAAC,CAAC5G,CAAC,GAAGoG,cAAc,IAAI,CAAC,CAAC;MACxC,IAAIS,QAAQ,GAAG,IAAInJ,OAAO,CAACqH,IAAI,CAAC;QAC9B/B,EAAE,EAAE,EAAE;QACNhC,KAAK,EAAEzD,MAAM,CAACI,eAAe,CAACwI,QAAQ,EAAE;UACtCnB,IAAI,EAAEzB,OAAO,CAACJ,GAAG;QACnB,CAAC,CAAC,EAAE,IAAI,CAAC6C,wBAAwB,CAAC3F,KAAK,EAAEpB,MAAM,EAAEmF,GAAG,EAAET,MAAM,EAAEsC,QAAQ,CAAC;MACzE,CAAC,CAAC;MACFtH,KAAK,CAACsC,GAAG,CAAC4F,QAAQ,CAAC;IACrB;EACF,CAAC;EAED7I,YAAY,CAACK,IAAI,GAAG,UAAU;EAC9B,OAAOL,YAAY;AACrB,CAAC,CAACF,aAAa,CAAC;AAEhB,eAAeE,YAAY"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}