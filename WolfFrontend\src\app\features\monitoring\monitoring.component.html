<div class="monitoring-container">
  <!-- Header -->
  <div class="header">
    <h1 class="title">Central de Monitoramento</h1>
  </div>

  <!-- Main Grid Layout -->
  <div class="main-grid">
    <!-- Top Countries Section -->
    <div class="top-countries-section">
      <h2 class="section-title">Top 3 Países</h2>
      <app-top-countries [countries]="topCountries"></app-top-countries>
    </div>

    <!-- Globe Section -->
    <div class="globe-section">
      <h2 class="section-title">Número de Conexões</h2>
      <app-globe [globeData]="globeData"></app-globe>
      <div class="connection-time">
        <span class="time-label">Tempo médio de conexão:</span>
        <span class="time-value">{{ stats.averageConnectionTime }}</span>
      </div>
    </div>

    <!-- Connection Logs Section -->
    <div class="logs-section">
      <h2 class="section-title">Logs de Conexão e Desconexão</h2>
      <app-connection-logs [logs]="connectionLogs"></app-connection-logs>
    </div>

    <!-- Ban Management Section -->
    <div class="ban-management-section">
      <h2 class="section-title">Central de Banimentos</h2>
      <app-ban-management></app-ban-management>
    </div>
  </div>

  <!-- Stats Bar -->
  <div class="stats-bar">
    <div class="stat-item">
      <span class="stat-label">IPs Únicos:</span>
      <span class="stat-value">{{ stats.totalUniqueIPs }}</span>
    </div>
    <div class="stat-item">
      <span class="stat-label">Conexões Ativas:</span>
      <span class="stat-value">{{ stats.activeConnections }}</span>
    </div>
    <div class="stat-item">
      <span class="stat-label">Países:</span>
      <span class="stat-value">{{ stats.totalCountries }}</span>
    </div>
  </div>
</div>
