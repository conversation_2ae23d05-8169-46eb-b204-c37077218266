{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\n/* global Uint32Array, Float64Array, Float32Array */\n\nimport SeriesModel from '../../model/Series.js';\nimport SeriesData from '../../data/SeriesData.js';\nimport { concatArray, mergeAll, map, isNumber } from 'zrender/lib/core/util.js';\nimport CoordinateSystem from '../../core/CoordinateSystem.js';\nimport { createTooltipMarkup } from '../../component/tooltip/tooltipMarkup.js';\nvar Uint32Arr = typeof Uint32Array === 'undefined' ? Array : Uint32Array;\nvar Float64Arr = typeof Float64Array === 'undefined' ? Array : Float64Array;\nfunction compatEc2(seriesOpt) {\n  var data = seriesOpt.data;\n  if (data && data[0] && data[0][0] && data[0][0].coord) {\n    if (process.env.NODE_ENV !== 'production') {\n      console.warn('Lines data configuration has been changed to' + ' { coords:[[1,2],[2,3]] }');\n    }\n    seriesOpt.data = map(data, function (itemOpt) {\n      var coords = [itemOpt[0].coord, itemOpt[1].coord];\n      var target = {\n        coords: coords\n      };\n      if (itemOpt[0].name) {\n        target.fromName = itemOpt[0].name;\n      }\n      if (itemOpt[1].name) {\n        target.toName = itemOpt[1].name;\n      }\n      return mergeAll([target, itemOpt[0], itemOpt[1]]);\n    });\n  }\n}\nvar LinesSeriesModel = /** @class */\nfunction (_super) {\n  __extends(LinesSeriesModel, _super);\n  function LinesSeriesModel() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.type = LinesSeriesModel.type;\n    _this.visualStyleAccessPath = 'lineStyle';\n    _this.visualDrawType = 'stroke';\n    return _this;\n  }\n  LinesSeriesModel.prototype.init = function (option) {\n    // The input data may be null/undefined.\n    option.data = option.data || []; // Not using preprocessor because mergeOption may not have series.type\n\n    compatEc2(option);\n    var result = this._processFlatCoordsArray(option.data);\n    this._flatCoords = result.flatCoords;\n    this._flatCoordsOffset = result.flatCoordsOffset;\n    if (result.flatCoords) {\n      option.data = new Float32Array(result.count);\n    }\n    _super.prototype.init.apply(this, arguments);\n  };\n  LinesSeriesModel.prototype.mergeOption = function (option) {\n    compatEc2(option);\n    if (option.data) {\n      // Only update when have option data to merge.\n      var result = this._processFlatCoordsArray(option.data);\n      this._flatCoords = result.flatCoords;\n      this._flatCoordsOffset = result.flatCoordsOffset;\n      if (result.flatCoords) {\n        option.data = new Float32Array(result.count);\n      }\n    }\n    _super.prototype.mergeOption.apply(this, arguments);\n  };\n  LinesSeriesModel.prototype.appendData = function (params) {\n    var result = this._processFlatCoordsArray(params.data);\n    if (result.flatCoords) {\n      if (!this._flatCoords) {\n        this._flatCoords = result.flatCoords;\n        this._flatCoordsOffset = result.flatCoordsOffset;\n      } else {\n        this._flatCoords = concatArray(this._flatCoords, result.flatCoords);\n        this._flatCoordsOffset = concatArray(this._flatCoordsOffset, result.flatCoordsOffset);\n      }\n      params.data = new Float32Array(result.count);\n    }\n    this.getRawData().appendData(params.data);\n  };\n  LinesSeriesModel.prototype._getCoordsFromItemModel = function (idx) {\n    var itemModel = this.getData().getItemModel(idx);\n    var coords = itemModel.option instanceof Array ? itemModel.option : itemModel.getShallow('coords');\n    if (process.env.NODE_ENV !== 'production') {\n      if (!(coords instanceof Array && coords.length > 0 && coords[0] instanceof Array)) {\n        throw new Error('Invalid coords ' + JSON.stringify(coords) + '. Lines must have 2d coords array in data item.');\n      }\n    }\n    return coords;\n  };\n  LinesSeriesModel.prototype.getLineCoordsCount = function (idx) {\n    if (this._flatCoordsOffset) {\n      return this._flatCoordsOffset[idx * 2 + 1];\n    } else {\n      return this._getCoordsFromItemModel(idx).length;\n    }\n  };\n  LinesSeriesModel.prototype.getLineCoords = function (idx, out) {\n    if (this._flatCoordsOffset) {\n      var offset = this._flatCoordsOffset[idx * 2];\n      var len = this._flatCoordsOffset[idx * 2 + 1];\n      for (var i = 0; i < len; i++) {\n        out[i] = out[i] || [];\n        out[i][0] = this._flatCoords[offset + i * 2];\n        out[i][1] = this._flatCoords[offset + i * 2 + 1];\n      }\n      return len;\n    } else {\n      var coords = this._getCoordsFromItemModel(idx);\n      for (var i = 0; i < coords.length; i++) {\n        out[i] = out[i] || [];\n        out[i][0] = coords[i][0];\n        out[i][1] = coords[i][1];\n      }\n      return coords.length;\n    }\n  };\n  LinesSeriesModel.prototype._processFlatCoordsArray = function (data) {\n    var startOffset = 0;\n    if (this._flatCoords) {\n      startOffset = this._flatCoords.length;\n    } // Stored as a typed array. In format\n    // Points Count(2) | x | y | x | y | Points Count(3) | x |  y | x | y | x | y |\n\n    if (isNumber(data[0])) {\n      var len = data.length; // Store offset and len of each segment\n\n      var coordsOffsetAndLenStorage = new Uint32Arr(len);\n      var coordsStorage = new Float64Arr(len);\n      var coordsCursor = 0;\n      var offsetCursor = 0;\n      var dataCount = 0;\n      for (var i = 0; i < len;) {\n        dataCount++;\n        var count = data[i++]; // Offset\n\n        coordsOffsetAndLenStorage[offsetCursor++] = coordsCursor + startOffset; // Len\n\n        coordsOffsetAndLenStorage[offsetCursor++] = count;\n        for (var k = 0; k < count; k++) {\n          var x = data[i++];\n          var y = data[i++];\n          coordsStorage[coordsCursor++] = x;\n          coordsStorage[coordsCursor++] = y;\n          if (i > len) {\n            if (process.env.NODE_ENV !== 'production') {\n              throw new Error('Invalid data format.');\n            }\n          }\n        }\n      }\n      return {\n        flatCoordsOffset: new Uint32Array(coordsOffsetAndLenStorage.buffer, 0, offsetCursor),\n        flatCoords: coordsStorage,\n        count: dataCount\n      };\n    }\n    return {\n      flatCoordsOffset: null,\n      flatCoords: null,\n      count: data.length\n    };\n  };\n  LinesSeriesModel.prototype.getInitialData = function (option, ecModel) {\n    if (process.env.NODE_ENV !== 'production') {\n      var CoordSys = CoordinateSystem.get(option.coordinateSystem);\n      if (!CoordSys) {\n        throw new Error('Unknown coordinate system ' + option.coordinateSystem);\n      }\n    }\n    var lineData = new SeriesData(['value'], this);\n    lineData.hasItemOption = false;\n    lineData.initData(option.data, [], function (dataItem, dimName, dataIndex, dimIndex) {\n      // dataItem is simply coords\n      if (dataItem instanceof Array) {\n        return NaN;\n      } else {\n        lineData.hasItemOption = true;\n        var value = dataItem.value;\n        if (value != null) {\n          return value instanceof Array ? value[dimIndex] : value;\n        }\n      }\n    });\n    return lineData;\n  };\n  LinesSeriesModel.prototype.formatTooltip = function (dataIndex, multipleSeries, dataType) {\n    var data = this.getData();\n    var itemModel = data.getItemModel(dataIndex);\n    var name = itemModel.get('name');\n    if (name) {\n      return name;\n    }\n    var fromName = itemModel.get('fromName');\n    var toName = itemModel.get('toName');\n    var nameArr = [];\n    fromName != null && nameArr.push(fromName);\n    toName != null && nameArr.push(toName);\n    return createTooltipMarkup('nameValue', {\n      name: nameArr.join(' > ')\n    });\n  };\n  LinesSeriesModel.prototype.preventIncremental = function () {\n    return !!this.get(['effect', 'show']);\n  };\n  LinesSeriesModel.prototype.getProgressive = function () {\n    var progressive = this.option.progressive;\n    if (progressive == null) {\n      return this.option.large ? 1e4 : this.get('progressive');\n    }\n    return progressive;\n  };\n  LinesSeriesModel.prototype.getProgressiveThreshold = function () {\n    var progressiveThreshold = this.option.progressiveThreshold;\n    if (progressiveThreshold == null) {\n      return this.option.large ? 2e4 : this.get('progressiveThreshold');\n    }\n    return progressiveThreshold;\n  };\n  LinesSeriesModel.prototype.getZLevelKey = function () {\n    var effectModel = this.getModel('effect');\n    var trailLength = effectModel.get('trailLength');\n    return this.getData().count() > this.getProgressiveThreshold() // Each progressive series has individual key.\n    ? this.id : effectModel.get('show') && trailLength > 0 ? trailLength + '' : '';\n  };\n  LinesSeriesModel.type = 'series.lines';\n  LinesSeriesModel.dependencies = ['grid', 'polar', 'geo', 'calendar'];\n  LinesSeriesModel.defaultOption = {\n    coordinateSystem: 'geo',\n    // zlevel: 0,\n    z: 2,\n    legendHoverLink: true,\n    // Cartesian coordinate system\n    xAxisIndex: 0,\n    yAxisIndex: 0,\n    symbol: ['none', 'none'],\n    symbolSize: [10, 10],\n    // Geo coordinate system\n    geoIndex: 0,\n    effect: {\n      show: false,\n      period: 4,\n      constantSpeed: 0,\n      symbol: 'circle',\n      symbolSize: 3,\n      loop: true,\n      trailLength: 0.2\n    },\n    large: false,\n    // Available when large is true\n    largeThreshold: 2000,\n    polyline: false,\n    clip: true,\n    label: {\n      show: false,\n      position: 'end' // distance: 5,\n      // formatter: 标签文本格式器，同Tooltip.formatter，不支持异步回调\n    },\n\n    lineStyle: {\n      opacity: 0.5\n    }\n  };\n  return LinesSeriesModel;\n}(SeriesModel);\nexport default LinesSeriesModel;", "map": {"version": 3, "names": ["__extends", "SeriesModel", "SeriesData", "concatArray", "mergeAll", "map", "isNumber", "CoordinateSystem", "createTooltipMarkup", "Uint32Arr", "Uint32Array", "Array", "Float64Arr", "Float64Array", "compatEc2", "seriesOpt", "data", "coord", "process", "env", "NODE_ENV", "console", "warn", "itemOpt", "coords", "target", "name", "fromName", "to<PERSON>ame", "LinesSeriesModel", "_super", "_this", "apply", "arguments", "type", "visualStyleAccessPath", "visualDrawType", "prototype", "init", "option", "result", "_processFlatCoordsArray", "_flatCoords", "flatCoords", "_flatCoordsOffset", "flatCoordsOffset", "Float32Array", "count", "mergeOption", "appendData", "params", "getRawData", "_getCoordsFromItemModel", "idx", "itemModel", "getData", "getItemModel", "getShallow", "length", "Error", "JSON", "stringify", "getLineCoordsCount", "getLineCoords", "out", "offset", "len", "i", "startOffset", "coordsOffsetAndLenStorage", "coordsStorage", "coordsCursor", "offsetCursor", "dataCount", "k", "x", "y", "buffer", "getInitialData", "ecModel", "CoordSys", "get", "coordinateSystem", "lineData", "hasItemOption", "initData", "dataItem", "dimName", "dataIndex", "dimIndex", "NaN", "value", "formatTooltip", "multipleSeries", "dataType", "nameArr", "push", "join", "preventIncremental", "getProgressive", "progressive", "large", "getProgressiveThreshold", "progressiveThreshold", "getZLevelKey", "effectModel", "getModel", "trailLength", "id", "dependencies", "defaultOption", "z", "legendHoverLink", "xAxisIndex", "yAxisIndex", "symbol", "symbolSize", "geoIndex", "effect", "show", "period", "constantSpeed", "loop", "largeThreshold", "polyline", "clip", "label", "position", "lineStyle", "opacity"], "sources": ["C:/WolfSystemV2/WolfOldFrontEnd/node_modules/echarts/lib/chart/lines/LinesSeries.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\n/* global Uint32Array, Float64Array, Float32Array */\n\nimport SeriesModel from '../../model/Series.js';\nimport SeriesData from '../../data/SeriesData.js';\nimport { concatArray, mergeAll, map, isNumber } from 'zrender/lib/core/util.js';\nimport CoordinateSystem from '../../core/CoordinateSystem.js';\nimport { createTooltipMarkup } from '../../component/tooltip/tooltipMarkup.js';\nvar Uint32Arr = typeof Uint32Array === 'undefined' ? Array : Uint32Array;\nvar Float64Arr = typeof Float64Array === 'undefined' ? Array : Float64Array;\n\nfunction compatEc2(seriesOpt) {\n  var data = seriesOpt.data;\n\n  if (data && data[0] && data[0][0] && data[0][0].coord) {\n    if (process.env.NODE_ENV !== 'production') {\n      console.warn('Lines data configuration has been changed to' + ' { coords:[[1,2],[2,3]] }');\n    }\n\n    seriesOpt.data = map(data, function (itemOpt) {\n      var coords = [itemOpt[0].coord, itemOpt[1].coord];\n      var target = {\n        coords: coords\n      };\n\n      if (itemOpt[0].name) {\n        target.fromName = itemOpt[0].name;\n      }\n\n      if (itemOpt[1].name) {\n        target.toName = itemOpt[1].name;\n      }\n\n      return mergeAll([target, itemOpt[0], itemOpt[1]]);\n    });\n  }\n}\n\nvar LinesSeriesModel =\n/** @class */\nfunction (_super) {\n  __extends(LinesSeriesModel, _super);\n\n  function LinesSeriesModel() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n\n    _this.type = LinesSeriesModel.type;\n    _this.visualStyleAccessPath = 'lineStyle';\n    _this.visualDrawType = 'stroke';\n    return _this;\n  }\n\n  LinesSeriesModel.prototype.init = function (option) {\n    // The input data may be null/undefined.\n    option.data = option.data || []; // Not using preprocessor because mergeOption may not have series.type\n\n    compatEc2(option);\n\n    var result = this._processFlatCoordsArray(option.data);\n\n    this._flatCoords = result.flatCoords;\n    this._flatCoordsOffset = result.flatCoordsOffset;\n\n    if (result.flatCoords) {\n      option.data = new Float32Array(result.count);\n    }\n\n    _super.prototype.init.apply(this, arguments);\n  };\n\n  LinesSeriesModel.prototype.mergeOption = function (option) {\n    compatEc2(option);\n\n    if (option.data) {\n      // Only update when have option data to merge.\n      var result = this._processFlatCoordsArray(option.data);\n\n      this._flatCoords = result.flatCoords;\n      this._flatCoordsOffset = result.flatCoordsOffset;\n\n      if (result.flatCoords) {\n        option.data = new Float32Array(result.count);\n      }\n    }\n\n    _super.prototype.mergeOption.apply(this, arguments);\n  };\n\n  LinesSeriesModel.prototype.appendData = function (params) {\n    var result = this._processFlatCoordsArray(params.data);\n\n    if (result.flatCoords) {\n      if (!this._flatCoords) {\n        this._flatCoords = result.flatCoords;\n        this._flatCoordsOffset = result.flatCoordsOffset;\n      } else {\n        this._flatCoords = concatArray(this._flatCoords, result.flatCoords);\n        this._flatCoordsOffset = concatArray(this._flatCoordsOffset, result.flatCoordsOffset);\n      }\n\n      params.data = new Float32Array(result.count);\n    }\n\n    this.getRawData().appendData(params.data);\n  };\n\n  LinesSeriesModel.prototype._getCoordsFromItemModel = function (idx) {\n    var itemModel = this.getData().getItemModel(idx);\n    var coords = itemModel.option instanceof Array ? itemModel.option : itemModel.getShallow('coords');\n\n    if (process.env.NODE_ENV !== 'production') {\n      if (!(coords instanceof Array && coords.length > 0 && coords[0] instanceof Array)) {\n        throw new Error('Invalid coords ' + JSON.stringify(coords) + '. Lines must have 2d coords array in data item.');\n      }\n    }\n\n    return coords;\n  };\n\n  LinesSeriesModel.prototype.getLineCoordsCount = function (idx) {\n    if (this._flatCoordsOffset) {\n      return this._flatCoordsOffset[idx * 2 + 1];\n    } else {\n      return this._getCoordsFromItemModel(idx).length;\n    }\n  };\n\n  LinesSeriesModel.prototype.getLineCoords = function (idx, out) {\n    if (this._flatCoordsOffset) {\n      var offset = this._flatCoordsOffset[idx * 2];\n      var len = this._flatCoordsOffset[idx * 2 + 1];\n\n      for (var i = 0; i < len; i++) {\n        out[i] = out[i] || [];\n        out[i][0] = this._flatCoords[offset + i * 2];\n        out[i][1] = this._flatCoords[offset + i * 2 + 1];\n      }\n\n      return len;\n    } else {\n      var coords = this._getCoordsFromItemModel(idx);\n\n      for (var i = 0; i < coords.length; i++) {\n        out[i] = out[i] || [];\n        out[i][0] = coords[i][0];\n        out[i][1] = coords[i][1];\n      }\n\n      return coords.length;\n    }\n  };\n\n  LinesSeriesModel.prototype._processFlatCoordsArray = function (data) {\n    var startOffset = 0;\n\n    if (this._flatCoords) {\n      startOffset = this._flatCoords.length;\n    } // Stored as a typed array. In format\n    // Points Count(2) | x | y | x | y | Points Count(3) | x |  y | x | y | x | y |\n\n\n    if (isNumber(data[0])) {\n      var len = data.length; // Store offset and len of each segment\n\n      var coordsOffsetAndLenStorage = new Uint32Arr(len);\n      var coordsStorage = new Float64Arr(len);\n      var coordsCursor = 0;\n      var offsetCursor = 0;\n      var dataCount = 0;\n\n      for (var i = 0; i < len;) {\n        dataCount++;\n        var count = data[i++]; // Offset\n\n        coordsOffsetAndLenStorage[offsetCursor++] = coordsCursor + startOffset; // Len\n\n        coordsOffsetAndLenStorage[offsetCursor++] = count;\n\n        for (var k = 0; k < count; k++) {\n          var x = data[i++];\n          var y = data[i++];\n          coordsStorage[coordsCursor++] = x;\n          coordsStorage[coordsCursor++] = y;\n\n          if (i > len) {\n            if (process.env.NODE_ENV !== 'production') {\n              throw new Error('Invalid data format.');\n            }\n          }\n        }\n      }\n\n      return {\n        flatCoordsOffset: new Uint32Array(coordsOffsetAndLenStorage.buffer, 0, offsetCursor),\n        flatCoords: coordsStorage,\n        count: dataCount\n      };\n    }\n\n    return {\n      flatCoordsOffset: null,\n      flatCoords: null,\n      count: data.length\n    };\n  };\n\n  LinesSeriesModel.prototype.getInitialData = function (option, ecModel) {\n    if (process.env.NODE_ENV !== 'production') {\n      var CoordSys = CoordinateSystem.get(option.coordinateSystem);\n\n      if (!CoordSys) {\n        throw new Error('Unknown coordinate system ' + option.coordinateSystem);\n      }\n    }\n\n    var lineData = new SeriesData(['value'], this);\n    lineData.hasItemOption = false;\n    lineData.initData(option.data, [], function (dataItem, dimName, dataIndex, dimIndex) {\n      // dataItem is simply coords\n      if (dataItem instanceof Array) {\n        return NaN;\n      } else {\n        lineData.hasItemOption = true;\n        var value = dataItem.value;\n\n        if (value != null) {\n          return value instanceof Array ? value[dimIndex] : value;\n        }\n      }\n    });\n    return lineData;\n  };\n\n  LinesSeriesModel.prototype.formatTooltip = function (dataIndex, multipleSeries, dataType) {\n    var data = this.getData();\n    var itemModel = data.getItemModel(dataIndex);\n    var name = itemModel.get('name');\n\n    if (name) {\n      return name;\n    }\n\n    var fromName = itemModel.get('fromName');\n    var toName = itemModel.get('toName');\n    var nameArr = [];\n    fromName != null && nameArr.push(fromName);\n    toName != null && nameArr.push(toName);\n    return createTooltipMarkup('nameValue', {\n      name: nameArr.join(' > ')\n    });\n  };\n\n  LinesSeriesModel.prototype.preventIncremental = function () {\n    return !!this.get(['effect', 'show']);\n  };\n\n  LinesSeriesModel.prototype.getProgressive = function () {\n    var progressive = this.option.progressive;\n\n    if (progressive == null) {\n      return this.option.large ? 1e4 : this.get('progressive');\n    }\n\n    return progressive;\n  };\n\n  LinesSeriesModel.prototype.getProgressiveThreshold = function () {\n    var progressiveThreshold = this.option.progressiveThreshold;\n\n    if (progressiveThreshold == null) {\n      return this.option.large ? 2e4 : this.get('progressiveThreshold');\n    }\n\n    return progressiveThreshold;\n  };\n\n  LinesSeriesModel.prototype.getZLevelKey = function () {\n    var effectModel = this.getModel('effect');\n    var trailLength = effectModel.get('trailLength');\n    return this.getData().count() > this.getProgressiveThreshold() // Each progressive series has individual key.\n    ? this.id : effectModel.get('show') && trailLength > 0 ? trailLength + '' : '';\n  };\n\n  LinesSeriesModel.type = 'series.lines';\n  LinesSeriesModel.dependencies = ['grid', 'polar', 'geo', 'calendar'];\n  LinesSeriesModel.defaultOption = {\n    coordinateSystem: 'geo',\n    // zlevel: 0,\n    z: 2,\n    legendHoverLink: true,\n    // Cartesian coordinate system\n    xAxisIndex: 0,\n    yAxisIndex: 0,\n    symbol: ['none', 'none'],\n    symbolSize: [10, 10],\n    // Geo coordinate system\n    geoIndex: 0,\n    effect: {\n      show: false,\n      period: 4,\n      constantSpeed: 0,\n      symbol: 'circle',\n      symbolSize: 3,\n      loop: true,\n      trailLength: 0.2\n    },\n    large: false,\n    // Available when large is true\n    largeThreshold: 2000,\n    polyline: false,\n    clip: true,\n    label: {\n      show: false,\n      position: 'end' // distance: 5,\n      // formatter: 标签文本格式器，同Tooltip.formatter，不支持异步回调\n\n    },\n    lineStyle: {\n      opacity: 0.5\n    }\n  };\n  return LinesSeriesModel;\n}(SeriesModel);\n\nexport default LinesSeriesModel;"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,SAAS,QAAQ,OAAO;AACjC;;AAEA,OAAOC,WAAW,MAAM,uBAAuB;AAC/C,OAAOC,UAAU,MAAM,0BAA0B;AACjD,SAASC,WAAW,EAAEC,QAAQ,EAAEC,GAAG,EAAEC,QAAQ,QAAQ,0BAA0B;AAC/E,OAAOC,gBAAgB,MAAM,gCAAgC;AAC7D,SAASC,mBAAmB,QAAQ,0CAA0C;AAC9E,IAAIC,SAAS,GAAG,OAAOC,WAAW,KAAK,WAAW,GAAGC,KAAK,GAAGD,WAAW;AACxE,IAAIE,UAAU,GAAG,OAAOC,YAAY,KAAK,WAAW,GAAGF,KAAK,GAAGE,YAAY;AAE3E,SAASC,SAASA,CAACC,SAAS,EAAE;EAC5B,IAAIC,IAAI,GAAGD,SAAS,CAACC,IAAI;EAEzB,IAAIA,IAAI,IAAIA,IAAI,CAAC,CAAC,CAAC,IAAIA,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAIA,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAACC,KAAK,EAAE;IACrD,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;MACzCC,OAAO,CAACC,IAAI,CAAC,8CAA8C,GAAG,2BAA2B,CAAC;IAC5F;IAEAP,SAAS,CAACC,IAAI,GAAGX,GAAG,CAACW,IAAI,EAAE,UAAUO,OAAO,EAAE;MAC5C,IAAIC,MAAM,GAAG,CAACD,OAAO,CAAC,CAAC,CAAC,CAACN,KAAK,EAAEM,OAAO,CAAC,CAAC,CAAC,CAACN,KAAK,CAAC;MACjD,IAAIQ,MAAM,GAAG;QACXD,MAAM,EAAEA;MACV,CAAC;MAED,IAAID,OAAO,CAAC,CAAC,CAAC,CAACG,IAAI,EAAE;QACnBD,MAAM,CAACE,QAAQ,GAAGJ,OAAO,CAAC,CAAC,CAAC,CAACG,IAAI;MACnC;MAEA,IAAIH,OAAO,CAAC,CAAC,CAAC,CAACG,IAAI,EAAE;QACnBD,MAAM,CAACG,MAAM,GAAGL,OAAO,CAAC,CAAC,CAAC,CAACG,IAAI;MACjC;MAEA,OAAOtB,QAAQ,CAAC,CAACqB,MAAM,EAAEF,OAAO,CAAC,CAAC,CAAC,EAAEA,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;IACnD,CAAC,CAAC;EACJ;AACF;AAEA,IAAIM,gBAAgB,GACpB;AACA,UAAUC,MAAM,EAAE;EAChB9B,SAAS,CAAC6B,gBAAgB,EAAEC,MAAM,CAAC;EAEnC,SAASD,gBAAgBA,CAAA,EAAG;IAC1B,IAAIE,KAAK,GAAGD,MAAM,KAAK,IAAI,IAAIA,MAAM,CAACE,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC,IAAI,IAAI;IAEpEF,KAAK,CAACG,IAAI,GAAGL,gBAAgB,CAACK,IAAI;IAClCH,KAAK,CAACI,qBAAqB,GAAG,WAAW;IACzCJ,KAAK,CAACK,cAAc,GAAG,QAAQ;IAC/B,OAAOL,KAAK;EACd;EAEAF,gBAAgB,CAACQ,SAAS,CAACC,IAAI,GAAG,UAAUC,MAAM,EAAE;IAClD;IACAA,MAAM,CAACvB,IAAI,GAAGuB,MAAM,CAACvB,IAAI,IAAI,EAAE,CAAC,CAAC;;IAEjCF,SAAS,CAACyB,MAAM,CAAC;IAEjB,IAAIC,MAAM,GAAG,IAAI,CAACC,uBAAuB,CAACF,MAAM,CAACvB,IAAI,CAAC;IAEtD,IAAI,CAAC0B,WAAW,GAAGF,MAAM,CAACG,UAAU;IACpC,IAAI,CAACC,iBAAiB,GAAGJ,MAAM,CAACK,gBAAgB;IAEhD,IAAIL,MAAM,CAACG,UAAU,EAAE;MACrBJ,MAAM,CAACvB,IAAI,GAAG,IAAI8B,YAAY,CAACN,MAAM,CAACO,KAAK,CAAC;IAC9C;IAEAjB,MAAM,CAACO,SAAS,CAACC,IAAI,CAACN,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC;EAC9C,CAAC;EAEDJ,gBAAgB,CAACQ,SAAS,CAACW,WAAW,GAAG,UAAUT,MAAM,EAAE;IACzDzB,SAAS,CAACyB,MAAM,CAAC;IAEjB,IAAIA,MAAM,CAACvB,IAAI,EAAE;MACf;MACA,IAAIwB,MAAM,GAAG,IAAI,CAACC,uBAAuB,CAACF,MAAM,CAACvB,IAAI,CAAC;MAEtD,IAAI,CAAC0B,WAAW,GAAGF,MAAM,CAACG,UAAU;MACpC,IAAI,CAACC,iBAAiB,GAAGJ,MAAM,CAACK,gBAAgB;MAEhD,IAAIL,MAAM,CAACG,UAAU,EAAE;QACrBJ,MAAM,CAACvB,IAAI,GAAG,IAAI8B,YAAY,CAACN,MAAM,CAACO,KAAK,CAAC;MAC9C;IACF;IAEAjB,MAAM,CAACO,SAAS,CAACW,WAAW,CAAChB,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC;EACrD,CAAC;EAEDJ,gBAAgB,CAACQ,SAAS,CAACY,UAAU,GAAG,UAAUC,MAAM,EAAE;IACxD,IAAIV,MAAM,GAAG,IAAI,CAACC,uBAAuB,CAACS,MAAM,CAAClC,IAAI,CAAC;IAEtD,IAAIwB,MAAM,CAACG,UAAU,EAAE;MACrB,IAAI,CAAC,IAAI,CAACD,WAAW,EAAE;QACrB,IAAI,CAACA,WAAW,GAAGF,MAAM,CAACG,UAAU;QACpC,IAAI,CAACC,iBAAiB,GAAGJ,MAAM,CAACK,gBAAgB;MAClD,CAAC,MAAM;QACL,IAAI,CAACH,WAAW,GAAGvC,WAAW,CAAC,IAAI,CAACuC,WAAW,EAAEF,MAAM,CAACG,UAAU,CAAC;QACnE,IAAI,CAACC,iBAAiB,GAAGzC,WAAW,CAAC,IAAI,CAACyC,iBAAiB,EAAEJ,MAAM,CAACK,gBAAgB,CAAC;MACvF;MAEAK,MAAM,CAAClC,IAAI,GAAG,IAAI8B,YAAY,CAACN,MAAM,CAACO,KAAK,CAAC;IAC9C;IAEA,IAAI,CAACI,UAAU,CAAC,CAAC,CAACF,UAAU,CAACC,MAAM,CAAClC,IAAI,CAAC;EAC3C,CAAC;EAEDa,gBAAgB,CAACQ,SAAS,CAACe,uBAAuB,GAAG,UAAUC,GAAG,EAAE;IAClE,IAAIC,SAAS,GAAG,IAAI,CAACC,OAAO,CAAC,CAAC,CAACC,YAAY,CAACH,GAAG,CAAC;IAChD,IAAI7B,MAAM,GAAG8B,SAAS,CAACf,MAAM,YAAY5B,KAAK,GAAG2C,SAAS,CAACf,MAAM,GAAGe,SAAS,CAACG,UAAU,CAAC,QAAQ,CAAC;IAElG,IAAIvC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;MACzC,IAAI,EAAEI,MAAM,YAAYb,KAAK,IAAIa,MAAM,CAACkC,MAAM,GAAG,CAAC,IAAIlC,MAAM,CAAC,CAAC,CAAC,YAAYb,KAAK,CAAC,EAAE;QACjF,MAAM,IAAIgD,KAAK,CAAC,iBAAiB,GAAGC,IAAI,CAACC,SAAS,CAACrC,MAAM,CAAC,GAAG,iDAAiD,CAAC;MACjH;IACF;IAEA,OAAOA,MAAM;EACf,CAAC;EAEDK,gBAAgB,CAACQ,SAAS,CAACyB,kBAAkB,GAAG,UAAUT,GAAG,EAAE;IAC7D,IAAI,IAAI,CAACT,iBAAiB,EAAE;MAC1B,OAAO,IAAI,CAACA,iBAAiB,CAACS,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC;IAC5C,CAAC,MAAM;MACL,OAAO,IAAI,CAACD,uBAAuB,CAACC,GAAG,CAAC,CAACK,MAAM;IACjD;EACF,CAAC;EAED7B,gBAAgB,CAACQ,SAAS,CAAC0B,aAAa,GAAG,UAAUV,GAAG,EAAEW,GAAG,EAAE;IAC7D,IAAI,IAAI,CAACpB,iBAAiB,EAAE;MAC1B,IAAIqB,MAAM,GAAG,IAAI,CAACrB,iBAAiB,CAACS,GAAG,GAAG,CAAC,CAAC;MAC5C,IAAIa,GAAG,GAAG,IAAI,CAACtB,iBAAiB,CAACS,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC;MAE7C,KAAK,IAAIc,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,GAAG,EAAEC,CAAC,EAAE,EAAE;QAC5BH,GAAG,CAACG,CAAC,CAAC,GAAGH,GAAG,CAACG,CAAC,CAAC,IAAI,EAAE;QACrBH,GAAG,CAACG,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,CAACzB,WAAW,CAACuB,MAAM,GAAGE,CAAC,GAAG,CAAC,CAAC;QAC5CH,GAAG,CAACG,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,CAACzB,WAAW,CAACuB,MAAM,GAAGE,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;MAClD;MAEA,OAAOD,GAAG;IACZ,CAAC,MAAM;MACL,IAAI1C,MAAM,GAAG,IAAI,CAAC4B,uBAAuB,CAACC,GAAG,CAAC;MAE9C,KAAK,IAAIc,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG3C,MAAM,CAACkC,MAAM,EAAES,CAAC,EAAE,EAAE;QACtCH,GAAG,CAACG,CAAC,CAAC,GAAGH,GAAG,CAACG,CAAC,CAAC,IAAI,EAAE;QACrBH,GAAG,CAACG,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG3C,MAAM,CAAC2C,CAAC,CAAC,CAAC,CAAC,CAAC;QACxBH,GAAG,CAACG,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG3C,MAAM,CAAC2C,CAAC,CAAC,CAAC,CAAC,CAAC;MAC1B;MAEA,OAAO3C,MAAM,CAACkC,MAAM;IACtB;EACF,CAAC;EAED7B,gBAAgB,CAACQ,SAAS,CAACI,uBAAuB,GAAG,UAAUzB,IAAI,EAAE;IACnE,IAAIoD,WAAW,GAAG,CAAC;IAEnB,IAAI,IAAI,CAAC1B,WAAW,EAAE;MACpB0B,WAAW,GAAG,IAAI,CAAC1B,WAAW,CAACgB,MAAM;IACvC,CAAC,CAAC;IACF;;IAGA,IAAIpD,QAAQ,CAACU,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE;MACrB,IAAIkD,GAAG,GAAGlD,IAAI,CAAC0C,MAAM,CAAC,CAAC;;MAEvB,IAAIW,yBAAyB,GAAG,IAAI5D,SAAS,CAACyD,GAAG,CAAC;MAClD,IAAII,aAAa,GAAG,IAAI1D,UAAU,CAACsD,GAAG,CAAC;MACvC,IAAIK,YAAY,GAAG,CAAC;MACpB,IAAIC,YAAY,GAAG,CAAC;MACpB,IAAIC,SAAS,GAAG,CAAC;MAEjB,KAAK,IAAIN,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,GAAG,GAAG;QACxBO,SAAS,EAAE;QACX,IAAI1B,KAAK,GAAG/B,IAAI,CAACmD,CAAC,EAAE,CAAC,CAAC,CAAC;;QAEvBE,yBAAyB,CAACG,YAAY,EAAE,CAAC,GAAGD,YAAY,GAAGH,WAAW,CAAC,CAAC;;QAExEC,yBAAyB,CAACG,YAAY,EAAE,CAAC,GAAGzB,KAAK;QAEjD,KAAK,IAAI2B,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG3B,KAAK,EAAE2B,CAAC,EAAE,EAAE;UAC9B,IAAIC,CAAC,GAAG3D,IAAI,CAACmD,CAAC,EAAE,CAAC;UACjB,IAAIS,CAAC,GAAG5D,IAAI,CAACmD,CAAC,EAAE,CAAC;UACjBG,aAAa,CAACC,YAAY,EAAE,CAAC,GAAGI,CAAC;UACjCL,aAAa,CAACC,YAAY,EAAE,CAAC,GAAGK,CAAC;UAEjC,IAAIT,CAAC,GAAGD,GAAG,EAAE;YACX,IAAIhD,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;cACzC,MAAM,IAAIuC,KAAK,CAAC,sBAAsB,CAAC;YACzC;UACF;QACF;MACF;MAEA,OAAO;QACLd,gBAAgB,EAAE,IAAInC,WAAW,CAAC2D,yBAAyB,CAACQ,MAAM,EAAE,CAAC,EAAEL,YAAY,CAAC;QACpF7B,UAAU,EAAE2B,aAAa;QACzBvB,KAAK,EAAE0B;MACT,CAAC;IACH;IAEA,OAAO;MACL5B,gBAAgB,EAAE,IAAI;MACtBF,UAAU,EAAE,IAAI;MAChBI,KAAK,EAAE/B,IAAI,CAAC0C;IACd,CAAC;EACH,CAAC;EAED7B,gBAAgB,CAACQ,SAAS,CAACyC,cAAc,GAAG,UAAUvC,MAAM,EAAEwC,OAAO,EAAE;IACrE,IAAI7D,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;MACzC,IAAI4D,QAAQ,GAAGzE,gBAAgB,CAAC0E,GAAG,CAAC1C,MAAM,CAAC2C,gBAAgB,CAAC;MAE5D,IAAI,CAACF,QAAQ,EAAE;QACb,MAAM,IAAIrB,KAAK,CAAC,4BAA4B,GAAGpB,MAAM,CAAC2C,gBAAgB,CAAC;MACzE;IACF;IAEA,IAAIC,QAAQ,GAAG,IAAIjF,UAAU,CAAC,CAAC,OAAO,CAAC,EAAE,IAAI,CAAC;IAC9CiF,QAAQ,CAACC,aAAa,GAAG,KAAK;IAC9BD,QAAQ,CAACE,QAAQ,CAAC9C,MAAM,CAACvB,IAAI,EAAE,EAAE,EAAE,UAAUsE,QAAQ,EAAEC,OAAO,EAAEC,SAAS,EAAEC,QAAQ,EAAE;MACnF;MACA,IAAIH,QAAQ,YAAY3E,KAAK,EAAE;QAC7B,OAAO+E,GAAG;MACZ,CAAC,MAAM;QACLP,QAAQ,CAACC,aAAa,GAAG,IAAI;QAC7B,IAAIO,KAAK,GAAGL,QAAQ,CAACK,KAAK;QAE1B,IAAIA,KAAK,IAAI,IAAI,EAAE;UACjB,OAAOA,KAAK,YAAYhF,KAAK,GAAGgF,KAAK,CAACF,QAAQ,CAAC,GAAGE,KAAK;QACzD;MACF;IACF,CAAC,CAAC;IACF,OAAOR,QAAQ;EACjB,CAAC;EAEDtD,gBAAgB,CAACQ,SAAS,CAACuD,aAAa,GAAG,UAAUJ,SAAS,EAAEK,cAAc,EAAEC,QAAQ,EAAE;IACxF,IAAI9E,IAAI,GAAG,IAAI,CAACuC,OAAO,CAAC,CAAC;IACzB,IAAID,SAAS,GAAGtC,IAAI,CAACwC,YAAY,CAACgC,SAAS,CAAC;IAC5C,IAAI9D,IAAI,GAAG4B,SAAS,CAAC2B,GAAG,CAAC,MAAM,CAAC;IAEhC,IAAIvD,IAAI,EAAE;MACR,OAAOA,IAAI;IACb;IAEA,IAAIC,QAAQ,GAAG2B,SAAS,CAAC2B,GAAG,CAAC,UAAU,CAAC;IACxC,IAAIrD,MAAM,GAAG0B,SAAS,CAAC2B,GAAG,CAAC,QAAQ,CAAC;IACpC,IAAIc,OAAO,GAAG,EAAE;IAChBpE,QAAQ,IAAI,IAAI,IAAIoE,OAAO,CAACC,IAAI,CAACrE,QAAQ,CAAC;IAC1CC,MAAM,IAAI,IAAI,IAAImE,OAAO,CAACC,IAAI,CAACpE,MAAM,CAAC;IACtC,OAAOpB,mBAAmB,CAAC,WAAW,EAAE;MACtCkB,IAAI,EAAEqE,OAAO,CAACE,IAAI,CAAC,KAAK;IAC1B,CAAC,CAAC;EACJ,CAAC;EAEDpE,gBAAgB,CAACQ,SAAS,CAAC6D,kBAAkB,GAAG,YAAY;IAC1D,OAAO,CAAC,CAAC,IAAI,CAACjB,GAAG,CAAC,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;EACvC,CAAC;EAEDpD,gBAAgB,CAACQ,SAAS,CAAC8D,cAAc,GAAG,YAAY;IACtD,IAAIC,WAAW,GAAG,IAAI,CAAC7D,MAAM,CAAC6D,WAAW;IAEzC,IAAIA,WAAW,IAAI,IAAI,EAAE;MACvB,OAAO,IAAI,CAAC7D,MAAM,CAAC8D,KAAK,GAAG,GAAG,GAAG,IAAI,CAACpB,GAAG,CAAC,aAAa,CAAC;IAC1D;IAEA,OAAOmB,WAAW;EACpB,CAAC;EAEDvE,gBAAgB,CAACQ,SAAS,CAACiE,uBAAuB,GAAG,YAAY;IAC/D,IAAIC,oBAAoB,GAAG,IAAI,CAAChE,MAAM,CAACgE,oBAAoB;IAE3D,IAAIA,oBAAoB,IAAI,IAAI,EAAE;MAChC,OAAO,IAAI,CAAChE,MAAM,CAAC8D,KAAK,GAAG,GAAG,GAAG,IAAI,CAACpB,GAAG,CAAC,sBAAsB,CAAC;IACnE;IAEA,OAAOsB,oBAAoB;EAC7B,CAAC;EAED1E,gBAAgB,CAACQ,SAAS,CAACmE,YAAY,GAAG,YAAY;IACpD,IAAIC,WAAW,GAAG,IAAI,CAACC,QAAQ,CAAC,QAAQ,CAAC;IACzC,IAAIC,WAAW,GAAGF,WAAW,CAACxB,GAAG,CAAC,aAAa,CAAC;IAChD,OAAO,IAAI,CAAC1B,OAAO,CAAC,CAAC,CAACR,KAAK,CAAC,CAAC,GAAG,IAAI,CAACuD,uBAAuB,CAAC,CAAC,CAAC;IAAA,EAC7D,IAAI,CAACM,EAAE,GAAGH,WAAW,CAACxB,GAAG,CAAC,MAAM,CAAC,IAAI0B,WAAW,GAAG,CAAC,GAAGA,WAAW,GAAG,EAAE,GAAG,EAAE;EAChF,CAAC;EAED9E,gBAAgB,CAACK,IAAI,GAAG,cAAc;EACtCL,gBAAgB,CAACgF,YAAY,GAAG,CAAC,MAAM,EAAE,OAAO,EAAE,KAAK,EAAE,UAAU,CAAC;EACpEhF,gBAAgB,CAACiF,aAAa,GAAG;IAC/B5B,gBAAgB,EAAE,KAAK;IACvB;IACA6B,CAAC,EAAE,CAAC;IACJC,eAAe,EAAE,IAAI;IACrB;IACAC,UAAU,EAAE,CAAC;IACbC,UAAU,EAAE,CAAC;IACbC,MAAM,EAAE,CAAC,MAAM,EAAE,MAAM,CAAC;IACxBC,UAAU,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC;IACpB;IACAC,QAAQ,EAAE,CAAC;IACXC,MAAM,EAAE;MACNC,IAAI,EAAE,KAAK;MACXC,MAAM,EAAE,CAAC;MACTC,aAAa,EAAE,CAAC;MAChBN,MAAM,EAAE,QAAQ;MAChBC,UAAU,EAAE,CAAC;MACbM,IAAI,EAAE,IAAI;MACVf,WAAW,EAAE;IACf,CAAC;IACDN,KAAK,EAAE,KAAK;IACZ;IACAsB,cAAc,EAAE,IAAI;IACpBC,QAAQ,EAAE,KAAK;IACfC,IAAI,EAAE,IAAI;IACVC,KAAK,EAAE;MACLP,IAAI,EAAE,KAAK;MACXQ,QAAQ,EAAE,KAAK,CAAC;MAChB;IAEF,CAAC;;IACDC,SAAS,EAAE;MACTC,OAAO,EAAE;IACX;EACF,CAAC;EACD,OAAOpG,gBAAgB;AACzB,CAAC,CAAC5B,WAAW,CAAC;AAEd,eAAe4B,gBAAgB"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}