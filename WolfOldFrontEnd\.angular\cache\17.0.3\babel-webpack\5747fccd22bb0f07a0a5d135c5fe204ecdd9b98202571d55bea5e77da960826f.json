{"ast": null, "code": "/** Used to escape characters for inclusion in compiled string literals. */\nvar stringEscapes = {\n  '\\\\': '\\\\',\n  \"'\": \"'\",\n  '\\n': 'n',\n  '\\r': 'r',\n  '\\u2028': 'u2028',\n  '\\u2029': 'u2029'\n};\n\n/**\n * Used by `_.template` to escape characters for inclusion in compiled string literals.\n *\n * @private\n * @param {string} chr The matched character to escape.\n * @returns {string} Returns the escaped character.\n */\nfunction escapeStringChar(chr) {\n  return '\\\\' + stringEscapes[chr];\n}\nexport default escapeStringChar;", "map": {"version": 3, "names": ["stringEscapes", "escapeStringChar", "chr"], "sources": ["C:/WolfSystemV2/WolfOldFrontEnd/node_modules/lodash-es/_escapeStringChar.js"], "sourcesContent": ["/** Used to escape characters for inclusion in compiled string literals. */\nvar stringEscapes = {\n  '\\\\': '\\\\',\n  \"'\": \"'\",\n  '\\n': 'n',\n  '\\r': 'r',\n  '\\u2028': 'u2028',\n  '\\u2029': 'u2029'\n};\n\n/**\n * Used by `_.template` to escape characters for inclusion in compiled string literals.\n *\n * @private\n * @param {string} chr The matched character to escape.\n * @returns {string} Returns the escaped character.\n */\nfunction escapeStringChar(chr) {\n  return '\\\\' + stringEscapes[chr];\n}\n\nexport default escapeStringChar;\n"], "mappings": "AAAA;AACA,IAAIA,aAAa,GAAG;EAClB,IAAI,EAAE,IAAI;EACV,GAAG,EAAE,GAAG;EACR,IAAI,EAAE,GAAG;EACT,IAAI,EAAE,GAAG;EACT,QAAQ,EAAE,OAAO;EACjB,QAAQ,EAAE;AACZ,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,gBAAgBA,CAACC,GAAG,EAAE;EAC7B,OAAO,IAAI,GAAGF,aAAa,CAACE,GAAG,CAAC;AAClC;AAEA,eAAeD,gBAAgB"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}