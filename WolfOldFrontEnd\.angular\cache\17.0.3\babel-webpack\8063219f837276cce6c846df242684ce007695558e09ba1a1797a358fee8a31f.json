{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { keys, filter, each, isArray, indexOf } from 'zrender/lib/core/util.js';\nimport { ELEMENT_ANIMATABLE_PROPS } from './customGraphicTransition.js';\nimport { getAnimationConfig } from './basicTransition.js';\nimport { warn } from '../util/log.js';\nimport { makeInner } from '../util/model.js';\nvar getStateToRestore = makeInner();\nvar KEYFRAME_EXCLUDE_KEYS = ['percent', 'easing', 'shape', 'style', 'extra'];\n/**\r\n * Stop previous keyframe animation and restore the attributes.\r\n * Avoid new keyframe animation starts with wrong internal state when the percent: 0 is not set.\r\n */\n\nexport function stopPreviousKeyframeAnimationAndRestore(el) {\n  // Stop previous keyframe animation.\n  el.stopAnimation('keyframe'); // Restore\n\n  el.attr(getStateToRestore(el));\n}\nexport function applyKeyframeAnimation(el, animationOpts, animatableModel) {\n  if (!animatableModel.isAnimationEnabled() || !animationOpts) {\n    return;\n  }\n  if (isArray(animationOpts)) {\n    each(animationOpts, function (singleAnimationOpts) {\n      applyKeyframeAnimation(el, singleAnimationOpts, animatableModel);\n    });\n    return;\n  }\n  var keyframes = animationOpts.keyframes;\n  var duration = animationOpts.duration;\n  if (animatableModel && duration == null) {\n    // Default to use duration of config.\n    // NOTE: animation config from payload will be ignored because they are mainly for transitions.\n    var config = getAnimationConfig('enter', animatableModel, 0);\n    duration = config && config.duration;\n  }\n  if (!keyframes || !duration) {\n    return;\n  }\n  var stateToRestore = getStateToRestore(el);\n  each(ELEMENT_ANIMATABLE_PROPS, function (targetPropName) {\n    if (targetPropName && !el[targetPropName]) {\n      return;\n    }\n    var animator;\n    var endFrameIsSet = false; // Sort keyframes by percent.\n\n    keyframes.sort(function (a, b) {\n      return a.percent - b.percent;\n    });\n    each(keyframes, function (kf) {\n      // Stop current animation.\n      var animators = el.animators;\n      var kfValues = targetPropName ? kf[targetPropName] : kf;\n      if (process.env.NODE_ENV !== 'production') {\n        if (kf.percent >= 1) {\n          endFrameIsSet = true;\n        }\n      }\n      if (!kfValues) {\n        return;\n      }\n      var propKeys = keys(kfValues);\n      if (!targetPropName) {\n        // PENDING performance?\n        propKeys = filter(propKeys, function (key) {\n          return indexOf(KEYFRAME_EXCLUDE_KEYS, key) < 0;\n        });\n      }\n      if (!propKeys.length) {\n        return;\n      }\n      if (!animator) {\n        animator = el.animate(targetPropName, animationOpts.loop, true);\n        animator.scope = 'keyframe';\n      }\n      for (var i = 0; i < animators.length; i++) {\n        // Stop all other animation that is not keyframe.\n        if (animators[i] !== animator && animators[i].targetName === animator.targetName) {\n          animators[i].stopTracks(propKeys);\n        }\n      }\n      targetPropName && (stateToRestore[targetPropName] = stateToRestore[targetPropName] || {});\n      var savedTarget = targetPropName ? stateToRestore[targetPropName] : stateToRestore;\n      each(propKeys, function (key) {\n        // Save original value.\n        savedTarget[key] = ((targetPropName ? el[targetPropName] : el) || {})[key];\n      });\n      animator.whenWithKeys(duration * kf.percent, kfValues, propKeys, kf.easing);\n    });\n    if (!animator) {\n      return;\n    }\n    if (process.env.NODE_ENV !== 'production') {\n      if (!endFrameIsSet) {\n        warn('End frame with percent: 1 is missing in the keyframeAnimation.', true);\n      }\n    }\n    animator.delay(animationOpts.delay || 0).duration(duration).start(animationOpts.easing);\n  });\n}", "map": {"version": 3, "names": ["keys", "filter", "each", "isArray", "indexOf", "ELEMENT_ANIMATABLE_PROPS", "getAnimationConfig", "warn", "makeInner", "getStateToRestore", "KEYFRAME_EXCLUDE_KEYS", "stopPreviousKeyframeAnimationAndRestore", "el", "stopAnimation", "attr", "applyKeyframeAnimation", "animationOpts", "animatableModel", "isAnimationEnabled", "singleAnimationOpts", "keyframes", "duration", "config", "stateToRestore", "targetPropName", "animator", "endFrameIsSet", "sort", "a", "b", "percent", "kf", "animators", "kfValues", "process", "env", "NODE_ENV", "propKeys", "key", "length", "animate", "loop", "scope", "i", "targetName", "stopTracks", "<PERSON><PERSON><PERSON><PERSON>", "when<PERSON>ith<PERSON>eys", "easing", "delay", "start"], "sources": ["C:/WolfSystemV2/WolfOldFrontEnd/node_modules/echarts/lib/animation/customGraphicKeyframeAnimation.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { keys, filter, each, isArray, indexOf } from 'zrender/lib/core/util.js';\nimport { ELEMENT_ANIMATABLE_PROPS } from './customGraphicTransition.js';\nimport { getAnimationConfig } from './basicTransition.js';\nimport { warn } from '../util/log.js';\nimport { makeInner } from '../util/model.js';\nvar getStateToRestore = makeInner();\nvar KEYFRAME_EXCLUDE_KEYS = ['percent', 'easing', 'shape', 'style', 'extra'];\n/**\r\n * Stop previous keyframe animation and restore the attributes.\r\n * Avoid new keyframe animation starts with wrong internal state when the percent: 0 is not set.\r\n */\n\nexport function stopPreviousKeyframeAnimationAndRestore(el) {\n  // Stop previous keyframe animation.\n  el.stopAnimation('keyframe'); // Restore\n\n  el.attr(getStateToRestore(el));\n}\nexport function applyKeyframeAnimation(el, animationOpts, animatableModel) {\n  if (!animatableModel.isAnimationEnabled() || !animationOpts) {\n    return;\n  }\n\n  if (isArray(animationOpts)) {\n    each(animationOpts, function (singleAnimationOpts) {\n      applyKeyframeAnimation(el, singleAnimationOpts, animatableModel);\n    });\n    return;\n  }\n\n  var keyframes = animationOpts.keyframes;\n  var duration = animationOpts.duration;\n\n  if (animatableModel && duration == null) {\n    // Default to use duration of config.\n    // NOTE: animation config from payload will be ignored because they are mainly for transitions.\n    var config = getAnimationConfig('enter', animatableModel, 0);\n    duration = config && config.duration;\n  }\n\n  if (!keyframes || !duration) {\n    return;\n  }\n\n  var stateToRestore = getStateToRestore(el);\n  each(ELEMENT_ANIMATABLE_PROPS, function (targetPropName) {\n    if (targetPropName && !el[targetPropName]) {\n      return;\n    }\n\n    var animator;\n    var endFrameIsSet = false; // Sort keyframes by percent.\n\n    keyframes.sort(function (a, b) {\n      return a.percent - b.percent;\n    });\n    each(keyframes, function (kf) {\n      // Stop current animation.\n      var animators = el.animators;\n      var kfValues = targetPropName ? kf[targetPropName] : kf;\n\n      if (process.env.NODE_ENV !== 'production') {\n        if (kf.percent >= 1) {\n          endFrameIsSet = true;\n        }\n      }\n\n      if (!kfValues) {\n        return;\n      }\n\n      var propKeys = keys(kfValues);\n\n      if (!targetPropName) {\n        // PENDING performance?\n        propKeys = filter(propKeys, function (key) {\n          return indexOf(KEYFRAME_EXCLUDE_KEYS, key) < 0;\n        });\n      }\n\n      if (!propKeys.length) {\n        return;\n      }\n\n      if (!animator) {\n        animator = el.animate(targetPropName, animationOpts.loop, true);\n        animator.scope = 'keyframe';\n      }\n\n      for (var i = 0; i < animators.length; i++) {\n        // Stop all other animation that is not keyframe.\n        if (animators[i] !== animator && animators[i].targetName === animator.targetName) {\n          animators[i].stopTracks(propKeys);\n        }\n      }\n\n      targetPropName && (stateToRestore[targetPropName] = stateToRestore[targetPropName] || {});\n      var savedTarget = targetPropName ? stateToRestore[targetPropName] : stateToRestore;\n      each(propKeys, function (key) {\n        // Save original value.\n        savedTarget[key] = ((targetPropName ? el[targetPropName] : el) || {})[key];\n      });\n      animator.whenWithKeys(duration * kf.percent, kfValues, propKeys, kf.easing);\n    });\n\n    if (!animator) {\n      return;\n    }\n\n    if (process.env.NODE_ENV !== 'production') {\n      if (!endFrameIsSet) {\n        warn('End frame with percent: 1 is missing in the keyframeAnimation.', true);\n      }\n    }\n\n    animator.delay(animationOpts.delay || 0).duration(duration).start(animationOpts.easing);\n  });\n}"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,IAAI,EAAEC,MAAM,EAAEC,IAAI,EAAEC,OAAO,EAAEC,OAAO,QAAQ,0BAA0B;AAC/E,SAASC,wBAAwB,QAAQ,8BAA8B;AACvE,SAASC,kBAAkB,QAAQ,sBAAsB;AACzD,SAASC,IAAI,QAAQ,gBAAgB;AACrC,SAASC,SAAS,QAAQ,kBAAkB;AAC5C,IAAIC,iBAAiB,GAAGD,SAAS,CAAC,CAAC;AACnC,IAAIE,qBAAqB,GAAG,CAAC,SAAS,EAAE,QAAQ,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,CAAC;AAC5E;AACA;AACA;AACA;;AAEA,OAAO,SAASC,uCAAuCA,CAACC,EAAE,EAAE;EAC1D;EACAA,EAAE,CAACC,aAAa,CAAC,UAAU,CAAC,CAAC,CAAC;;EAE9BD,EAAE,CAACE,IAAI,CAACL,iBAAiB,CAACG,EAAE,CAAC,CAAC;AAChC;AACA,OAAO,SAASG,sBAAsBA,CAACH,EAAE,EAAEI,aAAa,EAAEC,eAAe,EAAE;EACzE,IAAI,CAACA,eAAe,CAACC,kBAAkB,CAAC,CAAC,IAAI,CAACF,aAAa,EAAE;IAC3D;EACF;EAEA,IAAIb,OAAO,CAACa,aAAa,CAAC,EAAE;IAC1Bd,IAAI,CAACc,aAAa,EAAE,UAAUG,mBAAmB,EAAE;MACjDJ,sBAAsB,CAACH,EAAE,EAAEO,mBAAmB,EAAEF,eAAe,CAAC;IAClE,CAAC,CAAC;IACF;EACF;EAEA,IAAIG,SAAS,GAAGJ,aAAa,CAACI,SAAS;EACvC,IAAIC,QAAQ,GAAGL,aAAa,CAACK,QAAQ;EAErC,IAAIJ,eAAe,IAAII,QAAQ,IAAI,IAAI,EAAE;IACvC;IACA;IACA,IAAIC,MAAM,GAAGhB,kBAAkB,CAAC,OAAO,EAAEW,eAAe,EAAE,CAAC,CAAC;IAC5DI,QAAQ,GAAGC,MAAM,IAAIA,MAAM,CAACD,QAAQ;EACtC;EAEA,IAAI,CAACD,SAAS,IAAI,CAACC,QAAQ,EAAE;IAC3B;EACF;EAEA,IAAIE,cAAc,GAAGd,iBAAiB,CAACG,EAAE,CAAC;EAC1CV,IAAI,CAACG,wBAAwB,EAAE,UAAUmB,cAAc,EAAE;IACvD,IAAIA,cAAc,IAAI,CAACZ,EAAE,CAACY,cAAc,CAAC,EAAE;MACzC;IACF;IAEA,IAAIC,QAAQ;IACZ,IAAIC,aAAa,GAAG,KAAK,CAAC,CAAC;;IAE3BN,SAAS,CAACO,IAAI,CAAC,UAAUC,CAAC,EAAEC,CAAC,EAAE;MAC7B,OAAOD,CAAC,CAACE,OAAO,GAAGD,CAAC,CAACC,OAAO;IAC9B,CAAC,CAAC;IACF5B,IAAI,CAACkB,SAAS,EAAE,UAAUW,EAAE,EAAE;MAC5B;MACA,IAAIC,SAAS,GAAGpB,EAAE,CAACoB,SAAS;MAC5B,IAAIC,QAAQ,GAAGT,cAAc,GAAGO,EAAE,CAACP,cAAc,CAAC,GAAGO,EAAE;MAEvD,IAAIG,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;QACzC,IAAIL,EAAE,CAACD,OAAO,IAAI,CAAC,EAAE;UACnBJ,aAAa,GAAG,IAAI;QACtB;MACF;MAEA,IAAI,CAACO,QAAQ,EAAE;QACb;MACF;MAEA,IAAII,QAAQ,GAAGrC,IAAI,CAACiC,QAAQ,CAAC;MAE7B,IAAI,CAACT,cAAc,EAAE;QACnB;QACAa,QAAQ,GAAGpC,MAAM,CAACoC,QAAQ,EAAE,UAAUC,GAAG,EAAE;UACzC,OAAOlC,OAAO,CAACM,qBAAqB,EAAE4B,GAAG,CAAC,GAAG,CAAC;QAChD,CAAC,CAAC;MACJ;MAEA,IAAI,CAACD,QAAQ,CAACE,MAAM,EAAE;QACpB;MACF;MAEA,IAAI,CAACd,QAAQ,EAAE;QACbA,QAAQ,GAAGb,EAAE,CAAC4B,OAAO,CAAChB,cAAc,EAAER,aAAa,CAACyB,IAAI,EAAE,IAAI,CAAC;QAC/DhB,QAAQ,CAACiB,KAAK,GAAG,UAAU;MAC7B;MAEA,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGX,SAAS,CAACO,MAAM,EAAEI,CAAC,EAAE,EAAE;QACzC;QACA,IAAIX,SAAS,CAACW,CAAC,CAAC,KAAKlB,QAAQ,IAAIO,SAAS,CAACW,CAAC,CAAC,CAACC,UAAU,KAAKnB,QAAQ,CAACmB,UAAU,EAAE;UAChFZ,SAAS,CAACW,CAAC,CAAC,CAACE,UAAU,CAACR,QAAQ,CAAC;QACnC;MACF;MAEAb,cAAc,KAAKD,cAAc,CAACC,cAAc,CAAC,GAAGD,cAAc,CAACC,cAAc,CAAC,IAAI,CAAC,CAAC,CAAC;MACzF,IAAIsB,WAAW,GAAGtB,cAAc,GAAGD,cAAc,CAACC,cAAc,CAAC,GAAGD,cAAc;MAClFrB,IAAI,CAACmC,QAAQ,EAAE,UAAUC,GAAG,EAAE;QAC5B;QACAQ,WAAW,CAACR,GAAG,CAAC,GAAG,CAAC,CAACd,cAAc,GAAGZ,EAAE,CAACY,cAAc,CAAC,GAAGZ,EAAE,KAAK,CAAC,CAAC,EAAE0B,GAAG,CAAC;MAC5E,CAAC,CAAC;MACFb,QAAQ,CAACsB,YAAY,CAAC1B,QAAQ,GAAGU,EAAE,CAACD,OAAO,EAAEG,QAAQ,EAAEI,QAAQ,EAAEN,EAAE,CAACiB,MAAM,CAAC;IAC7E,CAAC,CAAC;IAEF,IAAI,CAACvB,QAAQ,EAAE;MACb;IACF;IAEA,IAAIS,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;MACzC,IAAI,CAACV,aAAa,EAAE;QAClBnB,IAAI,CAAC,gEAAgE,EAAE,IAAI,CAAC;MAC9E;IACF;IAEAkB,QAAQ,CAACwB,KAAK,CAACjC,aAAa,CAACiC,KAAK,IAAI,CAAC,CAAC,CAAC5B,QAAQ,CAACA,QAAQ,CAAC,CAAC6B,KAAK,CAAClC,aAAa,CAACgC,MAAM,CAAC;EACzF,CAAC,CAAC;AACJ"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}