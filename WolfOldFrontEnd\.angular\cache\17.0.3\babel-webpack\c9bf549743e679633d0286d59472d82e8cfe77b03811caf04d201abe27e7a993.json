{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\nimport SeriesModel from '../../model/Series.js';\nimport { WhiskerBoxCommonMixin } from '../helper/whiskerBoxCommon.js';\nimport { mixin } from 'zrender/lib/core/util.js';\nvar CandlestickSeriesModel = /** @class */\nfunction (_super) {\n  __extends(CandlestickSeriesModel, _super);\n  function CandlestickSeriesModel() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.type = CandlestickSeriesModel.type;\n    _this.defaultValueDimensions = [{\n      name: 'open',\n      defaultTooltip: true\n    }, {\n      name: 'close',\n      defaultTooltip: true\n    }, {\n      name: 'lowest',\n      defaultTooltip: true\n    }, {\n      name: 'highest',\n      defaultTooltip: true\n    }];\n    return _this;\n  }\n  /**\r\n   * Get dimension for shadow in dataZoom\r\n   * @return dimension name\r\n   */\n\n  CandlestickSeriesModel.prototype.getShadowDim = function () {\n    return 'open';\n  };\n  CandlestickSeriesModel.prototype.brushSelector = function (dataIndex, data, selectors) {\n    var itemLayout = data.getItemLayout(dataIndex);\n    return itemLayout && selectors.rect(itemLayout.brushRect);\n  };\n  CandlestickSeriesModel.type = 'series.candlestick';\n  CandlestickSeriesModel.dependencies = ['xAxis', 'yAxis', 'grid'];\n  CandlestickSeriesModel.defaultOption = {\n    // zlevel: 0,\n    z: 2,\n    coordinateSystem: 'cartesian2d',\n    legendHoverLink: true,\n    // xAxisIndex: 0,\n    // yAxisIndex: 0,\n    layout: null,\n    clip: true,\n    itemStyle: {\n      color: '#eb5454',\n      color0: '#47b262',\n      borderColor: '#eb5454',\n      borderColor0: '#47b262',\n      borderColorDoji: null,\n      // borderColor: '#d24040',\n      // borderColor0: '#398f4f',\n      borderWidth: 1\n    },\n    emphasis: {\n      scale: true,\n      itemStyle: {\n        borderWidth: 2\n      }\n    },\n    barMaxWidth: null,\n    barMinWidth: null,\n    barWidth: null,\n    large: true,\n    largeThreshold: 600,\n    progressive: 3e3,\n    progressiveThreshold: 1e4,\n    progressiveChunkMode: 'mod',\n    animationEasing: 'linear',\n    animationDuration: 300\n  };\n  return CandlestickSeriesModel;\n}(SeriesModel);\nmixin(CandlestickSeriesModel, WhiskerBoxCommonMixin, true);\nexport default CandlestickSeriesModel;", "map": {"version": 3, "names": ["__extends", "SeriesModel", "WhiskerBoxCommonMixin", "mixin", "CandlestickSeriesModel", "_super", "_this", "apply", "arguments", "type", "defaultValueDimensions", "name", "defaultTooltip", "prototype", "getS<PERSON>owDim", "brushSelector", "dataIndex", "data", "selectors", "itemLayout", "getItemLayout", "rect", "brushRect", "dependencies", "defaultOption", "z", "coordinateSystem", "legendHoverLink", "layout", "clip", "itemStyle", "color", "color0", "borderColor", "borderColor0", "borderColorDoji", "borderWidth", "emphasis", "scale", "barMaxWidth", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "large", "largeThreshold", "progressive", "progressiveThreshold", "progressiveChunkMode", "animationEasing", "animationDuration"], "sources": ["C:/WolfSystemV2/WolfOldFrontEnd/node_modules/echarts/lib/chart/candlestick/CandlestickSeries.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\nimport SeriesModel from '../../model/Series.js';\nimport { WhiskerBoxCommonMixin } from '../helper/whiskerBoxCommon.js';\nimport { mixin } from 'zrender/lib/core/util.js';\n\nvar CandlestickSeriesModel =\n/** @class */\nfunction (_super) {\n  __extends(CandlestickSeriesModel, _super);\n\n  function CandlestickSeriesModel() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n\n    _this.type = CandlestickSeriesModel.type;\n    _this.defaultValueDimensions = [{\n      name: 'open',\n      defaultTooltip: true\n    }, {\n      name: 'close',\n      defaultTooltip: true\n    }, {\n      name: 'lowest',\n      defaultTooltip: true\n    }, {\n      name: 'highest',\n      defaultTooltip: true\n    }];\n    return _this;\n  }\n  /**\r\n   * Get dimension for shadow in dataZoom\r\n   * @return dimension name\r\n   */\n\n\n  CandlestickSeriesModel.prototype.getShadowDim = function () {\n    return 'open';\n  };\n\n  CandlestickSeriesModel.prototype.brushSelector = function (dataIndex, data, selectors) {\n    var itemLayout = data.getItemLayout(dataIndex);\n    return itemLayout && selectors.rect(itemLayout.brushRect);\n  };\n\n  CandlestickSeriesModel.type = 'series.candlestick';\n  CandlestickSeriesModel.dependencies = ['xAxis', 'yAxis', 'grid'];\n  CandlestickSeriesModel.defaultOption = {\n    // zlevel: 0,\n    z: 2,\n    coordinateSystem: 'cartesian2d',\n    legendHoverLink: true,\n    // xAxisIndex: 0,\n    // yAxisIndex: 0,\n    layout: null,\n    clip: true,\n    itemStyle: {\n      color: '#eb5454',\n      color0: '#47b262',\n      borderColor: '#eb5454',\n      borderColor0: '#47b262',\n      borderColorDoji: null,\n      // borderColor: '#d24040',\n      // borderColor0: '#398f4f',\n      borderWidth: 1\n    },\n    emphasis: {\n      scale: true,\n      itemStyle: {\n        borderWidth: 2\n      }\n    },\n    barMaxWidth: null,\n    barMinWidth: null,\n    barWidth: null,\n    large: true,\n    largeThreshold: 600,\n    progressive: 3e3,\n    progressiveThreshold: 1e4,\n    progressiveChunkMode: 'mod',\n    animationEasing: 'linear',\n    animationDuration: 300\n  };\n  return CandlestickSeriesModel;\n}(SeriesModel);\n\nmixin(CandlestickSeriesModel, WhiskerBoxCommonMixin, true);\nexport default CandlestickSeriesModel;"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,SAAS,QAAQ,OAAO;AACjC,OAAOC,WAAW,MAAM,uBAAuB;AAC/C,SAASC,qBAAqB,QAAQ,+BAA+B;AACrE,SAASC,KAAK,QAAQ,0BAA0B;AAEhD,IAAIC,sBAAsB,GAC1B;AACA,UAAUC,MAAM,EAAE;EAChBL,SAAS,CAACI,sBAAsB,EAAEC,MAAM,CAAC;EAEzC,SAASD,sBAAsBA,CAAA,EAAG;IAChC,IAAIE,KAAK,GAAGD,MAAM,KAAK,IAAI,IAAIA,MAAM,CAACE,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC,IAAI,IAAI;IAEpEF,KAAK,CAACG,IAAI,GAAGL,sBAAsB,CAACK,IAAI;IACxCH,KAAK,CAACI,sBAAsB,GAAG,CAAC;MAC9BC,IAAI,EAAE,MAAM;MACZC,cAAc,EAAE;IAClB,CAAC,EAAE;MACDD,IAAI,EAAE,OAAO;MACbC,cAAc,EAAE;IAClB,CAAC,EAAE;MACDD,IAAI,EAAE,QAAQ;MACdC,cAAc,EAAE;IAClB,CAAC,EAAE;MACDD,IAAI,EAAE,SAAS;MACfC,cAAc,EAAE;IAClB,CAAC,CAAC;IACF,OAAON,KAAK;EACd;EACA;AACF;AACA;AACA;;EAGEF,sBAAsB,CAACS,SAAS,CAACC,YAAY,GAAG,YAAY;IAC1D,OAAO,MAAM;EACf,CAAC;EAEDV,sBAAsB,CAACS,SAAS,CAACE,aAAa,GAAG,UAAUC,SAAS,EAAEC,IAAI,EAAEC,SAAS,EAAE;IACrF,IAAIC,UAAU,GAAGF,IAAI,CAACG,aAAa,CAACJ,SAAS,CAAC;IAC9C,OAAOG,UAAU,IAAID,SAAS,CAACG,IAAI,CAACF,UAAU,CAACG,SAAS,CAAC;EAC3D,CAAC;EAEDlB,sBAAsB,CAACK,IAAI,GAAG,oBAAoB;EAClDL,sBAAsB,CAACmB,YAAY,GAAG,CAAC,OAAO,EAAE,OAAO,EAAE,MAAM,CAAC;EAChEnB,sBAAsB,CAACoB,aAAa,GAAG;IACrC;IACAC,CAAC,EAAE,CAAC;IACJC,gBAAgB,EAAE,aAAa;IAC/BC,eAAe,EAAE,IAAI;IACrB;IACA;IACAC,MAAM,EAAE,IAAI;IACZC,IAAI,EAAE,IAAI;IACVC,SAAS,EAAE;MACTC,KAAK,EAAE,SAAS;MAChBC,MAAM,EAAE,SAAS;MACjBC,WAAW,EAAE,SAAS;MACtBC,YAAY,EAAE,SAAS;MACvBC,eAAe,EAAE,IAAI;MACrB;MACA;MACAC,WAAW,EAAE;IACf,CAAC;IACDC,QAAQ,EAAE;MACRC,KAAK,EAAE,IAAI;MACXR,SAAS,EAAE;QACTM,WAAW,EAAE;MACf;IACF,CAAC;IACDG,WAAW,EAAE,IAAI;IACjBC,WAAW,EAAE,IAAI;IACjBC,QAAQ,EAAE,IAAI;IACdC,KAAK,EAAE,IAAI;IACXC,cAAc,EAAE,GAAG;IACnBC,WAAW,EAAE,GAAG;IAChBC,oBAAoB,EAAE,GAAG;IACzBC,oBAAoB,EAAE,KAAK;IAC3BC,eAAe,EAAE,QAAQ;IACzBC,iBAAiB,EAAE;EACrB,CAAC;EACD,OAAO5C,sBAAsB;AAC/B,CAAC,CAACH,WAAW,CAAC;AAEdE,KAAK,CAACC,sBAAsB,EAAEF,qBAAqB,EAAE,IAAI,CAAC;AAC1D,eAAeE,sBAAsB"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}