{"ast": null, "code": "import * as imageHelper from '../helper/image.js';\nimport { extend, retrieve2, retrieve3, reduce } from '../../core/util.js';\nimport { getLineHeight, getWidth, parsePercent } from '../../contain/text.js';\nvar STYLE_REG = /\\{([a-zA-Z0-9_]+)\\|([^}]*)\\}/g;\nexport function truncateText(text, containerWidth, font, ellipsis, options) {\n  if (!containerWidth) {\n    return '';\n  }\n  var textLines = (text + '').split('\\n');\n  options = prepareTruncateOptions(containerWidth, font, ellipsis, options);\n  for (var i = 0, len = textLines.length; i < len; i++) {\n    textLines[i] = truncateSingleLine(textLines[i], options);\n  }\n  return textLines.join('\\n');\n}\nfunction prepareTruncateOptions(containerWidth, font, ellipsis, options) {\n  options = options || {};\n  var preparedOpts = extend({}, options);\n  preparedOpts.font = font;\n  ellipsis = retrieve2(ellipsis, '...');\n  preparedOpts.maxIterations = retrieve2(options.maxIterations, 2);\n  var minChar = preparedOpts.minChar = retrieve2(options.minChar, 0);\n  preparedOpts.cnCharWidth = getWidth('国', font);\n  var ascCharWidth = preparedOpts.ascCharWidth = getWidth('a', font);\n  preparedOpts.placeholder = retrieve2(options.placeholder, '');\n  var contentWidth = containerWidth = Math.max(0, containerWidth - 1);\n  for (var i = 0; i < minChar && contentWidth >= ascCharWidth; i++) {\n    contentWidth -= ascCharWidth;\n  }\n  var ellipsisWidth = getWidth(ellipsis, font);\n  if (ellipsisWidth > contentWidth) {\n    ellipsis = '';\n    ellipsisWidth = 0;\n  }\n  contentWidth = containerWidth - ellipsisWidth;\n  preparedOpts.ellipsis = ellipsis;\n  preparedOpts.ellipsisWidth = ellipsisWidth;\n  preparedOpts.contentWidth = contentWidth;\n  preparedOpts.containerWidth = containerWidth;\n  return preparedOpts;\n}\nfunction truncateSingleLine(textLine, options) {\n  var containerWidth = options.containerWidth;\n  var font = options.font;\n  var contentWidth = options.contentWidth;\n  if (!containerWidth) {\n    return '';\n  }\n  var lineWidth = getWidth(textLine, font);\n  if (lineWidth <= containerWidth) {\n    return textLine;\n  }\n  for (var j = 0;; j++) {\n    if (lineWidth <= contentWidth || j >= options.maxIterations) {\n      textLine += options.ellipsis;\n      break;\n    }\n    var subLength = j === 0 ? estimateLength(textLine, contentWidth, options.ascCharWidth, options.cnCharWidth) : lineWidth > 0 ? Math.floor(textLine.length * contentWidth / lineWidth) : 0;\n    textLine = textLine.substr(0, subLength);\n    lineWidth = getWidth(textLine, font);\n  }\n  if (textLine === '') {\n    textLine = options.placeholder;\n  }\n  return textLine;\n}\nfunction estimateLength(text, contentWidth, ascCharWidth, cnCharWidth) {\n  var width = 0;\n  var i = 0;\n  for (var len = text.length; i < len && width < contentWidth; i++) {\n    var charCode = text.charCodeAt(i);\n    width += 0 <= charCode && charCode <= 127 ? ascCharWidth : cnCharWidth;\n  }\n  return i;\n}\nexport function parsePlainText(text, style) {\n  text != null && (text += '');\n  var overflow = style.overflow;\n  var padding = style.padding;\n  var font = style.font;\n  var truncate = overflow === 'truncate';\n  var calculatedLineHeight = getLineHeight(font);\n  var lineHeight = retrieve2(style.lineHeight, calculatedLineHeight);\n  var bgColorDrawn = !!style.backgroundColor;\n  var truncateLineOverflow = style.lineOverflow === 'truncate';\n  var width = style.width;\n  var lines;\n  if (width != null && (overflow === 'break' || overflow === 'breakAll')) {\n    lines = text ? wrapText(text, style.font, width, overflow === 'breakAll', 0).lines : [];\n  } else {\n    lines = text ? text.split('\\n') : [];\n  }\n  var contentHeight = lines.length * lineHeight;\n  var height = retrieve2(style.height, contentHeight);\n  if (contentHeight > height && truncateLineOverflow) {\n    var lineCount = Math.floor(height / lineHeight);\n    lines = lines.slice(0, lineCount);\n  }\n  if (text && truncate && width != null) {\n    var options = prepareTruncateOptions(width, font, style.ellipsis, {\n      minChar: style.truncateMinChar,\n      placeholder: style.placeholder\n    });\n    for (var i = 0; i < lines.length; i++) {\n      lines[i] = truncateSingleLine(lines[i], options);\n    }\n  }\n  var outerHeight = height;\n  var contentWidth = 0;\n  for (var i = 0; i < lines.length; i++) {\n    contentWidth = Math.max(getWidth(lines[i], font), contentWidth);\n  }\n  if (width == null) {\n    width = contentWidth;\n  }\n  var outerWidth = contentWidth;\n  if (padding) {\n    outerHeight += padding[0] + padding[2];\n    outerWidth += padding[1] + padding[3];\n    width += padding[1] + padding[3];\n  }\n  if (bgColorDrawn) {\n    outerWidth = width;\n  }\n  return {\n    lines: lines,\n    height: height,\n    outerWidth: outerWidth,\n    outerHeight: outerHeight,\n    lineHeight: lineHeight,\n    calculatedLineHeight: calculatedLineHeight,\n    contentWidth: contentWidth,\n    contentHeight: contentHeight,\n    width: width\n  };\n}\nvar RichTextToken = function () {\n  function RichTextToken() {}\n  return RichTextToken;\n}();\nvar RichTextLine = function () {\n  function RichTextLine(tokens) {\n    this.tokens = [];\n    if (tokens) {\n      this.tokens = tokens;\n    }\n  }\n  return RichTextLine;\n}();\nvar RichTextContentBlock = function () {\n  function RichTextContentBlock() {\n    this.width = 0;\n    this.height = 0;\n    this.contentWidth = 0;\n    this.contentHeight = 0;\n    this.outerWidth = 0;\n    this.outerHeight = 0;\n    this.lines = [];\n  }\n  return RichTextContentBlock;\n}();\nexport { RichTextContentBlock };\nexport function parseRichText(text, style) {\n  var contentBlock = new RichTextContentBlock();\n  text != null && (text += '');\n  if (!text) {\n    return contentBlock;\n  }\n  var topWidth = style.width;\n  var topHeight = style.height;\n  var overflow = style.overflow;\n  var wrapInfo = (overflow === 'break' || overflow === 'breakAll') && topWidth != null ? {\n    width: topWidth,\n    accumWidth: 0,\n    breakAll: overflow === 'breakAll'\n  } : null;\n  var lastIndex = STYLE_REG.lastIndex = 0;\n  var result;\n  while ((result = STYLE_REG.exec(text)) != null) {\n    var matchedIndex = result.index;\n    if (matchedIndex > lastIndex) {\n      pushTokens(contentBlock, text.substring(lastIndex, matchedIndex), style, wrapInfo);\n    }\n    pushTokens(contentBlock, result[2], style, wrapInfo, result[1]);\n    lastIndex = STYLE_REG.lastIndex;\n  }\n  if (lastIndex < text.length) {\n    pushTokens(contentBlock, text.substring(lastIndex, text.length), style, wrapInfo);\n  }\n  var pendingList = [];\n  var calculatedHeight = 0;\n  var calculatedWidth = 0;\n  var stlPadding = style.padding;\n  var truncate = overflow === 'truncate';\n  var truncateLine = style.lineOverflow === 'truncate';\n  function finishLine(line, lineWidth, lineHeight) {\n    line.width = lineWidth;\n    line.lineHeight = lineHeight;\n    calculatedHeight += lineHeight;\n    calculatedWidth = Math.max(calculatedWidth, lineWidth);\n  }\n  outer: for (var i = 0; i < contentBlock.lines.length; i++) {\n    var line = contentBlock.lines[i];\n    var lineHeight = 0;\n    var lineWidth = 0;\n    for (var j = 0; j < line.tokens.length; j++) {\n      var token = line.tokens[j];\n      var tokenStyle = token.styleName && style.rich[token.styleName] || {};\n      var textPadding = token.textPadding = tokenStyle.padding;\n      var paddingH = textPadding ? textPadding[1] + textPadding[3] : 0;\n      var font = token.font = tokenStyle.font || style.font;\n      token.contentHeight = getLineHeight(font);\n      var tokenHeight = retrieve2(tokenStyle.height, token.contentHeight);\n      token.innerHeight = tokenHeight;\n      textPadding && (tokenHeight += textPadding[0] + textPadding[2]);\n      token.height = tokenHeight;\n      token.lineHeight = retrieve3(tokenStyle.lineHeight, style.lineHeight, tokenHeight);\n      token.align = tokenStyle && tokenStyle.align || style.align;\n      token.verticalAlign = tokenStyle && tokenStyle.verticalAlign || 'middle';\n      if (truncateLine && topHeight != null && calculatedHeight + token.lineHeight > topHeight) {\n        if (j > 0) {\n          line.tokens = line.tokens.slice(0, j);\n          finishLine(line, lineWidth, lineHeight);\n          contentBlock.lines = contentBlock.lines.slice(0, i + 1);\n        } else {\n          contentBlock.lines = contentBlock.lines.slice(0, i);\n        }\n        break outer;\n      }\n      var styleTokenWidth = tokenStyle.width;\n      var tokenWidthNotSpecified = styleTokenWidth == null || styleTokenWidth === 'auto';\n      if (typeof styleTokenWidth === 'string' && styleTokenWidth.charAt(styleTokenWidth.length - 1) === '%') {\n        token.percentWidth = styleTokenWidth;\n        pendingList.push(token);\n        token.contentWidth = getWidth(token.text, font);\n      } else {\n        if (tokenWidthNotSpecified) {\n          var textBackgroundColor = tokenStyle.backgroundColor;\n          var bgImg = textBackgroundColor && textBackgroundColor.image;\n          if (bgImg) {\n            bgImg = imageHelper.findExistImage(bgImg);\n            if (imageHelper.isImageReady(bgImg)) {\n              token.width = Math.max(token.width, bgImg.width * tokenHeight / bgImg.height);\n            }\n          }\n        }\n        var remainTruncWidth = truncate && topWidth != null ? topWidth - lineWidth : null;\n        if (remainTruncWidth != null && remainTruncWidth < token.width) {\n          if (!tokenWidthNotSpecified || remainTruncWidth < paddingH) {\n            token.text = '';\n            token.width = token.contentWidth = 0;\n          } else {\n            token.text = truncateText(token.text, remainTruncWidth - paddingH, font, style.ellipsis, {\n              minChar: style.truncateMinChar\n            });\n            token.width = token.contentWidth = getWidth(token.text, font);\n          }\n        } else {\n          token.contentWidth = getWidth(token.text, font);\n        }\n      }\n      token.width += paddingH;\n      lineWidth += token.width;\n      tokenStyle && (lineHeight = Math.max(lineHeight, token.lineHeight));\n    }\n    finishLine(line, lineWidth, lineHeight);\n  }\n  contentBlock.outerWidth = contentBlock.width = retrieve2(topWidth, calculatedWidth);\n  contentBlock.outerHeight = contentBlock.height = retrieve2(topHeight, calculatedHeight);\n  contentBlock.contentHeight = calculatedHeight;\n  contentBlock.contentWidth = calculatedWidth;\n  if (stlPadding) {\n    contentBlock.outerWidth += stlPadding[1] + stlPadding[3];\n    contentBlock.outerHeight += stlPadding[0] + stlPadding[2];\n  }\n  for (var i = 0; i < pendingList.length; i++) {\n    var token = pendingList[i];\n    var percentWidth = token.percentWidth;\n    token.width = parseInt(percentWidth, 10) / 100 * contentBlock.width;\n  }\n  return contentBlock;\n}\nfunction pushTokens(block, str, style, wrapInfo, styleName) {\n  var isEmptyStr = str === '';\n  var tokenStyle = styleName && style.rich[styleName] || {};\n  var lines = block.lines;\n  var font = tokenStyle.font || style.font;\n  var newLine = false;\n  var strLines;\n  var linesWidths;\n  if (wrapInfo) {\n    var tokenPadding = tokenStyle.padding;\n    var tokenPaddingH = tokenPadding ? tokenPadding[1] + tokenPadding[3] : 0;\n    if (tokenStyle.width != null && tokenStyle.width !== 'auto') {\n      var outerWidth_1 = parsePercent(tokenStyle.width, wrapInfo.width) + tokenPaddingH;\n      if (lines.length > 0) {\n        if (outerWidth_1 + wrapInfo.accumWidth > wrapInfo.width) {\n          strLines = str.split('\\n');\n          newLine = true;\n        }\n      }\n      wrapInfo.accumWidth = outerWidth_1;\n    } else {\n      var res = wrapText(str, font, wrapInfo.width, wrapInfo.breakAll, wrapInfo.accumWidth);\n      wrapInfo.accumWidth = res.accumWidth + tokenPaddingH;\n      linesWidths = res.linesWidths;\n      strLines = res.lines;\n    }\n  } else {\n    strLines = str.split('\\n');\n  }\n  for (var i = 0; i < strLines.length; i++) {\n    var text = strLines[i];\n    var token = new RichTextToken();\n    token.styleName = styleName;\n    token.text = text;\n    token.isLineHolder = !text && !isEmptyStr;\n    if (typeof tokenStyle.width === 'number') {\n      token.width = tokenStyle.width;\n    } else {\n      token.width = linesWidths ? linesWidths[i] : getWidth(text, font);\n    }\n    if (!i && !newLine) {\n      var tokens = (lines[lines.length - 1] || (lines[0] = new RichTextLine())).tokens;\n      var tokensLen = tokens.length;\n      tokensLen === 1 && tokens[0].isLineHolder ? tokens[0] = token : (text || !tokensLen || isEmptyStr) && tokens.push(token);\n    } else {\n      lines.push(new RichTextLine([token]));\n    }\n  }\n}\nfunction isAlphabeticLetter(ch) {\n  var code = ch.charCodeAt(0);\n  return code >= 0x20 && code <= 0x24F || code >= 0x370 && code <= 0x10FF || code >= 0x1200 && code <= 0x13FF || code >= 0x1E00 && code <= 0x206F;\n}\nvar breakCharMap = reduce(',&?/;] '.split(''), function (obj, ch) {\n  obj[ch] = true;\n  return obj;\n}, {});\nfunction isWordBreakChar(ch) {\n  if (isAlphabeticLetter(ch)) {\n    if (breakCharMap[ch]) {\n      return true;\n    }\n    return false;\n  }\n  return true;\n}\nfunction wrapText(text, font, lineWidth, isBreakAll, lastAccumWidth) {\n  var lines = [];\n  var linesWidths = [];\n  var line = '';\n  var currentWord = '';\n  var currentWordWidth = 0;\n  var accumWidth = 0;\n  for (var i = 0; i < text.length; i++) {\n    var ch = text.charAt(i);\n    if (ch === '\\n') {\n      if (currentWord) {\n        line += currentWord;\n        accumWidth += currentWordWidth;\n      }\n      lines.push(line);\n      linesWidths.push(accumWidth);\n      line = '';\n      currentWord = '';\n      currentWordWidth = 0;\n      accumWidth = 0;\n      continue;\n    }\n    var chWidth = getWidth(ch, font);\n    var inWord = isBreakAll ? false : !isWordBreakChar(ch);\n    if (!lines.length ? lastAccumWidth + accumWidth + chWidth > lineWidth : accumWidth + chWidth > lineWidth) {\n      if (!accumWidth) {\n        if (inWord) {\n          lines.push(currentWord);\n          linesWidths.push(currentWordWidth);\n          currentWord = ch;\n          currentWordWidth = chWidth;\n        } else {\n          lines.push(ch);\n          linesWidths.push(chWidth);\n        }\n      } else if (line || currentWord) {\n        if (inWord) {\n          if (!line) {\n            line = currentWord;\n            currentWord = '';\n            currentWordWidth = 0;\n            accumWidth = currentWordWidth;\n          }\n          lines.push(line);\n          linesWidths.push(accumWidth - currentWordWidth);\n          currentWord += ch;\n          currentWordWidth += chWidth;\n          line = '';\n          accumWidth = currentWordWidth;\n        } else {\n          if (currentWord) {\n            line += currentWord;\n            currentWord = '';\n            currentWordWidth = 0;\n          }\n          lines.push(line);\n          linesWidths.push(accumWidth);\n          line = ch;\n          accumWidth = chWidth;\n        }\n      }\n      continue;\n    }\n    accumWidth += chWidth;\n    if (inWord) {\n      currentWord += ch;\n      currentWordWidth += chWidth;\n    } else {\n      if (currentWord) {\n        line += currentWord;\n        currentWord = '';\n        currentWordWidth = 0;\n      }\n      line += ch;\n    }\n  }\n  if (!lines.length && !line) {\n    line = text;\n    currentWord = '';\n    currentWordWidth = 0;\n  }\n  if (currentWord) {\n    line += currentWord;\n  }\n  if (line) {\n    lines.push(line);\n    linesWidths.push(accumWidth);\n  }\n  if (lines.length === 1) {\n    accumWidth += lastAccumWidth;\n  }\n  return {\n    accumWidth: accumWidth,\n    lines: lines,\n    linesWidths: linesWidths\n  };\n}", "map": {"version": 3, "names": ["imageHelper", "extend", "retrieve2", "retrieve3", "reduce", "getLineHeight", "getWidth", "parsePercent", "STYLE_REG", "truncateText", "text", "containerWidth", "font", "ellipsis", "options", "textLines", "split", "prepareTruncateOptions", "i", "len", "length", "truncateSingleLine", "join", "preparedOpts", "maxIterations", "minChar", "cnChar<PERSON>idth", "ascCharWidth", "placeholder", "contentWidth", "Math", "max", "ellip<PERSON><PERSON><PERSON><PERSON>", "textLine", "lineWidth", "j", "subLength", "estimateLength", "floor", "substr", "width", "charCode", "charCodeAt", "parsePlainText", "style", "overflow", "padding", "truncate", "calculatedLineHeight", "lineHeight", "bgColorDrawn", "backgroundColor", "truncateLineOverflow", "lineOverflow", "lines", "wrapText", "contentHeight", "height", "lineCount", "slice", "truncateMinChar", "outerHeight", "outerWidth", "RichTextToken", "RichTextLine", "tokens", "RichTextContentBlock", "parseRichText", "contentBlock", "topWidth", "topHeight", "wrapInfo", "accumWidth", "breakAll", "lastIndex", "result", "exec", "matchedIndex", "index", "pushTokens", "substring", "pendingList", "calculatedHeight", "calculatedWidth", "stlPadding", "truncateLine", "finishLine", "line", "outer", "token", "tokenStyle", "styleName", "rich", "textPadding", "paddingH", "tokenHeight", "innerHeight", "align", "verticalAlign", "styleTokenWidth", "tokenWidthNotSpecified", "char<PERSON>t", "percentWidth", "push", "textBackgroundColor", "bgImg", "image", "findExistImage", "isImageReady", "remainTrunc<PERSON>idth", "parseInt", "block", "str", "isEmptyStr", "newLine", "strLines", "linesWidths", "tokenPadding", "tokenPaddingH", "outerWidth_1", "res", "isLineHolder", "tokensLen", "isAlphabeticLetter", "ch", "code", "breakCharMap", "obj", "isWordBreakChar", "isBreakAll", "lastAccum<PERSON>idth", "currentWord", "currentWordWidth", "ch<PERSON><PERSON><PERSON>", "inWord"], "sources": ["C:/WolfSystemV2/WolfOldFrontEnd/node_modules/zrender/lib/graphic/helper/parseText.js"], "sourcesContent": ["import * as imageHelper from '../helper/image.js';\nimport { extend, retrieve2, retrieve3, reduce } from '../../core/util.js';\nimport { getLineHeight, getWidth, parsePercent } from '../../contain/text.js';\nvar STYLE_REG = /\\{([a-zA-Z0-9_]+)\\|([^}]*)\\}/g;\nexport function truncateText(text, containerWidth, font, ellipsis, options) {\n    if (!containerWidth) {\n        return '';\n    }\n    var textLines = (text + '').split('\\n');\n    options = prepareTruncateOptions(containerWidth, font, ellipsis, options);\n    for (var i = 0, len = textLines.length; i < len; i++) {\n        textLines[i] = truncateSingleLine(textLines[i], options);\n    }\n    return textLines.join('\\n');\n}\nfunction prepareTruncateOptions(containerWidth, font, ellipsis, options) {\n    options = options || {};\n    var preparedOpts = extend({}, options);\n    preparedOpts.font = font;\n    ellipsis = retrieve2(ellipsis, '...');\n    preparedOpts.maxIterations = retrieve2(options.maxIterations, 2);\n    var minChar = preparedOpts.minChar = retrieve2(options.minChar, 0);\n    preparedOpts.cnCharWidth = getWidth('国', font);\n    var ascCharWidth = preparedOpts.ascCharWidth = getWidth('a', font);\n    preparedOpts.placeholder = retrieve2(options.placeholder, '');\n    var contentWidth = containerWidth = Math.max(0, containerWidth - 1);\n    for (var i = 0; i < minChar && contentWidth >= ascCharWidth; i++) {\n        contentWidth -= ascCharWidth;\n    }\n    var ellipsisWidth = getWidth(ellipsis, font);\n    if (ellipsisWidth > contentWidth) {\n        ellipsis = '';\n        ellipsisWidth = 0;\n    }\n    contentWidth = containerWidth - ellipsisWidth;\n    preparedOpts.ellipsis = ellipsis;\n    preparedOpts.ellipsisWidth = ellipsisWidth;\n    preparedOpts.contentWidth = contentWidth;\n    preparedOpts.containerWidth = containerWidth;\n    return preparedOpts;\n}\nfunction truncateSingleLine(textLine, options) {\n    var containerWidth = options.containerWidth;\n    var font = options.font;\n    var contentWidth = options.contentWidth;\n    if (!containerWidth) {\n        return '';\n    }\n    var lineWidth = getWidth(textLine, font);\n    if (lineWidth <= containerWidth) {\n        return textLine;\n    }\n    for (var j = 0;; j++) {\n        if (lineWidth <= contentWidth || j >= options.maxIterations) {\n            textLine += options.ellipsis;\n            break;\n        }\n        var subLength = j === 0\n            ? estimateLength(textLine, contentWidth, options.ascCharWidth, options.cnCharWidth)\n            : lineWidth > 0\n                ? Math.floor(textLine.length * contentWidth / lineWidth)\n                : 0;\n        textLine = textLine.substr(0, subLength);\n        lineWidth = getWidth(textLine, font);\n    }\n    if (textLine === '') {\n        textLine = options.placeholder;\n    }\n    return textLine;\n}\nfunction estimateLength(text, contentWidth, ascCharWidth, cnCharWidth) {\n    var width = 0;\n    var i = 0;\n    for (var len = text.length; i < len && width < contentWidth; i++) {\n        var charCode = text.charCodeAt(i);\n        width += (0 <= charCode && charCode <= 127) ? ascCharWidth : cnCharWidth;\n    }\n    return i;\n}\nexport function parsePlainText(text, style) {\n    text != null && (text += '');\n    var overflow = style.overflow;\n    var padding = style.padding;\n    var font = style.font;\n    var truncate = overflow === 'truncate';\n    var calculatedLineHeight = getLineHeight(font);\n    var lineHeight = retrieve2(style.lineHeight, calculatedLineHeight);\n    var bgColorDrawn = !!(style.backgroundColor);\n    var truncateLineOverflow = style.lineOverflow === 'truncate';\n    var width = style.width;\n    var lines;\n    if (width != null && (overflow === 'break' || overflow === 'breakAll')) {\n        lines = text ? wrapText(text, style.font, width, overflow === 'breakAll', 0).lines : [];\n    }\n    else {\n        lines = text ? text.split('\\n') : [];\n    }\n    var contentHeight = lines.length * lineHeight;\n    var height = retrieve2(style.height, contentHeight);\n    if (contentHeight > height && truncateLineOverflow) {\n        var lineCount = Math.floor(height / lineHeight);\n        lines = lines.slice(0, lineCount);\n    }\n    if (text && truncate && width != null) {\n        var options = prepareTruncateOptions(width, font, style.ellipsis, {\n            minChar: style.truncateMinChar,\n            placeholder: style.placeholder\n        });\n        for (var i = 0; i < lines.length; i++) {\n            lines[i] = truncateSingleLine(lines[i], options);\n        }\n    }\n    var outerHeight = height;\n    var contentWidth = 0;\n    for (var i = 0; i < lines.length; i++) {\n        contentWidth = Math.max(getWidth(lines[i], font), contentWidth);\n    }\n    if (width == null) {\n        width = contentWidth;\n    }\n    var outerWidth = contentWidth;\n    if (padding) {\n        outerHeight += padding[0] + padding[2];\n        outerWidth += padding[1] + padding[3];\n        width += padding[1] + padding[3];\n    }\n    if (bgColorDrawn) {\n        outerWidth = width;\n    }\n    return {\n        lines: lines,\n        height: height,\n        outerWidth: outerWidth,\n        outerHeight: outerHeight,\n        lineHeight: lineHeight,\n        calculatedLineHeight: calculatedLineHeight,\n        contentWidth: contentWidth,\n        contentHeight: contentHeight,\n        width: width\n    };\n}\nvar RichTextToken = (function () {\n    function RichTextToken() {\n    }\n    return RichTextToken;\n}());\nvar RichTextLine = (function () {\n    function RichTextLine(tokens) {\n        this.tokens = [];\n        if (tokens) {\n            this.tokens = tokens;\n        }\n    }\n    return RichTextLine;\n}());\nvar RichTextContentBlock = (function () {\n    function RichTextContentBlock() {\n        this.width = 0;\n        this.height = 0;\n        this.contentWidth = 0;\n        this.contentHeight = 0;\n        this.outerWidth = 0;\n        this.outerHeight = 0;\n        this.lines = [];\n    }\n    return RichTextContentBlock;\n}());\nexport { RichTextContentBlock };\nexport function parseRichText(text, style) {\n    var contentBlock = new RichTextContentBlock();\n    text != null && (text += '');\n    if (!text) {\n        return contentBlock;\n    }\n    var topWidth = style.width;\n    var topHeight = style.height;\n    var overflow = style.overflow;\n    var wrapInfo = (overflow === 'break' || overflow === 'breakAll') && topWidth != null\n        ? { width: topWidth, accumWidth: 0, breakAll: overflow === 'breakAll' }\n        : null;\n    var lastIndex = STYLE_REG.lastIndex = 0;\n    var result;\n    while ((result = STYLE_REG.exec(text)) != null) {\n        var matchedIndex = result.index;\n        if (matchedIndex > lastIndex) {\n            pushTokens(contentBlock, text.substring(lastIndex, matchedIndex), style, wrapInfo);\n        }\n        pushTokens(contentBlock, result[2], style, wrapInfo, result[1]);\n        lastIndex = STYLE_REG.lastIndex;\n    }\n    if (lastIndex < text.length) {\n        pushTokens(contentBlock, text.substring(lastIndex, text.length), style, wrapInfo);\n    }\n    var pendingList = [];\n    var calculatedHeight = 0;\n    var calculatedWidth = 0;\n    var stlPadding = style.padding;\n    var truncate = overflow === 'truncate';\n    var truncateLine = style.lineOverflow === 'truncate';\n    function finishLine(line, lineWidth, lineHeight) {\n        line.width = lineWidth;\n        line.lineHeight = lineHeight;\n        calculatedHeight += lineHeight;\n        calculatedWidth = Math.max(calculatedWidth, lineWidth);\n    }\n    outer: for (var i = 0; i < contentBlock.lines.length; i++) {\n        var line = contentBlock.lines[i];\n        var lineHeight = 0;\n        var lineWidth = 0;\n        for (var j = 0; j < line.tokens.length; j++) {\n            var token = line.tokens[j];\n            var tokenStyle = token.styleName && style.rich[token.styleName] || {};\n            var textPadding = token.textPadding = tokenStyle.padding;\n            var paddingH = textPadding ? textPadding[1] + textPadding[3] : 0;\n            var font = token.font = tokenStyle.font || style.font;\n            token.contentHeight = getLineHeight(font);\n            var tokenHeight = retrieve2(tokenStyle.height, token.contentHeight);\n            token.innerHeight = tokenHeight;\n            textPadding && (tokenHeight += textPadding[0] + textPadding[2]);\n            token.height = tokenHeight;\n            token.lineHeight = retrieve3(tokenStyle.lineHeight, style.lineHeight, tokenHeight);\n            token.align = tokenStyle && tokenStyle.align || style.align;\n            token.verticalAlign = tokenStyle && tokenStyle.verticalAlign || 'middle';\n            if (truncateLine && topHeight != null && calculatedHeight + token.lineHeight > topHeight) {\n                if (j > 0) {\n                    line.tokens = line.tokens.slice(0, j);\n                    finishLine(line, lineWidth, lineHeight);\n                    contentBlock.lines = contentBlock.lines.slice(0, i + 1);\n                }\n                else {\n                    contentBlock.lines = contentBlock.lines.slice(0, i);\n                }\n                break outer;\n            }\n            var styleTokenWidth = tokenStyle.width;\n            var tokenWidthNotSpecified = styleTokenWidth == null || styleTokenWidth === 'auto';\n            if (typeof styleTokenWidth === 'string' && styleTokenWidth.charAt(styleTokenWidth.length - 1) === '%') {\n                token.percentWidth = styleTokenWidth;\n                pendingList.push(token);\n                token.contentWidth = getWidth(token.text, font);\n            }\n            else {\n                if (tokenWidthNotSpecified) {\n                    var textBackgroundColor = tokenStyle.backgroundColor;\n                    var bgImg = textBackgroundColor && textBackgroundColor.image;\n                    if (bgImg) {\n                        bgImg = imageHelper.findExistImage(bgImg);\n                        if (imageHelper.isImageReady(bgImg)) {\n                            token.width = Math.max(token.width, bgImg.width * tokenHeight / bgImg.height);\n                        }\n                    }\n                }\n                var remainTruncWidth = truncate && topWidth != null\n                    ? topWidth - lineWidth : null;\n                if (remainTruncWidth != null && remainTruncWidth < token.width) {\n                    if (!tokenWidthNotSpecified || remainTruncWidth < paddingH) {\n                        token.text = '';\n                        token.width = token.contentWidth = 0;\n                    }\n                    else {\n                        token.text = truncateText(token.text, remainTruncWidth - paddingH, font, style.ellipsis, { minChar: style.truncateMinChar });\n                        token.width = token.contentWidth = getWidth(token.text, font);\n                    }\n                }\n                else {\n                    token.contentWidth = getWidth(token.text, font);\n                }\n            }\n            token.width += paddingH;\n            lineWidth += token.width;\n            tokenStyle && (lineHeight = Math.max(lineHeight, token.lineHeight));\n        }\n        finishLine(line, lineWidth, lineHeight);\n    }\n    contentBlock.outerWidth = contentBlock.width = retrieve2(topWidth, calculatedWidth);\n    contentBlock.outerHeight = contentBlock.height = retrieve2(topHeight, calculatedHeight);\n    contentBlock.contentHeight = calculatedHeight;\n    contentBlock.contentWidth = calculatedWidth;\n    if (stlPadding) {\n        contentBlock.outerWidth += stlPadding[1] + stlPadding[3];\n        contentBlock.outerHeight += stlPadding[0] + stlPadding[2];\n    }\n    for (var i = 0; i < pendingList.length; i++) {\n        var token = pendingList[i];\n        var percentWidth = token.percentWidth;\n        token.width = parseInt(percentWidth, 10) / 100 * contentBlock.width;\n    }\n    return contentBlock;\n}\nfunction pushTokens(block, str, style, wrapInfo, styleName) {\n    var isEmptyStr = str === '';\n    var tokenStyle = styleName && style.rich[styleName] || {};\n    var lines = block.lines;\n    var font = tokenStyle.font || style.font;\n    var newLine = false;\n    var strLines;\n    var linesWidths;\n    if (wrapInfo) {\n        var tokenPadding = tokenStyle.padding;\n        var tokenPaddingH = tokenPadding ? tokenPadding[1] + tokenPadding[3] : 0;\n        if (tokenStyle.width != null && tokenStyle.width !== 'auto') {\n            var outerWidth_1 = parsePercent(tokenStyle.width, wrapInfo.width) + tokenPaddingH;\n            if (lines.length > 0) {\n                if (outerWidth_1 + wrapInfo.accumWidth > wrapInfo.width) {\n                    strLines = str.split('\\n');\n                    newLine = true;\n                }\n            }\n            wrapInfo.accumWidth = outerWidth_1;\n        }\n        else {\n            var res = wrapText(str, font, wrapInfo.width, wrapInfo.breakAll, wrapInfo.accumWidth);\n            wrapInfo.accumWidth = res.accumWidth + tokenPaddingH;\n            linesWidths = res.linesWidths;\n            strLines = res.lines;\n        }\n    }\n    else {\n        strLines = str.split('\\n');\n    }\n    for (var i = 0; i < strLines.length; i++) {\n        var text = strLines[i];\n        var token = new RichTextToken();\n        token.styleName = styleName;\n        token.text = text;\n        token.isLineHolder = !text && !isEmptyStr;\n        if (typeof tokenStyle.width === 'number') {\n            token.width = tokenStyle.width;\n        }\n        else {\n            token.width = linesWidths\n                ? linesWidths[i]\n                : getWidth(text, font);\n        }\n        if (!i && !newLine) {\n            var tokens = (lines[lines.length - 1] || (lines[0] = new RichTextLine())).tokens;\n            var tokensLen = tokens.length;\n            (tokensLen === 1 && tokens[0].isLineHolder)\n                ? (tokens[0] = token)\n                : ((text || !tokensLen || isEmptyStr) && tokens.push(token));\n        }\n        else {\n            lines.push(new RichTextLine([token]));\n        }\n    }\n}\nfunction isAlphabeticLetter(ch) {\n    var code = ch.charCodeAt(0);\n    return code >= 0x20 && code <= 0x24F\n        || code >= 0x370 && code <= 0x10FF\n        || code >= 0x1200 && code <= 0x13FF\n        || code >= 0x1E00 && code <= 0x206F;\n}\nvar breakCharMap = reduce(',&?/;] '.split(''), function (obj, ch) {\n    obj[ch] = true;\n    return obj;\n}, {});\nfunction isWordBreakChar(ch) {\n    if (isAlphabeticLetter(ch)) {\n        if (breakCharMap[ch]) {\n            return true;\n        }\n        return false;\n    }\n    return true;\n}\nfunction wrapText(text, font, lineWidth, isBreakAll, lastAccumWidth) {\n    var lines = [];\n    var linesWidths = [];\n    var line = '';\n    var currentWord = '';\n    var currentWordWidth = 0;\n    var accumWidth = 0;\n    for (var i = 0; i < text.length; i++) {\n        var ch = text.charAt(i);\n        if (ch === '\\n') {\n            if (currentWord) {\n                line += currentWord;\n                accumWidth += currentWordWidth;\n            }\n            lines.push(line);\n            linesWidths.push(accumWidth);\n            line = '';\n            currentWord = '';\n            currentWordWidth = 0;\n            accumWidth = 0;\n            continue;\n        }\n        var chWidth = getWidth(ch, font);\n        var inWord = isBreakAll ? false : !isWordBreakChar(ch);\n        if (!lines.length\n            ? lastAccumWidth + accumWidth + chWidth > lineWidth\n            : accumWidth + chWidth > lineWidth) {\n            if (!accumWidth) {\n                if (inWord) {\n                    lines.push(currentWord);\n                    linesWidths.push(currentWordWidth);\n                    currentWord = ch;\n                    currentWordWidth = chWidth;\n                }\n                else {\n                    lines.push(ch);\n                    linesWidths.push(chWidth);\n                }\n            }\n            else if (line || currentWord) {\n                if (inWord) {\n                    if (!line) {\n                        line = currentWord;\n                        currentWord = '';\n                        currentWordWidth = 0;\n                        accumWidth = currentWordWidth;\n                    }\n                    lines.push(line);\n                    linesWidths.push(accumWidth - currentWordWidth);\n                    currentWord += ch;\n                    currentWordWidth += chWidth;\n                    line = '';\n                    accumWidth = currentWordWidth;\n                }\n                else {\n                    if (currentWord) {\n                        line += currentWord;\n                        currentWord = '';\n                        currentWordWidth = 0;\n                    }\n                    lines.push(line);\n                    linesWidths.push(accumWidth);\n                    line = ch;\n                    accumWidth = chWidth;\n                }\n            }\n            continue;\n        }\n        accumWidth += chWidth;\n        if (inWord) {\n            currentWord += ch;\n            currentWordWidth += chWidth;\n        }\n        else {\n            if (currentWord) {\n                line += currentWord;\n                currentWord = '';\n                currentWordWidth = 0;\n            }\n            line += ch;\n        }\n    }\n    if (!lines.length && !line) {\n        line = text;\n        currentWord = '';\n        currentWordWidth = 0;\n    }\n    if (currentWord) {\n        line += currentWord;\n    }\n    if (line) {\n        lines.push(line);\n        linesWidths.push(accumWidth);\n    }\n    if (lines.length === 1) {\n        accumWidth += lastAccumWidth;\n    }\n    return {\n        accumWidth: accumWidth,\n        lines: lines,\n        linesWidths: linesWidths\n    };\n}\n"], "mappings": "AAAA,OAAO,KAAKA,WAAW,MAAM,oBAAoB;AACjD,SAASC,MAAM,EAAEC,SAAS,EAAEC,SAAS,EAAEC,MAAM,QAAQ,oBAAoB;AACzE,SAASC,aAAa,EAAEC,QAAQ,EAAEC,YAAY,QAAQ,uBAAuB;AAC7E,IAAIC,SAAS,GAAG,+BAA+B;AAC/C,OAAO,SAASC,YAAYA,CAACC,IAAI,EAAEC,cAAc,EAAEC,IAAI,EAAEC,QAAQ,EAAEC,OAAO,EAAE;EACxE,IAAI,CAACH,cAAc,EAAE;IACjB,OAAO,EAAE;EACb;EACA,IAAII,SAAS,GAAG,CAACL,IAAI,GAAG,EAAE,EAAEM,KAAK,CAAC,IAAI,CAAC;EACvCF,OAAO,GAAGG,sBAAsB,CAACN,cAAc,EAAEC,IAAI,EAAEC,QAAQ,EAAEC,OAAO,CAAC;EACzE,KAAK,IAAII,CAAC,GAAG,CAAC,EAAEC,GAAG,GAAGJ,SAAS,CAACK,MAAM,EAAEF,CAAC,GAAGC,GAAG,EAAED,CAAC,EAAE,EAAE;IAClDH,SAAS,CAACG,CAAC,CAAC,GAAGG,kBAAkB,CAACN,SAAS,CAACG,CAAC,CAAC,EAAEJ,OAAO,CAAC;EAC5D;EACA,OAAOC,SAAS,CAACO,IAAI,CAAC,IAAI,CAAC;AAC/B;AACA,SAASL,sBAAsBA,CAACN,cAAc,EAAEC,IAAI,EAAEC,QAAQ,EAAEC,OAAO,EAAE;EACrEA,OAAO,GAAGA,OAAO,IAAI,CAAC,CAAC;EACvB,IAAIS,YAAY,GAAGtB,MAAM,CAAC,CAAC,CAAC,EAAEa,OAAO,CAAC;EACtCS,YAAY,CAACX,IAAI,GAAGA,IAAI;EACxBC,QAAQ,GAAGX,SAAS,CAACW,QAAQ,EAAE,KAAK,CAAC;EACrCU,YAAY,CAACC,aAAa,GAAGtB,SAAS,CAACY,OAAO,CAACU,aAAa,EAAE,CAAC,CAAC;EAChE,IAAIC,OAAO,GAAGF,YAAY,CAACE,OAAO,GAAGvB,SAAS,CAACY,OAAO,CAACW,OAAO,EAAE,CAAC,CAAC;EAClEF,YAAY,CAACG,WAAW,GAAGpB,QAAQ,CAAC,GAAG,EAAEM,IAAI,CAAC;EAC9C,IAAIe,YAAY,GAAGJ,YAAY,CAACI,YAAY,GAAGrB,QAAQ,CAAC,GAAG,EAAEM,IAAI,CAAC;EAClEW,YAAY,CAACK,WAAW,GAAG1B,SAAS,CAACY,OAAO,CAACc,WAAW,EAAE,EAAE,CAAC;EAC7D,IAAIC,YAAY,GAAGlB,cAAc,GAAGmB,IAAI,CAACC,GAAG,CAAC,CAAC,EAAEpB,cAAc,GAAG,CAAC,CAAC;EACnE,KAAK,IAAIO,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGO,OAAO,IAAII,YAAY,IAAIF,YAAY,EAAET,CAAC,EAAE,EAAE;IAC9DW,YAAY,IAAIF,YAAY;EAChC;EACA,IAAIK,aAAa,GAAG1B,QAAQ,CAACO,QAAQ,EAAED,IAAI,CAAC;EAC5C,IAAIoB,aAAa,GAAGH,YAAY,EAAE;IAC9BhB,QAAQ,GAAG,EAAE;IACbmB,aAAa,GAAG,CAAC;EACrB;EACAH,YAAY,GAAGlB,cAAc,GAAGqB,aAAa;EAC7CT,YAAY,CAACV,QAAQ,GAAGA,QAAQ;EAChCU,YAAY,CAACS,aAAa,GAAGA,aAAa;EAC1CT,YAAY,CAACM,YAAY,GAAGA,YAAY;EACxCN,YAAY,CAACZ,cAAc,GAAGA,cAAc;EAC5C,OAAOY,YAAY;AACvB;AACA,SAASF,kBAAkBA,CAACY,QAAQ,EAAEnB,OAAO,EAAE;EAC3C,IAAIH,cAAc,GAAGG,OAAO,CAACH,cAAc;EAC3C,IAAIC,IAAI,GAAGE,OAAO,CAACF,IAAI;EACvB,IAAIiB,YAAY,GAAGf,OAAO,CAACe,YAAY;EACvC,IAAI,CAAClB,cAAc,EAAE;IACjB,OAAO,EAAE;EACb;EACA,IAAIuB,SAAS,GAAG5B,QAAQ,CAAC2B,QAAQ,EAAErB,IAAI,CAAC;EACxC,IAAIsB,SAAS,IAAIvB,cAAc,EAAE;IAC7B,OAAOsB,QAAQ;EACnB;EACA,KAAK,IAAIE,CAAC,GAAG,CAAC,GAAGA,CAAC,EAAE,EAAE;IAClB,IAAID,SAAS,IAAIL,YAAY,IAAIM,CAAC,IAAIrB,OAAO,CAACU,aAAa,EAAE;MACzDS,QAAQ,IAAInB,OAAO,CAACD,QAAQ;MAC5B;IACJ;IACA,IAAIuB,SAAS,GAAGD,CAAC,KAAK,CAAC,GACjBE,cAAc,CAACJ,QAAQ,EAAEJ,YAAY,EAAEf,OAAO,CAACa,YAAY,EAAEb,OAAO,CAACY,WAAW,CAAC,GACjFQ,SAAS,GAAG,CAAC,GACTJ,IAAI,CAACQ,KAAK,CAACL,QAAQ,CAACb,MAAM,GAAGS,YAAY,GAAGK,SAAS,CAAC,GACtD,CAAC;IACXD,QAAQ,GAAGA,QAAQ,CAACM,MAAM,CAAC,CAAC,EAAEH,SAAS,CAAC;IACxCF,SAAS,GAAG5B,QAAQ,CAAC2B,QAAQ,EAAErB,IAAI,CAAC;EACxC;EACA,IAAIqB,QAAQ,KAAK,EAAE,EAAE;IACjBA,QAAQ,GAAGnB,OAAO,CAACc,WAAW;EAClC;EACA,OAAOK,QAAQ;AACnB;AACA,SAASI,cAAcA,CAAC3B,IAAI,EAAEmB,YAAY,EAAEF,YAAY,EAAED,WAAW,EAAE;EACnE,IAAIc,KAAK,GAAG,CAAC;EACb,IAAItB,CAAC,GAAG,CAAC;EACT,KAAK,IAAIC,GAAG,GAAGT,IAAI,CAACU,MAAM,EAAEF,CAAC,GAAGC,GAAG,IAAIqB,KAAK,GAAGX,YAAY,EAAEX,CAAC,EAAE,EAAE;IAC9D,IAAIuB,QAAQ,GAAG/B,IAAI,CAACgC,UAAU,CAACxB,CAAC,CAAC;IACjCsB,KAAK,IAAK,CAAC,IAAIC,QAAQ,IAAIA,QAAQ,IAAI,GAAG,GAAId,YAAY,GAAGD,WAAW;EAC5E;EACA,OAAOR,CAAC;AACZ;AACA,OAAO,SAASyB,cAAcA,CAACjC,IAAI,EAAEkC,KAAK,EAAE;EACxClC,IAAI,IAAI,IAAI,KAAKA,IAAI,IAAI,EAAE,CAAC;EAC5B,IAAImC,QAAQ,GAAGD,KAAK,CAACC,QAAQ;EAC7B,IAAIC,OAAO,GAAGF,KAAK,CAACE,OAAO;EAC3B,IAAIlC,IAAI,GAAGgC,KAAK,CAAChC,IAAI;EACrB,IAAImC,QAAQ,GAAGF,QAAQ,KAAK,UAAU;EACtC,IAAIG,oBAAoB,GAAG3C,aAAa,CAACO,IAAI,CAAC;EAC9C,IAAIqC,UAAU,GAAG/C,SAAS,CAAC0C,KAAK,CAACK,UAAU,EAAED,oBAAoB,CAAC;EAClE,IAAIE,YAAY,GAAG,CAAC,CAAEN,KAAK,CAACO,eAAgB;EAC5C,IAAIC,oBAAoB,GAAGR,KAAK,CAACS,YAAY,KAAK,UAAU;EAC5D,IAAIb,KAAK,GAAGI,KAAK,CAACJ,KAAK;EACvB,IAAIc,KAAK;EACT,IAAId,KAAK,IAAI,IAAI,KAAKK,QAAQ,KAAK,OAAO,IAAIA,QAAQ,KAAK,UAAU,CAAC,EAAE;IACpES,KAAK,GAAG5C,IAAI,GAAG6C,QAAQ,CAAC7C,IAAI,EAAEkC,KAAK,CAAChC,IAAI,EAAE4B,KAAK,EAAEK,QAAQ,KAAK,UAAU,EAAE,CAAC,CAAC,CAACS,KAAK,GAAG,EAAE;EAC3F,CAAC,MACI;IACDA,KAAK,GAAG5C,IAAI,GAAGA,IAAI,CAACM,KAAK,CAAC,IAAI,CAAC,GAAG,EAAE;EACxC;EACA,IAAIwC,aAAa,GAAGF,KAAK,CAAClC,MAAM,GAAG6B,UAAU;EAC7C,IAAIQ,MAAM,GAAGvD,SAAS,CAAC0C,KAAK,CAACa,MAAM,EAAED,aAAa,CAAC;EACnD,IAAIA,aAAa,GAAGC,MAAM,IAAIL,oBAAoB,EAAE;IAChD,IAAIM,SAAS,GAAG5B,IAAI,CAACQ,KAAK,CAACmB,MAAM,GAAGR,UAAU,CAAC;IAC/CK,KAAK,GAAGA,KAAK,CAACK,KAAK,CAAC,CAAC,EAAED,SAAS,CAAC;EACrC;EACA,IAAIhD,IAAI,IAAIqC,QAAQ,IAAIP,KAAK,IAAI,IAAI,EAAE;IACnC,IAAI1B,OAAO,GAAGG,sBAAsB,CAACuB,KAAK,EAAE5B,IAAI,EAAEgC,KAAK,CAAC/B,QAAQ,EAAE;MAC9DY,OAAO,EAAEmB,KAAK,CAACgB,eAAe;MAC9BhC,WAAW,EAAEgB,KAAK,CAAChB;IACvB,CAAC,CAAC;IACF,KAAK,IAAIV,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGoC,KAAK,CAAClC,MAAM,EAAEF,CAAC,EAAE,EAAE;MACnCoC,KAAK,CAACpC,CAAC,CAAC,GAAGG,kBAAkB,CAACiC,KAAK,CAACpC,CAAC,CAAC,EAAEJ,OAAO,CAAC;IACpD;EACJ;EACA,IAAI+C,WAAW,GAAGJ,MAAM;EACxB,IAAI5B,YAAY,GAAG,CAAC;EACpB,KAAK,IAAIX,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGoC,KAAK,CAAClC,MAAM,EAAEF,CAAC,EAAE,EAAE;IACnCW,YAAY,GAAGC,IAAI,CAACC,GAAG,CAACzB,QAAQ,CAACgD,KAAK,CAACpC,CAAC,CAAC,EAAEN,IAAI,CAAC,EAAEiB,YAAY,CAAC;EACnE;EACA,IAAIW,KAAK,IAAI,IAAI,EAAE;IACfA,KAAK,GAAGX,YAAY;EACxB;EACA,IAAIiC,UAAU,GAAGjC,YAAY;EAC7B,IAAIiB,OAAO,EAAE;IACTe,WAAW,IAAIf,OAAO,CAAC,CAAC,CAAC,GAAGA,OAAO,CAAC,CAAC,CAAC;IACtCgB,UAAU,IAAIhB,OAAO,CAAC,CAAC,CAAC,GAAGA,OAAO,CAAC,CAAC,CAAC;IACrCN,KAAK,IAAIM,OAAO,CAAC,CAAC,CAAC,GAAGA,OAAO,CAAC,CAAC,CAAC;EACpC;EACA,IAAII,YAAY,EAAE;IACdY,UAAU,GAAGtB,KAAK;EACtB;EACA,OAAO;IACHc,KAAK,EAAEA,KAAK;IACZG,MAAM,EAAEA,MAAM;IACdK,UAAU,EAAEA,UAAU;IACtBD,WAAW,EAAEA,WAAW;IACxBZ,UAAU,EAAEA,UAAU;IACtBD,oBAAoB,EAAEA,oBAAoB;IAC1CnB,YAAY,EAAEA,YAAY;IAC1B2B,aAAa,EAAEA,aAAa;IAC5BhB,KAAK,EAAEA;EACX,CAAC;AACL;AACA,IAAIuB,aAAa,GAAI,YAAY;EAC7B,SAASA,aAAaA,CAAA,EAAG,CACzB;EACA,OAAOA,aAAa;AACxB,CAAC,CAAC,CAAE;AACJ,IAAIC,YAAY,GAAI,YAAY;EAC5B,SAASA,YAAYA,CAACC,MAAM,EAAE;IAC1B,IAAI,CAACA,MAAM,GAAG,EAAE;IAChB,IAAIA,MAAM,EAAE;MACR,IAAI,CAACA,MAAM,GAAGA,MAAM;IACxB;EACJ;EACA,OAAOD,YAAY;AACvB,CAAC,CAAC,CAAE;AACJ,IAAIE,oBAAoB,GAAI,YAAY;EACpC,SAASA,oBAAoBA,CAAA,EAAG;IAC5B,IAAI,CAAC1B,KAAK,GAAG,CAAC;IACd,IAAI,CAACiB,MAAM,GAAG,CAAC;IACf,IAAI,CAAC5B,YAAY,GAAG,CAAC;IACrB,IAAI,CAAC2B,aAAa,GAAG,CAAC;IACtB,IAAI,CAACM,UAAU,GAAG,CAAC;IACnB,IAAI,CAACD,WAAW,GAAG,CAAC;IACpB,IAAI,CAACP,KAAK,GAAG,EAAE;EACnB;EACA,OAAOY,oBAAoB;AAC/B,CAAC,CAAC,CAAE;AACJ,SAASA,oBAAoB;AAC7B,OAAO,SAASC,aAAaA,CAACzD,IAAI,EAAEkC,KAAK,EAAE;EACvC,IAAIwB,YAAY,GAAG,IAAIF,oBAAoB,CAAC,CAAC;EAC7CxD,IAAI,IAAI,IAAI,KAAKA,IAAI,IAAI,EAAE,CAAC;EAC5B,IAAI,CAACA,IAAI,EAAE;IACP,OAAO0D,YAAY;EACvB;EACA,IAAIC,QAAQ,GAAGzB,KAAK,CAACJ,KAAK;EAC1B,IAAI8B,SAAS,GAAG1B,KAAK,CAACa,MAAM;EAC5B,IAAIZ,QAAQ,GAAGD,KAAK,CAACC,QAAQ;EAC7B,IAAI0B,QAAQ,GAAG,CAAC1B,QAAQ,KAAK,OAAO,IAAIA,QAAQ,KAAK,UAAU,KAAKwB,QAAQ,IAAI,IAAI,GAC9E;IAAE7B,KAAK,EAAE6B,QAAQ;IAAEG,UAAU,EAAE,CAAC;IAAEC,QAAQ,EAAE5B,QAAQ,KAAK;EAAW,CAAC,GACrE,IAAI;EACV,IAAI6B,SAAS,GAAGlE,SAAS,CAACkE,SAAS,GAAG,CAAC;EACvC,IAAIC,MAAM;EACV,OAAO,CAACA,MAAM,GAAGnE,SAAS,CAACoE,IAAI,CAAClE,IAAI,CAAC,KAAK,IAAI,EAAE;IAC5C,IAAImE,YAAY,GAAGF,MAAM,CAACG,KAAK;IAC/B,IAAID,YAAY,GAAGH,SAAS,EAAE;MAC1BK,UAAU,CAACX,YAAY,EAAE1D,IAAI,CAACsE,SAAS,CAACN,SAAS,EAAEG,YAAY,CAAC,EAAEjC,KAAK,EAAE2B,QAAQ,CAAC;IACtF;IACAQ,UAAU,CAACX,YAAY,EAAEO,MAAM,CAAC,CAAC,CAAC,EAAE/B,KAAK,EAAE2B,QAAQ,EAAEI,MAAM,CAAC,CAAC,CAAC,CAAC;IAC/DD,SAAS,GAAGlE,SAAS,CAACkE,SAAS;EACnC;EACA,IAAIA,SAAS,GAAGhE,IAAI,CAACU,MAAM,EAAE;IACzB2D,UAAU,CAACX,YAAY,EAAE1D,IAAI,CAACsE,SAAS,CAACN,SAAS,EAAEhE,IAAI,CAACU,MAAM,CAAC,EAAEwB,KAAK,EAAE2B,QAAQ,CAAC;EACrF;EACA,IAAIU,WAAW,GAAG,EAAE;EACpB,IAAIC,gBAAgB,GAAG,CAAC;EACxB,IAAIC,eAAe,GAAG,CAAC;EACvB,IAAIC,UAAU,GAAGxC,KAAK,CAACE,OAAO;EAC9B,IAAIC,QAAQ,GAAGF,QAAQ,KAAK,UAAU;EACtC,IAAIwC,YAAY,GAAGzC,KAAK,CAACS,YAAY,KAAK,UAAU;EACpD,SAASiC,UAAUA,CAACC,IAAI,EAAErD,SAAS,EAAEe,UAAU,EAAE;IAC7CsC,IAAI,CAAC/C,KAAK,GAAGN,SAAS;IACtBqD,IAAI,CAACtC,UAAU,GAAGA,UAAU;IAC5BiC,gBAAgB,IAAIjC,UAAU;IAC9BkC,eAAe,GAAGrD,IAAI,CAACC,GAAG,CAACoD,eAAe,EAAEjD,SAAS,CAAC;EAC1D;EACAsD,KAAK,EAAE,KAAK,IAAItE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGkD,YAAY,CAACd,KAAK,CAAClC,MAAM,EAAEF,CAAC,EAAE,EAAE;IACvD,IAAIqE,IAAI,GAAGnB,YAAY,CAACd,KAAK,CAACpC,CAAC,CAAC;IAChC,IAAI+B,UAAU,GAAG,CAAC;IAClB,IAAIf,SAAS,GAAG,CAAC;IACjB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGoD,IAAI,CAACtB,MAAM,CAAC7C,MAAM,EAAEe,CAAC,EAAE,EAAE;MACzC,IAAIsD,KAAK,GAAGF,IAAI,CAACtB,MAAM,CAAC9B,CAAC,CAAC;MAC1B,IAAIuD,UAAU,GAAGD,KAAK,CAACE,SAAS,IAAI/C,KAAK,CAACgD,IAAI,CAACH,KAAK,CAACE,SAAS,CAAC,IAAI,CAAC,CAAC;MACrE,IAAIE,WAAW,GAAGJ,KAAK,CAACI,WAAW,GAAGH,UAAU,CAAC5C,OAAO;MACxD,IAAIgD,QAAQ,GAAGD,WAAW,GAAGA,WAAW,CAAC,CAAC,CAAC,GAAGA,WAAW,CAAC,CAAC,CAAC,GAAG,CAAC;MAChE,IAAIjF,IAAI,GAAG6E,KAAK,CAAC7E,IAAI,GAAG8E,UAAU,CAAC9E,IAAI,IAAIgC,KAAK,CAAChC,IAAI;MACrD6E,KAAK,CAACjC,aAAa,GAAGnD,aAAa,CAACO,IAAI,CAAC;MACzC,IAAImF,WAAW,GAAG7F,SAAS,CAACwF,UAAU,CAACjC,MAAM,EAAEgC,KAAK,CAACjC,aAAa,CAAC;MACnEiC,KAAK,CAACO,WAAW,GAAGD,WAAW;MAC/BF,WAAW,KAAKE,WAAW,IAAIF,WAAW,CAAC,CAAC,CAAC,GAAGA,WAAW,CAAC,CAAC,CAAC,CAAC;MAC/DJ,KAAK,CAAChC,MAAM,GAAGsC,WAAW;MAC1BN,KAAK,CAACxC,UAAU,GAAG9C,SAAS,CAACuF,UAAU,CAACzC,UAAU,EAAEL,KAAK,CAACK,UAAU,EAAE8C,WAAW,CAAC;MAClFN,KAAK,CAACQ,KAAK,GAAGP,UAAU,IAAIA,UAAU,CAACO,KAAK,IAAIrD,KAAK,CAACqD,KAAK;MAC3DR,KAAK,CAACS,aAAa,GAAGR,UAAU,IAAIA,UAAU,CAACQ,aAAa,IAAI,QAAQ;MACxE,IAAIb,YAAY,IAAIf,SAAS,IAAI,IAAI,IAAIY,gBAAgB,GAAGO,KAAK,CAACxC,UAAU,GAAGqB,SAAS,EAAE;QACtF,IAAInC,CAAC,GAAG,CAAC,EAAE;UACPoD,IAAI,CAACtB,MAAM,GAAGsB,IAAI,CAACtB,MAAM,CAACN,KAAK,CAAC,CAAC,EAAExB,CAAC,CAAC;UACrCmD,UAAU,CAACC,IAAI,EAAErD,SAAS,EAAEe,UAAU,CAAC;UACvCmB,YAAY,CAACd,KAAK,GAAGc,YAAY,CAACd,KAAK,CAACK,KAAK,CAAC,CAAC,EAAEzC,CAAC,GAAG,CAAC,CAAC;QAC3D,CAAC,MACI;UACDkD,YAAY,CAACd,KAAK,GAAGc,YAAY,CAACd,KAAK,CAACK,KAAK,CAAC,CAAC,EAAEzC,CAAC,CAAC;QACvD;QACA,MAAMsE,KAAK;MACf;MACA,IAAIW,eAAe,GAAGT,UAAU,CAAClD,KAAK;MACtC,IAAI4D,sBAAsB,GAAGD,eAAe,IAAI,IAAI,IAAIA,eAAe,KAAK,MAAM;MAClF,IAAI,OAAOA,eAAe,KAAK,QAAQ,IAAIA,eAAe,CAACE,MAAM,CAACF,eAAe,CAAC/E,MAAM,GAAG,CAAC,CAAC,KAAK,GAAG,EAAE;QACnGqE,KAAK,CAACa,YAAY,GAAGH,eAAe;QACpClB,WAAW,CAACsB,IAAI,CAACd,KAAK,CAAC;QACvBA,KAAK,CAAC5D,YAAY,GAAGvB,QAAQ,CAACmF,KAAK,CAAC/E,IAAI,EAAEE,IAAI,CAAC;MACnD,CAAC,MACI;QACD,IAAIwF,sBAAsB,EAAE;UACxB,IAAII,mBAAmB,GAAGd,UAAU,CAACvC,eAAe;UACpD,IAAIsD,KAAK,GAAGD,mBAAmB,IAAIA,mBAAmB,CAACE,KAAK;UAC5D,IAAID,KAAK,EAAE;YACPA,KAAK,GAAGzG,WAAW,CAAC2G,cAAc,CAACF,KAAK,CAAC;YACzC,IAAIzG,WAAW,CAAC4G,YAAY,CAACH,KAAK,CAAC,EAAE;cACjChB,KAAK,CAACjD,KAAK,GAAGV,IAAI,CAACC,GAAG,CAAC0D,KAAK,CAACjD,KAAK,EAAEiE,KAAK,CAACjE,KAAK,GAAGuD,WAAW,GAAGU,KAAK,CAAChD,MAAM,CAAC;YACjF;UACJ;QACJ;QACA,IAAIoD,gBAAgB,GAAG9D,QAAQ,IAAIsB,QAAQ,IAAI,IAAI,GAC7CA,QAAQ,GAAGnC,SAAS,GAAG,IAAI;QACjC,IAAI2E,gBAAgB,IAAI,IAAI,IAAIA,gBAAgB,GAAGpB,KAAK,CAACjD,KAAK,EAAE;UAC5D,IAAI,CAAC4D,sBAAsB,IAAIS,gBAAgB,GAAGf,QAAQ,EAAE;YACxDL,KAAK,CAAC/E,IAAI,GAAG,EAAE;YACf+E,KAAK,CAACjD,KAAK,GAAGiD,KAAK,CAAC5D,YAAY,GAAG,CAAC;UACxC,CAAC,MACI;YACD4D,KAAK,CAAC/E,IAAI,GAAGD,YAAY,CAACgF,KAAK,CAAC/E,IAAI,EAAEmG,gBAAgB,GAAGf,QAAQ,EAAElF,IAAI,EAAEgC,KAAK,CAAC/B,QAAQ,EAAE;cAAEY,OAAO,EAAEmB,KAAK,CAACgB;YAAgB,CAAC,CAAC;YAC5H6B,KAAK,CAACjD,KAAK,GAAGiD,KAAK,CAAC5D,YAAY,GAAGvB,QAAQ,CAACmF,KAAK,CAAC/E,IAAI,EAAEE,IAAI,CAAC;UACjE;QACJ,CAAC,MACI;UACD6E,KAAK,CAAC5D,YAAY,GAAGvB,QAAQ,CAACmF,KAAK,CAAC/E,IAAI,EAAEE,IAAI,CAAC;QACnD;MACJ;MACA6E,KAAK,CAACjD,KAAK,IAAIsD,QAAQ;MACvB5D,SAAS,IAAIuD,KAAK,CAACjD,KAAK;MACxBkD,UAAU,KAAKzC,UAAU,GAAGnB,IAAI,CAACC,GAAG,CAACkB,UAAU,EAAEwC,KAAK,CAACxC,UAAU,CAAC,CAAC;IACvE;IACAqC,UAAU,CAACC,IAAI,EAAErD,SAAS,EAAEe,UAAU,CAAC;EAC3C;EACAmB,YAAY,CAACN,UAAU,GAAGM,YAAY,CAAC5B,KAAK,GAAGtC,SAAS,CAACmE,QAAQ,EAAEc,eAAe,CAAC;EACnFf,YAAY,CAACP,WAAW,GAAGO,YAAY,CAACX,MAAM,GAAGvD,SAAS,CAACoE,SAAS,EAAEY,gBAAgB,CAAC;EACvFd,YAAY,CAACZ,aAAa,GAAG0B,gBAAgB;EAC7Cd,YAAY,CAACvC,YAAY,GAAGsD,eAAe;EAC3C,IAAIC,UAAU,EAAE;IACZhB,YAAY,CAACN,UAAU,IAAIsB,UAAU,CAAC,CAAC,CAAC,GAAGA,UAAU,CAAC,CAAC,CAAC;IACxDhB,YAAY,CAACP,WAAW,IAAIuB,UAAU,CAAC,CAAC,CAAC,GAAGA,UAAU,CAAC,CAAC,CAAC;EAC7D;EACA,KAAK,IAAIlE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG+D,WAAW,CAAC7D,MAAM,EAAEF,CAAC,EAAE,EAAE;IACzC,IAAIuE,KAAK,GAAGR,WAAW,CAAC/D,CAAC,CAAC;IAC1B,IAAIoF,YAAY,GAAGb,KAAK,CAACa,YAAY;IACrCb,KAAK,CAACjD,KAAK,GAAGsE,QAAQ,CAACR,YAAY,EAAE,EAAE,CAAC,GAAG,GAAG,GAAGlC,YAAY,CAAC5B,KAAK;EACvE;EACA,OAAO4B,YAAY;AACvB;AACA,SAASW,UAAUA,CAACgC,KAAK,EAAEC,GAAG,EAAEpE,KAAK,EAAE2B,QAAQ,EAAEoB,SAAS,EAAE;EACxD,IAAIsB,UAAU,GAAGD,GAAG,KAAK,EAAE;EAC3B,IAAItB,UAAU,GAAGC,SAAS,IAAI/C,KAAK,CAACgD,IAAI,CAACD,SAAS,CAAC,IAAI,CAAC,CAAC;EACzD,IAAIrC,KAAK,GAAGyD,KAAK,CAACzD,KAAK;EACvB,IAAI1C,IAAI,GAAG8E,UAAU,CAAC9E,IAAI,IAAIgC,KAAK,CAAChC,IAAI;EACxC,IAAIsG,OAAO,GAAG,KAAK;EACnB,IAAIC,QAAQ;EACZ,IAAIC,WAAW;EACf,IAAI7C,QAAQ,EAAE;IACV,IAAI8C,YAAY,GAAG3B,UAAU,CAAC5C,OAAO;IACrC,IAAIwE,aAAa,GAAGD,YAAY,GAAGA,YAAY,CAAC,CAAC,CAAC,GAAGA,YAAY,CAAC,CAAC,CAAC,GAAG,CAAC;IACxE,IAAI3B,UAAU,CAAClD,KAAK,IAAI,IAAI,IAAIkD,UAAU,CAAClD,KAAK,KAAK,MAAM,EAAE;MACzD,IAAI+E,YAAY,GAAGhH,YAAY,CAACmF,UAAU,CAAClD,KAAK,EAAE+B,QAAQ,CAAC/B,KAAK,CAAC,GAAG8E,aAAa;MACjF,IAAIhE,KAAK,CAAClC,MAAM,GAAG,CAAC,EAAE;QAClB,IAAImG,YAAY,GAAGhD,QAAQ,CAACC,UAAU,GAAGD,QAAQ,CAAC/B,KAAK,EAAE;UACrD2E,QAAQ,GAAGH,GAAG,CAAChG,KAAK,CAAC,IAAI,CAAC;UAC1BkG,OAAO,GAAG,IAAI;QAClB;MACJ;MACA3C,QAAQ,CAACC,UAAU,GAAG+C,YAAY;IACtC,CAAC,MACI;MACD,IAAIC,GAAG,GAAGjE,QAAQ,CAACyD,GAAG,EAAEpG,IAAI,EAAE2D,QAAQ,CAAC/B,KAAK,EAAE+B,QAAQ,CAACE,QAAQ,EAAEF,QAAQ,CAACC,UAAU,CAAC;MACrFD,QAAQ,CAACC,UAAU,GAAGgD,GAAG,CAAChD,UAAU,GAAG8C,aAAa;MACpDF,WAAW,GAAGI,GAAG,CAACJ,WAAW;MAC7BD,QAAQ,GAAGK,GAAG,CAAClE,KAAK;IACxB;EACJ,CAAC,MACI;IACD6D,QAAQ,GAAGH,GAAG,CAAChG,KAAK,CAAC,IAAI,CAAC;EAC9B;EACA,KAAK,IAAIE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGiG,QAAQ,CAAC/F,MAAM,EAAEF,CAAC,EAAE,EAAE;IACtC,IAAIR,IAAI,GAAGyG,QAAQ,CAACjG,CAAC,CAAC;IACtB,IAAIuE,KAAK,GAAG,IAAI1B,aAAa,CAAC,CAAC;IAC/B0B,KAAK,CAACE,SAAS,GAAGA,SAAS;IAC3BF,KAAK,CAAC/E,IAAI,GAAGA,IAAI;IACjB+E,KAAK,CAACgC,YAAY,GAAG,CAAC/G,IAAI,IAAI,CAACuG,UAAU;IACzC,IAAI,OAAOvB,UAAU,CAAClD,KAAK,KAAK,QAAQ,EAAE;MACtCiD,KAAK,CAACjD,KAAK,GAAGkD,UAAU,CAAClD,KAAK;IAClC,CAAC,MACI;MACDiD,KAAK,CAACjD,KAAK,GAAG4E,WAAW,GACnBA,WAAW,CAAClG,CAAC,CAAC,GACdZ,QAAQ,CAACI,IAAI,EAAEE,IAAI,CAAC;IAC9B;IACA,IAAI,CAACM,CAAC,IAAI,CAACgG,OAAO,EAAE;MAChB,IAAIjD,MAAM,GAAG,CAACX,KAAK,CAACA,KAAK,CAAClC,MAAM,GAAG,CAAC,CAAC,KAAKkC,KAAK,CAAC,CAAC,CAAC,GAAG,IAAIU,YAAY,CAAC,CAAC,CAAC,EAAEC,MAAM;MAChF,IAAIyD,SAAS,GAAGzD,MAAM,CAAC7C,MAAM;MAC5BsG,SAAS,KAAK,CAAC,IAAIzD,MAAM,CAAC,CAAC,CAAC,CAACwD,YAAY,GACnCxD,MAAM,CAAC,CAAC,CAAC,GAAGwB,KAAK,GACjB,CAAC/E,IAAI,IAAI,CAACgH,SAAS,IAAIT,UAAU,KAAKhD,MAAM,CAACsC,IAAI,CAACd,KAAK,CAAE;IACpE,CAAC,MACI;MACDnC,KAAK,CAACiD,IAAI,CAAC,IAAIvC,YAAY,CAAC,CAACyB,KAAK,CAAC,CAAC,CAAC;IACzC;EACJ;AACJ;AACA,SAASkC,kBAAkBA,CAACC,EAAE,EAAE;EAC5B,IAAIC,IAAI,GAAGD,EAAE,CAAClF,UAAU,CAAC,CAAC,CAAC;EAC3B,OAAOmF,IAAI,IAAI,IAAI,IAAIA,IAAI,IAAI,KAAK,IAC7BA,IAAI,IAAI,KAAK,IAAIA,IAAI,IAAI,MAAM,IAC/BA,IAAI,IAAI,MAAM,IAAIA,IAAI,IAAI,MAAM,IAChCA,IAAI,IAAI,MAAM,IAAIA,IAAI,IAAI,MAAM;AAC3C;AACA,IAAIC,YAAY,GAAG1H,MAAM,CAAC,SAAS,CAACY,KAAK,CAAC,EAAE,CAAC,EAAE,UAAU+G,GAAG,EAAEH,EAAE,EAAE;EAC9DG,GAAG,CAACH,EAAE,CAAC,GAAG,IAAI;EACd,OAAOG,GAAG;AACd,CAAC,EAAE,CAAC,CAAC,CAAC;AACN,SAASC,eAAeA,CAACJ,EAAE,EAAE;EACzB,IAAID,kBAAkB,CAACC,EAAE,CAAC,EAAE;IACxB,IAAIE,YAAY,CAACF,EAAE,CAAC,EAAE;MAClB,OAAO,IAAI;IACf;IACA,OAAO,KAAK;EAChB;EACA,OAAO,IAAI;AACf;AACA,SAASrE,QAAQA,CAAC7C,IAAI,EAAEE,IAAI,EAAEsB,SAAS,EAAE+F,UAAU,EAAEC,cAAc,EAAE;EACjE,IAAI5E,KAAK,GAAG,EAAE;EACd,IAAI8D,WAAW,GAAG,EAAE;EACpB,IAAI7B,IAAI,GAAG,EAAE;EACb,IAAI4C,WAAW,GAAG,EAAE;EACpB,IAAIC,gBAAgB,GAAG,CAAC;EACxB,IAAI5D,UAAU,GAAG,CAAC;EAClB,KAAK,IAAItD,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGR,IAAI,CAACU,MAAM,EAAEF,CAAC,EAAE,EAAE;IAClC,IAAI0G,EAAE,GAAGlH,IAAI,CAAC2F,MAAM,CAACnF,CAAC,CAAC;IACvB,IAAI0G,EAAE,KAAK,IAAI,EAAE;MACb,IAAIO,WAAW,EAAE;QACb5C,IAAI,IAAI4C,WAAW;QACnB3D,UAAU,IAAI4D,gBAAgB;MAClC;MACA9E,KAAK,CAACiD,IAAI,CAAChB,IAAI,CAAC;MAChB6B,WAAW,CAACb,IAAI,CAAC/B,UAAU,CAAC;MAC5Be,IAAI,GAAG,EAAE;MACT4C,WAAW,GAAG,EAAE;MAChBC,gBAAgB,GAAG,CAAC;MACpB5D,UAAU,GAAG,CAAC;MACd;IACJ;IACA,IAAI6D,OAAO,GAAG/H,QAAQ,CAACsH,EAAE,EAAEhH,IAAI,CAAC;IAChC,IAAI0H,MAAM,GAAGL,UAAU,GAAG,KAAK,GAAG,CAACD,eAAe,CAACJ,EAAE,CAAC;IACtD,IAAI,CAACtE,KAAK,CAAClC,MAAM,GACX8G,cAAc,GAAG1D,UAAU,GAAG6D,OAAO,GAAGnG,SAAS,GACjDsC,UAAU,GAAG6D,OAAO,GAAGnG,SAAS,EAAE;MACpC,IAAI,CAACsC,UAAU,EAAE;QACb,IAAI8D,MAAM,EAAE;UACRhF,KAAK,CAACiD,IAAI,CAAC4B,WAAW,CAAC;UACvBf,WAAW,CAACb,IAAI,CAAC6B,gBAAgB,CAAC;UAClCD,WAAW,GAAGP,EAAE;UAChBQ,gBAAgB,GAAGC,OAAO;QAC9B,CAAC,MACI;UACD/E,KAAK,CAACiD,IAAI,CAACqB,EAAE,CAAC;UACdR,WAAW,CAACb,IAAI,CAAC8B,OAAO,CAAC;QAC7B;MACJ,CAAC,MACI,IAAI9C,IAAI,IAAI4C,WAAW,EAAE;QAC1B,IAAIG,MAAM,EAAE;UACR,IAAI,CAAC/C,IAAI,EAAE;YACPA,IAAI,GAAG4C,WAAW;YAClBA,WAAW,GAAG,EAAE;YAChBC,gBAAgB,GAAG,CAAC;YACpB5D,UAAU,GAAG4D,gBAAgB;UACjC;UACA9E,KAAK,CAACiD,IAAI,CAAChB,IAAI,CAAC;UAChB6B,WAAW,CAACb,IAAI,CAAC/B,UAAU,GAAG4D,gBAAgB,CAAC;UAC/CD,WAAW,IAAIP,EAAE;UACjBQ,gBAAgB,IAAIC,OAAO;UAC3B9C,IAAI,GAAG,EAAE;UACTf,UAAU,GAAG4D,gBAAgB;QACjC,CAAC,MACI;UACD,IAAID,WAAW,EAAE;YACb5C,IAAI,IAAI4C,WAAW;YACnBA,WAAW,GAAG,EAAE;YAChBC,gBAAgB,GAAG,CAAC;UACxB;UACA9E,KAAK,CAACiD,IAAI,CAAChB,IAAI,CAAC;UAChB6B,WAAW,CAACb,IAAI,CAAC/B,UAAU,CAAC;UAC5Be,IAAI,GAAGqC,EAAE;UACTpD,UAAU,GAAG6D,OAAO;QACxB;MACJ;MACA;IACJ;IACA7D,UAAU,IAAI6D,OAAO;IACrB,IAAIC,MAAM,EAAE;MACRH,WAAW,IAAIP,EAAE;MACjBQ,gBAAgB,IAAIC,OAAO;IAC/B,CAAC,MACI;MACD,IAAIF,WAAW,EAAE;QACb5C,IAAI,IAAI4C,WAAW;QACnBA,WAAW,GAAG,EAAE;QAChBC,gBAAgB,GAAG,CAAC;MACxB;MACA7C,IAAI,IAAIqC,EAAE;IACd;EACJ;EACA,IAAI,CAACtE,KAAK,CAAClC,MAAM,IAAI,CAACmE,IAAI,EAAE;IACxBA,IAAI,GAAG7E,IAAI;IACXyH,WAAW,GAAG,EAAE;IAChBC,gBAAgB,GAAG,CAAC;EACxB;EACA,IAAID,WAAW,EAAE;IACb5C,IAAI,IAAI4C,WAAW;EACvB;EACA,IAAI5C,IAAI,EAAE;IACNjC,KAAK,CAACiD,IAAI,CAAChB,IAAI,CAAC;IAChB6B,WAAW,CAACb,IAAI,CAAC/B,UAAU,CAAC;EAChC;EACA,IAAIlB,KAAK,CAAClC,MAAM,KAAK,CAAC,EAAE;IACpBoD,UAAU,IAAI0D,cAAc;EAChC;EACA,OAAO;IACH1D,UAAU,EAAEA,UAAU;IACtBlB,KAAK,EAAEA,KAAK;IACZ8D,WAAW,EAAEA;EACjB,CAAC;AACL"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}