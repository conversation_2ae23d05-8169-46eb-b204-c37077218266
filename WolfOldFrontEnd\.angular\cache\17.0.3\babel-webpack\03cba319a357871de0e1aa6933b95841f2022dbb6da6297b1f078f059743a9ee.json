{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport RoamController from './RoamController.js';\nimport * as roamHelper from '../../component/helper/roamHelper.js';\nimport { onIrrelevantElement } from '../../component/helper/cursorHelper.js';\nimport * as graphic from '../../util/graphic.js';\nimport { toggleHoverEmphasis, enableComponentHighDownFeatures, setDefaultStateProxy } from '../../util/states.js';\nimport geoSourceManager from '../../coord/geo/geoSourceManager.js';\nimport { getUID } from '../../util/component.js';\nimport { setLabelStyle, getLabelStatesModels } from '../../label/labelStyle.js';\nimport { getECData } from '../../util/innerStore.js';\nimport { createOrUpdatePatternFromDecal } from '../../util/decal.js';\nimport Displayable from 'zrender/lib/graphic/Displayable.js';\nimport { makeInner } from '../../util/model.js';\n/**\r\n * Only these tags enable use `itemStyle` if they are named in SVG.\r\n * Other tags like <text> <tspan> <image> might not suitable for `itemStyle`.\r\n * They will not be considered to be styled until some requirements come.\r\n */\n\nvar OPTION_STYLE_ENABLED_TAGS = ['rect', 'circle', 'line', 'ellipse', 'polygon', 'polyline', 'path'];\nvar OPTION_STYLE_ENABLED_TAG_MAP = zrUtil.createHashMap(OPTION_STYLE_ENABLED_TAGS);\nvar STATE_TRIGGER_TAG_MAP = zrUtil.createHashMap(OPTION_STYLE_ENABLED_TAGS.concat(['g']));\nvar LABEL_HOST_MAP = zrUtil.createHashMap(OPTION_STYLE_ENABLED_TAGS.concat(['g']));\nvar mapLabelRaw = makeInner();\nfunction getFixedItemStyle(model) {\n  var itemStyle = model.getItemStyle();\n  var areaColor = model.get('areaColor'); // If user want the color not to be changed when hover,\n  // they should both set areaColor and color to be null.\n\n  if (areaColor != null) {\n    itemStyle.fill = areaColor;\n  }\n  return itemStyle;\n} // Only stroke can be used for line.\n// Using fill in style if stroke not exits.\n// TODO Not sure yet. Perhaps a separate `lineStyle`?\n\nfunction fixLineStyle(styleHost) {\n  var style = styleHost.style;\n  if (style) {\n    style.stroke = style.stroke || style.fill;\n    style.fill = null;\n  }\n}\nvar MapDraw = /** @class */\nfunction () {\n  function MapDraw(api) {\n    var group = new graphic.Group();\n    this.uid = getUID('ec_map_draw');\n    this._controller = new RoamController(api.getZr());\n    this._controllerHost = {\n      target: group\n    };\n    this.group = group;\n    group.add(this._regionsGroup = new graphic.Group());\n    group.add(this._svgGroup = new graphic.Group());\n  }\n  MapDraw.prototype.draw = function (mapOrGeoModel, ecModel, api, fromView, payload) {\n    var isGeo = mapOrGeoModel.mainType === 'geo'; // Map series has data. GEO model that controlled by map series\n    // will be assigned with map data. Other GEO model has no data.\n\n    var data = mapOrGeoModel.getData && mapOrGeoModel.getData();\n    isGeo && ecModel.eachComponent({\n      mainType: 'series',\n      subType: 'map'\n    }, function (mapSeries) {\n      if (!data && mapSeries.getHostGeoModel() === mapOrGeoModel) {\n        data = mapSeries.getData();\n      }\n    });\n    var geo = mapOrGeoModel.coordinateSystem;\n    var regionsGroup = this._regionsGroup;\n    var group = this.group;\n    var transformInfo = geo.getTransformInfo();\n    var transformInfoRaw = transformInfo.raw;\n    var transformInfoRoam = transformInfo.roam; // No animation when first draw or in action\n\n    var isFirstDraw = !regionsGroup.childAt(0) || payload;\n    if (isFirstDraw) {\n      group.x = transformInfoRoam.x;\n      group.y = transformInfoRoam.y;\n      group.scaleX = transformInfoRoam.scaleX;\n      group.scaleY = transformInfoRoam.scaleY;\n      group.dirty();\n    } else {\n      graphic.updateProps(group, transformInfoRoam, mapOrGeoModel);\n    }\n    var isVisualEncodedByVisualMap = data && data.getVisual('visualMeta') && data.getVisual('visualMeta').length > 0;\n    var viewBuildCtx = {\n      api: api,\n      geo: geo,\n      mapOrGeoModel: mapOrGeoModel,\n      data: data,\n      isVisualEncodedByVisualMap: isVisualEncodedByVisualMap,\n      isGeo: isGeo,\n      transformInfoRaw: transformInfoRaw\n    };\n    if (geo.resourceType === 'geoJSON') {\n      this._buildGeoJSON(viewBuildCtx);\n    } else if (geo.resourceType === 'geoSVG') {\n      this._buildSVG(viewBuildCtx);\n    }\n    this._updateController(mapOrGeoModel, ecModel, api);\n    this._updateMapSelectHandler(mapOrGeoModel, regionsGroup, api, fromView);\n  };\n  MapDraw.prototype._buildGeoJSON = function (viewBuildCtx) {\n    var regionsGroupByName = this._regionsGroupByName = zrUtil.createHashMap();\n    var regionsInfoByName = zrUtil.createHashMap();\n    var regionsGroup = this._regionsGroup;\n    var transformInfoRaw = viewBuildCtx.transformInfoRaw;\n    var mapOrGeoModel = viewBuildCtx.mapOrGeoModel;\n    var data = viewBuildCtx.data;\n    var projection = viewBuildCtx.geo.projection;\n    var projectionStream = projection && projection.stream;\n    function transformPoint(point, project) {\n      if (project) {\n        // projection may return null point.\n        point = project(point);\n      }\n      return point && [point[0] * transformInfoRaw.scaleX + transformInfoRaw.x, point[1] * transformInfoRaw.scaleY + transformInfoRaw.y];\n    }\n    ;\n    function transformPolygonPoints(inPoints) {\n      var outPoints = []; // If projectionStream is provided. Use it instead of single point project.\n\n      var project = !projectionStream && projection && projection.project;\n      for (var i = 0; i < inPoints.length; ++i) {\n        var newPt = transformPoint(inPoints[i], project);\n        newPt && outPoints.push(newPt);\n      }\n      return outPoints;\n    }\n    function getPolyShape(points) {\n      return {\n        shape: {\n          points: transformPolygonPoints(points)\n        }\n      };\n    }\n    regionsGroup.removeAll(); // Only when the resource is GeoJSON, there is `geo.regions`.\n\n    zrUtil.each(viewBuildCtx.geo.regions, function (region) {\n      var regionName = region.name; // Consider in GeoJson properties.name may be duplicated, for example,\n      // there is multiple region named \"United Kindom\" or \"France\" (so many\n      // colonies). And it is not appropriate to merge them in geo, which\n      // will make them share the same label and bring trouble in label\n      // location calculation.\n\n      var regionGroup = regionsGroupByName.get(regionName);\n      var _a = regionsInfoByName.get(regionName) || {},\n        dataIdx = _a.dataIdx,\n        regionModel = _a.regionModel;\n      if (!regionGroup) {\n        regionGroup = regionsGroupByName.set(regionName, new graphic.Group());\n        regionsGroup.add(regionGroup);\n        dataIdx = data ? data.indexOfName(regionName) : null;\n        regionModel = viewBuildCtx.isGeo ? mapOrGeoModel.getRegionModel(regionName) : data ? data.getItemModel(dataIdx) : null;\n        regionsInfoByName.set(regionName, {\n          dataIdx: dataIdx,\n          regionModel: regionModel\n        });\n      }\n      var polygonSubpaths = [];\n      var polylineSubpaths = [];\n      zrUtil.each(region.geometries, function (geometry) {\n        // Polygon and MultiPolygon\n        if (geometry.type === 'polygon') {\n          var polys = [geometry.exterior].concat(geometry.interiors || []);\n          if (projectionStream) {\n            polys = projectPolys(polys, projectionStream);\n          }\n          zrUtil.each(polys, function (poly) {\n            polygonSubpaths.push(new graphic.Polygon(getPolyShape(poly)));\n          });\n        } // LineString and MultiLineString\n        else {\n          var points = geometry.points;\n          if (projectionStream) {\n            points = projectPolys(points, projectionStream, true);\n          }\n          zrUtil.each(points, function (points) {\n            polylineSubpaths.push(new graphic.Polyline(getPolyShape(points)));\n          });\n        }\n      });\n      var centerPt = transformPoint(region.getCenter(), projection && projection.project);\n      function createCompoundPath(subpaths, isLine) {\n        if (!subpaths.length) {\n          return;\n        }\n        var compoundPath = new graphic.CompoundPath({\n          culling: true,\n          segmentIgnoreThreshold: 1,\n          shape: {\n            paths: subpaths\n          }\n        });\n        regionGroup.add(compoundPath);\n        applyOptionStyleForRegion(viewBuildCtx, compoundPath, dataIdx, regionModel);\n        resetLabelForRegion(viewBuildCtx, compoundPath, regionName, regionModel, mapOrGeoModel, dataIdx, centerPt);\n        if (isLine) {\n          fixLineStyle(compoundPath);\n          zrUtil.each(compoundPath.states, fixLineStyle);\n        }\n      }\n      createCompoundPath(polygonSubpaths);\n      createCompoundPath(polylineSubpaths, true);\n    }); // Ensure children have been added to `regionGroup` before calling them.\n\n    regionsGroupByName.each(function (regionGroup, regionName) {\n      var _a = regionsInfoByName.get(regionName),\n        dataIdx = _a.dataIdx,\n        regionModel = _a.regionModel;\n      resetEventTriggerForRegion(viewBuildCtx, regionGroup, regionName, regionModel, mapOrGeoModel, dataIdx);\n      resetTooltipForRegion(viewBuildCtx, regionGroup, regionName, regionModel, mapOrGeoModel);\n      resetStateTriggerForRegion(viewBuildCtx, regionGroup, regionName, regionModel, mapOrGeoModel);\n    }, this);\n  };\n  MapDraw.prototype._buildSVG = function (viewBuildCtx) {\n    var mapName = viewBuildCtx.geo.map;\n    var transformInfoRaw = viewBuildCtx.transformInfoRaw;\n    this._svgGroup.x = transformInfoRaw.x;\n    this._svgGroup.y = transformInfoRaw.y;\n    this._svgGroup.scaleX = transformInfoRaw.scaleX;\n    this._svgGroup.scaleY = transformInfoRaw.scaleY;\n    if (this._svgResourceChanged(mapName)) {\n      this._freeSVG();\n      this._useSVG(mapName);\n    }\n    var svgDispatcherMap = this._svgDispatcherMap = zrUtil.createHashMap();\n    var focusSelf = false;\n    zrUtil.each(this._svgGraphicRecord.named, function (namedItem) {\n      // Note that we also allow different elements have the same name.\n      // For example, a glyph of a city and the label of the city have\n      // the same name and their tooltip info can be defined in a single\n      // region option.\n      var regionName = namedItem.name;\n      var mapOrGeoModel = viewBuildCtx.mapOrGeoModel;\n      var data = viewBuildCtx.data;\n      var svgNodeTagLower = namedItem.svgNodeTagLower;\n      var el = namedItem.el;\n      var dataIdx = data ? data.indexOfName(regionName) : null;\n      var regionModel = mapOrGeoModel.getRegionModel(regionName);\n      if (OPTION_STYLE_ENABLED_TAG_MAP.get(svgNodeTagLower) != null && el instanceof Displayable) {\n        applyOptionStyleForRegion(viewBuildCtx, el, dataIdx, regionModel);\n      }\n      if (el instanceof Displayable) {\n        el.culling = true;\n      } // We do not know how the SVG like so we'd better not to change z2.\n      // Otherwise it might bring some unexpected result. For example,\n      // an area hovered that make some inner city can not be clicked.\n\n      el.z2EmphasisLift = 0; // If self named:\n\n      if (!namedItem.namedFrom) {\n        // label should batter to be displayed based on the center of <g>\n        // if it is named rather than displayed on each child.\n        if (LABEL_HOST_MAP.get(svgNodeTagLower) != null) {\n          resetLabelForRegion(viewBuildCtx, el, regionName, regionModel, mapOrGeoModel, dataIdx, null);\n        }\n        resetEventTriggerForRegion(viewBuildCtx, el, regionName, regionModel, mapOrGeoModel, dataIdx);\n        resetTooltipForRegion(viewBuildCtx, el, regionName, regionModel, mapOrGeoModel);\n        if (STATE_TRIGGER_TAG_MAP.get(svgNodeTagLower) != null) {\n          var focus_1 = resetStateTriggerForRegion(viewBuildCtx, el, regionName, regionModel, mapOrGeoModel);\n          if (focus_1 === 'self') {\n            focusSelf = true;\n          }\n          var els = svgDispatcherMap.get(regionName) || svgDispatcherMap.set(regionName, []);\n          els.push(el);\n        }\n      }\n    }, this);\n    this._enableBlurEntireSVG(focusSelf, viewBuildCtx);\n  };\n  MapDraw.prototype._enableBlurEntireSVG = function (focusSelf, viewBuildCtx) {\n    // It's a little complicated to support blurring the entire geoSVG in series-map.\n    // So do not support it until some requirements come.\n    // At present, in series-map, only regions can be blurred.\n    if (focusSelf && viewBuildCtx.isGeo) {\n      var blurStyle = viewBuildCtx.mapOrGeoModel.getModel(['blur', 'itemStyle']).getItemStyle(); // Only support `opacity` here. Because not sure that other props are suitable for\n      // all of the elements generated by SVG (especially for Text/TSpan/Image/... ).\n\n      var opacity_1 = blurStyle.opacity;\n      this._svgGraphicRecord.root.traverse(function (el) {\n        if (!el.isGroup) {\n          // PENDING: clear those settings to SVG elements when `_freeSVG`.\n          // (Currently it happen not to be needed.)\n          setDefaultStateProxy(el);\n          var style = el.ensureState('blur').style || {}; // Do not overwrite the region style that already set from region option.\n\n          if (style.opacity == null && opacity_1 != null) {\n            style.opacity = opacity_1;\n          } // If `ensureState('blur').style = {}`, there will be default opacity.\n          // Enable `stateTransition` (animation).\n\n          el.ensureState('emphasis');\n        }\n      });\n    }\n  };\n  MapDraw.prototype.remove = function () {\n    this._regionsGroup.removeAll();\n    this._regionsGroupByName = null;\n    this._svgGroup.removeAll();\n    this._freeSVG();\n    this._controller.dispose();\n    this._controllerHost = null;\n  };\n  MapDraw.prototype.findHighDownDispatchers = function (name, geoModel) {\n    if (name == null) {\n      return [];\n    }\n    var geo = geoModel.coordinateSystem;\n    if (geo.resourceType === 'geoJSON') {\n      var regionsGroupByName = this._regionsGroupByName;\n      if (regionsGroupByName) {\n        var regionGroup = regionsGroupByName.get(name);\n        return regionGroup ? [regionGroup] : [];\n      }\n    } else if (geo.resourceType === 'geoSVG') {\n      return this._svgDispatcherMap && this._svgDispatcherMap.get(name) || [];\n    }\n  };\n  MapDraw.prototype._svgResourceChanged = function (mapName) {\n    return this._svgMapName !== mapName;\n  };\n  MapDraw.prototype._useSVG = function (mapName) {\n    var resource = geoSourceManager.getGeoResource(mapName);\n    if (resource && resource.type === 'geoSVG') {\n      var svgGraphic = resource.useGraphic(this.uid);\n      this._svgGroup.add(svgGraphic.root);\n      this._svgGraphicRecord = svgGraphic;\n      this._svgMapName = mapName;\n    }\n  };\n  MapDraw.prototype._freeSVG = function () {\n    var mapName = this._svgMapName;\n    if (mapName == null) {\n      return;\n    }\n    var resource = geoSourceManager.getGeoResource(mapName);\n    if (resource && resource.type === 'geoSVG') {\n      resource.freeGraphic(this.uid);\n    }\n    this._svgGraphicRecord = null;\n    this._svgDispatcherMap = null;\n    this._svgGroup.removeAll();\n    this._svgMapName = null;\n  };\n  MapDraw.prototype._updateController = function (mapOrGeoModel, ecModel, api) {\n    var geo = mapOrGeoModel.coordinateSystem;\n    var controller = this._controller;\n    var controllerHost = this._controllerHost; // @ts-ignore FIXME:TS\n\n    controllerHost.zoomLimit = mapOrGeoModel.get('scaleLimit');\n    controllerHost.zoom = geo.getZoom(); // roamType is will be set default true if it is null\n    // @ts-ignore FIXME:TS\n\n    controller.enable(mapOrGeoModel.get('roam') || false);\n    var mainType = mapOrGeoModel.mainType;\n    function makeActionBase() {\n      var action = {\n        type: 'geoRoam',\n        componentType: mainType\n      };\n      action[mainType + 'Id'] = mapOrGeoModel.id;\n      return action;\n    }\n    controller.off('pan').on('pan', function (e) {\n      this._mouseDownFlag = false;\n      roamHelper.updateViewOnPan(controllerHost, e.dx, e.dy);\n      api.dispatchAction(zrUtil.extend(makeActionBase(), {\n        dx: e.dx,\n        dy: e.dy,\n        animation: {\n          duration: 0\n        }\n      }));\n    }, this);\n    controller.off('zoom').on('zoom', function (e) {\n      this._mouseDownFlag = false;\n      roamHelper.updateViewOnZoom(controllerHost, e.scale, e.originX, e.originY);\n      api.dispatchAction(zrUtil.extend(makeActionBase(), {\n        zoom: e.scale,\n        originX: e.originX,\n        originY: e.originY,\n        animation: {\n          duration: 0\n        }\n      }));\n    }, this);\n    controller.setPointerChecker(function (e, x, y) {\n      return geo.containPoint([x, y]) && !onIrrelevantElement(e, api, mapOrGeoModel);\n    });\n  };\n  /**\r\n   * FIXME: this is a temporarily workaround.\r\n   * When `geoRoam` the elements need to be reset in `MapView['render']`, because the props like\r\n   * `ignore` might have been modified by `LabelManager`, and `LabelManager#addLabelsOfSeries`\r\n   * will subsequently cache `defaultAttr` like `ignore`. If do not do this reset, the modified\r\n   * props will have no chance to be restored.\r\n   * Note: This reset should be after `clearStates` in `renderSeries` because `useStates` in\r\n   * `renderSeries` will cache the modified `ignore` to `el._normalState`.\r\n   * TODO:\r\n   * Use clone/immutable in `LabelManager`?\r\n   */\n\n  MapDraw.prototype.resetForLabelLayout = function () {\n    this.group.traverse(function (el) {\n      var label = el.getTextContent();\n      if (label) {\n        label.ignore = mapLabelRaw(label).ignore;\n      }\n    });\n  };\n  MapDraw.prototype._updateMapSelectHandler = function (mapOrGeoModel, regionsGroup, api, fromView) {\n    var mapDraw = this;\n    regionsGroup.off('mousedown');\n    regionsGroup.off('click'); // @ts-ignore FIXME:TS resolve type conflict\n\n    if (mapOrGeoModel.get('selectedMode')) {\n      regionsGroup.on('mousedown', function () {\n        mapDraw._mouseDownFlag = true;\n      });\n      regionsGroup.on('click', function (e) {\n        if (!mapDraw._mouseDownFlag) {\n          return;\n        }\n        mapDraw._mouseDownFlag = false;\n      });\n    }\n  };\n  return MapDraw;\n}();\n;\nfunction applyOptionStyleForRegion(viewBuildCtx, el, dataIndex, regionModel) {\n  // All of the path are using `itemStyle`, because\n  // (1) Some SVG also use fill on polyline (The different between\n  // polyline and polygon is \"open\" or \"close\" but not fill or not).\n  // (2) For the common props like opacity, if some use itemStyle\n  // and some use `lineStyle`, it might confuse users.\n  // (3) Most SVG use <path>, where can not detect whether to draw a \"line\"\n  // or a filled shape, so use `itemStyle` for <path>.\n  var normalStyleModel = regionModel.getModel('itemStyle');\n  var emphasisStyleModel = regionModel.getModel(['emphasis', 'itemStyle']);\n  var blurStyleModel = regionModel.getModel(['blur', 'itemStyle']);\n  var selectStyleModel = regionModel.getModel(['select', 'itemStyle']); // NOTE: DON'T use 'style' in visual when drawing map.\n  // This component is used for drawing underlying map for both geo component and map series.\n\n  var normalStyle = getFixedItemStyle(normalStyleModel);\n  var emphasisStyle = getFixedItemStyle(emphasisStyleModel);\n  var selectStyle = getFixedItemStyle(selectStyleModel);\n  var blurStyle = getFixedItemStyle(blurStyleModel); // Update the itemStyle if has data visual\n\n  var data = viewBuildCtx.data;\n  if (data) {\n    // Only visual color of each item will be used. It can be encoded by visualMap\n    // But visual color of series is used in symbol drawing\n    // Visual color for each series is for the symbol draw\n    var style = data.getItemVisual(dataIndex, 'style');\n    var decal = data.getItemVisual(dataIndex, 'decal');\n    if (viewBuildCtx.isVisualEncodedByVisualMap && style.fill) {\n      normalStyle.fill = style.fill;\n    }\n    if (decal) {\n      normalStyle.decal = createOrUpdatePatternFromDecal(decal, viewBuildCtx.api);\n    }\n  } // SVG text, tspan and image can be named but not supporeted\n  // to be styled by region option yet.\n\n  el.setStyle(normalStyle);\n  el.style.strokeNoScale = true;\n  el.ensureState('emphasis').style = emphasisStyle;\n  el.ensureState('select').style = selectStyle;\n  el.ensureState('blur').style = blurStyle; // Enable blur\n\n  setDefaultStateProxy(el);\n}\nfunction resetLabelForRegion(viewBuildCtx, el, regionName, regionModel, mapOrGeoModel,\n// Exist only if `viewBuildCtx.data` exists.\ndataIdx,\n// If labelXY not provided, use `textConfig.position: 'inside'`\nlabelXY) {\n  var data = viewBuildCtx.data;\n  var isGeo = viewBuildCtx.isGeo;\n  var isDataNaN = data && isNaN(data.get(data.mapDimension('value'), dataIdx));\n  var itemLayout = data && data.getItemLayout(dataIdx); // In the following cases label will be drawn\n  // 1. In map series and data value is NaN\n  // 2. In geo component\n  // 3. Region has no series legendIcon, which will be add a showLabel flag in mapSymbolLayout\n\n  if (isGeo || isDataNaN || itemLayout && itemLayout.showLabel) {\n    var query = !isGeo ? dataIdx : regionName;\n    var labelFetcher = void 0; // Consider dataIdx not found.\n\n    if (!data || dataIdx >= 0) {\n      labelFetcher = mapOrGeoModel;\n    }\n    var specifiedTextOpt = labelXY ? {\n      normal: {\n        align: 'center',\n        verticalAlign: 'middle'\n      }\n    } : null; // Caveat: must be called after `setDefaultStateProxy(el);` called.\n    // because textContent will be assign with `el.stateProxy` inside.\n\n    setLabelStyle(el, getLabelStatesModels(regionModel), {\n      labelFetcher: labelFetcher,\n      labelDataIndex: query,\n      defaultText: regionName\n    }, specifiedTextOpt);\n    var textEl = el.getTextContent();\n    if (textEl) {\n      mapLabelRaw(textEl).ignore = textEl.ignore;\n      if (el.textConfig && labelXY) {\n        // Compute a relative offset based on the el bounding rect.\n        var rect = el.getBoundingRect().clone(); // Need to make sure the percent position base on the same rect in normal and\n        // emphasis state. Otherwise if using boundingRect of el, but the emphasis state\n        // has borderWidth (even 0.5px), the text position will be changed obviously\n        // if the position is very big like ['1234%', '1345%'].\n\n        el.textConfig.layoutRect = rect;\n        el.textConfig.position = [(labelXY[0] - rect.x) / rect.width * 100 + '%', (labelXY[1] - rect.y) / rect.height * 100 + '%'];\n      }\n    } // PENDING:\n    // If labelLayout is enabled (test/label-layout.html), el.dataIndex should be specified.\n    // But el.dataIndex is also used to determine whether user event should be triggered,\n    // where el.seriesIndex or el.dataModel must be specified. At present for a single el\n    // there is not case that \"only label layout enabled but user event disabled\", so here\n    // we depends `resetEventTriggerForRegion` to do the job of setting `el.dataIndex`.\n\n    el.disableLabelAnimation = true;\n  } else {\n    el.removeTextContent();\n    el.removeTextConfig();\n    el.disableLabelAnimation = null;\n  }\n}\nfunction resetEventTriggerForRegion(viewBuildCtx, eventTrigger, regionName, regionModel, mapOrGeoModel,\n// Exist only if `viewBuildCtx.data` exists.\ndataIdx) {\n  // setItemGraphicEl, setHoverStyle after all polygons and labels\n  // are added to the regionGroup\n  if (viewBuildCtx.data) {\n    // FIXME: when series-map use a SVG map, and there are duplicated name specified\n    // on different SVG elements, after `data.setItemGraphicEl(...)`:\n    // (1) all of them will be mounted with `dataIndex`, `seriesIndex`, so that tooltip\n    // can be triggered only mouse hover. That's correct.\n    // (2) only the last element will be kept in `data`, so that if trigger tooltip\n    // by `dispatchAction`, only the last one can be found and triggered. That might be\n    // not correct. We will fix it in future if anyone demanding that.\n    viewBuildCtx.data.setItemGraphicEl(dataIdx, eventTrigger);\n  } // series-map will not trigger \"geoselectchange\" no matter it is\n  // based on a declared geo component. Because series-map will\n  // trigger \"selectchange\". If it trigger both the two events,\n  // If users call `chart.dispatchAction({type: 'toggleSelect'})`,\n  // it not easy to also fire event \"geoselectchanged\".\n  else {\n    // Package custom mouse event for geo component\n    getECData(eventTrigger).eventData = {\n      componentType: 'geo',\n      componentIndex: mapOrGeoModel.componentIndex,\n      geoIndex: mapOrGeoModel.componentIndex,\n      name: regionName,\n      region: regionModel && regionModel.option || {}\n    };\n  }\n}\nfunction resetTooltipForRegion(viewBuildCtx, el, regionName, regionModel, mapOrGeoModel) {\n  if (!viewBuildCtx.data) {\n    graphic.setTooltipConfig({\n      el: el,\n      componentModel: mapOrGeoModel,\n      itemName: regionName,\n      // @ts-ignore FIXME:TS fix the \"compatible with each other\"?\n      itemTooltipOption: regionModel.get('tooltip')\n    });\n  }\n}\nfunction resetStateTriggerForRegion(viewBuildCtx, el, regionName, regionModel, mapOrGeoModel) {\n  // @ts-ignore FIXME:TS fix the \"compatible with each other\"?\n  el.highDownSilentOnTouch = !!mapOrGeoModel.get('selectedMode'); // @ts-ignore FIXME:TS fix the \"compatible with each other\"?\n\n  var emphasisModel = regionModel.getModel('emphasis');\n  var focus = emphasisModel.get('focus');\n  toggleHoverEmphasis(el, focus, emphasisModel.get('blurScope'), emphasisModel.get('disabled'));\n  if (viewBuildCtx.isGeo) {\n    enableComponentHighDownFeatures(el, mapOrGeoModel, regionName);\n  }\n  return focus;\n}\nfunction projectPolys(rings,\n// Polygons include exterior and interiors. Or polylines.\ncreateStream, isLine) {\n  var polygons = [];\n  var curPoly;\n  function startPolygon() {\n    curPoly = [];\n  }\n  function endPolygon() {\n    if (curPoly.length) {\n      polygons.push(curPoly);\n      curPoly = [];\n    }\n  }\n  var stream = createStream({\n    polygonStart: startPolygon,\n    polygonEnd: endPolygon,\n    lineStart: startPolygon,\n    lineEnd: endPolygon,\n    point: function (x, y) {\n      // May have NaN values from stream.\n      if (isFinite(x) && isFinite(y)) {\n        curPoly.push([x, y]);\n      }\n    },\n    sphere: function () {}\n  });\n  !isLine && stream.polygonStart();\n  zrUtil.each(rings, function (ring) {\n    stream.lineStart();\n    for (var i = 0; i < ring.length; i++) {\n      stream.point(ring[i][0], ring[i][1]);\n    }\n    stream.lineEnd();\n  });\n  !isLine && stream.polygonEnd();\n  return polygons;\n}\nexport default MapDraw; // @ts-ignore FIXME:TS fix the \"compatible with each other\"?", "map": {"version": 3, "names": ["zrUtil", "RoamController", "roamHelper", "onIrrelevantElement", "graphic", "toggleHoverEmphasis", "enableComponentHighDownFeatures", "setDefaultStateProxy", "geoSourceManager", "getUID", "setLabelStyle", "getLabelStatesModels", "getECData", "createOrUpdatePatternFromDecal", "Displayable", "makeInner", "OPTION_STYLE_ENABLED_TAGS", "OPTION_STYLE_ENABLED_TAG_MAP", "createHashMap", "STATE_TRIGGER_TAG_MAP", "concat", "LABEL_HOST_MAP", "mapLabelRaw", "getFixedItemStyle", "model", "itemStyle", "getItemStyle", "areaColor", "get", "fill", "fixLineStyle", "styleHost", "style", "stroke", "MapDraw", "api", "group", "Group", "uid", "_controller", "getZr", "_controllerHost", "target", "add", "_regionsGroup", "_svgGroup", "prototype", "draw", "mapOrGeoModel", "ecModel", "fromView", "payload", "isGeo", "mainType", "data", "getData", "eachComponent", "subType", "mapSeries", "getHostGeoModel", "geo", "coordinateSystem", "regionsGroup", "transformInfo", "getTransformInfo", "transformInfoRaw", "raw", "transformInfoRoam", "roam", "isFirstDraw", "childAt", "x", "y", "scaleX", "scaleY", "dirty", "updateProps", "isVisualEncodedByVisualMap", "getVisual", "length", "viewBuildCtx", "resourceType", "_buildGeoJSON", "_buildSVG", "_updateController", "_updateMapSelectHandler", "regionsGroupByName", "_regionsGroupByName", "regionsInfoByName", "projection", "projectionStream", "stream", "transformPoint", "point", "project", "transformPolygonPoints", "inPoints", "outPoints", "i", "newPt", "push", "getPolyShape", "points", "shape", "removeAll", "each", "regions", "region", "regionName", "name", "regionGroup", "_a", "dataIdx", "regionModel", "set", "indexOfName", "getRegionModel", "getItemModel", "polygonSubpaths", "polylineSubpaths", "geometries", "geometry", "type", "polys", "exterior", "interiors", "projectPolys", "poly", "Polygon", "Polyline", "centerPt", "getCenter", "createCompoundPath", "subpaths", "isLine", "compoundPath", "CompoundPath", "culling", "segmentIgnoreThreshold", "paths", "applyOptionStyleForRegion", "resetLabelForRegion", "states", "resetEventTriggerForRegion", "resetTooltipForRegion", "resetStateTriggerForRegion", "mapName", "map", "_svgResourceChanged", "_freeSVG", "_useSVG", "svgDispatcherMap", "_svgDispatcherMap", "focusSelf", "_svgGraphicRecord", "named", "namedItem", "svgNodeTagLower", "el", "z2EmphasisLift", "named<PERSON><PERSON>", "focus_1", "els", "_enableBlurEntireSVG", "blurStyle", "getModel", "opacity_1", "opacity", "root", "traverse", "isGroup", "ensureState", "remove", "dispose", "findHighDownDispatchers", "geoModel", "_svgMapName", "resource", "getGeoResource", "svgGraphic", "useGraphic", "freeGraphic", "controller", "controllerHost", "zoomLimit", "zoom", "getZoom", "enable", "makeActionBase", "action", "componentType", "id", "off", "on", "e", "_mouseDownFlag", "updateViewOnPan", "dx", "dy", "dispatchAction", "extend", "animation", "duration", "updateViewOnZoom", "scale", "originX", "originY", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "containPoint", "resetForLabelLayout", "label", "getTextContent", "ignore", "mapDraw", "dataIndex", "normalStyleModel", "emphasisStyleModel", "blurStyleModel", "selectStyleModel", "normalStyle", "emphasisStyle", "selectStyle", "getItemVisual", "decal", "setStyle", "strokeNoScale", "labelXY", "isDataNaN", "isNaN", "mapDimension", "itemLayout", "getItemLayout", "showLabel", "query", "labelFetcher", "specifiedTextOpt", "normal", "align", "verticalAlign", "labelDataIndex", "defaultText", "textEl", "textConfig", "rect", "getBoundingRect", "clone", "layoutRect", "position", "width", "height", "disableLabelAnimation", "removeTextContent", "removeTextConfig", "eventTrigger", "setItemGraphicEl", "eventData", "componentIndex", "geoIndex", "option", "setTooltipConfig", "componentModel", "itemName", "itemTooltipOption", "highDownSilentOnTouch", "emphasisModel", "focus", "rings", "createStream", "polygons", "cur<PERSON><PERSON>", "startPolygon", "endPolygon", "polygonStart", "polygonEnd", "lineStart", "lineEnd", "isFinite", "sphere", "ring"], "sources": ["C:/WolfSystemV2/WolfOldFrontEnd/node_modules/echarts/lib/component/helper/MapDraw.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport RoamController from './RoamController.js';\nimport * as roamHelper from '../../component/helper/roamHelper.js';\nimport { onIrrelevantElement } from '../../component/helper/cursorHelper.js';\nimport * as graphic from '../../util/graphic.js';\nimport { toggleHoverEmphasis, enableComponentHighDownFeatures, setDefaultStateProxy } from '../../util/states.js';\nimport geoSourceManager from '../../coord/geo/geoSourceManager.js';\nimport { getUID } from '../../util/component.js';\nimport { setLabelStyle, getLabelStatesModels } from '../../label/labelStyle.js';\nimport { getECData } from '../../util/innerStore.js';\nimport { createOrUpdatePatternFromDecal } from '../../util/decal.js';\nimport Displayable from 'zrender/lib/graphic/Displayable.js';\nimport { makeInner } from '../../util/model.js';\n/**\r\n * Only these tags enable use `itemStyle` if they are named in SVG.\r\n * Other tags like <text> <tspan> <image> might not suitable for `itemStyle`.\r\n * They will not be considered to be styled until some requirements come.\r\n */\n\nvar OPTION_STYLE_ENABLED_TAGS = ['rect', 'circle', 'line', 'ellipse', 'polygon', 'polyline', 'path'];\nvar OPTION_STYLE_ENABLED_TAG_MAP = zrUtil.createHashMap(OPTION_STYLE_ENABLED_TAGS);\nvar STATE_TRIGGER_TAG_MAP = zrUtil.createHashMap(OPTION_STYLE_ENABLED_TAGS.concat(['g']));\nvar LABEL_HOST_MAP = zrUtil.createHashMap(OPTION_STYLE_ENABLED_TAGS.concat(['g']));\nvar mapLabelRaw = makeInner();\n\nfunction getFixedItemStyle(model) {\n  var itemStyle = model.getItemStyle();\n  var areaColor = model.get('areaColor'); // If user want the color not to be changed when hover,\n  // they should both set areaColor and color to be null.\n\n  if (areaColor != null) {\n    itemStyle.fill = areaColor;\n  }\n\n  return itemStyle;\n} // Only stroke can be used for line.\n// Using fill in style if stroke not exits.\n// TODO Not sure yet. Perhaps a separate `lineStyle`?\n\n\nfunction fixLineStyle(styleHost) {\n  var style = styleHost.style;\n\n  if (style) {\n    style.stroke = style.stroke || style.fill;\n    style.fill = null;\n  }\n}\n\nvar MapDraw =\n/** @class */\nfunction () {\n  function MapDraw(api) {\n    var group = new graphic.Group();\n    this.uid = getUID('ec_map_draw');\n    this._controller = new RoamController(api.getZr());\n    this._controllerHost = {\n      target: group\n    };\n    this.group = group;\n    group.add(this._regionsGroup = new graphic.Group());\n    group.add(this._svgGroup = new graphic.Group());\n  }\n\n  MapDraw.prototype.draw = function (mapOrGeoModel, ecModel, api, fromView, payload) {\n    var isGeo = mapOrGeoModel.mainType === 'geo'; // Map series has data. GEO model that controlled by map series\n    // will be assigned with map data. Other GEO model has no data.\n\n    var data = mapOrGeoModel.getData && mapOrGeoModel.getData();\n    isGeo && ecModel.eachComponent({\n      mainType: 'series',\n      subType: 'map'\n    }, function (mapSeries) {\n      if (!data && mapSeries.getHostGeoModel() === mapOrGeoModel) {\n        data = mapSeries.getData();\n      }\n    });\n    var geo = mapOrGeoModel.coordinateSystem;\n    var regionsGroup = this._regionsGroup;\n    var group = this.group;\n    var transformInfo = geo.getTransformInfo();\n    var transformInfoRaw = transformInfo.raw;\n    var transformInfoRoam = transformInfo.roam; // No animation when first draw or in action\n\n    var isFirstDraw = !regionsGroup.childAt(0) || payload;\n\n    if (isFirstDraw) {\n      group.x = transformInfoRoam.x;\n      group.y = transformInfoRoam.y;\n      group.scaleX = transformInfoRoam.scaleX;\n      group.scaleY = transformInfoRoam.scaleY;\n      group.dirty();\n    } else {\n      graphic.updateProps(group, transformInfoRoam, mapOrGeoModel);\n    }\n\n    var isVisualEncodedByVisualMap = data && data.getVisual('visualMeta') && data.getVisual('visualMeta').length > 0;\n    var viewBuildCtx = {\n      api: api,\n      geo: geo,\n      mapOrGeoModel: mapOrGeoModel,\n      data: data,\n      isVisualEncodedByVisualMap: isVisualEncodedByVisualMap,\n      isGeo: isGeo,\n      transformInfoRaw: transformInfoRaw\n    };\n\n    if (geo.resourceType === 'geoJSON') {\n      this._buildGeoJSON(viewBuildCtx);\n    } else if (geo.resourceType === 'geoSVG') {\n      this._buildSVG(viewBuildCtx);\n    }\n\n    this._updateController(mapOrGeoModel, ecModel, api);\n\n    this._updateMapSelectHandler(mapOrGeoModel, regionsGroup, api, fromView);\n  };\n\n  MapDraw.prototype._buildGeoJSON = function (viewBuildCtx) {\n    var regionsGroupByName = this._regionsGroupByName = zrUtil.createHashMap();\n    var regionsInfoByName = zrUtil.createHashMap();\n    var regionsGroup = this._regionsGroup;\n    var transformInfoRaw = viewBuildCtx.transformInfoRaw;\n    var mapOrGeoModel = viewBuildCtx.mapOrGeoModel;\n    var data = viewBuildCtx.data;\n    var projection = viewBuildCtx.geo.projection;\n    var projectionStream = projection && projection.stream;\n\n    function transformPoint(point, project) {\n      if (project) {\n        // projection may return null point.\n        point = project(point);\n      }\n\n      return point && [point[0] * transformInfoRaw.scaleX + transformInfoRaw.x, point[1] * transformInfoRaw.scaleY + transformInfoRaw.y];\n    }\n\n    ;\n\n    function transformPolygonPoints(inPoints) {\n      var outPoints = []; // If projectionStream is provided. Use it instead of single point project.\n\n      var project = !projectionStream && projection && projection.project;\n\n      for (var i = 0; i < inPoints.length; ++i) {\n        var newPt = transformPoint(inPoints[i], project);\n        newPt && outPoints.push(newPt);\n      }\n\n      return outPoints;\n    }\n\n    function getPolyShape(points) {\n      return {\n        shape: {\n          points: transformPolygonPoints(points)\n        }\n      };\n    }\n\n    regionsGroup.removeAll(); // Only when the resource is GeoJSON, there is `geo.regions`.\n\n    zrUtil.each(viewBuildCtx.geo.regions, function (region) {\n      var regionName = region.name; // Consider in GeoJson properties.name may be duplicated, for example,\n      // there is multiple region named \"United Kindom\" or \"France\" (so many\n      // colonies). And it is not appropriate to merge them in geo, which\n      // will make them share the same label and bring trouble in label\n      // location calculation.\n\n      var regionGroup = regionsGroupByName.get(regionName);\n\n      var _a = regionsInfoByName.get(regionName) || {},\n          dataIdx = _a.dataIdx,\n          regionModel = _a.regionModel;\n\n      if (!regionGroup) {\n        regionGroup = regionsGroupByName.set(regionName, new graphic.Group());\n        regionsGroup.add(regionGroup);\n        dataIdx = data ? data.indexOfName(regionName) : null;\n        regionModel = viewBuildCtx.isGeo ? mapOrGeoModel.getRegionModel(regionName) : data ? data.getItemModel(dataIdx) : null;\n        regionsInfoByName.set(regionName, {\n          dataIdx: dataIdx,\n          regionModel: regionModel\n        });\n      }\n\n      var polygonSubpaths = [];\n      var polylineSubpaths = [];\n      zrUtil.each(region.geometries, function (geometry) {\n        // Polygon and MultiPolygon\n        if (geometry.type === 'polygon') {\n          var polys = [geometry.exterior].concat(geometry.interiors || []);\n\n          if (projectionStream) {\n            polys = projectPolys(polys, projectionStream);\n          }\n\n          zrUtil.each(polys, function (poly) {\n            polygonSubpaths.push(new graphic.Polygon(getPolyShape(poly)));\n          });\n        } // LineString and MultiLineString\n        else {\n            var points = geometry.points;\n\n            if (projectionStream) {\n              points = projectPolys(points, projectionStream, true);\n            }\n\n            zrUtil.each(points, function (points) {\n              polylineSubpaths.push(new graphic.Polyline(getPolyShape(points)));\n            });\n          }\n      });\n      var centerPt = transformPoint(region.getCenter(), projection && projection.project);\n\n      function createCompoundPath(subpaths, isLine) {\n        if (!subpaths.length) {\n          return;\n        }\n\n        var compoundPath = new graphic.CompoundPath({\n          culling: true,\n          segmentIgnoreThreshold: 1,\n          shape: {\n            paths: subpaths\n          }\n        });\n        regionGroup.add(compoundPath);\n        applyOptionStyleForRegion(viewBuildCtx, compoundPath, dataIdx, regionModel);\n        resetLabelForRegion(viewBuildCtx, compoundPath, regionName, regionModel, mapOrGeoModel, dataIdx, centerPt);\n\n        if (isLine) {\n          fixLineStyle(compoundPath);\n          zrUtil.each(compoundPath.states, fixLineStyle);\n        }\n      }\n\n      createCompoundPath(polygonSubpaths);\n      createCompoundPath(polylineSubpaths, true);\n    }); // Ensure children have been added to `regionGroup` before calling them.\n\n    regionsGroupByName.each(function (regionGroup, regionName) {\n      var _a = regionsInfoByName.get(regionName),\n          dataIdx = _a.dataIdx,\n          regionModel = _a.regionModel;\n\n      resetEventTriggerForRegion(viewBuildCtx, regionGroup, regionName, regionModel, mapOrGeoModel, dataIdx);\n      resetTooltipForRegion(viewBuildCtx, regionGroup, regionName, regionModel, mapOrGeoModel);\n      resetStateTriggerForRegion(viewBuildCtx, regionGroup, regionName, regionModel, mapOrGeoModel);\n    }, this);\n  };\n\n  MapDraw.prototype._buildSVG = function (viewBuildCtx) {\n    var mapName = viewBuildCtx.geo.map;\n    var transformInfoRaw = viewBuildCtx.transformInfoRaw;\n    this._svgGroup.x = transformInfoRaw.x;\n    this._svgGroup.y = transformInfoRaw.y;\n    this._svgGroup.scaleX = transformInfoRaw.scaleX;\n    this._svgGroup.scaleY = transformInfoRaw.scaleY;\n\n    if (this._svgResourceChanged(mapName)) {\n      this._freeSVG();\n\n      this._useSVG(mapName);\n    }\n\n    var svgDispatcherMap = this._svgDispatcherMap = zrUtil.createHashMap();\n    var focusSelf = false;\n    zrUtil.each(this._svgGraphicRecord.named, function (namedItem) {\n      // Note that we also allow different elements have the same name.\n      // For example, a glyph of a city and the label of the city have\n      // the same name and their tooltip info can be defined in a single\n      // region option.\n      var regionName = namedItem.name;\n      var mapOrGeoModel = viewBuildCtx.mapOrGeoModel;\n      var data = viewBuildCtx.data;\n      var svgNodeTagLower = namedItem.svgNodeTagLower;\n      var el = namedItem.el;\n      var dataIdx = data ? data.indexOfName(regionName) : null;\n      var regionModel = mapOrGeoModel.getRegionModel(regionName);\n\n      if (OPTION_STYLE_ENABLED_TAG_MAP.get(svgNodeTagLower) != null && el instanceof Displayable) {\n        applyOptionStyleForRegion(viewBuildCtx, el, dataIdx, regionModel);\n      }\n\n      if (el instanceof Displayable) {\n        el.culling = true;\n      } // We do not know how the SVG like so we'd better not to change z2.\n      // Otherwise it might bring some unexpected result. For example,\n      // an area hovered that make some inner city can not be clicked.\n\n\n      el.z2EmphasisLift = 0; // If self named:\n\n      if (!namedItem.namedFrom) {\n        // label should batter to be displayed based on the center of <g>\n        // if it is named rather than displayed on each child.\n        if (LABEL_HOST_MAP.get(svgNodeTagLower) != null) {\n          resetLabelForRegion(viewBuildCtx, el, regionName, regionModel, mapOrGeoModel, dataIdx, null);\n        }\n\n        resetEventTriggerForRegion(viewBuildCtx, el, regionName, regionModel, mapOrGeoModel, dataIdx);\n        resetTooltipForRegion(viewBuildCtx, el, regionName, regionModel, mapOrGeoModel);\n\n        if (STATE_TRIGGER_TAG_MAP.get(svgNodeTagLower) != null) {\n          var focus_1 = resetStateTriggerForRegion(viewBuildCtx, el, regionName, regionModel, mapOrGeoModel);\n\n          if (focus_1 === 'self') {\n            focusSelf = true;\n          }\n\n          var els = svgDispatcherMap.get(regionName) || svgDispatcherMap.set(regionName, []);\n          els.push(el);\n        }\n      }\n    }, this);\n\n    this._enableBlurEntireSVG(focusSelf, viewBuildCtx);\n  };\n\n  MapDraw.prototype._enableBlurEntireSVG = function (focusSelf, viewBuildCtx) {\n    // It's a little complicated to support blurring the entire geoSVG in series-map.\n    // So do not support it until some requirements come.\n    // At present, in series-map, only regions can be blurred.\n    if (focusSelf && viewBuildCtx.isGeo) {\n      var blurStyle = viewBuildCtx.mapOrGeoModel.getModel(['blur', 'itemStyle']).getItemStyle(); // Only support `opacity` here. Because not sure that other props are suitable for\n      // all of the elements generated by SVG (especially for Text/TSpan/Image/... ).\n\n      var opacity_1 = blurStyle.opacity;\n\n      this._svgGraphicRecord.root.traverse(function (el) {\n        if (!el.isGroup) {\n          // PENDING: clear those settings to SVG elements when `_freeSVG`.\n          // (Currently it happen not to be needed.)\n          setDefaultStateProxy(el);\n          var style = el.ensureState('blur').style || {}; // Do not overwrite the region style that already set from region option.\n\n          if (style.opacity == null && opacity_1 != null) {\n            style.opacity = opacity_1;\n          } // If `ensureState('blur').style = {}`, there will be default opacity.\n          // Enable `stateTransition` (animation).\n\n\n          el.ensureState('emphasis');\n        }\n      });\n    }\n  };\n\n  MapDraw.prototype.remove = function () {\n    this._regionsGroup.removeAll();\n\n    this._regionsGroupByName = null;\n\n    this._svgGroup.removeAll();\n\n    this._freeSVG();\n\n    this._controller.dispose();\n\n    this._controllerHost = null;\n  };\n\n  MapDraw.prototype.findHighDownDispatchers = function (name, geoModel) {\n    if (name == null) {\n      return [];\n    }\n\n    var geo = geoModel.coordinateSystem;\n\n    if (geo.resourceType === 'geoJSON') {\n      var regionsGroupByName = this._regionsGroupByName;\n\n      if (regionsGroupByName) {\n        var regionGroup = regionsGroupByName.get(name);\n        return regionGroup ? [regionGroup] : [];\n      }\n    } else if (geo.resourceType === 'geoSVG') {\n      return this._svgDispatcherMap && this._svgDispatcherMap.get(name) || [];\n    }\n  };\n\n  MapDraw.prototype._svgResourceChanged = function (mapName) {\n    return this._svgMapName !== mapName;\n  };\n\n  MapDraw.prototype._useSVG = function (mapName) {\n    var resource = geoSourceManager.getGeoResource(mapName);\n\n    if (resource && resource.type === 'geoSVG') {\n      var svgGraphic = resource.useGraphic(this.uid);\n\n      this._svgGroup.add(svgGraphic.root);\n\n      this._svgGraphicRecord = svgGraphic;\n      this._svgMapName = mapName;\n    }\n  };\n\n  MapDraw.prototype._freeSVG = function () {\n    var mapName = this._svgMapName;\n\n    if (mapName == null) {\n      return;\n    }\n\n    var resource = geoSourceManager.getGeoResource(mapName);\n\n    if (resource && resource.type === 'geoSVG') {\n      resource.freeGraphic(this.uid);\n    }\n\n    this._svgGraphicRecord = null;\n    this._svgDispatcherMap = null;\n\n    this._svgGroup.removeAll();\n\n    this._svgMapName = null;\n  };\n\n  MapDraw.prototype._updateController = function (mapOrGeoModel, ecModel, api) {\n    var geo = mapOrGeoModel.coordinateSystem;\n    var controller = this._controller;\n    var controllerHost = this._controllerHost; // @ts-ignore FIXME:TS\n\n    controllerHost.zoomLimit = mapOrGeoModel.get('scaleLimit');\n    controllerHost.zoom = geo.getZoom(); // roamType is will be set default true if it is null\n    // @ts-ignore FIXME:TS\n\n    controller.enable(mapOrGeoModel.get('roam') || false);\n    var mainType = mapOrGeoModel.mainType;\n\n    function makeActionBase() {\n      var action = {\n        type: 'geoRoam',\n        componentType: mainType\n      };\n      action[mainType + 'Id'] = mapOrGeoModel.id;\n      return action;\n    }\n\n    controller.off('pan').on('pan', function (e) {\n      this._mouseDownFlag = false;\n      roamHelper.updateViewOnPan(controllerHost, e.dx, e.dy);\n      api.dispatchAction(zrUtil.extend(makeActionBase(), {\n        dx: e.dx,\n        dy: e.dy,\n        animation: {\n          duration: 0\n        }\n      }));\n    }, this);\n    controller.off('zoom').on('zoom', function (e) {\n      this._mouseDownFlag = false;\n      roamHelper.updateViewOnZoom(controllerHost, e.scale, e.originX, e.originY);\n      api.dispatchAction(zrUtil.extend(makeActionBase(), {\n        zoom: e.scale,\n        originX: e.originX,\n        originY: e.originY,\n        animation: {\n          duration: 0\n        }\n      }));\n    }, this);\n    controller.setPointerChecker(function (e, x, y) {\n      return geo.containPoint([x, y]) && !onIrrelevantElement(e, api, mapOrGeoModel);\n    });\n  };\n  /**\r\n   * FIXME: this is a temporarily workaround.\r\n   * When `geoRoam` the elements need to be reset in `MapView['render']`, because the props like\r\n   * `ignore` might have been modified by `LabelManager`, and `LabelManager#addLabelsOfSeries`\r\n   * will subsequently cache `defaultAttr` like `ignore`. If do not do this reset, the modified\r\n   * props will have no chance to be restored.\r\n   * Note: This reset should be after `clearStates` in `renderSeries` because `useStates` in\r\n   * `renderSeries` will cache the modified `ignore` to `el._normalState`.\r\n   * TODO:\r\n   * Use clone/immutable in `LabelManager`?\r\n   */\n\n\n  MapDraw.prototype.resetForLabelLayout = function () {\n    this.group.traverse(function (el) {\n      var label = el.getTextContent();\n\n      if (label) {\n        label.ignore = mapLabelRaw(label).ignore;\n      }\n    });\n  };\n\n  MapDraw.prototype._updateMapSelectHandler = function (mapOrGeoModel, regionsGroup, api, fromView) {\n    var mapDraw = this;\n    regionsGroup.off('mousedown');\n    regionsGroup.off('click'); // @ts-ignore FIXME:TS resolve type conflict\n\n    if (mapOrGeoModel.get('selectedMode')) {\n      regionsGroup.on('mousedown', function () {\n        mapDraw._mouseDownFlag = true;\n      });\n      regionsGroup.on('click', function (e) {\n        if (!mapDraw._mouseDownFlag) {\n          return;\n        }\n\n        mapDraw._mouseDownFlag = false;\n      });\n    }\n  };\n\n  return MapDraw;\n}();\n\n;\n\nfunction applyOptionStyleForRegion(viewBuildCtx, el, dataIndex, regionModel) {\n  // All of the path are using `itemStyle`, because\n  // (1) Some SVG also use fill on polyline (The different between\n  // polyline and polygon is \"open\" or \"close\" but not fill or not).\n  // (2) For the common props like opacity, if some use itemStyle\n  // and some use `lineStyle`, it might confuse users.\n  // (3) Most SVG use <path>, where can not detect whether to draw a \"line\"\n  // or a filled shape, so use `itemStyle` for <path>.\n  var normalStyleModel = regionModel.getModel('itemStyle');\n  var emphasisStyleModel = regionModel.getModel(['emphasis', 'itemStyle']);\n  var blurStyleModel = regionModel.getModel(['blur', 'itemStyle']);\n  var selectStyleModel = regionModel.getModel(['select', 'itemStyle']); // NOTE: DON'T use 'style' in visual when drawing map.\n  // This component is used for drawing underlying map for both geo component and map series.\n\n  var normalStyle = getFixedItemStyle(normalStyleModel);\n  var emphasisStyle = getFixedItemStyle(emphasisStyleModel);\n  var selectStyle = getFixedItemStyle(selectStyleModel);\n  var blurStyle = getFixedItemStyle(blurStyleModel); // Update the itemStyle if has data visual\n\n  var data = viewBuildCtx.data;\n\n  if (data) {\n    // Only visual color of each item will be used. It can be encoded by visualMap\n    // But visual color of series is used in symbol drawing\n    // Visual color for each series is for the symbol draw\n    var style = data.getItemVisual(dataIndex, 'style');\n    var decal = data.getItemVisual(dataIndex, 'decal');\n\n    if (viewBuildCtx.isVisualEncodedByVisualMap && style.fill) {\n      normalStyle.fill = style.fill;\n    }\n\n    if (decal) {\n      normalStyle.decal = createOrUpdatePatternFromDecal(decal, viewBuildCtx.api);\n    }\n  } // SVG text, tspan and image can be named but not supporeted\n  // to be styled by region option yet.\n\n\n  el.setStyle(normalStyle);\n  el.style.strokeNoScale = true;\n  el.ensureState('emphasis').style = emphasisStyle;\n  el.ensureState('select').style = selectStyle;\n  el.ensureState('blur').style = blurStyle; // Enable blur\n\n  setDefaultStateProxy(el);\n}\n\nfunction resetLabelForRegion(viewBuildCtx, el, regionName, regionModel, mapOrGeoModel, // Exist only if `viewBuildCtx.data` exists.\ndataIdx, // If labelXY not provided, use `textConfig.position: 'inside'`\nlabelXY) {\n  var data = viewBuildCtx.data;\n  var isGeo = viewBuildCtx.isGeo;\n  var isDataNaN = data && isNaN(data.get(data.mapDimension('value'), dataIdx));\n  var itemLayout = data && data.getItemLayout(dataIdx); // In the following cases label will be drawn\n  // 1. In map series and data value is NaN\n  // 2. In geo component\n  // 3. Region has no series legendIcon, which will be add a showLabel flag in mapSymbolLayout\n\n  if (isGeo || isDataNaN || itemLayout && itemLayout.showLabel) {\n    var query = !isGeo ? dataIdx : regionName;\n    var labelFetcher = void 0; // Consider dataIdx not found.\n\n    if (!data || dataIdx >= 0) {\n      labelFetcher = mapOrGeoModel;\n    }\n\n    var specifiedTextOpt = labelXY ? {\n      normal: {\n        align: 'center',\n        verticalAlign: 'middle'\n      }\n    } : null; // Caveat: must be called after `setDefaultStateProxy(el);` called.\n    // because textContent will be assign with `el.stateProxy` inside.\n\n    setLabelStyle(el, getLabelStatesModels(regionModel), {\n      labelFetcher: labelFetcher,\n      labelDataIndex: query,\n      defaultText: regionName\n    }, specifiedTextOpt);\n    var textEl = el.getTextContent();\n\n    if (textEl) {\n      mapLabelRaw(textEl).ignore = textEl.ignore;\n\n      if (el.textConfig && labelXY) {\n        // Compute a relative offset based on the el bounding rect.\n        var rect = el.getBoundingRect().clone(); // Need to make sure the percent position base on the same rect in normal and\n        // emphasis state. Otherwise if using boundingRect of el, but the emphasis state\n        // has borderWidth (even 0.5px), the text position will be changed obviously\n        // if the position is very big like ['1234%', '1345%'].\n\n        el.textConfig.layoutRect = rect;\n        el.textConfig.position = [(labelXY[0] - rect.x) / rect.width * 100 + '%', (labelXY[1] - rect.y) / rect.height * 100 + '%'];\n      }\n    } // PENDING:\n    // If labelLayout is enabled (test/label-layout.html), el.dataIndex should be specified.\n    // But el.dataIndex is also used to determine whether user event should be triggered,\n    // where el.seriesIndex or el.dataModel must be specified. At present for a single el\n    // there is not case that \"only label layout enabled but user event disabled\", so here\n    // we depends `resetEventTriggerForRegion` to do the job of setting `el.dataIndex`.\n\n\n    el.disableLabelAnimation = true;\n  } else {\n    el.removeTextContent();\n    el.removeTextConfig();\n    el.disableLabelAnimation = null;\n  }\n}\n\nfunction resetEventTriggerForRegion(viewBuildCtx, eventTrigger, regionName, regionModel, mapOrGeoModel, // Exist only if `viewBuildCtx.data` exists.\ndataIdx) {\n  // setItemGraphicEl, setHoverStyle after all polygons and labels\n  // are added to the regionGroup\n  if (viewBuildCtx.data) {\n    // FIXME: when series-map use a SVG map, and there are duplicated name specified\n    // on different SVG elements, after `data.setItemGraphicEl(...)`:\n    // (1) all of them will be mounted with `dataIndex`, `seriesIndex`, so that tooltip\n    // can be triggered only mouse hover. That's correct.\n    // (2) only the last element will be kept in `data`, so that if trigger tooltip\n    // by `dispatchAction`, only the last one can be found and triggered. That might be\n    // not correct. We will fix it in future if anyone demanding that.\n    viewBuildCtx.data.setItemGraphicEl(dataIdx, eventTrigger);\n  } // series-map will not trigger \"geoselectchange\" no matter it is\n  // based on a declared geo component. Because series-map will\n  // trigger \"selectchange\". If it trigger both the two events,\n  // If users call `chart.dispatchAction({type: 'toggleSelect'})`,\n  // it not easy to also fire event \"geoselectchanged\".\n  else {\n      // Package custom mouse event for geo component\n      getECData(eventTrigger).eventData = {\n        componentType: 'geo',\n        componentIndex: mapOrGeoModel.componentIndex,\n        geoIndex: mapOrGeoModel.componentIndex,\n        name: regionName,\n        region: regionModel && regionModel.option || {}\n      };\n    }\n}\n\nfunction resetTooltipForRegion(viewBuildCtx, el, regionName, regionModel, mapOrGeoModel) {\n  if (!viewBuildCtx.data) {\n    graphic.setTooltipConfig({\n      el: el,\n      componentModel: mapOrGeoModel,\n      itemName: regionName,\n      // @ts-ignore FIXME:TS fix the \"compatible with each other\"?\n      itemTooltipOption: regionModel.get('tooltip')\n    });\n  }\n}\n\nfunction resetStateTriggerForRegion(viewBuildCtx, el, regionName, regionModel, mapOrGeoModel) {\n  // @ts-ignore FIXME:TS fix the \"compatible with each other\"?\n  el.highDownSilentOnTouch = !!mapOrGeoModel.get('selectedMode'); // @ts-ignore FIXME:TS fix the \"compatible with each other\"?\n\n  var emphasisModel = regionModel.getModel('emphasis');\n  var focus = emphasisModel.get('focus');\n  toggleHoverEmphasis(el, focus, emphasisModel.get('blurScope'), emphasisModel.get('disabled'));\n\n  if (viewBuildCtx.isGeo) {\n    enableComponentHighDownFeatures(el, mapOrGeoModel, regionName);\n  }\n\n  return focus;\n}\n\nfunction projectPolys(rings, // Polygons include exterior and interiors. Or polylines.\ncreateStream, isLine) {\n  var polygons = [];\n  var curPoly;\n\n  function startPolygon() {\n    curPoly = [];\n  }\n\n  function endPolygon() {\n    if (curPoly.length) {\n      polygons.push(curPoly);\n      curPoly = [];\n    }\n  }\n\n  var stream = createStream({\n    polygonStart: startPolygon,\n    polygonEnd: endPolygon,\n    lineStart: startPolygon,\n    lineEnd: endPolygon,\n    point: function (x, y) {\n      // May have NaN values from stream.\n      if (isFinite(x) && isFinite(y)) {\n        curPoly.push([x, y]);\n      }\n    },\n    sphere: function () {}\n  });\n  !isLine && stream.polygonStart();\n  zrUtil.each(rings, function (ring) {\n    stream.lineStart();\n\n    for (var i = 0; i < ring.length; i++) {\n      stream.point(ring[i][0], ring[i][1]);\n    }\n\n    stream.lineEnd();\n  });\n  !isLine && stream.polygonEnd();\n  return polygons;\n}\n\nexport default MapDraw; // @ts-ignore FIXME:TS fix the \"compatible with each other\"?"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,KAAKA,MAAM,MAAM,0BAA0B;AAClD,OAAOC,cAAc,MAAM,qBAAqB;AAChD,OAAO,KAAKC,UAAU,MAAM,sCAAsC;AAClE,SAASC,mBAAmB,QAAQ,wCAAwC;AAC5E,OAAO,KAAKC,OAAO,MAAM,uBAAuB;AAChD,SAASC,mBAAmB,EAAEC,+BAA+B,EAAEC,oBAAoB,QAAQ,sBAAsB;AACjH,OAAOC,gBAAgB,MAAM,qCAAqC;AAClE,SAASC,MAAM,QAAQ,yBAAyB;AAChD,SAASC,aAAa,EAAEC,oBAAoB,QAAQ,2BAA2B;AAC/E,SAASC,SAAS,QAAQ,0BAA0B;AACpD,SAASC,8BAA8B,QAAQ,qBAAqB;AACpE,OAAOC,WAAW,MAAM,oCAAoC;AAC5D,SAASC,SAAS,QAAQ,qBAAqB;AAC/C;AACA;AACA;AACA;AACA;;AAEA,IAAIC,yBAAyB,GAAG,CAAC,MAAM,EAAE,QAAQ,EAAE,MAAM,EAAE,SAAS,EAAE,SAAS,EAAE,UAAU,EAAE,MAAM,CAAC;AACpG,IAAIC,4BAA4B,GAAGjB,MAAM,CAACkB,aAAa,CAACF,yBAAyB,CAAC;AAClF,IAAIG,qBAAqB,GAAGnB,MAAM,CAACkB,aAAa,CAACF,yBAAyB,CAACI,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;AACzF,IAAIC,cAAc,GAAGrB,MAAM,CAACkB,aAAa,CAACF,yBAAyB,CAACI,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;AAClF,IAAIE,WAAW,GAAGP,SAAS,CAAC,CAAC;AAE7B,SAASQ,iBAAiBA,CAACC,KAAK,EAAE;EAChC,IAAIC,SAAS,GAAGD,KAAK,CAACE,YAAY,CAAC,CAAC;EACpC,IAAIC,SAAS,GAAGH,KAAK,CAACI,GAAG,CAAC,WAAW,CAAC,CAAC,CAAC;EACxC;;EAEA,IAAID,SAAS,IAAI,IAAI,EAAE;IACrBF,SAAS,CAACI,IAAI,GAAGF,SAAS;EAC5B;EAEA,OAAOF,SAAS;AAClB,CAAC,CAAC;AACF;AACA;;AAGA,SAASK,YAAYA,CAACC,SAAS,EAAE;EAC/B,IAAIC,KAAK,GAAGD,SAAS,CAACC,KAAK;EAE3B,IAAIA,KAAK,EAAE;IACTA,KAAK,CAACC,MAAM,GAAGD,KAAK,CAACC,MAAM,IAAID,KAAK,CAACH,IAAI;IACzCG,KAAK,CAACH,IAAI,GAAG,IAAI;EACnB;AACF;AAEA,IAAIK,OAAO,GACX;AACA,YAAY;EACV,SAASA,OAAOA,CAACC,GAAG,EAAE;IACpB,IAAIC,KAAK,GAAG,IAAIhC,OAAO,CAACiC,KAAK,CAAC,CAAC;IAC/B,IAAI,CAACC,GAAG,GAAG7B,MAAM,CAAC,aAAa,CAAC;IAChC,IAAI,CAAC8B,WAAW,GAAG,IAAItC,cAAc,CAACkC,GAAG,CAACK,KAAK,CAAC,CAAC,CAAC;IAClD,IAAI,CAACC,eAAe,GAAG;MACrBC,MAAM,EAAEN;IACV,CAAC;IACD,IAAI,CAACA,KAAK,GAAGA,KAAK;IAClBA,KAAK,CAACO,GAAG,CAAC,IAAI,CAACC,aAAa,GAAG,IAAIxC,OAAO,CAACiC,KAAK,CAAC,CAAC,CAAC;IACnDD,KAAK,CAACO,GAAG,CAAC,IAAI,CAACE,SAAS,GAAG,IAAIzC,OAAO,CAACiC,KAAK,CAAC,CAAC,CAAC;EACjD;EAEAH,OAAO,CAACY,SAAS,CAACC,IAAI,GAAG,UAAUC,aAAa,EAAEC,OAAO,EAAEd,GAAG,EAAEe,QAAQ,EAAEC,OAAO,EAAE;IACjF,IAAIC,KAAK,GAAGJ,aAAa,CAACK,QAAQ,KAAK,KAAK,CAAC,CAAC;IAC9C;;IAEA,IAAIC,IAAI,GAAGN,aAAa,CAACO,OAAO,IAAIP,aAAa,CAACO,OAAO,CAAC,CAAC;IAC3DH,KAAK,IAAIH,OAAO,CAACO,aAAa,CAAC;MAC7BH,QAAQ,EAAE,QAAQ;MAClBI,OAAO,EAAE;IACX,CAAC,EAAE,UAAUC,SAAS,EAAE;MACtB,IAAI,CAACJ,IAAI,IAAII,SAAS,CAACC,eAAe,CAAC,CAAC,KAAKX,aAAa,EAAE;QAC1DM,IAAI,GAAGI,SAAS,CAACH,OAAO,CAAC,CAAC;MAC5B;IACF,CAAC,CAAC;IACF,IAAIK,GAAG,GAAGZ,aAAa,CAACa,gBAAgB;IACxC,IAAIC,YAAY,GAAG,IAAI,CAAClB,aAAa;IACrC,IAAIR,KAAK,GAAG,IAAI,CAACA,KAAK;IACtB,IAAI2B,aAAa,GAAGH,GAAG,CAACI,gBAAgB,CAAC,CAAC;IAC1C,IAAIC,gBAAgB,GAAGF,aAAa,CAACG,GAAG;IACxC,IAAIC,iBAAiB,GAAGJ,aAAa,CAACK,IAAI,CAAC,CAAC;;IAE5C,IAAIC,WAAW,GAAG,CAACP,YAAY,CAACQ,OAAO,CAAC,CAAC,CAAC,IAAInB,OAAO;IAErD,IAAIkB,WAAW,EAAE;MACfjC,KAAK,CAACmC,CAAC,GAAGJ,iBAAiB,CAACI,CAAC;MAC7BnC,KAAK,CAACoC,CAAC,GAAGL,iBAAiB,CAACK,CAAC;MAC7BpC,KAAK,CAACqC,MAAM,GAAGN,iBAAiB,CAACM,MAAM;MACvCrC,KAAK,CAACsC,MAAM,GAAGP,iBAAiB,CAACO,MAAM;MACvCtC,KAAK,CAACuC,KAAK,CAAC,CAAC;IACf,CAAC,MAAM;MACLvE,OAAO,CAACwE,WAAW,CAACxC,KAAK,EAAE+B,iBAAiB,EAAEnB,aAAa,CAAC;IAC9D;IAEA,IAAI6B,0BAA0B,GAAGvB,IAAI,IAAIA,IAAI,CAACwB,SAAS,CAAC,YAAY,CAAC,IAAIxB,IAAI,CAACwB,SAAS,CAAC,YAAY,CAAC,CAACC,MAAM,GAAG,CAAC;IAChH,IAAIC,YAAY,GAAG;MACjB7C,GAAG,EAAEA,GAAG;MACRyB,GAAG,EAAEA,GAAG;MACRZ,aAAa,EAAEA,aAAa;MAC5BM,IAAI,EAAEA,IAAI;MACVuB,0BAA0B,EAAEA,0BAA0B;MACtDzB,KAAK,EAAEA,KAAK;MACZa,gBAAgB,EAAEA;IACpB,CAAC;IAED,IAAIL,GAAG,CAACqB,YAAY,KAAK,SAAS,EAAE;MAClC,IAAI,CAACC,aAAa,CAACF,YAAY,CAAC;IAClC,CAAC,MAAM,IAAIpB,GAAG,CAACqB,YAAY,KAAK,QAAQ,EAAE;MACxC,IAAI,CAACE,SAAS,CAACH,YAAY,CAAC;IAC9B;IAEA,IAAI,CAACI,iBAAiB,CAACpC,aAAa,EAAEC,OAAO,EAAEd,GAAG,CAAC;IAEnD,IAAI,CAACkD,uBAAuB,CAACrC,aAAa,EAAEc,YAAY,EAAE3B,GAAG,EAAEe,QAAQ,CAAC;EAC1E,CAAC;EAEDhB,OAAO,CAACY,SAAS,CAACoC,aAAa,GAAG,UAAUF,YAAY,EAAE;IACxD,IAAIM,kBAAkB,GAAG,IAAI,CAACC,mBAAmB,GAAGvF,MAAM,CAACkB,aAAa,CAAC,CAAC;IAC1E,IAAIsE,iBAAiB,GAAGxF,MAAM,CAACkB,aAAa,CAAC,CAAC;IAC9C,IAAI4C,YAAY,GAAG,IAAI,CAAClB,aAAa;IACrC,IAAIqB,gBAAgB,GAAGe,YAAY,CAACf,gBAAgB;IACpD,IAAIjB,aAAa,GAAGgC,YAAY,CAAChC,aAAa;IAC9C,IAAIM,IAAI,GAAG0B,YAAY,CAAC1B,IAAI;IAC5B,IAAImC,UAAU,GAAGT,YAAY,CAACpB,GAAG,CAAC6B,UAAU;IAC5C,IAAIC,gBAAgB,GAAGD,UAAU,IAAIA,UAAU,CAACE,MAAM;IAEtD,SAASC,cAAcA,CAACC,KAAK,EAAEC,OAAO,EAAE;MACtC,IAAIA,OAAO,EAAE;QACX;QACAD,KAAK,GAAGC,OAAO,CAACD,KAAK,CAAC;MACxB;MAEA,OAAOA,KAAK,IAAI,CAACA,KAAK,CAAC,CAAC,CAAC,GAAG5B,gBAAgB,CAACQ,MAAM,GAAGR,gBAAgB,CAACM,CAAC,EAAEsB,KAAK,CAAC,CAAC,CAAC,GAAG5B,gBAAgB,CAACS,MAAM,GAAGT,gBAAgB,CAACO,CAAC,CAAC;IACpI;IAEA;IAEA,SAASuB,sBAAsBA,CAACC,QAAQ,EAAE;MACxC,IAAIC,SAAS,GAAG,EAAE,CAAC,CAAC;;MAEpB,IAAIH,OAAO,GAAG,CAACJ,gBAAgB,IAAID,UAAU,IAAIA,UAAU,CAACK,OAAO;MAEnE,KAAK,IAAII,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,QAAQ,CAACjB,MAAM,EAAE,EAAEmB,CAAC,EAAE;QACxC,IAAIC,KAAK,GAAGP,cAAc,CAACI,QAAQ,CAACE,CAAC,CAAC,EAAEJ,OAAO,CAAC;QAChDK,KAAK,IAAIF,SAAS,CAACG,IAAI,CAACD,KAAK,CAAC;MAChC;MAEA,OAAOF,SAAS;IAClB;IAEA,SAASI,YAAYA,CAACC,MAAM,EAAE;MAC5B,OAAO;QACLC,KAAK,EAAE;UACLD,MAAM,EAAEP,sBAAsB,CAACO,MAAM;QACvC;MACF,CAAC;IACH;IAEAxC,YAAY,CAAC0C,SAAS,CAAC,CAAC,CAAC,CAAC;;IAE1BxG,MAAM,CAACyG,IAAI,CAACzB,YAAY,CAACpB,GAAG,CAAC8C,OAAO,EAAE,UAAUC,MAAM,EAAE;MACtD,IAAIC,UAAU,GAAGD,MAAM,CAACE,IAAI,CAAC,CAAC;MAC9B;MACA;MACA;MACA;;MAEA,IAAIC,WAAW,GAAGxB,kBAAkB,CAAC1D,GAAG,CAACgF,UAAU,CAAC;MAEpD,IAAIG,EAAE,GAAGvB,iBAAiB,CAAC5D,GAAG,CAACgF,UAAU,CAAC,IAAI,CAAC,CAAC;QAC5CI,OAAO,GAAGD,EAAE,CAACC,OAAO;QACpBC,WAAW,GAAGF,EAAE,CAACE,WAAW;MAEhC,IAAI,CAACH,WAAW,EAAE;QAChBA,WAAW,GAAGxB,kBAAkB,CAAC4B,GAAG,CAACN,UAAU,EAAE,IAAIxG,OAAO,CAACiC,KAAK,CAAC,CAAC,CAAC;QACrEyB,YAAY,CAACnB,GAAG,CAACmE,WAAW,CAAC;QAC7BE,OAAO,GAAG1D,IAAI,GAAGA,IAAI,CAAC6D,WAAW,CAACP,UAAU,CAAC,GAAG,IAAI;QACpDK,WAAW,GAAGjC,YAAY,CAAC5B,KAAK,GAAGJ,aAAa,CAACoE,cAAc,CAACR,UAAU,CAAC,GAAGtD,IAAI,GAAGA,IAAI,CAAC+D,YAAY,CAACL,OAAO,CAAC,GAAG,IAAI;QACtHxB,iBAAiB,CAAC0B,GAAG,CAACN,UAAU,EAAE;UAChCI,OAAO,EAAEA,OAAO;UAChBC,WAAW,EAAEA;QACf,CAAC,CAAC;MACJ;MAEA,IAAIK,eAAe,GAAG,EAAE;MACxB,IAAIC,gBAAgB,GAAG,EAAE;MACzBvH,MAAM,CAACyG,IAAI,CAACE,MAAM,CAACa,UAAU,EAAE,UAAUC,QAAQ,EAAE;QACjD;QACA,IAAIA,QAAQ,CAACC,IAAI,KAAK,SAAS,EAAE;UAC/B,IAAIC,KAAK,GAAG,CAACF,QAAQ,CAACG,QAAQ,CAAC,CAACxG,MAAM,CAACqG,QAAQ,CAACI,SAAS,IAAI,EAAE,CAAC;UAEhE,IAAInC,gBAAgB,EAAE;YACpBiC,KAAK,GAAGG,YAAY,CAACH,KAAK,EAAEjC,gBAAgB,CAAC;UAC/C;UAEA1F,MAAM,CAACyG,IAAI,CAACkB,KAAK,EAAE,UAAUI,IAAI,EAAE;YACjCT,eAAe,CAAClB,IAAI,CAAC,IAAIhG,OAAO,CAAC4H,OAAO,CAAC3B,YAAY,CAAC0B,IAAI,CAAC,CAAC,CAAC;UAC/D,CAAC,CAAC;QACJ,CAAC,CAAC;QAAA,KACG;UACD,IAAIzB,MAAM,GAAGmB,QAAQ,CAACnB,MAAM;UAE5B,IAAIZ,gBAAgB,EAAE;YACpBY,MAAM,GAAGwB,YAAY,CAACxB,MAAM,EAAEZ,gBAAgB,EAAE,IAAI,CAAC;UACvD;UAEA1F,MAAM,CAACyG,IAAI,CAACH,MAAM,EAAE,UAAUA,MAAM,EAAE;YACpCiB,gBAAgB,CAACnB,IAAI,CAAC,IAAIhG,OAAO,CAAC6H,QAAQ,CAAC5B,YAAY,CAACC,MAAM,CAAC,CAAC,CAAC;UACnE,CAAC,CAAC;QACJ;MACJ,CAAC,CAAC;MACF,IAAI4B,QAAQ,GAAGtC,cAAc,CAACe,MAAM,CAACwB,SAAS,CAAC,CAAC,EAAE1C,UAAU,IAAIA,UAAU,CAACK,OAAO,CAAC;MAEnF,SAASsC,kBAAkBA,CAACC,QAAQ,EAAEC,MAAM,EAAE;QAC5C,IAAI,CAACD,QAAQ,CAACtD,MAAM,EAAE;UACpB;QACF;QAEA,IAAIwD,YAAY,GAAG,IAAInI,OAAO,CAACoI,YAAY,CAAC;UAC1CC,OAAO,EAAE,IAAI;UACbC,sBAAsB,EAAE,CAAC;UACzBnC,KAAK,EAAE;YACLoC,KAAK,EAAEN;UACT;QACF,CAAC,CAAC;QACFvB,WAAW,CAACnE,GAAG,CAAC4F,YAAY,CAAC;QAC7BK,yBAAyB,CAAC5D,YAAY,EAAEuD,YAAY,EAAEvB,OAAO,EAAEC,WAAW,CAAC;QAC3E4B,mBAAmB,CAAC7D,YAAY,EAAEuD,YAAY,EAAE3B,UAAU,EAAEK,WAAW,EAAEjE,aAAa,EAAEgE,OAAO,EAAEkB,QAAQ,CAAC;QAE1G,IAAII,MAAM,EAAE;UACVxG,YAAY,CAACyG,YAAY,CAAC;UAC1BvI,MAAM,CAACyG,IAAI,CAAC8B,YAAY,CAACO,MAAM,EAAEhH,YAAY,CAAC;QAChD;MACF;MAEAsG,kBAAkB,CAACd,eAAe,CAAC;MACnCc,kBAAkB,CAACb,gBAAgB,EAAE,IAAI,CAAC;IAC5C,CAAC,CAAC,CAAC,CAAC;;IAEJjC,kBAAkB,CAACmB,IAAI,CAAC,UAAUK,WAAW,EAAEF,UAAU,EAAE;MACzD,IAAIG,EAAE,GAAGvB,iBAAiB,CAAC5D,GAAG,CAACgF,UAAU,CAAC;QACtCI,OAAO,GAAGD,EAAE,CAACC,OAAO;QACpBC,WAAW,GAAGF,EAAE,CAACE,WAAW;MAEhC8B,0BAA0B,CAAC/D,YAAY,EAAE8B,WAAW,EAAEF,UAAU,EAAEK,WAAW,EAAEjE,aAAa,EAAEgE,OAAO,CAAC;MACtGgC,qBAAqB,CAAChE,YAAY,EAAE8B,WAAW,EAAEF,UAAU,EAAEK,WAAW,EAAEjE,aAAa,CAAC;MACxFiG,0BAA0B,CAACjE,YAAY,EAAE8B,WAAW,EAAEF,UAAU,EAAEK,WAAW,EAAEjE,aAAa,CAAC;IAC/F,CAAC,EAAE,IAAI,CAAC;EACV,CAAC;EAEDd,OAAO,CAACY,SAAS,CAACqC,SAAS,GAAG,UAAUH,YAAY,EAAE;IACpD,IAAIkE,OAAO,GAAGlE,YAAY,CAACpB,GAAG,CAACuF,GAAG;IAClC,IAAIlF,gBAAgB,GAAGe,YAAY,CAACf,gBAAgB;IACpD,IAAI,CAACpB,SAAS,CAAC0B,CAAC,GAAGN,gBAAgB,CAACM,CAAC;IACrC,IAAI,CAAC1B,SAAS,CAAC2B,CAAC,GAAGP,gBAAgB,CAACO,CAAC;IACrC,IAAI,CAAC3B,SAAS,CAAC4B,MAAM,GAAGR,gBAAgB,CAACQ,MAAM;IAC/C,IAAI,CAAC5B,SAAS,CAAC6B,MAAM,GAAGT,gBAAgB,CAACS,MAAM;IAE/C,IAAI,IAAI,CAAC0E,mBAAmB,CAACF,OAAO,CAAC,EAAE;MACrC,IAAI,CAACG,QAAQ,CAAC,CAAC;MAEf,IAAI,CAACC,OAAO,CAACJ,OAAO,CAAC;IACvB;IAEA,IAAIK,gBAAgB,GAAG,IAAI,CAACC,iBAAiB,GAAGxJ,MAAM,CAACkB,aAAa,CAAC,CAAC;IACtE,IAAIuI,SAAS,GAAG,KAAK;IACrBzJ,MAAM,CAACyG,IAAI,CAAC,IAAI,CAACiD,iBAAiB,CAACC,KAAK,EAAE,UAAUC,SAAS,EAAE;MAC7D;MACA;MACA;MACA;MACA,IAAIhD,UAAU,GAAGgD,SAAS,CAAC/C,IAAI;MAC/B,IAAI7D,aAAa,GAAGgC,YAAY,CAAChC,aAAa;MAC9C,IAAIM,IAAI,GAAG0B,YAAY,CAAC1B,IAAI;MAC5B,IAAIuG,eAAe,GAAGD,SAAS,CAACC,eAAe;MAC/C,IAAIC,EAAE,GAAGF,SAAS,CAACE,EAAE;MACrB,IAAI9C,OAAO,GAAG1D,IAAI,GAAGA,IAAI,CAAC6D,WAAW,CAACP,UAAU,CAAC,GAAG,IAAI;MACxD,IAAIK,WAAW,GAAGjE,aAAa,CAACoE,cAAc,CAACR,UAAU,CAAC;MAE1D,IAAI3F,4BAA4B,CAACW,GAAG,CAACiI,eAAe,CAAC,IAAI,IAAI,IAAIC,EAAE,YAAYhJ,WAAW,EAAE;QAC1F8H,yBAAyB,CAAC5D,YAAY,EAAE8E,EAAE,EAAE9C,OAAO,EAAEC,WAAW,CAAC;MACnE;MAEA,IAAI6C,EAAE,YAAYhJ,WAAW,EAAE;QAC7BgJ,EAAE,CAACrB,OAAO,GAAG,IAAI;MACnB,CAAC,CAAC;MACF;MACA;;MAGAqB,EAAE,CAACC,cAAc,GAAG,CAAC,CAAC,CAAC;;MAEvB,IAAI,CAACH,SAAS,CAACI,SAAS,EAAE;QACxB;QACA;QACA,IAAI3I,cAAc,CAACO,GAAG,CAACiI,eAAe,CAAC,IAAI,IAAI,EAAE;UAC/ChB,mBAAmB,CAAC7D,YAAY,EAAE8E,EAAE,EAAElD,UAAU,EAAEK,WAAW,EAAEjE,aAAa,EAAEgE,OAAO,EAAE,IAAI,CAAC;QAC9F;QAEA+B,0BAA0B,CAAC/D,YAAY,EAAE8E,EAAE,EAAElD,UAAU,EAAEK,WAAW,EAAEjE,aAAa,EAAEgE,OAAO,CAAC;QAC7FgC,qBAAqB,CAAChE,YAAY,EAAE8E,EAAE,EAAElD,UAAU,EAAEK,WAAW,EAAEjE,aAAa,CAAC;QAE/E,IAAI7B,qBAAqB,CAACS,GAAG,CAACiI,eAAe,CAAC,IAAI,IAAI,EAAE;UACtD,IAAII,OAAO,GAAGhB,0BAA0B,CAACjE,YAAY,EAAE8E,EAAE,EAAElD,UAAU,EAAEK,WAAW,EAAEjE,aAAa,CAAC;UAElG,IAAIiH,OAAO,KAAK,MAAM,EAAE;YACtBR,SAAS,GAAG,IAAI;UAClB;UAEA,IAAIS,GAAG,GAAGX,gBAAgB,CAAC3H,GAAG,CAACgF,UAAU,CAAC,IAAI2C,gBAAgB,CAACrC,GAAG,CAACN,UAAU,EAAE,EAAE,CAAC;UAClFsD,GAAG,CAAC9D,IAAI,CAAC0D,EAAE,CAAC;QACd;MACF;IACF,CAAC,EAAE,IAAI,CAAC;IAER,IAAI,CAACK,oBAAoB,CAACV,SAAS,EAAEzE,YAAY,CAAC;EACpD,CAAC;EAED9C,OAAO,CAACY,SAAS,CAACqH,oBAAoB,GAAG,UAAUV,SAAS,EAAEzE,YAAY,EAAE;IAC1E;IACA;IACA;IACA,IAAIyE,SAAS,IAAIzE,YAAY,CAAC5B,KAAK,EAAE;MACnC,IAAIgH,SAAS,GAAGpF,YAAY,CAAChC,aAAa,CAACqH,QAAQ,CAAC,CAAC,MAAM,EAAE,WAAW,CAAC,CAAC,CAAC3I,YAAY,CAAC,CAAC,CAAC,CAAC;MAC3F;;MAEA,IAAI4I,SAAS,GAAGF,SAAS,CAACG,OAAO;MAEjC,IAAI,CAACb,iBAAiB,CAACc,IAAI,CAACC,QAAQ,CAAC,UAAUX,EAAE,EAAE;QACjD,IAAI,CAACA,EAAE,CAACY,OAAO,EAAE;UACf;UACA;UACAnK,oBAAoB,CAACuJ,EAAE,CAAC;UACxB,IAAI9H,KAAK,GAAG8H,EAAE,CAACa,WAAW,CAAC,MAAM,CAAC,CAAC3I,KAAK,IAAI,CAAC,CAAC,CAAC,CAAC;;UAEhD,IAAIA,KAAK,CAACuI,OAAO,IAAI,IAAI,IAAID,SAAS,IAAI,IAAI,EAAE;YAC9CtI,KAAK,CAACuI,OAAO,GAAGD,SAAS;UAC3B,CAAC,CAAC;UACF;;UAGAR,EAAE,CAACa,WAAW,CAAC,UAAU,CAAC;QAC5B;MACF,CAAC,CAAC;IACJ;EACF,CAAC;EAEDzI,OAAO,CAACY,SAAS,CAAC8H,MAAM,GAAG,YAAY;IACrC,IAAI,CAAChI,aAAa,CAAC4D,SAAS,CAAC,CAAC;IAE9B,IAAI,CAACjB,mBAAmB,GAAG,IAAI;IAE/B,IAAI,CAAC1C,SAAS,CAAC2D,SAAS,CAAC,CAAC;IAE1B,IAAI,CAAC6C,QAAQ,CAAC,CAAC;IAEf,IAAI,CAAC9G,WAAW,CAACsI,OAAO,CAAC,CAAC;IAE1B,IAAI,CAACpI,eAAe,GAAG,IAAI;EAC7B,CAAC;EAEDP,OAAO,CAACY,SAAS,CAACgI,uBAAuB,GAAG,UAAUjE,IAAI,EAAEkE,QAAQ,EAAE;IACpE,IAAIlE,IAAI,IAAI,IAAI,EAAE;MAChB,OAAO,EAAE;IACX;IAEA,IAAIjD,GAAG,GAAGmH,QAAQ,CAAClH,gBAAgB;IAEnC,IAAID,GAAG,CAACqB,YAAY,KAAK,SAAS,EAAE;MAClC,IAAIK,kBAAkB,GAAG,IAAI,CAACC,mBAAmB;MAEjD,IAAID,kBAAkB,EAAE;QACtB,IAAIwB,WAAW,GAAGxB,kBAAkB,CAAC1D,GAAG,CAACiF,IAAI,CAAC;QAC9C,OAAOC,WAAW,GAAG,CAACA,WAAW,CAAC,GAAG,EAAE;MACzC;IACF,CAAC,MAAM,IAAIlD,GAAG,CAACqB,YAAY,KAAK,QAAQ,EAAE;MACxC,OAAO,IAAI,CAACuE,iBAAiB,IAAI,IAAI,CAACA,iBAAiB,CAAC5H,GAAG,CAACiF,IAAI,CAAC,IAAI,EAAE;IACzE;EACF,CAAC;EAED3E,OAAO,CAACY,SAAS,CAACsG,mBAAmB,GAAG,UAAUF,OAAO,EAAE;IACzD,OAAO,IAAI,CAAC8B,WAAW,KAAK9B,OAAO;EACrC,CAAC;EAEDhH,OAAO,CAACY,SAAS,CAACwG,OAAO,GAAG,UAAUJ,OAAO,EAAE;IAC7C,IAAI+B,QAAQ,GAAGzK,gBAAgB,CAAC0K,cAAc,CAAChC,OAAO,CAAC;IAEvD,IAAI+B,QAAQ,IAAIA,QAAQ,CAACvD,IAAI,KAAK,QAAQ,EAAE;MAC1C,IAAIyD,UAAU,GAAGF,QAAQ,CAACG,UAAU,CAAC,IAAI,CAAC9I,GAAG,CAAC;MAE9C,IAAI,CAACO,SAAS,CAACF,GAAG,CAACwI,UAAU,CAACX,IAAI,CAAC;MAEnC,IAAI,CAACd,iBAAiB,GAAGyB,UAAU;MACnC,IAAI,CAACH,WAAW,GAAG9B,OAAO;IAC5B;EACF,CAAC;EAEDhH,OAAO,CAACY,SAAS,CAACuG,QAAQ,GAAG,YAAY;IACvC,IAAIH,OAAO,GAAG,IAAI,CAAC8B,WAAW;IAE9B,IAAI9B,OAAO,IAAI,IAAI,EAAE;MACnB;IACF;IAEA,IAAI+B,QAAQ,GAAGzK,gBAAgB,CAAC0K,cAAc,CAAChC,OAAO,CAAC;IAEvD,IAAI+B,QAAQ,IAAIA,QAAQ,CAACvD,IAAI,KAAK,QAAQ,EAAE;MAC1CuD,QAAQ,CAACI,WAAW,CAAC,IAAI,CAAC/I,GAAG,CAAC;IAChC;IAEA,IAAI,CAACoH,iBAAiB,GAAG,IAAI;IAC7B,IAAI,CAACF,iBAAiB,GAAG,IAAI;IAE7B,IAAI,CAAC3G,SAAS,CAAC2D,SAAS,CAAC,CAAC;IAE1B,IAAI,CAACwE,WAAW,GAAG,IAAI;EACzB,CAAC;EAED9I,OAAO,CAACY,SAAS,CAACsC,iBAAiB,GAAG,UAAUpC,aAAa,EAAEC,OAAO,EAAEd,GAAG,EAAE;IAC3E,IAAIyB,GAAG,GAAGZ,aAAa,CAACa,gBAAgB;IACxC,IAAIyH,UAAU,GAAG,IAAI,CAAC/I,WAAW;IACjC,IAAIgJ,cAAc,GAAG,IAAI,CAAC9I,eAAe,CAAC,CAAC;;IAE3C8I,cAAc,CAACC,SAAS,GAAGxI,aAAa,CAACpB,GAAG,CAAC,YAAY,CAAC;IAC1D2J,cAAc,CAACE,IAAI,GAAG7H,GAAG,CAAC8H,OAAO,CAAC,CAAC,CAAC,CAAC;IACrC;;IAEAJ,UAAU,CAACK,MAAM,CAAC3I,aAAa,CAACpB,GAAG,CAAC,MAAM,CAAC,IAAI,KAAK,CAAC;IACrD,IAAIyB,QAAQ,GAAGL,aAAa,CAACK,QAAQ;IAErC,SAASuI,cAAcA,CAAA,EAAG;MACxB,IAAIC,MAAM,GAAG;QACXnE,IAAI,EAAE,SAAS;QACfoE,aAAa,EAAEzI;MACjB,CAAC;MACDwI,MAAM,CAACxI,QAAQ,GAAG,IAAI,CAAC,GAAGL,aAAa,CAAC+I,EAAE;MAC1C,OAAOF,MAAM;IACf;IAEAP,UAAU,CAACU,GAAG,CAAC,KAAK,CAAC,CAACC,EAAE,CAAC,KAAK,EAAE,UAAUC,CAAC,EAAE;MAC3C,IAAI,CAACC,cAAc,GAAG,KAAK;MAC3BjM,UAAU,CAACkM,eAAe,CAACb,cAAc,EAAEW,CAAC,CAACG,EAAE,EAAEH,CAAC,CAACI,EAAE,CAAC;MACtDnK,GAAG,CAACoK,cAAc,CAACvM,MAAM,CAACwM,MAAM,CAACZ,cAAc,CAAC,CAAC,EAAE;QACjDS,EAAE,EAAEH,CAAC,CAACG,EAAE;QACRC,EAAE,EAAEJ,CAAC,CAACI,EAAE;QACRG,SAAS,EAAE;UACTC,QAAQ,EAAE;QACZ;MACF,CAAC,CAAC,CAAC;IACL,CAAC,EAAE,IAAI,CAAC;IACRpB,UAAU,CAACU,GAAG,CAAC,MAAM,CAAC,CAACC,EAAE,CAAC,MAAM,EAAE,UAAUC,CAAC,EAAE;MAC7C,IAAI,CAACC,cAAc,GAAG,KAAK;MAC3BjM,UAAU,CAACyM,gBAAgB,CAACpB,cAAc,EAAEW,CAAC,CAACU,KAAK,EAAEV,CAAC,CAACW,OAAO,EAAEX,CAAC,CAACY,OAAO,CAAC;MAC1E3K,GAAG,CAACoK,cAAc,CAACvM,MAAM,CAACwM,MAAM,CAACZ,cAAc,CAAC,CAAC,EAAE;QACjDH,IAAI,EAAES,CAAC,CAACU,KAAK;QACbC,OAAO,EAAEX,CAAC,CAACW,OAAO;QAClBC,OAAO,EAAEZ,CAAC,CAACY,OAAO;QAClBL,SAAS,EAAE;UACTC,QAAQ,EAAE;QACZ;MACF,CAAC,CAAC,CAAC;IACL,CAAC,EAAE,IAAI,CAAC;IACRpB,UAAU,CAACyB,iBAAiB,CAAC,UAAUb,CAAC,EAAE3H,CAAC,EAAEC,CAAC,EAAE;MAC9C,OAAOZ,GAAG,CAACoJ,YAAY,CAAC,CAACzI,CAAC,EAAEC,CAAC,CAAC,CAAC,IAAI,CAACrE,mBAAmB,CAAC+L,CAAC,EAAE/J,GAAG,EAAEa,aAAa,CAAC;IAChF,CAAC,CAAC;EACJ,CAAC;EACD;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;EAGEd,OAAO,CAACY,SAAS,CAACmK,mBAAmB,GAAG,YAAY;IAClD,IAAI,CAAC7K,KAAK,CAACqI,QAAQ,CAAC,UAAUX,EAAE,EAAE;MAChC,IAAIoD,KAAK,GAAGpD,EAAE,CAACqD,cAAc,CAAC,CAAC;MAE/B,IAAID,KAAK,EAAE;QACTA,KAAK,CAACE,MAAM,GAAG9L,WAAW,CAAC4L,KAAK,CAAC,CAACE,MAAM;MAC1C;IACF,CAAC,CAAC;EACJ,CAAC;EAEDlL,OAAO,CAACY,SAAS,CAACuC,uBAAuB,GAAG,UAAUrC,aAAa,EAAEc,YAAY,EAAE3B,GAAG,EAAEe,QAAQ,EAAE;IAChG,IAAImK,OAAO,GAAG,IAAI;IAClBvJ,YAAY,CAACkI,GAAG,CAAC,WAAW,CAAC;IAC7BlI,YAAY,CAACkI,GAAG,CAAC,OAAO,CAAC,CAAC,CAAC;;IAE3B,IAAIhJ,aAAa,CAACpB,GAAG,CAAC,cAAc,CAAC,EAAE;MACrCkC,YAAY,CAACmI,EAAE,CAAC,WAAW,EAAE,YAAY;QACvCoB,OAAO,CAAClB,cAAc,GAAG,IAAI;MAC/B,CAAC,CAAC;MACFrI,YAAY,CAACmI,EAAE,CAAC,OAAO,EAAE,UAAUC,CAAC,EAAE;QACpC,IAAI,CAACmB,OAAO,CAAClB,cAAc,EAAE;UAC3B;QACF;QAEAkB,OAAO,CAAClB,cAAc,GAAG,KAAK;MAChC,CAAC,CAAC;IACJ;EACF,CAAC;EAED,OAAOjK,OAAO;AAChB,CAAC,CAAC,CAAC;AAEH;AAEA,SAAS0G,yBAAyBA,CAAC5D,YAAY,EAAE8E,EAAE,EAAEwD,SAAS,EAAErG,WAAW,EAAE;EAC3E;EACA;EACA;EACA;EACA;EACA;EACA;EACA,IAAIsG,gBAAgB,GAAGtG,WAAW,CAACoD,QAAQ,CAAC,WAAW,CAAC;EACxD,IAAImD,kBAAkB,GAAGvG,WAAW,CAACoD,QAAQ,CAAC,CAAC,UAAU,EAAE,WAAW,CAAC,CAAC;EACxE,IAAIoD,cAAc,GAAGxG,WAAW,CAACoD,QAAQ,CAAC,CAAC,MAAM,EAAE,WAAW,CAAC,CAAC;EAChE,IAAIqD,gBAAgB,GAAGzG,WAAW,CAACoD,QAAQ,CAAC,CAAC,QAAQ,EAAE,WAAW,CAAC,CAAC,CAAC,CAAC;EACtE;;EAEA,IAAIsD,WAAW,GAAGpM,iBAAiB,CAACgM,gBAAgB,CAAC;EACrD,IAAIK,aAAa,GAAGrM,iBAAiB,CAACiM,kBAAkB,CAAC;EACzD,IAAIK,WAAW,GAAGtM,iBAAiB,CAACmM,gBAAgB,CAAC;EACrD,IAAItD,SAAS,GAAG7I,iBAAiB,CAACkM,cAAc,CAAC,CAAC,CAAC;;EAEnD,IAAInK,IAAI,GAAG0B,YAAY,CAAC1B,IAAI;EAE5B,IAAIA,IAAI,EAAE;IACR;IACA;IACA;IACA,IAAItB,KAAK,GAAGsB,IAAI,CAACwK,aAAa,CAACR,SAAS,EAAE,OAAO,CAAC;IAClD,IAAIS,KAAK,GAAGzK,IAAI,CAACwK,aAAa,CAACR,SAAS,EAAE,OAAO,CAAC;IAElD,IAAItI,YAAY,CAACH,0BAA0B,IAAI7C,KAAK,CAACH,IAAI,EAAE;MACzD8L,WAAW,CAAC9L,IAAI,GAAGG,KAAK,CAACH,IAAI;IAC/B;IAEA,IAAIkM,KAAK,EAAE;MACTJ,WAAW,CAACI,KAAK,GAAGlN,8BAA8B,CAACkN,KAAK,EAAE/I,YAAY,CAAC7C,GAAG,CAAC;IAC7E;EACF,CAAC,CAAC;EACF;;EAGA2H,EAAE,CAACkE,QAAQ,CAACL,WAAW,CAAC;EACxB7D,EAAE,CAAC9H,KAAK,CAACiM,aAAa,GAAG,IAAI;EAC7BnE,EAAE,CAACa,WAAW,CAAC,UAAU,CAAC,CAAC3I,KAAK,GAAG4L,aAAa;EAChD9D,EAAE,CAACa,WAAW,CAAC,QAAQ,CAAC,CAAC3I,KAAK,GAAG6L,WAAW;EAC5C/D,EAAE,CAACa,WAAW,CAAC,MAAM,CAAC,CAAC3I,KAAK,GAAGoI,SAAS,CAAC,CAAC;;EAE1C7J,oBAAoB,CAACuJ,EAAE,CAAC;AAC1B;AAEA,SAASjB,mBAAmBA,CAAC7D,YAAY,EAAE8E,EAAE,EAAElD,UAAU,EAAEK,WAAW,EAAEjE,aAAa;AAAE;AACvFgE,OAAO;AAAE;AACTkH,OAAO,EAAE;EACP,IAAI5K,IAAI,GAAG0B,YAAY,CAAC1B,IAAI;EAC5B,IAAIF,KAAK,GAAG4B,YAAY,CAAC5B,KAAK;EAC9B,IAAI+K,SAAS,GAAG7K,IAAI,IAAI8K,KAAK,CAAC9K,IAAI,CAAC1B,GAAG,CAAC0B,IAAI,CAAC+K,YAAY,CAAC,OAAO,CAAC,EAAErH,OAAO,CAAC,CAAC;EAC5E,IAAIsH,UAAU,GAAGhL,IAAI,IAAIA,IAAI,CAACiL,aAAa,CAACvH,OAAO,CAAC,CAAC,CAAC;EACtD;EACA;EACA;;EAEA,IAAI5D,KAAK,IAAI+K,SAAS,IAAIG,UAAU,IAAIA,UAAU,CAACE,SAAS,EAAE;IAC5D,IAAIC,KAAK,GAAG,CAACrL,KAAK,GAAG4D,OAAO,GAAGJ,UAAU;IACzC,IAAI8H,YAAY,GAAG,KAAK,CAAC,CAAC,CAAC;;IAE3B,IAAI,CAACpL,IAAI,IAAI0D,OAAO,IAAI,CAAC,EAAE;MACzB0H,YAAY,GAAG1L,aAAa;IAC9B;IAEA,IAAI2L,gBAAgB,GAAGT,OAAO,GAAG;MAC/BU,MAAM,EAAE;QACNC,KAAK,EAAE,QAAQ;QACfC,aAAa,EAAE;MACjB;IACF,CAAC,GAAG,IAAI,CAAC,CAAC;IACV;;IAEApO,aAAa,CAACoJ,EAAE,EAAEnJ,oBAAoB,CAACsG,WAAW,CAAC,EAAE;MACnDyH,YAAY,EAAEA,YAAY;MAC1BK,cAAc,EAAEN,KAAK;MACrBO,WAAW,EAAEpI;IACf,CAAC,EAAE+H,gBAAgB,CAAC;IACpB,IAAIM,MAAM,GAAGnF,EAAE,CAACqD,cAAc,CAAC,CAAC;IAEhC,IAAI8B,MAAM,EAAE;MACV3N,WAAW,CAAC2N,MAAM,CAAC,CAAC7B,MAAM,GAAG6B,MAAM,CAAC7B,MAAM;MAE1C,IAAItD,EAAE,CAACoF,UAAU,IAAIhB,OAAO,EAAE;QAC5B;QACA,IAAIiB,IAAI,GAAGrF,EAAE,CAACsF,eAAe,CAAC,CAAC,CAACC,KAAK,CAAC,CAAC,CAAC,CAAC;QACzC;QACA;QACA;;QAEAvF,EAAE,CAACoF,UAAU,CAACI,UAAU,GAAGH,IAAI;QAC/BrF,EAAE,CAACoF,UAAU,CAACK,QAAQ,GAAG,CAAC,CAACrB,OAAO,CAAC,CAAC,CAAC,GAAGiB,IAAI,CAAC5K,CAAC,IAAI4K,IAAI,CAACK,KAAK,GAAG,GAAG,GAAG,GAAG,EAAE,CAACtB,OAAO,CAAC,CAAC,CAAC,GAAGiB,IAAI,CAAC3K,CAAC,IAAI2K,IAAI,CAACM,MAAM,GAAG,GAAG,GAAG,GAAG,CAAC;MAC5H;IACF,CAAC,CAAC;IACF;IACA;IACA;IACA;IACA;;IAGA3F,EAAE,CAAC4F,qBAAqB,GAAG,IAAI;EACjC,CAAC,MAAM;IACL5F,EAAE,CAAC6F,iBAAiB,CAAC,CAAC;IACtB7F,EAAE,CAAC8F,gBAAgB,CAAC,CAAC;IACrB9F,EAAE,CAAC4F,qBAAqB,GAAG,IAAI;EACjC;AACF;AAEA,SAAS3G,0BAA0BA,CAAC/D,YAAY,EAAE6K,YAAY,EAAEjJ,UAAU,EAAEK,WAAW,EAAEjE,aAAa;AAAE;AACxGgE,OAAO,EAAE;EACP;EACA;EACA,IAAIhC,YAAY,CAAC1B,IAAI,EAAE;IACrB;IACA;IACA;IACA;IACA;IACA;IACA;IACA0B,YAAY,CAAC1B,IAAI,CAACwM,gBAAgB,CAAC9I,OAAO,EAAE6I,YAAY,CAAC;EAC3D,CAAC,CAAC;EACF;EACA;EACA;EACA;EAAA,KACK;IACD;IACAjP,SAAS,CAACiP,YAAY,CAAC,CAACE,SAAS,GAAG;MAClCjE,aAAa,EAAE,KAAK;MACpBkE,cAAc,EAAEhN,aAAa,CAACgN,cAAc;MAC5CC,QAAQ,EAAEjN,aAAa,CAACgN,cAAc;MACtCnJ,IAAI,EAAED,UAAU;MAChBD,MAAM,EAAEM,WAAW,IAAIA,WAAW,CAACiJ,MAAM,IAAI,CAAC;IAChD,CAAC;EACH;AACJ;AAEA,SAASlH,qBAAqBA,CAAChE,YAAY,EAAE8E,EAAE,EAAElD,UAAU,EAAEK,WAAW,EAAEjE,aAAa,EAAE;EACvF,IAAI,CAACgC,YAAY,CAAC1B,IAAI,EAAE;IACtBlD,OAAO,CAAC+P,gBAAgB,CAAC;MACvBrG,EAAE,EAAEA,EAAE;MACNsG,cAAc,EAAEpN,aAAa;MAC7BqN,QAAQ,EAAEzJ,UAAU;MACpB;MACA0J,iBAAiB,EAAErJ,WAAW,CAACrF,GAAG,CAAC,SAAS;IAC9C,CAAC,CAAC;EACJ;AACF;AAEA,SAASqH,0BAA0BA,CAACjE,YAAY,EAAE8E,EAAE,EAAElD,UAAU,EAAEK,WAAW,EAAEjE,aAAa,EAAE;EAC5F;EACA8G,EAAE,CAACyG,qBAAqB,GAAG,CAAC,CAACvN,aAAa,CAACpB,GAAG,CAAC,cAAc,CAAC,CAAC,CAAC;;EAEhE,IAAI4O,aAAa,GAAGvJ,WAAW,CAACoD,QAAQ,CAAC,UAAU,CAAC;EACpD,IAAIoG,KAAK,GAAGD,aAAa,CAAC5O,GAAG,CAAC,OAAO,CAAC;EACtCvB,mBAAmB,CAACyJ,EAAE,EAAE2G,KAAK,EAAED,aAAa,CAAC5O,GAAG,CAAC,WAAW,CAAC,EAAE4O,aAAa,CAAC5O,GAAG,CAAC,UAAU,CAAC,CAAC;EAE7F,IAAIoD,YAAY,CAAC5B,KAAK,EAAE;IACtB9C,+BAA+B,CAACwJ,EAAE,EAAE9G,aAAa,EAAE4D,UAAU,CAAC;EAChE;EAEA,OAAO6J,KAAK;AACd;AAEA,SAAS3I,YAAYA,CAAC4I,KAAK;AAAE;AAC7BC,YAAY,EAAErI,MAAM,EAAE;EACpB,IAAIsI,QAAQ,GAAG,EAAE;EACjB,IAAIC,OAAO;EAEX,SAASC,YAAYA,CAAA,EAAG;IACtBD,OAAO,GAAG,EAAE;EACd;EAEA,SAASE,UAAUA,CAAA,EAAG;IACpB,IAAIF,OAAO,CAAC9L,MAAM,EAAE;MAClB6L,QAAQ,CAACxK,IAAI,CAACyK,OAAO,CAAC;MACtBA,OAAO,GAAG,EAAE;IACd;EACF;EAEA,IAAIlL,MAAM,GAAGgL,YAAY,CAAC;IACxBK,YAAY,EAAEF,YAAY;IAC1BG,UAAU,EAAEF,UAAU;IACtBG,SAAS,EAAEJ,YAAY;IACvBK,OAAO,EAAEJ,UAAU;IACnBlL,KAAK,EAAE,SAAAA,CAAUtB,CAAC,EAAEC,CAAC,EAAE;MACrB;MACA,IAAI4M,QAAQ,CAAC7M,CAAC,CAAC,IAAI6M,QAAQ,CAAC5M,CAAC,CAAC,EAAE;QAC9BqM,OAAO,CAACzK,IAAI,CAAC,CAAC7B,CAAC,EAAEC,CAAC,CAAC,CAAC;MACtB;IACF,CAAC;IACD6M,MAAM,EAAE,SAAAA,CAAA,EAAY,CAAC;EACvB,CAAC,CAAC;EACF,CAAC/I,MAAM,IAAI3C,MAAM,CAACqL,YAAY,CAAC,CAAC;EAChChR,MAAM,CAACyG,IAAI,CAACiK,KAAK,EAAE,UAAUY,IAAI,EAAE;IACjC3L,MAAM,CAACuL,SAAS,CAAC,CAAC;IAElB,KAAK,IAAIhL,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGoL,IAAI,CAACvM,MAAM,EAAEmB,CAAC,EAAE,EAAE;MACpCP,MAAM,CAACE,KAAK,CAACyL,IAAI,CAACpL,CAAC,CAAC,CAAC,CAAC,CAAC,EAAEoL,IAAI,CAACpL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACtC;IAEAP,MAAM,CAACwL,OAAO,CAAC,CAAC;EAClB,CAAC,CAAC;EACF,CAAC7I,MAAM,IAAI3C,MAAM,CAACsL,UAAU,CAAC,CAAC;EAC9B,OAAOL,QAAQ;AACjB;AAEA,eAAe1O,OAAO,CAAC,CAAC"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}