import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';

interface BannedIP {
  ip: string;
  country: string;
  countryCode: string;
  reason: string;
  bannedAt: string;
  attempts: number;
}

@Component({
  selector: 'app-ban-management',
  standalone: true,
  imports: [CommonModule, FormsModule],
  template: `
    <div class="ban-management-container">
      <div class="ban-input-section">
        <div class="input-group">
          <input 
            type="text" 
            [(ngModel)]="newIpToBan"
            placeholder="Digite o IP"
            class="ip-input"
            (keyup.enter)="banIP()"
          >
          <button 
            class="ban-button"
            (click)="banIP()"
            [disabled]="!isValidIP(newIpToBan)"
          >
            BAN
          </button>
        </div>
      </div>

      <div class="banned-list">
        <div class="list-header">
          <span class="banned-count">{{ bannedIPs.length }} IPs banidos</span>
        </div>
        
        <div class="banned-items">
          <div 
            *ngFor="let banned of bannedIPs; trackBy: trackByIP" 
            class="banned-item"
          >
            <div class="banned-info">
              <div class="banned-ip">{{ banned.ip }}</div>
              <div class="banned-details">
                <span class="banned-flag">{{ getCountryFlag(banned.countryCode) }}</span>
                <span class="banned-country">{{ banned.country }}</span>
                <span class="banned-attempts">{{ banned.attempts }} tentativas</span>
              </div>
            </div>
            <button 
              class="unban-button"
              (click)="unbanIP(banned.ip)"
              title="Desbanir IP"
            >
              UNBAN
            </button>
          </div>
        </div>
      </div>

      <div class="empty-state" *ngIf="bannedIPs.length === 0">
        <div class="empty-icon">🚫</div>
        <div class="empty-text">Nenhum IP banido</div>
      </div>

      <div class="ban-stats">
        <div class="stat-item">
          <span class="stat-value">{{ bannedIPs.length }}</span>
          <span class="stat-label">Banidos</span>
        </div>
        <div class="stat-item">
          <span class="stat-value">{{ getTotalAttempts() }}</span>
          <span class="stat-label">Tentativas</span>
        </div>
      </div>
    </div>
  `,
  styleUrls: ['./ban-management.component.scss']
})
export class BanManagementComponent {
  newIpToBan = '';
  bannedIPs: BannedIP[] = [
    {
      ip: '**********',
      country: 'Estados Unidos',
      countryCode: 'US',
      reason: 'Múltiplas tentativas',
      bannedAt: '10:25:30',
      attempts: 8
    }
  ];

  banIP() {
    if (!this.isValidIP(this.newIpToBan)) return;

    const newBan: BannedIP = {
      ip: this.newIpToBan,
      country: 'Desconhecido',
      countryCode: 'XX',
      reason: 'Banimento manual',
      bannedAt: new Date().toLocaleTimeString(),
      attempts: 1
    };

    this.bannedIPs.unshift(newBan);
    this.newIpToBan = '';

    // TODO: Enviar comando de banimento via WebSocket
    console.log('🚫 IP banido:', newBan.ip);
  }

  unbanIP(ip: string) {
    this.bannedIPs = this.bannedIPs.filter(banned => banned.ip !== ip);
    
    // TODO: Enviar comando de desbanimento via WebSocket
    console.log('✅ IP desbanido:', ip);
  }

  isValidIP(ip: string): boolean {
    const ipRegex = /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/;
    return ipRegex.test(ip);
  }

  trackByIP(index: number, banned: BannedIP): string {
    return banned.ip;
  }

  getCountryFlag(countryCode: string): string {
    const flags: { [key: string]: string } = {
      'BR': '🇧🇷',
      'US': '🇺🇸', 
      'DE': '🇩🇪',
      'FR': '🇫🇷',
      'GB': '🇬🇧',
      'CN': '🇨🇳',
      'JP': '🇯🇵',
      'RU': '🇷🇺',
      'CA': '🇨🇦',
      'AU': '🇦🇺',
      'IT': '🇮🇹',
      'ES': '🇪🇸',
      'NL': '🇳🇱',
      'IN': '🇮🇳',
      'MX': '🇲🇽',
      'AR': '🇦🇷',
      'LO': '🏠'
    };
    return flags[countryCode] || '🌍';
  }

  getTotalAttempts(): number {
    return this.bannedIPs.reduce((total, banned) => total + banned.attempts, 0);
  }
}
