{"ast": null, "code": "import { encodePacket, encodePacketToBinary } from \"./encodePacket.js\";\nimport { decodePacket } from \"./decodePacket.js\";\nimport { ERROR_PACKET } from \"./commons.js\";\nconst SEPARATOR = String.fromCharCode(30); // see https://en.wikipedia.org/wiki/Delimiter#ASCII_delimited_text\nconst encodePayload = (packets, callback) => {\n  // some packets may be added to the array while encoding, so the initial length must be saved\n  const length = packets.length;\n  const encodedPackets = new Array(length);\n  let count = 0;\n  packets.forEach((packet, i) => {\n    // force base64 encoding for binary packets\n    encodePacket(packet, false, encodedPacket => {\n      encodedPackets[i] = encodedPacket;\n      if (++count === length) {\n        callback(encodedPackets.join(SEPARATOR));\n      }\n    });\n  });\n};\nconst decodePayload = (encodedPayload, binaryType) => {\n  const encodedPackets = encodedPayload.split(SEPARATOR);\n  const packets = [];\n  for (let i = 0; i < encodedPackets.length; i++) {\n    const decodedPacket = decodePacket(encodedPackets[i], binaryType);\n    packets.push(decodedPacket);\n    if (decodedPacket.type === \"error\") {\n      break;\n    }\n  }\n  return packets;\n};\nexport function createPacketEncoderStream() {\n  return new TransformStream({\n    transform(packet, controller) {\n      encodePacketToBinary(packet, encodedPacket => {\n        const payloadLength = encodedPacket.length;\n        let header;\n        // inspired by the WebSocket format: https://developer.mozilla.org/en-US/docs/Web/API/WebSockets_API/Writing_WebSocket_servers#decoding_payload_length\n        if (payloadLength < 126) {\n          header = new Uint8Array(1);\n          new DataView(header.buffer).setUint8(0, payloadLength);\n        } else if (payloadLength < 65536) {\n          header = new Uint8Array(3);\n          const view = new DataView(header.buffer);\n          view.setUint8(0, 126);\n          view.setUint16(1, payloadLength);\n        } else {\n          header = new Uint8Array(9);\n          const view = new DataView(header.buffer);\n          view.setUint8(0, 127);\n          view.setBigUint64(1, BigInt(payloadLength));\n        }\n        // first bit indicates whether the payload is plain text (0) or binary (1)\n        if (packet.data && typeof packet.data !== \"string\") {\n          header[0] |= 0x80;\n        }\n        controller.enqueue(header);\n        controller.enqueue(encodedPacket);\n      });\n    }\n  });\n}\nlet TEXT_DECODER;\nfunction totalLength(chunks) {\n  return chunks.reduce((acc, chunk) => acc + chunk.length, 0);\n}\nfunction concatChunks(chunks, size) {\n  if (chunks[0].length === size) {\n    return chunks.shift();\n  }\n  const buffer = new Uint8Array(size);\n  let j = 0;\n  for (let i = 0; i < size; i++) {\n    buffer[i] = chunks[0][j++];\n    if (j === chunks[0].length) {\n      chunks.shift();\n      j = 0;\n    }\n  }\n  if (chunks.length && j < chunks[0].length) {\n    chunks[0] = chunks[0].slice(j);\n  }\n  return buffer;\n}\nexport function createPacketDecoderStream(maxPayload, binaryType) {\n  if (!TEXT_DECODER) {\n    TEXT_DECODER = new TextDecoder();\n  }\n  const chunks = [];\n  let state = 0 /* State.READ_HEADER */;\n  let expectedLength = -1;\n  let isBinary = false;\n  return new TransformStream({\n    transform(chunk, controller) {\n      chunks.push(chunk);\n      while (true) {\n        if (state === 0 /* State.READ_HEADER */) {\n          if (totalLength(chunks) < 1) {\n            break;\n          }\n          const header = concatChunks(chunks, 1);\n          isBinary = (header[0] & 0x80) === 0x80;\n          expectedLength = header[0] & 0x7f;\n          if (expectedLength < 126) {\n            state = 3 /* State.READ_PAYLOAD */;\n          } else if (expectedLength === 126) {\n            state = 1 /* State.READ_EXTENDED_LENGTH_16 */;\n          } else {\n            state = 2 /* State.READ_EXTENDED_LENGTH_64 */;\n          }\n        } else if (state === 1 /* State.READ_EXTENDED_LENGTH_16 */) {\n          if (totalLength(chunks) < 2) {\n            break;\n          }\n          const headerArray = concatChunks(chunks, 2);\n          expectedLength = new DataView(headerArray.buffer, headerArray.byteOffset, headerArray.length).getUint16(0);\n          state = 3 /* State.READ_PAYLOAD */;\n        } else if (state === 2 /* State.READ_EXTENDED_LENGTH_64 */) {\n          if (totalLength(chunks) < 8) {\n            break;\n          }\n          const headerArray = concatChunks(chunks, 8);\n          const view = new DataView(headerArray.buffer, headerArray.byteOffset, headerArray.length);\n          const n = view.getUint32(0);\n          if (n > Math.pow(2, 53 - 32) - 1) {\n            // the maximum safe integer in JavaScript is 2^53 - 1\n            controller.enqueue(ERROR_PACKET);\n            break;\n          }\n          expectedLength = n * Math.pow(2, 32) + view.getUint32(4);\n          state = 3 /* State.READ_PAYLOAD */;\n        } else {\n          if (totalLength(chunks) < expectedLength) {\n            break;\n          }\n          const data = concatChunks(chunks, expectedLength);\n          controller.enqueue(decodePacket(isBinary ? data : TEXT_DECODER.decode(data), binaryType));\n          state = 0 /* State.READ_HEADER */;\n        }\n\n        if (expectedLength === 0 || expectedLength > maxPayload) {\n          controller.enqueue(ERROR_PACKET);\n          break;\n        }\n      }\n    }\n  });\n}\nexport const protocol = 4;\nexport { encodePacket, encodePayload, decodePacket, decodePayload };", "map": {"version": 3, "names": ["encodePacket", "encodePacketToBinary", "decodePacket", "ERROR_PACKET", "SEPARATOR", "String", "fromCharCode", "encodePayload", "packets", "callback", "length", "encodedPackets", "Array", "count", "for<PERSON>ach", "packet", "i", "encodedPacket", "join", "decodePayload", "encodedPayload", "binaryType", "split", "decodedPacket", "push", "type", "createPacketEncoderStream", "TransformStream", "transform", "controller", "payloadLength", "header", "Uint8Array", "DataView", "buffer", "setUint8", "view", "setUint16", "setBigUint64", "BigInt", "data", "enqueue", "TEXT_DECODER", "totalLength", "chunks", "reduce", "acc", "chunk", "concatChunks", "size", "shift", "j", "slice", "createPacketDecoderStream", "maxPayload", "TextDecoder", "state", "<PERSON><PERSON><PERSON><PERSON>", "isBinary", "headerArray", "byteOffset", "getUint16", "n", "getUint32", "Math", "pow", "decode", "protocol"], "sources": ["C:/WolfSystemV2/WolfOldFrontEnd/node_modules/engine.io-client/node_modules/engine.io-parser/build/esm/index.js"], "sourcesContent": ["import { encodePacket, encodePacketToBinary } from \"./encodePacket.js\";\nimport { decodePacket } from \"./decodePacket.js\";\nimport { ERROR_PACKET, } from \"./commons.js\";\nconst SEPARATOR = String.fromCharCode(30); // see https://en.wikipedia.org/wiki/Delimiter#ASCII_delimited_text\nconst encodePayload = (packets, callback) => {\n    // some packets may be added to the array while encoding, so the initial length must be saved\n    const length = packets.length;\n    const encodedPackets = new Array(length);\n    let count = 0;\n    packets.forEach((packet, i) => {\n        // force base64 encoding for binary packets\n        encodePacket(packet, false, (encodedPacket) => {\n            encodedPackets[i] = encodedPacket;\n            if (++count === length) {\n                callback(encodedPackets.join(SEPARATOR));\n            }\n        });\n    });\n};\nconst decodePayload = (encodedPayload, binaryType) => {\n    const encodedPackets = encodedPayload.split(SEPARATOR);\n    const packets = [];\n    for (let i = 0; i < encodedPackets.length; i++) {\n        const decodedPacket = decodePacket(encodedPackets[i], binaryType);\n        packets.push(decodedPacket);\n        if (decodedPacket.type === \"error\") {\n            break;\n        }\n    }\n    return packets;\n};\nexport function createPacketEncoderStream() {\n    return new TransformStream({\n        transform(packet, controller) {\n            encodePacketToBinary(packet, (encodedPacket) => {\n                const payloadLength = encodedPacket.length;\n                let header;\n                // inspired by the WebSocket format: https://developer.mozilla.org/en-US/docs/Web/API/WebSockets_API/Writing_WebSocket_servers#decoding_payload_length\n                if (payloadLength < 126) {\n                    header = new Uint8Array(1);\n                    new DataView(header.buffer).setUint8(0, payloadLength);\n                }\n                else if (payloadLength < 65536) {\n                    header = new Uint8Array(3);\n                    const view = new DataView(header.buffer);\n                    view.setUint8(0, 126);\n                    view.setUint16(1, payloadLength);\n                }\n                else {\n                    header = new Uint8Array(9);\n                    const view = new DataView(header.buffer);\n                    view.setUint8(0, 127);\n                    view.setBigUint64(1, BigInt(payloadLength));\n                }\n                // first bit indicates whether the payload is plain text (0) or binary (1)\n                if (packet.data && typeof packet.data !== \"string\") {\n                    header[0] |= 0x80;\n                }\n                controller.enqueue(header);\n                controller.enqueue(encodedPacket);\n            });\n        },\n    });\n}\nlet TEXT_DECODER;\nfunction totalLength(chunks) {\n    return chunks.reduce((acc, chunk) => acc + chunk.length, 0);\n}\nfunction concatChunks(chunks, size) {\n    if (chunks[0].length === size) {\n        return chunks.shift();\n    }\n    const buffer = new Uint8Array(size);\n    let j = 0;\n    for (let i = 0; i < size; i++) {\n        buffer[i] = chunks[0][j++];\n        if (j === chunks[0].length) {\n            chunks.shift();\n            j = 0;\n        }\n    }\n    if (chunks.length && j < chunks[0].length) {\n        chunks[0] = chunks[0].slice(j);\n    }\n    return buffer;\n}\nexport function createPacketDecoderStream(maxPayload, binaryType) {\n    if (!TEXT_DECODER) {\n        TEXT_DECODER = new TextDecoder();\n    }\n    const chunks = [];\n    let state = 0 /* State.READ_HEADER */;\n    let expectedLength = -1;\n    let isBinary = false;\n    return new TransformStream({\n        transform(chunk, controller) {\n            chunks.push(chunk);\n            while (true) {\n                if (state === 0 /* State.READ_HEADER */) {\n                    if (totalLength(chunks) < 1) {\n                        break;\n                    }\n                    const header = concatChunks(chunks, 1);\n                    isBinary = (header[0] & 0x80) === 0x80;\n                    expectedLength = header[0] & 0x7f;\n                    if (expectedLength < 126) {\n                        state = 3 /* State.READ_PAYLOAD */;\n                    }\n                    else if (expectedLength === 126) {\n                        state = 1 /* State.READ_EXTENDED_LENGTH_16 */;\n                    }\n                    else {\n                        state = 2 /* State.READ_EXTENDED_LENGTH_64 */;\n                    }\n                }\n                else if (state === 1 /* State.READ_EXTENDED_LENGTH_16 */) {\n                    if (totalLength(chunks) < 2) {\n                        break;\n                    }\n                    const headerArray = concatChunks(chunks, 2);\n                    expectedLength = new DataView(headerArray.buffer, headerArray.byteOffset, headerArray.length).getUint16(0);\n                    state = 3 /* State.READ_PAYLOAD */;\n                }\n                else if (state === 2 /* State.READ_EXTENDED_LENGTH_64 */) {\n                    if (totalLength(chunks) < 8) {\n                        break;\n                    }\n                    const headerArray = concatChunks(chunks, 8);\n                    const view = new DataView(headerArray.buffer, headerArray.byteOffset, headerArray.length);\n                    const n = view.getUint32(0);\n                    if (n > Math.pow(2, 53 - 32) - 1) {\n                        // the maximum safe integer in JavaScript is 2^53 - 1\n                        controller.enqueue(ERROR_PACKET);\n                        break;\n                    }\n                    expectedLength = n * Math.pow(2, 32) + view.getUint32(4);\n                    state = 3 /* State.READ_PAYLOAD */;\n                }\n                else {\n                    if (totalLength(chunks) < expectedLength) {\n                        break;\n                    }\n                    const data = concatChunks(chunks, expectedLength);\n                    controller.enqueue(decodePacket(isBinary ? data : TEXT_DECODER.decode(data), binaryType));\n                    state = 0 /* State.READ_HEADER */;\n                }\n                if (expectedLength === 0 || expectedLength > maxPayload) {\n                    controller.enqueue(ERROR_PACKET);\n                    break;\n                }\n            }\n        },\n    });\n}\nexport const protocol = 4;\nexport { encodePacket, encodePayload, decodePacket, decodePayload, };\n"], "mappings": "AAAA,SAASA,YAAY,EAAEC,oBAAoB,QAAQ,mBAAmB;AACtE,SAASC,YAAY,QAAQ,mBAAmB;AAChD,SAASC,YAAY,QAAS,cAAc;AAC5C,MAAMC,SAAS,GAAGC,MAAM,CAACC,YAAY,CAAC,EAAE,CAAC,CAAC,CAAC;AAC3C,MAAMC,aAAa,GAAGA,CAACC,OAAO,EAAEC,QAAQ,KAAK;EACzC;EACA,MAAMC,MAAM,GAAGF,OAAO,CAACE,MAAM;EAC7B,MAAMC,cAAc,GAAG,IAAIC,KAAK,CAACF,MAAM,CAAC;EACxC,IAAIG,KAAK,GAAG,CAAC;EACbL,OAAO,CAACM,OAAO,CAAC,CAACC,MAAM,EAAEC,CAAC,KAAK;IAC3B;IACAhB,YAAY,CAACe,MAAM,EAAE,KAAK,EAAGE,aAAa,IAAK;MAC3CN,cAAc,CAACK,CAAC,CAAC,GAAGC,aAAa;MACjC,IAAI,EAAEJ,KAAK,KAAKH,MAAM,EAAE;QACpBD,QAAQ,CAACE,cAAc,CAACO,IAAI,CAACd,SAAS,CAAC,CAAC;MAC5C;IACJ,CAAC,CAAC;EACN,CAAC,CAAC;AACN,CAAC;AACD,MAAMe,aAAa,GAAGA,CAACC,cAAc,EAAEC,UAAU,KAAK;EAClD,MAAMV,cAAc,GAAGS,cAAc,CAACE,KAAK,CAAClB,SAAS,CAAC;EACtD,MAAMI,OAAO,GAAG,EAAE;EAClB,KAAK,IAAIQ,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGL,cAAc,CAACD,MAAM,EAAEM,CAAC,EAAE,EAAE;IAC5C,MAAMO,aAAa,GAAGrB,YAAY,CAACS,cAAc,CAACK,CAAC,CAAC,EAAEK,UAAU,CAAC;IACjEb,OAAO,CAACgB,IAAI,CAACD,aAAa,CAAC;IAC3B,IAAIA,aAAa,CAACE,IAAI,KAAK,OAAO,EAAE;MAChC;IACJ;EACJ;EACA,OAAOjB,OAAO;AAClB,CAAC;AACD,OAAO,SAASkB,yBAAyBA,CAAA,EAAG;EACxC,OAAO,IAAIC,eAAe,CAAC;IACvBC,SAASA,CAACb,MAAM,EAAEc,UAAU,EAAE;MAC1B5B,oBAAoB,CAACc,MAAM,EAAGE,aAAa,IAAK;QAC5C,MAAMa,aAAa,GAAGb,aAAa,CAACP,MAAM;QAC1C,IAAIqB,MAAM;QACV;QACA,IAAID,aAAa,GAAG,GAAG,EAAE;UACrBC,MAAM,GAAG,IAAIC,UAAU,CAAC,CAAC,CAAC;UAC1B,IAAIC,QAAQ,CAACF,MAAM,CAACG,MAAM,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAEL,aAAa,CAAC;QAC1D,CAAC,MACI,IAAIA,aAAa,GAAG,KAAK,EAAE;UAC5BC,MAAM,GAAG,IAAIC,UAAU,CAAC,CAAC,CAAC;UAC1B,MAAMI,IAAI,GAAG,IAAIH,QAAQ,CAACF,MAAM,CAACG,MAAM,CAAC;UACxCE,IAAI,CAACD,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;UACrBC,IAAI,CAACC,SAAS,CAAC,CAAC,EAAEP,aAAa,CAAC;QACpC,CAAC,MACI;UACDC,MAAM,GAAG,IAAIC,UAAU,CAAC,CAAC,CAAC;UAC1B,MAAMI,IAAI,GAAG,IAAIH,QAAQ,CAACF,MAAM,CAACG,MAAM,CAAC;UACxCE,IAAI,CAACD,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;UACrBC,IAAI,CAACE,YAAY,CAAC,CAAC,EAAEC,MAAM,CAACT,aAAa,CAAC,CAAC;QAC/C;QACA;QACA,IAAIf,MAAM,CAACyB,IAAI,IAAI,OAAOzB,MAAM,CAACyB,IAAI,KAAK,QAAQ,EAAE;UAChDT,MAAM,CAAC,CAAC,CAAC,IAAI,IAAI;QACrB;QACAF,UAAU,CAACY,OAAO,CAACV,MAAM,CAAC;QAC1BF,UAAU,CAACY,OAAO,CAACxB,aAAa,CAAC;MACrC,CAAC,CAAC;IACN;EACJ,CAAC,CAAC;AACN;AACA,IAAIyB,YAAY;AAChB,SAASC,WAAWA,CAACC,MAAM,EAAE;EACzB,OAAOA,MAAM,CAACC,MAAM,CAAC,CAACC,GAAG,EAAEC,KAAK,KAAKD,GAAG,GAAGC,KAAK,CAACrC,MAAM,EAAE,CAAC,CAAC;AAC/D;AACA,SAASsC,YAAYA,CAACJ,MAAM,EAAEK,IAAI,EAAE;EAChC,IAAIL,MAAM,CAAC,CAAC,CAAC,CAAClC,MAAM,KAAKuC,IAAI,EAAE;IAC3B,OAAOL,MAAM,CAACM,KAAK,CAAC,CAAC;EACzB;EACA,MAAMhB,MAAM,GAAG,IAAIF,UAAU,CAACiB,IAAI,CAAC;EACnC,IAAIE,CAAC,GAAG,CAAC;EACT,KAAK,IAAInC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGiC,IAAI,EAAEjC,CAAC,EAAE,EAAE;IAC3BkB,MAAM,CAAClB,CAAC,CAAC,GAAG4B,MAAM,CAAC,CAAC,CAAC,CAACO,CAAC,EAAE,CAAC;IAC1B,IAAIA,CAAC,KAAKP,MAAM,CAAC,CAAC,CAAC,CAAClC,MAAM,EAAE;MACxBkC,MAAM,CAACM,KAAK,CAAC,CAAC;MACdC,CAAC,GAAG,CAAC;IACT;EACJ;EACA,IAAIP,MAAM,CAAClC,MAAM,IAAIyC,CAAC,GAAGP,MAAM,CAAC,CAAC,CAAC,CAAClC,MAAM,EAAE;IACvCkC,MAAM,CAAC,CAAC,CAAC,GAAGA,MAAM,CAAC,CAAC,CAAC,CAACQ,KAAK,CAACD,CAAC,CAAC;EAClC;EACA,OAAOjB,MAAM;AACjB;AACA,OAAO,SAASmB,yBAAyBA,CAACC,UAAU,EAAEjC,UAAU,EAAE;EAC9D,IAAI,CAACqB,YAAY,EAAE;IACfA,YAAY,GAAG,IAAIa,WAAW,CAAC,CAAC;EACpC;EACA,MAAMX,MAAM,GAAG,EAAE;EACjB,IAAIY,KAAK,GAAG,CAAC,CAAC;EACd,IAAIC,cAAc,GAAG,CAAC,CAAC;EACvB,IAAIC,QAAQ,GAAG,KAAK;EACpB,OAAO,IAAI/B,eAAe,CAAC;IACvBC,SAASA,CAACmB,KAAK,EAAElB,UAAU,EAAE;MACzBe,MAAM,CAACpB,IAAI,CAACuB,KAAK,CAAC;MAClB,OAAO,IAAI,EAAE;QACT,IAAIS,KAAK,KAAK,CAAC,CAAC,yBAAyB;UACrC,IAAIb,WAAW,CAACC,MAAM,CAAC,GAAG,CAAC,EAAE;YACzB;UACJ;UACA,MAAMb,MAAM,GAAGiB,YAAY,CAACJ,MAAM,EAAE,CAAC,CAAC;UACtCc,QAAQ,GAAG,CAAC3B,MAAM,CAAC,CAAC,CAAC,GAAG,IAAI,MAAM,IAAI;UACtC0B,cAAc,GAAG1B,MAAM,CAAC,CAAC,CAAC,GAAG,IAAI;UACjC,IAAI0B,cAAc,GAAG,GAAG,EAAE;YACtBD,KAAK,GAAG,CAAC,CAAC;UACd,CAAC,MACI,IAAIC,cAAc,KAAK,GAAG,EAAE;YAC7BD,KAAK,GAAG,CAAC,CAAC;UACd,CAAC,MACI;YACDA,KAAK,GAAG,CAAC,CAAC;UACd;QACJ,CAAC,MACI,IAAIA,KAAK,KAAK,CAAC,CAAC,qCAAqC;UACtD,IAAIb,WAAW,CAACC,MAAM,CAAC,GAAG,CAAC,EAAE;YACzB;UACJ;UACA,MAAMe,WAAW,GAAGX,YAAY,CAACJ,MAAM,EAAE,CAAC,CAAC;UAC3Ca,cAAc,GAAG,IAAIxB,QAAQ,CAAC0B,WAAW,CAACzB,MAAM,EAAEyB,WAAW,CAACC,UAAU,EAAED,WAAW,CAACjD,MAAM,CAAC,CAACmD,SAAS,CAAC,CAAC,CAAC;UAC1GL,KAAK,GAAG,CAAC,CAAC;QACd,CAAC,MACI,IAAIA,KAAK,KAAK,CAAC,CAAC,qCAAqC;UACtD,IAAIb,WAAW,CAACC,MAAM,CAAC,GAAG,CAAC,EAAE;YACzB;UACJ;UACA,MAAMe,WAAW,GAAGX,YAAY,CAACJ,MAAM,EAAE,CAAC,CAAC;UAC3C,MAAMR,IAAI,GAAG,IAAIH,QAAQ,CAAC0B,WAAW,CAACzB,MAAM,EAAEyB,WAAW,CAACC,UAAU,EAAED,WAAW,CAACjD,MAAM,CAAC;UACzF,MAAMoD,CAAC,GAAG1B,IAAI,CAAC2B,SAAS,CAAC,CAAC,CAAC;UAC3B,IAAID,CAAC,GAAGE,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE,EAAE,GAAG,EAAE,CAAC,GAAG,CAAC,EAAE;YAC9B;YACApC,UAAU,CAACY,OAAO,CAACtC,YAAY,CAAC;YAChC;UACJ;UACAsD,cAAc,GAAGK,CAAC,GAAGE,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG7B,IAAI,CAAC2B,SAAS,CAAC,CAAC,CAAC;UACxDP,KAAK,GAAG,CAAC,CAAC;QACd,CAAC,MACI;UACD,IAAIb,WAAW,CAACC,MAAM,CAAC,GAAGa,cAAc,EAAE;YACtC;UACJ;UACA,MAAMjB,IAAI,GAAGQ,YAAY,CAACJ,MAAM,EAAEa,cAAc,CAAC;UACjD5B,UAAU,CAACY,OAAO,CAACvC,YAAY,CAACwD,QAAQ,GAAGlB,IAAI,GAAGE,YAAY,CAACwB,MAAM,CAAC1B,IAAI,CAAC,EAAEnB,UAAU,CAAC,CAAC;UACzFmC,KAAK,GAAG,CAAC,CAAC;QACd;;QACA,IAAIC,cAAc,KAAK,CAAC,IAAIA,cAAc,GAAGH,UAAU,EAAE;UACrDzB,UAAU,CAACY,OAAO,CAACtC,YAAY,CAAC;UAChC;QACJ;MACJ;IACJ;EACJ,CAAC,CAAC;AACN;AACA,OAAO,MAAMgE,QAAQ,GAAG,CAAC;AACzB,SAASnE,YAAY,EAAEO,aAAa,EAAEL,YAAY,EAAEiB,aAAa"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}