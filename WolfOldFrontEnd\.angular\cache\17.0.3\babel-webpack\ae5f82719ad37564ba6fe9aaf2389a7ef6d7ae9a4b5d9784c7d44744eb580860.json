{"ast": null, "code": "import { __extends } from \"tslib\";\nimport * as util from '../core/util.js';\nimport { devicePixelRatio } from '../config.js';\nimport Eventful from '../core/Eventful.js';\nimport { getCanvasGradient } from './helper.js';\nimport { createCanvasPattern } from './graphic.js';\nimport BoundingRect from '../core/BoundingRect.js';\nimport { REDRAW_BIT } from '../graphic/constants.js';\nimport { platformApi } from '../core/platform.js';\nfunction createDom(id, painter, dpr) {\n  var newDom = platformApi.createCanvas();\n  var width = painter.getWidth();\n  var height = painter.getHeight();\n  var newDomStyle = newDom.style;\n  if (newDomStyle) {\n    newDomStyle.position = 'absolute';\n    newDomStyle.left = '0';\n    newDomStyle.top = '0';\n    newDomStyle.width = width + 'px';\n    newDomStyle.height = height + 'px';\n    newDom.setAttribute('data-zr-dom-id', id);\n  }\n  newDom.width = width * dpr;\n  newDom.height = height * dpr;\n  return newDom;\n}\n;\nvar Layer = function (_super) {\n  __extends(Layer, _super);\n  function Layer(id, painter, dpr) {\n    var _this = _super.call(this) || this;\n    _this.motionBlur = false;\n    _this.lastFrameAlpha = 0.7;\n    _this.dpr = 1;\n    _this.virtual = false;\n    _this.config = {};\n    _this.incremental = false;\n    _this.zlevel = 0;\n    _this.maxRepaintRectCount = 5;\n    _this.__dirty = true;\n    _this.__firstTimePaint = true;\n    _this.__used = false;\n    _this.__drawIndex = 0;\n    _this.__startIndex = 0;\n    _this.__endIndex = 0;\n    _this.__prevStartIndex = null;\n    _this.__prevEndIndex = null;\n    var dom;\n    dpr = dpr || devicePixelRatio;\n    if (typeof id === 'string') {\n      dom = createDom(id, painter, dpr);\n    } else if (util.isObject(id)) {\n      dom = id;\n      id = dom.id;\n    }\n    _this.id = id;\n    _this.dom = dom;\n    var domStyle = dom.style;\n    if (domStyle) {\n      util.disableUserSelect(dom);\n      dom.onselectstart = function () {\n        return false;\n      };\n      domStyle.padding = '0';\n      domStyle.margin = '0';\n      domStyle.borderWidth = '0';\n    }\n    _this.painter = painter;\n    _this.dpr = dpr;\n    return _this;\n  }\n  Layer.prototype.getElementCount = function () {\n    return this.__endIndex - this.__startIndex;\n  };\n  Layer.prototype.afterBrush = function () {\n    this.__prevStartIndex = this.__startIndex;\n    this.__prevEndIndex = this.__endIndex;\n  };\n  Layer.prototype.initContext = function () {\n    this.ctx = this.dom.getContext('2d');\n    this.ctx.dpr = this.dpr;\n  };\n  Layer.prototype.setUnpainted = function () {\n    this.__firstTimePaint = true;\n  };\n  Layer.prototype.createBackBuffer = function () {\n    var dpr = this.dpr;\n    this.domBack = createDom('back-' + this.id, this.painter, dpr);\n    this.ctxBack = this.domBack.getContext('2d');\n    if (dpr !== 1) {\n      this.ctxBack.scale(dpr, dpr);\n    }\n  };\n  Layer.prototype.createRepaintRects = function (displayList, prevList, viewWidth, viewHeight) {\n    if (this.__firstTimePaint) {\n      this.__firstTimePaint = false;\n      return null;\n    }\n    var mergedRepaintRects = [];\n    var maxRepaintRectCount = this.maxRepaintRectCount;\n    var full = false;\n    var pendingRect = new BoundingRect(0, 0, 0, 0);\n    function addRectToMergePool(rect) {\n      if (!rect.isFinite() || rect.isZero()) {\n        return;\n      }\n      if (mergedRepaintRects.length === 0) {\n        var boundingRect = new BoundingRect(0, 0, 0, 0);\n        boundingRect.copy(rect);\n        mergedRepaintRects.push(boundingRect);\n      } else {\n        var isMerged = false;\n        var minDeltaArea = Infinity;\n        var bestRectToMergeIdx = 0;\n        for (var i = 0; i < mergedRepaintRects.length; ++i) {\n          var mergedRect = mergedRepaintRects[i];\n          if (mergedRect.intersect(rect)) {\n            var pendingRect_1 = new BoundingRect(0, 0, 0, 0);\n            pendingRect_1.copy(mergedRect);\n            pendingRect_1.union(rect);\n            mergedRepaintRects[i] = pendingRect_1;\n            isMerged = true;\n            break;\n          } else if (full) {\n            pendingRect.copy(rect);\n            pendingRect.union(mergedRect);\n            var aArea = rect.width * rect.height;\n            var bArea = mergedRect.width * mergedRect.height;\n            var pendingArea = pendingRect.width * pendingRect.height;\n            var deltaArea = pendingArea - aArea - bArea;\n            if (deltaArea < minDeltaArea) {\n              minDeltaArea = deltaArea;\n              bestRectToMergeIdx = i;\n            }\n          }\n        }\n        if (full) {\n          mergedRepaintRects[bestRectToMergeIdx].union(rect);\n          isMerged = true;\n        }\n        if (!isMerged) {\n          var boundingRect = new BoundingRect(0, 0, 0, 0);\n          boundingRect.copy(rect);\n          mergedRepaintRects.push(boundingRect);\n        }\n        if (!full) {\n          full = mergedRepaintRects.length >= maxRepaintRectCount;\n        }\n      }\n    }\n    for (var i = this.__startIndex; i < this.__endIndex; ++i) {\n      var el = displayList[i];\n      if (el) {\n        var shouldPaint = el.shouldBePainted(viewWidth, viewHeight, true, true);\n        var prevRect = el.__isRendered && (el.__dirty & REDRAW_BIT || !shouldPaint) ? el.getPrevPaintRect() : null;\n        if (prevRect) {\n          addRectToMergePool(prevRect);\n        }\n        var curRect = shouldPaint && (el.__dirty & REDRAW_BIT || !el.__isRendered) ? el.getPaintRect() : null;\n        if (curRect) {\n          addRectToMergePool(curRect);\n        }\n      }\n    }\n    for (var i = this.__prevStartIndex; i < this.__prevEndIndex; ++i) {\n      var el = prevList[i];\n      var shouldPaint = el.shouldBePainted(viewWidth, viewHeight, true, true);\n      if (el && (!shouldPaint || !el.__zr) && el.__isRendered) {\n        var prevRect = el.getPrevPaintRect();\n        if (prevRect) {\n          addRectToMergePool(prevRect);\n        }\n      }\n    }\n    var hasIntersections;\n    do {\n      hasIntersections = false;\n      for (var i = 0; i < mergedRepaintRects.length;) {\n        if (mergedRepaintRects[i].isZero()) {\n          mergedRepaintRects.splice(i, 1);\n          continue;\n        }\n        for (var j = i + 1; j < mergedRepaintRects.length;) {\n          if (mergedRepaintRects[i].intersect(mergedRepaintRects[j])) {\n            hasIntersections = true;\n            mergedRepaintRects[i].union(mergedRepaintRects[j]);\n            mergedRepaintRects.splice(j, 1);\n          } else {\n            j++;\n          }\n        }\n        i++;\n      }\n    } while (hasIntersections);\n    this._paintRects = mergedRepaintRects;\n    return mergedRepaintRects;\n  };\n  Layer.prototype.debugGetPaintRects = function () {\n    return (this._paintRects || []).slice();\n  };\n  Layer.prototype.resize = function (width, height) {\n    var dpr = this.dpr;\n    var dom = this.dom;\n    var domStyle = dom.style;\n    var domBack = this.domBack;\n    if (domStyle) {\n      domStyle.width = width + 'px';\n      domStyle.height = height + 'px';\n    }\n    dom.width = width * dpr;\n    dom.height = height * dpr;\n    if (domBack) {\n      domBack.width = width * dpr;\n      domBack.height = height * dpr;\n      if (dpr !== 1) {\n        this.ctxBack.scale(dpr, dpr);\n      }\n    }\n  };\n  Layer.prototype.clear = function (clearAll, clearColor, repaintRects) {\n    var dom = this.dom;\n    var ctx = this.ctx;\n    var width = dom.width;\n    var height = dom.height;\n    clearColor = clearColor || this.clearColor;\n    var haveMotionBLur = this.motionBlur && !clearAll;\n    var lastFrameAlpha = this.lastFrameAlpha;\n    var dpr = this.dpr;\n    var self = this;\n    if (haveMotionBLur) {\n      if (!this.domBack) {\n        this.createBackBuffer();\n      }\n      this.ctxBack.globalCompositeOperation = 'copy';\n      this.ctxBack.drawImage(dom, 0, 0, width / dpr, height / dpr);\n    }\n    var domBack = this.domBack;\n    function doClear(x, y, width, height) {\n      ctx.clearRect(x, y, width, height);\n      if (clearColor && clearColor !== 'transparent') {\n        var clearColorGradientOrPattern = void 0;\n        if (util.isGradientObject(clearColor)) {\n          var shouldCache = clearColor.global || clearColor.__width === width && clearColor.__height === height;\n          clearColorGradientOrPattern = shouldCache && clearColor.__canvasGradient || getCanvasGradient(ctx, clearColor, {\n            x: 0,\n            y: 0,\n            width: width,\n            height: height\n          });\n          clearColor.__canvasGradient = clearColorGradientOrPattern;\n          clearColor.__width = width;\n          clearColor.__height = height;\n        } else if (util.isImagePatternObject(clearColor)) {\n          clearColor.scaleX = clearColor.scaleX || dpr;\n          clearColor.scaleY = clearColor.scaleY || dpr;\n          clearColorGradientOrPattern = createCanvasPattern(ctx, clearColor, {\n            dirty: function () {\n              self.setUnpainted();\n              self.__painter.refresh();\n            }\n          });\n        }\n        ctx.save();\n        ctx.fillStyle = clearColorGradientOrPattern || clearColor;\n        ctx.fillRect(x, y, width, height);\n        ctx.restore();\n      }\n      if (haveMotionBLur) {\n        ctx.save();\n        ctx.globalAlpha = lastFrameAlpha;\n        ctx.drawImage(domBack, x, y, width, height);\n        ctx.restore();\n      }\n    }\n    ;\n    if (!repaintRects || haveMotionBLur) {\n      doClear(0, 0, width, height);\n    } else if (repaintRects.length) {\n      util.each(repaintRects, function (rect) {\n        doClear(rect.x * dpr, rect.y * dpr, rect.width * dpr, rect.height * dpr);\n      });\n    }\n  };\n  return Layer;\n}(Eventful);\nexport default Layer;", "map": {"version": 3, "names": ["__extends", "util", "devicePixelRatio", "Eventful", "getCanvasGradient", "createCanvasPattern", "BoundingRect", "REDRAW_BIT", "platformApi", "createDom", "id", "painter", "dpr", "newDom", "createCanvas", "width", "getWidth", "height", "getHeight", "newDomStyle", "style", "position", "left", "top", "setAttribute", "Layer", "_super", "_this", "call", "motionBlur", "lastFrameAlpha", "virtual", "config", "incremental", "zlevel", "maxRepaintRectCount", "__dirty", "__firstTime<PERSON><PERSON>t", "__used", "__drawIndex", "__startIndex", "__endIndex", "__prevStartIndex", "__prevEndIndex", "dom", "isObject", "domStyle", "disableUserSelect", "onselectstart", "padding", "margin", "borderWidth", "prototype", "getElementCount", "afterBrush", "initContext", "ctx", "getContext", "setUnpainted", "createBackBuffer", "domBack", "ctxBack", "scale", "createRepaintRects", "displayList", "prevList", "viewWidth", "viewHeight", "mergedRepaintRects", "full", "pendingRect", "addRectToMergePool", "rect", "isFinite", "isZero", "length", "boundingRect", "copy", "push", "isMerged", "minDeltaArea", "Infinity", "bestRectToMergeIdx", "i", "mergedRect", "intersect", "pendingRect_1", "union", "aArea", "bArea", "pendingArea", "deltaArea", "el", "<PERSON><PERSON><PERSON><PERSON>", "shouldBePainted", "prevRect", "__isRendered", "getPrevPaintRect", "curR<PERSON>t", "getPaintRect", "__zr", "hasIntersections", "splice", "j", "_paintRects", "debugGetPaintRects", "slice", "resize", "clear", "clearAll", "clearColor", "repaintRects", "haveMotionBLur", "self", "globalCompositeOperation", "drawImage", "doClear", "x", "y", "clearRect", "clearColorGradientOrPattern", "isGradientObject", "shouldCache", "global", "__width", "__height", "__canvasGradient", "isImagePatternObject", "scaleX", "scaleY", "dirty", "__painter", "refresh", "save", "fillStyle", "fillRect", "restore", "globalAlpha", "each"], "sources": ["C:/WolfSystemV2/WolfOldFrontEnd/node_modules/zrender/lib/canvas/Layer.js"], "sourcesContent": ["import { __extends } from \"tslib\";\nimport * as util from '../core/util.js';\nimport { devicePixelRatio } from '../config.js';\nimport Eventful from '../core/Eventful.js';\nimport { getCanvasGradient } from './helper.js';\nimport { createCanvasPattern } from './graphic.js';\nimport BoundingRect from '../core/BoundingRect.js';\nimport { REDRAW_BIT } from '../graphic/constants.js';\nimport { platformApi } from '../core/platform.js';\nfunction createDom(id, painter, dpr) {\n    var newDom = platformApi.createCanvas();\n    var width = painter.getWidth();\n    var height = painter.getHeight();\n    var newDomStyle = newDom.style;\n    if (newDomStyle) {\n        newDomStyle.position = 'absolute';\n        newDomStyle.left = '0';\n        newDomStyle.top = '0';\n        newDomStyle.width = width + 'px';\n        newDomStyle.height = height + 'px';\n        newDom.setAttribute('data-zr-dom-id', id);\n    }\n    newDom.width = width * dpr;\n    newDom.height = height * dpr;\n    return newDom;\n}\n;\nvar Layer = (function (_super) {\n    __extends(Layer, _super);\n    function Layer(id, painter, dpr) {\n        var _this = _super.call(this) || this;\n        _this.motionBlur = false;\n        _this.lastFrameAlpha = 0.7;\n        _this.dpr = 1;\n        _this.virtual = false;\n        _this.config = {};\n        _this.incremental = false;\n        _this.zlevel = 0;\n        _this.maxRepaintRectCount = 5;\n        _this.__dirty = true;\n        _this.__firstTimePaint = true;\n        _this.__used = false;\n        _this.__drawIndex = 0;\n        _this.__startIndex = 0;\n        _this.__endIndex = 0;\n        _this.__prevStartIndex = null;\n        _this.__prevEndIndex = null;\n        var dom;\n        dpr = dpr || devicePixelRatio;\n        if (typeof id === 'string') {\n            dom = createDom(id, painter, dpr);\n        }\n        else if (util.isObject(id)) {\n            dom = id;\n            id = dom.id;\n        }\n        _this.id = id;\n        _this.dom = dom;\n        var domStyle = dom.style;\n        if (domStyle) {\n            util.disableUserSelect(dom);\n            dom.onselectstart = function () { return false; };\n            domStyle.padding = '0';\n            domStyle.margin = '0';\n            domStyle.borderWidth = '0';\n        }\n        _this.painter = painter;\n        _this.dpr = dpr;\n        return _this;\n    }\n    Layer.prototype.getElementCount = function () {\n        return this.__endIndex - this.__startIndex;\n    };\n    Layer.prototype.afterBrush = function () {\n        this.__prevStartIndex = this.__startIndex;\n        this.__prevEndIndex = this.__endIndex;\n    };\n    Layer.prototype.initContext = function () {\n        this.ctx = this.dom.getContext('2d');\n        this.ctx.dpr = this.dpr;\n    };\n    Layer.prototype.setUnpainted = function () {\n        this.__firstTimePaint = true;\n    };\n    Layer.prototype.createBackBuffer = function () {\n        var dpr = this.dpr;\n        this.domBack = createDom('back-' + this.id, this.painter, dpr);\n        this.ctxBack = this.domBack.getContext('2d');\n        if (dpr !== 1) {\n            this.ctxBack.scale(dpr, dpr);\n        }\n    };\n    Layer.prototype.createRepaintRects = function (displayList, prevList, viewWidth, viewHeight) {\n        if (this.__firstTimePaint) {\n            this.__firstTimePaint = false;\n            return null;\n        }\n        var mergedRepaintRects = [];\n        var maxRepaintRectCount = this.maxRepaintRectCount;\n        var full = false;\n        var pendingRect = new BoundingRect(0, 0, 0, 0);\n        function addRectToMergePool(rect) {\n            if (!rect.isFinite() || rect.isZero()) {\n                return;\n            }\n            if (mergedRepaintRects.length === 0) {\n                var boundingRect = new BoundingRect(0, 0, 0, 0);\n                boundingRect.copy(rect);\n                mergedRepaintRects.push(boundingRect);\n            }\n            else {\n                var isMerged = false;\n                var minDeltaArea = Infinity;\n                var bestRectToMergeIdx = 0;\n                for (var i = 0; i < mergedRepaintRects.length; ++i) {\n                    var mergedRect = mergedRepaintRects[i];\n                    if (mergedRect.intersect(rect)) {\n                        var pendingRect_1 = new BoundingRect(0, 0, 0, 0);\n                        pendingRect_1.copy(mergedRect);\n                        pendingRect_1.union(rect);\n                        mergedRepaintRects[i] = pendingRect_1;\n                        isMerged = true;\n                        break;\n                    }\n                    else if (full) {\n                        pendingRect.copy(rect);\n                        pendingRect.union(mergedRect);\n                        var aArea = rect.width * rect.height;\n                        var bArea = mergedRect.width * mergedRect.height;\n                        var pendingArea = pendingRect.width * pendingRect.height;\n                        var deltaArea = pendingArea - aArea - bArea;\n                        if (deltaArea < minDeltaArea) {\n                            minDeltaArea = deltaArea;\n                            bestRectToMergeIdx = i;\n                        }\n                    }\n                }\n                if (full) {\n                    mergedRepaintRects[bestRectToMergeIdx].union(rect);\n                    isMerged = true;\n                }\n                if (!isMerged) {\n                    var boundingRect = new BoundingRect(0, 0, 0, 0);\n                    boundingRect.copy(rect);\n                    mergedRepaintRects.push(boundingRect);\n                }\n                if (!full) {\n                    full = mergedRepaintRects.length >= maxRepaintRectCount;\n                }\n            }\n        }\n        for (var i = this.__startIndex; i < this.__endIndex; ++i) {\n            var el = displayList[i];\n            if (el) {\n                var shouldPaint = el.shouldBePainted(viewWidth, viewHeight, true, true);\n                var prevRect = el.__isRendered && ((el.__dirty & REDRAW_BIT) || !shouldPaint)\n                    ? el.getPrevPaintRect()\n                    : null;\n                if (prevRect) {\n                    addRectToMergePool(prevRect);\n                }\n                var curRect = shouldPaint && ((el.__dirty & REDRAW_BIT) || !el.__isRendered)\n                    ? el.getPaintRect()\n                    : null;\n                if (curRect) {\n                    addRectToMergePool(curRect);\n                }\n            }\n        }\n        for (var i = this.__prevStartIndex; i < this.__prevEndIndex; ++i) {\n            var el = prevList[i];\n            var shouldPaint = el.shouldBePainted(viewWidth, viewHeight, true, true);\n            if (el && (!shouldPaint || !el.__zr) && el.__isRendered) {\n                var prevRect = el.getPrevPaintRect();\n                if (prevRect) {\n                    addRectToMergePool(prevRect);\n                }\n            }\n        }\n        var hasIntersections;\n        do {\n            hasIntersections = false;\n            for (var i = 0; i < mergedRepaintRects.length;) {\n                if (mergedRepaintRects[i].isZero()) {\n                    mergedRepaintRects.splice(i, 1);\n                    continue;\n                }\n                for (var j = i + 1; j < mergedRepaintRects.length;) {\n                    if (mergedRepaintRects[i].intersect(mergedRepaintRects[j])) {\n                        hasIntersections = true;\n                        mergedRepaintRects[i].union(mergedRepaintRects[j]);\n                        mergedRepaintRects.splice(j, 1);\n                    }\n                    else {\n                        j++;\n                    }\n                }\n                i++;\n            }\n        } while (hasIntersections);\n        this._paintRects = mergedRepaintRects;\n        return mergedRepaintRects;\n    };\n    Layer.prototype.debugGetPaintRects = function () {\n        return (this._paintRects || []).slice();\n    };\n    Layer.prototype.resize = function (width, height) {\n        var dpr = this.dpr;\n        var dom = this.dom;\n        var domStyle = dom.style;\n        var domBack = this.domBack;\n        if (domStyle) {\n            domStyle.width = width + 'px';\n            domStyle.height = height + 'px';\n        }\n        dom.width = width * dpr;\n        dom.height = height * dpr;\n        if (domBack) {\n            domBack.width = width * dpr;\n            domBack.height = height * dpr;\n            if (dpr !== 1) {\n                this.ctxBack.scale(dpr, dpr);\n            }\n        }\n    };\n    Layer.prototype.clear = function (clearAll, clearColor, repaintRects) {\n        var dom = this.dom;\n        var ctx = this.ctx;\n        var width = dom.width;\n        var height = dom.height;\n        clearColor = clearColor || this.clearColor;\n        var haveMotionBLur = this.motionBlur && !clearAll;\n        var lastFrameAlpha = this.lastFrameAlpha;\n        var dpr = this.dpr;\n        var self = this;\n        if (haveMotionBLur) {\n            if (!this.domBack) {\n                this.createBackBuffer();\n            }\n            this.ctxBack.globalCompositeOperation = 'copy';\n            this.ctxBack.drawImage(dom, 0, 0, width / dpr, height / dpr);\n        }\n        var domBack = this.domBack;\n        function doClear(x, y, width, height) {\n            ctx.clearRect(x, y, width, height);\n            if (clearColor && clearColor !== 'transparent') {\n                var clearColorGradientOrPattern = void 0;\n                if (util.isGradientObject(clearColor)) {\n                    var shouldCache = clearColor.global || (clearColor.__width === width\n                        && clearColor.__height === height);\n                    clearColorGradientOrPattern = shouldCache\n                        && clearColor.__canvasGradient\n                        || getCanvasGradient(ctx, clearColor, {\n                            x: 0,\n                            y: 0,\n                            width: width,\n                            height: height\n                        });\n                    clearColor.__canvasGradient = clearColorGradientOrPattern;\n                    clearColor.__width = width;\n                    clearColor.__height = height;\n                }\n                else if (util.isImagePatternObject(clearColor)) {\n                    clearColor.scaleX = clearColor.scaleX || dpr;\n                    clearColor.scaleY = clearColor.scaleY || dpr;\n                    clearColorGradientOrPattern = createCanvasPattern(ctx, clearColor, {\n                        dirty: function () {\n                            self.setUnpainted();\n                            self.__painter.refresh();\n                        }\n                    });\n                }\n                ctx.save();\n                ctx.fillStyle = clearColorGradientOrPattern || clearColor;\n                ctx.fillRect(x, y, width, height);\n                ctx.restore();\n            }\n            if (haveMotionBLur) {\n                ctx.save();\n                ctx.globalAlpha = lastFrameAlpha;\n                ctx.drawImage(domBack, x, y, width, height);\n                ctx.restore();\n            }\n        }\n        ;\n        if (!repaintRects || haveMotionBLur) {\n            doClear(0, 0, width, height);\n        }\n        else if (repaintRects.length) {\n            util.each(repaintRects, function (rect) {\n                doClear(rect.x * dpr, rect.y * dpr, rect.width * dpr, rect.height * dpr);\n            });\n        }\n    };\n    return Layer;\n}(Eventful));\nexport default Layer;\n"], "mappings": "AAAA,SAASA,SAAS,QAAQ,OAAO;AACjC,OAAO,KAAKC,IAAI,MAAM,iBAAiB;AACvC,SAASC,gBAAgB,QAAQ,cAAc;AAC/C,OAAOC,QAAQ,MAAM,qBAAqB;AAC1C,SAASC,iBAAiB,QAAQ,aAAa;AAC/C,SAASC,mBAAmB,QAAQ,cAAc;AAClD,OAAOC,YAAY,MAAM,yBAAyB;AAClD,SAASC,UAAU,QAAQ,yBAAyB;AACpD,SAASC,WAAW,QAAQ,qBAAqB;AACjD,SAASC,SAASA,CAACC,EAAE,EAAEC,OAAO,EAAEC,GAAG,EAAE;EACjC,IAAIC,MAAM,GAAGL,WAAW,CAACM,YAAY,CAAC,CAAC;EACvC,IAAIC,KAAK,GAAGJ,OAAO,CAACK,QAAQ,CAAC,CAAC;EAC9B,IAAIC,MAAM,GAAGN,OAAO,CAACO,SAAS,CAAC,CAAC;EAChC,IAAIC,WAAW,GAAGN,MAAM,CAACO,KAAK;EAC9B,IAAID,WAAW,EAAE;IACbA,WAAW,CAACE,QAAQ,GAAG,UAAU;IACjCF,WAAW,CAACG,IAAI,GAAG,GAAG;IACtBH,WAAW,CAACI,GAAG,GAAG,GAAG;IACrBJ,WAAW,CAACJ,KAAK,GAAGA,KAAK,GAAG,IAAI;IAChCI,WAAW,CAACF,MAAM,GAAGA,MAAM,GAAG,IAAI;IAClCJ,MAAM,CAACW,YAAY,CAAC,gBAAgB,EAAEd,EAAE,CAAC;EAC7C;EACAG,MAAM,CAACE,KAAK,GAAGA,KAAK,GAAGH,GAAG;EAC1BC,MAAM,CAACI,MAAM,GAAGA,MAAM,GAAGL,GAAG;EAC5B,OAAOC,MAAM;AACjB;AACA;AACA,IAAIY,KAAK,GAAI,UAAUC,MAAM,EAAE;EAC3B1B,SAAS,CAACyB,KAAK,EAAEC,MAAM,CAAC;EACxB,SAASD,KAAKA,CAACf,EAAE,EAAEC,OAAO,EAAEC,GAAG,EAAE;IAC7B,IAAIe,KAAK,GAAGD,MAAM,CAACE,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI;IACrCD,KAAK,CAACE,UAAU,GAAG,KAAK;IACxBF,KAAK,CAACG,cAAc,GAAG,GAAG;IAC1BH,KAAK,CAACf,GAAG,GAAG,CAAC;IACbe,KAAK,CAACI,OAAO,GAAG,KAAK;IACrBJ,KAAK,CAACK,MAAM,GAAG,CAAC,CAAC;IACjBL,KAAK,CAACM,WAAW,GAAG,KAAK;IACzBN,KAAK,CAACO,MAAM,GAAG,CAAC;IAChBP,KAAK,CAACQ,mBAAmB,GAAG,CAAC;IAC7BR,KAAK,CAACS,OAAO,GAAG,IAAI;IACpBT,KAAK,CAACU,gBAAgB,GAAG,IAAI;IAC7BV,KAAK,CAACW,MAAM,GAAG,KAAK;IACpBX,KAAK,CAACY,WAAW,GAAG,CAAC;IACrBZ,KAAK,CAACa,YAAY,GAAG,CAAC;IACtBb,KAAK,CAACc,UAAU,GAAG,CAAC;IACpBd,KAAK,CAACe,gBAAgB,GAAG,IAAI;IAC7Bf,KAAK,CAACgB,cAAc,GAAG,IAAI;IAC3B,IAAIC,GAAG;IACPhC,GAAG,GAAGA,GAAG,IAAIV,gBAAgB;IAC7B,IAAI,OAAOQ,EAAE,KAAK,QAAQ,EAAE;MACxBkC,GAAG,GAAGnC,SAAS,CAACC,EAAE,EAAEC,OAAO,EAAEC,GAAG,CAAC;IACrC,CAAC,MACI,IAAIX,IAAI,CAAC4C,QAAQ,CAACnC,EAAE,CAAC,EAAE;MACxBkC,GAAG,GAAGlC,EAAE;MACRA,EAAE,GAAGkC,GAAG,CAAClC,EAAE;IACf;IACAiB,KAAK,CAACjB,EAAE,GAAGA,EAAE;IACbiB,KAAK,CAACiB,GAAG,GAAGA,GAAG;IACf,IAAIE,QAAQ,GAAGF,GAAG,CAACxB,KAAK;IACxB,IAAI0B,QAAQ,EAAE;MACV7C,IAAI,CAAC8C,iBAAiB,CAACH,GAAG,CAAC;MAC3BA,GAAG,CAACI,aAAa,GAAG,YAAY;QAAE,OAAO,KAAK;MAAE,CAAC;MACjDF,QAAQ,CAACG,OAAO,GAAG,GAAG;MACtBH,QAAQ,CAACI,MAAM,GAAG,GAAG;MACrBJ,QAAQ,CAACK,WAAW,GAAG,GAAG;IAC9B;IACAxB,KAAK,CAAChB,OAAO,GAAGA,OAAO;IACvBgB,KAAK,CAACf,GAAG,GAAGA,GAAG;IACf,OAAOe,KAAK;EAChB;EACAF,KAAK,CAAC2B,SAAS,CAACC,eAAe,GAAG,YAAY;IAC1C,OAAO,IAAI,CAACZ,UAAU,GAAG,IAAI,CAACD,YAAY;EAC9C,CAAC;EACDf,KAAK,CAAC2B,SAAS,CAACE,UAAU,GAAG,YAAY;IACrC,IAAI,CAACZ,gBAAgB,GAAG,IAAI,CAACF,YAAY;IACzC,IAAI,CAACG,cAAc,GAAG,IAAI,CAACF,UAAU;EACzC,CAAC;EACDhB,KAAK,CAAC2B,SAAS,CAACG,WAAW,GAAG,YAAY;IACtC,IAAI,CAACC,GAAG,GAAG,IAAI,CAACZ,GAAG,CAACa,UAAU,CAAC,IAAI,CAAC;IACpC,IAAI,CAACD,GAAG,CAAC5C,GAAG,GAAG,IAAI,CAACA,GAAG;EAC3B,CAAC;EACDa,KAAK,CAAC2B,SAAS,CAACM,YAAY,GAAG,YAAY;IACvC,IAAI,CAACrB,gBAAgB,GAAG,IAAI;EAChC,CAAC;EACDZ,KAAK,CAAC2B,SAAS,CAACO,gBAAgB,GAAG,YAAY;IAC3C,IAAI/C,GAAG,GAAG,IAAI,CAACA,GAAG;IAClB,IAAI,CAACgD,OAAO,GAAGnD,SAAS,CAAC,OAAO,GAAG,IAAI,CAACC,EAAE,EAAE,IAAI,CAACC,OAAO,EAAEC,GAAG,CAAC;IAC9D,IAAI,CAACiD,OAAO,GAAG,IAAI,CAACD,OAAO,CAACH,UAAU,CAAC,IAAI,CAAC;IAC5C,IAAI7C,GAAG,KAAK,CAAC,EAAE;MACX,IAAI,CAACiD,OAAO,CAACC,KAAK,CAAClD,GAAG,EAAEA,GAAG,CAAC;IAChC;EACJ,CAAC;EACDa,KAAK,CAAC2B,SAAS,CAACW,kBAAkB,GAAG,UAAUC,WAAW,EAAEC,QAAQ,EAAEC,SAAS,EAAEC,UAAU,EAAE;IACzF,IAAI,IAAI,CAAC9B,gBAAgB,EAAE;MACvB,IAAI,CAACA,gBAAgB,GAAG,KAAK;MAC7B,OAAO,IAAI;IACf;IACA,IAAI+B,kBAAkB,GAAG,EAAE;IAC3B,IAAIjC,mBAAmB,GAAG,IAAI,CAACA,mBAAmB;IAClD,IAAIkC,IAAI,GAAG,KAAK;IAChB,IAAIC,WAAW,GAAG,IAAIhE,YAAY,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IAC9C,SAASiE,kBAAkBA,CAACC,IAAI,EAAE;MAC9B,IAAI,CAACA,IAAI,CAACC,QAAQ,CAAC,CAAC,IAAID,IAAI,CAACE,MAAM,CAAC,CAAC,EAAE;QACnC;MACJ;MACA,IAAIN,kBAAkB,CAACO,MAAM,KAAK,CAAC,EAAE;QACjC,IAAIC,YAAY,GAAG,IAAItE,YAAY,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;QAC/CsE,YAAY,CAACC,IAAI,CAACL,IAAI,CAAC;QACvBJ,kBAAkB,CAACU,IAAI,CAACF,YAAY,CAAC;MACzC,CAAC,MACI;QACD,IAAIG,QAAQ,GAAG,KAAK;QACpB,IAAIC,YAAY,GAAGC,QAAQ;QAC3B,IAAIC,kBAAkB,GAAG,CAAC;QAC1B,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGf,kBAAkB,CAACO,MAAM,EAAE,EAAEQ,CAAC,EAAE;UAChD,IAAIC,UAAU,GAAGhB,kBAAkB,CAACe,CAAC,CAAC;UACtC,IAAIC,UAAU,CAACC,SAAS,CAACb,IAAI,CAAC,EAAE;YAC5B,IAAIc,aAAa,GAAG,IAAIhF,YAAY,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;YAChDgF,aAAa,CAACT,IAAI,CAACO,UAAU,CAAC;YAC9BE,aAAa,CAACC,KAAK,CAACf,IAAI,CAAC;YACzBJ,kBAAkB,CAACe,CAAC,CAAC,GAAGG,aAAa;YACrCP,QAAQ,GAAG,IAAI;YACf;UACJ,CAAC,MACI,IAAIV,IAAI,EAAE;YACXC,WAAW,CAACO,IAAI,CAACL,IAAI,CAAC;YACtBF,WAAW,CAACiB,KAAK,CAACH,UAAU,CAAC;YAC7B,IAAII,KAAK,GAAGhB,IAAI,CAACzD,KAAK,GAAGyD,IAAI,CAACvD,MAAM;YACpC,IAAIwE,KAAK,GAAGL,UAAU,CAACrE,KAAK,GAAGqE,UAAU,CAACnE,MAAM;YAChD,IAAIyE,WAAW,GAAGpB,WAAW,CAACvD,KAAK,GAAGuD,WAAW,CAACrD,MAAM;YACxD,IAAI0E,SAAS,GAAGD,WAAW,GAAGF,KAAK,GAAGC,KAAK;YAC3C,IAAIE,SAAS,GAAGX,YAAY,EAAE;cAC1BA,YAAY,GAAGW,SAAS;cACxBT,kBAAkB,GAAGC,CAAC;YAC1B;UACJ;QACJ;QACA,IAAId,IAAI,EAAE;UACND,kBAAkB,CAACc,kBAAkB,CAAC,CAACK,KAAK,CAACf,IAAI,CAAC;UAClDO,QAAQ,GAAG,IAAI;QACnB;QACA,IAAI,CAACA,QAAQ,EAAE;UACX,IAAIH,YAAY,GAAG,IAAItE,YAAY,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;UAC/CsE,YAAY,CAACC,IAAI,CAACL,IAAI,CAAC;UACvBJ,kBAAkB,CAACU,IAAI,CAACF,YAAY,CAAC;QACzC;QACA,IAAI,CAACP,IAAI,EAAE;UACPA,IAAI,GAAGD,kBAAkB,CAACO,MAAM,IAAIxC,mBAAmB;QAC3D;MACJ;IACJ;IACA,KAAK,IAAIgD,CAAC,GAAG,IAAI,CAAC3C,YAAY,EAAE2C,CAAC,GAAG,IAAI,CAAC1C,UAAU,EAAE,EAAE0C,CAAC,EAAE;MACtD,IAAIS,EAAE,GAAG5B,WAAW,CAACmB,CAAC,CAAC;MACvB,IAAIS,EAAE,EAAE;QACJ,IAAIC,WAAW,GAAGD,EAAE,CAACE,eAAe,CAAC5B,SAAS,EAAEC,UAAU,EAAE,IAAI,EAAE,IAAI,CAAC;QACvE,IAAI4B,QAAQ,GAAGH,EAAE,CAACI,YAAY,KAAMJ,EAAE,CAACxD,OAAO,GAAG7B,UAAU,IAAK,CAACsF,WAAW,CAAC,GACvED,EAAE,CAACK,gBAAgB,CAAC,CAAC,GACrB,IAAI;QACV,IAAIF,QAAQ,EAAE;UACVxB,kBAAkB,CAACwB,QAAQ,CAAC;QAChC;QACA,IAAIG,OAAO,GAAGL,WAAW,KAAMD,EAAE,CAACxD,OAAO,GAAG7B,UAAU,IAAK,CAACqF,EAAE,CAACI,YAAY,CAAC,GACtEJ,EAAE,CAACO,YAAY,CAAC,CAAC,GACjB,IAAI;QACV,IAAID,OAAO,EAAE;UACT3B,kBAAkB,CAAC2B,OAAO,CAAC;QAC/B;MACJ;IACJ;IACA,KAAK,IAAIf,CAAC,GAAG,IAAI,CAACzC,gBAAgB,EAAEyC,CAAC,GAAG,IAAI,CAACxC,cAAc,EAAE,EAAEwC,CAAC,EAAE;MAC9D,IAAIS,EAAE,GAAG3B,QAAQ,CAACkB,CAAC,CAAC;MACpB,IAAIU,WAAW,GAAGD,EAAE,CAACE,eAAe,CAAC5B,SAAS,EAAEC,UAAU,EAAE,IAAI,EAAE,IAAI,CAAC;MACvE,IAAIyB,EAAE,KAAK,CAACC,WAAW,IAAI,CAACD,EAAE,CAACQ,IAAI,CAAC,IAAIR,EAAE,CAACI,YAAY,EAAE;QACrD,IAAID,QAAQ,GAAGH,EAAE,CAACK,gBAAgB,CAAC,CAAC;QACpC,IAAIF,QAAQ,EAAE;UACVxB,kBAAkB,CAACwB,QAAQ,CAAC;QAChC;MACJ;IACJ;IACA,IAAIM,gBAAgB;IACpB,GAAG;MACCA,gBAAgB,GAAG,KAAK;MACxB,KAAK,IAAIlB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGf,kBAAkB,CAACO,MAAM,GAAG;QAC5C,IAAIP,kBAAkB,CAACe,CAAC,CAAC,CAACT,MAAM,CAAC,CAAC,EAAE;UAChCN,kBAAkB,CAACkC,MAAM,CAACnB,CAAC,EAAE,CAAC,CAAC;UAC/B;QACJ;QACA,KAAK,IAAIoB,CAAC,GAAGpB,CAAC,GAAG,CAAC,EAAEoB,CAAC,GAAGnC,kBAAkB,CAACO,MAAM,GAAG;UAChD,IAAIP,kBAAkB,CAACe,CAAC,CAAC,CAACE,SAAS,CAACjB,kBAAkB,CAACmC,CAAC,CAAC,CAAC,EAAE;YACxDF,gBAAgB,GAAG,IAAI;YACvBjC,kBAAkB,CAACe,CAAC,CAAC,CAACI,KAAK,CAACnB,kBAAkB,CAACmC,CAAC,CAAC,CAAC;YAClDnC,kBAAkB,CAACkC,MAAM,CAACC,CAAC,EAAE,CAAC,CAAC;UACnC,CAAC,MACI;YACDA,CAAC,EAAE;UACP;QACJ;QACApB,CAAC,EAAE;MACP;IACJ,CAAC,QAAQkB,gBAAgB;IACzB,IAAI,CAACG,WAAW,GAAGpC,kBAAkB;IACrC,OAAOA,kBAAkB;EAC7B,CAAC;EACD3C,KAAK,CAAC2B,SAAS,CAACqD,kBAAkB,GAAG,YAAY;IAC7C,OAAO,CAAC,IAAI,CAACD,WAAW,IAAI,EAAE,EAAEE,KAAK,CAAC,CAAC;EAC3C,CAAC;EACDjF,KAAK,CAAC2B,SAAS,CAACuD,MAAM,GAAG,UAAU5F,KAAK,EAAEE,MAAM,EAAE;IAC9C,IAAIL,GAAG,GAAG,IAAI,CAACA,GAAG;IAClB,IAAIgC,GAAG,GAAG,IAAI,CAACA,GAAG;IAClB,IAAIE,QAAQ,GAAGF,GAAG,CAACxB,KAAK;IACxB,IAAIwC,OAAO,GAAG,IAAI,CAACA,OAAO;IAC1B,IAAId,QAAQ,EAAE;MACVA,QAAQ,CAAC/B,KAAK,GAAGA,KAAK,GAAG,IAAI;MAC7B+B,QAAQ,CAAC7B,MAAM,GAAGA,MAAM,GAAG,IAAI;IACnC;IACA2B,GAAG,CAAC7B,KAAK,GAAGA,KAAK,GAAGH,GAAG;IACvBgC,GAAG,CAAC3B,MAAM,GAAGA,MAAM,GAAGL,GAAG;IACzB,IAAIgD,OAAO,EAAE;MACTA,OAAO,CAAC7C,KAAK,GAAGA,KAAK,GAAGH,GAAG;MAC3BgD,OAAO,CAAC3C,MAAM,GAAGA,MAAM,GAAGL,GAAG;MAC7B,IAAIA,GAAG,KAAK,CAAC,EAAE;QACX,IAAI,CAACiD,OAAO,CAACC,KAAK,CAAClD,GAAG,EAAEA,GAAG,CAAC;MAChC;IACJ;EACJ,CAAC;EACDa,KAAK,CAAC2B,SAAS,CAACwD,KAAK,GAAG,UAAUC,QAAQ,EAAEC,UAAU,EAAEC,YAAY,EAAE;IAClE,IAAInE,GAAG,GAAG,IAAI,CAACA,GAAG;IAClB,IAAIY,GAAG,GAAG,IAAI,CAACA,GAAG;IAClB,IAAIzC,KAAK,GAAG6B,GAAG,CAAC7B,KAAK;IACrB,IAAIE,MAAM,GAAG2B,GAAG,CAAC3B,MAAM;IACvB6F,UAAU,GAAGA,UAAU,IAAI,IAAI,CAACA,UAAU;IAC1C,IAAIE,cAAc,GAAG,IAAI,CAACnF,UAAU,IAAI,CAACgF,QAAQ;IACjD,IAAI/E,cAAc,GAAG,IAAI,CAACA,cAAc;IACxC,IAAIlB,GAAG,GAAG,IAAI,CAACA,GAAG;IAClB,IAAIqG,IAAI,GAAG,IAAI;IACf,IAAID,cAAc,EAAE;MAChB,IAAI,CAAC,IAAI,CAACpD,OAAO,EAAE;QACf,IAAI,CAACD,gBAAgB,CAAC,CAAC;MAC3B;MACA,IAAI,CAACE,OAAO,CAACqD,wBAAwB,GAAG,MAAM;MAC9C,IAAI,CAACrD,OAAO,CAACsD,SAAS,CAACvE,GAAG,EAAE,CAAC,EAAE,CAAC,EAAE7B,KAAK,GAAGH,GAAG,EAAEK,MAAM,GAAGL,GAAG,CAAC;IAChE;IACA,IAAIgD,OAAO,GAAG,IAAI,CAACA,OAAO;IAC1B,SAASwD,OAAOA,CAACC,CAAC,EAAEC,CAAC,EAAEvG,KAAK,EAAEE,MAAM,EAAE;MAClCuC,GAAG,CAAC+D,SAAS,CAACF,CAAC,EAAEC,CAAC,EAAEvG,KAAK,EAAEE,MAAM,CAAC;MAClC,IAAI6F,UAAU,IAAIA,UAAU,KAAK,aAAa,EAAE;QAC5C,IAAIU,2BAA2B,GAAG,KAAK,CAAC;QACxC,IAAIvH,IAAI,CAACwH,gBAAgB,CAACX,UAAU,CAAC,EAAE;UACnC,IAAIY,WAAW,GAAGZ,UAAU,CAACa,MAAM,IAAKb,UAAU,CAACc,OAAO,KAAK7G,KAAK,IAC7D+F,UAAU,CAACe,QAAQ,KAAK5G,MAAO;UACtCuG,2BAA2B,GAAGE,WAAW,IAClCZ,UAAU,CAACgB,gBAAgB,IAC3B1H,iBAAiB,CAACoD,GAAG,EAAEsD,UAAU,EAAE;YAClCO,CAAC,EAAE,CAAC;YACJC,CAAC,EAAE,CAAC;YACJvG,KAAK,EAAEA,KAAK;YACZE,MAAM,EAAEA;UACZ,CAAC,CAAC;UACN6F,UAAU,CAACgB,gBAAgB,GAAGN,2BAA2B;UACzDV,UAAU,CAACc,OAAO,GAAG7G,KAAK;UAC1B+F,UAAU,CAACe,QAAQ,GAAG5G,MAAM;QAChC,CAAC,MACI,IAAIhB,IAAI,CAAC8H,oBAAoB,CAACjB,UAAU,CAAC,EAAE;UAC5CA,UAAU,CAACkB,MAAM,GAAGlB,UAAU,CAACkB,MAAM,IAAIpH,GAAG;UAC5CkG,UAAU,CAACmB,MAAM,GAAGnB,UAAU,CAACmB,MAAM,IAAIrH,GAAG;UAC5C4G,2BAA2B,GAAGnH,mBAAmB,CAACmD,GAAG,EAAEsD,UAAU,EAAE;YAC/DoB,KAAK,EAAE,SAAAA,CAAA,EAAY;cACfjB,IAAI,CAACvD,YAAY,CAAC,CAAC;cACnBuD,IAAI,CAACkB,SAAS,CAACC,OAAO,CAAC,CAAC;YAC5B;UACJ,CAAC,CAAC;QACN;QACA5E,GAAG,CAAC6E,IAAI,CAAC,CAAC;QACV7E,GAAG,CAAC8E,SAAS,GAAGd,2BAA2B,IAAIV,UAAU;QACzDtD,GAAG,CAAC+E,QAAQ,CAAClB,CAAC,EAAEC,CAAC,EAAEvG,KAAK,EAAEE,MAAM,CAAC;QACjCuC,GAAG,CAACgF,OAAO,CAAC,CAAC;MACjB;MACA,IAAIxB,cAAc,EAAE;QAChBxD,GAAG,CAAC6E,IAAI,CAAC,CAAC;QACV7E,GAAG,CAACiF,WAAW,GAAG3G,cAAc;QAChC0B,GAAG,CAAC2D,SAAS,CAACvD,OAAO,EAAEyD,CAAC,EAAEC,CAAC,EAAEvG,KAAK,EAAEE,MAAM,CAAC;QAC3CuC,GAAG,CAACgF,OAAO,CAAC,CAAC;MACjB;IACJ;IACA;IACA,IAAI,CAACzB,YAAY,IAAIC,cAAc,EAAE;MACjCI,OAAO,CAAC,CAAC,EAAE,CAAC,EAAErG,KAAK,EAAEE,MAAM,CAAC;IAChC,CAAC,MACI,IAAI8F,YAAY,CAACpC,MAAM,EAAE;MAC1B1E,IAAI,CAACyI,IAAI,CAAC3B,YAAY,EAAE,UAAUvC,IAAI,EAAE;QACpC4C,OAAO,CAAC5C,IAAI,CAAC6C,CAAC,GAAGzG,GAAG,EAAE4D,IAAI,CAAC8C,CAAC,GAAG1G,GAAG,EAAE4D,IAAI,CAACzD,KAAK,GAAGH,GAAG,EAAE4D,IAAI,CAACvD,MAAM,GAAGL,GAAG,CAAC;MAC5E,CAAC,CAAC;IACN;EACJ,CAAC;EACD,OAAOa,KAAK;AAChB,CAAC,CAACtB,QAAQ,CAAE;AACZ,eAAesB,KAAK"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}