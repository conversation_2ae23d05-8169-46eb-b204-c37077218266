{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\nvar ToolboxFeature = /** @class */\nfunction () {\n  function ToolboxFeature() {}\n  return ToolboxFeature;\n}();\nexport { ToolboxFeature };\nvar features = {};\nexport function registerFeature(name, ctor) {\n  features[name] = ctor;\n}\nexport function getFeature(name) {\n  return features[name];\n}", "map": {"version": 3, "names": ["ToolboxFeature", "features", "registerFeature", "name", "ctor", "getFeature"], "sources": ["C:/WolfSystemV2/WolfOldFrontEnd/node_modules/echarts/lib/component/toolbox/featureManager.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\nvar ToolboxFeature =\n/** @class */\nfunction () {\n  function ToolboxFeature() {}\n\n  return ToolboxFeature;\n}();\n\nexport { ToolboxFeature };\nvar features = {};\nexport function registerFeature(name, ctor) {\n  features[name] = ctor;\n}\nexport function getFeature(name) {\n  return features[name];\n}"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA,IAAIA,cAAc,GAClB;AACA,YAAY;EACV,SAASA,cAAcA,CAAA,EAAG,CAAC;EAE3B,OAAOA,cAAc;AACvB,CAAC,CAAC,CAAC;AAEH,SAASA,cAAc;AACvB,IAAIC,QAAQ,GAAG,CAAC,CAAC;AACjB,OAAO,SAASC,eAAeA,CAACC,IAAI,EAAEC,IAAI,EAAE;EAC1CH,QAAQ,CAACE,IAAI,CAAC,GAAGC,IAAI;AACvB;AACA,OAAO,SAASC,UAAUA,CAACF,IAAI,EAAE;EAC/B,OAAOF,QAAQ,CAACE,IAAI,CAAC;AACvB"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}