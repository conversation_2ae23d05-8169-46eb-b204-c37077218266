import { Component, OnInit, OnDestroy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { GlobeComponent } from './components/globe/globe.component';
import { TopCountriesComponent } from './components/top-countries/top-countries.component';
import { BanManagementComponent } from './components/ban-management/ban-management.component';
import { ConnectionLogsComponent } from './components/connection-logs/connection-logs.component';
import { MonitoringService } from './services/monitoring.service';
import { Subscription } from 'rxjs';

export interface MonitoringStats {
  totalUniqueIPs: number;
  activeConnections: number;
  totalCountries: number;
  averageConnectionTime: string;
}

export interface CountryData {
  countryCode: string;
  country: string;
  count: number;
  percentage: number;
  coordinates: { lat: number; lng: number };
}

export interface GlobePoint {
  country: string;
  countryName: string;
  lat: number;
  lng: number;
  connections: number;
  intensity: 'low' | 'medium' | 'high';
  recentActivity: boolean;
}

export interface ConnectionLog {
  timestamp: string;
  level: 'success' | 'warning' | 'error' | 'info';
  country: string;
  countryCode: string;
  ip: string;
  page: string;
  message: string;
  accessCount: number;
  isRepeated: boolean;
}

@Component({
  selector: 'app-monitoring',
  standalone: true,
  imports: [
    CommonModule,
    GlobeComponent,
    TopCountriesComponent,
    BanManagementComponent,
    ConnectionLogsComponent
  ],
  templateUrl: './monitoring.component.html',
  styleUrls: ['./monitoring.component.scss']
})
export class MonitoringComponent implements OnInit, OnDestroy {
  stats: MonitoringStats = {
    totalUniqueIPs: 0,
    activeConnections: 0,
    totalCountries: 0,
    averageConnectionTime: '0s'
  };

  topCountries: CountryData[] = [];
  globeData: GlobePoint[] = [];
  connectionLogs: ConnectionLog[] = [];
  
  private subscriptions: Subscription[] = [];

  constructor(private monitoringService: MonitoringService) {}

  ngOnInit() {
    // Conectar ao WebSocket
    this.monitoringService.connect();

    // Subscrever aos dados
    this.subscriptions.push(
      this.monitoringService.stats$.subscribe(stats => {
        this.stats = stats;
      }),

      this.monitoringService.topCountries$.subscribe(countries => {
        this.topCountries = countries;
      }),

      this.monitoringService.globeData$.subscribe(data => {
        this.globeData = data;
      }),

      this.monitoringService.connectionLogs$.subscribe(logs => {
        this.connectionLogs = logs;
      })
    );
  }

  ngOnDestroy() {
    this.subscriptions.forEach(sub => sub.unsubscribe());
    this.monitoringService.disconnect();
  }
}
