{"ast": null, "code": "import baseCreate from './_baseCreate.js';\nimport getPrototype from './_getPrototype.js';\nimport isPrototype from './_isPrototype.js';\n\n/**\n * Initializes an object clone.\n *\n * @private\n * @param {Object} object The object to clone.\n * @returns {Object} Returns the initialized clone.\n */\nfunction initCloneObject(object) {\n  return typeof object.constructor == 'function' && !isPrototype(object) ? baseCreate(getPrototype(object)) : {};\n}\nexport default initCloneObject;", "map": {"version": 3, "names": ["baseCreate", "getPrototype", "isPrototype", "initCloneObject", "object", "constructor"], "sources": ["C:/WolfSystemV2/WolfOldFrontEnd/node_modules/lodash-es/_initCloneObject.js"], "sourcesContent": ["import baseCreate from './_baseCreate.js';\nimport getPrototype from './_getPrototype.js';\nimport isPrototype from './_isPrototype.js';\n\n/**\n * Initializes an object clone.\n *\n * @private\n * @param {Object} object The object to clone.\n * @returns {Object} Returns the initialized clone.\n */\nfunction initCloneObject(object) {\n  return (typeof object.constructor == 'function' && !isPrototype(object))\n    ? baseCreate(getPrototype(object))\n    : {};\n}\n\nexport default initCloneObject;\n"], "mappings": "AAAA,OAAOA,UAAU,MAAM,kBAAkB;AACzC,OAAOC,YAAY,MAAM,oBAAoB;AAC7C,OAAOC,WAAW,MAAM,mBAAmB;;AAE3C;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,eAAeA,CAACC,MAAM,EAAE;EAC/B,OAAQ,OAAOA,MAAM,CAACC,WAAW,IAAI,UAAU,IAAI,CAACH,WAAW,CAACE,MAAM,CAAC,GACnEJ,UAAU,CAACC,YAAY,CAACG,MAAM,CAAC,CAAC,GAChC,CAAC,CAAC;AACR;AAEA,eAAeD,eAAe"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}