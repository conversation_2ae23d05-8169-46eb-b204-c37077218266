{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\nimport { __extends } from \"tslib\";\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\n\nimport { bind, each, clone, trim, isString, isFunction, isArray, isObject, extend } from 'zrender/lib/core/util.js';\nimport env from 'zrender/lib/core/env.js';\nimport TooltipHTMLContent from './TooltipHTMLContent.js';\nimport TooltipRichContent from './TooltipRichContent.js';\nimport { convertToColorString, formatTpl } from '../../util/format.js';\nimport { parsePercent } from '../../util/number.js';\nimport { Rect } from '../../util/graphic.js';\nimport findPointFromSeries from '../axisPointer/findPointFromSeries.js';\nimport { getLayoutRect } from '../../util/layout.js';\nimport Model from '../../model/Model.js';\nimport * as globalListener from '../axisPointer/globalListener.js';\nimport * as axisHelper from '../../coord/axisHelper.js';\nimport * as axisPointerViewHelper from '../axisPointer/viewHelper.js';\nimport { getTooltipRenderMode, preParseFinder, queryReferringComponents } from '../../util/model.js';\nimport ComponentView from '../../view/Component.js';\nimport { format as timeFormat } from '../../util/time.js';\nimport { getECData } from '../../util/innerStore.js';\nimport { shouldTooltipConfine } from './helper.js';\nimport { normalizeTooltipFormatResult } from '../../model/mixin/dataFormat.js';\nimport { createTooltipMarkup, buildTooltipMarkup, TooltipMarkupStyleCreator } from './tooltipMarkup.js';\nimport { findEventDispatcher } from '../../util/event.js';\nimport { clear, createOrUpdate } from '../../util/throttle.js';\nvar proxyRect = new Rect({\n  shape: {\n    x: -1,\n    y: -1,\n    width: 2,\n    height: 2\n  }\n});\nvar TooltipView = /** @class */\nfunction (_super) {\n  __extends(TooltipView, _super);\n  function TooltipView() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.type = TooltipView.type;\n    return _this;\n  }\n  TooltipView.prototype.init = function (ecModel, api) {\n    if (env.node || !api.getDom()) {\n      return;\n    }\n    var tooltipModel = ecModel.getComponent('tooltip');\n    var renderMode = this._renderMode = getTooltipRenderMode(tooltipModel.get('renderMode'));\n    this._tooltipContent = renderMode === 'richText' ? new TooltipRichContent(api) : new TooltipHTMLContent(api.getDom(), api, {\n      appendToBody: tooltipModel.get('appendToBody', true)\n    });\n  };\n  TooltipView.prototype.render = function (tooltipModel, ecModel, api) {\n    if (env.node || !api.getDom()) {\n      return;\n    } // Reset\n\n    this.group.removeAll();\n    this._tooltipModel = tooltipModel;\n    this._ecModel = ecModel;\n    this._api = api;\n    var tooltipContent = this._tooltipContent;\n    tooltipContent.update(tooltipModel);\n    tooltipContent.setEnterable(tooltipModel.get('enterable'));\n    this._initGlobalListener();\n    this._keepShow(); // PENDING\n    // `mousemove` event will be triggered very frequently when the mouse moves fast,\n    // which causes that the `updatePosition` function was also called frequently.\n    // In Chrome with devtools open and Firefox, tooltip looks laggy and shakes. See #14695 #16101\n    // To avoid frequent triggering,\n    // consider throttling it in 50ms when transition is enabled\n\n    if (this._renderMode !== 'richText' && tooltipModel.get('transitionDuration')) {\n      createOrUpdate(this, '_updatePosition', 50, 'fixRate');\n    } else {\n      clear(this, '_updatePosition');\n    }\n  };\n  TooltipView.prototype._initGlobalListener = function () {\n    var tooltipModel = this._tooltipModel;\n    var triggerOn = tooltipModel.get('triggerOn');\n    globalListener.register('itemTooltip', this._api, bind(function (currTrigger, e, dispatchAction) {\n      // If 'none', it is not controlled by mouse totally.\n      if (triggerOn !== 'none') {\n        if (triggerOn.indexOf(currTrigger) >= 0) {\n          this._tryShow(e, dispatchAction);\n        } else if (currTrigger === 'leave') {\n          this._hide(dispatchAction);\n        }\n      }\n    }, this));\n  };\n  TooltipView.prototype._keepShow = function () {\n    var tooltipModel = this._tooltipModel;\n    var ecModel = this._ecModel;\n    var api = this._api;\n    var triggerOn = tooltipModel.get('triggerOn'); // Try to keep the tooltip show when refreshing\n\n    if (this._lastX != null && this._lastY != null // When user is willing to control tooltip totally using API,\n    // self.manuallyShowTip({x, y}) might cause tooltip hide,\n    // which is not expected.\n    && triggerOn !== 'none' && triggerOn !== 'click') {\n      var self_1 = this;\n      clearTimeout(this._refreshUpdateTimeout);\n      this._refreshUpdateTimeout = setTimeout(function () {\n        // Show tip next tick after other charts are rendered\n        // In case highlight action has wrong result\n        // FIXME\n        !api.isDisposed() && self_1.manuallyShowTip(tooltipModel, ecModel, api, {\n          x: self_1._lastX,\n          y: self_1._lastY,\n          dataByCoordSys: self_1._lastDataByCoordSys\n        });\n      });\n    }\n  };\n  /**\r\n   * Show tip manually by\r\n   * dispatchAction({\r\n   *     type: 'showTip',\r\n   *     x: 10,\r\n   *     y: 10\r\n   * });\r\n   * Or\r\n   * dispatchAction({\r\n   *      type: 'showTip',\r\n   *      seriesIndex: 0,\r\n   *      dataIndex or dataIndexInside or name\r\n   * });\r\n   *\r\n   *  TODO Batch\r\n   */\n\n  TooltipView.prototype.manuallyShowTip = function (tooltipModel, ecModel, api, payload) {\n    if (payload.from === this.uid || env.node || !api.getDom()) {\n      return;\n    }\n    var dispatchAction = makeDispatchAction(payload, api); // Reset ticket\n\n    this._ticket = ''; // When triggered from axisPointer.\n\n    var dataByCoordSys = payload.dataByCoordSys;\n    var cmptRef = findComponentReference(payload, ecModel, api);\n    if (cmptRef) {\n      var rect = cmptRef.el.getBoundingRect().clone();\n      rect.applyTransform(cmptRef.el.transform);\n      this._tryShow({\n        offsetX: rect.x + rect.width / 2,\n        offsetY: rect.y + rect.height / 2,\n        target: cmptRef.el,\n        position: payload.position,\n        // When manully trigger, the mouse is not on the el, so we'd better to\n        // position tooltip on the bottom of the el and display arrow is possible.\n        positionDefault: 'bottom'\n      }, dispatchAction);\n    } else if (payload.tooltip && payload.x != null && payload.y != null) {\n      var el = proxyRect;\n      el.x = payload.x;\n      el.y = payload.y;\n      el.update();\n      getECData(el).tooltipConfig = {\n        name: null,\n        option: payload.tooltip\n      }; // Manually show tooltip while view is not using zrender elements.\n\n      this._tryShow({\n        offsetX: payload.x,\n        offsetY: payload.y,\n        target: el\n      }, dispatchAction);\n    } else if (dataByCoordSys) {\n      this._tryShow({\n        offsetX: payload.x,\n        offsetY: payload.y,\n        position: payload.position,\n        dataByCoordSys: dataByCoordSys,\n        tooltipOption: payload.tooltipOption\n      }, dispatchAction);\n    } else if (payload.seriesIndex != null) {\n      if (this._manuallyAxisShowTip(tooltipModel, ecModel, api, payload)) {\n        return;\n      }\n      var pointInfo = findPointFromSeries(payload, ecModel);\n      var cx = pointInfo.point[0];\n      var cy = pointInfo.point[1];\n      if (cx != null && cy != null) {\n        this._tryShow({\n          offsetX: cx,\n          offsetY: cy,\n          target: pointInfo.el,\n          position: payload.position,\n          // When manully trigger, the mouse is not on the el, so we'd better to\n          // position tooltip on the bottom of the el and display arrow is possible.\n          positionDefault: 'bottom'\n        }, dispatchAction);\n      }\n    } else if (payload.x != null && payload.y != null) {\n      // FIXME\n      // should wrap dispatchAction like `axisPointer/globalListener` ?\n      api.dispatchAction({\n        type: 'updateAxisPointer',\n        x: payload.x,\n        y: payload.y\n      });\n      this._tryShow({\n        offsetX: payload.x,\n        offsetY: payload.y,\n        position: payload.position,\n        target: api.getZr().findHover(payload.x, payload.y).target\n      }, dispatchAction);\n    }\n  };\n  TooltipView.prototype.manuallyHideTip = function (tooltipModel, ecModel, api, payload) {\n    var tooltipContent = this._tooltipContent;\n    if (this._tooltipModel) {\n      tooltipContent.hideLater(this._tooltipModel.get('hideDelay'));\n    }\n    this._lastX = this._lastY = this._lastDataByCoordSys = null;\n    if (payload.from !== this.uid) {\n      this._hide(makeDispatchAction(payload, api));\n    }\n  }; // Be compatible with previous design, that is, when tooltip.type is 'axis' and\n  // dispatchAction 'showTip' with seriesIndex and dataIndex will trigger axis pointer\n  // and tooltip.\n\n  TooltipView.prototype._manuallyAxisShowTip = function (tooltipModel, ecModel, api, payload) {\n    var seriesIndex = payload.seriesIndex;\n    var dataIndex = payload.dataIndex; // @ts-ignore\n\n    var coordSysAxesInfo = ecModel.getComponent('axisPointer').coordSysAxesInfo;\n    if (seriesIndex == null || dataIndex == null || coordSysAxesInfo == null) {\n      return;\n    }\n    var seriesModel = ecModel.getSeriesByIndex(seriesIndex);\n    if (!seriesModel) {\n      return;\n    }\n    var data = seriesModel.getData();\n    var tooltipCascadedModel = buildTooltipModel([data.getItemModel(dataIndex), seriesModel, (seriesModel.coordinateSystem || {}).model], this._tooltipModel);\n    if (tooltipCascadedModel.get('trigger') !== 'axis') {\n      return;\n    }\n    api.dispatchAction({\n      type: 'updateAxisPointer',\n      seriesIndex: seriesIndex,\n      dataIndex: dataIndex,\n      position: payload.position\n    });\n    return true;\n  };\n  TooltipView.prototype._tryShow = function (e, dispatchAction) {\n    var el = e.target;\n    var tooltipModel = this._tooltipModel;\n    if (!tooltipModel) {\n      return;\n    } // Save mouse x, mouse y. So we can try to keep showing the tip if chart is refreshed\n\n    this._lastX = e.offsetX;\n    this._lastY = e.offsetY;\n    var dataByCoordSys = e.dataByCoordSys;\n    if (dataByCoordSys && dataByCoordSys.length) {\n      this._showAxisTooltip(dataByCoordSys, e);\n    } else if (el) {\n      this._lastDataByCoordSys = null;\n      var seriesDispatcher_1;\n      var cmptDispatcher_1;\n      findEventDispatcher(el, function (target) {\n        // Always show item tooltip if mouse is on the element with dataIndex\n        if (getECData(target).dataIndex != null) {\n          seriesDispatcher_1 = target;\n          return true;\n        } // Tooltip provided directly. Like legend.\n\n        if (getECData(target).tooltipConfig != null) {\n          cmptDispatcher_1 = target;\n          return true;\n        }\n      }, true);\n      if (seriesDispatcher_1) {\n        this._showSeriesItemTooltip(e, seriesDispatcher_1, dispatchAction);\n      } else if (cmptDispatcher_1) {\n        this._showComponentItemTooltip(e, cmptDispatcher_1, dispatchAction);\n      } else {\n        this._hide(dispatchAction);\n      }\n    } else {\n      this._lastDataByCoordSys = null;\n      this._hide(dispatchAction);\n    }\n  };\n  TooltipView.prototype._showOrMove = function (tooltipModel, cb) {\n    // showDelay is used in this case: tooltip.enterable is set\n    // as true. User intent to move mouse into tooltip and click\n    // something. `showDelay` makes it easier to enter the content\n    // but tooltip do not move immediately.\n    var delay = tooltipModel.get('showDelay');\n    cb = bind(cb, this);\n    clearTimeout(this._showTimout);\n    delay > 0 ? this._showTimout = setTimeout(cb, delay) : cb();\n  };\n  TooltipView.prototype._showAxisTooltip = function (dataByCoordSys, e) {\n    var ecModel = this._ecModel;\n    var globalTooltipModel = this._tooltipModel;\n    var point = [e.offsetX, e.offsetY];\n    var singleTooltipModel = buildTooltipModel([e.tooltipOption], globalTooltipModel);\n    var renderMode = this._renderMode;\n    var cbParamsList = [];\n    var articleMarkup = createTooltipMarkup('section', {\n      blocks: [],\n      noHeader: true\n    }); // Only for legacy: `Serise['formatTooltip']` returns a string.\n\n    var markupTextArrLegacy = [];\n    var markupStyleCreator = new TooltipMarkupStyleCreator();\n    each(dataByCoordSys, function (itemCoordSys) {\n      each(itemCoordSys.dataByAxis, function (axisItem) {\n        var axisModel = ecModel.getComponent(axisItem.axisDim + 'Axis', axisItem.axisIndex);\n        var axisValue = axisItem.value;\n        if (!axisModel || axisValue == null) {\n          return;\n        }\n        var axisValueLabel = axisPointerViewHelper.getValueLabel(axisValue, axisModel.axis, ecModel, axisItem.seriesDataIndices, axisItem.valueLabelOpt);\n        var axisSectionMarkup = createTooltipMarkup('section', {\n          header: axisValueLabel,\n          noHeader: !trim(axisValueLabel),\n          sortBlocks: true,\n          blocks: []\n        });\n        articleMarkup.blocks.push(axisSectionMarkup);\n        each(axisItem.seriesDataIndices, function (idxItem) {\n          var series = ecModel.getSeriesByIndex(idxItem.seriesIndex);\n          var dataIndex = idxItem.dataIndexInside;\n          var cbParams = series.getDataParams(dataIndex); // Can't find data.\n\n          if (cbParams.dataIndex < 0) {\n            return;\n          }\n          cbParams.axisDim = axisItem.axisDim;\n          cbParams.axisIndex = axisItem.axisIndex;\n          cbParams.axisType = axisItem.axisType;\n          cbParams.axisId = axisItem.axisId;\n          cbParams.axisValue = axisHelper.getAxisRawValue(axisModel.axis, {\n            value: axisValue\n          });\n          cbParams.axisValueLabel = axisValueLabel; // Pre-create marker style for makers. Users can assemble richText\n          // text in `formatter` callback and use those markers style.\n\n          cbParams.marker = markupStyleCreator.makeTooltipMarker('item', convertToColorString(cbParams.color), renderMode);\n          var seriesTooltipResult = normalizeTooltipFormatResult(series.formatTooltip(dataIndex, true, null));\n          var frag = seriesTooltipResult.frag;\n          if (frag) {\n            var valueFormatter = buildTooltipModel([series], globalTooltipModel).get('valueFormatter');\n            axisSectionMarkup.blocks.push(valueFormatter ? extend({\n              valueFormatter: valueFormatter\n            }, frag) : frag);\n          }\n          if (seriesTooltipResult.text) {\n            markupTextArrLegacy.push(seriesTooltipResult.text);\n          }\n          cbParamsList.push(cbParams);\n        });\n      });\n    }); // In most cases, the second axis is displays upper on the first one.\n    // So we reverse it to look better.\n\n    articleMarkup.blocks.reverse();\n    markupTextArrLegacy.reverse();\n    var positionExpr = e.position;\n    var orderMode = singleTooltipModel.get('order');\n    var builtMarkupText = buildTooltipMarkup(articleMarkup, markupStyleCreator, renderMode, orderMode, ecModel.get('useUTC'), singleTooltipModel.get('textStyle'));\n    builtMarkupText && markupTextArrLegacy.unshift(builtMarkupText);\n    var blockBreak = renderMode === 'richText' ? '\\n\\n' : '<br/>';\n    var allMarkupText = markupTextArrLegacy.join(blockBreak);\n    this._showOrMove(singleTooltipModel, function () {\n      if (this._updateContentNotChangedOnAxis(dataByCoordSys, cbParamsList)) {\n        this._updatePosition(singleTooltipModel, positionExpr, point[0], point[1], this._tooltipContent, cbParamsList);\n      } else {\n        this._showTooltipContent(singleTooltipModel, allMarkupText, cbParamsList, Math.random() + '', point[0], point[1], positionExpr, null, markupStyleCreator);\n      }\n    }); // Do not trigger events here, because this branch only be entered\n    // from dispatchAction.\n  };\n\n  TooltipView.prototype._showSeriesItemTooltip = function (e, dispatcher, dispatchAction) {\n    var ecModel = this._ecModel;\n    var ecData = getECData(dispatcher); // Use dataModel in element if possible\n    // Used when mouseover on a element like markPoint or edge\n    // In which case, the data is not main data in series.\n\n    var seriesIndex = ecData.seriesIndex;\n    var seriesModel = ecModel.getSeriesByIndex(seriesIndex); // For example, graph link.\n\n    var dataModel = ecData.dataModel || seriesModel;\n    var dataIndex = ecData.dataIndex;\n    var dataType = ecData.dataType;\n    var data = dataModel.getData(dataType);\n    var renderMode = this._renderMode;\n    var positionDefault = e.positionDefault;\n    var tooltipModel = buildTooltipModel([data.getItemModel(dataIndex), dataModel, seriesModel && (seriesModel.coordinateSystem || {}).model], this._tooltipModel, positionDefault ? {\n      position: positionDefault\n    } : null);\n    var tooltipTrigger = tooltipModel.get('trigger');\n    if (tooltipTrigger != null && tooltipTrigger !== 'item') {\n      return;\n    }\n    var params = dataModel.getDataParams(dataIndex, dataType);\n    var markupStyleCreator = new TooltipMarkupStyleCreator(); // Pre-create marker style for makers. Users can assemble richText\n    // text in `formatter` callback and use those markers style.\n\n    params.marker = markupStyleCreator.makeTooltipMarker('item', convertToColorString(params.color), renderMode);\n    var seriesTooltipResult = normalizeTooltipFormatResult(dataModel.formatTooltip(dataIndex, false, dataType));\n    var orderMode = tooltipModel.get('order');\n    var valueFormatter = tooltipModel.get('valueFormatter');\n    var frag = seriesTooltipResult.frag;\n    var markupText = frag ? buildTooltipMarkup(valueFormatter ? extend({\n      valueFormatter: valueFormatter\n    }, frag) : frag, markupStyleCreator, renderMode, orderMode, ecModel.get('useUTC'), tooltipModel.get('textStyle')) : seriesTooltipResult.text;\n    var asyncTicket = 'item_' + dataModel.name + '_' + dataIndex;\n    this._showOrMove(tooltipModel, function () {\n      this._showTooltipContent(tooltipModel, markupText, params, asyncTicket, e.offsetX, e.offsetY, e.position, e.target, markupStyleCreator);\n    }); // FIXME\n    // duplicated showtip if manuallyShowTip is called from dispatchAction.\n\n    dispatchAction({\n      type: 'showTip',\n      dataIndexInside: dataIndex,\n      dataIndex: data.getRawIndex(dataIndex),\n      seriesIndex: seriesIndex,\n      from: this.uid\n    });\n  };\n  TooltipView.prototype._showComponentItemTooltip = function (e, el, dispatchAction) {\n    var ecData = getECData(el);\n    var tooltipConfig = ecData.tooltipConfig;\n    var tooltipOpt = tooltipConfig.option || {};\n    if (isString(tooltipOpt)) {\n      var content = tooltipOpt;\n      tooltipOpt = {\n        content: content,\n        // Fixed formatter\n        formatter: content\n      };\n    }\n    var tooltipModelCascade = [tooltipOpt];\n    var cmpt = this._ecModel.getComponent(ecData.componentMainType, ecData.componentIndex);\n    if (cmpt) {\n      tooltipModelCascade.push(cmpt);\n    } // In most cases, component tooltip formatter has different params with series tooltip formatter,\n    // so that they cannot share the same formatter. Since the global tooltip formatter is used for series\n    // by convention, we do not use it as the default formatter for component.\n\n    tooltipModelCascade.push({\n      formatter: tooltipOpt.content\n    });\n    var positionDefault = e.positionDefault;\n    var subTooltipModel = buildTooltipModel(tooltipModelCascade, this._tooltipModel, positionDefault ? {\n      position: positionDefault\n    } : null);\n    var defaultHtml = subTooltipModel.get('content');\n    var asyncTicket = Math.random() + ''; // PENDING: this case do not support richText style yet.\n\n    var markupStyleCreator = new TooltipMarkupStyleCreator(); // Do not check whether `trigger` is 'none' here, because `trigger`\n    // only works on coordinate system. In fact, we have not found case\n    // that requires setting `trigger` nothing on component yet.\n\n    this._showOrMove(subTooltipModel, function () {\n      // Use formatterParams from element defined in component\n      // Avoid users modify it.\n      var formatterParams = clone(subTooltipModel.get('formatterParams') || {});\n      this._showTooltipContent(subTooltipModel, defaultHtml, formatterParams, asyncTicket, e.offsetX, e.offsetY, e.position, el, markupStyleCreator);\n    }); // If not dispatch showTip, tip may be hide triggered by axis.\n\n    dispatchAction({\n      type: 'showTip',\n      from: this.uid\n    });\n  };\n  TooltipView.prototype._showTooltipContent = function (\n  // Use Model<TooltipOption> insteadof TooltipModel because this model may be from series or other options.\n  // Instead of top level tooltip.\n  tooltipModel, defaultHtml, params, asyncTicket, x, y, positionExpr, el, markupStyleCreator) {\n    // Reset ticket\n    this._ticket = '';\n    if (!tooltipModel.get('showContent') || !tooltipModel.get('show')) {\n      return;\n    }\n    var tooltipContent = this._tooltipContent;\n    tooltipContent.setEnterable(tooltipModel.get('enterable'));\n    var formatter = tooltipModel.get('formatter');\n    positionExpr = positionExpr || tooltipModel.get('position');\n    var html = defaultHtml;\n    var nearPoint = this._getNearestPoint([x, y], params, tooltipModel.get('trigger'), tooltipModel.get('borderColor'));\n    var nearPointColor = nearPoint.color;\n    if (formatter) {\n      if (isString(formatter)) {\n        var useUTC = tooltipModel.ecModel.get('useUTC');\n        var params0 = isArray(params) ? params[0] : params;\n        var isTimeAxis = params0 && params0.axisType && params0.axisType.indexOf('time') >= 0;\n        html = formatter;\n        if (isTimeAxis) {\n          html = timeFormat(params0.axisValue, html, useUTC);\n        }\n        html = formatTpl(html, params, true);\n      } else if (isFunction(formatter)) {\n        var callback = bind(function (cbTicket, html) {\n          if (cbTicket === this._ticket) {\n            tooltipContent.setContent(html, markupStyleCreator, tooltipModel, nearPointColor, positionExpr);\n            this._updatePosition(tooltipModel, positionExpr, x, y, tooltipContent, params, el);\n          }\n        }, this);\n        this._ticket = asyncTicket;\n        html = formatter(params, asyncTicket, callback);\n      } else {\n        html = formatter;\n      }\n    }\n    tooltipContent.setContent(html, markupStyleCreator, tooltipModel, nearPointColor, positionExpr);\n    tooltipContent.show(tooltipModel, nearPointColor);\n    this._updatePosition(tooltipModel, positionExpr, x, y, tooltipContent, params, el);\n  };\n  TooltipView.prototype._getNearestPoint = function (point, tooltipDataParams, trigger, borderColor) {\n    if (trigger === 'axis' || isArray(tooltipDataParams)) {\n      return {\n        color: borderColor || (this._renderMode === 'html' ? '#fff' : 'none')\n      };\n    }\n    if (!isArray(tooltipDataParams)) {\n      return {\n        color: borderColor || tooltipDataParams.color || tooltipDataParams.borderColor\n      };\n    }\n  };\n  TooltipView.prototype._updatePosition = function (tooltipModel, positionExpr, x,\n  // Mouse x\n  y,\n  // Mouse y\n  content, params, el) {\n    var viewWidth = this._api.getWidth();\n    var viewHeight = this._api.getHeight();\n    positionExpr = positionExpr || tooltipModel.get('position');\n    var contentSize = content.getSize();\n    var align = tooltipModel.get('align');\n    var vAlign = tooltipModel.get('verticalAlign');\n    var rect = el && el.getBoundingRect().clone();\n    el && rect.applyTransform(el.transform);\n    if (isFunction(positionExpr)) {\n      // Callback of position can be an array or a string specify the position\n      positionExpr = positionExpr([x, y], params, content.el, rect, {\n        viewSize: [viewWidth, viewHeight],\n        contentSize: contentSize.slice()\n      });\n    }\n    if (isArray(positionExpr)) {\n      x = parsePercent(positionExpr[0], viewWidth);\n      y = parsePercent(positionExpr[1], viewHeight);\n    } else if (isObject(positionExpr)) {\n      var boxLayoutPosition = positionExpr;\n      boxLayoutPosition.width = contentSize[0];\n      boxLayoutPosition.height = contentSize[1];\n      var layoutRect = getLayoutRect(boxLayoutPosition, {\n        width: viewWidth,\n        height: viewHeight\n      });\n      x = layoutRect.x;\n      y = layoutRect.y;\n      align = null; // When positionExpr is left/top/right/bottom,\n      // align and verticalAlign will not work.\n\n      vAlign = null;\n    } // Specify tooltip position by string 'top' 'bottom' 'left' 'right' around graphic element\n    else if (isString(positionExpr) && el) {\n      var pos = calcTooltipPosition(positionExpr, rect, contentSize, tooltipModel.get('borderWidth'));\n      x = pos[0];\n      y = pos[1];\n    } else {\n      var pos = refixTooltipPosition(x, y, content, viewWidth, viewHeight, align ? null : 20, vAlign ? null : 20);\n      x = pos[0];\n      y = pos[1];\n    }\n    align && (x -= isCenterAlign(align) ? contentSize[0] / 2 : align === 'right' ? contentSize[0] : 0);\n    vAlign && (y -= isCenterAlign(vAlign) ? contentSize[1] / 2 : vAlign === 'bottom' ? contentSize[1] : 0);\n    if (shouldTooltipConfine(tooltipModel)) {\n      var pos = confineTooltipPosition(x, y, content, viewWidth, viewHeight);\n      x = pos[0];\n      y = pos[1];\n    }\n    content.moveTo(x, y);\n  }; // FIXME\n  // Should we remove this but leave this to user?\n\n  TooltipView.prototype._updateContentNotChangedOnAxis = function (dataByCoordSys, cbParamsList) {\n    var lastCoordSys = this._lastDataByCoordSys;\n    var lastCbParamsList = this._cbParamsList;\n    var contentNotChanged = !!lastCoordSys && lastCoordSys.length === dataByCoordSys.length;\n    contentNotChanged && each(lastCoordSys, function (lastItemCoordSys, indexCoordSys) {\n      var lastDataByAxis = lastItemCoordSys.dataByAxis || [];\n      var thisItemCoordSys = dataByCoordSys[indexCoordSys] || {};\n      var thisDataByAxis = thisItemCoordSys.dataByAxis || [];\n      contentNotChanged = contentNotChanged && lastDataByAxis.length === thisDataByAxis.length;\n      contentNotChanged && each(lastDataByAxis, function (lastItem, indexAxis) {\n        var thisItem = thisDataByAxis[indexAxis] || {};\n        var lastIndices = lastItem.seriesDataIndices || [];\n        var newIndices = thisItem.seriesDataIndices || [];\n        contentNotChanged = contentNotChanged && lastItem.value === thisItem.value && lastItem.axisType === thisItem.axisType && lastItem.axisId === thisItem.axisId && lastIndices.length === newIndices.length;\n        contentNotChanged && each(lastIndices, function (lastIdxItem, j) {\n          var newIdxItem = newIndices[j];\n          contentNotChanged = contentNotChanged && lastIdxItem.seriesIndex === newIdxItem.seriesIndex && lastIdxItem.dataIndex === newIdxItem.dataIndex;\n        }); // check is cbParams data value changed\n\n        lastCbParamsList && each(lastItem.seriesDataIndices, function (idxItem) {\n          var seriesIdx = idxItem.seriesIndex;\n          var cbParams = cbParamsList[seriesIdx];\n          var lastCbParams = lastCbParamsList[seriesIdx];\n          if (cbParams && lastCbParams && lastCbParams.data !== cbParams.data) {\n            contentNotChanged = false;\n          }\n        });\n      });\n    });\n    this._lastDataByCoordSys = dataByCoordSys;\n    this._cbParamsList = cbParamsList;\n    return !!contentNotChanged;\n  };\n  TooltipView.prototype._hide = function (dispatchAction) {\n    // Do not directly hideLater here, because this behavior may be prevented\n    // in dispatchAction when showTip is dispatched.\n    // FIXME\n    // duplicated hideTip if manuallyHideTip is called from dispatchAction.\n    this._lastDataByCoordSys = null;\n    dispatchAction({\n      type: 'hideTip',\n      from: this.uid\n    });\n  };\n  TooltipView.prototype.dispose = function (ecModel, api) {\n    if (env.node || !api.getDom()) {\n      return;\n    }\n    clear(this, '_updatePosition');\n    this._tooltipContent.dispose();\n    globalListener.unregister('itemTooltip', api);\n  };\n  TooltipView.type = 'tooltip';\n  return TooltipView;\n}(ComponentView);\n/**\r\n * From top to bottom. (the last one should be globalTooltipModel);\r\n */\n\nfunction buildTooltipModel(modelCascade, globalTooltipModel, defaultTooltipOption) {\n  // Last is always tooltip model.\n  var ecModel = globalTooltipModel.ecModel;\n  var resultModel;\n  if (defaultTooltipOption) {\n    resultModel = new Model(defaultTooltipOption, ecModel, ecModel);\n    resultModel = new Model(globalTooltipModel.option, resultModel, ecModel);\n  } else {\n    resultModel = globalTooltipModel;\n  }\n  for (var i = modelCascade.length - 1; i >= 0; i--) {\n    var tooltipOpt = modelCascade[i];\n    if (tooltipOpt) {\n      if (tooltipOpt instanceof Model) {\n        tooltipOpt = tooltipOpt.get('tooltip', true);\n      } // In each data item tooltip can be simply write:\n      // {\n      //  value: 10,\n      //  tooltip: 'Something you need to know'\n      // }\n\n      if (isString(tooltipOpt)) {\n        tooltipOpt = {\n          formatter: tooltipOpt\n        };\n      }\n      if (tooltipOpt) {\n        resultModel = new Model(tooltipOpt, resultModel, ecModel);\n      }\n    }\n  }\n  return resultModel;\n}\nfunction makeDispatchAction(payload, api) {\n  return payload.dispatchAction || bind(api.dispatchAction, api);\n}\nfunction refixTooltipPosition(x, y, content, viewWidth, viewHeight, gapH, gapV) {\n  var size = content.getSize();\n  var width = size[0];\n  var height = size[1];\n  if (gapH != null) {\n    // Add extra 2 pixels for this case:\n    // At present the \"values\" in default tooltip are using CSS `float: right`.\n    // When the right edge of the tooltip box is on the right side of the\n    // viewport, the `float` layout might push the \"values\" to the second line.\n    if (x + width + gapH + 2 > viewWidth) {\n      x -= width + gapH;\n    } else {\n      x += gapH;\n    }\n  }\n  if (gapV != null) {\n    if (y + height + gapV > viewHeight) {\n      y -= height + gapV;\n    } else {\n      y += gapV;\n    }\n  }\n  return [x, y];\n}\nfunction confineTooltipPosition(x, y, content, viewWidth, viewHeight) {\n  var size = content.getSize();\n  var width = size[0];\n  var height = size[1];\n  x = Math.min(x + width, viewWidth) - width;\n  y = Math.min(y + height, viewHeight) - height;\n  x = Math.max(x, 0);\n  y = Math.max(y, 0);\n  return [x, y];\n}\nfunction calcTooltipPosition(position, rect, contentSize, borderWidth) {\n  var domWidth = contentSize[0];\n  var domHeight = contentSize[1];\n  var offset = Math.ceil(Math.SQRT2 * borderWidth) + 8;\n  var x = 0;\n  var y = 0;\n  var rectWidth = rect.width;\n  var rectHeight = rect.height;\n  switch (position) {\n    case 'inside':\n      x = rect.x + rectWidth / 2 - domWidth / 2;\n      y = rect.y + rectHeight / 2 - domHeight / 2;\n      break;\n    case 'top':\n      x = rect.x + rectWidth / 2 - domWidth / 2;\n      y = rect.y - domHeight - offset;\n      break;\n    case 'bottom':\n      x = rect.x + rectWidth / 2 - domWidth / 2;\n      y = rect.y + rectHeight + offset;\n      break;\n    case 'left':\n      x = rect.x - domWidth - offset;\n      y = rect.y + rectHeight / 2 - domHeight / 2;\n      break;\n    case 'right':\n      x = rect.x + rectWidth + offset;\n      y = rect.y + rectHeight / 2 - domHeight / 2;\n  }\n  return [x, y];\n}\nfunction isCenterAlign(align) {\n  return align === 'center' || align === 'middle';\n}\n/**\r\n * Find target component by payload like:\r\n * ```js\r\n * { legendId: 'some_id', name: 'xxx' }\r\n * { toolboxIndex: 1, name: 'xxx' }\r\n * { geoName: 'some_name', name: 'xxx' }\r\n * ```\r\n * PENDING: at present only\r\n *\r\n * If not found, return null/undefined.\r\n */\n\nfunction findComponentReference(payload, ecModel, api) {\n  var queryOptionMap = preParseFinder(payload).queryOptionMap;\n  var componentMainType = queryOptionMap.keys()[0];\n  if (!componentMainType || componentMainType === 'series') {\n    return;\n  }\n  var queryResult = queryReferringComponents(ecModel, componentMainType, queryOptionMap.get(componentMainType), {\n    useDefault: false,\n    enableAll: false,\n    enableNone: false\n  });\n  var model = queryResult.models[0];\n  if (!model) {\n    return;\n  }\n  var view = api.getViewOfComponentModel(model);\n  var el;\n  view.group.traverse(function (subEl) {\n    var tooltipConfig = getECData(subEl).tooltipConfig;\n    if (tooltipConfig && tooltipConfig.name === payload.name) {\n      el = subEl;\n      return true; // stop\n    }\n  });\n\n  if (el) {\n    return {\n      componentMainType: componentMainType,\n      componentIndex: model.componentIndex,\n      el: el\n    };\n  }\n}\nexport default TooltipView;", "map": {"version": 3, "names": ["__extends", "bind", "each", "clone", "trim", "isString", "isFunction", "isArray", "isObject", "extend", "env", "TooltipHTMLContent", "TooltipRichContent", "convertToColorString", "formatTpl", "parsePercent", "Rect", "findPointFromSeries", "getLayoutRect", "Model", "globalListener", "axisHelper", "axisPointerViewHelper", "getTooltipRenderMode", "preParseFinder", "queryReferringComponents", "ComponentView", "format", "timeFormat", "getECData", "shouldTooltipConfine", "normalizeTooltipFormatResult", "createTooltipMarkup", "buildTooltipMarkup", "TooltipMarkupStyleCreator", "find<PERSON>vent<PERSON><PERSON><PERSON><PERSON><PERSON>", "clear", "createOrUpdate", "proxyRect", "shape", "x", "y", "width", "height", "TooltipView", "_super", "_this", "apply", "arguments", "type", "prototype", "init", "ecModel", "api", "node", "getDom", "tooltipModel", "getComponent", "renderMode", "_renderMode", "get", "_tooltipContent", "appendToBody", "render", "group", "removeAll", "_tooltipModel", "_ecModel", "_api", "tooltipContent", "update", "setEnterable", "_initGlobalListener", "_keepShow", "triggerOn", "register", "currTrigger", "e", "dispatchAction", "indexOf", "_tryShow", "_hide", "_lastX", "_lastY", "self_1", "clearTimeout", "_refreshUpdateTimeout", "setTimeout", "isDisposed", "manuallyShowTip", "dataByCoordSys", "_lastDataByCoordSys", "payload", "from", "uid", "makeDispatchAction", "_ticket", "cmptRef", "findComponentReference", "rect", "el", "getBoundingRect", "applyTransform", "transform", "offsetX", "offsetY", "target", "position", "<PERSON><PERSON><PERSON><PERSON>", "tooltip", "tooltipConfig", "name", "option", "tooltipOption", "seriesIndex", "_manuallyAxisShowTip", "pointInfo", "cx", "point", "cy", "getZr", "findHover", "manuallyHideTip", "hideLater", "dataIndex", "coordSysAxesInfo", "seriesModel", "getSeriesByIndex", "data", "getData", "tooltipCascadedModel", "buildTooltipModel", "getItemModel", "coordinateSystem", "model", "length", "_showAxisTooltip", "seriesDispatcher_1", "cmptDispatcher_1", "_showSeriesItemTooltip", "_showComponentItemTooltip", "_showOrMove", "cb", "delay", "_showTimout", "globalTooltipModel", "singleTooltipModel", "cbParamsList", "articleMarkup", "blocks", "<PERSON><PERSON><PERSON><PERSON>", "markupTextArrLegacy", "markupStyleCreator", "itemCoordSys", "dataByAxis", "axisItem", "axisModel", "axisDim", "axisIndex", "axisValue", "value", "axisValueLabel", "getValueLabel", "axis", "seriesDataIndices", "valueLabelOpt", "axisSectionMarkup", "header", "sortBlocks", "push", "idxItem", "series", "dataIndexInside", "cbParams", "getDataParams", "axisType", "axisId", "getAxisRawValue", "marker", "makeTooltipMarker", "color", "seriesTooltipResult", "formatTooltip", "frag", "valueFormatter", "text", "reverse", "positionExpr", "orderMode", "builtMarkupText", "unshift", "blockBreak", "allMarkupText", "join", "_updateContentNotChangedOnAxis", "_updatePosition", "_showTooltipContent", "Math", "random", "dispatcher", "ecData", "dataModel", "dataType", "tooltipTrigger", "params", "markupText", "asyncTicket", "getRawIndex", "tooltipOpt", "content", "formatter", "tooltipModelCascade", "cmpt", "componentMainType", "componentIndex", "subTooltipModel", "defaultHtml", "formatterParams", "html", "nearPoint", "_getNearestPoint", "nearPointColor", "useUTC", "params0", "isTimeAxis", "callback", "cbTicket", "<PERSON><PERSON><PERSON><PERSON>", "show", "tooltipDataParams", "trigger", "borderColor", "viewWidth", "getWidth", "viewHeight", "getHeight", "contentSize", "getSize", "align", "vAlign", "viewSize", "slice", "boxLayoutPosition", "layoutRect", "pos", "calcTooltipPosition", "refixTooltipPosition", "isCenterAlign", "confineTooltipPosition", "moveTo", "lastCoordSys", "lastCbParamsList", "_cbParamsList", "contentNotChanged", "lastItemCoordSys", "indexCoordSys", "lastDataByAxis", "thisItemCoordSys", "thisDataByAxis", "lastItem", "indexAxis", "thisItem", "lastIndices", "newIndices", "lastIdxItem", "j", "newIdxItem", "seriesIdx", "lastCbParams", "dispose", "unregister", "modelCascade", "defaultTooltipOption", "resultModel", "i", "gapH", "gapV", "size", "min", "max", "borderWidth", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "domHeight", "offset", "ceil", "SQRT2", "rectWidth", "rectHeight", "queryOptionMap", "keys", "query<PERSON><PERSON>ult", "useDefault", "enableAll", "enableNone", "models", "view", "getViewOfComponentModel", "traverse", "subEl"], "sources": ["C:/WolfSystemV2/WolfOldFrontEnd/node_modules/echarts/lib/component/tooltip/TooltipView.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\nimport { __extends } from \"tslib\";\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\n\nimport { bind, each, clone, trim, isString, isFunction, isArray, isObject, extend } from 'zrender/lib/core/util.js';\nimport env from 'zrender/lib/core/env.js';\nimport TooltipHTMLContent from './TooltipHTMLContent.js';\nimport TooltipRichContent from './TooltipRichContent.js';\nimport { convertToColorString, formatTpl } from '../../util/format.js';\nimport { parsePercent } from '../../util/number.js';\nimport { Rect } from '../../util/graphic.js';\nimport findPointFromSeries from '../axisPointer/findPointFromSeries.js';\nimport { getLayoutRect } from '../../util/layout.js';\nimport Model from '../../model/Model.js';\nimport * as globalListener from '../axisPointer/globalListener.js';\nimport * as axisHelper from '../../coord/axisHelper.js';\nimport * as axisPointerViewHelper from '../axisPointer/viewHelper.js';\nimport { getTooltipRenderMode, preParseFinder, queryReferringComponents } from '../../util/model.js';\nimport ComponentView from '../../view/Component.js';\nimport { format as timeFormat } from '../../util/time.js';\nimport { getECData } from '../../util/innerStore.js';\nimport { shouldTooltipConfine } from './helper.js';\nimport { normalizeTooltipFormatResult } from '../../model/mixin/dataFormat.js';\nimport { createTooltipMarkup, buildTooltipMarkup, TooltipMarkupStyleCreator } from './tooltipMarkup.js';\nimport { findEventDispatcher } from '../../util/event.js';\nimport { clear, createOrUpdate } from '../../util/throttle.js';\nvar proxyRect = new Rect({\n  shape: {\n    x: -1,\n    y: -1,\n    width: 2,\n    height: 2\n  }\n});\n\nvar TooltipView =\n/** @class */\nfunction (_super) {\n  __extends(TooltipView, _super);\n\n  function TooltipView() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n\n    _this.type = TooltipView.type;\n    return _this;\n  }\n\n  TooltipView.prototype.init = function (ecModel, api) {\n    if (env.node || !api.getDom()) {\n      return;\n    }\n\n    var tooltipModel = ecModel.getComponent('tooltip');\n    var renderMode = this._renderMode = getTooltipRenderMode(tooltipModel.get('renderMode'));\n    this._tooltipContent = renderMode === 'richText' ? new TooltipRichContent(api) : new TooltipHTMLContent(api.getDom(), api, {\n      appendToBody: tooltipModel.get('appendToBody', true)\n    });\n  };\n\n  TooltipView.prototype.render = function (tooltipModel, ecModel, api) {\n    if (env.node || !api.getDom()) {\n      return;\n    } // Reset\n\n\n    this.group.removeAll();\n    this._tooltipModel = tooltipModel;\n    this._ecModel = ecModel;\n    this._api = api;\n    var tooltipContent = this._tooltipContent;\n    tooltipContent.update(tooltipModel);\n    tooltipContent.setEnterable(tooltipModel.get('enterable'));\n\n    this._initGlobalListener();\n\n    this._keepShow(); // PENDING\n    // `mousemove` event will be triggered very frequently when the mouse moves fast,\n    // which causes that the `updatePosition` function was also called frequently.\n    // In Chrome with devtools open and Firefox, tooltip looks laggy and shakes. See #14695 #16101\n    // To avoid frequent triggering,\n    // consider throttling it in 50ms when transition is enabled\n\n\n    if (this._renderMode !== 'richText' && tooltipModel.get('transitionDuration')) {\n      createOrUpdate(this, '_updatePosition', 50, 'fixRate');\n    } else {\n      clear(this, '_updatePosition');\n    }\n  };\n\n  TooltipView.prototype._initGlobalListener = function () {\n    var tooltipModel = this._tooltipModel;\n    var triggerOn = tooltipModel.get('triggerOn');\n    globalListener.register('itemTooltip', this._api, bind(function (currTrigger, e, dispatchAction) {\n      // If 'none', it is not controlled by mouse totally.\n      if (triggerOn !== 'none') {\n        if (triggerOn.indexOf(currTrigger) >= 0) {\n          this._tryShow(e, dispatchAction);\n        } else if (currTrigger === 'leave') {\n          this._hide(dispatchAction);\n        }\n      }\n    }, this));\n  };\n\n  TooltipView.prototype._keepShow = function () {\n    var tooltipModel = this._tooltipModel;\n    var ecModel = this._ecModel;\n    var api = this._api;\n    var triggerOn = tooltipModel.get('triggerOn'); // Try to keep the tooltip show when refreshing\n\n    if (this._lastX != null && this._lastY != null // When user is willing to control tooltip totally using API,\n    // self.manuallyShowTip({x, y}) might cause tooltip hide,\n    // which is not expected.\n    && triggerOn !== 'none' && triggerOn !== 'click') {\n      var self_1 = this;\n      clearTimeout(this._refreshUpdateTimeout);\n      this._refreshUpdateTimeout = setTimeout(function () {\n        // Show tip next tick after other charts are rendered\n        // In case highlight action has wrong result\n        // FIXME\n        !api.isDisposed() && self_1.manuallyShowTip(tooltipModel, ecModel, api, {\n          x: self_1._lastX,\n          y: self_1._lastY,\n          dataByCoordSys: self_1._lastDataByCoordSys\n        });\n      });\n    }\n  };\n  /**\r\n   * Show tip manually by\r\n   * dispatchAction({\r\n   *     type: 'showTip',\r\n   *     x: 10,\r\n   *     y: 10\r\n   * });\r\n   * Or\r\n   * dispatchAction({\r\n   *      type: 'showTip',\r\n   *      seriesIndex: 0,\r\n   *      dataIndex or dataIndexInside or name\r\n   * });\r\n   *\r\n   *  TODO Batch\r\n   */\n\n\n  TooltipView.prototype.manuallyShowTip = function (tooltipModel, ecModel, api, payload) {\n    if (payload.from === this.uid || env.node || !api.getDom()) {\n      return;\n    }\n\n    var dispatchAction = makeDispatchAction(payload, api); // Reset ticket\n\n    this._ticket = ''; // When triggered from axisPointer.\n\n    var dataByCoordSys = payload.dataByCoordSys;\n    var cmptRef = findComponentReference(payload, ecModel, api);\n\n    if (cmptRef) {\n      var rect = cmptRef.el.getBoundingRect().clone();\n      rect.applyTransform(cmptRef.el.transform);\n\n      this._tryShow({\n        offsetX: rect.x + rect.width / 2,\n        offsetY: rect.y + rect.height / 2,\n        target: cmptRef.el,\n        position: payload.position,\n        // When manully trigger, the mouse is not on the el, so we'd better to\n        // position tooltip on the bottom of the el and display arrow is possible.\n        positionDefault: 'bottom'\n      }, dispatchAction);\n    } else if (payload.tooltip && payload.x != null && payload.y != null) {\n      var el = proxyRect;\n      el.x = payload.x;\n      el.y = payload.y;\n      el.update();\n      getECData(el).tooltipConfig = {\n        name: null,\n        option: payload.tooltip\n      }; // Manually show tooltip while view is not using zrender elements.\n\n      this._tryShow({\n        offsetX: payload.x,\n        offsetY: payload.y,\n        target: el\n      }, dispatchAction);\n    } else if (dataByCoordSys) {\n      this._tryShow({\n        offsetX: payload.x,\n        offsetY: payload.y,\n        position: payload.position,\n        dataByCoordSys: dataByCoordSys,\n        tooltipOption: payload.tooltipOption\n      }, dispatchAction);\n    } else if (payload.seriesIndex != null) {\n      if (this._manuallyAxisShowTip(tooltipModel, ecModel, api, payload)) {\n        return;\n      }\n\n      var pointInfo = findPointFromSeries(payload, ecModel);\n      var cx = pointInfo.point[0];\n      var cy = pointInfo.point[1];\n\n      if (cx != null && cy != null) {\n        this._tryShow({\n          offsetX: cx,\n          offsetY: cy,\n          target: pointInfo.el,\n          position: payload.position,\n          // When manully trigger, the mouse is not on the el, so we'd better to\n          // position tooltip on the bottom of the el and display arrow is possible.\n          positionDefault: 'bottom'\n        }, dispatchAction);\n      }\n    } else if (payload.x != null && payload.y != null) {\n      // FIXME\n      // should wrap dispatchAction like `axisPointer/globalListener` ?\n      api.dispatchAction({\n        type: 'updateAxisPointer',\n        x: payload.x,\n        y: payload.y\n      });\n\n      this._tryShow({\n        offsetX: payload.x,\n        offsetY: payload.y,\n        position: payload.position,\n        target: api.getZr().findHover(payload.x, payload.y).target\n      }, dispatchAction);\n    }\n  };\n\n  TooltipView.prototype.manuallyHideTip = function (tooltipModel, ecModel, api, payload) {\n    var tooltipContent = this._tooltipContent;\n\n    if (this._tooltipModel) {\n      tooltipContent.hideLater(this._tooltipModel.get('hideDelay'));\n    }\n\n    this._lastX = this._lastY = this._lastDataByCoordSys = null;\n\n    if (payload.from !== this.uid) {\n      this._hide(makeDispatchAction(payload, api));\n    }\n  }; // Be compatible with previous design, that is, when tooltip.type is 'axis' and\n  // dispatchAction 'showTip' with seriesIndex and dataIndex will trigger axis pointer\n  // and tooltip.\n\n\n  TooltipView.prototype._manuallyAxisShowTip = function (tooltipModel, ecModel, api, payload) {\n    var seriesIndex = payload.seriesIndex;\n    var dataIndex = payload.dataIndex; // @ts-ignore\n\n    var coordSysAxesInfo = ecModel.getComponent('axisPointer').coordSysAxesInfo;\n\n    if (seriesIndex == null || dataIndex == null || coordSysAxesInfo == null) {\n      return;\n    }\n\n    var seriesModel = ecModel.getSeriesByIndex(seriesIndex);\n\n    if (!seriesModel) {\n      return;\n    }\n\n    var data = seriesModel.getData();\n    var tooltipCascadedModel = buildTooltipModel([data.getItemModel(dataIndex), seriesModel, (seriesModel.coordinateSystem || {}).model], this._tooltipModel);\n\n    if (tooltipCascadedModel.get('trigger') !== 'axis') {\n      return;\n    }\n\n    api.dispatchAction({\n      type: 'updateAxisPointer',\n      seriesIndex: seriesIndex,\n      dataIndex: dataIndex,\n      position: payload.position\n    });\n    return true;\n  };\n\n  TooltipView.prototype._tryShow = function (e, dispatchAction) {\n    var el = e.target;\n    var tooltipModel = this._tooltipModel;\n\n    if (!tooltipModel) {\n      return;\n    } // Save mouse x, mouse y. So we can try to keep showing the tip if chart is refreshed\n\n\n    this._lastX = e.offsetX;\n    this._lastY = e.offsetY;\n    var dataByCoordSys = e.dataByCoordSys;\n\n    if (dataByCoordSys && dataByCoordSys.length) {\n      this._showAxisTooltip(dataByCoordSys, e);\n    } else if (el) {\n      this._lastDataByCoordSys = null;\n      var seriesDispatcher_1;\n      var cmptDispatcher_1;\n      findEventDispatcher(el, function (target) {\n        // Always show item tooltip if mouse is on the element with dataIndex\n        if (getECData(target).dataIndex != null) {\n          seriesDispatcher_1 = target;\n          return true;\n        } // Tooltip provided directly. Like legend.\n\n\n        if (getECData(target).tooltipConfig != null) {\n          cmptDispatcher_1 = target;\n          return true;\n        }\n      }, true);\n\n      if (seriesDispatcher_1) {\n        this._showSeriesItemTooltip(e, seriesDispatcher_1, dispatchAction);\n      } else if (cmptDispatcher_1) {\n        this._showComponentItemTooltip(e, cmptDispatcher_1, dispatchAction);\n      } else {\n        this._hide(dispatchAction);\n      }\n    } else {\n      this._lastDataByCoordSys = null;\n\n      this._hide(dispatchAction);\n    }\n  };\n\n  TooltipView.prototype._showOrMove = function (tooltipModel, cb) {\n    // showDelay is used in this case: tooltip.enterable is set\n    // as true. User intent to move mouse into tooltip and click\n    // something. `showDelay` makes it easier to enter the content\n    // but tooltip do not move immediately.\n    var delay = tooltipModel.get('showDelay');\n    cb = bind(cb, this);\n    clearTimeout(this._showTimout);\n    delay > 0 ? this._showTimout = setTimeout(cb, delay) : cb();\n  };\n\n  TooltipView.prototype._showAxisTooltip = function (dataByCoordSys, e) {\n    var ecModel = this._ecModel;\n    var globalTooltipModel = this._tooltipModel;\n    var point = [e.offsetX, e.offsetY];\n    var singleTooltipModel = buildTooltipModel([e.tooltipOption], globalTooltipModel);\n    var renderMode = this._renderMode;\n    var cbParamsList = [];\n    var articleMarkup = createTooltipMarkup('section', {\n      blocks: [],\n      noHeader: true\n    }); // Only for legacy: `Serise['formatTooltip']` returns a string.\n\n    var markupTextArrLegacy = [];\n    var markupStyleCreator = new TooltipMarkupStyleCreator();\n    each(dataByCoordSys, function (itemCoordSys) {\n      each(itemCoordSys.dataByAxis, function (axisItem) {\n        var axisModel = ecModel.getComponent(axisItem.axisDim + 'Axis', axisItem.axisIndex);\n        var axisValue = axisItem.value;\n\n        if (!axisModel || axisValue == null) {\n          return;\n        }\n\n        var axisValueLabel = axisPointerViewHelper.getValueLabel(axisValue, axisModel.axis, ecModel, axisItem.seriesDataIndices, axisItem.valueLabelOpt);\n        var axisSectionMarkup = createTooltipMarkup('section', {\n          header: axisValueLabel,\n          noHeader: !trim(axisValueLabel),\n          sortBlocks: true,\n          blocks: []\n        });\n        articleMarkup.blocks.push(axisSectionMarkup);\n        each(axisItem.seriesDataIndices, function (idxItem) {\n          var series = ecModel.getSeriesByIndex(idxItem.seriesIndex);\n          var dataIndex = idxItem.dataIndexInside;\n          var cbParams = series.getDataParams(dataIndex); // Can't find data.\n\n          if (cbParams.dataIndex < 0) {\n            return;\n          }\n\n          cbParams.axisDim = axisItem.axisDim;\n          cbParams.axisIndex = axisItem.axisIndex;\n          cbParams.axisType = axisItem.axisType;\n          cbParams.axisId = axisItem.axisId;\n          cbParams.axisValue = axisHelper.getAxisRawValue(axisModel.axis, {\n            value: axisValue\n          });\n          cbParams.axisValueLabel = axisValueLabel; // Pre-create marker style for makers. Users can assemble richText\n          // text in `formatter` callback and use those markers style.\n\n          cbParams.marker = markupStyleCreator.makeTooltipMarker('item', convertToColorString(cbParams.color), renderMode);\n          var seriesTooltipResult = normalizeTooltipFormatResult(series.formatTooltip(dataIndex, true, null));\n          var frag = seriesTooltipResult.frag;\n\n          if (frag) {\n            var valueFormatter = buildTooltipModel([series], globalTooltipModel).get('valueFormatter');\n            axisSectionMarkup.blocks.push(valueFormatter ? extend({\n              valueFormatter: valueFormatter\n            }, frag) : frag);\n          }\n\n          if (seriesTooltipResult.text) {\n            markupTextArrLegacy.push(seriesTooltipResult.text);\n          }\n\n          cbParamsList.push(cbParams);\n        });\n      });\n    }); // In most cases, the second axis is displays upper on the first one.\n    // So we reverse it to look better.\n\n    articleMarkup.blocks.reverse();\n    markupTextArrLegacy.reverse();\n    var positionExpr = e.position;\n    var orderMode = singleTooltipModel.get('order');\n    var builtMarkupText = buildTooltipMarkup(articleMarkup, markupStyleCreator, renderMode, orderMode, ecModel.get('useUTC'), singleTooltipModel.get('textStyle'));\n    builtMarkupText && markupTextArrLegacy.unshift(builtMarkupText);\n    var blockBreak = renderMode === 'richText' ? '\\n\\n' : '<br/>';\n    var allMarkupText = markupTextArrLegacy.join(blockBreak);\n\n    this._showOrMove(singleTooltipModel, function () {\n      if (this._updateContentNotChangedOnAxis(dataByCoordSys, cbParamsList)) {\n        this._updatePosition(singleTooltipModel, positionExpr, point[0], point[1], this._tooltipContent, cbParamsList);\n      } else {\n        this._showTooltipContent(singleTooltipModel, allMarkupText, cbParamsList, Math.random() + '', point[0], point[1], positionExpr, null, markupStyleCreator);\n      }\n    }); // Do not trigger events here, because this branch only be entered\n    // from dispatchAction.\n\n  };\n\n  TooltipView.prototype._showSeriesItemTooltip = function (e, dispatcher, dispatchAction) {\n    var ecModel = this._ecModel;\n    var ecData = getECData(dispatcher); // Use dataModel in element if possible\n    // Used when mouseover on a element like markPoint or edge\n    // In which case, the data is not main data in series.\n\n    var seriesIndex = ecData.seriesIndex;\n    var seriesModel = ecModel.getSeriesByIndex(seriesIndex); // For example, graph link.\n\n    var dataModel = ecData.dataModel || seriesModel;\n    var dataIndex = ecData.dataIndex;\n    var dataType = ecData.dataType;\n    var data = dataModel.getData(dataType);\n    var renderMode = this._renderMode;\n    var positionDefault = e.positionDefault;\n    var tooltipModel = buildTooltipModel([data.getItemModel(dataIndex), dataModel, seriesModel && (seriesModel.coordinateSystem || {}).model], this._tooltipModel, positionDefault ? {\n      position: positionDefault\n    } : null);\n    var tooltipTrigger = tooltipModel.get('trigger');\n\n    if (tooltipTrigger != null && tooltipTrigger !== 'item') {\n      return;\n    }\n\n    var params = dataModel.getDataParams(dataIndex, dataType);\n    var markupStyleCreator = new TooltipMarkupStyleCreator(); // Pre-create marker style for makers. Users can assemble richText\n    // text in `formatter` callback and use those markers style.\n\n    params.marker = markupStyleCreator.makeTooltipMarker('item', convertToColorString(params.color), renderMode);\n    var seriesTooltipResult = normalizeTooltipFormatResult(dataModel.formatTooltip(dataIndex, false, dataType));\n    var orderMode = tooltipModel.get('order');\n    var valueFormatter = tooltipModel.get('valueFormatter');\n    var frag = seriesTooltipResult.frag;\n    var markupText = frag ? buildTooltipMarkup(valueFormatter ? extend({\n      valueFormatter: valueFormatter\n    }, frag) : frag, markupStyleCreator, renderMode, orderMode, ecModel.get('useUTC'), tooltipModel.get('textStyle')) : seriesTooltipResult.text;\n    var asyncTicket = 'item_' + dataModel.name + '_' + dataIndex;\n\n    this._showOrMove(tooltipModel, function () {\n      this._showTooltipContent(tooltipModel, markupText, params, asyncTicket, e.offsetX, e.offsetY, e.position, e.target, markupStyleCreator);\n    }); // FIXME\n    // duplicated showtip if manuallyShowTip is called from dispatchAction.\n\n\n    dispatchAction({\n      type: 'showTip',\n      dataIndexInside: dataIndex,\n      dataIndex: data.getRawIndex(dataIndex),\n      seriesIndex: seriesIndex,\n      from: this.uid\n    });\n  };\n\n  TooltipView.prototype._showComponentItemTooltip = function (e, el, dispatchAction) {\n    var ecData = getECData(el);\n    var tooltipConfig = ecData.tooltipConfig;\n    var tooltipOpt = tooltipConfig.option || {};\n\n    if (isString(tooltipOpt)) {\n      var content = tooltipOpt;\n      tooltipOpt = {\n        content: content,\n        // Fixed formatter\n        formatter: content\n      };\n    }\n\n    var tooltipModelCascade = [tooltipOpt];\n\n    var cmpt = this._ecModel.getComponent(ecData.componentMainType, ecData.componentIndex);\n\n    if (cmpt) {\n      tooltipModelCascade.push(cmpt);\n    } // In most cases, component tooltip formatter has different params with series tooltip formatter,\n    // so that they cannot share the same formatter. Since the global tooltip formatter is used for series\n    // by convention, we do not use it as the default formatter for component.\n\n\n    tooltipModelCascade.push({\n      formatter: tooltipOpt.content\n    });\n    var positionDefault = e.positionDefault;\n    var subTooltipModel = buildTooltipModel(tooltipModelCascade, this._tooltipModel, positionDefault ? {\n      position: positionDefault\n    } : null);\n    var defaultHtml = subTooltipModel.get('content');\n    var asyncTicket = Math.random() + ''; // PENDING: this case do not support richText style yet.\n\n    var markupStyleCreator = new TooltipMarkupStyleCreator(); // Do not check whether `trigger` is 'none' here, because `trigger`\n    // only works on coordinate system. In fact, we have not found case\n    // that requires setting `trigger` nothing on component yet.\n\n    this._showOrMove(subTooltipModel, function () {\n      // Use formatterParams from element defined in component\n      // Avoid users modify it.\n      var formatterParams = clone(subTooltipModel.get('formatterParams') || {});\n\n      this._showTooltipContent(subTooltipModel, defaultHtml, formatterParams, asyncTicket, e.offsetX, e.offsetY, e.position, el, markupStyleCreator);\n    }); // If not dispatch showTip, tip may be hide triggered by axis.\n\n\n    dispatchAction({\n      type: 'showTip',\n      from: this.uid\n    });\n  };\n\n  TooltipView.prototype._showTooltipContent = function ( // Use Model<TooltipOption> insteadof TooltipModel because this model may be from series or other options.\n  // Instead of top level tooltip.\n  tooltipModel, defaultHtml, params, asyncTicket, x, y, positionExpr, el, markupStyleCreator) {\n    // Reset ticket\n    this._ticket = '';\n\n    if (!tooltipModel.get('showContent') || !tooltipModel.get('show')) {\n      return;\n    }\n\n    var tooltipContent = this._tooltipContent;\n    tooltipContent.setEnterable(tooltipModel.get('enterable'));\n    var formatter = tooltipModel.get('formatter');\n    positionExpr = positionExpr || tooltipModel.get('position');\n    var html = defaultHtml;\n\n    var nearPoint = this._getNearestPoint([x, y], params, tooltipModel.get('trigger'), tooltipModel.get('borderColor'));\n\n    var nearPointColor = nearPoint.color;\n\n    if (formatter) {\n      if (isString(formatter)) {\n        var useUTC = tooltipModel.ecModel.get('useUTC');\n        var params0 = isArray(params) ? params[0] : params;\n        var isTimeAxis = params0 && params0.axisType && params0.axisType.indexOf('time') >= 0;\n        html = formatter;\n\n        if (isTimeAxis) {\n          html = timeFormat(params0.axisValue, html, useUTC);\n        }\n\n        html = formatTpl(html, params, true);\n      } else if (isFunction(formatter)) {\n        var callback = bind(function (cbTicket, html) {\n          if (cbTicket === this._ticket) {\n            tooltipContent.setContent(html, markupStyleCreator, tooltipModel, nearPointColor, positionExpr);\n\n            this._updatePosition(tooltipModel, positionExpr, x, y, tooltipContent, params, el);\n          }\n        }, this);\n        this._ticket = asyncTicket;\n        html = formatter(params, asyncTicket, callback);\n      } else {\n        html = formatter;\n      }\n    }\n\n    tooltipContent.setContent(html, markupStyleCreator, tooltipModel, nearPointColor, positionExpr);\n    tooltipContent.show(tooltipModel, nearPointColor);\n\n    this._updatePosition(tooltipModel, positionExpr, x, y, tooltipContent, params, el);\n  };\n\n  TooltipView.prototype._getNearestPoint = function (point, tooltipDataParams, trigger, borderColor) {\n    if (trigger === 'axis' || isArray(tooltipDataParams)) {\n      return {\n        color: borderColor || (this._renderMode === 'html' ? '#fff' : 'none')\n      };\n    }\n\n    if (!isArray(tooltipDataParams)) {\n      return {\n        color: borderColor || tooltipDataParams.color || tooltipDataParams.borderColor\n      };\n    }\n  };\n\n  TooltipView.prototype._updatePosition = function (tooltipModel, positionExpr, x, // Mouse x\n  y, // Mouse y\n  content, params, el) {\n    var viewWidth = this._api.getWidth();\n\n    var viewHeight = this._api.getHeight();\n\n    positionExpr = positionExpr || tooltipModel.get('position');\n    var contentSize = content.getSize();\n    var align = tooltipModel.get('align');\n    var vAlign = tooltipModel.get('verticalAlign');\n    var rect = el && el.getBoundingRect().clone();\n    el && rect.applyTransform(el.transform);\n\n    if (isFunction(positionExpr)) {\n      // Callback of position can be an array or a string specify the position\n      positionExpr = positionExpr([x, y], params, content.el, rect, {\n        viewSize: [viewWidth, viewHeight],\n        contentSize: contentSize.slice()\n      });\n    }\n\n    if (isArray(positionExpr)) {\n      x = parsePercent(positionExpr[0], viewWidth);\n      y = parsePercent(positionExpr[1], viewHeight);\n    } else if (isObject(positionExpr)) {\n      var boxLayoutPosition = positionExpr;\n      boxLayoutPosition.width = contentSize[0];\n      boxLayoutPosition.height = contentSize[1];\n      var layoutRect = getLayoutRect(boxLayoutPosition, {\n        width: viewWidth,\n        height: viewHeight\n      });\n      x = layoutRect.x;\n      y = layoutRect.y;\n      align = null; // When positionExpr is left/top/right/bottom,\n      // align and verticalAlign will not work.\n\n      vAlign = null;\n    } // Specify tooltip position by string 'top' 'bottom' 'left' 'right' around graphic element\n    else if (isString(positionExpr) && el) {\n        var pos = calcTooltipPosition(positionExpr, rect, contentSize, tooltipModel.get('borderWidth'));\n        x = pos[0];\n        y = pos[1];\n      } else {\n        var pos = refixTooltipPosition(x, y, content, viewWidth, viewHeight, align ? null : 20, vAlign ? null : 20);\n        x = pos[0];\n        y = pos[1];\n      }\n\n    align && (x -= isCenterAlign(align) ? contentSize[0] / 2 : align === 'right' ? contentSize[0] : 0);\n    vAlign && (y -= isCenterAlign(vAlign) ? contentSize[1] / 2 : vAlign === 'bottom' ? contentSize[1] : 0);\n\n    if (shouldTooltipConfine(tooltipModel)) {\n      var pos = confineTooltipPosition(x, y, content, viewWidth, viewHeight);\n      x = pos[0];\n      y = pos[1];\n    }\n\n    content.moveTo(x, y);\n  }; // FIXME\n  // Should we remove this but leave this to user?\n\n\n  TooltipView.prototype._updateContentNotChangedOnAxis = function (dataByCoordSys, cbParamsList) {\n    var lastCoordSys = this._lastDataByCoordSys;\n    var lastCbParamsList = this._cbParamsList;\n    var contentNotChanged = !!lastCoordSys && lastCoordSys.length === dataByCoordSys.length;\n    contentNotChanged && each(lastCoordSys, function (lastItemCoordSys, indexCoordSys) {\n      var lastDataByAxis = lastItemCoordSys.dataByAxis || [];\n      var thisItemCoordSys = dataByCoordSys[indexCoordSys] || {};\n      var thisDataByAxis = thisItemCoordSys.dataByAxis || [];\n      contentNotChanged = contentNotChanged && lastDataByAxis.length === thisDataByAxis.length;\n      contentNotChanged && each(lastDataByAxis, function (lastItem, indexAxis) {\n        var thisItem = thisDataByAxis[indexAxis] || {};\n        var lastIndices = lastItem.seriesDataIndices || [];\n        var newIndices = thisItem.seriesDataIndices || [];\n        contentNotChanged = contentNotChanged && lastItem.value === thisItem.value && lastItem.axisType === thisItem.axisType && lastItem.axisId === thisItem.axisId && lastIndices.length === newIndices.length;\n        contentNotChanged && each(lastIndices, function (lastIdxItem, j) {\n          var newIdxItem = newIndices[j];\n          contentNotChanged = contentNotChanged && lastIdxItem.seriesIndex === newIdxItem.seriesIndex && lastIdxItem.dataIndex === newIdxItem.dataIndex;\n        }); // check is cbParams data value changed\n\n        lastCbParamsList && each(lastItem.seriesDataIndices, function (idxItem) {\n          var seriesIdx = idxItem.seriesIndex;\n          var cbParams = cbParamsList[seriesIdx];\n          var lastCbParams = lastCbParamsList[seriesIdx];\n\n          if (cbParams && lastCbParams && lastCbParams.data !== cbParams.data) {\n            contentNotChanged = false;\n          }\n        });\n      });\n    });\n    this._lastDataByCoordSys = dataByCoordSys;\n    this._cbParamsList = cbParamsList;\n    return !!contentNotChanged;\n  };\n\n  TooltipView.prototype._hide = function (dispatchAction) {\n    // Do not directly hideLater here, because this behavior may be prevented\n    // in dispatchAction when showTip is dispatched.\n    // FIXME\n    // duplicated hideTip if manuallyHideTip is called from dispatchAction.\n    this._lastDataByCoordSys = null;\n    dispatchAction({\n      type: 'hideTip',\n      from: this.uid\n    });\n  };\n\n  TooltipView.prototype.dispose = function (ecModel, api) {\n    if (env.node || !api.getDom()) {\n      return;\n    }\n\n    clear(this, '_updatePosition');\n\n    this._tooltipContent.dispose();\n\n    globalListener.unregister('itemTooltip', api);\n  };\n\n  TooltipView.type = 'tooltip';\n  return TooltipView;\n}(ComponentView);\n/**\r\n * From top to bottom. (the last one should be globalTooltipModel);\r\n */\n\n\nfunction buildTooltipModel(modelCascade, globalTooltipModel, defaultTooltipOption) {\n  // Last is always tooltip model.\n  var ecModel = globalTooltipModel.ecModel;\n  var resultModel;\n\n  if (defaultTooltipOption) {\n    resultModel = new Model(defaultTooltipOption, ecModel, ecModel);\n    resultModel = new Model(globalTooltipModel.option, resultModel, ecModel);\n  } else {\n    resultModel = globalTooltipModel;\n  }\n\n  for (var i = modelCascade.length - 1; i >= 0; i--) {\n    var tooltipOpt = modelCascade[i];\n\n    if (tooltipOpt) {\n      if (tooltipOpt instanceof Model) {\n        tooltipOpt = tooltipOpt.get('tooltip', true);\n      } // In each data item tooltip can be simply write:\n      // {\n      //  value: 10,\n      //  tooltip: 'Something you need to know'\n      // }\n\n\n      if (isString(tooltipOpt)) {\n        tooltipOpt = {\n          formatter: tooltipOpt\n        };\n      }\n\n      if (tooltipOpt) {\n        resultModel = new Model(tooltipOpt, resultModel, ecModel);\n      }\n    }\n  }\n\n  return resultModel;\n}\n\nfunction makeDispatchAction(payload, api) {\n  return payload.dispatchAction || bind(api.dispatchAction, api);\n}\n\nfunction refixTooltipPosition(x, y, content, viewWidth, viewHeight, gapH, gapV) {\n  var size = content.getSize();\n  var width = size[0];\n  var height = size[1];\n\n  if (gapH != null) {\n    // Add extra 2 pixels for this case:\n    // At present the \"values\" in default tooltip are using CSS `float: right`.\n    // When the right edge of the tooltip box is on the right side of the\n    // viewport, the `float` layout might push the \"values\" to the second line.\n    if (x + width + gapH + 2 > viewWidth) {\n      x -= width + gapH;\n    } else {\n      x += gapH;\n    }\n  }\n\n  if (gapV != null) {\n    if (y + height + gapV > viewHeight) {\n      y -= height + gapV;\n    } else {\n      y += gapV;\n    }\n  }\n\n  return [x, y];\n}\n\nfunction confineTooltipPosition(x, y, content, viewWidth, viewHeight) {\n  var size = content.getSize();\n  var width = size[0];\n  var height = size[1];\n  x = Math.min(x + width, viewWidth) - width;\n  y = Math.min(y + height, viewHeight) - height;\n  x = Math.max(x, 0);\n  y = Math.max(y, 0);\n  return [x, y];\n}\n\nfunction calcTooltipPosition(position, rect, contentSize, borderWidth) {\n  var domWidth = contentSize[0];\n  var domHeight = contentSize[1];\n  var offset = Math.ceil(Math.SQRT2 * borderWidth) + 8;\n  var x = 0;\n  var y = 0;\n  var rectWidth = rect.width;\n  var rectHeight = rect.height;\n\n  switch (position) {\n    case 'inside':\n      x = rect.x + rectWidth / 2 - domWidth / 2;\n      y = rect.y + rectHeight / 2 - domHeight / 2;\n      break;\n\n    case 'top':\n      x = rect.x + rectWidth / 2 - domWidth / 2;\n      y = rect.y - domHeight - offset;\n      break;\n\n    case 'bottom':\n      x = rect.x + rectWidth / 2 - domWidth / 2;\n      y = rect.y + rectHeight + offset;\n      break;\n\n    case 'left':\n      x = rect.x - domWidth - offset;\n      y = rect.y + rectHeight / 2 - domHeight / 2;\n      break;\n\n    case 'right':\n      x = rect.x + rectWidth + offset;\n      y = rect.y + rectHeight / 2 - domHeight / 2;\n  }\n\n  return [x, y];\n}\n\nfunction isCenterAlign(align) {\n  return align === 'center' || align === 'middle';\n}\n/**\r\n * Find target component by payload like:\r\n * ```js\r\n * { legendId: 'some_id', name: 'xxx' }\r\n * { toolboxIndex: 1, name: 'xxx' }\r\n * { geoName: 'some_name', name: 'xxx' }\r\n * ```\r\n * PENDING: at present only\r\n *\r\n * If not found, return null/undefined.\r\n */\n\n\nfunction findComponentReference(payload, ecModel, api) {\n  var queryOptionMap = preParseFinder(payload).queryOptionMap;\n  var componentMainType = queryOptionMap.keys()[0];\n\n  if (!componentMainType || componentMainType === 'series') {\n    return;\n  }\n\n  var queryResult = queryReferringComponents(ecModel, componentMainType, queryOptionMap.get(componentMainType), {\n    useDefault: false,\n    enableAll: false,\n    enableNone: false\n  });\n  var model = queryResult.models[0];\n\n  if (!model) {\n    return;\n  }\n\n  var view = api.getViewOfComponentModel(model);\n  var el;\n  view.group.traverse(function (subEl) {\n    var tooltipConfig = getECData(subEl).tooltipConfig;\n\n    if (tooltipConfig && tooltipConfig.name === payload.name) {\n      el = subEl;\n      return true; // stop\n    }\n  });\n\n  if (el) {\n    return {\n      componentMainType: componentMainType,\n      componentIndex: model.componentIndex,\n      el: el\n    };\n  }\n}\n\nexport default TooltipView;"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA,SAASA,SAAS,QAAQ,OAAO;AACjC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAASC,IAAI,EAAEC,IAAI,EAAEC,KAAK,EAAEC,IAAI,EAAEC,QAAQ,EAAEC,UAAU,EAAEC,OAAO,EAAEC,QAAQ,EAAEC,MAAM,QAAQ,0BAA0B;AACnH,OAAOC,GAAG,MAAM,yBAAyB;AACzC,OAAOC,kBAAkB,MAAM,yBAAyB;AACxD,OAAOC,kBAAkB,MAAM,yBAAyB;AACxD,SAASC,oBAAoB,EAAEC,SAAS,QAAQ,sBAAsB;AACtE,SAASC,YAAY,QAAQ,sBAAsB;AACnD,SAASC,IAAI,QAAQ,uBAAuB;AAC5C,OAAOC,mBAAmB,MAAM,uCAAuC;AACvE,SAASC,aAAa,QAAQ,sBAAsB;AACpD,OAAOC,KAAK,MAAM,sBAAsB;AACxC,OAAO,KAAKC,cAAc,MAAM,kCAAkC;AAClE,OAAO,KAAKC,UAAU,MAAM,2BAA2B;AACvD,OAAO,KAAKC,qBAAqB,MAAM,8BAA8B;AACrE,SAASC,oBAAoB,EAAEC,cAAc,EAAEC,wBAAwB,QAAQ,qBAAqB;AACpG,OAAOC,aAAa,MAAM,yBAAyB;AACnD,SAASC,MAAM,IAAIC,UAAU,QAAQ,oBAAoB;AACzD,SAASC,SAAS,QAAQ,0BAA0B;AACpD,SAASC,oBAAoB,QAAQ,aAAa;AAClD,SAASC,4BAA4B,QAAQ,iCAAiC;AAC9E,SAASC,mBAAmB,EAAEC,kBAAkB,EAAEC,yBAAyB,QAAQ,oBAAoB;AACvG,SAASC,mBAAmB,QAAQ,qBAAqB;AACzD,SAASC,KAAK,EAAEC,cAAc,QAAQ,wBAAwB;AAC9D,IAAIC,SAAS,GAAG,IAAItB,IAAI,CAAC;EACvBuB,KAAK,EAAE;IACLC,CAAC,EAAE,CAAC,CAAC;IACLC,CAAC,EAAE,CAAC,CAAC;IACLC,KAAK,EAAE,CAAC;IACRC,MAAM,EAAE;EACV;AACF,CAAC,CAAC;AAEF,IAAIC,WAAW,GACf;AACA,UAAUC,MAAM,EAAE;EAChB7C,SAAS,CAAC4C,WAAW,EAAEC,MAAM,CAAC;EAE9B,SAASD,WAAWA,CAAA,EAAG;IACrB,IAAIE,KAAK,GAAGD,MAAM,KAAK,IAAI,IAAIA,MAAM,CAACE,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC,IAAI,IAAI;IAEpEF,KAAK,CAACG,IAAI,GAAGL,WAAW,CAACK,IAAI;IAC7B,OAAOH,KAAK;EACd;EAEAF,WAAW,CAACM,SAAS,CAACC,IAAI,GAAG,UAAUC,OAAO,EAAEC,GAAG,EAAE;IACnD,IAAI3C,GAAG,CAAC4C,IAAI,IAAI,CAACD,GAAG,CAACE,MAAM,CAAC,CAAC,EAAE;MAC7B;IACF;IAEA,IAAIC,YAAY,GAAGJ,OAAO,CAACK,YAAY,CAAC,SAAS,CAAC;IAClD,IAAIC,UAAU,GAAG,IAAI,CAACC,WAAW,GAAGpC,oBAAoB,CAACiC,YAAY,CAACI,GAAG,CAAC,YAAY,CAAC,CAAC;IACxF,IAAI,CAACC,eAAe,GAAGH,UAAU,KAAK,UAAU,GAAG,IAAI9C,kBAAkB,CAACyC,GAAG,CAAC,GAAG,IAAI1C,kBAAkB,CAAC0C,GAAG,CAACE,MAAM,CAAC,CAAC,EAAEF,GAAG,EAAE;MACzHS,YAAY,EAAEN,YAAY,CAACI,GAAG,CAAC,cAAc,EAAE,IAAI;IACrD,CAAC,CAAC;EACJ,CAAC;EAEDhB,WAAW,CAACM,SAAS,CAACa,MAAM,GAAG,UAAUP,YAAY,EAAEJ,OAAO,EAAEC,GAAG,EAAE;IACnE,IAAI3C,GAAG,CAAC4C,IAAI,IAAI,CAACD,GAAG,CAACE,MAAM,CAAC,CAAC,EAAE;MAC7B;IACF,CAAC,CAAC;;IAGF,IAAI,CAACS,KAAK,CAACC,SAAS,CAAC,CAAC;IACtB,IAAI,CAACC,aAAa,GAAGV,YAAY;IACjC,IAAI,CAACW,QAAQ,GAAGf,OAAO;IACvB,IAAI,CAACgB,IAAI,GAAGf,GAAG;IACf,IAAIgB,cAAc,GAAG,IAAI,CAACR,eAAe;IACzCQ,cAAc,CAACC,MAAM,CAACd,YAAY,CAAC;IACnCa,cAAc,CAACE,YAAY,CAACf,YAAY,CAACI,GAAG,CAAC,WAAW,CAAC,CAAC;IAE1D,IAAI,CAACY,mBAAmB,CAAC,CAAC;IAE1B,IAAI,CAACC,SAAS,CAAC,CAAC,CAAC,CAAC;IAClB;IACA;IACA;IACA;IACA;;IAGA,IAAI,IAAI,CAACd,WAAW,KAAK,UAAU,IAAIH,YAAY,CAACI,GAAG,CAAC,oBAAoB,CAAC,EAAE;MAC7EvB,cAAc,CAAC,IAAI,EAAE,iBAAiB,EAAE,EAAE,EAAE,SAAS,CAAC;IACxD,CAAC,MAAM;MACLD,KAAK,CAAC,IAAI,EAAE,iBAAiB,CAAC;IAChC;EACF,CAAC;EAEDQ,WAAW,CAACM,SAAS,CAACsB,mBAAmB,GAAG,YAAY;IACtD,IAAIhB,YAAY,GAAG,IAAI,CAACU,aAAa;IACrC,IAAIQ,SAAS,GAAGlB,YAAY,CAACI,GAAG,CAAC,WAAW,CAAC;IAC7CxC,cAAc,CAACuD,QAAQ,CAAC,aAAa,EAAE,IAAI,CAACP,IAAI,EAAEnE,IAAI,CAAC,UAAU2E,WAAW,EAAEC,CAAC,EAAEC,cAAc,EAAE;MAC/F;MACA,IAAIJ,SAAS,KAAK,MAAM,EAAE;QACxB,IAAIA,SAAS,CAACK,OAAO,CAACH,WAAW,CAAC,IAAI,CAAC,EAAE;UACvC,IAAI,CAACI,QAAQ,CAACH,CAAC,EAAEC,cAAc,CAAC;QAClC,CAAC,MAAM,IAAIF,WAAW,KAAK,OAAO,EAAE;UAClC,IAAI,CAACK,KAAK,CAACH,cAAc,CAAC;QAC5B;MACF;IACF,CAAC,EAAE,IAAI,CAAC,CAAC;EACX,CAAC;EAEDlC,WAAW,CAACM,SAAS,CAACuB,SAAS,GAAG,YAAY;IAC5C,IAAIjB,YAAY,GAAG,IAAI,CAACU,aAAa;IACrC,IAAId,OAAO,GAAG,IAAI,CAACe,QAAQ;IAC3B,IAAId,GAAG,GAAG,IAAI,CAACe,IAAI;IACnB,IAAIM,SAAS,GAAGlB,YAAY,CAACI,GAAG,CAAC,WAAW,CAAC,CAAC,CAAC;;IAE/C,IAAI,IAAI,CAACsB,MAAM,IAAI,IAAI,IAAI,IAAI,CAACC,MAAM,IAAI,IAAI,CAAC;IAC/C;IACA;IAAA,GACGT,SAAS,KAAK,MAAM,IAAIA,SAAS,KAAK,OAAO,EAAE;MAChD,IAAIU,MAAM,GAAG,IAAI;MACjBC,YAAY,CAAC,IAAI,CAACC,qBAAqB,CAAC;MACxC,IAAI,CAACA,qBAAqB,GAAGC,UAAU,CAAC,YAAY;QAClD;QACA;QACA;QACA,CAAClC,GAAG,CAACmC,UAAU,CAAC,CAAC,IAAIJ,MAAM,CAACK,eAAe,CAACjC,YAAY,EAAEJ,OAAO,EAAEC,GAAG,EAAE;UACtEb,CAAC,EAAE4C,MAAM,CAACF,MAAM;UAChBzC,CAAC,EAAE2C,MAAM,CAACD,MAAM;UAChBO,cAAc,EAAEN,MAAM,CAACO;QACzB,CAAC,CAAC;MACJ,CAAC,CAAC;IACJ;EACF,CAAC;EACD;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;EAGE/C,WAAW,CAACM,SAAS,CAACuC,eAAe,GAAG,UAAUjC,YAAY,EAAEJ,OAAO,EAAEC,GAAG,EAAEuC,OAAO,EAAE;IACrF,IAAIA,OAAO,CAACC,IAAI,KAAK,IAAI,CAACC,GAAG,IAAIpF,GAAG,CAAC4C,IAAI,IAAI,CAACD,GAAG,CAACE,MAAM,CAAC,CAAC,EAAE;MAC1D;IACF;IAEA,IAAIuB,cAAc,GAAGiB,kBAAkB,CAACH,OAAO,EAAEvC,GAAG,CAAC,CAAC,CAAC;;IAEvD,IAAI,CAAC2C,OAAO,GAAG,EAAE,CAAC,CAAC;;IAEnB,IAAIN,cAAc,GAAGE,OAAO,CAACF,cAAc;IAC3C,IAAIO,OAAO,GAAGC,sBAAsB,CAACN,OAAO,EAAExC,OAAO,EAAEC,GAAG,CAAC;IAE3D,IAAI4C,OAAO,EAAE;MACX,IAAIE,IAAI,GAAGF,OAAO,CAACG,EAAE,CAACC,eAAe,CAAC,CAAC,CAAClG,KAAK,CAAC,CAAC;MAC/CgG,IAAI,CAACG,cAAc,CAACL,OAAO,CAACG,EAAE,CAACG,SAAS,CAAC;MAEzC,IAAI,CAACvB,QAAQ,CAAC;QACZwB,OAAO,EAAEL,IAAI,CAAC3D,CAAC,GAAG2D,IAAI,CAACzD,KAAK,GAAG,CAAC;QAChC+D,OAAO,EAAEN,IAAI,CAAC1D,CAAC,GAAG0D,IAAI,CAACxD,MAAM,GAAG,CAAC;QACjC+D,MAAM,EAAET,OAAO,CAACG,EAAE;QAClBO,QAAQ,EAAEf,OAAO,CAACe,QAAQ;QAC1B;QACA;QACAC,eAAe,EAAE;MACnB,CAAC,EAAE9B,cAAc,CAAC;IACpB,CAAC,MAAM,IAAIc,OAAO,CAACiB,OAAO,IAAIjB,OAAO,CAACpD,CAAC,IAAI,IAAI,IAAIoD,OAAO,CAACnD,CAAC,IAAI,IAAI,EAAE;MACpE,IAAI2D,EAAE,GAAG9D,SAAS;MAClB8D,EAAE,CAAC5D,CAAC,GAAGoD,OAAO,CAACpD,CAAC;MAChB4D,EAAE,CAAC3D,CAAC,GAAGmD,OAAO,CAACnD,CAAC;MAChB2D,EAAE,CAAC9B,MAAM,CAAC,CAAC;MACXzC,SAAS,CAACuE,EAAE,CAAC,CAACU,aAAa,GAAG;QAC5BC,IAAI,EAAE,IAAI;QACVC,MAAM,EAAEpB,OAAO,CAACiB;MAClB,CAAC,CAAC,CAAC;;MAEH,IAAI,CAAC7B,QAAQ,CAAC;QACZwB,OAAO,EAAEZ,OAAO,CAACpD,CAAC;QAClBiE,OAAO,EAAEb,OAAO,CAACnD,CAAC;QAClBiE,MAAM,EAAEN;MACV,CAAC,EAAEtB,cAAc,CAAC;IACpB,CAAC,MAAM,IAAIY,cAAc,EAAE;MACzB,IAAI,CAACV,QAAQ,CAAC;QACZwB,OAAO,EAAEZ,OAAO,CAACpD,CAAC;QAClBiE,OAAO,EAAEb,OAAO,CAACnD,CAAC;QAClBkE,QAAQ,EAAEf,OAAO,CAACe,QAAQ;QAC1BjB,cAAc,EAAEA,cAAc;QAC9BuB,aAAa,EAAErB,OAAO,CAACqB;MACzB,CAAC,EAAEnC,cAAc,CAAC;IACpB,CAAC,MAAM,IAAIc,OAAO,CAACsB,WAAW,IAAI,IAAI,EAAE;MACtC,IAAI,IAAI,CAACC,oBAAoB,CAAC3D,YAAY,EAAEJ,OAAO,EAAEC,GAAG,EAAEuC,OAAO,CAAC,EAAE;QAClE;MACF;MAEA,IAAIwB,SAAS,GAAGnG,mBAAmB,CAAC2E,OAAO,EAAExC,OAAO,CAAC;MACrD,IAAIiE,EAAE,GAAGD,SAAS,CAACE,KAAK,CAAC,CAAC,CAAC;MAC3B,IAAIC,EAAE,GAAGH,SAAS,CAACE,KAAK,CAAC,CAAC,CAAC;MAE3B,IAAID,EAAE,IAAI,IAAI,IAAIE,EAAE,IAAI,IAAI,EAAE;QAC5B,IAAI,CAACvC,QAAQ,CAAC;UACZwB,OAAO,EAAEa,EAAE;UACXZ,OAAO,EAAEc,EAAE;UACXb,MAAM,EAAEU,SAAS,CAAChB,EAAE;UACpBO,QAAQ,EAAEf,OAAO,CAACe,QAAQ;UAC1B;UACA;UACAC,eAAe,EAAE;QACnB,CAAC,EAAE9B,cAAc,CAAC;MACpB;IACF,CAAC,MAAM,IAAIc,OAAO,CAACpD,CAAC,IAAI,IAAI,IAAIoD,OAAO,CAACnD,CAAC,IAAI,IAAI,EAAE;MACjD;MACA;MACAY,GAAG,CAACyB,cAAc,CAAC;QACjB7B,IAAI,EAAE,mBAAmB;QACzBT,CAAC,EAAEoD,OAAO,CAACpD,CAAC;QACZC,CAAC,EAAEmD,OAAO,CAACnD;MACb,CAAC,CAAC;MAEF,IAAI,CAACuC,QAAQ,CAAC;QACZwB,OAAO,EAAEZ,OAAO,CAACpD,CAAC;QAClBiE,OAAO,EAAEb,OAAO,CAACnD,CAAC;QAClBkE,QAAQ,EAAEf,OAAO,CAACe,QAAQ;QAC1BD,MAAM,EAAErD,GAAG,CAACmE,KAAK,CAAC,CAAC,CAACC,SAAS,CAAC7B,OAAO,CAACpD,CAAC,EAAEoD,OAAO,CAACnD,CAAC,CAAC,CAACiE;MACtD,CAAC,EAAE5B,cAAc,CAAC;IACpB;EACF,CAAC;EAEDlC,WAAW,CAACM,SAAS,CAACwE,eAAe,GAAG,UAAUlE,YAAY,EAAEJ,OAAO,EAAEC,GAAG,EAAEuC,OAAO,EAAE;IACrF,IAAIvB,cAAc,GAAG,IAAI,CAACR,eAAe;IAEzC,IAAI,IAAI,CAACK,aAAa,EAAE;MACtBG,cAAc,CAACsD,SAAS,CAAC,IAAI,CAACzD,aAAa,CAACN,GAAG,CAAC,WAAW,CAAC,CAAC;IAC/D;IAEA,IAAI,CAACsB,MAAM,GAAG,IAAI,CAACC,MAAM,GAAG,IAAI,CAACQ,mBAAmB,GAAG,IAAI;IAE3D,IAAIC,OAAO,CAACC,IAAI,KAAK,IAAI,CAACC,GAAG,EAAE;MAC7B,IAAI,CAACb,KAAK,CAACc,kBAAkB,CAACH,OAAO,EAAEvC,GAAG,CAAC,CAAC;IAC9C;EACF,CAAC,CAAC,CAAC;EACH;EACA;;EAGAT,WAAW,CAACM,SAAS,CAACiE,oBAAoB,GAAG,UAAU3D,YAAY,EAAEJ,OAAO,EAAEC,GAAG,EAAEuC,OAAO,EAAE;IAC1F,IAAIsB,WAAW,GAAGtB,OAAO,CAACsB,WAAW;IACrC,IAAIU,SAAS,GAAGhC,OAAO,CAACgC,SAAS,CAAC,CAAC;;IAEnC,IAAIC,gBAAgB,GAAGzE,OAAO,CAACK,YAAY,CAAC,aAAa,CAAC,CAACoE,gBAAgB;IAE3E,IAAIX,WAAW,IAAI,IAAI,IAAIU,SAAS,IAAI,IAAI,IAAIC,gBAAgB,IAAI,IAAI,EAAE;MACxE;IACF;IAEA,IAAIC,WAAW,GAAG1E,OAAO,CAAC2E,gBAAgB,CAACb,WAAW,CAAC;IAEvD,IAAI,CAACY,WAAW,EAAE;MAChB;IACF;IAEA,IAAIE,IAAI,GAAGF,WAAW,CAACG,OAAO,CAAC,CAAC;IAChC,IAAIC,oBAAoB,GAAGC,iBAAiB,CAAC,CAACH,IAAI,CAACI,YAAY,CAACR,SAAS,CAAC,EAAEE,WAAW,EAAE,CAACA,WAAW,CAACO,gBAAgB,IAAI,CAAC,CAAC,EAAEC,KAAK,CAAC,EAAE,IAAI,CAACpE,aAAa,CAAC;IAEzJ,IAAIgE,oBAAoB,CAACtE,GAAG,CAAC,SAAS,CAAC,KAAK,MAAM,EAAE;MAClD;IACF;IAEAP,GAAG,CAACyB,cAAc,CAAC;MACjB7B,IAAI,EAAE,mBAAmB;MACzBiE,WAAW,EAAEA,WAAW;MACxBU,SAAS,EAAEA,SAAS;MACpBjB,QAAQ,EAAEf,OAAO,CAACe;IACpB,CAAC,CAAC;IACF,OAAO,IAAI;EACb,CAAC;EAED/D,WAAW,CAACM,SAAS,CAAC8B,QAAQ,GAAG,UAAUH,CAAC,EAAEC,cAAc,EAAE;IAC5D,IAAIsB,EAAE,GAAGvB,CAAC,CAAC6B,MAAM;IACjB,IAAIlD,YAAY,GAAG,IAAI,CAACU,aAAa;IAErC,IAAI,CAACV,YAAY,EAAE;MACjB;IACF,CAAC,CAAC;;IAGF,IAAI,CAAC0B,MAAM,GAAGL,CAAC,CAAC2B,OAAO;IACvB,IAAI,CAACrB,MAAM,GAAGN,CAAC,CAAC4B,OAAO;IACvB,IAAIf,cAAc,GAAGb,CAAC,CAACa,cAAc;IAErC,IAAIA,cAAc,IAAIA,cAAc,CAAC6C,MAAM,EAAE;MAC3C,IAAI,CAACC,gBAAgB,CAAC9C,cAAc,EAAEb,CAAC,CAAC;IAC1C,CAAC,MAAM,IAAIuB,EAAE,EAAE;MACb,IAAI,CAACT,mBAAmB,GAAG,IAAI;MAC/B,IAAI8C,kBAAkB;MACtB,IAAIC,gBAAgB;MACpBvG,mBAAmB,CAACiE,EAAE,EAAE,UAAUM,MAAM,EAAE;QACxC;QACA,IAAI7E,SAAS,CAAC6E,MAAM,CAAC,CAACkB,SAAS,IAAI,IAAI,EAAE;UACvCa,kBAAkB,GAAG/B,MAAM;UAC3B,OAAO,IAAI;QACb,CAAC,CAAC;;QAGF,IAAI7E,SAAS,CAAC6E,MAAM,CAAC,CAACI,aAAa,IAAI,IAAI,EAAE;UAC3C4B,gBAAgB,GAAGhC,MAAM;UACzB,OAAO,IAAI;QACb;MACF,CAAC,EAAE,IAAI,CAAC;MAER,IAAI+B,kBAAkB,EAAE;QACtB,IAAI,CAACE,sBAAsB,CAAC9D,CAAC,EAAE4D,kBAAkB,EAAE3D,cAAc,CAAC;MACpE,CAAC,MAAM,IAAI4D,gBAAgB,EAAE;QAC3B,IAAI,CAACE,yBAAyB,CAAC/D,CAAC,EAAE6D,gBAAgB,EAAE5D,cAAc,CAAC;MACrE,CAAC,MAAM;QACL,IAAI,CAACG,KAAK,CAACH,cAAc,CAAC;MAC5B;IACF,CAAC,MAAM;MACL,IAAI,CAACa,mBAAmB,GAAG,IAAI;MAE/B,IAAI,CAACV,KAAK,CAACH,cAAc,CAAC;IAC5B;EACF,CAAC;EAEDlC,WAAW,CAACM,SAAS,CAAC2F,WAAW,GAAG,UAAUrF,YAAY,EAAEsF,EAAE,EAAE;IAC9D;IACA;IACA;IACA;IACA,IAAIC,KAAK,GAAGvF,YAAY,CAACI,GAAG,CAAC,WAAW,CAAC;IACzCkF,EAAE,GAAG7I,IAAI,CAAC6I,EAAE,EAAE,IAAI,CAAC;IACnBzD,YAAY,CAAC,IAAI,CAAC2D,WAAW,CAAC;IAC9BD,KAAK,GAAG,CAAC,GAAG,IAAI,CAACC,WAAW,GAAGzD,UAAU,CAACuD,EAAE,EAAEC,KAAK,CAAC,GAAGD,EAAE,CAAC,CAAC;EAC7D,CAAC;EAEDlG,WAAW,CAACM,SAAS,CAACsF,gBAAgB,GAAG,UAAU9C,cAAc,EAAEb,CAAC,EAAE;IACpE,IAAIzB,OAAO,GAAG,IAAI,CAACe,QAAQ;IAC3B,IAAI8E,kBAAkB,GAAG,IAAI,CAAC/E,aAAa;IAC3C,IAAIoD,KAAK,GAAG,CAACzC,CAAC,CAAC2B,OAAO,EAAE3B,CAAC,CAAC4B,OAAO,CAAC;IAClC,IAAIyC,kBAAkB,GAAGf,iBAAiB,CAAC,CAACtD,CAAC,CAACoC,aAAa,CAAC,EAAEgC,kBAAkB,CAAC;IACjF,IAAIvF,UAAU,GAAG,IAAI,CAACC,WAAW;IACjC,IAAIwF,YAAY,GAAG,EAAE;IACrB,IAAIC,aAAa,GAAGpH,mBAAmB,CAAC,SAAS,EAAE;MACjDqH,MAAM,EAAE,EAAE;MACVC,QAAQ,EAAE;IACZ,CAAC,CAAC,CAAC,CAAC;;IAEJ,IAAIC,mBAAmB,GAAG,EAAE;IAC5B,IAAIC,kBAAkB,GAAG,IAAItH,yBAAyB,CAAC,CAAC;IACxDhC,IAAI,CAACwF,cAAc,EAAE,UAAU+D,YAAY,EAAE;MAC3CvJ,IAAI,CAACuJ,YAAY,CAACC,UAAU,EAAE,UAAUC,QAAQ,EAAE;QAChD,IAAIC,SAAS,GAAGxG,OAAO,CAACK,YAAY,CAACkG,QAAQ,CAACE,OAAO,GAAG,MAAM,EAAEF,QAAQ,CAACG,SAAS,CAAC;QACnF,IAAIC,SAAS,GAAGJ,QAAQ,CAACK,KAAK;QAE9B,IAAI,CAACJ,SAAS,IAAIG,SAAS,IAAI,IAAI,EAAE;UACnC;QACF;QAEA,IAAIE,cAAc,GAAG3I,qBAAqB,CAAC4I,aAAa,CAACH,SAAS,EAAEH,SAAS,CAACO,IAAI,EAAE/G,OAAO,EAAEuG,QAAQ,CAACS,iBAAiB,EAAET,QAAQ,CAACU,aAAa,CAAC;QAChJ,IAAIC,iBAAiB,GAAGtI,mBAAmB,CAAC,SAAS,EAAE;UACrDuI,MAAM,EAAEN,cAAc;UACtBX,QAAQ,EAAE,CAAClJ,IAAI,CAAC6J,cAAc,CAAC;UAC/BO,UAAU,EAAE,IAAI;UAChBnB,MAAM,EAAE;QACV,CAAC,CAAC;QACFD,aAAa,CAACC,MAAM,CAACoB,IAAI,CAACH,iBAAiB,CAAC;QAC5CpK,IAAI,CAACyJ,QAAQ,CAACS,iBAAiB,EAAE,UAAUM,OAAO,EAAE;UAClD,IAAIC,MAAM,GAAGvH,OAAO,CAAC2E,gBAAgB,CAAC2C,OAAO,CAACxD,WAAW,CAAC;UAC1D,IAAIU,SAAS,GAAG8C,OAAO,CAACE,eAAe;UACvC,IAAIC,QAAQ,GAAGF,MAAM,CAACG,aAAa,CAAClD,SAAS,CAAC,CAAC,CAAC;;UAEhD,IAAIiD,QAAQ,CAACjD,SAAS,GAAG,CAAC,EAAE;YAC1B;UACF;UAEAiD,QAAQ,CAAChB,OAAO,GAAGF,QAAQ,CAACE,OAAO;UACnCgB,QAAQ,CAACf,SAAS,GAAGH,QAAQ,CAACG,SAAS;UACvCe,QAAQ,CAACE,QAAQ,GAAGpB,QAAQ,CAACoB,QAAQ;UACrCF,QAAQ,CAACG,MAAM,GAAGrB,QAAQ,CAACqB,MAAM;UACjCH,QAAQ,CAACd,SAAS,GAAG1I,UAAU,CAAC4J,eAAe,CAACrB,SAAS,CAACO,IAAI,EAAE;YAC9DH,KAAK,EAAED;UACT,CAAC,CAAC;UACFc,QAAQ,CAACZ,cAAc,GAAGA,cAAc,CAAC,CAAC;UAC1C;;UAEAY,QAAQ,CAACK,MAAM,GAAG1B,kBAAkB,CAAC2B,iBAAiB,CAAC,MAAM,EAAEtK,oBAAoB,CAACgK,QAAQ,CAACO,KAAK,CAAC,EAAE1H,UAAU,CAAC;UAChH,IAAI2H,mBAAmB,GAAGtJ,4BAA4B,CAAC4I,MAAM,CAACW,aAAa,CAAC1D,SAAS,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;UACnG,IAAI2D,IAAI,GAAGF,mBAAmB,CAACE,IAAI;UAEnC,IAAIA,IAAI,EAAE;YACR,IAAIC,cAAc,GAAGrD,iBAAiB,CAAC,CAACwC,MAAM,CAAC,EAAE1B,kBAAkB,CAAC,CAACrF,GAAG,CAAC,gBAAgB,CAAC;YAC1F0G,iBAAiB,CAACjB,MAAM,CAACoB,IAAI,CAACe,cAAc,GAAG/K,MAAM,CAAC;cACpD+K,cAAc,EAAEA;YAClB,CAAC,EAAED,IAAI,CAAC,GAAGA,IAAI,CAAC;UAClB;UAEA,IAAIF,mBAAmB,CAACI,IAAI,EAAE;YAC5BlC,mBAAmB,CAACkB,IAAI,CAACY,mBAAmB,CAACI,IAAI,CAAC;UACpD;UAEAtC,YAAY,CAACsB,IAAI,CAACI,QAAQ,CAAC;QAC7B,CAAC,CAAC;MACJ,CAAC,CAAC;IACJ,CAAC,CAAC,CAAC,CAAC;IACJ;;IAEAzB,aAAa,CAACC,MAAM,CAACqC,OAAO,CAAC,CAAC;IAC9BnC,mBAAmB,CAACmC,OAAO,CAAC,CAAC;IAC7B,IAAIC,YAAY,GAAG9G,CAAC,CAAC8B,QAAQ;IAC7B,IAAIiF,SAAS,GAAG1C,kBAAkB,CAACtF,GAAG,CAAC,OAAO,CAAC;IAC/C,IAAIiI,eAAe,GAAG5J,kBAAkB,CAACmH,aAAa,EAAEI,kBAAkB,EAAE9F,UAAU,EAAEkI,SAAS,EAAExI,OAAO,CAACQ,GAAG,CAAC,QAAQ,CAAC,EAAEsF,kBAAkB,CAACtF,GAAG,CAAC,WAAW,CAAC,CAAC;IAC9JiI,eAAe,IAAItC,mBAAmB,CAACuC,OAAO,CAACD,eAAe,CAAC;IAC/D,IAAIE,UAAU,GAAGrI,UAAU,KAAK,UAAU,GAAG,MAAM,GAAG,OAAO;IAC7D,IAAIsI,aAAa,GAAGzC,mBAAmB,CAAC0C,IAAI,CAACF,UAAU,CAAC;IAExD,IAAI,CAAClD,WAAW,CAACK,kBAAkB,EAAE,YAAY;MAC/C,IAAI,IAAI,CAACgD,8BAA8B,CAACxG,cAAc,EAAEyD,YAAY,CAAC,EAAE;QACrE,IAAI,CAACgD,eAAe,CAACjD,kBAAkB,EAAEyC,YAAY,EAAErE,KAAK,CAAC,CAAC,CAAC,EAAEA,KAAK,CAAC,CAAC,CAAC,EAAE,IAAI,CAACzD,eAAe,EAAEsF,YAAY,CAAC;MAChH,CAAC,MAAM;QACL,IAAI,CAACiD,mBAAmB,CAAClD,kBAAkB,EAAE8C,aAAa,EAAE7C,YAAY,EAAEkD,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,EAAE,EAAEhF,KAAK,CAAC,CAAC,CAAC,EAAEA,KAAK,CAAC,CAAC,CAAC,EAAEqE,YAAY,EAAE,IAAI,EAAEnC,kBAAkB,CAAC;MAC3J;IACF,CAAC,CAAC,CAAC,CAAC;IACJ;EAEF,CAAC;;EAED5G,WAAW,CAACM,SAAS,CAACyF,sBAAsB,GAAG,UAAU9D,CAAC,EAAE0H,UAAU,EAAEzH,cAAc,EAAE;IACtF,IAAI1B,OAAO,GAAG,IAAI,CAACe,QAAQ;IAC3B,IAAIqI,MAAM,GAAG3K,SAAS,CAAC0K,UAAU,CAAC,CAAC,CAAC;IACpC;IACA;;IAEA,IAAIrF,WAAW,GAAGsF,MAAM,CAACtF,WAAW;IACpC,IAAIY,WAAW,GAAG1E,OAAO,CAAC2E,gBAAgB,CAACb,WAAW,CAAC,CAAC,CAAC;;IAEzD,IAAIuF,SAAS,GAAGD,MAAM,CAACC,SAAS,IAAI3E,WAAW;IAC/C,IAAIF,SAAS,GAAG4E,MAAM,CAAC5E,SAAS;IAChC,IAAI8E,QAAQ,GAAGF,MAAM,CAACE,QAAQ;IAC9B,IAAI1E,IAAI,GAAGyE,SAAS,CAACxE,OAAO,CAACyE,QAAQ,CAAC;IACtC,IAAIhJ,UAAU,GAAG,IAAI,CAACC,WAAW;IACjC,IAAIiD,eAAe,GAAG/B,CAAC,CAAC+B,eAAe;IACvC,IAAIpD,YAAY,GAAG2E,iBAAiB,CAAC,CAACH,IAAI,CAACI,YAAY,CAACR,SAAS,CAAC,EAAE6E,SAAS,EAAE3E,WAAW,IAAI,CAACA,WAAW,CAACO,gBAAgB,IAAI,CAAC,CAAC,EAAEC,KAAK,CAAC,EAAE,IAAI,CAACpE,aAAa,EAAE0C,eAAe,GAAG;MAC/KD,QAAQ,EAAEC;IACZ,CAAC,GAAG,IAAI,CAAC;IACT,IAAI+F,cAAc,GAAGnJ,YAAY,CAACI,GAAG,CAAC,SAAS,CAAC;IAEhD,IAAI+I,cAAc,IAAI,IAAI,IAAIA,cAAc,KAAK,MAAM,EAAE;MACvD;IACF;IAEA,IAAIC,MAAM,GAAGH,SAAS,CAAC3B,aAAa,CAAClD,SAAS,EAAE8E,QAAQ,CAAC;IACzD,IAAIlD,kBAAkB,GAAG,IAAItH,yBAAyB,CAAC,CAAC,CAAC,CAAC;IAC1D;;IAEA0K,MAAM,CAAC1B,MAAM,GAAG1B,kBAAkB,CAAC2B,iBAAiB,CAAC,MAAM,EAAEtK,oBAAoB,CAAC+L,MAAM,CAACxB,KAAK,CAAC,EAAE1H,UAAU,CAAC;IAC5G,IAAI2H,mBAAmB,GAAGtJ,4BAA4B,CAAC0K,SAAS,CAACnB,aAAa,CAAC1D,SAAS,EAAE,KAAK,EAAE8E,QAAQ,CAAC,CAAC;IAC3G,IAAId,SAAS,GAAGpI,YAAY,CAACI,GAAG,CAAC,OAAO,CAAC;IACzC,IAAI4H,cAAc,GAAGhI,YAAY,CAACI,GAAG,CAAC,gBAAgB,CAAC;IACvD,IAAI2H,IAAI,GAAGF,mBAAmB,CAACE,IAAI;IACnC,IAAIsB,UAAU,GAAGtB,IAAI,GAAGtJ,kBAAkB,CAACuJ,cAAc,GAAG/K,MAAM,CAAC;MACjE+K,cAAc,EAAEA;IAClB,CAAC,EAAED,IAAI,CAAC,GAAGA,IAAI,EAAE/B,kBAAkB,EAAE9F,UAAU,EAAEkI,SAAS,EAAExI,OAAO,CAACQ,GAAG,CAAC,QAAQ,CAAC,EAAEJ,YAAY,CAACI,GAAG,CAAC,WAAW,CAAC,CAAC,GAAGyH,mBAAmB,CAACI,IAAI;IAC5I,IAAIqB,WAAW,GAAG,OAAO,GAAGL,SAAS,CAAC1F,IAAI,GAAG,GAAG,GAAGa,SAAS;IAE5D,IAAI,CAACiB,WAAW,CAACrF,YAAY,EAAE,YAAY;MACzC,IAAI,CAAC4I,mBAAmB,CAAC5I,YAAY,EAAEqJ,UAAU,EAAED,MAAM,EAAEE,WAAW,EAAEjI,CAAC,CAAC2B,OAAO,EAAE3B,CAAC,CAAC4B,OAAO,EAAE5B,CAAC,CAAC8B,QAAQ,EAAE9B,CAAC,CAAC6B,MAAM,EAAE8C,kBAAkB,CAAC;IACzI,CAAC,CAAC,CAAC,CAAC;IACJ;;IAGA1E,cAAc,CAAC;MACb7B,IAAI,EAAE,SAAS;MACf2H,eAAe,EAAEhD,SAAS;MAC1BA,SAAS,EAAEI,IAAI,CAAC+E,WAAW,CAACnF,SAAS,CAAC;MACtCV,WAAW,EAAEA,WAAW;MACxBrB,IAAI,EAAE,IAAI,CAACC;IACb,CAAC,CAAC;EACJ,CAAC;EAEDlD,WAAW,CAACM,SAAS,CAAC0F,yBAAyB,GAAG,UAAU/D,CAAC,EAAEuB,EAAE,EAAEtB,cAAc,EAAE;IACjF,IAAI0H,MAAM,GAAG3K,SAAS,CAACuE,EAAE,CAAC;IAC1B,IAAIU,aAAa,GAAG0F,MAAM,CAAC1F,aAAa;IACxC,IAAIkG,UAAU,GAAGlG,aAAa,CAACE,MAAM,IAAI,CAAC,CAAC;IAE3C,IAAI3G,QAAQ,CAAC2M,UAAU,CAAC,EAAE;MACxB,IAAIC,OAAO,GAAGD,UAAU;MACxBA,UAAU,GAAG;QACXC,OAAO,EAAEA,OAAO;QAChB;QACAC,SAAS,EAAED;MACb,CAAC;IACH;IAEA,IAAIE,mBAAmB,GAAG,CAACH,UAAU,CAAC;IAEtC,IAAII,IAAI,GAAG,IAAI,CAACjJ,QAAQ,CAACV,YAAY,CAAC+I,MAAM,CAACa,iBAAiB,EAAEb,MAAM,CAACc,cAAc,CAAC;IAEtF,IAAIF,IAAI,EAAE;MACRD,mBAAmB,CAAC1C,IAAI,CAAC2C,IAAI,CAAC;IAChC,CAAC,CAAC;IACF;IACA;;IAGAD,mBAAmB,CAAC1C,IAAI,CAAC;MACvByC,SAAS,EAAEF,UAAU,CAACC;IACxB,CAAC,CAAC;IACF,IAAIrG,eAAe,GAAG/B,CAAC,CAAC+B,eAAe;IACvC,IAAI2G,eAAe,GAAGpF,iBAAiB,CAACgF,mBAAmB,EAAE,IAAI,CAACjJ,aAAa,EAAE0C,eAAe,GAAG;MACjGD,QAAQ,EAAEC;IACZ,CAAC,GAAG,IAAI,CAAC;IACT,IAAI4G,WAAW,GAAGD,eAAe,CAAC3J,GAAG,CAAC,SAAS,CAAC;IAChD,IAAIkJ,WAAW,GAAGT,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC;;IAEtC,IAAI9C,kBAAkB,GAAG,IAAItH,yBAAyB,CAAC,CAAC,CAAC,CAAC;IAC1D;IACA;;IAEA,IAAI,CAAC2G,WAAW,CAAC0E,eAAe,EAAE,YAAY;MAC5C;MACA;MACA,IAAIE,eAAe,GAAGtN,KAAK,CAACoN,eAAe,CAAC3J,GAAG,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC,CAAC;MAEzE,IAAI,CAACwI,mBAAmB,CAACmB,eAAe,EAAEC,WAAW,EAAEC,eAAe,EAAEX,WAAW,EAAEjI,CAAC,CAAC2B,OAAO,EAAE3B,CAAC,CAAC4B,OAAO,EAAE5B,CAAC,CAAC8B,QAAQ,EAAEP,EAAE,EAAEoD,kBAAkB,CAAC;IAChJ,CAAC,CAAC,CAAC,CAAC;;IAGJ1E,cAAc,CAAC;MACb7B,IAAI,EAAE,SAAS;MACf4C,IAAI,EAAE,IAAI,CAACC;IACb,CAAC,CAAC;EACJ,CAAC;EAEDlD,WAAW,CAACM,SAAS,CAACkJ,mBAAmB,GAAG;EAAW;EACvD;EACA5I,YAAY,EAAEgK,WAAW,EAAEZ,MAAM,EAAEE,WAAW,EAAEtK,CAAC,EAAEC,CAAC,EAAEkJ,YAAY,EAAEvF,EAAE,EAAEoD,kBAAkB,EAAE;IAC1F;IACA,IAAI,CAACxD,OAAO,GAAG,EAAE;IAEjB,IAAI,CAACxC,YAAY,CAACI,GAAG,CAAC,aAAa,CAAC,IAAI,CAACJ,YAAY,CAACI,GAAG,CAAC,MAAM,CAAC,EAAE;MACjE;IACF;IAEA,IAAIS,cAAc,GAAG,IAAI,CAACR,eAAe;IACzCQ,cAAc,CAACE,YAAY,CAACf,YAAY,CAACI,GAAG,CAAC,WAAW,CAAC,CAAC;IAC1D,IAAIsJ,SAAS,GAAG1J,YAAY,CAACI,GAAG,CAAC,WAAW,CAAC;IAC7C+H,YAAY,GAAGA,YAAY,IAAInI,YAAY,CAACI,GAAG,CAAC,UAAU,CAAC;IAC3D,IAAI8J,IAAI,GAAGF,WAAW;IAEtB,IAAIG,SAAS,GAAG,IAAI,CAACC,gBAAgB,CAAC,CAACpL,CAAC,EAAEC,CAAC,CAAC,EAAEmK,MAAM,EAAEpJ,YAAY,CAACI,GAAG,CAAC,SAAS,CAAC,EAAEJ,YAAY,CAACI,GAAG,CAAC,aAAa,CAAC,CAAC;IAEnH,IAAIiK,cAAc,GAAGF,SAAS,CAACvC,KAAK;IAEpC,IAAI8B,SAAS,EAAE;MACb,IAAI7M,QAAQ,CAAC6M,SAAS,CAAC,EAAE;QACvB,IAAIY,MAAM,GAAGtK,YAAY,CAACJ,OAAO,CAACQ,GAAG,CAAC,QAAQ,CAAC;QAC/C,IAAImK,OAAO,GAAGxN,OAAO,CAACqM,MAAM,CAAC,GAAGA,MAAM,CAAC,CAAC,CAAC,GAAGA,MAAM;QAClD,IAAIoB,UAAU,GAAGD,OAAO,IAAIA,OAAO,CAAChD,QAAQ,IAAIgD,OAAO,CAAChD,QAAQ,CAAChG,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC;QACrF2I,IAAI,GAAGR,SAAS;QAEhB,IAAIc,UAAU,EAAE;UACdN,IAAI,GAAG9L,UAAU,CAACmM,OAAO,CAAChE,SAAS,EAAE2D,IAAI,EAAEI,MAAM,CAAC;QACpD;QAEAJ,IAAI,GAAG5M,SAAS,CAAC4M,IAAI,EAAEd,MAAM,EAAE,IAAI,CAAC;MACtC,CAAC,MAAM,IAAItM,UAAU,CAAC4M,SAAS,CAAC,EAAE;QAChC,IAAIe,QAAQ,GAAGhO,IAAI,CAAC,UAAUiO,QAAQ,EAAER,IAAI,EAAE;UAC5C,IAAIQ,QAAQ,KAAK,IAAI,CAAClI,OAAO,EAAE;YAC7B3B,cAAc,CAAC8J,UAAU,CAACT,IAAI,EAAElE,kBAAkB,EAAEhG,YAAY,EAAEqK,cAAc,EAAElC,YAAY,CAAC;YAE/F,IAAI,CAACQ,eAAe,CAAC3I,YAAY,EAAEmI,YAAY,EAAEnJ,CAAC,EAAEC,CAAC,EAAE4B,cAAc,EAAEuI,MAAM,EAAExG,EAAE,CAAC;UACpF;QACF,CAAC,EAAE,IAAI,CAAC;QACR,IAAI,CAACJ,OAAO,GAAG8G,WAAW;QAC1BY,IAAI,GAAGR,SAAS,CAACN,MAAM,EAAEE,WAAW,EAAEmB,QAAQ,CAAC;MACjD,CAAC,MAAM;QACLP,IAAI,GAAGR,SAAS;MAClB;IACF;IAEA7I,cAAc,CAAC8J,UAAU,CAACT,IAAI,EAAElE,kBAAkB,EAAEhG,YAAY,EAAEqK,cAAc,EAAElC,YAAY,CAAC;IAC/FtH,cAAc,CAAC+J,IAAI,CAAC5K,YAAY,EAAEqK,cAAc,CAAC;IAEjD,IAAI,CAAC1B,eAAe,CAAC3I,YAAY,EAAEmI,YAAY,EAAEnJ,CAAC,EAAEC,CAAC,EAAE4B,cAAc,EAAEuI,MAAM,EAAExG,EAAE,CAAC;EACpF,CAAC;EAEDxD,WAAW,CAACM,SAAS,CAAC0K,gBAAgB,GAAG,UAAUtG,KAAK,EAAE+G,iBAAiB,EAAEC,OAAO,EAAEC,WAAW,EAAE;IACjG,IAAID,OAAO,KAAK,MAAM,IAAI/N,OAAO,CAAC8N,iBAAiB,CAAC,EAAE;MACpD,OAAO;QACLjD,KAAK,EAAEmD,WAAW,KAAK,IAAI,CAAC5K,WAAW,KAAK,MAAM,GAAG,MAAM,GAAG,MAAM;MACtE,CAAC;IACH;IAEA,IAAI,CAACpD,OAAO,CAAC8N,iBAAiB,CAAC,EAAE;MAC/B,OAAO;QACLjD,KAAK,EAAEmD,WAAW,IAAIF,iBAAiB,CAACjD,KAAK,IAAIiD,iBAAiB,CAACE;MACrE,CAAC;IACH;EACF,CAAC;EAED3L,WAAW,CAACM,SAAS,CAACiJ,eAAe,GAAG,UAAU3I,YAAY,EAAEmI,YAAY,EAAEnJ,CAAC;EAAE;EACjFC,CAAC;EAAE;EACHwK,OAAO,EAAEL,MAAM,EAAExG,EAAE,EAAE;IACnB,IAAIoI,SAAS,GAAG,IAAI,CAACpK,IAAI,CAACqK,QAAQ,CAAC,CAAC;IAEpC,IAAIC,UAAU,GAAG,IAAI,CAACtK,IAAI,CAACuK,SAAS,CAAC,CAAC;IAEtChD,YAAY,GAAGA,YAAY,IAAInI,YAAY,CAACI,GAAG,CAAC,UAAU,CAAC;IAC3D,IAAIgL,WAAW,GAAG3B,OAAO,CAAC4B,OAAO,CAAC,CAAC;IACnC,IAAIC,KAAK,GAAGtL,YAAY,CAACI,GAAG,CAAC,OAAO,CAAC;IACrC,IAAImL,MAAM,GAAGvL,YAAY,CAACI,GAAG,CAAC,eAAe,CAAC;IAC9C,IAAIuC,IAAI,GAAGC,EAAE,IAAIA,EAAE,CAACC,eAAe,CAAC,CAAC,CAAClG,KAAK,CAAC,CAAC;IAC7CiG,EAAE,IAAID,IAAI,CAACG,cAAc,CAACF,EAAE,CAACG,SAAS,CAAC;IAEvC,IAAIjG,UAAU,CAACqL,YAAY,CAAC,EAAE;MAC5B;MACAA,YAAY,GAAGA,YAAY,CAAC,CAACnJ,CAAC,EAAEC,CAAC,CAAC,EAAEmK,MAAM,EAAEK,OAAO,CAAC7G,EAAE,EAAED,IAAI,EAAE;QAC5D6I,QAAQ,EAAE,CAACR,SAAS,EAAEE,UAAU,CAAC;QACjCE,WAAW,EAAEA,WAAW,CAACK,KAAK,CAAC;MACjC,CAAC,CAAC;IACJ;IAEA,IAAI1O,OAAO,CAACoL,YAAY,CAAC,EAAE;MACzBnJ,CAAC,GAAGzB,YAAY,CAAC4K,YAAY,CAAC,CAAC,CAAC,EAAE6C,SAAS,CAAC;MAC5C/L,CAAC,GAAG1B,YAAY,CAAC4K,YAAY,CAAC,CAAC,CAAC,EAAE+C,UAAU,CAAC;IAC/C,CAAC,MAAM,IAAIlO,QAAQ,CAACmL,YAAY,CAAC,EAAE;MACjC,IAAIuD,iBAAiB,GAAGvD,YAAY;MACpCuD,iBAAiB,CAACxM,KAAK,GAAGkM,WAAW,CAAC,CAAC,CAAC;MACxCM,iBAAiB,CAACvM,MAAM,GAAGiM,WAAW,CAAC,CAAC,CAAC;MACzC,IAAIO,UAAU,GAAGjO,aAAa,CAACgO,iBAAiB,EAAE;QAChDxM,KAAK,EAAE8L,SAAS;QAChB7L,MAAM,EAAE+L;MACV,CAAC,CAAC;MACFlM,CAAC,GAAG2M,UAAU,CAAC3M,CAAC;MAChBC,CAAC,GAAG0M,UAAU,CAAC1M,CAAC;MAChBqM,KAAK,GAAG,IAAI,CAAC,CAAC;MACd;;MAEAC,MAAM,GAAG,IAAI;IACf,CAAC,CAAC;IAAA,KACG,IAAI1O,QAAQ,CAACsL,YAAY,CAAC,IAAIvF,EAAE,EAAE;MACnC,IAAIgJ,GAAG,GAAGC,mBAAmB,CAAC1D,YAAY,EAAExF,IAAI,EAAEyI,WAAW,EAAEpL,YAAY,CAACI,GAAG,CAAC,aAAa,CAAC,CAAC;MAC/FpB,CAAC,GAAG4M,GAAG,CAAC,CAAC,CAAC;MACV3M,CAAC,GAAG2M,GAAG,CAAC,CAAC,CAAC;IACZ,CAAC,MAAM;MACL,IAAIA,GAAG,GAAGE,oBAAoB,CAAC9M,CAAC,EAAEC,CAAC,EAAEwK,OAAO,EAAEuB,SAAS,EAAEE,UAAU,EAAEI,KAAK,GAAG,IAAI,GAAG,EAAE,EAAEC,MAAM,GAAG,IAAI,GAAG,EAAE,CAAC;MAC3GvM,CAAC,GAAG4M,GAAG,CAAC,CAAC,CAAC;MACV3M,CAAC,GAAG2M,GAAG,CAAC,CAAC,CAAC;IACZ;IAEFN,KAAK,KAAKtM,CAAC,IAAI+M,aAAa,CAACT,KAAK,CAAC,GAAGF,WAAW,CAAC,CAAC,CAAC,GAAG,CAAC,GAAGE,KAAK,KAAK,OAAO,GAAGF,WAAW,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;IAClGG,MAAM,KAAKtM,CAAC,IAAI8M,aAAa,CAACR,MAAM,CAAC,GAAGH,WAAW,CAAC,CAAC,CAAC,GAAG,CAAC,GAAGG,MAAM,KAAK,QAAQ,GAAGH,WAAW,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;IAEtG,IAAI9M,oBAAoB,CAAC0B,YAAY,CAAC,EAAE;MACtC,IAAI4L,GAAG,GAAGI,sBAAsB,CAAChN,CAAC,EAAEC,CAAC,EAAEwK,OAAO,EAAEuB,SAAS,EAAEE,UAAU,CAAC;MACtElM,CAAC,GAAG4M,GAAG,CAAC,CAAC,CAAC;MACV3M,CAAC,GAAG2M,GAAG,CAAC,CAAC,CAAC;IACZ;IAEAnC,OAAO,CAACwC,MAAM,CAACjN,CAAC,EAAEC,CAAC,CAAC;EACtB,CAAC,CAAC,CAAC;EACH;;EAGAG,WAAW,CAACM,SAAS,CAACgJ,8BAA8B,GAAG,UAAUxG,cAAc,EAAEyD,YAAY,EAAE;IAC7F,IAAIuG,YAAY,GAAG,IAAI,CAAC/J,mBAAmB;IAC3C,IAAIgK,gBAAgB,GAAG,IAAI,CAACC,aAAa;IACzC,IAAIC,iBAAiB,GAAG,CAAC,CAACH,YAAY,IAAIA,YAAY,CAACnH,MAAM,KAAK7C,cAAc,CAAC6C,MAAM;IACvFsH,iBAAiB,IAAI3P,IAAI,CAACwP,YAAY,EAAE,UAAUI,gBAAgB,EAAEC,aAAa,EAAE;MACjF,IAAIC,cAAc,GAAGF,gBAAgB,CAACpG,UAAU,IAAI,EAAE;MACtD,IAAIuG,gBAAgB,GAAGvK,cAAc,CAACqK,aAAa,CAAC,IAAI,CAAC,CAAC;MAC1D,IAAIG,cAAc,GAAGD,gBAAgB,CAACvG,UAAU,IAAI,EAAE;MACtDmG,iBAAiB,GAAGA,iBAAiB,IAAIG,cAAc,CAACzH,MAAM,KAAK2H,cAAc,CAAC3H,MAAM;MACxFsH,iBAAiB,IAAI3P,IAAI,CAAC8P,cAAc,EAAE,UAAUG,QAAQ,EAAEC,SAAS,EAAE;QACvE,IAAIC,QAAQ,GAAGH,cAAc,CAACE,SAAS,CAAC,IAAI,CAAC,CAAC;QAC9C,IAAIE,WAAW,GAAGH,QAAQ,CAAC/F,iBAAiB,IAAI,EAAE;QAClD,IAAImG,UAAU,GAAGF,QAAQ,CAACjG,iBAAiB,IAAI,EAAE;QACjDyF,iBAAiB,GAAGA,iBAAiB,IAAIM,QAAQ,CAACnG,KAAK,KAAKqG,QAAQ,CAACrG,KAAK,IAAImG,QAAQ,CAACpF,QAAQ,KAAKsF,QAAQ,CAACtF,QAAQ,IAAIoF,QAAQ,CAACnF,MAAM,KAAKqF,QAAQ,CAACrF,MAAM,IAAIsF,WAAW,CAAC/H,MAAM,KAAKgI,UAAU,CAAChI,MAAM;QACxMsH,iBAAiB,IAAI3P,IAAI,CAACoQ,WAAW,EAAE,UAAUE,WAAW,EAAEC,CAAC,EAAE;UAC/D,IAAIC,UAAU,GAAGH,UAAU,CAACE,CAAC,CAAC;UAC9BZ,iBAAiB,GAAGA,iBAAiB,IAAIW,WAAW,CAACtJ,WAAW,KAAKwJ,UAAU,CAACxJ,WAAW,IAAIsJ,WAAW,CAAC5I,SAAS,KAAK8I,UAAU,CAAC9I,SAAS;QAC/I,CAAC,CAAC,CAAC,CAAC;;QAEJ+H,gBAAgB,IAAIzP,IAAI,CAACiQ,QAAQ,CAAC/F,iBAAiB,EAAE,UAAUM,OAAO,EAAE;UACtE,IAAIiG,SAAS,GAAGjG,OAAO,CAACxD,WAAW;UACnC,IAAI2D,QAAQ,GAAG1B,YAAY,CAACwH,SAAS,CAAC;UACtC,IAAIC,YAAY,GAAGjB,gBAAgB,CAACgB,SAAS,CAAC;UAE9C,IAAI9F,QAAQ,IAAI+F,YAAY,IAAIA,YAAY,CAAC5I,IAAI,KAAK6C,QAAQ,CAAC7C,IAAI,EAAE;YACnE6H,iBAAiB,GAAG,KAAK;UAC3B;QACF,CAAC,CAAC;MACJ,CAAC,CAAC;IACJ,CAAC,CAAC;IACF,IAAI,CAAClK,mBAAmB,GAAGD,cAAc;IACzC,IAAI,CAACkK,aAAa,GAAGzG,YAAY;IACjC,OAAO,CAAC,CAAC0G,iBAAiB;EAC5B,CAAC;EAEDjN,WAAW,CAACM,SAAS,CAAC+B,KAAK,GAAG,UAAUH,cAAc,EAAE;IACtD;IACA;IACA;IACA;IACA,IAAI,CAACa,mBAAmB,GAAG,IAAI;IAC/Bb,cAAc,CAAC;MACb7B,IAAI,EAAE,SAAS;MACf4C,IAAI,EAAE,IAAI,CAACC;IACb,CAAC,CAAC;EACJ,CAAC;EAEDlD,WAAW,CAACM,SAAS,CAAC2N,OAAO,GAAG,UAAUzN,OAAO,EAAEC,GAAG,EAAE;IACtD,IAAI3C,GAAG,CAAC4C,IAAI,IAAI,CAACD,GAAG,CAACE,MAAM,CAAC,CAAC,EAAE;MAC7B;IACF;IAEAnB,KAAK,CAAC,IAAI,EAAE,iBAAiB,CAAC;IAE9B,IAAI,CAACyB,eAAe,CAACgN,OAAO,CAAC,CAAC;IAE9BzP,cAAc,CAAC0P,UAAU,CAAC,aAAa,EAAEzN,GAAG,CAAC;EAC/C,CAAC;EAEDT,WAAW,CAACK,IAAI,GAAG,SAAS;EAC5B,OAAOL,WAAW;AACpB,CAAC,CAAClB,aAAa,CAAC;AAChB;AACA;AACA;;AAGA,SAASyG,iBAAiBA,CAAC4I,YAAY,EAAE9H,kBAAkB,EAAE+H,oBAAoB,EAAE;EACjF;EACA,IAAI5N,OAAO,GAAG6F,kBAAkB,CAAC7F,OAAO;EACxC,IAAI6N,WAAW;EAEf,IAAID,oBAAoB,EAAE;IACxBC,WAAW,GAAG,IAAI9P,KAAK,CAAC6P,oBAAoB,EAAE5N,OAAO,EAAEA,OAAO,CAAC;IAC/D6N,WAAW,GAAG,IAAI9P,KAAK,CAAC8H,kBAAkB,CAACjC,MAAM,EAAEiK,WAAW,EAAE7N,OAAO,CAAC;EAC1E,CAAC,MAAM;IACL6N,WAAW,GAAGhI,kBAAkB;EAClC;EAEA,KAAK,IAAIiI,CAAC,GAAGH,YAAY,CAACxI,MAAM,GAAG,CAAC,EAAE2I,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;IACjD,IAAIlE,UAAU,GAAG+D,YAAY,CAACG,CAAC,CAAC;IAEhC,IAAIlE,UAAU,EAAE;MACd,IAAIA,UAAU,YAAY7L,KAAK,EAAE;QAC/B6L,UAAU,GAAGA,UAAU,CAACpJ,GAAG,CAAC,SAAS,EAAE,IAAI,CAAC;MAC9C,CAAC,CAAC;MACF;MACA;MACA;MACA;;MAGA,IAAIvD,QAAQ,CAAC2M,UAAU,CAAC,EAAE;QACxBA,UAAU,GAAG;UACXE,SAAS,EAAEF;QACb,CAAC;MACH;MAEA,IAAIA,UAAU,EAAE;QACdiE,WAAW,GAAG,IAAI9P,KAAK,CAAC6L,UAAU,EAAEiE,WAAW,EAAE7N,OAAO,CAAC;MAC3D;IACF;EACF;EAEA,OAAO6N,WAAW;AACpB;AAEA,SAASlL,kBAAkBA,CAACH,OAAO,EAAEvC,GAAG,EAAE;EACxC,OAAOuC,OAAO,CAACd,cAAc,IAAI7E,IAAI,CAACoD,GAAG,CAACyB,cAAc,EAAEzB,GAAG,CAAC;AAChE;AAEA,SAASiM,oBAAoBA,CAAC9M,CAAC,EAAEC,CAAC,EAAEwK,OAAO,EAAEuB,SAAS,EAAEE,UAAU,EAAEyC,IAAI,EAAEC,IAAI,EAAE;EAC9E,IAAIC,IAAI,GAAGpE,OAAO,CAAC4B,OAAO,CAAC,CAAC;EAC5B,IAAInM,KAAK,GAAG2O,IAAI,CAAC,CAAC,CAAC;EACnB,IAAI1O,MAAM,GAAG0O,IAAI,CAAC,CAAC,CAAC;EAEpB,IAAIF,IAAI,IAAI,IAAI,EAAE;IAChB;IACA;IACA;IACA;IACA,IAAI3O,CAAC,GAAGE,KAAK,GAAGyO,IAAI,GAAG,CAAC,GAAG3C,SAAS,EAAE;MACpChM,CAAC,IAAIE,KAAK,GAAGyO,IAAI;IACnB,CAAC,MAAM;MACL3O,CAAC,IAAI2O,IAAI;IACX;EACF;EAEA,IAAIC,IAAI,IAAI,IAAI,EAAE;IAChB,IAAI3O,CAAC,GAAGE,MAAM,GAAGyO,IAAI,GAAG1C,UAAU,EAAE;MAClCjM,CAAC,IAAIE,MAAM,GAAGyO,IAAI;IACpB,CAAC,MAAM;MACL3O,CAAC,IAAI2O,IAAI;IACX;EACF;EAEA,OAAO,CAAC5O,CAAC,EAAEC,CAAC,CAAC;AACf;AAEA,SAAS+M,sBAAsBA,CAAChN,CAAC,EAAEC,CAAC,EAAEwK,OAAO,EAAEuB,SAAS,EAAEE,UAAU,EAAE;EACpE,IAAI2C,IAAI,GAAGpE,OAAO,CAAC4B,OAAO,CAAC,CAAC;EAC5B,IAAInM,KAAK,GAAG2O,IAAI,CAAC,CAAC,CAAC;EACnB,IAAI1O,MAAM,GAAG0O,IAAI,CAAC,CAAC,CAAC;EACpB7O,CAAC,GAAG6J,IAAI,CAACiF,GAAG,CAAC9O,CAAC,GAAGE,KAAK,EAAE8L,SAAS,CAAC,GAAG9L,KAAK;EAC1CD,CAAC,GAAG4J,IAAI,CAACiF,GAAG,CAAC7O,CAAC,GAAGE,MAAM,EAAE+L,UAAU,CAAC,GAAG/L,MAAM;EAC7CH,CAAC,GAAG6J,IAAI,CAACkF,GAAG,CAAC/O,CAAC,EAAE,CAAC,CAAC;EAClBC,CAAC,GAAG4J,IAAI,CAACkF,GAAG,CAAC9O,CAAC,EAAE,CAAC,CAAC;EAClB,OAAO,CAACD,CAAC,EAAEC,CAAC,CAAC;AACf;AAEA,SAAS4M,mBAAmBA,CAAC1I,QAAQ,EAAER,IAAI,EAAEyI,WAAW,EAAE4C,WAAW,EAAE;EACrE,IAAIC,QAAQ,GAAG7C,WAAW,CAAC,CAAC,CAAC;EAC7B,IAAI8C,SAAS,GAAG9C,WAAW,CAAC,CAAC,CAAC;EAC9B,IAAI+C,MAAM,GAAGtF,IAAI,CAACuF,IAAI,CAACvF,IAAI,CAACwF,KAAK,GAAGL,WAAW,CAAC,GAAG,CAAC;EACpD,IAAIhP,CAAC,GAAG,CAAC;EACT,IAAIC,CAAC,GAAG,CAAC;EACT,IAAIqP,SAAS,GAAG3L,IAAI,CAACzD,KAAK;EAC1B,IAAIqP,UAAU,GAAG5L,IAAI,CAACxD,MAAM;EAE5B,QAAQgE,QAAQ;IACd,KAAK,QAAQ;MACXnE,CAAC,GAAG2D,IAAI,CAAC3D,CAAC,GAAGsP,SAAS,GAAG,CAAC,GAAGL,QAAQ,GAAG,CAAC;MACzChP,CAAC,GAAG0D,IAAI,CAAC1D,CAAC,GAAGsP,UAAU,GAAG,CAAC,GAAGL,SAAS,GAAG,CAAC;MAC3C;IAEF,KAAK,KAAK;MACRlP,CAAC,GAAG2D,IAAI,CAAC3D,CAAC,GAAGsP,SAAS,GAAG,CAAC,GAAGL,QAAQ,GAAG,CAAC;MACzChP,CAAC,GAAG0D,IAAI,CAAC1D,CAAC,GAAGiP,SAAS,GAAGC,MAAM;MAC/B;IAEF,KAAK,QAAQ;MACXnP,CAAC,GAAG2D,IAAI,CAAC3D,CAAC,GAAGsP,SAAS,GAAG,CAAC,GAAGL,QAAQ,GAAG,CAAC;MACzChP,CAAC,GAAG0D,IAAI,CAAC1D,CAAC,GAAGsP,UAAU,GAAGJ,MAAM;MAChC;IAEF,KAAK,MAAM;MACTnP,CAAC,GAAG2D,IAAI,CAAC3D,CAAC,GAAGiP,QAAQ,GAAGE,MAAM;MAC9BlP,CAAC,GAAG0D,IAAI,CAAC1D,CAAC,GAAGsP,UAAU,GAAG,CAAC,GAAGL,SAAS,GAAG,CAAC;MAC3C;IAEF,KAAK,OAAO;MACVlP,CAAC,GAAG2D,IAAI,CAAC3D,CAAC,GAAGsP,SAAS,GAAGH,MAAM;MAC/BlP,CAAC,GAAG0D,IAAI,CAAC1D,CAAC,GAAGsP,UAAU,GAAG,CAAC,GAAGL,SAAS,GAAG,CAAC;EAC/C;EAEA,OAAO,CAAClP,CAAC,EAAEC,CAAC,CAAC;AACf;AAEA,SAAS8M,aAAaA,CAACT,KAAK,EAAE;EAC5B,OAAOA,KAAK,KAAK,QAAQ,IAAIA,KAAK,KAAK,QAAQ;AACjD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA,SAAS5I,sBAAsBA,CAACN,OAAO,EAAExC,OAAO,EAAEC,GAAG,EAAE;EACrD,IAAI2O,cAAc,GAAGxQ,cAAc,CAACoE,OAAO,CAAC,CAACoM,cAAc;EAC3D,IAAI3E,iBAAiB,GAAG2E,cAAc,CAACC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;EAEhD,IAAI,CAAC5E,iBAAiB,IAAIA,iBAAiB,KAAK,QAAQ,EAAE;IACxD;EACF;EAEA,IAAI6E,WAAW,GAAGzQ,wBAAwB,CAAC2B,OAAO,EAAEiK,iBAAiB,EAAE2E,cAAc,CAACpO,GAAG,CAACyJ,iBAAiB,CAAC,EAAE;IAC5G8E,UAAU,EAAE,KAAK;IACjBC,SAAS,EAAE,KAAK;IAChBC,UAAU,EAAE;EACd,CAAC,CAAC;EACF,IAAI/J,KAAK,GAAG4J,WAAW,CAACI,MAAM,CAAC,CAAC,CAAC;EAEjC,IAAI,CAAChK,KAAK,EAAE;IACV;EACF;EAEA,IAAIiK,IAAI,GAAGlP,GAAG,CAACmP,uBAAuB,CAAClK,KAAK,CAAC;EAC7C,IAAIlC,EAAE;EACNmM,IAAI,CAACvO,KAAK,CAACyO,QAAQ,CAAC,UAAUC,KAAK,EAAE;IACnC,IAAI5L,aAAa,GAAGjF,SAAS,CAAC6Q,KAAK,CAAC,CAAC5L,aAAa;IAElD,IAAIA,aAAa,IAAIA,aAAa,CAACC,IAAI,KAAKnB,OAAO,CAACmB,IAAI,EAAE;MACxDX,EAAE,GAAGsM,KAAK;MACV,OAAO,IAAI,CAAC,CAAC;IACf;EACF,CAAC,CAAC;;EAEF,IAAItM,EAAE,EAAE;IACN,OAAO;MACLiH,iBAAiB,EAAEA,iBAAiB;MACpCC,cAAc,EAAEhF,KAAK,CAACgF,cAAc;MACpClH,EAAE,EAAEA;IACN,CAAC;EACH;AACF;AAEA,eAAexD,WAAW"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}