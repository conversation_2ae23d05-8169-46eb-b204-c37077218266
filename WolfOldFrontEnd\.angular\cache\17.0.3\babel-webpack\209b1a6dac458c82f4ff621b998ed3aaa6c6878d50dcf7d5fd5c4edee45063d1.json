{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\nimport Path from 'zrender/lib/graphic/Path.js';\nimport Group from 'zrender/lib/graphic/Group.js';\nimport { extend, each, map } from 'zrender/lib/core/util.js';\nimport { Rect, Sector, updateProps, initProps, removeElementWithFadeOut, traverseElements } from '../../util/graphic.js';\nimport { getECData } from '../../util/innerStore.js';\nimport { setStatesStylesFromModel, toggleHoverEmphasis } from '../../util/states.js';\nimport { setLabelStyle, getLabelStatesModels, setLabelValueAnimation, labelInner } from '../../label/labelStyle.js';\nimport { throttle } from '../../util/throttle.js';\nimport { createClipPath } from '../helper/createClipPathFromCoordSys.js';\nimport Sausage from '../../util/shape/sausage.js';\nimport ChartView from '../../view/Chart.js';\nimport { isCoordinateSystemType } from '../../coord/CoordinateSystem.js';\nimport { getDefaultLabel, getDefaultInterpolatedLabel } from '../helper/labelHelper.js';\nimport { warn } from '../../util/log.js';\nimport { createSectorCalculateTextPosition, setSectorTextRotation } from '../../label/sectorLabel.js';\nimport { saveOldStyle } from '../../animation/basicTransition.js';\nimport { getSectorCornerRadius } from '../helper/sectorHelper.js';\nvar mathMax = Math.max;\nvar mathMin = Math.min;\nfunction getClipArea(coord, data) {\n  var coordSysClipArea = coord.getArea && coord.getArea();\n  if (isCoordinateSystemType(coord, 'cartesian2d')) {\n    var baseAxis = coord.getBaseAxis(); // When boundaryGap is false or using time axis. bar may exceed the grid.\n    // We should not clip this part.\n    // See test/bar2.html\n\n    if (baseAxis.type !== 'category' || !baseAxis.onBand) {\n      var expandWidth = data.getLayout('bandWidth');\n      if (baseAxis.isHorizontal()) {\n        coordSysClipArea.x -= expandWidth;\n        coordSysClipArea.width += expandWidth * 2;\n      } else {\n        coordSysClipArea.y -= expandWidth;\n        coordSysClipArea.height += expandWidth * 2;\n      }\n    }\n  }\n  return coordSysClipArea;\n}\nvar BarView = /** @class */\nfunction (_super) {\n  __extends(BarView, _super);\n  function BarView() {\n    var _this = _super.call(this) || this;\n    _this.type = BarView.type;\n    _this._isFirstFrame = true;\n    return _this;\n  }\n  BarView.prototype.render = function (seriesModel, ecModel, api, payload) {\n    this._model = seriesModel;\n    this._removeOnRenderedListener(api);\n    this._updateDrawMode(seriesModel);\n    var coordinateSystemType = seriesModel.get('coordinateSystem');\n    if (coordinateSystemType === 'cartesian2d' || coordinateSystemType === 'polar') {\n      // Clear previously rendered progressive elements.\n      this._progressiveEls = null;\n      this._isLargeDraw ? this._renderLarge(seriesModel, ecModel, api) : this._renderNormal(seriesModel, ecModel, api, payload);\n    } else if (process.env.NODE_ENV !== 'production') {\n      warn('Only cartesian2d and polar supported for bar.');\n    }\n  };\n  BarView.prototype.incrementalPrepareRender = function (seriesModel) {\n    this._clear();\n    this._updateDrawMode(seriesModel); // incremental also need to clip, otherwise might be overlow.\n    // But must not set clip in each frame, otherwise all of the children will be marked redraw.\n\n    this._updateLargeClip(seriesModel);\n  };\n  BarView.prototype.incrementalRender = function (params, seriesModel) {\n    // Reset\n    this._progressiveEls = []; // Do not support progressive in normal mode.\n\n    this._incrementalRenderLarge(params, seriesModel);\n  };\n  BarView.prototype.eachRendered = function (cb) {\n    traverseElements(this._progressiveEls || this.group, cb);\n  };\n  BarView.prototype._updateDrawMode = function (seriesModel) {\n    var isLargeDraw = seriesModel.pipelineContext.large;\n    if (this._isLargeDraw == null || isLargeDraw !== this._isLargeDraw) {\n      this._isLargeDraw = isLargeDraw;\n      this._clear();\n    }\n  };\n  BarView.prototype._renderNormal = function (seriesModel, ecModel, api, payload) {\n    var group = this.group;\n    var data = seriesModel.getData();\n    var oldData = this._data;\n    var coord = seriesModel.coordinateSystem;\n    var baseAxis = coord.getBaseAxis();\n    var isHorizontalOrRadial;\n    if (coord.type === 'cartesian2d') {\n      isHorizontalOrRadial = baseAxis.isHorizontal();\n    } else if (coord.type === 'polar') {\n      isHorizontalOrRadial = baseAxis.dim === 'angle';\n    }\n    var animationModel = seriesModel.isAnimationEnabled() ? seriesModel : null;\n    var realtimeSortCfg = shouldRealtimeSort(seriesModel, coord);\n    if (realtimeSortCfg) {\n      this._enableRealtimeSort(realtimeSortCfg, data, api);\n    }\n    var needsClip = seriesModel.get('clip', true) || realtimeSortCfg;\n    var coordSysClipArea = getClipArea(coord, data); // If there is clipPath created in large mode. Remove it.\n\n    group.removeClipPath(); // We don't use clipPath in normal mode because we needs a perfect animation\n    // And don't want the label are clipped.\n\n    var roundCap = seriesModel.get('roundCap', true);\n    var drawBackground = seriesModel.get('showBackground', true);\n    var backgroundModel = seriesModel.getModel('backgroundStyle');\n    var barBorderRadius = backgroundModel.get('borderRadius') || 0;\n    var bgEls = [];\n    var oldBgEls = this._backgroundEls;\n    var isInitSort = payload && payload.isInitSort;\n    var isChangeOrder = payload && payload.type === 'changeAxisOrder';\n    function createBackground(dataIndex) {\n      var bgLayout = getLayout[coord.type](data, dataIndex);\n      var bgEl = createBackgroundEl(coord, isHorizontalOrRadial, bgLayout);\n      bgEl.useStyle(backgroundModel.getItemStyle()); // Only cartesian2d support borderRadius.\n\n      if (coord.type === 'cartesian2d') {\n        bgEl.setShape('r', barBorderRadius);\n      } else {\n        bgEl.setShape('cornerRadius', barBorderRadius);\n      }\n      bgEls[dataIndex] = bgEl;\n      return bgEl;\n    }\n    ;\n    data.diff(oldData).add(function (dataIndex) {\n      var itemModel = data.getItemModel(dataIndex);\n      var layout = getLayout[coord.type](data, dataIndex, itemModel);\n      if (drawBackground) {\n        createBackground(dataIndex);\n      } // If dataZoom in filteMode: 'empty', the baseValue can be set as NaN in \"axisProxy\".\n\n      if (!data.hasValue(dataIndex) || !isValidLayout[coord.type](layout)) {\n        return;\n      }\n      var isClipped = false;\n      if (needsClip) {\n        // Clip will modify the layout params.\n        // And return a boolean to determine if the shape are fully clipped.\n        isClipped = clip[coord.type](coordSysClipArea, layout);\n      }\n      var el = elementCreator[coord.type](seriesModel, data, dataIndex, layout, isHorizontalOrRadial, animationModel, baseAxis.model, false, roundCap);\n      if (realtimeSortCfg) {\n        /**\r\n         * Force label animation because even if the element is\r\n         * ignored because it's clipped, it may not be clipped after\r\n         * changing order. Then, if not using forceLabelAnimation,\r\n         * the label animation was never started, in which case,\r\n         * the label will be the final value and doesn't have label\r\n         * animation.\r\n         */\n        el.forceLabelAnimation = true;\n      }\n      updateStyle(el, data, dataIndex, itemModel, layout, seriesModel, isHorizontalOrRadial, coord.type === 'polar');\n      if (isInitSort) {\n        el.attr({\n          shape: layout\n        });\n      } else if (realtimeSortCfg) {\n        updateRealtimeAnimation(realtimeSortCfg, animationModel, el, layout, dataIndex, isHorizontalOrRadial, false, false);\n      } else {\n        initProps(el, {\n          shape: layout\n        }, seriesModel, dataIndex);\n      }\n      data.setItemGraphicEl(dataIndex, el);\n      group.add(el);\n      el.ignore = isClipped;\n    }).update(function (newIndex, oldIndex) {\n      var itemModel = data.getItemModel(newIndex);\n      var layout = getLayout[coord.type](data, newIndex, itemModel);\n      if (drawBackground) {\n        var bgEl = void 0;\n        if (oldBgEls.length === 0) {\n          bgEl = createBackground(oldIndex);\n        } else {\n          bgEl = oldBgEls[oldIndex];\n          bgEl.useStyle(backgroundModel.getItemStyle()); // Only cartesian2d support borderRadius.\n\n          if (coord.type === 'cartesian2d') {\n            bgEl.setShape('r', barBorderRadius);\n          } else {\n            bgEl.setShape('cornerRadius', barBorderRadius);\n          }\n          bgEls[newIndex] = bgEl;\n        }\n        var bgLayout = getLayout[coord.type](data, newIndex);\n        var shape = createBackgroundShape(isHorizontalOrRadial, bgLayout, coord);\n        updateProps(bgEl, {\n          shape: shape\n        }, animationModel, newIndex);\n      }\n      var el = oldData.getItemGraphicEl(oldIndex);\n      if (!data.hasValue(newIndex) || !isValidLayout[coord.type](layout)) {\n        group.remove(el);\n        return;\n      }\n      var isClipped = false;\n      if (needsClip) {\n        isClipped = clip[coord.type](coordSysClipArea, layout);\n        if (isClipped) {\n          group.remove(el);\n        }\n      }\n      if (!el) {\n        el = elementCreator[coord.type](seriesModel, data, newIndex, layout, isHorizontalOrRadial, animationModel, baseAxis.model, !!el, roundCap);\n      } else {\n        saveOldStyle(el);\n      }\n      if (realtimeSortCfg) {\n        el.forceLabelAnimation = true;\n      }\n      if (isChangeOrder) {\n        var textEl = el.getTextContent();\n        if (textEl) {\n          var labelInnerStore = labelInner(textEl);\n          if (labelInnerStore.prevValue != null) {\n            /**\r\n             * Set preValue to be value so that no new label\r\n             * should be started, otherwise, it will take a full\r\n             * `animationDurationUpdate` time to finish the\r\n             * animation, which is not expected.\r\n             */\n            labelInnerStore.prevValue = labelInnerStore.value;\n          }\n        }\n      } // Not change anything if only order changed.\n      // Especially not change label.\n      else {\n        updateStyle(el, data, newIndex, itemModel, layout, seriesModel, isHorizontalOrRadial, coord.type === 'polar');\n      }\n      if (isInitSort) {\n        el.attr({\n          shape: layout\n        });\n      } else if (realtimeSortCfg) {\n        updateRealtimeAnimation(realtimeSortCfg, animationModel, el, layout, newIndex, isHorizontalOrRadial, true, isChangeOrder);\n      } else {\n        updateProps(el, {\n          shape: layout\n        }, seriesModel, newIndex, null);\n      }\n      data.setItemGraphicEl(newIndex, el);\n      el.ignore = isClipped;\n      group.add(el);\n    }).remove(function (dataIndex) {\n      var el = oldData.getItemGraphicEl(dataIndex);\n      el && removeElementWithFadeOut(el, seriesModel, dataIndex);\n    }).execute();\n    var bgGroup = this._backgroundGroup || (this._backgroundGroup = new Group());\n    bgGroup.removeAll();\n    for (var i = 0; i < bgEls.length; ++i) {\n      bgGroup.add(bgEls[i]);\n    }\n    group.add(bgGroup);\n    this._backgroundEls = bgEls;\n    this._data = data;\n  };\n  BarView.prototype._renderLarge = function (seriesModel, ecModel, api) {\n    this._clear();\n    createLarge(seriesModel, this.group);\n    this._updateLargeClip(seriesModel);\n  };\n  BarView.prototype._incrementalRenderLarge = function (params, seriesModel) {\n    this._removeBackground();\n    createLarge(seriesModel, this.group, this._progressiveEls, true);\n  };\n  BarView.prototype._updateLargeClip = function (seriesModel) {\n    // Use clipPath in large mode.\n    var clipPath = seriesModel.get('clip', true) && createClipPath(seriesModel.coordinateSystem, false, seriesModel);\n    var group = this.group;\n    if (clipPath) {\n      group.setClipPath(clipPath);\n    } else {\n      group.removeClipPath();\n    }\n  };\n  BarView.prototype._enableRealtimeSort = function (realtimeSortCfg, data, api) {\n    var _this = this; // If no data in the first frame, wait for data to initSort\n\n    if (!data.count()) {\n      return;\n    }\n    var baseAxis = realtimeSortCfg.baseAxis;\n    if (this._isFirstFrame) {\n      this._dispatchInitSort(data, realtimeSortCfg, api);\n      this._isFirstFrame = false;\n    } else {\n      var orderMapping_1 = function (idx) {\n        var el = data.getItemGraphicEl(idx);\n        var shape = el && el.shape;\n        return shape &&\n        // The result should be consistent with the initial sort by data value.\n        // Do not support the case that both positive and negative exist.\n        Math.abs(baseAxis.isHorizontal() ? shape.height : shape.width) // If data is NaN, shape.xxx may be NaN, so use || 0 here in case\n        || 0;\n      };\n      this._onRendered = function () {\n        _this._updateSortWithinSameData(data, orderMapping_1, baseAxis, api);\n      };\n      api.getZr().on('rendered', this._onRendered);\n    }\n  };\n  BarView.prototype._dataSort = function (data, baseAxis, orderMapping) {\n    var info = [];\n    data.each(data.mapDimension(baseAxis.dim), function (ordinalNumber, dataIdx) {\n      var mappedValue = orderMapping(dataIdx);\n      mappedValue = mappedValue == null ? NaN : mappedValue;\n      info.push({\n        dataIndex: dataIdx,\n        mappedValue: mappedValue,\n        ordinalNumber: ordinalNumber\n      });\n    });\n    info.sort(function (a, b) {\n      // If NaN, it will be treated as min val.\n      return b.mappedValue - a.mappedValue;\n    });\n    return {\n      ordinalNumbers: map(info, function (item) {\n        return item.ordinalNumber;\n      })\n    };\n  };\n  BarView.prototype._isOrderChangedWithinSameData = function (data, orderMapping, baseAxis) {\n    var scale = baseAxis.scale;\n    var ordinalDataDim = data.mapDimension(baseAxis.dim);\n    var lastValue = Number.MAX_VALUE;\n    for (var tickNum = 0, len = scale.getOrdinalMeta().categories.length; tickNum < len; ++tickNum) {\n      var rawIdx = data.rawIndexOf(ordinalDataDim, scale.getRawOrdinalNumber(tickNum));\n      var value = rawIdx < 0 // If some tick have no bar, the tick will be treated as min.\n      ? Number.MIN_VALUE // PENDING: if dataZoom on baseAxis exits, is it a performance issue?\n      : orderMapping(data.indexOfRawIndex(rawIdx));\n      if (value > lastValue) {\n        return true;\n      }\n      lastValue = value;\n    }\n    return false;\n  };\n  /*\r\n   * Consider the case when A and B changed order, whose representing\r\n   * bars are both out of sight, we don't wish to trigger reorder action\r\n   * as long as the order in the view doesn't change.\r\n   */\n\n  BarView.prototype._isOrderDifferentInView = function (orderInfo, baseAxis) {\n    var scale = baseAxis.scale;\n    var extent = scale.getExtent();\n    var tickNum = Math.max(0, extent[0]);\n    var tickMax = Math.min(extent[1], scale.getOrdinalMeta().categories.length - 1);\n    for (; tickNum <= tickMax; ++tickNum) {\n      if (orderInfo.ordinalNumbers[tickNum] !== scale.getRawOrdinalNumber(tickNum)) {\n        return true;\n      }\n    }\n  };\n  BarView.prototype._updateSortWithinSameData = function (data, orderMapping, baseAxis, api) {\n    if (!this._isOrderChangedWithinSameData(data, orderMapping, baseAxis)) {\n      return;\n    }\n    var sortInfo = this._dataSort(data, baseAxis, orderMapping);\n    if (this._isOrderDifferentInView(sortInfo, baseAxis)) {\n      this._removeOnRenderedListener(api);\n      api.dispatchAction({\n        type: 'changeAxisOrder',\n        componentType: baseAxis.dim + 'Axis',\n        axisId: baseAxis.index,\n        sortInfo: sortInfo\n      });\n    }\n  };\n  BarView.prototype._dispatchInitSort = function (data, realtimeSortCfg, api) {\n    var baseAxis = realtimeSortCfg.baseAxis;\n    var sortResult = this._dataSort(data, baseAxis, function (dataIdx) {\n      return data.get(data.mapDimension(realtimeSortCfg.otherAxis.dim), dataIdx);\n    });\n    api.dispatchAction({\n      type: 'changeAxisOrder',\n      componentType: baseAxis.dim + 'Axis',\n      isInitSort: true,\n      axisId: baseAxis.index,\n      sortInfo: sortResult\n    });\n  };\n  BarView.prototype.remove = function (ecModel, api) {\n    this._clear(this._model);\n    this._removeOnRenderedListener(api);\n  };\n  BarView.prototype.dispose = function (ecModel, api) {\n    this._removeOnRenderedListener(api);\n  };\n  BarView.prototype._removeOnRenderedListener = function (api) {\n    if (this._onRendered) {\n      api.getZr().off('rendered', this._onRendered);\n      this._onRendered = null;\n    }\n  };\n  BarView.prototype._clear = function (model) {\n    var group = this.group;\n    var data = this._data;\n    if (model && model.isAnimationEnabled() && data && !this._isLargeDraw) {\n      this._removeBackground();\n      this._backgroundEls = [];\n      data.eachItemGraphicEl(function (el) {\n        removeElementWithFadeOut(el, model, getECData(el).dataIndex);\n      });\n    } else {\n      group.removeAll();\n    }\n    this._data = null;\n    this._isFirstFrame = true;\n  };\n  BarView.prototype._removeBackground = function () {\n    this.group.remove(this._backgroundGroup);\n    this._backgroundGroup = null;\n  };\n  BarView.type = 'bar';\n  return BarView;\n}(ChartView);\nvar clip = {\n  cartesian2d: function (coordSysBoundingRect, layout) {\n    var signWidth = layout.width < 0 ? -1 : 1;\n    var signHeight = layout.height < 0 ? -1 : 1; // Needs positive width and height\n\n    if (signWidth < 0) {\n      layout.x += layout.width;\n      layout.width = -layout.width;\n    }\n    if (signHeight < 0) {\n      layout.y += layout.height;\n      layout.height = -layout.height;\n    }\n    var coordSysX2 = coordSysBoundingRect.x + coordSysBoundingRect.width;\n    var coordSysY2 = coordSysBoundingRect.y + coordSysBoundingRect.height;\n    var x = mathMax(layout.x, coordSysBoundingRect.x);\n    var x2 = mathMin(layout.x + layout.width, coordSysX2);\n    var y = mathMax(layout.y, coordSysBoundingRect.y);\n    var y2 = mathMin(layout.y + layout.height, coordSysY2);\n    var xClipped = x2 < x;\n    var yClipped = y2 < y; // When xClipped or yClipped, the element will be marked as `ignore`.\n    // But we should also place the element at the edge of the coord sys bounding rect.\n    // Because if data changed and the bar shows again, its transition animation\n    // will begin at this place.\n\n    layout.x = xClipped && x > coordSysX2 ? x2 : x;\n    layout.y = yClipped && y > coordSysY2 ? y2 : y;\n    layout.width = xClipped ? 0 : x2 - x;\n    layout.height = yClipped ? 0 : y2 - y; // Reverse back\n\n    if (signWidth < 0) {\n      layout.x += layout.width;\n      layout.width = -layout.width;\n    }\n    if (signHeight < 0) {\n      layout.y += layout.height;\n      layout.height = -layout.height;\n    }\n    return xClipped || yClipped;\n  },\n  polar: function (coordSysClipArea, layout) {\n    var signR = layout.r0 <= layout.r ? 1 : -1; // Make sure r is larger than r0\n\n    if (signR < 0) {\n      var tmp = layout.r;\n      layout.r = layout.r0;\n      layout.r0 = tmp;\n    }\n    var r = mathMin(layout.r, coordSysClipArea.r);\n    var r0 = mathMax(layout.r0, coordSysClipArea.r0);\n    layout.r = r;\n    layout.r0 = r0;\n    var clipped = r - r0 < 0; // Reverse back\n\n    if (signR < 0) {\n      var tmp = layout.r;\n      layout.r = layout.r0;\n      layout.r0 = tmp;\n    }\n    return clipped;\n  }\n};\nvar elementCreator = {\n  cartesian2d: function (seriesModel, data, newIndex, layout, isHorizontal, animationModel, axisModel, isUpdate, roundCap) {\n    var rect = new Rect({\n      shape: extend({}, layout),\n      z2: 1\n    });\n    rect.__dataIndex = newIndex;\n    rect.name = 'item';\n    if (animationModel) {\n      var rectShape = rect.shape;\n      var animateProperty = isHorizontal ? 'height' : 'width';\n      rectShape[animateProperty] = 0;\n    }\n    return rect;\n  },\n  polar: function (seriesModel, data, newIndex, layout, isRadial, animationModel, axisModel, isUpdate, roundCap) {\n    var ShapeClass = !isRadial && roundCap ? Sausage : Sector;\n    var sector = new ShapeClass({\n      shape: layout,\n      z2: 1\n    });\n    sector.name = 'item';\n    var positionMap = createPolarPositionMapping(isRadial);\n    sector.calculateTextPosition = createSectorCalculateTextPosition(positionMap, {\n      isRoundCap: ShapeClass === Sausage\n    }); // Animation\n\n    if (animationModel) {\n      var sectorShape = sector.shape;\n      var animateProperty = isRadial ? 'r' : 'endAngle';\n      var animateTarget = {};\n      sectorShape[animateProperty] = isRadial ? layout.r0 : layout.startAngle;\n      animateTarget[animateProperty] = layout[animateProperty];\n      (isUpdate ? updateProps : initProps)(sector, {\n        shape: animateTarget // __value: typeof dataValue === 'string' ? parseInt(dataValue, 10) : dataValue\n      }, animationModel);\n    }\n    return sector;\n  }\n};\nfunction shouldRealtimeSort(seriesModel, coordSys) {\n  var realtimeSortOption = seriesModel.get('realtimeSort', true);\n  var baseAxis = coordSys.getBaseAxis();\n  if (process.env.NODE_ENV !== 'production') {\n    if (realtimeSortOption) {\n      if (baseAxis.type !== 'category') {\n        warn('`realtimeSort` will not work because this bar series is not based on a category axis.');\n      }\n      if (coordSys.type !== 'cartesian2d') {\n        warn('`realtimeSort` will not work because this bar series is not on cartesian2d.');\n      }\n    }\n  }\n  if (realtimeSortOption && baseAxis.type === 'category' && coordSys.type === 'cartesian2d') {\n    return {\n      baseAxis: baseAxis,\n      otherAxis: coordSys.getOtherAxis(baseAxis)\n    };\n  }\n}\nfunction updateRealtimeAnimation(realtimeSortCfg, seriesAnimationModel, el, layout, newIndex, isHorizontal, isUpdate, isChangeOrder) {\n  var seriesTarget;\n  var axisTarget;\n  if (isHorizontal) {\n    axisTarget = {\n      x: layout.x,\n      width: layout.width\n    };\n    seriesTarget = {\n      y: layout.y,\n      height: layout.height\n    };\n  } else {\n    axisTarget = {\n      y: layout.y,\n      height: layout.height\n    };\n    seriesTarget = {\n      x: layout.x,\n      width: layout.width\n    };\n  }\n  if (!isChangeOrder) {\n    // Keep the original growth animation if only axis order changed.\n    // Not start a new animation.\n    (isUpdate ? updateProps : initProps)(el, {\n      shape: seriesTarget\n    }, seriesAnimationModel, newIndex, null);\n  }\n  var axisAnimationModel = seriesAnimationModel ? realtimeSortCfg.baseAxis.model : null;\n  (isUpdate ? updateProps : initProps)(el, {\n    shape: axisTarget\n  }, axisAnimationModel, newIndex);\n}\nfunction checkPropertiesNotValid(obj, props) {\n  for (var i = 0; i < props.length; i++) {\n    if (!isFinite(obj[props[i]])) {\n      return true;\n    }\n  }\n  return false;\n}\nvar rectPropties = ['x', 'y', 'width', 'height'];\nvar polarPropties = ['cx', 'cy', 'r', 'startAngle', 'endAngle'];\nvar isValidLayout = {\n  cartesian2d: function (layout) {\n    return !checkPropertiesNotValid(layout, rectPropties);\n  },\n  polar: function (layout) {\n    return !checkPropertiesNotValid(layout, polarPropties);\n  }\n};\nvar getLayout = {\n  // itemModel is only used to get borderWidth, which is not needed\n  // when calculating bar background layout.\n  cartesian2d: function (data, dataIndex, itemModel) {\n    var layout = data.getItemLayout(dataIndex);\n    var fixedLineWidth = itemModel ? getLineWidth(itemModel, layout) : 0; // fix layout with lineWidth\n\n    var signX = layout.width > 0 ? 1 : -1;\n    var signY = layout.height > 0 ? 1 : -1;\n    return {\n      x: layout.x + signX * fixedLineWidth / 2,\n      y: layout.y + signY * fixedLineWidth / 2,\n      width: layout.width - signX * fixedLineWidth,\n      height: layout.height - signY * fixedLineWidth\n    };\n  },\n  polar: function (data, dataIndex, itemModel) {\n    var layout = data.getItemLayout(dataIndex);\n    return {\n      cx: layout.cx,\n      cy: layout.cy,\n      r0: layout.r0,\n      r: layout.r,\n      startAngle: layout.startAngle,\n      endAngle: layout.endAngle,\n      clockwise: layout.clockwise\n    };\n  }\n};\nfunction isZeroOnPolar(layout) {\n  return layout.startAngle != null && layout.endAngle != null && layout.startAngle === layout.endAngle;\n}\nfunction createPolarPositionMapping(isRadial) {\n  return function (isRadial) {\n    var arcOrAngle = isRadial ? 'Arc' : 'Angle';\n    return function (position) {\n      switch (position) {\n        case 'start':\n        case 'insideStart':\n        case 'end':\n        case 'insideEnd':\n          return position + arcOrAngle;\n        default:\n          return position;\n      }\n    };\n  }(isRadial);\n}\nfunction updateStyle(el, data, dataIndex, itemModel, layout, seriesModel, isHorizontalOrRadial, isPolar) {\n  var style = data.getItemVisual(dataIndex, 'style');\n  if (!isPolar) {\n    var borderRadius = itemModel.get(['itemStyle', 'borderRadius']) || 0;\n    el.setShape('r', borderRadius);\n  } else if (!seriesModel.get('roundCap')) {\n    var sectorShape = el.shape;\n    var cornerRadius = getSectorCornerRadius(itemModel.getModel('itemStyle'), sectorShape, true);\n    extend(sectorShape, cornerRadius);\n    el.setShape(sectorShape);\n  }\n  el.useStyle(style);\n  var cursorStyle = itemModel.getShallow('cursor');\n  cursorStyle && el.attr('cursor', cursorStyle);\n  var labelPositionOutside = isPolar ? isHorizontalOrRadial ? layout.r >= layout.r0 ? 'endArc' : 'startArc' : layout.endAngle >= layout.startAngle ? 'endAngle' : 'startAngle' : isHorizontalOrRadial ? layout.height >= 0 ? 'bottom' : 'top' : layout.width >= 0 ? 'right' : 'left';\n  var labelStatesModels = getLabelStatesModels(itemModel);\n  setLabelStyle(el, labelStatesModels, {\n    labelFetcher: seriesModel,\n    labelDataIndex: dataIndex,\n    defaultText: getDefaultLabel(seriesModel.getData(), dataIndex),\n    inheritColor: style.fill,\n    defaultOpacity: style.opacity,\n    defaultOutsidePosition: labelPositionOutside\n  });\n  var label = el.getTextContent();\n  if (isPolar && label) {\n    var position = itemModel.get(['label', 'position']);\n    el.textConfig.inside = position === 'middle' ? true : null;\n    setSectorTextRotation(el, position === 'outside' ? labelPositionOutside : position, createPolarPositionMapping(isHorizontalOrRadial), itemModel.get(['label', 'rotate']));\n  }\n  setLabelValueAnimation(label, labelStatesModels, seriesModel.getRawValue(dataIndex), function (value) {\n    return getDefaultInterpolatedLabel(data, value);\n  });\n  var emphasisModel = itemModel.getModel(['emphasis']);\n  toggleHoverEmphasis(el, emphasisModel.get('focus'), emphasisModel.get('blurScope'), emphasisModel.get('disabled'));\n  setStatesStylesFromModel(el, itemModel);\n  if (isZeroOnPolar(layout)) {\n    el.style.fill = 'none';\n    el.style.stroke = 'none';\n    each(el.states, function (state) {\n      if (state.style) {\n        state.style.fill = state.style.stroke = 'none';\n      }\n    });\n  }\n} // In case width or height are too small.\n\nfunction getLineWidth(itemModel, rawLayout) {\n  // Has no border.\n  var borderColor = itemModel.get(['itemStyle', 'borderColor']);\n  if (!borderColor || borderColor === 'none') {\n    return 0;\n  }\n  var lineWidth = itemModel.get(['itemStyle', 'borderWidth']) || 0; // width or height may be NaN for empty data\n\n  var width = isNaN(rawLayout.width) ? Number.MAX_VALUE : Math.abs(rawLayout.width);\n  var height = isNaN(rawLayout.height) ? Number.MAX_VALUE : Math.abs(rawLayout.height);\n  return Math.min(lineWidth, width, height);\n}\nvar LagePathShape = /** @class */\nfunction () {\n  function LagePathShape() {}\n  return LagePathShape;\n}();\nvar LargePath = /** @class */\nfunction (_super) {\n  __extends(LargePath, _super);\n  function LargePath(opts) {\n    var _this = _super.call(this, opts) || this;\n    _this.type = 'largeBar';\n    return _this;\n  }\n  LargePath.prototype.getDefaultShape = function () {\n    return new LagePathShape();\n  };\n  LargePath.prototype.buildPath = function (ctx, shape) {\n    // Drawing lines is more efficient than drawing\n    // a whole line or drawing rects.\n    var points = shape.points;\n    var baseDimIdx = this.baseDimIdx;\n    var valueDimIdx = 1 - this.baseDimIdx;\n    var startPoint = [];\n    var size = [];\n    var barWidth = this.barWidth;\n    for (var i = 0; i < points.length; i += 3) {\n      size[baseDimIdx] = barWidth;\n      size[valueDimIdx] = points[i + 2];\n      startPoint[baseDimIdx] = points[i + baseDimIdx];\n      startPoint[valueDimIdx] = points[i + valueDimIdx];\n      ctx.rect(startPoint[0], startPoint[1], size[0], size[1]);\n    }\n  };\n  return LargePath;\n}(Path);\nfunction createLarge(seriesModel, group, progressiveEls, incremental) {\n  // TODO support polar\n  var data = seriesModel.getData();\n  var baseDimIdx = data.getLayout('valueAxisHorizontal') ? 1 : 0;\n  var largeDataIndices = data.getLayout('largeDataIndices');\n  var barWidth = data.getLayout('size');\n  var backgroundModel = seriesModel.getModel('backgroundStyle');\n  var bgPoints = data.getLayout('largeBackgroundPoints');\n  if (bgPoints) {\n    var bgEl = new LargePath({\n      shape: {\n        points: bgPoints\n      },\n      incremental: !!incremental,\n      silent: true,\n      z2: 0\n    });\n    bgEl.baseDimIdx = baseDimIdx;\n    bgEl.largeDataIndices = largeDataIndices;\n    bgEl.barWidth = barWidth;\n    bgEl.useStyle(backgroundModel.getItemStyle());\n    group.add(bgEl);\n    progressiveEls && progressiveEls.push(bgEl);\n  }\n  var el = new LargePath({\n    shape: {\n      points: data.getLayout('largePoints')\n    },\n    incremental: !!incremental,\n    ignoreCoarsePointer: true,\n    z2: 1\n  });\n  el.baseDimIdx = baseDimIdx;\n  el.largeDataIndices = largeDataIndices;\n  el.barWidth = barWidth;\n  group.add(el);\n  el.useStyle(data.getVisual('style')); // Enable tooltip and user mouse/touch event handlers.\n\n  getECData(el).seriesIndex = seriesModel.seriesIndex;\n  if (!seriesModel.get('silent')) {\n    el.on('mousedown', largePathUpdateDataIndex);\n    el.on('mousemove', largePathUpdateDataIndex);\n  }\n  progressiveEls && progressiveEls.push(el);\n} // Use throttle to avoid frequently traverse to find dataIndex.\n\nvar largePathUpdateDataIndex = throttle(function (event) {\n  var largePath = this;\n  var dataIndex = largePathFindDataIndex(largePath, event.offsetX, event.offsetY);\n  getECData(largePath).dataIndex = dataIndex >= 0 ? dataIndex : null;\n}, 30, false);\nfunction largePathFindDataIndex(largePath, x, y) {\n  var baseDimIdx = largePath.baseDimIdx;\n  var valueDimIdx = 1 - baseDimIdx;\n  var points = largePath.shape.points;\n  var largeDataIndices = largePath.largeDataIndices;\n  var startPoint = [];\n  var size = [];\n  var barWidth = largePath.barWidth;\n  for (var i = 0, len = points.length / 3; i < len; i++) {\n    var ii = i * 3;\n    size[baseDimIdx] = barWidth;\n    size[valueDimIdx] = points[ii + 2];\n    startPoint[baseDimIdx] = points[ii + baseDimIdx];\n    startPoint[valueDimIdx] = points[ii + valueDimIdx];\n    if (size[valueDimIdx] < 0) {\n      startPoint[valueDimIdx] += size[valueDimIdx];\n      size[valueDimIdx] = -size[valueDimIdx];\n    }\n    if (x >= startPoint[0] && x <= startPoint[0] + size[0] && y >= startPoint[1] && y <= startPoint[1] + size[1]) {\n      return largeDataIndices[i];\n    }\n  }\n  return -1;\n}\nfunction createBackgroundShape(isHorizontalOrRadial, layout, coord) {\n  if (isCoordinateSystemType(coord, 'cartesian2d')) {\n    var rectShape = layout;\n    var coordLayout = coord.getArea();\n    return {\n      x: isHorizontalOrRadial ? rectShape.x : coordLayout.x,\n      y: isHorizontalOrRadial ? coordLayout.y : rectShape.y,\n      width: isHorizontalOrRadial ? rectShape.width : coordLayout.width,\n      height: isHorizontalOrRadial ? coordLayout.height : rectShape.height\n    };\n  } else {\n    var coordLayout = coord.getArea();\n    var sectorShape = layout;\n    return {\n      cx: coordLayout.cx,\n      cy: coordLayout.cy,\n      r0: isHorizontalOrRadial ? coordLayout.r0 : sectorShape.r0,\n      r: isHorizontalOrRadial ? coordLayout.r : sectorShape.r,\n      startAngle: isHorizontalOrRadial ? sectorShape.startAngle : 0,\n      endAngle: isHorizontalOrRadial ? sectorShape.endAngle : Math.PI * 2\n    };\n  }\n}\nfunction createBackgroundEl(coord, isHorizontalOrRadial, layout) {\n  var ElementClz = coord.type === 'polar' ? Sector : Rect;\n  return new ElementClz({\n    shape: createBackgroundShape(isHorizontalOrRadial, layout, coord),\n    silent: true,\n    z2: 0\n  });\n}\nexport default BarView;", "map": {"version": 3, "names": ["__extends", "Path", "Group", "extend", "each", "map", "Rect", "Sector", "updateProps", "initProps", "removeElementWithFadeOut", "traverseElements", "getECData", "setStatesStylesFromModel", "toggleHoverEmphasis", "setLabelStyle", "getLabelStatesModels", "setLabelValueAnimation", "labelInner", "throttle", "createClipPath", "Sausage", "ChartView", "isCoordinateSystemType", "getDefaultLabel", "getDefaultInterpolatedLabel", "warn", "createSectorCalculateTextPosition", "setSectorTextRotation", "saveOldStyle", "getSectorCornerRadius", "mathMax", "Math", "max", "mathMin", "min", "getClipArea", "coord", "data", "coordSysClipArea", "getArea", "baseAxis", "getBaseAxis", "type", "onBand", "expandWidth", "getLayout", "isHorizontal", "x", "width", "y", "height", "BarView", "_super", "_this", "call", "_isFirstFrame", "prototype", "render", "seriesModel", "ecModel", "api", "payload", "_model", "_removeOnRenderedListener", "_updateDrawMode", "coordinateSystemType", "get", "_progressiveEls", "_isLargeDraw", "_renderLarge", "_renderNormal", "process", "env", "NODE_ENV", "incrementalPrepareRender", "_clear", "_updateLarge<PERSON>lip", "incrementalRender", "params", "_incrementalRenderLarge", "eachRendered", "cb", "group", "isLargeDraw", "pipelineContext", "large", "getData", "oldData", "_data", "coordinateSystem", "isHorizontalOrRadial", "dim", "animationModel", "isAnimationEnabled", "realtimeSortCfg", "shouldRealtimeSort", "_enableRealtimeSort", "needsClip", "removeClip<PERSON>ath", "roundCap", "drawBackground", "backgroundModel", "getModel", "barBorderRadius", "bgEls", "oldBgEls", "_backgroundEls", "isInitSort", "isChangeOrder", "createBackground", "dataIndex", "bgLayout", "bgEl", "createBackgroundEl", "useStyle", "getItemStyle", "setShape", "diff", "add", "itemModel", "getItemModel", "layout", "hasValue", "isValidLayout", "isClipped", "clip", "el", "elementCreator", "model", "forceLabelAnimation", "updateStyle", "attr", "shape", "updateRealtimeAnimation", "setItemGraphicEl", "ignore", "update", "newIndex", "oldIndex", "length", "createBackgroundShape", "getItemGraphicEl", "remove", "textEl", "getTextContent", "labelInnerStore", "prevValue", "value", "execute", "bgGroup", "_backgroundGroup", "removeAll", "i", "createLarge", "_removeBackground", "clipPath", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "count", "_dispatchInitSort", "orderMapping_1", "idx", "abs", "_onRendered", "_updateSortWithinSameData", "getZr", "on", "_dataSort", "orderMapping", "info", "mapDimension", "ordinalNumber", "dataIdx", "mappedValue", "NaN", "push", "sort", "a", "b", "ordinalNumbers", "item", "_isOrderChangedWithinSameData", "scale", "ordinalDataDim", "lastValue", "Number", "MAX_VALUE", "tickNum", "len", "getOrdinalMeta", "categories", "rawIdx", "rawIndexOf", "getRawOrdinalNumber", "MIN_VALUE", "indexOfRawIndex", "_isOrderDifferentInView", "orderInfo", "extent", "getExtent", "tickMax", "sortInfo", "dispatchAction", "componentType", "axisId", "index", "sortResult", "otherAxis", "dispose", "off", "eachItemGraphicEl", "cartesian2d", "coordSysBoundingRect", "signWidth", "signHeight", "coordSysX2", "coordSysY2", "x2", "y2", "xClipped", "yClipped", "polar", "signR", "r0", "r", "tmp", "clipped", "axisModel", "isUpdate", "rect", "z2", "__dataIndex", "name", "rectShape", "animateProperty", "isRadial", "ShapeClass", "sector", "positionMap", "createPolarPositionMapping", "calculateTextPosition", "isRoundCap", "sectorShape", "animate<PERSON>arget", "startAngle", "coordSys", "realtimeSortOption", "getOtherAxis", "seriesAnimationModel", "seriesTarget", "axisTarget", "axisAnimationModel", "checkPropertiesNotValid", "obj", "props", "isFinite", "rectPropties", "polarPropties", "getItemLayout", "fixedLineWidth", "getLineWidth", "signX", "signY", "cx", "cy", "endAngle", "clockwise", "isZeroOnPolar", "arcOrAngle", "position", "isPolar", "style", "getItemVisual", "borderRadius", "cornerRadius", "cursorStyle", "getShallow", "labelPositionOutside", "labelStatesModels", "labelFetcher", "labelDataIndex", "defaultText", "inheritColor", "fill", "defaultOpacity", "opacity", "defaultOutsidePosition", "label", "textConfig", "inside", "getRawValue", "emphasisModel", "stroke", "states", "state", "rawLayout", "borderColor", "lineWidth", "isNaN", "LagePathShape", "LargePath", "opts", "getDefaultShape", "buildPath", "ctx", "points", "baseDimIdx", "valueDimIdx", "startPoint", "size", "<PERSON><PERSON><PERSON><PERSON>", "progressiveEls", "incremental", "largeDataIndices", "bgPoints", "silent", "ignoreCoarsePointer", "getVisual", "seriesIndex", "largePathUpdateDataIndex", "event", "largePath", "largePathFindDataIndex", "offsetX", "offsetY", "ii", "coordLayout", "PI", "ElementClz"], "sources": ["C:/WolfSystemV2/WolfOldFrontEnd/node_modules/echarts/lib/chart/bar/BarView.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\nimport Path from 'zrender/lib/graphic/Path.js';\nimport Group from 'zrender/lib/graphic/Group.js';\nimport { extend, each, map } from 'zrender/lib/core/util.js';\nimport { Rect, Sector, updateProps, initProps, removeElementWithFadeOut, traverseElements } from '../../util/graphic.js';\nimport { getECData } from '../../util/innerStore.js';\nimport { setStatesStylesFromModel, toggleHoverEmphasis } from '../../util/states.js';\nimport { setLabelStyle, getLabelStatesModels, setLabelValueAnimation, labelInner } from '../../label/labelStyle.js';\nimport { throttle } from '../../util/throttle.js';\nimport { createClipPath } from '../helper/createClipPathFromCoordSys.js';\nimport Sausage from '../../util/shape/sausage.js';\nimport ChartView from '../../view/Chart.js';\nimport { isCoordinateSystemType } from '../../coord/CoordinateSystem.js';\nimport { getDefaultLabel, getDefaultInterpolatedLabel } from '../helper/labelHelper.js';\nimport { warn } from '../../util/log.js';\nimport { createSectorCalculateTextPosition, setSectorTextRotation } from '../../label/sectorLabel.js';\nimport { saveOldStyle } from '../../animation/basicTransition.js';\nimport { getSectorCornerRadius } from '../helper/sectorHelper.js';\nvar mathMax = Math.max;\nvar mathMin = Math.min;\n\nfunction getClipArea(coord, data) {\n  var coordSysClipArea = coord.getArea && coord.getArea();\n\n  if (isCoordinateSystemType(coord, 'cartesian2d')) {\n    var baseAxis = coord.getBaseAxis(); // When boundaryGap is false or using time axis. bar may exceed the grid.\n    // We should not clip this part.\n    // See test/bar2.html\n\n    if (baseAxis.type !== 'category' || !baseAxis.onBand) {\n      var expandWidth = data.getLayout('bandWidth');\n\n      if (baseAxis.isHorizontal()) {\n        coordSysClipArea.x -= expandWidth;\n        coordSysClipArea.width += expandWidth * 2;\n      } else {\n        coordSysClipArea.y -= expandWidth;\n        coordSysClipArea.height += expandWidth * 2;\n      }\n    }\n  }\n\n  return coordSysClipArea;\n}\n\nvar BarView =\n/** @class */\nfunction (_super) {\n  __extends(BarView, _super);\n\n  function BarView() {\n    var _this = _super.call(this) || this;\n\n    _this.type = BarView.type;\n    _this._isFirstFrame = true;\n    return _this;\n  }\n\n  BarView.prototype.render = function (seriesModel, ecModel, api, payload) {\n    this._model = seriesModel;\n\n    this._removeOnRenderedListener(api);\n\n    this._updateDrawMode(seriesModel);\n\n    var coordinateSystemType = seriesModel.get('coordinateSystem');\n\n    if (coordinateSystemType === 'cartesian2d' || coordinateSystemType === 'polar') {\n      // Clear previously rendered progressive elements.\n      this._progressiveEls = null;\n      this._isLargeDraw ? this._renderLarge(seriesModel, ecModel, api) : this._renderNormal(seriesModel, ecModel, api, payload);\n    } else if (process.env.NODE_ENV !== 'production') {\n      warn('Only cartesian2d and polar supported for bar.');\n    }\n  };\n\n  BarView.prototype.incrementalPrepareRender = function (seriesModel) {\n    this._clear();\n\n    this._updateDrawMode(seriesModel); // incremental also need to clip, otherwise might be overlow.\n    // But must not set clip in each frame, otherwise all of the children will be marked redraw.\n\n\n    this._updateLargeClip(seriesModel);\n  };\n\n  BarView.prototype.incrementalRender = function (params, seriesModel) {\n    // Reset\n    this._progressiveEls = []; // Do not support progressive in normal mode.\n\n    this._incrementalRenderLarge(params, seriesModel);\n  };\n\n  BarView.prototype.eachRendered = function (cb) {\n    traverseElements(this._progressiveEls || this.group, cb);\n  };\n\n  BarView.prototype._updateDrawMode = function (seriesModel) {\n    var isLargeDraw = seriesModel.pipelineContext.large;\n\n    if (this._isLargeDraw == null || isLargeDraw !== this._isLargeDraw) {\n      this._isLargeDraw = isLargeDraw;\n\n      this._clear();\n    }\n  };\n\n  BarView.prototype._renderNormal = function (seriesModel, ecModel, api, payload) {\n    var group = this.group;\n    var data = seriesModel.getData();\n    var oldData = this._data;\n    var coord = seriesModel.coordinateSystem;\n    var baseAxis = coord.getBaseAxis();\n    var isHorizontalOrRadial;\n\n    if (coord.type === 'cartesian2d') {\n      isHorizontalOrRadial = baseAxis.isHorizontal();\n    } else if (coord.type === 'polar') {\n      isHorizontalOrRadial = baseAxis.dim === 'angle';\n    }\n\n    var animationModel = seriesModel.isAnimationEnabled() ? seriesModel : null;\n    var realtimeSortCfg = shouldRealtimeSort(seriesModel, coord);\n\n    if (realtimeSortCfg) {\n      this._enableRealtimeSort(realtimeSortCfg, data, api);\n    }\n\n    var needsClip = seriesModel.get('clip', true) || realtimeSortCfg;\n    var coordSysClipArea = getClipArea(coord, data); // If there is clipPath created in large mode. Remove it.\n\n    group.removeClipPath(); // We don't use clipPath in normal mode because we needs a perfect animation\n    // And don't want the label are clipped.\n\n    var roundCap = seriesModel.get('roundCap', true);\n    var drawBackground = seriesModel.get('showBackground', true);\n    var backgroundModel = seriesModel.getModel('backgroundStyle');\n    var barBorderRadius = backgroundModel.get('borderRadius') || 0;\n    var bgEls = [];\n    var oldBgEls = this._backgroundEls;\n    var isInitSort = payload && payload.isInitSort;\n    var isChangeOrder = payload && payload.type === 'changeAxisOrder';\n\n    function createBackground(dataIndex) {\n      var bgLayout = getLayout[coord.type](data, dataIndex);\n      var bgEl = createBackgroundEl(coord, isHorizontalOrRadial, bgLayout);\n      bgEl.useStyle(backgroundModel.getItemStyle()); // Only cartesian2d support borderRadius.\n\n      if (coord.type === 'cartesian2d') {\n        bgEl.setShape('r', barBorderRadius);\n      } else {\n        bgEl.setShape('cornerRadius', barBorderRadius);\n      }\n\n      bgEls[dataIndex] = bgEl;\n      return bgEl;\n    }\n\n    ;\n    data.diff(oldData).add(function (dataIndex) {\n      var itemModel = data.getItemModel(dataIndex);\n      var layout = getLayout[coord.type](data, dataIndex, itemModel);\n\n      if (drawBackground) {\n        createBackground(dataIndex);\n      } // If dataZoom in filteMode: 'empty', the baseValue can be set as NaN in \"axisProxy\".\n\n\n      if (!data.hasValue(dataIndex) || !isValidLayout[coord.type](layout)) {\n        return;\n      }\n\n      var isClipped = false;\n\n      if (needsClip) {\n        // Clip will modify the layout params.\n        // And return a boolean to determine if the shape are fully clipped.\n        isClipped = clip[coord.type](coordSysClipArea, layout);\n      }\n\n      var el = elementCreator[coord.type](seriesModel, data, dataIndex, layout, isHorizontalOrRadial, animationModel, baseAxis.model, false, roundCap);\n\n      if (realtimeSortCfg) {\n        /**\r\n         * Force label animation because even if the element is\r\n         * ignored because it's clipped, it may not be clipped after\r\n         * changing order. Then, if not using forceLabelAnimation,\r\n         * the label animation was never started, in which case,\r\n         * the label will be the final value and doesn't have label\r\n         * animation.\r\n         */\n        el.forceLabelAnimation = true;\n      }\n\n      updateStyle(el, data, dataIndex, itemModel, layout, seriesModel, isHorizontalOrRadial, coord.type === 'polar');\n\n      if (isInitSort) {\n        el.attr({\n          shape: layout\n        });\n      } else if (realtimeSortCfg) {\n        updateRealtimeAnimation(realtimeSortCfg, animationModel, el, layout, dataIndex, isHorizontalOrRadial, false, false);\n      } else {\n        initProps(el, {\n          shape: layout\n        }, seriesModel, dataIndex);\n      }\n\n      data.setItemGraphicEl(dataIndex, el);\n      group.add(el);\n      el.ignore = isClipped;\n    }).update(function (newIndex, oldIndex) {\n      var itemModel = data.getItemModel(newIndex);\n      var layout = getLayout[coord.type](data, newIndex, itemModel);\n\n      if (drawBackground) {\n        var bgEl = void 0;\n\n        if (oldBgEls.length === 0) {\n          bgEl = createBackground(oldIndex);\n        } else {\n          bgEl = oldBgEls[oldIndex];\n          bgEl.useStyle(backgroundModel.getItemStyle()); // Only cartesian2d support borderRadius.\n\n          if (coord.type === 'cartesian2d') {\n            bgEl.setShape('r', barBorderRadius);\n          } else {\n            bgEl.setShape('cornerRadius', barBorderRadius);\n          }\n\n          bgEls[newIndex] = bgEl;\n        }\n\n        var bgLayout = getLayout[coord.type](data, newIndex);\n        var shape = createBackgroundShape(isHorizontalOrRadial, bgLayout, coord);\n        updateProps(bgEl, {\n          shape: shape\n        }, animationModel, newIndex);\n      }\n\n      var el = oldData.getItemGraphicEl(oldIndex);\n\n      if (!data.hasValue(newIndex) || !isValidLayout[coord.type](layout)) {\n        group.remove(el);\n        return;\n      }\n\n      var isClipped = false;\n\n      if (needsClip) {\n        isClipped = clip[coord.type](coordSysClipArea, layout);\n\n        if (isClipped) {\n          group.remove(el);\n        }\n      }\n\n      if (!el) {\n        el = elementCreator[coord.type](seriesModel, data, newIndex, layout, isHorizontalOrRadial, animationModel, baseAxis.model, !!el, roundCap);\n      } else {\n        saveOldStyle(el);\n      }\n\n      if (realtimeSortCfg) {\n        el.forceLabelAnimation = true;\n      }\n\n      if (isChangeOrder) {\n        var textEl = el.getTextContent();\n\n        if (textEl) {\n          var labelInnerStore = labelInner(textEl);\n\n          if (labelInnerStore.prevValue != null) {\n            /**\r\n             * Set preValue to be value so that no new label\r\n             * should be started, otherwise, it will take a full\r\n             * `animationDurationUpdate` time to finish the\r\n             * animation, which is not expected.\r\n             */\n            labelInnerStore.prevValue = labelInnerStore.value;\n          }\n        }\n      } // Not change anything if only order changed.\n      // Especially not change label.\n      else {\n          updateStyle(el, data, newIndex, itemModel, layout, seriesModel, isHorizontalOrRadial, coord.type === 'polar');\n        }\n\n      if (isInitSort) {\n        el.attr({\n          shape: layout\n        });\n      } else if (realtimeSortCfg) {\n        updateRealtimeAnimation(realtimeSortCfg, animationModel, el, layout, newIndex, isHorizontalOrRadial, true, isChangeOrder);\n      } else {\n        updateProps(el, {\n          shape: layout\n        }, seriesModel, newIndex, null);\n      }\n\n      data.setItemGraphicEl(newIndex, el);\n      el.ignore = isClipped;\n      group.add(el);\n    }).remove(function (dataIndex) {\n      var el = oldData.getItemGraphicEl(dataIndex);\n      el && removeElementWithFadeOut(el, seriesModel, dataIndex);\n    }).execute();\n    var bgGroup = this._backgroundGroup || (this._backgroundGroup = new Group());\n    bgGroup.removeAll();\n\n    for (var i = 0; i < bgEls.length; ++i) {\n      bgGroup.add(bgEls[i]);\n    }\n\n    group.add(bgGroup);\n    this._backgroundEls = bgEls;\n    this._data = data;\n  };\n\n  BarView.prototype._renderLarge = function (seriesModel, ecModel, api) {\n    this._clear();\n\n    createLarge(seriesModel, this.group);\n\n    this._updateLargeClip(seriesModel);\n  };\n\n  BarView.prototype._incrementalRenderLarge = function (params, seriesModel) {\n    this._removeBackground();\n\n    createLarge(seriesModel, this.group, this._progressiveEls, true);\n  };\n\n  BarView.prototype._updateLargeClip = function (seriesModel) {\n    // Use clipPath in large mode.\n    var clipPath = seriesModel.get('clip', true) && createClipPath(seriesModel.coordinateSystem, false, seriesModel);\n    var group = this.group;\n\n    if (clipPath) {\n      group.setClipPath(clipPath);\n    } else {\n      group.removeClipPath();\n    }\n  };\n\n  BarView.prototype._enableRealtimeSort = function (realtimeSortCfg, data, api) {\n    var _this = this; // If no data in the first frame, wait for data to initSort\n\n\n    if (!data.count()) {\n      return;\n    }\n\n    var baseAxis = realtimeSortCfg.baseAxis;\n\n    if (this._isFirstFrame) {\n      this._dispatchInitSort(data, realtimeSortCfg, api);\n\n      this._isFirstFrame = false;\n    } else {\n      var orderMapping_1 = function (idx) {\n        var el = data.getItemGraphicEl(idx);\n        var shape = el && el.shape;\n        return shape && // The result should be consistent with the initial sort by data value.\n        // Do not support the case that both positive and negative exist.\n        Math.abs(baseAxis.isHorizontal() ? shape.height : shape.width) // If data is NaN, shape.xxx may be NaN, so use || 0 here in case\n        || 0;\n      };\n\n      this._onRendered = function () {\n        _this._updateSortWithinSameData(data, orderMapping_1, baseAxis, api);\n      };\n\n      api.getZr().on('rendered', this._onRendered);\n    }\n  };\n\n  BarView.prototype._dataSort = function (data, baseAxis, orderMapping) {\n    var info = [];\n    data.each(data.mapDimension(baseAxis.dim), function (ordinalNumber, dataIdx) {\n      var mappedValue = orderMapping(dataIdx);\n      mappedValue = mappedValue == null ? NaN : mappedValue;\n      info.push({\n        dataIndex: dataIdx,\n        mappedValue: mappedValue,\n        ordinalNumber: ordinalNumber\n      });\n    });\n    info.sort(function (a, b) {\n      // If NaN, it will be treated as min val.\n      return b.mappedValue - a.mappedValue;\n    });\n    return {\n      ordinalNumbers: map(info, function (item) {\n        return item.ordinalNumber;\n      })\n    };\n  };\n\n  BarView.prototype._isOrderChangedWithinSameData = function (data, orderMapping, baseAxis) {\n    var scale = baseAxis.scale;\n    var ordinalDataDim = data.mapDimension(baseAxis.dim);\n    var lastValue = Number.MAX_VALUE;\n\n    for (var tickNum = 0, len = scale.getOrdinalMeta().categories.length; tickNum < len; ++tickNum) {\n      var rawIdx = data.rawIndexOf(ordinalDataDim, scale.getRawOrdinalNumber(tickNum));\n      var value = rawIdx < 0 // If some tick have no bar, the tick will be treated as min.\n      ? Number.MIN_VALUE // PENDING: if dataZoom on baseAxis exits, is it a performance issue?\n      : orderMapping(data.indexOfRawIndex(rawIdx));\n\n      if (value > lastValue) {\n        return true;\n      }\n\n      lastValue = value;\n    }\n\n    return false;\n  };\n  /*\r\n   * Consider the case when A and B changed order, whose representing\r\n   * bars are both out of sight, we don't wish to trigger reorder action\r\n   * as long as the order in the view doesn't change.\r\n   */\n\n\n  BarView.prototype._isOrderDifferentInView = function (orderInfo, baseAxis) {\n    var scale = baseAxis.scale;\n    var extent = scale.getExtent();\n    var tickNum = Math.max(0, extent[0]);\n    var tickMax = Math.min(extent[1], scale.getOrdinalMeta().categories.length - 1);\n\n    for (; tickNum <= tickMax; ++tickNum) {\n      if (orderInfo.ordinalNumbers[tickNum] !== scale.getRawOrdinalNumber(tickNum)) {\n        return true;\n      }\n    }\n  };\n\n  BarView.prototype._updateSortWithinSameData = function (data, orderMapping, baseAxis, api) {\n    if (!this._isOrderChangedWithinSameData(data, orderMapping, baseAxis)) {\n      return;\n    }\n\n    var sortInfo = this._dataSort(data, baseAxis, orderMapping);\n\n    if (this._isOrderDifferentInView(sortInfo, baseAxis)) {\n      this._removeOnRenderedListener(api);\n\n      api.dispatchAction({\n        type: 'changeAxisOrder',\n        componentType: baseAxis.dim + 'Axis',\n        axisId: baseAxis.index,\n        sortInfo: sortInfo\n      });\n    }\n  };\n\n  BarView.prototype._dispatchInitSort = function (data, realtimeSortCfg, api) {\n    var baseAxis = realtimeSortCfg.baseAxis;\n\n    var sortResult = this._dataSort(data, baseAxis, function (dataIdx) {\n      return data.get(data.mapDimension(realtimeSortCfg.otherAxis.dim), dataIdx);\n    });\n\n    api.dispatchAction({\n      type: 'changeAxisOrder',\n      componentType: baseAxis.dim + 'Axis',\n      isInitSort: true,\n      axisId: baseAxis.index,\n      sortInfo: sortResult\n    });\n  };\n\n  BarView.prototype.remove = function (ecModel, api) {\n    this._clear(this._model);\n\n    this._removeOnRenderedListener(api);\n  };\n\n  BarView.prototype.dispose = function (ecModel, api) {\n    this._removeOnRenderedListener(api);\n  };\n\n  BarView.prototype._removeOnRenderedListener = function (api) {\n    if (this._onRendered) {\n      api.getZr().off('rendered', this._onRendered);\n      this._onRendered = null;\n    }\n  };\n\n  BarView.prototype._clear = function (model) {\n    var group = this.group;\n    var data = this._data;\n\n    if (model && model.isAnimationEnabled() && data && !this._isLargeDraw) {\n      this._removeBackground();\n\n      this._backgroundEls = [];\n      data.eachItemGraphicEl(function (el) {\n        removeElementWithFadeOut(el, model, getECData(el).dataIndex);\n      });\n    } else {\n      group.removeAll();\n    }\n\n    this._data = null;\n    this._isFirstFrame = true;\n  };\n\n  BarView.prototype._removeBackground = function () {\n    this.group.remove(this._backgroundGroup);\n    this._backgroundGroup = null;\n  };\n\n  BarView.type = 'bar';\n  return BarView;\n}(ChartView);\n\nvar clip = {\n  cartesian2d: function (coordSysBoundingRect, layout) {\n    var signWidth = layout.width < 0 ? -1 : 1;\n    var signHeight = layout.height < 0 ? -1 : 1; // Needs positive width and height\n\n    if (signWidth < 0) {\n      layout.x += layout.width;\n      layout.width = -layout.width;\n    }\n\n    if (signHeight < 0) {\n      layout.y += layout.height;\n      layout.height = -layout.height;\n    }\n\n    var coordSysX2 = coordSysBoundingRect.x + coordSysBoundingRect.width;\n    var coordSysY2 = coordSysBoundingRect.y + coordSysBoundingRect.height;\n    var x = mathMax(layout.x, coordSysBoundingRect.x);\n    var x2 = mathMin(layout.x + layout.width, coordSysX2);\n    var y = mathMax(layout.y, coordSysBoundingRect.y);\n    var y2 = mathMin(layout.y + layout.height, coordSysY2);\n    var xClipped = x2 < x;\n    var yClipped = y2 < y; // When xClipped or yClipped, the element will be marked as `ignore`.\n    // But we should also place the element at the edge of the coord sys bounding rect.\n    // Because if data changed and the bar shows again, its transition animation\n    // will begin at this place.\n\n    layout.x = xClipped && x > coordSysX2 ? x2 : x;\n    layout.y = yClipped && y > coordSysY2 ? y2 : y;\n    layout.width = xClipped ? 0 : x2 - x;\n    layout.height = yClipped ? 0 : y2 - y; // Reverse back\n\n    if (signWidth < 0) {\n      layout.x += layout.width;\n      layout.width = -layout.width;\n    }\n\n    if (signHeight < 0) {\n      layout.y += layout.height;\n      layout.height = -layout.height;\n    }\n\n    return xClipped || yClipped;\n  },\n  polar: function (coordSysClipArea, layout) {\n    var signR = layout.r0 <= layout.r ? 1 : -1; // Make sure r is larger than r0\n\n    if (signR < 0) {\n      var tmp = layout.r;\n      layout.r = layout.r0;\n      layout.r0 = tmp;\n    }\n\n    var r = mathMin(layout.r, coordSysClipArea.r);\n    var r0 = mathMax(layout.r0, coordSysClipArea.r0);\n    layout.r = r;\n    layout.r0 = r0;\n    var clipped = r - r0 < 0; // Reverse back\n\n    if (signR < 0) {\n      var tmp = layout.r;\n      layout.r = layout.r0;\n      layout.r0 = tmp;\n    }\n\n    return clipped;\n  }\n};\nvar elementCreator = {\n  cartesian2d: function (seriesModel, data, newIndex, layout, isHorizontal, animationModel, axisModel, isUpdate, roundCap) {\n    var rect = new Rect({\n      shape: extend({}, layout),\n      z2: 1\n    });\n    rect.__dataIndex = newIndex;\n    rect.name = 'item';\n\n    if (animationModel) {\n      var rectShape = rect.shape;\n      var animateProperty = isHorizontal ? 'height' : 'width';\n      rectShape[animateProperty] = 0;\n    }\n\n    return rect;\n  },\n  polar: function (seriesModel, data, newIndex, layout, isRadial, animationModel, axisModel, isUpdate, roundCap) {\n    var ShapeClass = !isRadial && roundCap ? Sausage : Sector;\n    var sector = new ShapeClass({\n      shape: layout,\n      z2: 1\n    });\n    sector.name = 'item';\n    var positionMap = createPolarPositionMapping(isRadial);\n    sector.calculateTextPosition = createSectorCalculateTextPosition(positionMap, {\n      isRoundCap: ShapeClass === Sausage\n    }); // Animation\n\n    if (animationModel) {\n      var sectorShape = sector.shape;\n      var animateProperty = isRadial ? 'r' : 'endAngle';\n      var animateTarget = {};\n      sectorShape[animateProperty] = isRadial ? layout.r0 : layout.startAngle;\n      animateTarget[animateProperty] = layout[animateProperty];\n      (isUpdate ? updateProps : initProps)(sector, {\n        shape: animateTarget // __value: typeof dataValue === 'string' ? parseInt(dataValue, 10) : dataValue\n\n      }, animationModel);\n    }\n\n    return sector;\n  }\n};\n\nfunction shouldRealtimeSort(seriesModel, coordSys) {\n  var realtimeSortOption = seriesModel.get('realtimeSort', true);\n  var baseAxis = coordSys.getBaseAxis();\n\n  if (process.env.NODE_ENV !== 'production') {\n    if (realtimeSortOption) {\n      if (baseAxis.type !== 'category') {\n        warn('`realtimeSort` will not work because this bar series is not based on a category axis.');\n      }\n\n      if (coordSys.type !== 'cartesian2d') {\n        warn('`realtimeSort` will not work because this bar series is not on cartesian2d.');\n      }\n    }\n  }\n\n  if (realtimeSortOption && baseAxis.type === 'category' && coordSys.type === 'cartesian2d') {\n    return {\n      baseAxis: baseAxis,\n      otherAxis: coordSys.getOtherAxis(baseAxis)\n    };\n  }\n}\n\nfunction updateRealtimeAnimation(realtimeSortCfg, seriesAnimationModel, el, layout, newIndex, isHorizontal, isUpdate, isChangeOrder) {\n  var seriesTarget;\n  var axisTarget;\n\n  if (isHorizontal) {\n    axisTarget = {\n      x: layout.x,\n      width: layout.width\n    };\n    seriesTarget = {\n      y: layout.y,\n      height: layout.height\n    };\n  } else {\n    axisTarget = {\n      y: layout.y,\n      height: layout.height\n    };\n    seriesTarget = {\n      x: layout.x,\n      width: layout.width\n    };\n  }\n\n  if (!isChangeOrder) {\n    // Keep the original growth animation if only axis order changed.\n    // Not start a new animation.\n    (isUpdate ? updateProps : initProps)(el, {\n      shape: seriesTarget\n    }, seriesAnimationModel, newIndex, null);\n  }\n\n  var axisAnimationModel = seriesAnimationModel ? realtimeSortCfg.baseAxis.model : null;\n  (isUpdate ? updateProps : initProps)(el, {\n    shape: axisTarget\n  }, axisAnimationModel, newIndex);\n}\n\nfunction checkPropertiesNotValid(obj, props) {\n  for (var i = 0; i < props.length; i++) {\n    if (!isFinite(obj[props[i]])) {\n      return true;\n    }\n  }\n\n  return false;\n}\n\nvar rectPropties = ['x', 'y', 'width', 'height'];\nvar polarPropties = ['cx', 'cy', 'r', 'startAngle', 'endAngle'];\nvar isValidLayout = {\n  cartesian2d: function (layout) {\n    return !checkPropertiesNotValid(layout, rectPropties);\n  },\n  polar: function (layout) {\n    return !checkPropertiesNotValid(layout, polarPropties);\n  }\n};\nvar getLayout = {\n  // itemModel is only used to get borderWidth, which is not needed\n  // when calculating bar background layout.\n  cartesian2d: function (data, dataIndex, itemModel) {\n    var layout = data.getItemLayout(dataIndex);\n    var fixedLineWidth = itemModel ? getLineWidth(itemModel, layout) : 0; // fix layout with lineWidth\n\n    var signX = layout.width > 0 ? 1 : -1;\n    var signY = layout.height > 0 ? 1 : -1;\n    return {\n      x: layout.x + signX * fixedLineWidth / 2,\n      y: layout.y + signY * fixedLineWidth / 2,\n      width: layout.width - signX * fixedLineWidth,\n      height: layout.height - signY * fixedLineWidth\n    };\n  },\n  polar: function (data, dataIndex, itemModel) {\n    var layout = data.getItemLayout(dataIndex);\n    return {\n      cx: layout.cx,\n      cy: layout.cy,\n      r0: layout.r0,\n      r: layout.r,\n      startAngle: layout.startAngle,\n      endAngle: layout.endAngle,\n      clockwise: layout.clockwise\n    };\n  }\n};\n\nfunction isZeroOnPolar(layout) {\n  return layout.startAngle != null && layout.endAngle != null && layout.startAngle === layout.endAngle;\n}\n\nfunction createPolarPositionMapping(isRadial) {\n  return function (isRadial) {\n    var arcOrAngle = isRadial ? 'Arc' : 'Angle';\n    return function (position) {\n      switch (position) {\n        case 'start':\n        case 'insideStart':\n        case 'end':\n        case 'insideEnd':\n          return position + arcOrAngle;\n\n        default:\n          return position;\n      }\n    };\n  }(isRadial);\n}\n\nfunction updateStyle(el, data, dataIndex, itemModel, layout, seriesModel, isHorizontalOrRadial, isPolar) {\n  var style = data.getItemVisual(dataIndex, 'style');\n\n  if (!isPolar) {\n    var borderRadius = itemModel.get(['itemStyle', 'borderRadius']) || 0;\n    el.setShape('r', borderRadius);\n  } else if (!seriesModel.get('roundCap')) {\n    var sectorShape = el.shape;\n    var cornerRadius = getSectorCornerRadius(itemModel.getModel('itemStyle'), sectorShape, true);\n    extend(sectorShape, cornerRadius);\n    el.setShape(sectorShape);\n  }\n\n  el.useStyle(style);\n  var cursorStyle = itemModel.getShallow('cursor');\n  cursorStyle && el.attr('cursor', cursorStyle);\n  var labelPositionOutside = isPolar ? isHorizontalOrRadial ? layout.r >= layout.r0 ? 'endArc' : 'startArc' : layout.endAngle >= layout.startAngle ? 'endAngle' : 'startAngle' : isHorizontalOrRadial ? layout.height >= 0 ? 'bottom' : 'top' : layout.width >= 0 ? 'right' : 'left';\n  var labelStatesModels = getLabelStatesModels(itemModel);\n  setLabelStyle(el, labelStatesModels, {\n    labelFetcher: seriesModel,\n    labelDataIndex: dataIndex,\n    defaultText: getDefaultLabel(seriesModel.getData(), dataIndex),\n    inheritColor: style.fill,\n    defaultOpacity: style.opacity,\n    defaultOutsidePosition: labelPositionOutside\n  });\n  var label = el.getTextContent();\n\n  if (isPolar && label) {\n    var position = itemModel.get(['label', 'position']);\n    el.textConfig.inside = position === 'middle' ? true : null;\n    setSectorTextRotation(el, position === 'outside' ? labelPositionOutside : position, createPolarPositionMapping(isHorizontalOrRadial), itemModel.get(['label', 'rotate']));\n  }\n\n  setLabelValueAnimation(label, labelStatesModels, seriesModel.getRawValue(dataIndex), function (value) {\n    return getDefaultInterpolatedLabel(data, value);\n  });\n  var emphasisModel = itemModel.getModel(['emphasis']);\n  toggleHoverEmphasis(el, emphasisModel.get('focus'), emphasisModel.get('blurScope'), emphasisModel.get('disabled'));\n  setStatesStylesFromModel(el, itemModel);\n\n  if (isZeroOnPolar(layout)) {\n    el.style.fill = 'none';\n    el.style.stroke = 'none';\n    each(el.states, function (state) {\n      if (state.style) {\n        state.style.fill = state.style.stroke = 'none';\n      }\n    });\n  }\n} // In case width or height are too small.\n\n\nfunction getLineWidth(itemModel, rawLayout) {\n  // Has no border.\n  var borderColor = itemModel.get(['itemStyle', 'borderColor']);\n\n  if (!borderColor || borderColor === 'none') {\n    return 0;\n  }\n\n  var lineWidth = itemModel.get(['itemStyle', 'borderWidth']) || 0; // width or height may be NaN for empty data\n\n  var width = isNaN(rawLayout.width) ? Number.MAX_VALUE : Math.abs(rawLayout.width);\n  var height = isNaN(rawLayout.height) ? Number.MAX_VALUE : Math.abs(rawLayout.height);\n  return Math.min(lineWidth, width, height);\n}\n\nvar LagePathShape =\n/** @class */\nfunction () {\n  function LagePathShape() {}\n\n  return LagePathShape;\n}();\n\nvar LargePath =\n/** @class */\nfunction (_super) {\n  __extends(LargePath, _super);\n\n  function LargePath(opts) {\n    var _this = _super.call(this, opts) || this;\n\n    _this.type = 'largeBar';\n    return _this;\n  }\n\n  LargePath.prototype.getDefaultShape = function () {\n    return new LagePathShape();\n  };\n\n  LargePath.prototype.buildPath = function (ctx, shape) {\n    // Drawing lines is more efficient than drawing\n    // a whole line or drawing rects.\n    var points = shape.points;\n    var baseDimIdx = this.baseDimIdx;\n    var valueDimIdx = 1 - this.baseDimIdx;\n    var startPoint = [];\n    var size = [];\n    var barWidth = this.barWidth;\n\n    for (var i = 0; i < points.length; i += 3) {\n      size[baseDimIdx] = barWidth;\n      size[valueDimIdx] = points[i + 2];\n      startPoint[baseDimIdx] = points[i + baseDimIdx];\n      startPoint[valueDimIdx] = points[i + valueDimIdx];\n      ctx.rect(startPoint[0], startPoint[1], size[0], size[1]);\n    }\n  };\n\n  return LargePath;\n}(Path);\n\nfunction createLarge(seriesModel, group, progressiveEls, incremental) {\n  // TODO support polar\n  var data = seriesModel.getData();\n  var baseDimIdx = data.getLayout('valueAxisHorizontal') ? 1 : 0;\n  var largeDataIndices = data.getLayout('largeDataIndices');\n  var barWidth = data.getLayout('size');\n  var backgroundModel = seriesModel.getModel('backgroundStyle');\n  var bgPoints = data.getLayout('largeBackgroundPoints');\n\n  if (bgPoints) {\n    var bgEl = new LargePath({\n      shape: {\n        points: bgPoints\n      },\n      incremental: !!incremental,\n      silent: true,\n      z2: 0\n    });\n    bgEl.baseDimIdx = baseDimIdx;\n    bgEl.largeDataIndices = largeDataIndices;\n    bgEl.barWidth = barWidth;\n    bgEl.useStyle(backgroundModel.getItemStyle());\n    group.add(bgEl);\n    progressiveEls && progressiveEls.push(bgEl);\n  }\n\n  var el = new LargePath({\n    shape: {\n      points: data.getLayout('largePoints')\n    },\n    incremental: !!incremental,\n    ignoreCoarsePointer: true,\n    z2: 1\n  });\n  el.baseDimIdx = baseDimIdx;\n  el.largeDataIndices = largeDataIndices;\n  el.barWidth = barWidth;\n  group.add(el);\n  el.useStyle(data.getVisual('style')); // Enable tooltip and user mouse/touch event handlers.\n\n  getECData(el).seriesIndex = seriesModel.seriesIndex;\n\n  if (!seriesModel.get('silent')) {\n    el.on('mousedown', largePathUpdateDataIndex);\n    el.on('mousemove', largePathUpdateDataIndex);\n  }\n\n  progressiveEls && progressiveEls.push(el);\n} // Use throttle to avoid frequently traverse to find dataIndex.\n\n\nvar largePathUpdateDataIndex = throttle(function (event) {\n  var largePath = this;\n  var dataIndex = largePathFindDataIndex(largePath, event.offsetX, event.offsetY);\n  getECData(largePath).dataIndex = dataIndex >= 0 ? dataIndex : null;\n}, 30, false);\n\nfunction largePathFindDataIndex(largePath, x, y) {\n  var baseDimIdx = largePath.baseDimIdx;\n  var valueDimIdx = 1 - baseDimIdx;\n  var points = largePath.shape.points;\n  var largeDataIndices = largePath.largeDataIndices;\n  var startPoint = [];\n  var size = [];\n  var barWidth = largePath.barWidth;\n\n  for (var i = 0, len = points.length / 3; i < len; i++) {\n    var ii = i * 3;\n    size[baseDimIdx] = barWidth;\n    size[valueDimIdx] = points[ii + 2];\n    startPoint[baseDimIdx] = points[ii + baseDimIdx];\n    startPoint[valueDimIdx] = points[ii + valueDimIdx];\n\n    if (size[valueDimIdx] < 0) {\n      startPoint[valueDimIdx] += size[valueDimIdx];\n      size[valueDimIdx] = -size[valueDimIdx];\n    }\n\n    if (x >= startPoint[0] && x <= startPoint[0] + size[0] && y >= startPoint[1] && y <= startPoint[1] + size[1]) {\n      return largeDataIndices[i];\n    }\n  }\n\n  return -1;\n}\n\nfunction createBackgroundShape(isHorizontalOrRadial, layout, coord) {\n  if (isCoordinateSystemType(coord, 'cartesian2d')) {\n    var rectShape = layout;\n    var coordLayout = coord.getArea();\n    return {\n      x: isHorizontalOrRadial ? rectShape.x : coordLayout.x,\n      y: isHorizontalOrRadial ? coordLayout.y : rectShape.y,\n      width: isHorizontalOrRadial ? rectShape.width : coordLayout.width,\n      height: isHorizontalOrRadial ? coordLayout.height : rectShape.height\n    };\n  } else {\n    var coordLayout = coord.getArea();\n    var sectorShape = layout;\n    return {\n      cx: coordLayout.cx,\n      cy: coordLayout.cy,\n      r0: isHorizontalOrRadial ? coordLayout.r0 : sectorShape.r0,\n      r: isHorizontalOrRadial ? coordLayout.r : sectorShape.r,\n      startAngle: isHorizontalOrRadial ? sectorShape.startAngle : 0,\n      endAngle: isHorizontalOrRadial ? sectorShape.endAngle : Math.PI * 2\n    };\n  }\n}\n\nfunction createBackgroundEl(coord, isHorizontalOrRadial, layout) {\n  var ElementClz = coord.type === 'polar' ? Sector : Rect;\n  return new ElementClz({\n    shape: createBackgroundShape(isHorizontalOrRadial, layout, coord),\n    silent: true,\n    z2: 0\n  });\n}\n\nexport default BarView;"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,SAAS,QAAQ,OAAO;AACjC,OAAOC,IAAI,MAAM,6BAA6B;AAC9C,OAAOC,KAAK,MAAM,8BAA8B;AAChD,SAASC,MAAM,EAAEC,IAAI,EAAEC,GAAG,QAAQ,0BAA0B;AAC5D,SAASC,IAAI,EAAEC,MAAM,EAAEC,WAAW,EAAEC,SAAS,EAAEC,wBAAwB,EAAEC,gBAAgB,QAAQ,uBAAuB;AACxH,SAASC,SAAS,QAAQ,0BAA0B;AACpD,SAASC,wBAAwB,EAAEC,mBAAmB,QAAQ,sBAAsB;AACpF,SAASC,aAAa,EAAEC,oBAAoB,EAAEC,sBAAsB,EAAEC,UAAU,QAAQ,2BAA2B;AACnH,SAASC,QAAQ,QAAQ,wBAAwB;AACjD,SAASC,cAAc,QAAQ,yCAAyC;AACxE,OAAOC,OAAO,MAAM,6BAA6B;AACjD,OAAOC,SAAS,MAAM,qBAAqB;AAC3C,SAASC,sBAAsB,QAAQ,iCAAiC;AACxE,SAASC,eAAe,EAAEC,2BAA2B,QAAQ,0BAA0B;AACvF,SAASC,IAAI,QAAQ,mBAAmB;AACxC,SAASC,iCAAiC,EAAEC,qBAAqB,QAAQ,4BAA4B;AACrG,SAASC,YAAY,QAAQ,oCAAoC;AACjE,SAASC,qBAAqB,QAAQ,2BAA2B;AACjE,IAAIC,OAAO,GAAGC,IAAI,CAACC,GAAG;AACtB,IAAIC,OAAO,GAAGF,IAAI,CAACG,GAAG;AAEtB,SAASC,WAAWA,CAACC,KAAK,EAAEC,IAAI,EAAE;EAChC,IAAIC,gBAAgB,GAAGF,KAAK,CAACG,OAAO,IAAIH,KAAK,CAACG,OAAO,CAAC,CAAC;EAEvD,IAAIjB,sBAAsB,CAACc,KAAK,EAAE,aAAa,CAAC,EAAE;IAChD,IAAII,QAAQ,GAAGJ,KAAK,CAACK,WAAW,CAAC,CAAC,CAAC,CAAC;IACpC;IACA;;IAEA,IAAID,QAAQ,CAACE,IAAI,KAAK,UAAU,IAAI,CAACF,QAAQ,CAACG,MAAM,EAAE;MACpD,IAAIC,WAAW,GAAGP,IAAI,CAACQ,SAAS,CAAC,WAAW,CAAC;MAE7C,IAAIL,QAAQ,CAACM,YAAY,CAAC,CAAC,EAAE;QAC3BR,gBAAgB,CAACS,CAAC,IAAIH,WAAW;QACjCN,gBAAgB,CAACU,KAAK,IAAIJ,WAAW,GAAG,CAAC;MAC3C,CAAC,MAAM;QACLN,gBAAgB,CAACW,CAAC,IAAIL,WAAW;QACjCN,gBAAgB,CAACY,MAAM,IAAIN,WAAW,GAAG,CAAC;MAC5C;IACF;EACF;EAEA,OAAON,gBAAgB;AACzB;AAEA,IAAIa,OAAO,GACX;AACA,UAAUC,MAAM,EAAE;EAChBrD,SAAS,CAACoD,OAAO,EAAEC,MAAM,CAAC;EAE1B,SAASD,OAAOA,CAAA,EAAG;IACjB,IAAIE,KAAK,GAAGD,MAAM,CAACE,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI;IAErCD,KAAK,CAACX,IAAI,GAAGS,OAAO,CAACT,IAAI;IACzBW,KAAK,CAACE,aAAa,GAAG,IAAI;IAC1B,OAAOF,KAAK;EACd;EAEAF,OAAO,CAACK,SAAS,CAACC,MAAM,GAAG,UAAUC,WAAW,EAAEC,OAAO,EAAEC,GAAG,EAAEC,OAAO,EAAE;IACvE,IAAI,CAACC,MAAM,GAAGJ,WAAW;IAEzB,IAAI,CAACK,yBAAyB,CAACH,GAAG,CAAC;IAEnC,IAAI,CAACI,eAAe,CAACN,WAAW,CAAC;IAEjC,IAAIO,oBAAoB,GAAGP,WAAW,CAACQ,GAAG,CAAC,kBAAkB,CAAC;IAE9D,IAAID,oBAAoB,KAAK,aAAa,IAAIA,oBAAoB,KAAK,OAAO,EAAE;MAC9E;MACA,IAAI,CAACE,eAAe,GAAG,IAAI;MAC3B,IAAI,CAACC,YAAY,GAAG,IAAI,CAACC,YAAY,CAACX,WAAW,EAAEC,OAAO,EAAEC,GAAG,CAAC,GAAG,IAAI,CAACU,aAAa,CAACZ,WAAW,EAAEC,OAAO,EAAEC,GAAG,EAAEC,OAAO,CAAC;IAC3H,CAAC,MAAM,IAAIU,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;MAChDhD,IAAI,CAAC,+CAA+C,CAAC;IACvD;EACF,CAAC;EAED0B,OAAO,CAACK,SAAS,CAACkB,wBAAwB,GAAG,UAAUhB,WAAW,EAAE;IAClE,IAAI,CAACiB,MAAM,CAAC,CAAC;IAEb,IAAI,CAACX,eAAe,CAACN,WAAW,CAAC,CAAC,CAAC;IACnC;;IAGA,IAAI,CAACkB,gBAAgB,CAAClB,WAAW,CAAC;EACpC,CAAC;EAEDP,OAAO,CAACK,SAAS,CAACqB,iBAAiB,GAAG,UAAUC,MAAM,EAAEpB,WAAW,EAAE;IACnE;IACA,IAAI,CAACS,eAAe,GAAG,EAAE,CAAC,CAAC;;IAE3B,IAAI,CAACY,uBAAuB,CAACD,MAAM,EAAEpB,WAAW,CAAC;EACnD,CAAC;EAEDP,OAAO,CAACK,SAAS,CAACwB,YAAY,GAAG,UAAUC,EAAE,EAAE;IAC7CvE,gBAAgB,CAAC,IAAI,CAACyD,eAAe,IAAI,IAAI,CAACe,KAAK,EAAED,EAAE,CAAC;EAC1D,CAAC;EAED9B,OAAO,CAACK,SAAS,CAACQ,eAAe,GAAG,UAAUN,WAAW,EAAE;IACzD,IAAIyB,WAAW,GAAGzB,WAAW,CAAC0B,eAAe,CAACC,KAAK;IAEnD,IAAI,IAAI,CAACjB,YAAY,IAAI,IAAI,IAAIe,WAAW,KAAK,IAAI,CAACf,YAAY,EAAE;MAClE,IAAI,CAACA,YAAY,GAAGe,WAAW;MAE/B,IAAI,CAACR,MAAM,CAAC,CAAC;IACf;EACF,CAAC;EAEDxB,OAAO,CAACK,SAAS,CAACc,aAAa,GAAG,UAAUZ,WAAW,EAAEC,OAAO,EAAEC,GAAG,EAAEC,OAAO,EAAE;IAC9E,IAAIqB,KAAK,GAAG,IAAI,CAACA,KAAK;IACtB,IAAI7C,IAAI,GAAGqB,WAAW,CAAC4B,OAAO,CAAC,CAAC;IAChC,IAAIC,OAAO,GAAG,IAAI,CAACC,KAAK;IACxB,IAAIpD,KAAK,GAAGsB,WAAW,CAAC+B,gBAAgB;IACxC,IAAIjD,QAAQ,GAAGJ,KAAK,CAACK,WAAW,CAAC,CAAC;IAClC,IAAIiD,oBAAoB;IAExB,IAAItD,KAAK,CAACM,IAAI,KAAK,aAAa,EAAE;MAChCgD,oBAAoB,GAAGlD,QAAQ,CAACM,YAAY,CAAC,CAAC;IAChD,CAAC,MAAM,IAAIV,KAAK,CAACM,IAAI,KAAK,OAAO,EAAE;MACjCgD,oBAAoB,GAAGlD,QAAQ,CAACmD,GAAG,KAAK,OAAO;IACjD;IAEA,IAAIC,cAAc,GAAGlC,WAAW,CAACmC,kBAAkB,CAAC,CAAC,GAAGnC,WAAW,GAAG,IAAI;IAC1E,IAAIoC,eAAe,GAAGC,kBAAkB,CAACrC,WAAW,EAAEtB,KAAK,CAAC;IAE5D,IAAI0D,eAAe,EAAE;MACnB,IAAI,CAACE,mBAAmB,CAACF,eAAe,EAAEzD,IAAI,EAAEuB,GAAG,CAAC;IACtD;IAEA,IAAIqC,SAAS,GAAGvC,WAAW,CAACQ,GAAG,CAAC,MAAM,EAAE,IAAI,CAAC,IAAI4B,eAAe;IAChE,IAAIxD,gBAAgB,GAAGH,WAAW,CAACC,KAAK,EAAEC,IAAI,CAAC,CAAC,CAAC;;IAEjD6C,KAAK,CAACgB,cAAc,CAAC,CAAC,CAAC,CAAC;IACxB;;IAEA,IAAIC,QAAQ,GAAGzC,WAAW,CAACQ,GAAG,CAAC,UAAU,EAAE,IAAI,CAAC;IAChD,IAAIkC,cAAc,GAAG1C,WAAW,CAACQ,GAAG,CAAC,gBAAgB,EAAE,IAAI,CAAC;IAC5D,IAAImC,eAAe,GAAG3C,WAAW,CAAC4C,QAAQ,CAAC,iBAAiB,CAAC;IAC7D,IAAIC,eAAe,GAAGF,eAAe,CAACnC,GAAG,CAAC,cAAc,CAAC,IAAI,CAAC;IAC9D,IAAIsC,KAAK,GAAG,EAAE;IACd,IAAIC,QAAQ,GAAG,IAAI,CAACC,cAAc;IAClC,IAAIC,UAAU,GAAG9C,OAAO,IAAIA,OAAO,CAAC8C,UAAU;IAC9C,IAAIC,aAAa,GAAG/C,OAAO,IAAIA,OAAO,CAACnB,IAAI,KAAK,iBAAiB;IAEjE,SAASmE,gBAAgBA,CAACC,SAAS,EAAE;MACnC,IAAIC,QAAQ,GAAGlE,SAAS,CAACT,KAAK,CAACM,IAAI,CAAC,CAACL,IAAI,EAAEyE,SAAS,CAAC;MACrD,IAAIE,IAAI,GAAGC,kBAAkB,CAAC7E,KAAK,EAAEsD,oBAAoB,EAAEqB,QAAQ,CAAC;MACpEC,IAAI,CAACE,QAAQ,CAACb,eAAe,CAACc,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC;;MAE/C,IAAI/E,KAAK,CAACM,IAAI,KAAK,aAAa,EAAE;QAChCsE,IAAI,CAACI,QAAQ,CAAC,GAAG,EAAEb,eAAe,CAAC;MACrC,CAAC,MAAM;QACLS,IAAI,CAACI,QAAQ,CAAC,cAAc,EAAEb,eAAe,CAAC;MAChD;MAEAC,KAAK,CAACM,SAAS,CAAC,GAAGE,IAAI;MACvB,OAAOA,IAAI;IACb;IAEA;IACA3E,IAAI,CAACgF,IAAI,CAAC9B,OAAO,CAAC,CAAC+B,GAAG,CAAC,UAAUR,SAAS,EAAE;MAC1C,IAAIS,SAAS,GAAGlF,IAAI,CAACmF,YAAY,CAACV,SAAS,CAAC;MAC5C,IAAIW,MAAM,GAAG5E,SAAS,CAACT,KAAK,CAACM,IAAI,CAAC,CAACL,IAAI,EAAEyE,SAAS,EAAES,SAAS,CAAC;MAE9D,IAAInB,cAAc,EAAE;QAClBS,gBAAgB,CAACC,SAAS,CAAC;MAC7B,CAAC,CAAC;;MAGF,IAAI,CAACzE,IAAI,CAACqF,QAAQ,CAACZ,SAAS,CAAC,IAAI,CAACa,aAAa,CAACvF,KAAK,CAACM,IAAI,CAAC,CAAC+E,MAAM,CAAC,EAAE;QACnE;MACF;MAEA,IAAIG,SAAS,GAAG,KAAK;MAErB,IAAI3B,SAAS,EAAE;QACb;QACA;QACA2B,SAAS,GAAGC,IAAI,CAACzF,KAAK,CAACM,IAAI,CAAC,CAACJ,gBAAgB,EAAEmF,MAAM,CAAC;MACxD;MAEA,IAAIK,EAAE,GAAGC,cAAc,CAAC3F,KAAK,CAACM,IAAI,CAAC,CAACgB,WAAW,EAAErB,IAAI,EAAEyE,SAAS,EAAEW,MAAM,EAAE/B,oBAAoB,EAAEE,cAAc,EAAEpD,QAAQ,CAACwF,KAAK,EAAE,KAAK,EAAE7B,QAAQ,CAAC;MAEhJ,IAAIL,eAAe,EAAE;QACnB;AACR;AACA;AACA;AACA;AACA;AACA;AACA;QACQgC,EAAE,CAACG,mBAAmB,GAAG,IAAI;MAC/B;MAEAC,WAAW,CAACJ,EAAE,EAAEzF,IAAI,EAAEyE,SAAS,EAAES,SAAS,EAAEE,MAAM,EAAE/D,WAAW,EAAEgC,oBAAoB,EAAEtD,KAAK,CAACM,IAAI,KAAK,OAAO,CAAC;MAE9G,IAAIiE,UAAU,EAAE;QACdmB,EAAE,CAACK,IAAI,CAAC;UACNC,KAAK,EAAEX;QACT,CAAC,CAAC;MACJ,CAAC,MAAM,IAAI3B,eAAe,EAAE;QAC1BuC,uBAAuB,CAACvC,eAAe,EAAEF,cAAc,EAAEkC,EAAE,EAAEL,MAAM,EAAEX,SAAS,EAAEpB,oBAAoB,EAAE,KAAK,EAAE,KAAK,CAAC;MACrH,CAAC,MAAM;QACLlF,SAAS,CAACsH,EAAE,EAAE;UACZM,KAAK,EAAEX;QACT,CAAC,EAAE/D,WAAW,EAAEoD,SAAS,CAAC;MAC5B;MAEAzE,IAAI,CAACiG,gBAAgB,CAACxB,SAAS,EAAEgB,EAAE,CAAC;MACpC5C,KAAK,CAACoC,GAAG,CAACQ,EAAE,CAAC;MACbA,EAAE,CAACS,MAAM,GAAGX,SAAS;IACvB,CAAC,CAAC,CAACY,MAAM,CAAC,UAAUC,QAAQ,EAAEC,QAAQ,EAAE;MACtC,IAAInB,SAAS,GAAGlF,IAAI,CAACmF,YAAY,CAACiB,QAAQ,CAAC;MAC3C,IAAIhB,MAAM,GAAG5E,SAAS,CAACT,KAAK,CAACM,IAAI,CAAC,CAACL,IAAI,EAAEoG,QAAQ,EAAElB,SAAS,CAAC;MAE7D,IAAInB,cAAc,EAAE;QAClB,IAAIY,IAAI,GAAG,KAAK,CAAC;QAEjB,IAAIP,QAAQ,CAACkC,MAAM,KAAK,CAAC,EAAE;UACzB3B,IAAI,GAAGH,gBAAgB,CAAC6B,QAAQ,CAAC;QACnC,CAAC,MAAM;UACL1B,IAAI,GAAGP,QAAQ,CAACiC,QAAQ,CAAC;UACzB1B,IAAI,CAACE,QAAQ,CAACb,eAAe,CAACc,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC;;UAE/C,IAAI/E,KAAK,CAACM,IAAI,KAAK,aAAa,EAAE;YAChCsE,IAAI,CAACI,QAAQ,CAAC,GAAG,EAAEb,eAAe,CAAC;UACrC,CAAC,MAAM;YACLS,IAAI,CAACI,QAAQ,CAAC,cAAc,EAAEb,eAAe,CAAC;UAChD;UAEAC,KAAK,CAACiC,QAAQ,CAAC,GAAGzB,IAAI;QACxB;QAEA,IAAID,QAAQ,GAAGlE,SAAS,CAACT,KAAK,CAACM,IAAI,CAAC,CAACL,IAAI,EAAEoG,QAAQ,CAAC;QACpD,IAAIL,KAAK,GAAGQ,qBAAqB,CAAClD,oBAAoB,EAAEqB,QAAQ,EAAE3E,KAAK,CAAC;QACxE7B,WAAW,CAACyG,IAAI,EAAE;UAChBoB,KAAK,EAAEA;QACT,CAAC,EAAExC,cAAc,EAAE6C,QAAQ,CAAC;MAC9B;MAEA,IAAIX,EAAE,GAAGvC,OAAO,CAACsD,gBAAgB,CAACH,QAAQ,CAAC;MAE3C,IAAI,CAACrG,IAAI,CAACqF,QAAQ,CAACe,QAAQ,CAAC,IAAI,CAACd,aAAa,CAACvF,KAAK,CAACM,IAAI,CAAC,CAAC+E,MAAM,CAAC,EAAE;QAClEvC,KAAK,CAAC4D,MAAM,CAAChB,EAAE,CAAC;QAChB;MACF;MAEA,IAAIF,SAAS,GAAG,KAAK;MAErB,IAAI3B,SAAS,EAAE;QACb2B,SAAS,GAAGC,IAAI,CAACzF,KAAK,CAACM,IAAI,CAAC,CAACJ,gBAAgB,EAAEmF,MAAM,CAAC;QAEtD,IAAIG,SAAS,EAAE;UACb1C,KAAK,CAAC4D,MAAM,CAAChB,EAAE,CAAC;QAClB;MACF;MAEA,IAAI,CAACA,EAAE,EAAE;QACPA,EAAE,GAAGC,cAAc,CAAC3F,KAAK,CAACM,IAAI,CAAC,CAACgB,WAAW,EAAErB,IAAI,EAAEoG,QAAQ,EAAEhB,MAAM,EAAE/B,oBAAoB,EAAEE,cAAc,EAAEpD,QAAQ,CAACwF,KAAK,EAAE,CAAC,CAACF,EAAE,EAAE3B,QAAQ,CAAC;MAC5I,CAAC,MAAM;QACLvE,YAAY,CAACkG,EAAE,CAAC;MAClB;MAEA,IAAIhC,eAAe,EAAE;QACnBgC,EAAE,CAACG,mBAAmB,GAAG,IAAI;MAC/B;MAEA,IAAIrB,aAAa,EAAE;QACjB,IAAImC,MAAM,GAAGjB,EAAE,CAACkB,cAAc,CAAC,CAAC;QAEhC,IAAID,MAAM,EAAE;UACV,IAAIE,eAAe,GAAGhI,UAAU,CAAC8H,MAAM,CAAC;UAExC,IAAIE,eAAe,CAACC,SAAS,IAAI,IAAI,EAAE;YACrC;AACZ;AACA;AACA;AACA;AACA;YACYD,eAAe,CAACC,SAAS,GAAGD,eAAe,CAACE,KAAK;UACnD;QACF;MACF,CAAC,CAAC;MACF;MAAA,KACK;QACDjB,WAAW,CAACJ,EAAE,EAAEzF,IAAI,EAAEoG,QAAQ,EAAElB,SAAS,EAAEE,MAAM,EAAE/D,WAAW,EAAEgC,oBAAoB,EAAEtD,KAAK,CAACM,IAAI,KAAK,OAAO,CAAC;MAC/G;MAEF,IAAIiE,UAAU,EAAE;QACdmB,EAAE,CAACK,IAAI,CAAC;UACNC,KAAK,EAAEX;QACT,CAAC,CAAC;MACJ,CAAC,MAAM,IAAI3B,eAAe,EAAE;QAC1BuC,uBAAuB,CAACvC,eAAe,EAAEF,cAAc,EAAEkC,EAAE,EAAEL,MAAM,EAAEgB,QAAQ,EAAE/C,oBAAoB,EAAE,IAAI,EAAEkB,aAAa,CAAC;MAC3H,CAAC,MAAM;QACLrG,WAAW,CAACuH,EAAE,EAAE;UACdM,KAAK,EAAEX;QACT,CAAC,EAAE/D,WAAW,EAAE+E,QAAQ,EAAE,IAAI,CAAC;MACjC;MAEApG,IAAI,CAACiG,gBAAgB,CAACG,QAAQ,EAAEX,EAAE,CAAC;MACnCA,EAAE,CAACS,MAAM,GAAGX,SAAS;MACrB1C,KAAK,CAACoC,GAAG,CAACQ,EAAE,CAAC;IACf,CAAC,CAAC,CAACgB,MAAM,CAAC,UAAUhC,SAAS,EAAE;MAC7B,IAAIgB,EAAE,GAAGvC,OAAO,CAACsD,gBAAgB,CAAC/B,SAAS,CAAC;MAC5CgB,EAAE,IAAIrH,wBAAwB,CAACqH,EAAE,EAAEpE,WAAW,EAAEoD,SAAS,CAAC;IAC5D,CAAC,CAAC,CAACsC,OAAO,CAAC,CAAC;IACZ,IAAIC,OAAO,GAAG,IAAI,CAACC,gBAAgB,KAAK,IAAI,CAACA,gBAAgB,GAAG,IAAIrJ,KAAK,CAAC,CAAC,CAAC;IAC5EoJ,OAAO,CAACE,SAAS,CAAC,CAAC;IAEnB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGhD,KAAK,CAACmC,MAAM,EAAE,EAAEa,CAAC,EAAE;MACrCH,OAAO,CAAC/B,GAAG,CAACd,KAAK,CAACgD,CAAC,CAAC,CAAC;IACvB;IAEAtE,KAAK,CAACoC,GAAG,CAAC+B,OAAO,CAAC;IAClB,IAAI,CAAC3C,cAAc,GAAGF,KAAK;IAC3B,IAAI,CAAChB,KAAK,GAAGnD,IAAI;EACnB,CAAC;EAEDc,OAAO,CAACK,SAAS,CAACa,YAAY,GAAG,UAAUX,WAAW,EAAEC,OAAO,EAAEC,GAAG,EAAE;IACpE,IAAI,CAACe,MAAM,CAAC,CAAC;IAEb8E,WAAW,CAAC/F,WAAW,EAAE,IAAI,CAACwB,KAAK,CAAC;IAEpC,IAAI,CAACN,gBAAgB,CAAClB,WAAW,CAAC;EACpC,CAAC;EAEDP,OAAO,CAACK,SAAS,CAACuB,uBAAuB,GAAG,UAAUD,MAAM,EAAEpB,WAAW,EAAE;IACzE,IAAI,CAACgG,iBAAiB,CAAC,CAAC;IAExBD,WAAW,CAAC/F,WAAW,EAAE,IAAI,CAACwB,KAAK,EAAE,IAAI,CAACf,eAAe,EAAE,IAAI,CAAC;EAClE,CAAC;EAEDhB,OAAO,CAACK,SAAS,CAACoB,gBAAgB,GAAG,UAAUlB,WAAW,EAAE;IAC1D;IACA,IAAIiG,QAAQ,GAAGjG,WAAW,CAACQ,GAAG,CAAC,MAAM,EAAE,IAAI,CAAC,IAAI/C,cAAc,CAACuC,WAAW,CAAC+B,gBAAgB,EAAE,KAAK,EAAE/B,WAAW,CAAC;IAChH,IAAIwB,KAAK,GAAG,IAAI,CAACA,KAAK;IAEtB,IAAIyE,QAAQ,EAAE;MACZzE,KAAK,CAAC0E,WAAW,CAACD,QAAQ,CAAC;IAC7B,CAAC,MAAM;MACLzE,KAAK,CAACgB,cAAc,CAAC,CAAC;IACxB;EACF,CAAC;EAED/C,OAAO,CAACK,SAAS,CAACwC,mBAAmB,GAAG,UAAUF,eAAe,EAAEzD,IAAI,EAAEuB,GAAG,EAAE;IAC5E,IAAIP,KAAK,GAAG,IAAI,CAAC,CAAC;;IAGlB,IAAI,CAAChB,IAAI,CAACwH,KAAK,CAAC,CAAC,EAAE;MACjB;IACF;IAEA,IAAIrH,QAAQ,GAAGsD,eAAe,CAACtD,QAAQ;IAEvC,IAAI,IAAI,CAACe,aAAa,EAAE;MACtB,IAAI,CAACuG,iBAAiB,CAACzH,IAAI,EAAEyD,eAAe,EAAElC,GAAG,CAAC;MAElD,IAAI,CAACL,aAAa,GAAG,KAAK;IAC5B,CAAC,MAAM;MACL,IAAIwG,cAAc,GAAG,SAAAA,CAAUC,GAAG,EAAE;QAClC,IAAIlC,EAAE,GAAGzF,IAAI,CAACwG,gBAAgB,CAACmB,GAAG,CAAC;QACnC,IAAI5B,KAAK,GAAGN,EAAE,IAAIA,EAAE,CAACM,KAAK;QAC1B,OAAOA,KAAK;QAAI;QAChB;QACArG,IAAI,CAACkI,GAAG,CAACzH,QAAQ,CAACM,YAAY,CAAC,CAAC,GAAGsF,KAAK,CAAClF,MAAM,GAAGkF,KAAK,CAACpF,KAAK,CAAC,CAAC;QAAA,GAC5D,CAAC;MACN,CAAC;MAED,IAAI,CAACkH,WAAW,GAAG,YAAY;QAC7B7G,KAAK,CAAC8G,yBAAyB,CAAC9H,IAAI,EAAE0H,cAAc,EAAEvH,QAAQ,EAAEoB,GAAG,CAAC;MACtE,CAAC;MAEDA,GAAG,CAACwG,KAAK,CAAC,CAAC,CAACC,EAAE,CAAC,UAAU,EAAE,IAAI,CAACH,WAAW,CAAC;IAC9C;EACF,CAAC;EAED/G,OAAO,CAACK,SAAS,CAAC8G,SAAS,GAAG,UAAUjI,IAAI,EAAEG,QAAQ,EAAE+H,YAAY,EAAE;IACpE,IAAIC,IAAI,GAAG,EAAE;IACbnI,IAAI,CAAClC,IAAI,CAACkC,IAAI,CAACoI,YAAY,CAACjI,QAAQ,CAACmD,GAAG,CAAC,EAAE,UAAU+E,aAAa,EAAEC,OAAO,EAAE;MAC3E,IAAIC,WAAW,GAAGL,YAAY,CAACI,OAAO,CAAC;MACvCC,WAAW,GAAGA,WAAW,IAAI,IAAI,GAAGC,GAAG,GAAGD,WAAW;MACrDJ,IAAI,CAACM,IAAI,CAAC;QACRhE,SAAS,EAAE6D,OAAO;QAClBC,WAAW,EAAEA,WAAW;QACxBF,aAAa,EAAEA;MACjB,CAAC,CAAC;IACJ,CAAC,CAAC;IACFF,IAAI,CAACO,IAAI,CAAC,UAAUC,CAAC,EAAEC,CAAC,EAAE;MACxB;MACA,OAAOA,CAAC,CAACL,WAAW,GAAGI,CAAC,CAACJ,WAAW;IACtC,CAAC,CAAC;IACF,OAAO;MACLM,cAAc,EAAE9K,GAAG,CAACoK,IAAI,EAAE,UAAUW,IAAI,EAAE;QACxC,OAAOA,IAAI,CAACT,aAAa;MAC3B,CAAC;IACH,CAAC;EACH,CAAC;EAEDvH,OAAO,CAACK,SAAS,CAAC4H,6BAA6B,GAAG,UAAU/I,IAAI,EAAEkI,YAAY,EAAE/H,QAAQ,EAAE;IACxF,IAAI6I,KAAK,GAAG7I,QAAQ,CAAC6I,KAAK;IAC1B,IAAIC,cAAc,GAAGjJ,IAAI,CAACoI,YAAY,CAACjI,QAAQ,CAACmD,GAAG,CAAC;IACpD,IAAI4F,SAAS,GAAGC,MAAM,CAACC,SAAS;IAEhC,KAAK,IAAIC,OAAO,GAAG,CAAC,EAAEC,GAAG,GAAGN,KAAK,CAACO,cAAc,CAAC,CAAC,CAACC,UAAU,CAAClD,MAAM,EAAE+C,OAAO,GAAGC,GAAG,EAAE,EAAED,OAAO,EAAE;MAC9F,IAAII,MAAM,GAAGzJ,IAAI,CAAC0J,UAAU,CAACT,cAAc,EAAED,KAAK,CAACW,mBAAmB,CAACN,OAAO,CAAC,CAAC;MAChF,IAAIvC,KAAK,GAAG2C,MAAM,GAAG,CAAC,CAAC;MAAA,EACrBN,MAAM,CAACS,SAAS,CAAC;MAAA,EACjB1B,YAAY,CAAClI,IAAI,CAAC6J,eAAe,CAACJ,MAAM,CAAC,CAAC;MAE5C,IAAI3C,KAAK,GAAGoC,SAAS,EAAE;QACrB,OAAO,IAAI;MACb;MAEAA,SAAS,GAAGpC,KAAK;IACnB;IAEA,OAAO,KAAK;EACd,CAAC;EACD;AACF;AACA;AACA;AACA;;EAGEhG,OAAO,CAACK,SAAS,CAAC2I,uBAAuB,GAAG,UAAUC,SAAS,EAAE5J,QAAQ,EAAE;IACzE,IAAI6I,KAAK,GAAG7I,QAAQ,CAAC6I,KAAK;IAC1B,IAAIgB,MAAM,GAAGhB,KAAK,CAACiB,SAAS,CAAC,CAAC;IAC9B,IAAIZ,OAAO,GAAG3J,IAAI,CAACC,GAAG,CAAC,CAAC,EAAEqK,MAAM,CAAC,CAAC,CAAC,CAAC;IACpC,IAAIE,OAAO,GAAGxK,IAAI,CAACG,GAAG,CAACmK,MAAM,CAAC,CAAC,CAAC,EAAEhB,KAAK,CAACO,cAAc,CAAC,CAAC,CAACC,UAAU,CAAClD,MAAM,GAAG,CAAC,CAAC;IAE/E,OAAO+C,OAAO,IAAIa,OAAO,EAAE,EAAEb,OAAO,EAAE;MACpC,IAAIU,SAAS,CAAClB,cAAc,CAACQ,OAAO,CAAC,KAAKL,KAAK,CAACW,mBAAmB,CAACN,OAAO,CAAC,EAAE;QAC5E,OAAO,IAAI;MACb;IACF;EACF,CAAC;EAEDvI,OAAO,CAACK,SAAS,CAAC2G,yBAAyB,GAAG,UAAU9H,IAAI,EAAEkI,YAAY,EAAE/H,QAAQ,EAAEoB,GAAG,EAAE;IACzF,IAAI,CAAC,IAAI,CAACwH,6BAA6B,CAAC/I,IAAI,EAAEkI,YAAY,EAAE/H,QAAQ,CAAC,EAAE;MACrE;IACF;IAEA,IAAIgK,QAAQ,GAAG,IAAI,CAAClC,SAAS,CAACjI,IAAI,EAAEG,QAAQ,EAAE+H,YAAY,CAAC;IAE3D,IAAI,IAAI,CAAC4B,uBAAuB,CAACK,QAAQ,EAAEhK,QAAQ,CAAC,EAAE;MACpD,IAAI,CAACuB,yBAAyB,CAACH,GAAG,CAAC;MAEnCA,GAAG,CAAC6I,cAAc,CAAC;QACjB/J,IAAI,EAAE,iBAAiB;QACvBgK,aAAa,EAAElK,QAAQ,CAACmD,GAAG,GAAG,MAAM;QACpCgH,MAAM,EAAEnK,QAAQ,CAACoK,KAAK;QACtBJ,QAAQ,EAAEA;MACZ,CAAC,CAAC;IACJ;EACF,CAAC;EAEDrJ,OAAO,CAACK,SAAS,CAACsG,iBAAiB,GAAG,UAAUzH,IAAI,EAAEyD,eAAe,EAAElC,GAAG,EAAE;IAC1E,IAAIpB,QAAQ,GAAGsD,eAAe,CAACtD,QAAQ;IAEvC,IAAIqK,UAAU,GAAG,IAAI,CAACvC,SAAS,CAACjI,IAAI,EAAEG,QAAQ,EAAE,UAAUmI,OAAO,EAAE;MACjE,OAAOtI,IAAI,CAAC6B,GAAG,CAAC7B,IAAI,CAACoI,YAAY,CAAC3E,eAAe,CAACgH,SAAS,CAACnH,GAAG,CAAC,EAAEgF,OAAO,CAAC;IAC5E,CAAC,CAAC;IAEF/G,GAAG,CAAC6I,cAAc,CAAC;MACjB/J,IAAI,EAAE,iBAAiB;MACvBgK,aAAa,EAAElK,QAAQ,CAACmD,GAAG,GAAG,MAAM;MACpCgB,UAAU,EAAE,IAAI;MAChBgG,MAAM,EAAEnK,QAAQ,CAACoK,KAAK;MACtBJ,QAAQ,EAAEK;IACZ,CAAC,CAAC;EACJ,CAAC;EAED1J,OAAO,CAACK,SAAS,CAACsF,MAAM,GAAG,UAAUnF,OAAO,EAAEC,GAAG,EAAE;IACjD,IAAI,CAACe,MAAM,CAAC,IAAI,CAACb,MAAM,CAAC;IAExB,IAAI,CAACC,yBAAyB,CAACH,GAAG,CAAC;EACrC,CAAC;EAEDT,OAAO,CAACK,SAAS,CAACuJ,OAAO,GAAG,UAAUpJ,OAAO,EAAEC,GAAG,EAAE;IAClD,IAAI,CAACG,yBAAyB,CAACH,GAAG,CAAC;EACrC,CAAC;EAEDT,OAAO,CAACK,SAAS,CAACO,yBAAyB,GAAG,UAAUH,GAAG,EAAE;IAC3D,IAAI,IAAI,CAACsG,WAAW,EAAE;MACpBtG,GAAG,CAACwG,KAAK,CAAC,CAAC,CAAC4C,GAAG,CAAC,UAAU,EAAE,IAAI,CAAC9C,WAAW,CAAC;MAC7C,IAAI,CAACA,WAAW,GAAG,IAAI;IACzB;EACF,CAAC;EAED/G,OAAO,CAACK,SAAS,CAACmB,MAAM,GAAG,UAAUqD,KAAK,EAAE;IAC1C,IAAI9C,KAAK,GAAG,IAAI,CAACA,KAAK;IACtB,IAAI7C,IAAI,GAAG,IAAI,CAACmD,KAAK;IAErB,IAAIwC,KAAK,IAAIA,KAAK,CAACnC,kBAAkB,CAAC,CAAC,IAAIxD,IAAI,IAAI,CAAC,IAAI,CAAC+B,YAAY,EAAE;MACrE,IAAI,CAACsF,iBAAiB,CAAC,CAAC;MAExB,IAAI,CAAChD,cAAc,GAAG,EAAE;MACxBrE,IAAI,CAAC4K,iBAAiB,CAAC,UAAUnF,EAAE,EAAE;QACnCrH,wBAAwB,CAACqH,EAAE,EAAEE,KAAK,EAAErH,SAAS,CAACmH,EAAE,CAAC,CAAChB,SAAS,CAAC;MAC9D,CAAC,CAAC;IACJ,CAAC,MAAM;MACL5B,KAAK,CAACqE,SAAS,CAAC,CAAC;IACnB;IAEA,IAAI,CAAC/D,KAAK,GAAG,IAAI;IACjB,IAAI,CAACjC,aAAa,GAAG,IAAI;EAC3B,CAAC;EAEDJ,OAAO,CAACK,SAAS,CAACkG,iBAAiB,GAAG,YAAY;IAChD,IAAI,CAACxE,KAAK,CAAC4D,MAAM,CAAC,IAAI,CAACQ,gBAAgB,CAAC;IACxC,IAAI,CAACA,gBAAgB,GAAG,IAAI;EAC9B,CAAC;EAEDnG,OAAO,CAACT,IAAI,GAAG,KAAK;EACpB,OAAOS,OAAO;AAChB,CAAC,CAAC9B,SAAS,CAAC;AAEZ,IAAIwG,IAAI,GAAG;EACTqF,WAAW,EAAE,SAAAA,CAAUC,oBAAoB,EAAE1F,MAAM,EAAE;IACnD,IAAI2F,SAAS,GAAG3F,MAAM,CAACzE,KAAK,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC;IACzC,IAAIqK,UAAU,GAAG5F,MAAM,CAACvE,MAAM,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;;IAE7C,IAAIkK,SAAS,GAAG,CAAC,EAAE;MACjB3F,MAAM,CAAC1E,CAAC,IAAI0E,MAAM,CAACzE,KAAK;MACxByE,MAAM,CAACzE,KAAK,GAAG,CAACyE,MAAM,CAACzE,KAAK;IAC9B;IAEA,IAAIqK,UAAU,GAAG,CAAC,EAAE;MAClB5F,MAAM,CAACxE,CAAC,IAAIwE,MAAM,CAACvE,MAAM;MACzBuE,MAAM,CAACvE,MAAM,GAAG,CAACuE,MAAM,CAACvE,MAAM;IAChC;IAEA,IAAIoK,UAAU,GAAGH,oBAAoB,CAACpK,CAAC,GAAGoK,oBAAoB,CAACnK,KAAK;IACpE,IAAIuK,UAAU,GAAGJ,oBAAoB,CAAClK,CAAC,GAAGkK,oBAAoB,CAACjK,MAAM;IACrE,IAAIH,CAAC,GAAGjB,OAAO,CAAC2F,MAAM,CAAC1E,CAAC,EAAEoK,oBAAoB,CAACpK,CAAC,CAAC;IACjD,IAAIyK,EAAE,GAAGvL,OAAO,CAACwF,MAAM,CAAC1E,CAAC,GAAG0E,MAAM,CAACzE,KAAK,EAAEsK,UAAU,CAAC;IACrD,IAAIrK,CAAC,GAAGnB,OAAO,CAAC2F,MAAM,CAACxE,CAAC,EAAEkK,oBAAoB,CAAClK,CAAC,CAAC;IACjD,IAAIwK,EAAE,GAAGxL,OAAO,CAACwF,MAAM,CAACxE,CAAC,GAAGwE,MAAM,CAACvE,MAAM,EAAEqK,UAAU,CAAC;IACtD,IAAIG,QAAQ,GAAGF,EAAE,GAAGzK,CAAC;IACrB,IAAI4K,QAAQ,GAAGF,EAAE,GAAGxK,CAAC,CAAC,CAAC;IACvB;IACA;IACA;;IAEAwE,MAAM,CAAC1E,CAAC,GAAG2K,QAAQ,IAAI3K,CAAC,GAAGuK,UAAU,GAAGE,EAAE,GAAGzK,CAAC;IAC9C0E,MAAM,CAACxE,CAAC,GAAG0K,QAAQ,IAAI1K,CAAC,GAAGsK,UAAU,GAAGE,EAAE,GAAGxK,CAAC;IAC9CwE,MAAM,CAACzE,KAAK,GAAG0K,QAAQ,GAAG,CAAC,GAAGF,EAAE,GAAGzK,CAAC;IACpC0E,MAAM,CAACvE,MAAM,GAAGyK,QAAQ,GAAG,CAAC,GAAGF,EAAE,GAAGxK,CAAC,CAAC,CAAC;;IAEvC,IAAImK,SAAS,GAAG,CAAC,EAAE;MACjB3F,MAAM,CAAC1E,CAAC,IAAI0E,MAAM,CAACzE,KAAK;MACxByE,MAAM,CAACzE,KAAK,GAAG,CAACyE,MAAM,CAACzE,KAAK;IAC9B;IAEA,IAAIqK,UAAU,GAAG,CAAC,EAAE;MAClB5F,MAAM,CAACxE,CAAC,IAAIwE,MAAM,CAACvE,MAAM;MACzBuE,MAAM,CAACvE,MAAM,GAAG,CAACuE,MAAM,CAACvE,MAAM;IAChC;IAEA,OAAOwK,QAAQ,IAAIC,QAAQ;EAC7B,CAAC;EACDC,KAAK,EAAE,SAAAA,CAAUtL,gBAAgB,EAAEmF,MAAM,EAAE;IACzC,IAAIoG,KAAK,GAAGpG,MAAM,CAACqG,EAAE,IAAIrG,MAAM,CAACsG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;;IAE5C,IAAIF,KAAK,GAAG,CAAC,EAAE;MACb,IAAIG,GAAG,GAAGvG,MAAM,CAACsG,CAAC;MAClBtG,MAAM,CAACsG,CAAC,GAAGtG,MAAM,CAACqG,EAAE;MACpBrG,MAAM,CAACqG,EAAE,GAAGE,GAAG;IACjB;IAEA,IAAID,CAAC,GAAG9L,OAAO,CAACwF,MAAM,CAACsG,CAAC,EAAEzL,gBAAgB,CAACyL,CAAC,CAAC;IAC7C,IAAID,EAAE,GAAGhM,OAAO,CAAC2F,MAAM,CAACqG,EAAE,EAAExL,gBAAgB,CAACwL,EAAE,CAAC;IAChDrG,MAAM,CAACsG,CAAC,GAAGA,CAAC;IACZtG,MAAM,CAACqG,EAAE,GAAGA,EAAE;IACd,IAAIG,OAAO,GAAGF,CAAC,GAAGD,EAAE,GAAG,CAAC,CAAC,CAAC;;IAE1B,IAAID,KAAK,GAAG,CAAC,EAAE;MACb,IAAIG,GAAG,GAAGvG,MAAM,CAACsG,CAAC;MAClBtG,MAAM,CAACsG,CAAC,GAAGtG,MAAM,CAACqG,EAAE;MACpBrG,MAAM,CAACqG,EAAE,GAAGE,GAAG;IACjB;IAEA,OAAOC,OAAO;EAChB;AACF,CAAC;AACD,IAAIlG,cAAc,GAAG;EACnBmF,WAAW,EAAE,SAAAA,CAAUxJ,WAAW,EAAErB,IAAI,EAAEoG,QAAQ,EAAEhB,MAAM,EAAE3E,YAAY,EAAE8C,cAAc,EAAEsI,SAAS,EAAEC,QAAQ,EAAEhI,QAAQ,EAAE;IACvH,IAAIiI,IAAI,GAAG,IAAI/N,IAAI,CAAC;MAClB+H,KAAK,EAAElI,MAAM,CAAC,CAAC,CAAC,EAAEuH,MAAM,CAAC;MACzB4G,EAAE,EAAE;IACN,CAAC,CAAC;IACFD,IAAI,CAACE,WAAW,GAAG7F,QAAQ;IAC3B2F,IAAI,CAACG,IAAI,GAAG,MAAM;IAElB,IAAI3I,cAAc,EAAE;MAClB,IAAI4I,SAAS,GAAGJ,IAAI,CAAChG,KAAK;MAC1B,IAAIqG,eAAe,GAAG3L,YAAY,GAAG,QAAQ,GAAG,OAAO;MACvD0L,SAAS,CAACC,eAAe,CAAC,GAAG,CAAC;IAChC;IAEA,OAAOL,IAAI;EACb,CAAC;EACDR,KAAK,EAAE,SAAAA,CAAUlK,WAAW,EAAErB,IAAI,EAAEoG,QAAQ,EAAEhB,MAAM,EAAEiH,QAAQ,EAAE9I,cAAc,EAAEsI,SAAS,EAAEC,QAAQ,EAAEhI,QAAQ,EAAE;IAC7G,IAAIwI,UAAU,GAAG,CAACD,QAAQ,IAAIvI,QAAQ,GAAG/E,OAAO,GAAGd,MAAM;IACzD,IAAIsO,MAAM,GAAG,IAAID,UAAU,CAAC;MAC1BvG,KAAK,EAAEX,MAAM;MACb4G,EAAE,EAAE;IACN,CAAC,CAAC;IACFO,MAAM,CAACL,IAAI,GAAG,MAAM;IACpB,IAAIM,WAAW,GAAGC,0BAA0B,CAACJ,QAAQ,CAAC;IACtDE,MAAM,CAACG,qBAAqB,GAAGrN,iCAAiC,CAACmN,WAAW,EAAE;MAC5EG,UAAU,EAAEL,UAAU,KAAKvN;IAC7B,CAAC,CAAC,CAAC,CAAC;;IAEJ,IAAIwE,cAAc,EAAE;MAClB,IAAIqJ,WAAW,GAAGL,MAAM,CAACxG,KAAK;MAC9B,IAAIqG,eAAe,GAAGC,QAAQ,GAAG,GAAG,GAAG,UAAU;MACjD,IAAIQ,aAAa,GAAG,CAAC,CAAC;MACtBD,WAAW,CAACR,eAAe,CAAC,GAAGC,QAAQ,GAAGjH,MAAM,CAACqG,EAAE,GAAGrG,MAAM,CAAC0H,UAAU;MACvED,aAAa,CAACT,eAAe,CAAC,GAAGhH,MAAM,CAACgH,eAAe,CAAC;MACxD,CAACN,QAAQ,GAAG5N,WAAW,GAAGC,SAAS,EAAEoO,MAAM,EAAE;QAC3CxG,KAAK,EAAE8G,aAAa,CAAC;MAEvB,CAAC,EAAEtJ,cAAc,CAAC;IACpB;IAEA,OAAOgJ,MAAM;EACf;AACF,CAAC;AAED,SAAS7I,kBAAkBA,CAACrC,WAAW,EAAE0L,QAAQ,EAAE;EACjD,IAAIC,kBAAkB,GAAG3L,WAAW,CAACQ,GAAG,CAAC,cAAc,EAAE,IAAI,CAAC;EAC9D,IAAI1B,QAAQ,GAAG4M,QAAQ,CAAC3M,WAAW,CAAC,CAAC;EAErC,IAAI8B,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IACzC,IAAI4K,kBAAkB,EAAE;MACtB,IAAI7M,QAAQ,CAACE,IAAI,KAAK,UAAU,EAAE;QAChCjB,IAAI,CAAC,uFAAuF,CAAC;MAC/F;MAEA,IAAI2N,QAAQ,CAAC1M,IAAI,KAAK,aAAa,EAAE;QACnCjB,IAAI,CAAC,6EAA6E,CAAC;MACrF;IACF;EACF;EAEA,IAAI4N,kBAAkB,IAAI7M,QAAQ,CAACE,IAAI,KAAK,UAAU,IAAI0M,QAAQ,CAAC1M,IAAI,KAAK,aAAa,EAAE;IACzF,OAAO;MACLF,QAAQ,EAAEA,QAAQ;MAClBsK,SAAS,EAAEsC,QAAQ,CAACE,YAAY,CAAC9M,QAAQ;IAC3C,CAAC;EACH;AACF;AAEA,SAAS6F,uBAAuBA,CAACvC,eAAe,EAAEyJ,oBAAoB,EAAEzH,EAAE,EAAEL,MAAM,EAAEgB,QAAQ,EAAE3F,YAAY,EAAEqL,QAAQ,EAAEvH,aAAa,EAAE;EACnI,IAAI4I,YAAY;EAChB,IAAIC,UAAU;EAEd,IAAI3M,YAAY,EAAE;IAChB2M,UAAU,GAAG;MACX1M,CAAC,EAAE0E,MAAM,CAAC1E,CAAC;MACXC,KAAK,EAAEyE,MAAM,CAACzE;IAChB,CAAC;IACDwM,YAAY,GAAG;MACbvM,CAAC,EAAEwE,MAAM,CAACxE,CAAC;MACXC,MAAM,EAAEuE,MAAM,CAACvE;IACjB,CAAC;EACH,CAAC,MAAM;IACLuM,UAAU,GAAG;MACXxM,CAAC,EAAEwE,MAAM,CAACxE,CAAC;MACXC,MAAM,EAAEuE,MAAM,CAACvE;IACjB,CAAC;IACDsM,YAAY,GAAG;MACbzM,CAAC,EAAE0E,MAAM,CAAC1E,CAAC;MACXC,KAAK,EAAEyE,MAAM,CAACzE;IAChB,CAAC;EACH;EAEA,IAAI,CAAC4D,aAAa,EAAE;IAClB;IACA;IACA,CAACuH,QAAQ,GAAG5N,WAAW,GAAGC,SAAS,EAAEsH,EAAE,EAAE;MACvCM,KAAK,EAAEoH;IACT,CAAC,EAAED,oBAAoB,EAAE9G,QAAQ,EAAE,IAAI,CAAC;EAC1C;EAEA,IAAIiH,kBAAkB,GAAGH,oBAAoB,GAAGzJ,eAAe,CAACtD,QAAQ,CAACwF,KAAK,GAAG,IAAI;EACrF,CAACmG,QAAQ,GAAG5N,WAAW,GAAGC,SAAS,EAAEsH,EAAE,EAAE;IACvCM,KAAK,EAAEqH;EACT,CAAC,EAAEC,kBAAkB,EAAEjH,QAAQ,CAAC;AAClC;AAEA,SAASkH,uBAAuBA,CAACC,GAAG,EAAEC,KAAK,EAAE;EAC3C,KAAK,IAAIrG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGqG,KAAK,CAAClH,MAAM,EAAEa,CAAC,EAAE,EAAE;IACrC,IAAI,CAACsG,QAAQ,CAACF,GAAG,CAACC,KAAK,CAACrG,CAAC,CAAC,CAAC,CAAC,EAAE;MAC5B,OAAO,IAAI;IACb;EACF;EAEA,OAAO,KAAK;AACd;AAEA,IAAIuG,YAAY,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,OAAO,EAAE,QAAQ,CAAC;AAChD,IAAIC,aAAa,GAAG,CAAC,IAAI,EAAE,IAAI,EAAE,GAAG,EAAE,YAAY,EAAE,UAAU,CAAC;AAC/D,IAAIrI,aAAa,GAAG;EAClBuF,WAAW,EAAE,SAAAA,CAAUzF,MAAM,EAAE;IAC7B,OAAO,CAACkI,uBAAuB,CAAClI,MAAM,EAAEsI,YAAY,CAAC;EACvD,CAAC;EACDnC,KAAK,EAAE,SAAAA,CAAUnG,MAAM,EAAE;IACvB,OAAO,CAACkI,uBAAuB,CAAClI,MAAM,EAAEuI,aAAa,CAAC;EACxD;AACF,CAAC;AACD,IAAInN,SAAS,GAAG;EACd;EACA;EACAqK,WAAW,EAAE,SAAAA,CAAU7K,IAAI,EAAEyE,SAAS,EAAES,SAAS,EAAE;IACjD,IAAIE,MAAM,GAAGpF,IAAI,CAAC4N,aAAa,CAACnJ,SAAS,CAAC;IAC1C,IAAIoJ,cAAc,GAAG3I,SAAS,GAAG4I,YAAY,CAAC5I,SAAS,EAAEE,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC;;IAEtE,IAAI2I,KAAK,GAAG3I,MAAM,CAACzE,KAAK,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;IACrC,IAAIqN,KAAK,GAAG5I,MAAM,CAACvE,MAAM,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;IACtC,OAAO;MACLH,CAAC,EAAE0E,MAAM,CAAC1E,CAAC,GAAGqN,KAAK,GAAGF,cAAc,GAAG,CAAC;MACxCjN,CAAC,EAAEwE,MAAM,CAACxE,CAAC,GAAGoN,KAAK,GAAGH,cAAc,GAAG,CAAC;MACxClN,KAAK,EAAEyE,MAAM,CAACzE,KAAK,GAAGoN,KAAK,GAAGF,cAAc;MAC5ChN,MAAM,EAAEuE,MAAM,CAACvE,MAAM,GAAGmN,KAAK,GAAGH;IAClC,CAAC;EACH,CAAC;EACDtC,KAAK,EAAE,SAAAA,CAAUvL,IAAI,EAAEyE,SAAS,EAAES,SAAS,EAAE;IAC3C,IAAIE,MAAM,GAAGpF,IAAI,CAAC4N,aAAa,CAACnJ,SAAS,CAAC;IAC1C,OAAO;MACLwJ,EAAE,EAAE7I,MAAM,CAAC6I,EAAE;MACbC,EAAE,EAAE9I,MAAM,CAAC8I,EAAE;MACbzC,EAAE,EAAErG,MAAM,CAACqG,EAAE;MACbC,CAAC,EAAEtG,MAAM,CAACsG,CAAC;MACXoB,UAAU,EAAE1H,MAAM,CAAC0H,UAAU;MAC7BqB,QAAQ,EAAE/I,MAAM,CAAC+I,QAAQ;MACzBC,SAAS,EAAEhJ,MAAM,CAACgJ;IACpB,CAAC;EACH;AACF,CAAC;AAED,SAASC,aAAaA,CAACjJ,MAAM,EAAE;EAC7B,OAAOA,MAAM,CAAC0H,UAAU,IAAI,IAAI,IAAI1H,MAAM,CAAC+I,QAAQ,IAAI,IAAI,IAAI/I,MAAM,CAAC0H,UAAU,KAAK1H,MAAM,CAAC+I,QAAQ;AACtG;AAEA,SAAS1B,0BAA0BA,CAACJ,QAAQ,EAAE;EAC5C,OAAO,UAAUA,QAAQ,EAAE;IACzB,IAAIiC,UAAU,GAAGjC,QAAQ,GAAG,KAAK,GAAG,OAAO;IAC3C,OAAO,UAAUkC,QAAQ,EAAE;MACzB,QAAQA,QAAQ;QACd,KAAK,OAAO;QACZ,KAAK,aAAa;QAClB,KAAK,KAAK;QACV,KAAK,WAAW;UACd,OAAOA,QAAQ,GAAGD,UAAU;QAE9B;UACE,OAAOC,QAAQ;MACnB;IACF,CAAC;EACH,CAAC,CAAClC,QAAQ,CAAC;AACb;AAEA,SAASxG,WAAWA,CAACJ,EAAE,EAAEzF,IAAI,EAAEyE,SAAS,EAAES,SAAS,EAAEE,MAAM,EAAE/D,WAAW,EAAEgC,oBAAoB,EAAEmL,OAAO,EAAE;EACvG,IAAIC,KAAK,GAAGzO,IAAI,CAAC0O,aAAa,CAACjK,SAAS,EAAE,OAAO,CAAC;EAElD,IAAI,CAAC+J,OAAO,EAAE;IACZ,IAAIG,YAAY,GAAGzJ,SAAS,CAACrD,GAAG,CAAC,CAAC,WAAW,EAAE,cAAc,CAAC,CAAC,IAAI,CAAC;IACpE4D,EAAE,CAACV,QAAQ,CAAC,GAAG,EAAE4J,YAAY,CAAC;EAChC,CAAC,MAAM,IAAI,CAACtN,WAAW,CAACQ,GAAG,CAAC,UAAU,CAAC,EAAE;IACvC,IAAI+K,WAAW,GAAGnH,EAAE,CAACM,KAAK;IAC1B,IAAI6I,YAAY,GAAGpP,qBAAqB,CAAC0F,SAAS,CAACjB,QAAQ,CAAC,WAAW,CAAC,EAAE2I,WAAW,EAAE,IAAI,CAAC;IAC5F/O,MAAM,CAAC+O,WAAW,EAAEgC,YAAY,CAAC;IACjCnJ,EAAE,CAACV,QAAQ,CAAC6H,WAAW,CAAC;EAC1B;EAEAnH,EAAE,CAACZ,QAAQ,CAAC4J,KAAK,CAAC;EAClB,IAAII,WAAW,GAAG3J,SAAS,CAAC4J,UAAU,CAAC,QAAQ,CAAC;EAChDD,WAAW,IAAIpJ,EAAE,CAACK,IAAI,CAAC,QAAQ,EAAE+I,WAAW,CAAC;EAC7C,IAAIE,oBAAoB,GAAGP,OAAO,GAAGnL,oBAAoB,GAAG+B,MAAM,CAACsG,CAAC,IAAItG,MAAM,CAACqG,EAAE,GAAG,QAAQ,GAAG,UAAU,GAAGrG,MAAM,CAAC+I,QAAQ,IAAI/I,MAAM,CAAC0H,UAAU,GAAG,UAAU,GAAG,YAAY,GAAGzJ,oBAAoB,GAAG+B,MAAM,CAACvE,MAAM,IAAI,CAAC,GAAG,QAAQ,GAAG,KAAK,GAAGuE,MAAM,CAACzE,KAAK,IAAI,CAAC,GAAG,OAAO,GAAG,MAAM;EAClR,IAAIqO,iBAAiB,GAAGtQ,oBAAoB,CAACwG,SAAS,CAAC;EACvDzG,aAAa,CAACgH,EAAE,EAAEuJ,iBAAiB,EAAE;IACnCC,YAAY,EAAE5N,WAAW;IACzB6N,cAAc,EAAEzK,SAAS;IACzB0K,WAAW,EAAEjQ,eAAe,CAACmC,WAAW,CAAC4B,OAAO,CAAC,CAAC,EAAEwB,SAAS,CAAC;IAC9D2K,YAAY,EAAEX,KAAK,CAACY,IAAI;IACxBC,cAAc,EAAEb,KAAK,CAACc,OAAO;IAC7BC,sBAAsB,EAAET;EAC1B,CAAC,CAAC;EACF,IAAIU,KAAK,GAAGhK,EAAE,CAACkB,cAAc,CAAC,CAAC;EAE/B,IAAI6H,OAAO,IAAIiB,KAAK,EAAE;IACpB,IAAIlB,QAAQ,GAAGrJ,SAAS,CAACrD,GAAG,CAAC,CAAC,OAAO,EAAE,UAAU,CAAC,CAAC;IACnD4D,EAAE,CAACiK,UAAU,CAACC,MAAM,GAAGpB,QAAQ,KAAK,QAAQ,GAAG,IAAI,GAAG,IAAI;IAC1DjP,qBAAqB,CAACmG,EAAE,EAAE8I,QAAQ,KAAK,SAAS,GAAGQ,oBAAoB,GAAGR,QAAQ,EAAE9B,0BAA0B,CAACpJ,oBAAoB,CAAC,EAAE6B,SAAS,CAACrD,GAAG,CAAC,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC,CAAC;EAC3K;EAEAlD,sBAAsB,CAAC8Q,KAAK,EAAET,iBAAiB,EAAE3N,WAAW,CAACuO,WAAW,CAACnL,SAAS,CAAC,EAAE,UAAUqC,KAAK,EAAE;IACpG,OAAO3H,2BAA2B,CAACa,IAAI,EAAE8G,KAAK,CAAC;EACjD,CAAC,CAAC;EACF,IAAI+I,aAAa,GAAG3K,SAAS,CAACjB,QAAQ,CAAC,CAAC,UAAU,CAAC,CAAC;EACpDzF,mBAAmB,CAACiH,EAAE,EAAEoK,aAAa,CAAChO,GAAG,CAAC,OAAO,CAAC,EAAEgO,aAAa,CAAChO,GAAG,CAAC,WAAW,CAAC,EAAEgO,aAAa,CAAChO,GAAG,CAAC,UAAU,CAAC,CAAC;EAClHtD,wBAAwB,CAACkH,EAAE,EAAEP,SAAS,CAAC;EAEvC,IAAImJ,aAAa,CAACjJ,MAAM,CAAC,EAAE;IACzBK,EAAE,CAACgJ,KAAK,CAACY,IAAI,GAAG,MAAM;IACtB5J,EAAE,CAACgJ,KAAK,CAACqB,MAAM,GAAG,MAAM;IACxBhS,IAAI,CAAC2H,EAAE,CAACsK,MAAM,EAAE,UAAUC,KAAK,EAAE;MAC/B,IAAIA,KAAK,CAACvB,KAAK,EAAE;QACfuB,KAAK,CAACvB,KAAK,CAACY,IAAI,GAAGW,KAAK,CAACvB,KAAK,CAACqB,MAAM,GAAG,MAAM;MAChD;IACF,CAAC,CAAC;EACJ;AACF,CAAC,CAAC;;AAGF,SAAShC,YAAYA,CAAC5I,SAAS,EAAE+K,SAAS,EAAE;EAC1C;EACA,IAAIC,WAAW,GAAGhL,SAAS,CAACrD,GAAG,CAAC,CAAC,WAAW,EAAE,aAAa,CAAC,CAAC;EAE7D,IAAI,CAACqO,WAAW,IAAIA,WAAW,KAAK,MAAM,EAAE;IAC1C,OAAO,CAAC;EACV;EAEA,IAAIC,SAAS,GAAGjL,SAAS,CAACrD,GAAG,CAAC,CAAC,WAAW,EAAE,aAAa,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;;EAElE,IAAIlB,KAAK,GAAGyP,KAAK,CAACH,SAAS,CAACtP,KAAK,CAAC,GAAGwI,MAAM,CAACC,SAAS,GAAG1J,IAAI,CAACkI,GAAG,CAACqI,SAAS,CAACtP,KAAK,CAAC;EACjF,IAAIE,MAAM,GAAGuP,KAAK,CAACH,SAAS,CAACpP,MAAM,CAAC,GAAGsI,MAAM,CAACC,SAAS,GAAG1J,IAAI,CAACkI,GAAG,CAACqI,SAAS,CAACpP,MAAM,CAAC;EACpF,OAAOnB,IAAI,CAACG,GAAG,CAACsQ,SAAS,EAAExP,KAAK,EAAEE,MAAM,CAAC;AAC3C;AAEA,IAAIwP,aAAa,GACjB;AACA,YAAY;EACV,SAASA,aAAaA,CAAA,EAAG,CAAC;EAE1B,OAAOA,aAAa;AACtB,CAAC,CAAC,CAAC;AAEH,IAAIC,SAAS,GACb;AACA,UAAUvP,MAAM,EAAE;EAChBrD,SAAS,CAAC4S,SAAS,EAAEvP,MAAM,CAAC;EAE5B,SAASuP,SAASA,CAACC,IAAI,EAAE;IACvB,IAAIvP,KAAK,GAAGD,MAAM,CAACE,IAAI,CAAC,IAAI,EAAEsP,IAAI,CAAC,IAAI,IAAI;IAE3CvP,KAAK,CAACX,IAAI,GAAG,UAAU;IACvB,OAAOW,KAAK;EACd;EAEAsP,SAAS,CAACnP,SAAS,CAACqP,eAAe,GAAG,YAAY;IAChD,OAAO,IAAIH,aAAa,CAAC,CAAC;EAC5B,CAAC;EAEDC,SAAS,CAACnP,SAAS,CAACsP,SAAS,GAAG,UAAUC,GAAG,EAAE3K,KAAK,EAAE;IACpD;IACA;IACA,IAAI4K,MAAM,GAAG5K,KAAK,CAAC4K,MAAM;IACzB,IAAIC,UAAU,GAAG,IAAI,CAACA,UAAU;IAChC,IAAIC,WAAW,GAAG,CAAC,GAAG,IAAI,CAACD,UAAU;IACrC,IAAIE,UAAU,GAAG,EAAE;IACnB,IAAIC,IAAI,GAAG,EAAE;IACb,IAAIC,QAAQ,GAAG,IAAI,CAACA,QAAQ;IAE5B,KAAK,IAAI7J,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGwJ,MAAM,CAACrK,MAAM,EAAEa,CAAC,IAAI,CAAC,EAAE;MACzC4J,IAAI,CAACH,UAAU,CAAC,GAAGI,QAAQ;MAC3BD,IAAI,CAACF,WAAW,CAAC,GAAGF,MAAM,CAACxJ,CAAC,GAAG,CAAC,CAAC;MACjC2J,UAAU,CAACF,UAAU,CAAC,GAAGD,MAAM,CAACxJ,CAAC,GAAGyJ,UAAU,CAAC;MAC/CE,UAAU,CAACD,WAAW,CAAC,GAAGF,MAAM,CAACxJ,CAAC,GAAG0J,WAAW,CAAC;MACjDH,GAAG,CAAC3E,IAAI,CAAC+E,UAAU,CAAC,CAAC,CAAC,EAAEA,UAAU,CAAC,CAAC,CAAC,EAAEC,IAAI,CAAC,CAAC,CAAC,EAAEA,IAAI,CAAC,CAAC,CAAC,CAAC;IAC1D;EACF,CAAC;EAED,OAAOT,SAAS;AAClB,CAAC,CAAC3S,IAAI,CAAC;AAEP,SAASyJ,WAAWA,CAAC/F,WAAW,EAAEwB,KAAK,EAAEoO,cAAc,EAAEC,WAAW,EAAE;EACpE;EACA,IAAIlR,IAAI,GAAGqB,WAAW,CAAC4B,OAAO,CAAC,CAAC;EAChC,IAAI2N,UAAU,GAAG5Q,IAAI,CAACQ,SAAS,CAAC,qBAAqB,CAAC,GAAG,CAAC,GAAG,CAAC;EAC9D,IAAI2Q,gBAAgB,GAAGnR,IAAI,CAACQ,SAAS,CAAC,kBAAkB,CAAC;EACzD,IAAIwQ,QAAQ,GAAGhR,IAAI,CAACQ,SAAS,CAAC,MAAM,CAAC;EACrC,IAAIwD,eAAe,GAAG3C,WAAW,CAAC4C,QAAQ,CAAC,iBAAiB,CAAC;EAC7D,IAAImN,QAAQ,GAAGpR,IAAI,CAACQ,SAAS,CAAC,uBAAuB,CAAC;EAEtD,IAAI4Q,QAAQ,EAAE;IACZ,IAAIzM,IAAI,GAAG,IAAI2L,SAAS,CAAC;MACvBvK,KAAK,EAAE;QACL4K,MAAM,EAAES;MACV,CAAC;MACDF,WAAW,EAAE,CAAC,CAACA,WAAW;MAC1BG,MAAM,EAAE,IAAI;MACZrF,EAAE,EAAE;IACN,CAAC,CAAC;IACFrH,IAAI,CAACiM,UAAU,GAAGA,UAAU;IAC5BjM,IAAI,CAACwM,gBAAgB,GAAGA,gBAAgB;IACxCxM,IAAI,CAACqM,QAAQ,GAAGA,QAAQ;IACxBrM,IAAI,CAACE,QAAQ,CAACb,eAAe,CAACc,YAAY,CAAC,CAAC,CAAC;IAC7CjC,KAAK,CAACoC,GAAG,CAACN,IAAI,CAAC;IACfsM,cAAc,IAAIA,cAAc,CAACxI,IAAI,CAAC9D,IAAI,CAAC;EAC7C;EAEA,IAAIc,EAAE,GAAG,IAAI6K,SAAS,CAAC;IACrBvK,KAAK,EAAE;MACL4K,MAAM,EAAE3Q,IAAI,CAACQ,SAAS,CAAC,aAAa;IACtC,CAAC;IACD0Q,WAAW,EAAE,CAAC,CAACA,WAAW;IAC1BI,mBAAmB,EAAE,IAAI;IACzBtF,EAAE,EAAE;EACN,CAAC,CAAC;EACFvG,EAAE,CAACmL,UAAU,GAAGA,UAAU;EAC1BnL,EAAE,CAAC0L,gBAAgB,GAAGA,gBAAgB;EACtC1L,EAAE,CAACuL,QAAQ,GAAGA,QAAQ;EACtBnO,KAAK,CAACoC,GAAG,CAACQ,EAAE,CAAC;EACbA,EAAE,CAACZ,QAAQ,CAAC7E,IAAI,CAACuR,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;;EAEtCjT,SAAS,CAACmH,EAAE,CAAC,CAAC+L,WAAW,GAAGnQ,WAAW,CAACmQ,WAAW;EAEnD,IAAI,CAACnQ,WAAW,CAACQ,GAAG,CAAC,QAAQ,CAAC,EAAE;IAC9B4D,EAAE,CAACuC,EAAE,CAAC,WAAW,EAAEyJ,wBAAwB,CAAC;IAC5ChM,EAAE,CAACuC,EAAE,CAAC,WAAW,EAAEyJ,wBAAwB,CAAC;EAC9C;EAEAR,cAAc,IAAIA,cAAc,CAACxI,IAAI,CAAChD,EAAE,CAAC;AAC3C,CAAC,CAAC;;AAGF,IAAIgM,wBAAwB,GAAG5S,QAAQ,CAAC,UAAU6S,KAAK,EAAE;EACvD,IAAIC,SAAS,GAAG,IAAI;EACpB,IAAIlN,SAAS,GAAGmN,sBAAsB,CAACD,SAAS,EAAED,KAAK,CAACG,OAAO,EAAEH,KAAK,CAACI,OAAO,CAAC;EAC/ExT,SAAS,CAACqT,SAAS,CAAC,CAAClN,SAAS,GAAGA,SAAS,IAAI,CAAC,GAAGA,SAAS,GAAG,IAAI;AACpE,CAAC,EAAE,EAAE,EAAE,KAAK,CAAC;AAEb,SAASmN,sBAAsBA,CAACD,SAAS,EAAEjR,CAAC,EAAEE,CAAC,EAAE;EAC/C,IAAIgQ,UAAU,GAAGe,SAAS,CAACf,UAAU;EACrC,IAAIC,WAAW,GAAG,CAAC,GAAGD,UAAU;EAChC,IAAID,MAAM,GAAGgB,SAAS,CAAC5L,KAAK,CAAC4K,MAAM;EACnC,IAAIQ,gBAAgB,GAAGQ,SAAS,CAACR,gBAAgB;EACjD,IAAIL,UAAU,GAAG,EAAE;EACnB,IAAIC,IAAI,GAAG,EAAE;EACb,IAAIC,QAAQ,GAAGW,SAAS,CAACX,QAAQ;EAEjC,KAAK,IAAI7J,CAAC,GAAG,CAAC,EAAEmC,GAAG,GAAGqH,MAAM,CAACrK,MAAM,GAAG,CAAC,EAAEa,CAAC,GAAGmC,GAAG,EAAEnC,CAAC,EAAE,EAAE;IACrD,IAAI4K,EAAE,GAAG5K,CAAC,GAAG,CAAC;IACd4J,IAAI,CAACH,UAAU,CAAC,GAAGI,QAAQ;IAC3BD,IAAI,CAACF,WAAW,CAAC,GAAGF,MAAM,CAACoB,EAAE,GAAG,CAAC,CAAC;IAClCjB,UAAU,CAACF,UAAU,CAAC,GAAGD,MAAM,CAACoB,EAAE,GAAGnB,UAAU,CAAC;IAChDE,UAAU,CAACD,WAAW,CAAC,GAAGF,MAAM,CAACoB,EAAE,GAAGlB,WAAW,CAAC;IAElD,IAAIE,IAAI,CAACF,WAAW,CAAC,GAAG,CAAC,EAAE;MACzBC,UAAU,CAACD,WAAW,CAAC,IAAIE,IAAI,CAACF,WAAW,CAAC;MAC5CE,IAAI,CAACF,WAAW,CAAC,GAAG,CAACE,IAAI,CAACF,WAAW,CAAC;IACxC;IAEA,IAAInQ,CAAC,IAAIoQ,UAAU,CAAC,CAAC,CAAC,IAAIpQ,CAAC,IAAIoQ,UAAU,CAAC,CAAC,CAAC,GAAGC,IAAI,CAAC,CAAC,CAAC,IAAInQ,CAAC,IAAIkQ,UAAU,CAAC,CAAC,CAAC,IAAIlQ,CAAC,IAAIkQ,UAAU,CAAC,CAAC,CAAC,GAAGC,IAAI,CAAC,CAAC,CAAC,EAAE;MAC5G,OAAOI,gBAAgB,CAAChK,CAAC,CAAC;IAC5B;EACF;EAEA,OAAO,CAAC,CAAC;AACX;AAEA,SAASZ,qBAAqBA,CAAClD,oBAAoB,EAAE+B,MAAM,EAAErF,KAAK,EAAE;EAClE,IAAId,sBAAsB,CAACc,KAAK,EAAE,aAAa,CAAC,EAAE;IAChD,IAAIoM,SAAS,GAAG/G,MAAM;IACtB,IAAI4M,WAAW,GAAGjS,KAAK,CAACG,OAAO,CAAC,CAAC;IACjC,OAAO;MACLQ,CAAC,EAAE2C,oBAAoB,GAAG8I,SAAS,CAACzL,CAAC,GAAGsR,WAAW,CAACtR,CAAC;MACrDE,CAAC,EAAEyC,oBAAoB,GAAG2O,WAAW,CAACpR,CAAC,GAAGuL,SAAS,CAACvL,CAAC;MACrDD,KAAK,EAAE0C,oBAAoB,GAAG8I,SAAS,CAACxL,KAAK,GAAGqR,WAAW,CAACrR,KAAK;MACjEE,MAAM,EAAEwC,oBAAoB,GAAG2O,WAAW,CAACnR,MAAM,GAAGsL,SAAS,CAACtL;IAChE,CAAC;EACH,CAAC,MAAM;IACL,IAAImR,WAAW,GAAGjS,KAAK,CAACG,OAAO,CAAC,CAAC;IACjC,IAAI0M,WAAW,GAAGxH,MAAM;IACxB,OAAO;MACL6I,EAAE,EAAE+D,WAAW,CAAC/D,EAAE;MAClBC,EAAE,EAAE8D,WAAW,CAAC9D,EAAE;MAClBzC,EAAE,EAAEpI,oBAAoB,GAAG2O,WAAW,CAACvG,EAAE,GAAGmB,WAAW,CAACnB,EAAE;MAC1DC,CAAC,EAAErI,oBAAoB,GAAG2O,WAAW,CAACtG,CAAC,GAAGkB,WAAW,CAAClB,CAAC;MACvDoB,UAAU,EAAEzJ,oBAAoB,GAAGuJ,WAAW,CAACE,UAAU,GAAG,CAAC;MAC7DqB,QAAQ,EAAE9K,oBAAoB,GAAGuJ,WAAW,CAACuB,QAAQ,GAAGzO,IAAI,CAACuS,EAAE,GAAG;IACpE,CAAC;EACH;AACF;AAEA,SAASrN,kBAAkBA,CAAC7E,KAAK,EAAEsD,oBAAoB,EAAE+B,MAAM,EAAE;EAC/D,IAAI8M,UAAU,GAAGnS,KAAK,CAACM,IAAI,KAAK,OAAO,GAAGpC,MAAM,GAAGD,IAAI;EACvD,OAAO,IAAIkU,UAAU,CAAC;IACpBnM,KAAK,EAAEQ,qBAAqB,CAAClD,oBAAoB,EAAE+B,MAAM,EAAErF,KAAK,CAAC;IACjEsR,MAAM,EAAE,IAAI;IACZrF,EAAE,EAAE;EACN,CAAC,CAAC;AACJ;AAEA,eAAelL,OAAO"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}