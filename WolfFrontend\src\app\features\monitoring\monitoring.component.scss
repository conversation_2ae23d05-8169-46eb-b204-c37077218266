.monitoring-container {
  height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  font-family: 'Se<PERSON>e UI', Tahoma, Geneva, Verdana, sans-serif;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.header {
  padding: 20px;
  text-align: center;
  background: rgba(0, 0, 0, 0.2);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.title {
  margin: 0;
  font-size: 2.5rem;
  font-weight: 300;
  color: #ff6b6b;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.main-grid {
  flex: 1;
  display: grid;
  grid-template-columns: 300px 1fr 400px;
  grid-template-rows: 1fr 300px;
  gap: 20px;
  padding: 20px;
  grid-template-areas: 
    "countries globe logs"
    "ban globe logs";
}

.top-countries-section {
  grid-area: countries;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-radius: 15px;
  border: 2px solid #ff6b6b;
  padding: 20px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

.globe-section {
  grid-area: globe;
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(10px);
  border-radius: 15px;
  padding: 20px;
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

.logs-section {
  grid-area: logs;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-radius: 15px;
  border: 2px solid #ff6b6b;
  padding: 20px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

.ban-management-section {
  grid-area: ban;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-radius: 15px;
  border: 2px solid #4ecdc4;
  padding: 20px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

.section-title {
  margin: 0 0 15px 0;
  font-size: 1.2rem;
  font-weight: 500;
  text-align: center;
  color: #fff;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
}

.connection-time {
  position: absolute;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(0, 0, 0, 0.7);
  padding: 10px 20px;
  border-radius: 25px;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.time-label {
  color: #ff6b6b;
  margin-right: 10px;
  font-weight: 500;
}

.time-value {
  color: #4ecdc4;
  font-weight: bold;
  font-size: 1.1rem;
}

.stats-bar {
  display: flex;
  justify-content: center;
  gap: 40px;
  padding: 15px;
  background: rgba(0, 0, 0, 0.3);
  backdrop-filter: blur(10px);
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.stat-label {
  color: #ccc;
  font-size: 0.9rem;
}

.stat-value {
  color: #4ecdc4;
  font-weight: bold;
  font-size: 1.1rem;
  background: rgba(78, 205, 196, 0.2);
  padding: 4px 12px;
  border-radius: 12px;
  border: 1px solid rgba(78, 205, 196, 0.3);
}

/* Responsive Design */
@media (max-width: 1200px) {
  .main-grid {
    grid-template-columns: 250px 1fr 350px;
  }
}

@media (max-width: 900px) {
  .main-grid {
    grid-template-columns: 1fr;
    grid-template-rows: auto auto auto auto;
    grid-template-areas: 
      "globe"
      "countries"
      "logs"
      "ban";
  }
  
  .title {
    font-size: 2rem;
  }
}
