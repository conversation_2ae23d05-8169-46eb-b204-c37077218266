{"ast": null, "code": "/**\n * EvEmitter v1.1.0\n * Lil' event emitter\n * MIT License\n */\n\n/* jshint unused: true, undef: true, strict: true */\n\n(function (global, factory) {\n  // universal module definition\n  /* jshint strict: false */ /* globals define, module, window */\n  if (typeof define == 'function' && define.amd) {\n    // AMD - RequireJS\n    define(factory);\n  } else if (typeof module == 'object' && module.exports) {\n    // CommonJS - Browserify, Webpack\n    module.exports = factory();\n  } else {\n    // Browser globals\n    global.EvEmitter = factory();\n  }\n})(typeof window != 'undefined' ? window : this, function () {\n  \"use strict\";\n\n  function EvEmitter() {}\n  var proto = EvEmitter.prototype;\n  proto.on = function (eventName, listener) {\n    if (!eventName || !listener) {\n      return;\n    }\n    // set events hash\n    var events = this._events = this._events || {};\n    // set listeners array\n    var listeners = events[eventName] = events[eventName] || [];\n    // only add once\n    if (listeners.indexOf(listener) == -1) {\n      listeners.push(listener);\n    }\n    return this;\n  };\n  proto.once = function (eventName, listener) {\n    if (!eventName || !listener) {\n      return;\n    }\n    // add event\n    this.on(eventName, listener);\n    // set once flag\n    // set onceEvents hash\n    var onceEvents = this._onceEvents = this._onceEvents || {};\n    // set onceListeners object\n    var onceListeners = onceEvents[eventName] = onceEvents[eventName] || {};\n    // set flag\n    onceListeners[listener] = true;\n    return this;\n  };\n  proto.off = function (eventName, listener) {\n    var listeners = this._events && this._events[eventName];\n    if (!listeners || !listeners.length) {\n      return;\n    }\n    var index = listeners.indexOf(listener);\n    if (index != -1) {\n      listeners.splice(index, 1);\n    }\n    return this;\n  };\n  proto.emitEvent = function (eventName, args) {\n    var listeners = this._events && this._events[eventName];\n    if (!listeners || !listeners.length) {\n      return;\n    }\n    // copy over to avoid interference if .off() in listener\n    listeners = listeners.slice(0);\n    args = args || [];\n    // once stuff\n    var onceListeners = this._onceEvents && this._onceEvents[eventName];\n    for (var i = 0; i < listeners.length; i++) {\n      var listener = listeners[i];\n      var isOnce = onceListeners && onceListeners[listener];\n      if (isOnce) {\n        // remove listener\n        // remove before trigger to prevent recursion\n        this.off(eventName, listener);\n        // unset once flag\n        delete onceListeners[listener];\n      }\n      // trigger listener\n      listener.apply(this, args);\n    }\n    return this;\n  };\n  proto.allOff = function () {\n    delete this._events;\n    delete this._onceEvents;\n  };\n  return EvEmitter;\n});", "map": {"version": 3, "names": ["global", "factory", "define", "amd", "module", "exports", "EvEmitter", "window", "proto", "prototype", "on", "eventName", "listener", "events", "_events", "listeners", "indexOf", "push", "once", "onceEvents", "_onceEvents", "onceListeners", "off", "length", "index", "splice", "emitEvent", "args", "slice", "i", "isOnce", "apply", "allOff"], "sources": ["C:/WolfSystemV2/WolfOldFrontEnd/node_modules/ev-emitter/ev-emitter.js"], "sourcesContent": ["/**\n * EvEmitter v1.1.0\n * Lil' event emitter\n * MIT License\n */\n\n/* jshint unused: true, undef: true, strict: true */\n\n( function( global, factory ) {\n  // universal module definition\n  /* jshint strict: false */ /* globals define, module, window */\n  if ( typeof define == 'function' && define.amd ) {\n    // AMD - RequireJS\n    define( factory );\n  } else if ( typeof module == 'object' && module.exports ) {\n    // CommonJS - Browserify, Webpack\n    module.exports = factory();\n  } else {\n    // Browser globals\n    global.EvEmitter = factory();\n  }\n\n}( typeof window != 'undefined' ? window : this, function() {\n\n\"use strict\";\n\nfunction EvEmitter() {}\n\nvar proto = EvEmitter.prototype;\n\nproto.on = function( eventName, listener ) {\n  if ( !eventName || !listener ) {\n    return;\n  }\n  // set events hash\n  var events = this._events = this._events || {};\n  // set listeners array\n  var listeners = events[ eventName ] = events[ eventName ] || [];\n  // only add once\n  if ( listeners.indexOf( listener ) == -1 ) {\n    listeners.push( listener );\n  }\n\n  return this;\n};\n\nproto.once = function( eventName, listener ) {\n  if ( !eventName || !listener ) {\n    return;\n  }\n  // add event\n  this.on( eventName, listener );\n  // set once flag\n  // set onceEvents hash\n  var onceEvents = this._onceEvents = this._onceEvents || {};\n  // set onceListeners object\n  var onceListeners = onceEvents[ eventName ] = onceEvents[ eventName ] || {};\n  // set flag\n  onceListeners[ listener ] = true;\n\n  return this;\n};\n\nproto.off = function( eventName, listener ) {\n  var listeners = this._events && this._events[ eventName ];\n  if ( !listeners || !listeners.length ) {\n    return;\n  }\n  var index = listeners.indexOf( listener );\n  if ( index != -1 ) {\n    listeners.splice( index, 1 );\n  }\n\n  return this;\n};\n\nproto.emitEvent = function( eventName, args ) {\n  var listeners = this._events && this._events[ eventName ];\n  if ( !listeners || !listeners.length ) {\n    return;\n  }\n  // copy over to avoid interference if .off() in listener\n  listeners = listeners.slice(0);\n  args = args || [];\n  // once stuff\n  var onceListeners = this._onceEvents && this._onceEvents[ eventName ];\n\n  for ( var i=0; i < listeners.length; i++ ) {\n    var listener = listeners[i]\n    var isOnce = onceListeners && onceListeners[ listener ];\n    if ( isOnce ) {\n      // remove listener\n      // remove before trigger to prevent recursion\n      this.off( eventName, listener );\n      // unset once flag\n      delete onceListeners[ listener ];\n    }\n    // trigger listener\n    listener.apply( this, args );\n  }\n\n  return this;\n};\n\nproto.allOff = function() {\n  delete this._events;\n  delete this._onceEvents;\n};\n\nreturn EvEmitter;\n\n}));\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;;AAEA;;AAEE,WAAUA,MAAM,EAAEC,OAAO,EAAG;EAC5B;EACA,2BAA2B;EAC3B,IAAK,OAAOC,MAAM,IAAI,UAAU,IAAIA,MAAM,CAACC,GAAG,EAAG;IAC/C;IACAD,MAAM,CAAED,OAAQ,CAAC;EACnB,CAAC,MAAM,IAAK,OAAOG,MAAM,IAAI,QAAQ,IAAIA,MAAM,CAACC,OAAO,EAAG;IACxD;IACAD,MAAM,CAACC,OAAO,GAAGJ,OAAO,CAAC,CAAC;EAC5B,CAAC,MAAM;IACL;IACAD,MAAM,CAACM,SAAS,GAAGL,OAAO,CAAC,CAAC;EAC9B;AAEF,CAAC,EAAE,OAAOM,MAAM,IAAI,WAAW,GAAGA,MAAM,GAAG,IAAI,EAAE,YAAW;EAE5D,YAAY;;EAEZ,SAASD,SAASA,CAAA,EAAG,CAAC;EAEtB,IAAIE,KAAK,GAAGF,SAAS,CAACG,SAAS;EAE/BD,KAAK,CAACE,EAAE,GAAG,UAAUC,SAAS,EAAEC,QAAQ,EAAG;IACzC,IAAK,CAACD,SAAS,IAAI,CAACC,QAAQ,EAAG;MAC7B;IACF;IACA;IACA,IAAIC,MAAM,GAAG,IAAI,CAACC,OAAO,GAAG,IAAI,CAACA,OAAO,IAAI,CAAC,CAAC;IAC9C;IACA,IAAIC,SAAS,GAAGF,MAAM,CAAEF,SAAS,CAAE,GAAGE,MAAM,CAAEF,SAAS,CAAE,IAAI,EAAE;IAC/D;IACA,IAAKI,SAAS,CAACC,OAAO,CAAEJ,QAAS,CAAC,IAAI,CAAC,CAAC,EAAG;MACzCG,SAAS,CAACE,IAAI,CAAEL,QAAS,CAAC;IAC5B;IAEA,OAAO,IAAI;EACb,CAAC;EAEDJ,KAAK,CAACU,IAAI,GAAG,UAAUP,SAAS,EAAEC,QAAQ,EAAG;IAC3C,IAAK,CAACD,SAAS,IAAI,CAACC,QAAQ,EAAG;MAC7B;IACF;IACA;IACA,IAAI,CAACF,EAAE,CAAEC,SAAS,EAAEC,QAAS,CAAC;IAC9B;IACA;IACA,IAAIO,UAAU,GAAG,IAAI,CAACC,WAAW,GAAG,IAAI,CAACA,WAAW,IAAI,CAAC,CAAC;IAC1D;IACA,IAAIC,aAAa,GAAGF,UAAU,CAAER,SAAS,CAAE,GAAGQ,UAAU,CAAER,SAAS,CAAE,IAAI,CAAC,CAAC;IAC3E;IACAU,aAAa,CAAET,QAAQ,CAAE,GAAG,IAAI;IAEhC,OAAO,IAAI;EACb,CAAC;EAEDJ,KAAK,CAACc,GAAG,GAAG,UAAUX,SAAS,EAAEC,QAAQ,EAAG;IAC1C,IAAIG,SAAS,GAAG,IAAI,CAACD,OAAO,IAAI,IAAI,CAACA,OAAO,CAAEH,SAAS,CAAE;IACzD,IAAK,CAACI,SAAS,IAAI,CAACA,SAAS,CAACQ,MAAM,EAAG;MACrC;IACF;IACA,IAAIC,KAAK,GAAGT,SAAS,CAACC,OAAO,CAAEJ,QAAS,CAAC;IACzC,IAAKY,KAAK,IAAI,CAAC,CAAC,EAAG;MACjBT,SAAS,CAACU,MAAM,CAAED,KAAK,EAAE,CAAE,CAAC;IAC9B;IAEA,OAAO,IAAI;EACb,CAAC;EAEDhB,KAAK,CAACkB,SAAS,GAAG,UAAUf,SAAS,EAAEgB,IAAI,EAAG;IAC5C,IAAIZ,SAAS,GAAG,IAAI,CAACD,OAAO,IAAI,IAAI,CAACA,OAAO,CAAEH,SAAS,CAAE;IACzD,IAAK,CAACI,SAAS,IAAI,CAACA,SAAS,CAACQ,MAAM,EAAG;MACrC;IACF;IACA;IACAR,SAAS,GAAGA,SAAS,CAACa,KAAK,CAAC,CAAC,CAAC;IAC9BD,IAAI,GAAGA,IAAI,IAAI,EAAE;IACjB;IACA,IAAIN,aAAa,GAAG,IAAI,CAACD,WAAW,IAAI,IAAI,CAACA,WAAW,CAAET,SAAS,CAAE;IAErE,KAAM,IAAIkB,CAAC,GAAC,CAAC,EAAEA,CAAC,GAAGd,SAAS,CAACQ,MAAM,EAAEM,CAAC,EAAE,EAAG;MACzC,IAAIjB,QAAQ,GAAGG,SAAS,CAACc,CAAC,CAAC;MAC3B,IAAIC,MAAM,GAAGT,aAAa,IAAIA,aAAa,CAAET,QAAQ,CAAE;MACvD,IAAKkB,MAAM,EAAG;QACZ;QACA;QACA,IAAI,CAACR,GAAG,CAAEX,SAAS,EAAEC,QAAS,CAAC;QAC/B;QACA,OAAOS,aAAa,CAAET,QAAQ,CAAE;MAClC;MACA;MACAA,QAAQ,CAACmB,KAAK,CAAE,IAAI,EAAEJ,IAAK,CAAC;IAC9B;IAEA,OAAO,IAAI;EACb,CAAC;EAEDnB,KAAK,CAACwB,MAAM,GAAG,YAAW;IACxB,OAAO,IAAI,CAAClB,OAAO;IACnB,OAAO,IAAI,CAACM,WAAW;EACzB,CAAC;EAED,OAAOd,SAAS;AAEhB,CAAC,CAAC"}, "metadata": {}, "sourceType": "script", "externalDependencies": []}