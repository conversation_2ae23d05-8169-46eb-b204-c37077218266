{"ast": null, "code": "/*!\n * Outlayer v2.1.1\n * the brains and guts of a layout library\n * MIT license\n */\n\n(function (window, factory) {\n  'use strict';\n\n  // universal module definition\n  /* jshint strict: false */ /* globals define, module, require */\n  if (typeof define == 'function' && define.amd) {\n    // AMD - RequireJS\n    define(['ev-emitter/ev-emitter', 'get-size/get-size', 'fizzy-ui-utils/utils', './item'], function (EvEmitter, getSize, utils, Item) {\n      return factory(window, EvEmitter, getSize, utils, Item);\n    });\n  } else if (typeof module == 'object' && module.exports) {\n    // CommonJS - Browserify, Webpack\n    module.exports = factory(window, require('ev-emitter'), require('get-size'), require('fizzy-ui-utils'), require('./item'));\n  } else {\n    // browser global\n    window.Outlayer = factory(window, window.EvEmitter, window.getSize, window.fizzyUIUtils, window.Outlayer.Item);\n  }\n})(window, function factory(window, EvEmitter, getSize, utils, Item) {\n  'use strict';\n\n  // ----- vars ----- //\n  var console = window.console;\n  var jQuery = window.jQuery;\n  var noop = function () {};\n\n  // -------------------------- Outlayer -------------------------- //\n\n  // globally unique identifiers\n  var GUID = 0;\n  // internal store of all Outlayer intances\n  var instances = {};\n\n  /**\n   * @param {Element, String} element\n   * @param {Object} options\n   * @constructor\n   */\n  function Outlayer(element, options) {\n    var queryElement = utils.getQueryElement(element);\n    if (!queryElement) {\n      if (console) {\n        console.error('Bad element for ' + this.constructor.namespace + ': ' + (queryElement || element));\n      }\n      return;\n    }\n    this.element = queryElement;\n    // add jQuery\n    if (jQuery) {\n      this.$element = jQuery(this.element);\n    }\n\n    // options\n    this.options = utils.extend({}, this.constructor.defaults);\n    this.option(options);\n\n    // add id for Outlayer.getFromElement\n    var id = ++GUID;\n    this.element.outlayerGUID = id; // expando\n    instances[id] = this; // associate via id\n\n    // kick it off\n    this._create();\n    var isInitLayout = this._getOption('initLayout');\n    if (isInitLayout) {\n      this.layout();\n    }\n  }\n\n  // settings are for internal use only\n  Outlayer.namespace = 'outlayer';\n  Outlayer.Item = Item;\n\n  // default options\n  Outlayer.defaults = {\n    containerStyle: {\n      position: 'relative'\n    },\n    initLayout: true,\n    originLeft: true,\n    originTop: true,\n    resize: true,\n    resizeContainer: true,\n    // item options\n    transitionDuration: '0.4s',\n    hiddenStyle: {\n      opacity: 0,\n      transform: 'scale(0.001)'\n    },\n    visibleStyle: {\n      opacity: 1,\n      transform: 'scale(1)'\n    }\n  };\n  var proto = Outlayer.prototype;\n  // inherit EvEmitter\n  utils.extend(proto, EvEmitter.prototype);\n\n  /**\n   * set options\n   * @param {Object} opts\n   */\n  proto.option = function (opts) {\n    utils.extend(this.options, opts);\n  };\n\n  /**\n   * get backwards compatible option value, check old name\n   */\n  proto._getOption = function (option) {\n    var oldOption = this.constructor.compatOptions[option];\n    return oldOption && this.options[oldOption] !== undefined ? this.options[oldOption] : this.options[option];\n  };\n  Outlayer.compatOptions = {\n    // currentName: oldName\n    initLayout: 'isInitLayout',\n    horizontal: 'isHorizontal',\n    layoutInstant: 'isLayoutInstant',\n    originLeft: 'isOriginLeft',\n    originTop: 'isOriginTop',\n    resize: 'isResizeBound',\n    resizeContainer: 'isResizingContainer'\n  };\n  proto._create = function () {\n    // get items from children\n    this.reloadItems();\n    // elements that affect layout, but are not laid out\n    this.stamps = [];\n    this.stamp(this.options.stamp);\n    // set container style\n    utils.extend(this.element.style, this.options.containerStyle);\n\n    // bind resize method\n    var canBindResize = this._getOption('resize');\n    if (canBindResize) {\n      this.bindResize();\n    }\n  };\n\n  // goes through all children again and gets bricks in proper order\n  proto.reloadItems = function () {\n    // collection of item elements\n    this.items = this._itemize(this.element.children);\n  };\n\n  /**\n   * turn elements into Outlayer.Items to be used in layout\n   * @param {Array or NodeList or HTMLElement} elems\n   * @returns {Array} items - collection of new Outlayer Items\n   */\n  proto._itemize = function (elems) {\n    var itemElems = this._filterFindItemElements(elems);\n    var Item = this.constructor.Item;\n\n    // create new Outlayer Items for collection\n    var items = [];\n    for (var i = 0; i < itemElems.length; i++) {\n      var elem = itemElems[i];\n      var item = new Item(elem, this);\n      items.push(item);\n    }\n    return items;\n  };\n\n  /**\n   * get item elements to be used in layout\n   * @param {Array or NodeList or HTMLElement} elems\n   * @returns {Array} items - item elements\n   */\n  proto._filterFindItemElements = function (elems) {\n    return utils.filterFindElements(elems, this.options.itemSelector);\n  };\n\n  /**\n   * getter method for getting item elements\n   * @returns {Array} elems - collection of item elements\n   */\n  proto.getItemElements = function () {\n    return this.items.map(function (item) {\n      return item.element;\n    });\n  };\n\n  // ----- init & layout ----- //\n\n  /**\n   * lays out all items\n   */\n  proto.layout = function () {\n    this._resetLayout();\n    this._manageStamps();\n\n    // don't animate first layout\n    var layoutInstant = this._getOption('layoutInstant');\n    var isInstant = layoutInstant !== undefined ? layoutInstant : !this._isLayoutInited;\n    this.layoutItems(this.items, isInstant);\n\n    // flag for initalized\n    this._isLayoutInited = true;\n  };\n\n  // _init is alias for layout\n  proto._init = proto.layout;\n\n  /**\n   * logic before any new layout\n   */\n  proto._resetLayout = function () {\n    this.getSize();\n  };\n  proto.getSize = function () {\n    this.size = getSize(this.element);\n  };\n\n  /**\n   * get measurement from option, for columnWidth, rowHeight, gutter\n   * if option is String -> get element from selector string, & get size of element\n   * if option is Element -> get size of element\n   * else use option as a number\n   *\n   * @param {String} measurement\n   * @param {String} size - width or height\n   * @private\n   */\n  proto._getMeasurement = function (measurement, size) {\n    var option = this.options[measurement];\n    var elem;\n    if (!option) {\n      // default to 0\n      this[measurement] = 0;\n    } else {\n      // use option as an element\n      if (typeof option == 'string') {\n        elem = this.element.querySelector(option);\n      } else if (option instanceof HTMLElement) {\n        elem = option;\n      }\n      // use size of element, if element\n      this[measurement] = elem ? getSize(elem)[size] : option;\n    }\n  };\n\n  /**\n   * layout a collection of item elements\n   * @api public\n   */\n  proto.layoutItems = function (items, isInstant) {\n    items = this._getItemsForLayout(items);\n    this._layoutItems(items, isInstant);\n    this._postLayout();\n  };\n\n  /**\n   * get the items to be laid out\n   * you may want to skip over some items\n   * @param {Array} items\n   * @returns {Array} items\n   */\n  proto._getItemsForLayout = function (items) {\n    return items.filter(function (item) {\n      return !item.isIgnored;\n    });\n  };\n\n  /**\n   * layout items\n   * @param {Array} items\n   * @param {Boolean} isInstant\n   */\n  proto._layoutItems = function (items, isInstant) {\n    this._emitCompleteOnItems('layout', items);\n    if (!items || !items.length) {\n      // no items, emit event with empty array\n      return;\n    }\n    var queue = [];\n    items.forEach(function (item) {\n      // get x/y object from method\n      var position = this._getItemLayoutPosition(item);\n      // enqueue\n      position.item = item;\n      position.isInstant = isInstant || item.isLayoutInstant;\n      queue.push(position);\n    }, this);\n    this._processLayoutQueue(queue);\n  };\n\n  /**\n   * get item layout position\n   * @param {Outlayer.Item} item\n   * @returns {Object} x and y position\n   */\n  proto._getItemLayoutPosition = function /* item */\n  () {\n    return {\n      x: 0,\n      y: 0\n    };\n  };\n\n  /**\n   * iterate over array and position each item\n   * Reason being - separating this logic prevents 'layout invalidation'\n   * thx @paul_irish\n   * @param {Array} queue\n   */\n  proto._processLayoutQueue = function (queue) {\n    this.updateStagger();\n    queue.forEach(function (obj, i) {\n      this._positionItem(obj.item, obj.x, obj.y, obj.isInstant, i);\n    }, this);\n  };\n\n  // set stagger from option in milliseconds number\n  proto.updateStagger = function () {\n    var stagger = this.options.stagger;\n    if (stagger === null || stagger === undefined) {\n      this.stagger = 0;\n      return;\n    }\n    this.stagger = getMilliseconds(stagger);\n    return this.stagger;\n  };\n\n  /**\n   * Sets position of item in DOM\n   * @param {Outlayer.Item} item\n   * @param {Number} x - horizontal position\n   * @param {Number} y - vertical position\n   * @param {Boolean} isInstant - disables transitions\n   */\n  proto._positionItem = function (item, x, y, isInstant, i) {\n    if (isInstant) {\n      // if not transition, just set CSS\n      item.goTo(x, y);\n    } else {\n      item.stagger(i * this.stagger);\n      item.moveTo(x, y);\n    }\n  };\n\n  /**\n   * Any logic you want to do after each layout,\n   * i.e. size the container\n   */\n  proto._postLayout = function () {\n    this.resizeContainer();\n  };\n  proto.resizeContainer = function () {\n    var isResizingContainer = this._getOption('resizeContainer');\n    if (!isResizingContainer) {\n      return;\n    }\n    var size = this._getContainerSize();\n    if (size) {\n      this._setContainerMeasure(size.width, true);\n      this._setContainerMeasure(size.height, false);\n    }\n  };\n\n  /**\n   * Sets width or height of container if returned\n   * @returns {Object} size\n   *   @param {Number} width\n   *   @param {Number} height\n   */\n  proto._getContainerSize = noop;\n\n  /**\n   * @param {Number} measure - size of width or height\n   * @param {Boolean} isWidth\n   */\n  proto._setContainerMeasure = function (measure, isWidth) {\n    if (measure === undefined) {\n      return;\n    }\n    var elemSize = this.size;\n    // add padding and border width if border box\n    if (elemSize.isBorderBox) {\n      measure += isWidth ? elemSize.paddingLeft + elemSize.paddingRight + elemSize.borderLeftWidth + elemSize.borderRightWidth : elemSize.paddingBottom + elemSize.paddingTop + elemSize.borderTopWidth + elemSize.borderBottomWidth;\n    }\n    measure = Math.max(measure, 0);\n    this.element.style[isWidth ? 'width' : 'height'] = measure + 'px';\n  };\n\n  /**\n   * emit eventComplete on a collection of items events\n   * @param {String} eventName\n   * @param {Array} items - Outlayer.Items\n   */\n  proto._emitCompleteOnItems = function (eventName, items) {\n    var _this = this;\n    function onComplete() {\n      _this.dispatchEvent(eventName + 'Complete', null, [items]);\n    }\n    var count = items.length;\n    if (!items || !count) {\n      onComplete();\n      return;\n    }\n    var doneCount = 0;\n    function tick() {\n      doneCount++;\n      if (doneCount == count) {\n        onComplete();\n      }\n    }\n\n    // bind callback\n    items.forEach(function (item) {\n      item.once(eventName, tick);\n    });\n  };\n\n  /**\n   * emits events via EvEmitter and jQuery events\n   * @param {String} type - name of event\n   * @param {Event} event - original event\n   * @param {Array} args - extra arguments\n   */\n  proto.dispatchEvent = function (type, event, args) {\n    // add original event to arguments\n    var emitArgs = event ? [event].concat(args) : args;\n    this.emitEvent(type, emitArgs);\n    if (jQuery) {\n      // set this.$element\n      this.$element = this.$element || jQuery(this.element);\n      if (event) {\n        // create jQuery event\n        var $event = jQuery.Event(event);\n        $event.type = type;\n        this.$element.trigger($event, args);\n      } else {\n        // just trigger with type if no event available\n        this.$element.trigger(type, args);\n      }\n    }\n  };\n\n  // -------------------------- ignore & stamps -------------------------- //\n\n  /**\n   * keep item in collection, but do not lay it out\n   * ignored items do not get skipped in layout\n   * @param {Element} elem\n   */\n  proto.ignore = function (elem) {\n    var item = this.getItem(elem);\n    if (item) {\n      item.isIgnored = true;\n    }\n  };\n\n  /**\n   * return item to layout collection\n   * @param {Element} elem\n   */\n  proto.unignore = function (elem) {\n    var item = this.getItem(elem);\n    if (item) {\n      delete item.isIgnored;\n    }\n  };\n\n  /**\n   * adds elements to stamps\n   * @param {NodeList, Array, Element, or String} elems\n   */\n  proto.stamp = function (elems) {\n    elems = this._find(elems);\n    if (!elems) {\n      return;\n    }\n    this.stamps = this.stamps.concat(elems);\n    // ignore\n    elems.forEach(this.ignore, this);\n  };\n\n  /**\n   * removes elements to stamps\n   * @param {NodeList, Array, or Element} elems\n   */\n  proto.unstamp = function (elems) {\n    elems = this._find(elems);\n    if (!elems) {\n      return;\n    }\n    elems.forEach(function (elem) {\n      // filter out removed stamp elements\n      utils.removeFrom(this.stamps, elem);\n      this.unignore(elem);\n    }, this);\n  };\n\n  /**\n   * finds child elements\n   * @param {NodeList, Array, Element, or String} elems\n   * @returns {Array} elems\n   */\n  proto._find = function (elems) {\n    if (!elems) {\n      return;\n    }\n    // if string, use argument as selector string\n    if (typeof elems == 'string') {\n      elems = this.element.querySelectorAll(elems);\n    }\n    elems = utils.makeArray(elems);\n    return elems;\n  };\n  proto._manageStamps = function () {\n    if (!this.stamps || !this.stamps.length) {\n      return;\n    }\n    this._getBoundingRect();\n    this.stamps.forEach(this._manageStamp, this);\n  };\n\n  // update boundingLeft / Top\n  proto._getBoundingRect = function () {\n    // get bounding rect for container element\n    var boundingRect = this.element.getBoundingClientRect();\n    var size = this.size;\n    this._boundingRect = {\n      left: boundingRect.left + size.paddingLeft + size.borderLeftWidth,\n      top: boundingRect.top + size.paddingTop + size.borderTopWidth,\n      right: boundingRect.right - (size.paddingRight + size.borderRightWidth),\n      bottom: boundingRect.bottom - (size.paddingBottom + size.borderBottomWidth)\n    };\n  };\n\n  /**\n   * @param {Element} stamp\n  **/\n  proto._manageStamp = noop;\n\n  /**\n   * get x/y position of element relative to container element\n   * @param {Element} elem\n   * @returns {Object} offset - has left, top, right, bottom\n   */\n  proto._getElementOffset = function (elem) {\n    var boundingRect = elem.getBoundingClientRect();\n    var thisRect = this._boundingRect;\n    var size = getSize(elem);\n    var offset = {\n      left: boundingRect.left - thisRect.left - size.marginLeft,\n      top: boundingRect.top - thisRect.top - size.marginTop,\n      right: thisRect.right - boundingRect.right - size.marginRight,\n      bottom: thisRect.bottom - boundingRect.bottom - size.marginBottom\n    };\n    return offset;\n  };\n\n  // -------------------------- resize -------------------------- //\n\n  // enable event handlers for listeners\n  // i.e. resize -> onresize\n  proto.handleEvent = utils.handleEvent;\n\n  /**\n   * Bind layout to window resizing\n   */\n  proto.bindResize = function () {\n    window.addEventListener('resize', this);\n    this.isResizeBound = true;\n  };\n\n  /**\n   * Unbind layout to window resizing\n   */\n  proto.unbindResize = function () {\n    window.removeEventListener('resize', this);\n    this.isResizeBound = false;\n  };\n  proto.onresize = function () {\n    this.resize();\n  };\n  utils.debounceMethod(Outlayer, 'onresize', 100);\n  proto.resize = function () {\n    // don't trigger if size did not change\n    // or if resize was unbound. See #9\n    if (!this.isResizeBound || !this.needsResizeLayout()) {\n      return;\n    }\n    this.layout();\n  };\n\n  /**\n   * check if layout is needed post layout\n   * @returns Boolean\n   */\n  proto.needsResizeLayout = function () {\n    var size = getSize(this.element);\n    // check that this.size and size are there\n    // IE8 triggers resize on body size change, so they might not be\n    var hasSizes = this.size && size;\n    return hasSizes && size.innerWidth !== this.size.innerWidth;\n  };\n\n  // -------------------------- methods -------------------------- //\n\n  /**\n   * add items to Outlayer instance\n   * @param {Array or NodeList or Element} elems\n   * @returns {Array} items - Outlayer.Items\n  **/\n  proto.addItems = function (elems) {\n    var items = this._itemize(elems);\n    // add items to collection\n    if (items.length) {\n      this.items = this.items.concat(items);\n    }\n    return items;\n  };\n\n  /**\n   * Layout newly-appended item elements\n   * @param {Array or NodeList or Element} elems\n   */\n  proto.appended = function (elems) {\n    var items = this.addItems(elems);\n    if (!items.length) {\n      return;\n    }\n    // layout and reveal just the new items\n    this.layoutItems(items, true);\n    this.reveal(items);\n  };\n\n  /**\n   * Layout prepended elements\n   * @param {Array or NodeList or Element} elems\n   */\n  proto.prepended = function (elems) {\n    var items = this._itemize(elems);\n    if (!items.length) {\n      return;\n    }\n    // add items to beginning of collection\n    var previousItems = this.items.slice(0);\n    this.items = items.concat(previousItems);\n    // start new layout\n    this._resetLayout();\n    this._manageStamps();\n    // layout new stuff without transition\n    this.layoutItems(items, true);\n    this.reveal(items);\n    // layout previous items\n    this.layoutItems(previousItems);\n  };\n\n  /**\n   * reveal a collection of items\n   * @param {Array of Outlayer.Items} items\n   */\n  proto.reveal = function (items) {\n    this._emitCompleteOnItems('reveal', items);\n    if (!items || !items.length) {\n      return;\n    }\n    var stagger = this.updateStagger();\n    items.forEach(function (item, i) {\n      item.stagger(i * stagger);\n      item.reveal();\n    });\n  };\n\n  /**\n   * hide a collection of items\n   * @param {Array of Outlayer.Items} items\n   */\n  proto.hide = function (items) {\n    this._emitCompleteOnItems('hide', items);\n    if (!items || !items.length) {\n      return;\n    }\n    var stagger = this.updateStagger();\n    items.forEach(function (item, i) {\n      item.stagger(i * stagger);\n      item.hide();\n    });\n  };\n\n  /**\n   * reveal item elements\n   * @param {Array}, {Element}, {NodeList} items\n   */\n  proto.revealItemElements = function (elems) {\n    var items = this.getItems(elems);\n    this.reveal(items);\n  };\n\n  /**\n   * hide item elements\n   * @param {Array}, {Element}, {NodeList} items\n   */\n  proto.hideItemElements = function (elems) {\n    var items = this.getItems(elems);\n    this.hide(items);\n  };\n\n  /**\n   * get Outlayer.Item, given an Element\n   * @param {Element} elem\n   * @param {Function} callback\n   * @returns {Outlayer.Item} item\n   */\n  proto.getItem = function (elem) {\n    // loop through items to get the one that matches\n    for (var i = 0; i < this.items.length; i++) {\n      var item = this.items[i];\n      if (item.element == elem) {\n        // return item\n        return item;\n      }\n    }\n  };\n\n  /**\n   * get collection of Outlayer.Items, given Elements\n   * @param {Array} elems\n   * @returns {Array} items - Outlayer.Items\n   */\n  proto.getItems = function (elems) {\n    elems = utils.makeArray(elems);\n    var items = [];\n    elems.forEach(function (elem) {\n      var item = this.getItem(elem);\n      if (item) {\n        items.push(item);\n      }\n    }, this);\n    return items;\n  };\n\n  /**\n   * remove element(s) from instance and DOM\n   * @param {Array or NodeList or Element} elems\n   */\n  proto.remove = function (elems) {\n    var removeItems = this.getItems(elems);\n    this._emitCompleteOnItems('remove', removeItems);\n\n    // bail if no items to remove\n    if (!removeItems || !removeItems.length) {\n      return;\n    }\n    removeItems.forEach(function (item) {\n      item.remove();\n      // remove item from collection\n      utils.removeFrom(this.items, item);\n    }, this);\n  };\n\n  // ----- destroy ----- //\n\n  // remove and disable Outlayer instance\n  proto.destroy = function () {\n    // clean up dynamic styles\n    var style = this.element.style;\n    style.height = '';\n    style.position = '';\n    style.width = '';\n    // destroy items\n    this.items.forEach(function (item) {\n      item.destroy();\n    });\n    this.unbindResize();\n    var id = this.element.outlayerGUID;\n    delete instances[id]; // remove reference to instance by id\n    delete this.element.outlayerGUID;\n    // remove data for jQuery\n    if (jQuery) {\n      jQuery.removeData(this.element, this.constructor.namespace);\n    }\n  };\n\n  // -------------------------- data -------------------------- //\n\n  /**\n   * get Outlayer instance from element\n   * @param {Element} elem\n   * @returns {Outlayer}\n   */\n  Outlayer.data = function (elem) {\n    elem = utils.getQueryElement(elem);\n    var id = elem && elem.outlayerGUID;\n    return id && instances[id];\n  };\n\n  // -------------------------- create Outlayer class -------------------------- //\n\n  /**\n   * create a layout class\n   * @param {String} namespace\n   */\n  Outlayer.create = function (namespace, options) {\n    // sub-class Outlayer\n    var Layout = subclass(Outlayer);\n    // apply new options and compatOptions\n    Layout.defaults = utils.extend({}, Outlayer.defaults);\n    utils.extend(Layout.defaults, options);\n    Layout.compatOptions = utils.extend({}, Outlayer.compatOptions);\n    Layout.namespace = namespace;\n    Layout.data = Outlayer.data;\n\n    // sub-class Item\n    Layout.Item = subclass(Item);\n\n    // -------------------------- declarative -------------------------- //\n\n    utils.htmlInit(Layout, namespace);\n\n    // -------------------------- jQuery bridge -------------------------- //\n\n    // make into jQuery plugin\n    if (jQuery && jQuery.bridget) {\n      jQuery.bridget(namespace, Layout);\n    }\n    return Layout;\n  };\n  function subclass(Parent) {\n    function SubClass() {\n      Parent.apply(this, arguments);\n    }\n    SubClass.prototype = Object.create(Parent.prototype);\n    SubClass.prototype.constructor = SubClass;\n    return SubClass;\n  }\n\n  // ----- helpers ----- //\n\n  // how many milliseconds are in each unit\n  var msUnits = {\n    ms: 1,\n    s: 1000\n  };\n\n  // munge time-like parameter into millisecond number\n  // '0.4s' -> 40\n  function getMilliseconds(time) {\n    if (typeof time == 'number') {\n      return time;\n    }\n    var matches = time.match(/(^\\d*\\.?\\d*)(\\w*)/);\n    var num = matches && matches[1];\n    var unit = matches && matches[2];\n    if (!num.length) {\n      return 0;\n    }\n    num = parseFloat(num);\n    var mult = msUnits[unit] || 1;\n    return num * mult;\n  }\n\n  // ----- fin ----- //\n\n  // back in global\n  Outlayer.Item = Item;\n  return Outlayer;\n});", "map": {"version": 3, "names": ["window", "factory", "define", "amd", "EvEmitter", "getSize", "utils", "<PERSON><PERSON>", "module", "exports", "require", "Outlayer", "fizzyUIUtils", "console", "j<PERSON><PERSON><PERSON>", "noop", "GUID", "instances", "element", "options", "queryElement", "getQueryElement", "error", "constructor", "namespace", "$element", "extend", "defaults", "option", "id", "outlayerGUID", "_create", "isInitLayout", "_getOption", "layout", "containerStyle", "position", "initLayout", "originLeft", "originTop", "resize", "resizeContainer", "transitionDuration", "hiddenStyle", "opacity", "transform", "visibleStyle", "proto", "prototype", "opts", "oldOption", "compatOptions", "undefined", "horizontal", "layoutInstant", "reloadItems", "stamps", "stamp", "style", "canBindResize", "bindResize", "items", "_itemize", "children", "elems", "itemElems", "_filterFindItemElements", "i", "length", "elem", "item", "push", "filterFindElements", "itemSelector", "getItemElements", "map", "_resetLayout", "_manageStamps", "isInstant", "_isLayoutInited", "layoutItems", "_init", "size", "_getMeasurement", "measurement", "querySelector", "HTMLElement", "_getItemsForLayout", "_layoutItems", "_postLayout", "filter", "isIgnored", "_emitCompleteOnItems", "queue", "for<PERSON>ach", "_getItemLayoutPosition", "isLayoutInstant", "_processLayoutQueue", "x", "y", "updateStagger", "obj", "_positionItem", "stagger", "getMilliseconds", "goTo", "moveTo", "isResizingContainer", "_getContainerSize", "_setContainerMeasure", "width", "height", "measure", "isWidth", "elemSize", "isBorderBox", "paddingLeft", "paddingRight", "borderLeftWidth", "borderRightWidth", "paddingBottom", "paddingTop", "borderTopWidth", "borderBottomWidth", "Math", "max", "eventName", "_this", "onComplete", "dispatchEvent", "count", "doneCount", "tick", "once", "type", "event", "args", "emitArgs", "concat", "emitEvent", "$event", "Event", "trigger", "ignore", "getItem", "unignore", "_find", "unstamp", "removeFrom", "querySelectorAll", "makeArray", "_getBoundingRect", "_manageStamp", "boundingRect", "getBoundingClientRect", "_boundingRect", "left", "top", "right", "bottom", "_getElementOffset", "thisRect", "offset", "marginLeft", "marginTop", "marginRight", "marginBottom", "handleEvent", "addEventListener", "isResizeBound", "unbindResize", "removeEventListener", "onresize", "debounceMethod", "needsResizeLayout", "hasSizes", "innerWidth", "addItems", "appended", "reveal", "prepended", "previousItems", "slice", "hide", "revealItemElements", "getItems", "hideItemElements", "remove", "removeItems", "destroy", "removeData", "data", "create", "Layout", "subclass", "htmlInit", "bridget", "Parent", "SubClass", "apply", "arguments", "Object", "msUnits", "ms", "s", "time", "matches", "match", "num", "unit", "parseFloat", "mult"], "sources": ["C:/WolfSystemV2/WolfOldFrontEnd/node_modules/outlayer/outlayer.js"], "sourcesContent": ["/*!\n * Outlayer v2.1.1\n * the brains and guts of a layout library\n * MIT license\n */\n\n( function( window, factory ) {\n  'use strict';\n  // universal module definition\n  /* jshint strict: false */ /* globals define, module, require */\n  if ( typeof define == 'function' && define.amd ) {\n    // AMD - RequireJS\n    define( [\n        'ev-emitter/ev-emitter',\n        'get-size/get-size',\n        'fizzy-ui-utils/utils',\n        './item'\n      ],\n      function( EvEmitter, getSize, utils, Item ) {\n        return factory( window, EvEmitter, getSize, utils, Item);\n      }\n    );\n  } else if ( typeof module == 'object' && module.exports ) {\n    // CommonJS - Browserify, Webpack\n    module.exports = factory(\n      window,\n      require('ev-emitter'),\n      require('get-size'),\n      require('fizzy-ui-utils'),\n      require('./item')\n    );\n  } else {\n    // browser global\n    window.Outlayer = factory(\n      window,\n      window.EvEmitter,\n      window.getSize,\n      window.fizzyUIUtils,\n      window.Outlayer.Item\n    );\n  }\n\n}( window, function factory( window, EvEmitter, getSize, utils, Item ) {\n'use strict';\n\n// ----- vars ----- //\n\nvar console = window.console;\nvar jQuery = window.jQuery;\nvar noop = function() {};\n\n// -------------------------- Outlayer -------------------------- //\n\n// globally unique identifiers\nvar GUID = 0;\n// internal store of all Outlayer intances\nvar instances = {};\n\n\n/**\n * @param {Element, String} element\n * @param {Object} options\n * @constructor\n */\nfunction Outlayer( element, options ) {\n  var queryElement = utils.getQueryElement( element );\n  if ( !queryElement ) {\n    if ( console ) {\n      console.error( 'Bad element for ' + this.constructor.namespace +\n        ': ' + ( queryElement || element ) );\n    }\n    return;\n  }\n  this.element = queryElement;\n  // add jQuery\n  if ( jQuery ) {\n    this.$element = jQuery( this.element );\n  }\n\n  // options\n  this.options = utils.extend( {}, this.constructor.defaults );\n  this.option( options );\n\n  // add id for Outlayer.getFromElement\n  var id = ++GUID;\n  this.element.outlayerGUID = id; // expando\n  instances[ id ] = this; // associate via id\n\n  // kick it off\n  this._create();\n\n  var isInitLayout = this._getOption('initLayout');\n  if ( isInitLayout ) {\n    this.layout();\n  }\n}\n\n// settings are for internal use only\nOutlayer.namespace = 'outlayer';\nOutlayer.Item = Item;\n\n// default options\nOutlayer.defaults = {\n  containerStyle: {\n    position: 'relative'\n  },\n  initLayout: true,\n  originLeft: true,\n  originTop: true,\n  resize: true,\n  resizeContainer: true,\n  // item options\n  transitionDuration: '0.4s',\n  hiddenStyle: {\n    opacity: 0,\n    transform: 'scale(0.001)'\n  },\n  visibleStyle: {\n    opacity: 1,\n    transform: 'scale(1)'\n  }\n};\n\nvar proto = Outlayer.prototype;\n// inherit EvEmitter\nutils.extend( proto, EvEmitter.prototype );\n\n/**\n * set options\n * @param {Object} opts\n */\nproto.option = function( opts ) {\n  utils.extend( this.options, opts );\n};\n\n/**\n * get backwards compatible option value, check old name\n */\nproto._getOption = function( option ) {\n  var oldOption = this.constructor.compatOptions[ option ];\n  return oldOption && this.options[ oldOption ] !== undefined ?\n    this.options[ oldOption ] : this.options[ option ];\n};\n\nOutlayer.compatOptions = {\n  // currentName: oldName\n  initLayout: 'isInitLayout',\n  horizontal: 'isHorizontal',\n  layoutInstant: 'isLayoutInstant',\n  originLeft: 'isOriginLeft',\n  originTop: 'isOriginTop',\n  resize: 'isResizeBound',\n  resizeContainer: 'isResizingContainer'\n};\n\nproto._create = function() {\n  // get items from children\n  this.reloadItems();\n  // elements that affect layout, but are not laid out\n  this.stamps = [];\n  this.stamp( this.options.stamp );\n  // set container style\n  utils.extend( this.element.style, this.options.containerStyle );\n\n  // bind resize method\n  var canBindResize = this._getOption('resize');\n  if ( canBindResize ) {\n    this.bindResize();\n  }\n};\n\n// goes through all children again and gets bricks in proper order\nproto.reloadItems = function() {\n  // collection of item elements\n  this.items = this._itemize( this.element.children );\n};\n\n\n/**\n * turn elements into Outlayer.Items to be used in layout\n * @param {Array or NodeList or HTMLElement} elems\n * @returns {Array} items - collection of new Outlayer Items\n */\nproto._itemize = function( elems ) {\n\n  var itemElems = this._filterFindItemElements( elems );\n  var Item = this.constructor.Item;\n\n  // create new Outlayer Items for collection\n  var items = [];\n  for ( var i=0; i < itemElems.length; i++ ) {\n    var elem = itemElems[i];\n    var item = new Item( elem, this );\n    items.push( item );\n  }\n\n  return items;\n};\n\n/**\n * get item elements to be used in layout\n * @param {Array or NodeList or HTMLElement} elems\n * @returns {Array} items - item elements\n */\nproto._filterFindItemElements = function( elems ) {\n  return utils.filterFindElements( elems, this.options.itemSelector );\n};\n\n/**\n * getter method for getting item elements\n * @returns {Array} elems - collection of item elements\n */\nproto.getItemElements = function() {\n  return this.items.map( function( item ) {\n    return item.element;\n  });\n};\n\n// ----- init & layout ----- //\n\n/**\n * lays out all items\n */\nproto.layout = function() {\n  this._resetLayout();\n  this._manageStamps();\n\n  // don't animate first layout\n  var layoutInstant = this._getOption('layoutInstant');\n  var isInstant = layoutInstant !== undefined ?\n    layoutInstant : !this._isLayoutInited;\n  this.layoutItems( this.items, isInstant );\n\n  // flag for initalized\n  this._isLayoutInited = true;\n};\n\n// _init is alias for layout\nproto._init = proto.layout;\n\n/**\n * logic before any new layout\n */\nproto._resetLayout = function() {\n  this.getSize();\n};\n\n\nproto.getSize = function() {\n  this.size = getSize( this.element );\n};\n\n/**\n * get measurement from option, for columnWidth, rowHeight, gutter\n * if option is String -> get element from selector string, & get size of element\n * if option is Element -> get size of element\n * else use option as a number\n *\n * @param {String} measurement\n * @param {String} size - width or height\n * @private\n */\nproto._getMeasurement = function( measurement, size ) {\n  var option = this.options[ measurement ];\n  var elem;\n  if ( !option ) {\n    // default to 0\n    this[ measurement ] = 0;\n  } else {\n    // use option as an element\n    if ( typeof option == 'string' ) {\n      elem = this.element.querySelector( option );\n    } else if ( option instanceof HTMLElement ) {\n      elem = option;\n    }\n    // use size of element, if element\n    this[ measurement ] = elem ? getSize( elem )[ size ] : option;\n  }\n};\n\n/**\n * layout a collection of item elements\n * @api public\n */\nproto.layoutItems = function( items, isInstant ) {\n  items = this._getItemsForLayout( items );\n\n  this._layoutItems( items, isInstant );\n\n  this._postLayout();\n};\n\n/**\n * get the items to be laid out\n * you may want to skip over some items\n * @param {Array} items\n * @returns {Array} items\n */\nproto._getItemsForLayout = function( items ) {\n  return items.filter( function( item ) {\n    return !item.isIgnored;\n  });\n};\n\n/**\n * layout items\n * @param {Array} items\n * @param {Boolean} isInstant\n */\nproto._layoutItems = function( items, isInstant ) {\n  this._emitCompleteOnItems( 'layout', items );\n\n  if ( !items || !items.length ) {\n    // no items, emit event with empty array\n    return;\n  }\n\n  var queue = [];\n\n  items.forEach( function( item ) {\n    // get x/y object from method\n    var position = this._getItemLayoutPosition( item );\n    // enqueue\n    position.item = item;\n    position.isInstant = isInstant || item.isLayoutInstant;\n    queue.push( position );\n  }, this );\n\n  this._processLayoutQueue( queue );\n};\n\n/**\n * get item layout position\n * @param {Outlayer.Item} item\n * @returns {Object} x and y position\n */\nproto._getItemLayoutPosition = function( /* item */ ) {\n  return {\n    x: 0,\n    y: 0\n  };\n};\n\n/**\n * iterate over array and position each item\n * Reason being - separating this logic prevents 'layout invalidation'\n * thx @paul_irish\n * @param {Array} queue\n */\nproto._processLayoutQueue = function( queue ) {\n  this.updateStagger();\n  queue.forEach( function( obj, i ) {\n    this._positionItem( obj.item, obj.x, obj.y, obj.isInstant, i );\n  }, this );\n};\n\n// set stagger from option in milliseconds number\nproto.updateStagger = function() {\n  var stagger = this.options.stagger;\n  if ( stagger === null || stagger === undefined ) {\n    this.stagger = 0;\n    return;\n  }\n  this.stagger = getMilliseconds( stagger );\n  return this.stagger;\n};\n\n/**\n * Sets position of item in DOM\n * @param {Outlayer.Item} item\n * @param {Number} x - horizontal position\n * @param {Number} y - vertical position\n * @param {Boolean} isInstant - disables transitions\n */\nproto._positionItem = function( item, x, y, isInstant, i ) {\n  if ( isInstant ) {\n    // if not transition, just set CSS\n    item.goTo( x, y );\n  } else {\n    item.stagger( i * this.stagger );\n    item.moveTo( x, y );\n  }\n};\n\n/**\n * Any logic you want to do after each layout,\n * i.e. size the container\n */\nproto._postLayout = function() {\n  this.resizeContainer();\n};\n\nproto.resizeContainer = function() {\n  var isResizingContainer = this._getOption('resizeContainer');\n  if ( !isResizingContainer ) {\n    return;\n  }\n  var size = this._getContainerSize();\n  if ( size ) {\n    this._setContainerMeasure( size.width, true );\n    this._setContainerMeasure( size.height, false );\n  }\n};\n\n/**\n * Sets width or height of container if returned\n * @returns {Object} size\n *   @param {Number} width\n *   @param {Number} height\n */\nproto._getContainerSize = noop;\n\n/**\n * @param {Number} measure - size of width or height\n * @param {Boolean} isWidth\n */\nproto._setContainerMeasure = function( measure, isWidth ) {\n  if ( measure === undefined ) {\n    return;\n  }\n\n  var elemSize = this.size;\n  // add padding and border width if border box\n  if ( elemSize.isBorderBox ) {\n    measure += isWidth ? elemSize.paddingLeft + elemSize.paddingRight +\n      elemSize.borderLeftWidth + elemSize.borderRightWidth :\n      elemSize.paddingBottom + elemSize.paddingTop +\n      elemSize.borderTopWidth + elemSize.borderBottomWidth;\n  }\n\n  measure = Math.max( measure, 0 );\n  this.element.style[ isWidth ? 'width' : 'height' ] = measure + 'px';\n};\n\n/**\n * emit eventComplete on a collection of items events\n * @param {String} eventName\n * @param {Array} items - Outlayer.Items\n */\nproto._emitCompleteOnItems = function( eventName, items ) {\n  var _this = this;\n  function onComplete() {\n    _this.dispatchEvent( eventName + 'Complete', null, [ items ] );\n  }\n\n  var count = items.length;\n  if ( !items || !count ) {\n    onComplete();\n    return;\n  }\n\n  var doneCount = 0;\n  function tick() {\n    doneCount++;\n    if ( doneCount == count ) {\n      onComplete();\n    }\n  }\n\n  // bind callback\n  items.forEach( function( item ) {\n    item.once( eventName, tick );\n  });\n};\n\n/**\n * emits events via EvEmitter and jQuery events\n * @param {String} type - name of event\n * @param {Event} event - original event\n * @param {Array} args - extra arguments\n */\nproto.dispatchEvent = function( type, event, args ) {\n  // add original event to arguments\n  var emitArgs = event ? [ event ].concat( args ) : args;\n  this.emitEvent( type, emitArgs );\n\n  if ( jQuery ) {\n    // set this.$element\n    this.$element = this.$element || jQuery( this.element );\n    if ( event ) {\n      // create jQuery event\n      var $event = jQuery.Event( event );\n      $event.type = type;\n      this.$element.trigger( $event, args );\n    } else {\n      // just trigger with type if no event available\n      this.$element.trigger( type, args );\n    }\n  }\n};\n\n// -------------------------- ignore & stamps -------------------------- //\n\n\n/**\n * keep item in collection, but do not lay it out\n * ignored items do not get skipped in layout\n * @param {Element} elem\n */\nproto.ignore = function( elem ) {\n  var item = this.getItem( elem );\n  if ( item ) {\n    item.isIgnored = true;\n  }\n};\n\n/**\n * return item to layout collection\n * @param {Element} elem\n */\nproto.unignore = function( elem ) {\n  var item = this.getItem( elem );\n  if ( item ) {\n    delete item.isIgnored;\n  }\n};\n\n/**\n * adds elements to stamps\n * @param {NodeList, Array, Element, or String} elems\n */\nproto.stamp = function( elems ) {\n  elems = this._find( elems );\n  if ( !elems ) {\n    return;\n  }\n\n  this.stamps = this.stamps.concat( elems );\n  // ignore\n  elems.forEach( this.ignore, this );\n};\n\n/**\n * removes elements to stamps\n * @param {NodeList, Array, or Element} elems\n */\nproto.unstamp = function( elems ) {\n  elems = this._find( elems );\n  if ( !elems ){\n    return;\n  }\n\n  elems.forEach( function( elem ) {\n    // filter out removed stamp elements\n    utils.removeFrom( this.stamps, elem );\n    this.unignore( elem );\n  }, this );\n};\n\n/**\n * finds child elements\n * @param {NodeList, Array, Element, or String} elems\n * @returns {Array} elems\n */\nproto._find = function( elems ) {\n  if ( !elems ) {\n    return;\n  }\n  // if string, use argument as selector string\n  if ( typeof elems == 'string' ) {\n    elems = this.element.querySelectorAll( elems );\n  }\n  elems = utils.makeArray( elems );\n  return elems;\n};\n\nproto._manageStamps = function() {\n  if ( !this.stamps || !this.stamps.length ) {\n    return;\n  }\n\n  this._getBoundingRect();\n\n  this.stamps.forEach( this._manageStamp, this );\n};\n\n// update boundingLeft / Top\nproto._getBoundingRect = function() {\n  // get bounding rect for container element\n  var boundingRect = this.element.getBoundingClientRect();\n  var size = this.size;\n  this._boundingRect = {\n    left: boundingRect.left + size.paddingLeft + size.borderLeftWidth,\n    top: boundingRect.top + size.paddingTop + size.borderTopWidth,\n    right: boundingRect.right - ( size.paddingRight + size.borderRightWidth ),\n    bottom: boundingRect.bottom - ( size.paddingBottom + size.borderBottomWidth )\n  };\n};\n\n/**\n * @param {Element} stamp\n**/\nproto._manageStamp = noop;\n\n/**\n * get x/y position of element relative to container element\n * @param {Element} elem\n * @returns {Object} offset - has left, top, right, bottom\n */\nproto._getElementOffset = function( elem ) {\n  var boundingRect = elem.getBoundingClientRect();\n  var thisRect = this._boundingRect;\n  var size = getSize( elem );\n  var offset = {\n    left: boundingRect.left - thisRect.left - size.marginLeft,\n    top: boundingRect.top - thisRect.top - size.marginTop,\n    right: thisRect.right - boundingRect.right - size.marginRight,\n    bottom: thisRect.bottom - boundingRect.bottom - size.marginBottom\n  };\n  return offset;\n};\n\n// -------------------------- resize -------------------------- //\n\n// enable event handlers for listeners\n// i.e. resize -> onresize\nproto.handleEvent = utils.handleEvent;\n\n/**\n * Bind layout to window resizing\n */\nproto.bindResize = function() {\n  window.addEventListener( 'resize', this );\n  this.isResizeBound = true;\n};\n\n/**\n * Unbind layout to window resizing\n */\nproto.unbindResize = function() {\n  window.removeEventListener( 'resize', this );\n  this.isResizeBound = false;\n};\n\nproto.onresize = function() {\n  this.resize();\n};\n\nutils.debounceMethod( Outlayer, 'onresize', 100 );\n\nproto.resize = function() {\n  // don't trigger if size did not change\n  // or if resize was unbound. See #9\n  if ( !this.isResizeBound || !this.needsResizeLayout() ) {\n    return;\n  }\n\n  this.layout();\n};\n\n/**\n * check if layout is needed post layout\n * @returns Boolean\n */\nproto.needsResizeLayout = function() {\n  var size = getSize( this.element );\n  // check that this.size and size are there\n  // IE8 triggers resize on body size change, so they might not be\n  var hasSizes = this.size && size;\n  return hasSizes && size.innerWidth !== this.size.innerWidth;\n};\n\n// -------------------------- methods -------------------------- //\n\n/**\n * add items to Outlayer instance\n * @param {Array or NodeList or Element} elems\n * @returns {Array} items - Outlayer.Items\n**/\nproto.addItems = function( elems ) {\n  var items = this._itemize( elems );\n  // add items to collection\n  if ( items.length ) {\n    this.items = this.items.concat( items );\n  }\n  return items;\n};\n\n/**\n * Layout newly-appended item elements\n * @param {Array or NodeList or Element} elems\n */\nproto.appended = function( elems ) {\n  var items = this.addItems( elems );\n  if ( !items.length ) {\n    return;\n  }\n  // layout and reveal just the new items\n  this.layoutItems( items, true );\n  this.reveal( items );\n};\n\n/**\n * Layout prepended elements\n * @param {Array or NodeList or Element} elems\n */\nproto.prepended = function( elems ) {\n  var items = this._itemize( elems );\n  if ( !items.length ) {\n    return;\n  }\n  // add items to beginning of collection\n  var previousItems = this.items.slice(0);\n  this.items = items.concat( previousItems );\n  // start new layout\n  this._resetLayout();\n  this._manageStamps();\n  // layout new stuff without transition\n  this.layoutItems( items, true );\n  this.reveal( items );\n  // layout previous items\n  this.layoutItems( previousItems );\n};\n\n/**\n * reveal a collection of items\n * @param {Array of Outlayer.Items} items\n */\nproto.reveal = function( items ) {\n  this._emitCompleteOnItems( 'reveal', items );\n  if ( !items || !items.length ) {\n    return;\n  }\n  var stagger = this.updateStagger();\n  items.forEach( function( item, i ) {\n    item.stagger( i * stagger );\n    item.reveal();\n  });\n};\n\n/**\n * hide a collection of items\n * @param {Array of Outlayer.Items} items\n */\nproto.hide = function( items ) {\n  this._emitCompleteOnItems( 'hide', items );\n  if ( !items || !items.length ) {\n    return;\n  }\n  var stagger = this.updateStagger();\n  items.forEach( function( item, i ) {\n    item.stagger( i * stagger );\n    item.hide();\n  });\n};\n\n/**\n * reveal item elements\n * @param {Array}, {Element}, {NodeList} items\n */\nproto.revealItemElements = function( elems ) {\n  var items = this.getItems( elems );\n  this.reveal( items );\n};\n\n/**\n * hide item elements\n * @param {Array}, {Element}, {NodeList} items\n */\nproto.hideItemElements = function( elems ) {\n  var items = this.getItems( elems );\n  this.hide( items );\n};\n\n/**\n * get Outlayer.Item, given an Element\n * @param {Element} elem\n * @param {Function} callback\n * @returns {Outlayer.Item} item\n */\nproto.getItem = function( elem ) {\n  // loop through items to get the one that matches\n  for ( var i=0; i < this.items.length; i++ ) {\n    var item = this.items[i];\n    if ( item.element == elem ) {\n      // return item\n      return item;\n    }\n  }\n};\n\n/**\n * get collection of Outlayer.Items, given Elements\n * @param {Array} elems\n * @returns {Array} items - Outlayer.Items\n */\nproto.getItems = function( elems ) {\n  elems = utils.makeArray( elems );\n  var items = [];\n  elems.forEach( function( elem ) {\n    var item = this.getItem( elem );\n    if ( item ) {\n      items.push( item );\n    }\n  }, this );\n\n  return items;\n};\n\n/**\n * remove element(s) from instance and DOM\n * @param {Array or NodeList or Element} elems\n */\nproto.remove = function( elems ) {\n  var removeItems = this.getItems( elems );\n\n  this._emitCompleteOnItems( 'remove', removeItems );\n\n  // bail if no items to remove\n  if ( !removeItems || !removeItems.length ) {\n    return;\n  }\n\n  removeItems.forEach( function( item ) {\n    item.remove();\n    // remove item from collection\n    utils.removeFrom( this.items, item );\n  }, this );\n};\n\n// ----- destroy ----- //\n\n// remove and disable Outlayer instance\nproto.destroy = function() {\n  // clean up dynamic styles\n  var style = this.element.style;\n  style.height = '';\n  style.position = '';\n  style.width = '';\n  // destroy items\n  this.items.forEach( function( item ) {\n    item.destroy();\n  });\n\n  this.unbindResize();\n\n  var id = this.element.outlayerGUID;\n  delete instances[ id ]; // remove reference to instance by id\n  delete this.element.outlayerGUID;\n  // remove data for jQuery\n  if ( jQuery ) {\n    jQuery.removeData( this.element, this.constructor.namespace );\n  }\n\n};\n\n// -------------------------- data -------------------------- //\n\n/**\n * get Outlayer instance from element\n * @param {Element} elem\n * @returns {Outlayer}\n */\nOutlayer.data = function( elem ) {\n  elem = utils.getQueryElement( elem );\n  var id = elem && elem.outlayerGUID;\n  return id && instances[ id ];\n};\n\n\n// -------------------------- create Outlayer class -------------------------- //\n\n/**\n * create a layout class\n * @param {String} namespace\n */\nOutlayer.create = function( namespace, options ) {\n  // sub-class Outlayer\n  var Layout = subclass( Outlayer );\n  // apply new options and compatOptions\n  Layout.defaults = utils.extend( {}, Outlayer.defaults );\n  utils.extend( Layout.defaults, options );\n  Layout.compatOptions = utils.extend( {}, Outlayer.compatOptions  );\n\n  Layout.namespace = namespace;\n\n  Layout.data = Outlayer.data;\n\n  // sub-class Item\n  Layout.Item = subclass( Item );\n\n  // -------------------------- declarative -------------------------- //\n\n  utils.htmlInit( Layout, namespace );\n\n  // -------------------------- jQuery bridge -------------------------- //\n\n  // make into jQuery plugin\n  if ( jQuery && jQuery.bridget ) {\n    jQuery.bridget( namespace, Layout );\n  }\n\n  return Layout;\n};\n\nfunction subclass( Parent ) {\n  function SubClass() {\n    Parent.apply( this, arguments );\n  }\n\n  SubClass.prototype = Object.create( Parent.prototype );\n  SubClass.prototype.constructor = SubClass;\n\n  return SubClass;\n}\n\n// ----- helpers ----- //\n\n// how many milliseconds are in each unit\nvar msUnits = {\n  ms: 1,\n  s: 1000\n};\n\n// munge time-like parameter into millisecond number\n// '0.4s' -> 40\nfunction getMilliseconds( time ) {\n  if ( typeof time == 'number' ) {\n    return time;\n  }\n  var matches = time.match( /(^\\d*\\.?\\d*)(\\w*)/ );\n  var num = matches && matches[1];\n  var unit = matches && matches[2];\n  if ( !num.length ) {\n    return 0;\n  }\n  num = parseFloat( num );\n  var mult = msUnits[ unit ] || 1;\n  return num * mult;\n}\n\n// ----- fin ----- //\n\n// back in global\nOutlayer.Item = Item;\n\nreturn Outlayer;\n\n}));\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;;AAEE,WAAUA,MAAM,EAAEC,OAAO,EAAG;EAC5B,YAAY;;EACZ;EACA,2BAA2B;EAC3B,IAAK,OAAOC,MAAM,IAAI,UAAU,IAAIA,MAAM,CAACC,GAAG,EAAG;IAC/C;IACAD,MAAM,CAAE,CACJ,uBAAuB,EACvB,mBAAmB,EACnB,sBAAsB,EACtB,QAAQ,CACT,EACD,UAAUE,SAAS,EAAEC,OAAO,EAAEC,KAAK,EAAEC,IAAI,EAAG;MAC1C,OAAON,OAAO,CAAED,MAAM,EAAEI,SAAS,EAAEC,OAAO,EAAEC,KAAK,EAAEC,IAAI,CAAC;IAC1D,CACF,CAAC;EACH,CAAC,MAAM,IAAK,OAAOC,MAAM,IAAI,QAAQ,IAAIA,MAAM,CAACC,OAAO,EAAG;IACxD;IACAD,MAAM,CAACC,OAAO,GAAGR,OAAO,CACtBD,MAAM,EACNU,OAAO,CAAC,YAAY,CAAC,EACrBA,OAAO,CAAC,UAAU,CAAC,EACnBA,OAAO,CAAC,gBAAgB,CAAC,EACzBA,OAAO,CAAC,QAAQ,CAClB,CAAC;EACH,CAAC,MAAM;IACL;IACAV,MAAM,CAACW,QAAQ,GAAGV,OAAO,CACvBD,MAAM,EACNA,MAAM,CAACI,SAAS,EAChBJ,MAAM,CAACK,OAAO,EACdL,MAAM,CAACY,YAAY,EACnBZ,MAAM,CAACW,QAAQ,CAACJ,IAClB,CAAC;EACH;AAEF,CAAC,EAAEP,MAAM,EAAE,SAASC,OAAOA,CAAED,MAAM,EAAEI,SAAS,EAAEC,OAAO,EAAEC,KAAK,EAAEC,IAAI,EAAG;EACvE,YAAY;;EAEZ;EAEA,IAAIM,OAAO,GAAGb,MAAM,CAACa,OAAO;EAC5B,IAAIC,MAAM,GAAGd,MAAM,CAACc,MAAM;EAC1B,IAAIC,IAAI,GAAG,SAAAA,CAAA,EAAW,CAAC,CAAC;;EAExB;;EAEA;EACA,IAAIC,IAAI,GAAG,CAAC;EACZ;EACA,IAAIC,SAAS,GAAG,CAAC,CAAC;;EAGlB;AACA;AACA;AACA;AACA;EACA,SAASN,QAAQA,CAAEO,OAAO,EAAEC,OAAO,EAAG;IACpC,IAAIC,YAAY,GAAGd,KAAK,CAACe,eAAe,CAAEH,OAAQ,CAAC;IACnD,IAAK,CAACE,YAAY,EAAG;MACnB,IAAKP,OAAO,EAAG;QACbA,OAAO,CAACS,KAAK,CAAE,kBAAkB,GAAG,IAAI,CAACC,WAAW,CAACC,SAAS,GAC5D,IAAI,IAAKJ,YAAY,IAAIF,OAAO,CAAG,CAAC;MACxC;MACA;IACF;IACA,IAAI,CAACA,OAAO,GAAGE,YAAY;IAC3B;IACA,IAAKN,MAAM,EAAG;MACZ,IAAI,CAACW,QAAQ,GAAGX,MAAM,CAAE,IAAI,CAACI,OAAQ,CAAC;IACxC;;IAEA;IACA,IAAI,CAACC,OAAO,GAAGb,KAAK,CAACoB,MAAM,CAAE,CAAC,CAAC,EAAE,IAAI,CAACH,WAAW,CAACI,QAAS,CAAC;IAC5D,IAAI,CAACC,MAAM,CAAET,OAAQ,CAAC;;IAEtB;IACA,IAAIU,EAAE,GAAG,EAAEb,IAAI;IACf,IAAI,CAACE,OAAO,CAACY,YAAY,GAAGD,EAAE,CAAC,CAAC;IAChCZ,SAAS,CAAEY,EAAE,CAAE,GAAG,IAAI,CAAC,CAAC;;IAExB;IACA,IAAI,CAACE,OAAO,CAAC,CAAC;IAEd,IAAIC,YAAY,GAAG,IAAI,CAACC,UAAU,CAAC,YAAY,CAAC;IAChD,IAAKD,YAAY,EAAG;MAClB,IAAI,CAACE,MAAM,CAAC,CAAC;IACf;EACF;;EAEA;EACAvB,QAAQ,CAACa,SAAS,GAAG,UAAU;EAC/Bb,QAAQ,CAACJ,IAAI,GAAGA,IAAI;;EAEpB;EACAI,QAAQ,CAACgB,QAAQ,GAAG;IAClBQ,cAAc,EAAE;MACdC,QAAQ,EAAE;IACZ,CAAC;IACDC,UAAU,EAAE,IAAI;IAChBC,UAAU,EAAE,IAAI;IAChBC,SAAS,EAAE,IAAI;IACfC,MAAM,EAAE,IAAI;IACZC,eAAe,EAAE,IAAI;IACrB;IACAC,kBAAkB,EAAE,MAAM;IAC1BC,WAAW,EAAE;MACXC,OAAO,EAAE,CAAC;MACVC,SAAS,EAAE;IACb,CAAC;IACDC,YAAY,EAAE;MACZF,OAAO,EAAE,CAAC;MACVC,SAAS,EAAE;IACb;EACF,CAAC;EAED,IAAIE,KAAK,GAAGpC,QAAQ,CAACqC,SAAS;EAC9B;EACA1C,KAAK,CAACoB,MAAM,CAAEqB,KAAK,EAAE3C,SAAS,CAAC4C,SAAU,CAAC;;EAE1C;AACA;AACA;AACA;EACAD,KAAK,CAACnB,MAAM,GAAG,UAAUqB,IAAI,EAAG;IAC9B3C,KAAK,CAACoB,MAAM,CAAE,IAAI,CAACP,OAAO,EAAE8B,IAAK,CAAC;EACpC,CAAC;;EAED;AACA;AACA;EACAF,KAAK,CAACd,UAAU,GAAG,UAAUL,MAAM,EAAG;IACpC,IAAIsB,SAAS,GAAG,IAAI,CAAC3B,WAAW,CAAC4B,aAAa,CAAEvB,MAAM,CAAE;IACxD,OAAOsB,SAAS,IAAI,IAAI,CAAC/B,OAAO,CAAE+B,SAAS,CAAE,KAAKE,SAAS,GACzD,IAAI,CAACjC,OAAO,CAAE+B,SAAS,CAAE,GAAG,IAAI,CAAC/B,OAAO,CAAES,MAAM,CAAE;EACtD,CAAC;EAEDjB,QAAQ,CAACwC,aAAa,GAAG;IACvB;IACAd,UAAU,EAAE,cAAc;IAC1BgB,UAAU,EAAE,cAAc;IAC1BC,aAAa,EAAE,iBAAiB;IAChChB,UAAU,EAAE,cAAc;IAC1BC,SAAS,EAAE,aAAa;IACxBC,MAAM,EAAE,eAAe;IACvBC,eAAe,EAAE;EACnB,CAAC;EAEDM,KAAK,CAAChB,OAAO,GAAG,YAAW;IACzB;IACA,IAAI,CAACwB,WAAW,CAAC,CAAC;IAClB;IACA,IAAI,CAACC,MAAM,GAAG,EAAE;IAChB,IAAI,CAACC,KAAK,CAAE,IAAI,CAACtC,OAAO,CAACsC,KAAM,CAAC;IAChC;IACAnD,KAAK,CAACoB,MAAM,CAAE,IAAI,CAACR,OAAO,CAACwC,KAAK,EAAE,IAAI,CAACvC,OAAO,CAACgB,cAAe,CAAC;;IAE/D;IACA,IAAIwB,aAAa,GAAG,IAAI,CAAC1B,UAAU,CAAC,QAAQ,CAAC;IAC7C,IAAK0B,aAAa,EAAG;MACnB,IAAI,CAACC,UAAU,CAAC,CAAC;IACnB;EACF,CAAC;;EAED;EACAb,KAAK,CAACQ,WAAW,GAAG,YAAW;IAC7B;IACA,IAAI,CAACM,KAAK,GAAG,IAAI,CAACC,QAAQ,CAAE,IAAI,CAAC5C,OAAO,CAAC6C,QAAS,CAAC;EACrD,CAAC;;EAGD;AACA;AACA;AACA;AACA;EACAhB,KAAK,CAACe,QAAQ,GAAG,UAAUE,KAAK,EAAG;IAEjC,IAAIC,SAAS,GAAG,IAAI,CAACC,uBAAuB,CAAEF,KAAM,CAAC;IACrD,IAAIzD,IAAI,GAAG,IAAI,CAACgB,WAAW,CAAChB,IAAI;;IAEhC;IACA,IAAIsD,KAAK,GAAG,EAAE;IACd,KAAM,IAAIM,CAAC,GAAC,CAAC,EAAEA,CAAC,GAAGF,SAAS,CAACG,MAAM,EAAED,CAAC,EAAE,EAAG;MACzC,IAAIE,IAAI,GAAGJ,SAAS,CAACE,CAAC,CAAC;MACvB,IAAIG,IAAI,GAAG,IAAI/D,IAAI,CAAE8D,IAAI,EAAE,IAAK,CAAC;MACjCR,KAAK,CAACU,IAAI,CAAED,IAAK,CAAC;IACpB;IAEA,OAAOT,KAAK;EACd,CAAC;;EAED;AACA;AACA;AACA;AACA;EACAd,KAAK,CAACmB,uBAAuB,GAAG,UAAUF,KAAK,EAAG;IAChD,OAAO1D,KAAK,CAACkE,kBAAkB,CAAER,KAAK,EAAE,IAAI,CAAC7C,OAAO,CAACsD,YAAa,CAAC;EACrE,CAAC;;EAED;AACA;AACA;AACA;EACA1B,KAAK,CAAC2B,eAAe,GAAG,YAAW;IACjC,OAAO,IAAI,CAACb,KAAK,CAACc,GAAG,CAAE,UAAUL,IAAI,EAAG;MACtC,OAAOA,IAAI,CAACpD,OAAO;IACrB,CAAC,CAAC;EACJ,CAAC;;EAED;;EAEA;AACA;AACA;EACA6B,KAAK,CAACb,MAAM,GAAG,YAAW;IACxB,IAAI,CAAC0C,YAAY,CAAC,CAAC;IACnB,IAAI,CAACC,aAAa,CAAC,CAAC;;IAEpB;IACA,IAAIvB,aAAa,GAAG,IAAI,CAACrB,UAAU,CAAC,eAAe,CAAC;IACpD,IAAI6C,SAAS,GAAGxB,aAAa,KAAKF,SAAS,GACzCE,aAAa,GAAG,CAAC,IAAI,CAACyB,eAAe;IACvC,IAAI,CAACC,WAAW,CAAE,IAAI,CAACnB,KAAK,EAAEiB,SAAU,CAAC;;IAEzC;IACA,IAAI,CAACC,eAAe,GAAG,IAAI;EAC7B,CAAC;;EAED;EACAhC,KAAK,CAACkC,KAAK,GAAGlC,KAAK,CAACb,MAAM;;EAE1B;AACA;AACA;EACAa,KAAK,CAAC6B,YAAY,GAAG,YAAW;IAC9B,IAAI,CAACvE,OAAO,CAAC,CAAC;EAChB,CAAC;EAGD0C,KAAK,CAAC1C,OAAO,GAAG,YAAW;IACzB,IAAI,CAAC6E,IAAI,GAAG7E,OAAO,CAAE,IAAI,CAACa,OAAQ,CAAC;EACrC,CAAC;;EAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACA6B,KAAK,CAACoC,eAAe,GAAG,UAAUC,WAAW,EAAEF,IAAI,EAAG;IACpD,IAAItD,MAAM,GAAG,IAAI,CAACT,OAAO,CAAEiE,WAAW,CAAE;IACxC,IAAIf,IAAI;IACR,IAAK,CAACzC,MAAM,EAAG;MACb;MACA,IAAI,CAAEwD,WAAW,CAAE,GAAG,CAAC;IACzB,CAAC,MAAM;MACL;MACA,IAAK,OAAOxD,MAAM,IAAI,QAAQ,EAAG;QAC/ByC,IAAI,GAAG,IAAI,CAACnD,OAAO,CAACmE,aAAa,CAAEzD,MAAO,CAAC;MAC7C,CAAC,MAAM,IAAKA,MAAM,YAAY0D,WAAW,EAAG;QAC1CjB,IAAI,GAAGzC,MAAM;MACf;MACA;MACA,IAAI,CAAEwD,WAAW,CAAE,GAAGf,IAAI,GAAGhE,OAAO,CAAEgE,IAAK,CAAC,CAAEa,IAAI,CAAE,GAAGtD,MAAM;IAC/D;EACF,CAAC;;EAED;AACA;AACA;AACA;EACAmB,KAAK,CAACiC,WAAW,GAAG,UAAUnB,KAAK,EAAEiB,SAAS,EAAG;IAC/CjB,KAAK,GAAG,IAAI,CAAC0B,kBAAkB,CAAE1B,KAAM,CAAC;IAExC,IAAI,CAAC2B,YAAY,CAAE3B,KAAK,EAAEiB,SAAU,CAAC;IAErC,IAAI,CAACW,WAAW,CAAC,CAAC;EACpB,CAAC;;EAED;AACA;AACA;AACA;AACA;AACA;EACA1C,KAAK,CAACwC,kBAAkB,GAAG,UAAU1B,KAAK,EAAG;IAC3C,OAAOA,KAAK,CAAC6B,MAAM,CAAE,UAAUpB,IAAI,EAAG;MACpC,OAAO,CAACA,IAAI,CAACqB,SAAS;IACxB,CAAC,CAAC;EACJ,CAAC;;EAED;AACA;AACA;AACA;AACA;EACA5C,KAAK,CAACyC,YAAY,GAAG,UAAU3B,KAAK,EAAEiB,SAAS,EAAG;IAChD,IAAI,CAACc,oBAAoB,CAAE,QAAQ,EAAE/B,KAAM,CAAC;IAE5C,IAAK,CAACA,KAAK,IAAI,CAACA,KAAK,CAACO,MAAM,EAAG;MAC7B;MACA;IACF;IAEA,IAAIyB,KAAK,GAAG,EAAE;IAEdhC,KAAK,CAACiC,OAAO,CAAE,UAAUxB,IAAI,EAAG;MAC9B;MACA,IAAIlC,QAAQ,GAAG,IAAI,CAAC2D,sBAAsB,CAAEzB,IAAK,CAAC;MAClD;MACAlC,QAAQ,CAACkC,IAAI,GAAGA,IAAI;MACpBlC,QAAQ,CAAC0C,SAAS,GAAGA,SAAS,IAAIR,IAAI,CAAC0B,eAAe;MACtDH,KAAK,CAACtB,IAAI,CAAEnC,QAAS,CAAC;IACxB,CAAC,EAAE,IAAK,CAAC;IAET,IAAI,CAAC6D,mBAAmB,CAAEJ,KAAM,CAAC;EACnC,CAAC;;EAED;AACA;AACA;AACA;AACA;EACA9C,KAAK,CAACgD,sBAAsB,GAAG,SAAU;EAAA,GAAa;IACpD,OAAO;MACLG,CAAC,EAAE,CAAC;MACJC,CAAC,EAAE;IACL,CAAC;EACH,CAAC;;EAED;AACA;AACA;AACA;AACA;AACA;EACApD,KAAK,CAACkD,mBAAmB,GAAG,UAAUJ,KAAK,EAAG;IAC5C,IAAI,CAACO,aAAa,CAAC,CAAC;IACpBP,KAAK,CAACC,OAAO,CAAE,UAAUO,GAAG,EAAElC,CAAC,EAAG;MAChC,IAAI,CAACmC,aAAa,CAAED,GAAG,CAAC/B,IAAI,EAAE+B,GAAG,CAACH,CAAC,EAAEG,GAAG,CAACF,CAAC,EAAEE,GAAG,CAACvB,SAAS,EAAEX,CAAE,CAAC;IAChE,CAAC,EAAE,IAAK,CAAC;EACX,CAAC;;EAED;EACApB,KAAK,CAACqD,aAAa,GAAG,YAAW;IAC/B,IAAIG,OAAO,GAAG,IAAI,CAACpF,OAAO,CAACoF,OAAO;IAClC,IAAKA,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAKnD,SAAS,EAAG;MAC/C,IAAI,CAACmD,OAAO,GAAG,CAAC;MAChB;IACF;IACA,IAAI,CAACA,OAAO,GAAGC,eAAe,CAAED,OAAQ,CAAC;IACzC,OAAO,IAAI,CAACA,OAAO;EACrB,CAAC;;EAED;AACA;AACA;AACA;AACA;AACA;AACA;EACAxD,KAAK,CAACuD,aAAa,GAAG,UAAUhC,IAAI,EAAE4B,CAAC,EAAEC,CAAC,EAAErB,SAAS,EAAEX,CAAC,EAAG;IACzD,IAAKW,SAAS,EAAG;MACf;MACAR,IAAI,CAACmC,IAAI,CAAEP,CAAC,EAAEC,CAAE,CAAC;IACnB,CAAC,MAAM;MACL7B,IAAI,CAACiC,OAAO,CAAEpC,CAAC,GAAG,IAAI,CAACoC,OAAQ,CAAC;MAChCjC,IAAI,CAACoC,MAAM,CAAER,CAAC,EAAEC,CAAE,CAAC;IACrB;EACF,CAAC;;EAED;AACA;AACA;AACA;EACApD,KAAK,CAAC0C,WAAW,GAAG,YAAW;IAC7B,IAAI,CAAChD,eAAe,CAAC,CAAC;EACxB,CAAC;EAEDM,KAAK,CAACN,eAAe,GAAG,YAAW;IACjC,IAAIkE,mBAAmB,GAAG,IAAI,CAAC1E,UAAU,CAAC,iBAAiB,CAAC;IAC5D,IAAK,CAAC0E,mBAAmB,EAAG;MAC1B;IACF;IACA,IAAIzB,IAAI,GAAG,IAAI,CAAC0B,iBAAiB,CAAC,CAAC;IACnC,IAAK1B,IAAI,EAAG;MACV,IAAI,CAAC2B,oBAAoB,CAAE3B,IAAI,CAAC4B,KAAK,EAAE,IAAK,CAAC;MAC7C,IAAI,CAACD,oBAAoB,CAAE3B,IAAI,CAAC6B,MAAM,EAAE,KAAM,CAAC;IACjD;EACF,CAAC;;EAED;AACA;AACA;AACA;AACA;AACA;EACAhE,KAAK,CAAC6D,iBAAiB,GAAG7F,IAAI;;EAE9B;AACA;AACA;AACA;EACAgC,KAAK,CAAC8D,oBAAoB,GAAG,UAAUG,OAAO,EAAEC,OAAO,EAAG;IACxD,IAAKD,OAAO,KAAK5D,SAAS,EAAG;MAC3B;IACF;IAEA,IAAI8D,QAAQ,GAAG,IAAI,CAAChC,IAAI;IACxB;IACA,IAAKgC,QAAQ,CAACC,WAAW,EAAG;MAC1BH,OAAO,IAAIC,OAAO,GAAGC,QAAQ,CAACE,WAAW,GAAGF,QAAQ,CAACG,YAAY,GAC/DH,QAAQ,CAACI,eAAe,GAAGJ,QAAQ,CAACK,gBAAgB,GACpDL,QAAQ,CAACM,aAAa,GAAGN,QAAQ,CAACO,UAAU,GAC5CP,QAAQ,CAACQ,cAAc,GAAGR,QAAQ,CAACS,iBAAiB;IACxD;IAEAX,OAAO,GAAGY,IAAI,CAACC,GAAG,CAAEb,OAAO,EAAE,CAAE,CAAC;IAChC,IAAI,CAAC9F,OAAO,CAACwC,KAAK,CAAEuD,OAAO,GAAG,OAAO,GAAG,QAAQ,CAAE,GAAGD,OAAO,GAAG,IAAI;EACrE,CAAC;;EAED;AACA;AACA;AACA;AACA;EACAjE,KAAK,CAAC6C,oBAAoB,GAAG,UAAUkC,SAAS,EAAEjE,KAAK,EAAG;IACxD,IAAIkE,KAAK,GAAG,IAAI;IAChB,SAASC,UAAUA,CAAA,EAAG;MACpBD,KAAK,CAACE,aAAa,CAAEH,SAAS,GAAG,UAAU,EAAE,IAAI,EAAE,CAAEjE,KAAK,CAAG,CAAC;IAChE;IAEA,IAAIqE,KAAK,GAAGrE,KAAK,CAACO,MAAM;IACxB,IAAK,CAACP,KAAK,IAAI,CAACqE,KAAK,EAAG;MACtBF,UAAU,CAAC,CAAC;MACZ;IACF;IAEA,IAAIG,SAAS,GAAG,CAAC;IACjB,SAASC,IAAIA,CAAA,EAAG;MACdD,SAAS,EAAE;MACX,IAAKA,SAAS,IAAID,KAAK,EAAG;QACxBF,UAAU,CAAC,CAAC;MACd;IACF;;IAEA;IACAnE,KAAK,CAACiC,OAAO,CAAE,UAAUxB,IAAI,EAAG;MAC9BA,IAAI,CAAC+D,IAAI,CAAEP,SAAS,EAAEM,IAAK,CAAC;IAC9B,CAAC,CAAC;EACJ,CAAC;;EAED;AACA;AACA;AACA;AACA;AACA;EACArF,KAAK,CAACkF,aAAa,GAAG,UAAUK,IAAI,EAAEC,KAAK,EAAEC,IAAI,EAAG;IAClD;IACA,IAAIC,QAAQ,GAAGF,KAAK,GAAG,CAAEA,KAAK,CAAE,CAACG,MAAM,CAAEF,IAAK,CAAC,GAAGA,IAAI;IACtD,IAAI,CAACG,SAAS,CAAEL,IAAI,EAAEG,QAAS,CAAC;IAEhC,IAAK3H,MAAM,EAAG;MACZ;MACA,IAAI,CAACW,QAAQ,GAAG,IAAI,CAACA,QAAQ,IAAIX,MAAM,CAAE,IAAI,CAACI,OAAQ,CAAC;MACvD,IAAKqH,KAAK,EAAG;QACX;QACA,IAAIK,MAAM,GAAG9H,MAAM,CAAC+H,KAAK,CAAEN,KAAM,CAAC;QAClCK,MAAM,CAACN,IAAI,GAAGA,IAAI;QAClB,IAAI,CAAC7G,QAAQ,CAACqH,OAAO,CAAEF,MAAM,EAAEJ,IAAK,CAAC;MACvC,CAAC,MAAM;QACL;QACA,IAAI,CAAC/G,QAAQ,CAACqH,OAAO,CAAER,IAAI,EAAEE,IAAK,CAAC;MACrC;IACF;EACF,CAAC;;EAED;;EAGA;AACA;AACA;AACA;AACA;EACAzF,KAAK,CAACgG,MAAM,GAAG,UAAU1E,IAAI,EAAG;IAC9B,IAAIC,IAAI,GAAG,IAAI,CAAC0E,OAAO,CAAE3E,IAAK,CAAC;IAC/B,IAAKC,IAAI,EAAG;MACVA,IAAI,CAACqB,SAAS,GAAG,IAAI;IACvB;EACF,CAAC;;EAED;AACA;AACA;AACA;EACA5C,KAAK,CAACkG,QAAQ,GAAG,UAAU5E,IAAI,EAAG;IAChC,IAAIC,IAAI,GAAG,IAAI,CAAC0E,OAAO,CAAE3E,IAAK,CAAC;IAC/B,IAAKC,IAAI,EAAG;MACV,OAAOA,IAAI,CAACqB,SAAS;IACvB;EACF,CAAC;;EAED;AACA;AACA;AACA;EACA5C,KAAK,CAACU,KAAK,GAAG,UAAUO,KAAK,EAAG;IAC9BA,KAAK,GAAG,IAAI,CAACkF,KAAK,CAAElF,KAAM,CAAC;IAC3B,IAAK,CAACA,KAAK,EAAG;MACZ;IACF;IAEA,IAAI,CAACR,MAAM,GAAG,IAAI,CAACA,MAAM,CAACkF,MAAM,CAAE1E,KAAM,CAAC;IACzC;IACAA,KAAK,CAAC8B,OAAO,CAAE,IAAI,CAACiD,MAAM,EAAE,IAAK,CAAC;EACpC,CAAC;;EAED;AACA;AACA;AACA;EACAhG,KAAK,CAACoG,OAAO,GAAG,UAAUnF,KAAK,EAAG;IAChCA,KAAK,GAAG,IAAI,CAACkF,KAAK,CAAElF,KAAM,CAAC;IAC3B,IAAK,CAACA,KAAK,EAAE;MACX;IACF;IAEAA,KAAK,CAAC8B,OAAO,CAAE,UAAUzB,IAAI,EAAG;MAC9B;MACA/D,KAAK,CAAC8I,UAAU,CAAE,IAAI,CAAC5F,MAAM,EAAEa,IAAK,CAAC;MACrC,IAAI,CAAC4E,QAAQ,CAAE5E,IAAK,CAAC;IACvB,CAAC,EAAE,IAAK,CAAC;EACX,CAAC;;EAED;AACA;AACA;AACA;AACA;EACAtB,KAAK,CAACmG,KAAK,GAAG,UAAUlF,KAAK,EAAG;IAC9B,IAAK,CAACA,KAAK,EAAG;MACZ;IACF;IACA;IACA,IAAK,OAAOA,KAAK,IAAI,QAAQ,EAAG;MAC9BA,KAAK,GAAG,IAAI,CAAC9C,OAAO,CAACmI,gBAAgB,CAAErF,KAAM,CAAC;IAChD;IACAA,KAAK,GAAG1D,KAAK,CAACgJ,SAAS,CAAEtF,KAAM,CAAC;IAChC,OAAOA,KAAK;EACd,CAAC;EAEDjB,KAAK,CAAC8B,aAAa,GAAG,YAAW;IAC/B,IAAK,CAAC,IAAI,CAACrB,MAAM,IAAI,CAAC,IAAI,CAACA,MAAM,CAACY,MAAM,EAAG;MACzC;IACF;IAEA,IAAI,CAACmF,gBAAgB,CAAC,CAAC;IAEvB,IAAI,CAAC/F,MAAM,CAACsC,OAAO,CAAE,IAAI,CAAC0D,YAAY,EAAE,IAAK,CAAC;EAChD,CAAC;;EAED;EACAzG,KAAK,CAACwG,gBAAgB,GAAG,YAAW;IAClC;IACA,IAAIE,YAAY,GAAG,IAAI,CAACvI,OAAO,CAACwI,qBAAqB,CAAC,CAAC;IACvD,IAAIxE,IAAI,GAAG,IAAI,CAACA,IAAI;IACpB,IAAI,CAACyE,aAAa,GAAG;MACnBC,IAAI,EAAEH,YAAY,CAACG,IAAI,GAAG1E,IAAI,CAACkC,WAAW,GAAGlC,IAAI,CAACoC,eAAe;MACjEuC,GAAG,EAAEJ,YAAY,CAACI,GAAG,GAAG3E,IAAI,CAACuC,UAAU,GAAGvC,IAAI,CAACwC,cAAc;MAC7DoC,KAAK,EAAEL,YAAY,CAACK,KAAK,IAAK5E,IAAI,CAACmC,YAAY,GAAGnC,IAAI,CAACqC,gBAAgB,CAAE;MACzEwC,MAAM,EAAEN,YAAY,CAACM,MAAM,IAAK7E,IAAI,CAACsC,aAAa,GAAGtC,IAAI,CAACyC,iBAAiB;IAC7E,CAAC;EACH,CAAC;;EAED;AACA;AACA;EACA5E,KAAK,CAACyG,YAAY,GAAGzI,IAAI;;EAEzB;AACA;AACA;AACA;AACA;EACAgC,KAAK,CAACiH,iBAAiB,GAAG,UAAU3F,IAAI,EAAG;IACzC,IAAIoF,YAAY,GAAGpF,IAAI,CAACqF,qBAAqB,CAAC,CAAC;IAC/C,IAAIO,QAAQ,GAAG,IAAI,CAACN,aAAa;IACjC,IAAIzE,IAAI,GAAG7E,OAAO,CAAEgE,IAAK,CAAC;IAC1B,IAAI6F,MAAM,GAAG;MACXN,IAAI,EAAEH,YAAY,CAACG,IAAI,GAAGK,QAAQ,CAACL,IAAI,GAAG1E,IAAI,CAACiF,UAAU;MACzDN,GAAG,EAAEJ,YAAY,CAACI,GAAG,GAAGI,QAAQ,CAACJ,GAAG,GAAG3E,IAAI,CAACkF,SAAS;MACrDN,KAAK,EAAEG,QAAQ,CAACH,KAAK,GAAGL,YAAY,CAACK,KAAK,GAAG5E,IAAI,CAACmF,WAAW;MAC7DN,MAAM,EAAEE,QAAQ,CAACF,MAAM,GAAGN,YAAY,CAACM,MAAM,GAAG7E,IAAI,CAACoF;IACvD,CAAC;IACD,OAAOJ,MAAM;EACf,CAAC;;EAED;;EAEA;EACA;EACAnH,KAAK,CAACwH,WAAW,GAAGjK,KAAK,CAACiK,WAAW;;EAErC;AACA;AACA;EACAxH,KAAK,CAACa,UAAU,GAAG,YAAW;IAC5B5D,MAAM,CAACwK,gBAAgB,CAAE,QAAQ,EAAE,IAAK,CAAC;IACzC,IAAI,CAACC,aAAa,GAAG,IAAI;EAC3B,CAAC;;EAED;AACA;AACA;EACA1H,KAAK,CAAC2H,YAAY,GAAG,YAAW;IAC9B1K,MAAM,CAAC2K,mBAAmB,CAAE,QAAQ,EAAE,IAAK,CAAC;IAC5C,IAAI,CAACF,aAAa,GAAG,KAAK;EAC5B,CAAC;EAED1H,KAAK,CAAC6H,QAAQ,GAAG,YAAW;IAC1B,IAAI,CAACpI,MAAM,CAAC,CAAC;EACf,CAAC;EAEDlC,KAAK,CAACuK,cAAc,CAAElK,QAAQ,EAAE,UAAU,EAAE,GAAI,CAAC;EAEjDoC,KAAK,CAACP,MAAM,GAAG,YAAW;IACxB;IACA;IACA,IAAK,CAAC,IAAI,CAACiI,aAAa,IAAI,CAAC,IAAI,CAACK,iBAAiB,CAAC,CAAC,EAAG;MACtD;IACF;IAEA,IAAI,CAAC5I,MAAM,CAAC,CAAC;EACf,CAAC;;EAED;AACA;AACA;AACA;EACAa,KAAK,CAAC+H,iBAAiB,GAAG,YAAW;IACnC,IAAI5F,IAAI,GAAG7E,OAAO,CAAE,IAAI,CAACa,OAAQ,CAAC;IAClC;IACA;IACA,IAAI6J,QAAQ,GAAG,IAAI,CAAC7F,IAAI,IAAIA,IAAI;IAChC,OAAO6F,QAAQ,IAAI7F,IAAI,CAAC8F,UAAU,KAAK,IAAI,CAAC9F,IAAI,CAAC8F,UAAU;EAC7D,CAAC;;EAED;;EAEA;AACA;AACA;AACA;AACA;EACAjI,KAAK,CAACkI,QAAQ,GAAG,UAAUjH,KAAK,EAAG;IACjC,IAAIH,KAAK,GAAG,IAAI,CAACC,QAAQ,CAAEE,KAAM,CAAC;IAClC;IACA,IAAKH,KAAK,CAACO,MAAM,EAAG;MAClB,IAAI,CAACP,KAAK,GAAG,IAAI,CAACA,KAAK,CAAC6E,MAAM,CAAE7E,KAAM,CAAC;IACzC;IACA,OAAOA,KAAK;EACd,CAAC;;EAED;AACA;AACA;AACA;EACAd,KAAK,CAACmI,QAAQ,GAAG,UAAUlH,KAAK,EAAG;IACjC,IAAIH,KAAK,GAAG,IAAI,CAACoH,QAAQ,CAAEjH,KAAM,CAAC;IAClC,IAAK,CAACH,KAAK,CAACO,MAAM,EAAG;MACnB;IACF;IACA;IACA,IAAI,CAACY,WAAW,CAAEnB,KAAK,EAAE,IAAK,CAAC;IAC/B,IAAI,CAACsH,MAAM,CAAEtH,KAAM,CAAC;EACtB,CAAC;;EAED;AACA;AACA;AACA;EACAd,KAAK,CAACqI,SAAS,GAAG,UAAUpH,KAAK,EAAG;IAClC,IAAIH,KAAK,GAAG,IAAI,CAACC,QAAQ,CAAEE,KAAM,CAAC;IAClC,IAAK,CAACH,KAAK,CAACO,MAAM,EAAG;MACnB;IACF;IACA;IACA,IAAIiH,aAAa,GAAG,IAAI,CAACxH,KAAK,CAACyH,KAAK,CAAC,CAAC,CAAC;IACvC,IAAI,CAACzH,KAAK,GAAGA,KAAK,CAAC6E,MAAM,CAAE2C,aAAc,CAAC;IAC1C;IACA,IAAI,CAACzG,YAAY,CAAC,CAAC;IACnB,IAAI,CAACC,aAAa,CAAC,CAAC;IACpB;IACA,IAAI,CAACG,WAAW,CAAEnB,KAAK,EAAE,IAAK,CAAC;IAC/B,IAAI,CAACsH,MAAM,CAAEtH,KAAM,CAAC;IACpB;IACA,IAAI,CAACmB,WAAW,CAAEqG,aAAc,CAAC;EACnC,CAAC;;EAED;AACA;AACA;AACA;EACAtI,KAAK,CAACoI,MAAM,GAAG,UAAUtH,KAAK,EAAG;IAC/B,IAAI,CAAC+B,oBAAoB,CAAE,QAAQ,EAAE/B,KAAM,CAAC;IAC5C,IAAK,CAACA,KAAK,IAAI,CAACA,KAAK,CAACO,MAAM,EAAG;MAC7B;IACF;IACA,IAAImC,OAAO,GAAG,IAAI,CAACH,aAAa,CAAC,CAAC;IAClCvC,KAAK,CAACiC,OAAO,CAAE,UAAUxB,IAAI,EAAEH,CAAC,EAAG;MACjCG,IAAI,CAACiC,OAAO,CAAEpC,CAAC,GAAGoC,OAAQ,CAAC;MAC3BjC,IAAI,CAAC6G,MAAM,CAAC,CAAC;IACf,CAAC,CAAC;EACJ,CAAC;;EAED;AACA;AACA;AACA;EACApI,KAAK,CAACwI,IAAI,GAAG,UAAU1H,KAAK,EAAG;IAC7B,IAAI,CAAC+B,oBAAoB,CAAE,MAAM,EAAE/B,KAAM,CAAC;IAC1C,IAAK,CAACA,KAAK,IAAI,CAACA,KAAK,CAACO,MAAM,EAAG;MAC7B;IACF;IACA,IAAImC,OAAO,GAAG,IAAI,CAACH,aAAa,CAAC,CAAC;IAClCvC,KAAK,CAACiC,OAAO,CAAE,UAAUxB,IAAI,EAAEH,CAAC,EAAG;MACjCG,IAAI,CAACiC,OAAO,CAAEpC,CAAC,GAAGoC,OAAQ,CAAC;MAC3BjC,IAAI,CAACiH,IAAI,CAAC,CAAC;IACb,CAAC,CAAC;EACJ,CAAC;;EAED;AACA;AACA;AACA;EACAxI,KAAK,CAACyI,kBAAkB,GAAG,UAAUxH,KAAK,EAAG;IAC3C,IAAIH,KAAK,GAAG,IAAI,CAAC4H,QAAQ,CAAEzH,KAAM,CAAC;IAClC,IAAI,CAACmH,MAAM,CAAEtH,KAAM,CAAC;EACtB,CAAC;;EAED;AACA;AACA;AACA;EACAd,KAAK,CAAC2I,gBAAgB,GAAG,UAAU1H,KAAK,EAAG;IACzC,IAAIH,KAAK,GAAG,IAAI,CAAC4H,QAAQ,CAAEzH,KAAM,CAAC;IAClC,IAAI,CAACuH,IAAI,CAAE1H,KAAM,CAAC;EACpB,CAAC;;EAED;AACA;AACA;AACA;AACA;AACA;EACAd,KAAK,CAACiG,OAAO,GAAG,UAAU3E,IAAI,EAAG;IAC/B;IACA,KAAM,IAAIF,CAAC,GAAC,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACN,KAAK,CAACO,MAAM,EAAED,CAAC,EAAE,EAAG;MAC1C,IAAIG,IAAI,GAAG,IAAI,CAACT,KAAK,CAACM,CAAC,CAAC;MACxB,IAAKG,IAAI,CAACpD,OAAO,IAAImD,IAAI,EAAG;QAC1B;QACA,OAAOC,IAAI;MACb;IACF;EACF,CAAC;;EAED;AACA;AACA;AACA;AACA;EACAvB,KAAK,CAAC0I,QAAQ,GAAG,UAAUzH,KAAK,EAAG;IACjCA,KAAK,GAAG1D,KAAK,CAACgJ,SAAS,CAAEtF,KAAM,CAAC;IAChC,IAAIH,KAAK,GAAG,EAAE;IACdG,KAAK,CAAC8B,OAAO,CAAE,UAAUzB,IAAI,EAAG;MAC9B,IAAIC,IAAI,GAAG,IAAI,CAAC0E,OAAO,CAAE3E,IAAK,CAAC;MAC/B,IAAKC,IAAI,EAAG;QACVT,KAAK,CAACU,IAAI,CAAED,IAAK,CAAC;MACpB;IACF,CAAC,EAAE,IAAK,CAAC;IAET,OAAOT,KAAK;EACd,CAAC;;EAED;AACA;AACA;AACA;EACAd,KAAK,CAAC4I,MAAM,GAAG,UAAU3H,KAAK,EAAG;IAC/B,IAAI4H,WAAW,GAAG,IAAI,CAACH,QAAQ,CAAEzH,KAAM,CAAC;IAExC,IAAI,CAAC4B,oBAAoB,CAAE,QAAQ,EAAEgG,WAAY,CAAC;;IAElD;IACA,IAAK,CAACA,WAAW,IAAI,CAACA,WAAW,CAACxH,MAAM,EAAG;MACzC;IACF;IAEAwH,WAAW,CAAC9F,OAAO,CAAE,UAAUxB,IAAI,EAAG;MACpCA,IAAI,CAACqH,MAAM,CAAC,CAAC;MACb;MACArL,KAAK,CAAC8I,UAAU,CAAE,IAAI,CAACvF,KAAK,EAAES,IAAK,CAAC;IACtC,CAAC,EAAE,IAAK,CAAC;EACX,CAAC;;EAED;;EAEA;EACAvB,KAAK,CAAC8I,OAAO,GAAG,YAAW;IACzB;IACA,IAAInI,KAAK,GAAG,IAAI,CAACxC,OAAO,CAACwC,KAAK;IAC9BA,KAAK,CAACqD,MAAM,GAAG,EAAE;IACjBrD,KAAK,CAACtB,QAAQ,GAAG,EAAE;IACnBsB,KAAK,CAACoD,KAAK,GAAG,EAAE;IAChB;IACA,IAAI,CAACjD,KAAK,CAACiC,OAAO,CAAE,UAAUxB,IAAI,EAAG;MACnCA,IAAI,CAACuH,OAAO,CAAC,CAAC;IAChB,CAAC,CAAC;IAEF,IAAI,CAACnB,YAAY,CAAC,CAAC;IAEnB,IAAI7I,EAAE,GAAG,IAAI,CAACX,OAAO,CAACY,YAAY;IAClC,OAAOb,SAAS,CAAEY,EAAE,CAAE,CAAC,CAAC;IACxB,OAAO,IAAI,CAACX,OAAO,CAACY,YAAY;IAChC;IACA,IAAKhB,MAAM,EAAG;MACZA,MAAM,CAACgL,UAAU,CAAE,IAAI,CAAC5K,OAAO,EAAE,IAAI,CAACK,WAAW,CAACC,SAAU,CAAC;IAC/D;EAEF,CAAC;;EAED;;EAEA;AACA;AACA;AACA;AACA;EACAb,QAAQ,CAACoL,IAAI,GAAG,UAAU1H,IAAI,EAAG;IAC/BA,IAAI,GAAG/D,KAAK,CAACe,eAAe,CAAEgD,IAAK,CAAC;IACpC,IAAIxC,EAAE,GAAGwC,IAAI,IAAIA,IAAI,CAACvC,YAAY;IAClC,OAAOD,EAAE,IAAIZ,SAAS,CAAEY,EAAE,CAAE;EAC9B,CAAC;;EAGD;;EAEA;AACA;AACA;AACA;EACAlB,QAAQ,CAACqL,MAAM,GAAG,UAAUxK,SAAS,EAAEL,OAAO,EAAG;IAC/C;IACA,IAAI8K,MAAM,GAAGC,QAAQ,CAAEvL,QAAS,CAAC;IACjC;IACAsL,MAAM,CAACtK,QAAQ,GAAGrB,KAAK,CAACoB,MAAM,CAAE,CAAC,CAAC,EAAEf,QAAQ,CAACgB,QAAS,CAAC;IACvDrB,KAAK,CAACoB,MAAM,CAAEuK,MAAM,CAACtK,QAAQ,EAAER,OAAQ,CAAC;IACxC8K,MAAM,CAAC9I,aAAa,GAAG7C,KAAK,CAACoB,MAAM,CAAE,CAAC,CAAC,EAAEf,QAAQ,CAACwC,aAAe,CAAC;IAElE8I,MAAM,CAACzK,SAAS,GAAGA,SAAS;IAE5ByK,MAAM,CAACF,IAAI,GAAGpL,QAAQ,CAACoL,IAAI;;IAE3B;IACAE,MAAM,CAAC1L,IAAI,GAAG2L,QAAQ,CAAE3L,IAAK,CAAC;;IAE9B;;IAEAD,KAAK,CAAC6L,QAAQ,CAAEF,MAAM,EAAEzK,SAAU,CAAC;;IAEnC;;IAEA;IACA,IAAKV,MAAM,IAAIA,MAAM,CAACsL,OAAO,EAAG;MAC9BtL,MAAM,CAACsL,OAAO,CAAE5K,SAAS,EAAEyK,MAAO,CAAC;IACrC;IAEA,OAAOA,MAAM;EACf,CAAC;EAED,SAASC,QAAQA,CAAEG,MAAM,EAAG;IAC1B,SAASC,QAAQA,CAAA,EAAG;MAClBD,MAAM,CAACE,KAAK,CAAE,IAAI,EAAEC,SAAU,CAAC;IACjC;IAEAF,QAAQ,CAACtJ,SAAS,GAAGyJ,MAAM,CAACT,MAAM,CAAEK,MAAM,CAACrJ,SAAU,CAAC;IACtDsJ,QAAQ,CAACtJ,SAAS,CAACzB,WAAW,GAAG+K,QAAQ;IAEzC,OAAOA,QAAQ;EACjB;;EAEA;;EAEA;EACA,IAAII,OAAO,GAAG;IACZC,EAAE,EAAE,CAAC;IACLC,CAAC,EAAE;EACL,CAAC;;EAED;EACA;EACA,SAASpG,eAAeA,CAAEqG,IAAI,EAAG;IAC/B,IAAK,OAAOA,IAAI,IAAI,QAAQ,EAAG;MAC7B,OAAOA,IAAI;IACb;IACA,IAAIC,OAAO,GAAGD,IAAI,CAACE,KAAK,CAAE,mBAAoB,CAAC;IAC/C,IAAIC,GAAG,GAAGF,OAAO,IAAIA,OAAO,CAAC,CAAC,CAAC;IAC/B,IAAIG,IAAI,GAAGH,OAAO,IAAIA,OAAO,CAAC,CAAC,CAAC;IAChC,IAAK,CAACE,GAAG,CAAC5I,MAAM,EAAG;MACjB,OAAO,CAAC;IACV;IACA4I,GAAG,GAAGE,UAAU,CAAEF,GAAI,CAAC;IACvB,IAAIG,IAAI,GAAGT,OAAO,CAAEO,IAAI,CAAE,IAAI,CAAC;IAC/B,OAAOD,GAAG,GAAGG,IAAI;EACnB;;EAEA;;EAEA;EACAxM,QAAQ,CAACJ,IAAI,GAAGA,IAAI;EAEpB,OAAOI,QAAQ;AAEf,CAAC,CAAC"}, "metadata": {}, "sourceType": "script", "externalDependencies": []}