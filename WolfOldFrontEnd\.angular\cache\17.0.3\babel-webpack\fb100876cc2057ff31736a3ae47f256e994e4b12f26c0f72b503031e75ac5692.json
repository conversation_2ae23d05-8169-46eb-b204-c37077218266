{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\n// @ts-nocheck\nimport { curry, each } from 'zrender/lib/core/util.js';\nfunction legendSelectActionHandler(methodName, payload, ecModel) {\n  var selectedMap = {};\n  var isToggleSelect = methodName === 'toggleSelected';\n  var isSelected; // Update all legend components\n\n  ecModel.eachComponent('legend', function (legendModel) {\n    if (isToggleSelect && isSelected != null) {\n      // Force other legend has same selected status\n      // Or the first is toggled to true and other are toggled to false\n      // In the case one legend has some item unSelected in option. And if other legend\n      // doesn't has the item, they will assume it is selected.\n      legendModel[isSelected ? 'select' : 'unSelect'](payload.name);\n    } else if (methodName === 'allSelect' || methodName === 'inverseSelect') {\n      legendModel[methodName]();\n    } else {\n      legendModel[methodName](payload.name);\n      isSelected = legendModel.isSelected(payload.name);\n    }\n    var legendData = legendModel.getData();\n    each(legendData, function (model) {\n      var name = model.get('name'); // Wrap element\n\n      if (name === '\\n' || name === '') {\n        return;\n      }\n      var isItemSelected = legendModel.isSelected(name);\n      if (selectedMap.hasOwnProperty(name)) {\n        // Unselected if any legend is unselected\n        selectedMap[name] = selectedMap[name] && isItemSelected;\n      } else {\n        selectedMap[name] = isItemSelected;\n      }\n    });\n  }); // Return the event explicitly\n\n  return methodName === 'allSelect' || methodName === 'inverseSelect' ? {\n    selected: selectedMap\n  } : {\n    name: payload.name,\n    selected: selectedMap\n  };\n}\nexport function installLegendAction(registers) {\n  /**\r\n   * @event legendToggleSelect\r\n   * @type {Object}\r\n   * @property {string} type 'legendToggleSelect'\r\n   * @property {string} [from]\r\n   * @property {string} name Series name or data item name\r\n   */\n  registers.registerAction('legendToggleSelect', 'legendselectchanged', curry(legendSelectActionHandler, 'toggleSelected'));\n  registers.registerAction('legendAllSelect', 'legendselectall', curry(legendSelectActionHandler, 'allSelect'));\n  registers.registerAction('legendInverseSelect', 'legendinverseselect', curry(legendSelectActionHandler, 'inverseSelect'));\n  /**\r\n   * @event legendSelect\r\n   * @type {Object}\r\n   * @property {string} type 'legendSelect'\r\n   * @property {string} name Series name or data item name\r\n   */\n\n  registers.registerAction('legendSelect', 'legendselected', curry(legendSelectActionHandler, 'select'));\n  /**\r\n   * @event legendUnSelect\r\n   * @type {Object}\r\n   * @property {string} type 'legendUnSelect'\r\n   * @property {string} name Series name or data item name\r\n   */\n\n  registers.registerAction('legendUnSelect', 'legendunselected', curry(legendSelectActionHandler, 'unSelect'));\n}", "map": {"version": 3, "names": ["curry", "each", "legendSelectActionHandler", "methodName", "payload", "ecModel", "selectedMap", "isToggleSelect", "isSelected", "eachComponent", "legend<PERSON><PERSON><PERSON>", "name", "legendData", "getData", "model", "get", "isItemSelected", "hasOwnProperty", "selected", "installLegendAction", "registers", "registerAction"], "sources": ["C:/WolfSystemV2/WolfOldFrontEnd/node_modules/echarts/lib/component/legend/legendAction.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\n// @ts-nocheck\nimport { curry, each } from 'zrender/lib/core/util.js';\n\nfunction legendSelectActionHandler(methodName, payload, ecModel) {\n  var selectedMap = {};\n  var isToggleSelect = methodName === 'toggleSelected';\n  var isSelected; // Update all legend components\n\n  ecModel.eachComponent('legend', function (legendModel) {\n    if (isToggleSelect && isSelected != null) {\n      // Force other legend has same selected status\n      // Or the first is toggled to true and other are toggled to false\n      // In the case one legend has some item unSelected in option. And if other legend\n      // doesn't has the item, they will assume it is selected.\n      legendModel[isSelected ? 'select' : 'unSelect'](payload.name);\n    } else if (methodName === 'allSelect' || methodName === 'inverseSelect') {\n      legendModel[methodName]();\n    } else {\n      legendModel[methodName](payload.name);\n      isSelected = legendModel.isSelected(payload.name);\n    }\n\n    var legendData = legendModel.getData();\n    each(legendData, function (model) {\n      var name = model.get('name'); // Wrap element\n\n      if (name === '\\n' || name === '') {\n        return;\n      }\n\n      var isItemSelected = legendModel.isSelected(name);\n\n      if (selectedMap.hasOwnProperty(name)) {\n        // Unselected if any legend is unselected\n        selectedMap[name] = selectedMap[name] && isItemSelected;\n      } else {\n        selectedMap[name] = isItemSelected;\n      }\n    });\n  }); // Return the event explicitly\n\n  return methodName === 'allSelect' || methodName === 'inverseSelect' ? {\n    selected: selectedMap\n  } : {\n    name: payload.name,\n    selected: selectedMap\n  };\n}\n\nexport function installLegendAction(registers) {\n  /**\r\n   * @event legendToggleSelect\r\n   * @type {Object}\r\n   * @property {string} type 'legendToggleSelect'\r\n   * @property {string} [from]\r\n   * @property {string} name Series name or data item name\r\n   */\n  registers.registerAction('legendToggleSelect', 'legendselectchanged', curry(legendSelectActionHandler, 'toggleSelected'));\n  registers.registerAction('legendAllSelect', 'legendselectall', curry(legendSelectActionHandler, 'allSelect'));\n  registers.registerAction('legendInverseSelect', 'legendinverseselect', curry(legendSelectActionHandler, 'inverseSelect'));\n  /**\r\n   * @event legendSelect\r\n   * @type {Object}\r\n   * @property {string} type 'legendSelect'\r\n   * @property {string} name Series name or data item name\r\n   */\n\n  registers.registerAction('legendSelect', 'legendselected', curry(legendSelectActionHandler, 'select'));\n  /**\r\n   * @event legendUnSelect\r\n   * @type {Object}\r\n   * @property {string} type 'legendUnSelect'\r\n   * @property {string} name Series name or data item name\r\n   */\n\n  registers.registerAction('legendUnSelect', 'legendunselected', curry(legendSelectActionHandler, 'unSelect'));\n}"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,KAAK,EAAEC,IAAI,QAAQ,0BAA0B;AAEtD,SAASC,yBAAyBA,CAACC,UAAU,EAAEC,OAAO,EAAEC,OAAO,EAAE;EAC/D,IAAIC,WAAW,GAAG,CAAC,CAAC;EACpB,IAAIC,cAAc,GAAGJ,UAAU,KAAK,gBAAgB;EACpD,IAAIK,UAAU,CAAC,CAAC;;EAEhBH,OAAO,CAACI,aAAa,CAAC,QAAQ,EAAE,UAAUC,WAAW,EAAE;IACrD,IAAIH,cAAc,IAAIC,UAAU,IAAI,IAAI,EAAE;MACxC;MACA;MACA;MACA;MACAE,WAAW,CAACF,UAAU,GAAG,QAAQ,GAAG,UAAU,CAAC,CAACJ,OAAO,CAACO,IAAI,CAAC;IAC/D,CAAC,MAAM,IAAIR,UAAU,KAAK,WAAW,IAAIA,UAAU,KAAK,eAAe,EAAE;MACvEO,WAAW,CAACP,UAAU,CAAC,CAAC,CAAC;IAC3B,CAAC,MAAM;MACLO,WAAW,CAACP,UAAU,CAAC,CAACC,OAAO,CAACO,IAAI,CAAC;MACrCH,UAAU,GAAGE,WAAW,CAACF,UAAU,CAACJ,OAAO,CAACO,IAAI,CAAC;IACnD;IAEA,IAAIC,UAAU,GAAGF,WAAW,CAACG,OAAO,CAAC,CAAC;IACtCZ,IAAI,CAACW,UAAU,EAAE,UAAUE,KAAK,EAAE;MAChC,IAAIH,IAAI,GAAGG,KAAK,CAACC,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC;;MAE9B,IAAIJ,IAAI,KAAK,IAAI,IAAIA,IAAI,KAAK,EAAE,EAAE;QAChC;MACF;MAEA,IAAIK,cAAc,GAAGN,WAAW,CAACF,UAAU,CAACG,IAAI,CAAC;MAEjD,IAAIL,WAAW,CAACW,cAAc,CAACN,IAAI,CAAC,EAAE;QACpC;QACAL,WAAW,CAACK,IAAI,CAAC,GAAGL,WAAW,CAACK,IAAI,CAAC,IAAIK,cAAc;MACzD,CAAC,MAAM;QACLV,WAAW,CAACK,IAAI,CAAC,GAAGK,cAAc;MACpC;IACF,CAAC,CAAC;EACJ,CAAC,CAAC,CAAC,CAAC;;EAEJ,OAAOb,UAAU,KAAK,WAAW,IAAIA,UAAU,KAAK,eAAe,GAAG;IACpEe,QAAQ,EAAEZ;EACZ,CAAC,GAAG;IACFK,IAAI,EAAEP,OAAO,CAACO,IAAI;IAClBO,QAAQ,EAAEZ;EACZ,CAAC;AACH;AAEA,OAAO,SAASa,mBAAmBA,CAACC,SAAS,EAAE;EAC7C;AACF;AACA;AACA;AACA;AACA;AACA;EACEA,SAAS,CAACC,cAAc,CAAC,oBAAoB,EAAE,qBAAqB,EAAErB,KAAK,CAACE,yBAAyB,EAAE,gBAAgB,CAAC,CAAC;EACzHkB,SAAS,CAACC,cAAc,CAAC,iBAAiB,EAAE,iBAAiB,EAAErB,KAAK,CAACE,yBAAyB,EAAE,WAAW,CAAC,CAAC;EAC7GkB,SAAS,CAACC,cAAc,CAAC,qBAAqB,EAAE,qBAAqB,EAAErB,KAAK,CAACE,yBAAyB,EAAE,eAAe,CAAC,CAAC;EACzH;AACF;AACA;AACA;AACA;AACA;;EAEEkB,SAAS,CAACC,cAAc,CAAC,cAAc,EAAE,gBAAgB,EAAErB,KAAK,CAACE,yBAAyB,EAAE,QAAQ,CAAC,CAAC;EACtG;AACF;AACA;AACA;AACA;AACA;;EAEEkB,SAAS,CAACC,cAAc,CAAC,gBAAgB,EAAE,kBAAkB,EAAErB,KAAK,CAACE,yBAAyB,EAAE,UAAU,CAAC,CAAC;AAC9G"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}