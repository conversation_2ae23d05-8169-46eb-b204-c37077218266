{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\nimport ComponentModel from '../../model/Component.js';\nvar AxisPointerModel = /** @class */\nfunction (_super) {\n  __extends(AxisPointerModel, _super);\n  function AxisPointerModel() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.type = AxisPointerModel.type;\n    return _this;\n  }\n  AxisPointerModel.type = 'axisPointer';\n  AxisPointerModel.defaultOption = {\n    // 'auto' means that show when triggered by tooltip or handle.\n    show: 'auto',\n    // zlevel: 0,\n    z: 50,\n    type: 'line',\n    // axispointer triggered by tootip determine snap automatically,\n    // see `modelHelper`.\n    snap: false,\n    triggerTooltip: true,\n    triggerEmphasis: true,\n    value: null,\n    status: null,\n    link: [],\n    // Do not set 'auto' here, otherwise global animation: false\n    // will not effect at this axispointer.\n    animation: null,\n    animationDurationUpdate: 200,\n    lineStyle: {\n      color: '#B9BEC9',\n      width: 1,\n      type: 'dashed'\n    },\n    shadowStyle: {\n      color: 'rgba(210,219,238,0.2)'\n    },\n    label: {\n      show: true,\n      formatter: null,\n      precision: 'auto',\n      margin: 3,\n      color: '#fff',\n      padding: [5, 7, 5, 7],\n      backgroundColor: 'auto',\n      borderColor: null,\n      borderWidth: 0,\n      borderRadius: 3\n    },\n    handle: {\n      show: false,\n      // eslint-disable-next-line\n      icon: 'M10.7,11.9v-1.3H9.3v1.3c-4.9,0.3-8.8,4.4-8.8,9.4c0,5,3.9,9.1,8.8,9.4h1.3c4.9-0.3,8.8-4.4,8.8-9.4C19.5,16.3,15.6,12.2,10.7,11.9z M13.3,24.4H6.7v-1.2h6.6z M13.3,22H6.7v-1.2h6.6z M13.3,19.6H6.7v-1.2h6.6z',\n      size: 45,\n      // handle margin is from symbol center to axis, which is stable when circular move.\n      margin: 50,\n      // color: '#1b8bbd'\n      // color: '#2f4554'\n      color: '#333',\n      shadowBlur: 3,\n      shadowColor: '#aaa',\n      shadowOffsetX: 0,\n      shadowOffsetY: 2,\n      // For mobile performance\n      throttle: 40\n    }\n  };\n  return AxisPointerModel;\n}(ComponentModel);\nexport default AxisPointerModel;", "map": {"version": 3, "names": ["__extends", "ComponentModel", "AxisPointerModel", "_super", "_this", "apply", "arguments", "type", "defaultOption", "show", "z", "snap", "triggerTooltip", "triggerEmphasis", "value", "status", "link", "animation", "animationDurationUpdate", "lineStyle", "color", "width", "shadowStyle", "label", "formatter", "precision", "margin", "padding", "backgroundColor", "borderColor", "borderWidth", "borderRadius", "handle", "icon", "size", "<PERSON><PERSON><PERSON><PERSON>", "shadowColor", "shadowOffsetX", "shadowOffsetY", "throttle"], "sources": ["C:/WolfSystemV2/WolfOldFrontEnd/node_modules/echarts/lib/component/axisPointer/AxisPointerModel.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\nimport ComponentModel from '../../model/Component.js';\n\nvar AxisPointerModel =\n/** @class */\nfunction (_super) {\n  __extends(AxisPointerModel, _super);\n\n  function AxisPointerModel() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n\n    _this.type = AxisPointerModel.type;\n    return _this;\n  }\n\n  AxisPointerModel.type = 'axisPointer';\n  AxisPointerModel.defaultOption = {\n    // 'auto' means that show when triggered by tooltip or handle.\n    show: 'auto',\n    // zlevel: 0,\n    z: 50,\n    type: 'line',\n    // axispointer triggered by tootip determine snap automatically,\n    // see `modelHelper`.\n    snap: false,\n    triggerTooltip: true,\n    triggerEmphasis: true,\n    value: null,\n    status: null,\n    link: [],\n    // Do not set 'auto' here, otherwise global animation: false\n    // will not effect at this axispointer.\n    animation: null,\n    animationDurationUpdate: 200,\n    lineStyle: {\n      color: '#B9BEC9',\n      width: 1,\n      type: 'dashed'\n    },\n    shadowStyle: {\n      color: 'rgba(210,219,238,0.2)'\n    },\n    label: {\n      show: true,\n      formatter: null,\n      precision: 'auto',\n      margin: 3,\n      color: '#fff',\n      padding: [5, 7, 5, 7],\n      backgroundColor: 'auto',\n      borderColor: null,\n      borderWidth: 0,\n      borderRadius: 3\n    },\n    handle: {\n      show: false,\n      // eslint-disable-next-line\n      icon: 'M10.7,11.9v-1.3H9.3v1.3c-4.9,0.3-8.8,4.4-8.8,9.4c0,5,3.9,9.1,8.8,9.4h1.3c4.9-0.3,8.8-4.4,8.8-9.4C19.5,16.3,15.6,12.2,10.7,11.9z M13.3,24.4H6.7v-1.2h6.6z M13.3,22H6.7v-1.2h6.6z M13.3,19.6H6.7v-1.2h6.6z',\n      size: 45,\n      // handle margin is from symbol center to axis, which is stable when circular move.\n      margin: 50,\n      // color: '#1b8bbd'\n      // color: '#2f4554'\n      color: '#333',\n      shadowBlur: 3,\n      shadowColor: '#aaa',\n      shadowOffsetX: 0,\n      shadowOffsetY: 2,\n      // For mobile performance\n      throttle: 40\n    }\n  };\n  return AxisPointerModel;\n}(ComponentModel);\n\nexport default AxisPointerModel;"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,SAAS,QAAQ,OAAO;AACjC,OAAOC,cAAc,MAAM,0BAA0B;AAErD,IAAIC,gBAAgB,GACpB;AACA,UAAUC,MAAM,EAAE;EAChBH,SAAS,CAACE,gBAAgB,EAAEC,MAAM,CAAC;EAEnC,SAASD,gBAAgBA,CAAA,EAAG;IAC1B,IAAIE,KAAK,GAAGD,MAAM,KAAK,IAAI,IAAIA,MAAM,CAACE,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC,IAAI,IAAI;IAEpEF,KAAK,CAACG,IAAI,GAAGL,gBAAgB,CAACK,IAAI;IAClC,OAAOH,KAAK;EACd;EAEAF,gBAAgB,CAACK,IAAI,GAAG,aAAa;EACrCL,gBAAgB,CAACM,aAAa,GAAG;IAC/B;IACAC,IAAI,EAAE,MAAM;IACZ;IACAC,CAAC,EAAE,EAAE;IACLH,IAAI,EAAE,MAAM;IACZ;IACA;IACAI,IAAI,EAAE,KAAK;IACXC,cAAc,EAAE,IAAI;IACpBC,eAAe,EAAE,IAAI;IACrBC,KAAK,EAAE,IAAI;IACXC,MAAM,EAAE,IAAI;IACZC,IAAI,EAAE,EAAE;IACR;IACA;IACAC,SAAS,EAAE,IAAI;IACfC,uBAAuB,EAAE,GAAG;IAC5BC,SAAS,EAAE;MACTC,KAAK,EAAE,SAAS;MAChBC,KAAK,EAAE,CAAC;MACRd,IAAI,EAAE;IACR,CAAC;IACDe,WAAW,EAAE;MACXF,KAAK,EAAE;IACT,CAAC;IACDG,KAAK,EAAE;MACLd,IAAI,EAAE,IAAI;MACVe,SAAS,EAAE,IAAI;MACfC,SAAS,EAAE,MAAM;MACjBC,MAAM,EAAE,CAAC;MACTN,KAAK,EAAE,MAAM;MACbO,OAAO,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;MACrBC,eAAe,EAAE,MAAM;MACvBC,WAAW,EAAE,IAAI;MACjBC,WAAW,EAAE,CAAC;MACdC,YAAY,EAAE;IAChB,CAAC;IACDC,MAAM,EAAE;MACNvB,IAAI,EAAE,KAAK;MACX;MACAwB,IAAI,EAAE,0MAA0M;MAChNC,IAAI,EAAE,EAAE;MACR;MACAR,MAAM,EAAE,EAAE;MACV;MACA;MACAN,KAAK,EAAE,MAAM;MACbe,UAAU,EAAE,CAAC;MACbC,WAAW,EAAE,MAAM;MACnBC,aAAa,EAAE,CAAC;MAChBC,aAAa,EAAE,CAAC;MAChB;MACAC,QAAQ,EAAE;IACZ;EACF,CAAC;EACD,OAAOrC,gBAAgB;AACzB,CAAC,CAACD,cAAc,CAAC;AAEjB,eAAeC,gBAAgB"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}