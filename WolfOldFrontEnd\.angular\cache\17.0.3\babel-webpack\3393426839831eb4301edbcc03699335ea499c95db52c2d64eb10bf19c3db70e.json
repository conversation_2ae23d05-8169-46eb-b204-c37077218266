{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport ChartView from '../../view/Chart.js';\nimport SunburstPiece from './SunburstPiece.js';\nimport DataDiffer from '../../data/DataDiffer.js';\nimport { ROOT_TO_NODE_ACTION } from './sunburstAction.js';\nimport { windowOpen } from '../../util/format.js';\nvar SunburstView = /** @class */\nfunction (_super) {\n  __extends(SunburstView, _super);\n  function SunburstView() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.type = SunburstView.type;\n    return _this;\n  }\n  SunburstView.prototype.render = function (seriesModel, ecModel, api,\n  // @ts-ignore\n  payload) {\n    var self = this;\n    this.seriesModel = seriesModel;\n    this.api = api;\n    this.ecModel = ecModel;\n    var data = seriesModel.getData();\n    var virtualRoot = data.tree.root;\n    var newRoot = seriesModel.getViewRoot();\n    var group = this.group;\n    var renderLabelForZeroData = seriesModel.get('renderLabelForZeroData');\n    var newChildren = [];\n    newRoot.eachNode(function (node) {\n      newChildren.push(node);\n    });\n    var oldChildren = this._oldChildren || [];\n    dualTravel(newChildren, oldChildren);\n    renderRollUp(virtualRoot, newRoot);\n    this._initEvents();\n    this._oldChildren = newChildren;\n    function dualTravel(newChildren, oldChildren) {\n      if (newChildren.length === 0 && oldChildren.length === 0) {\n        return;\n      }\n      new DataDiffer(oldChildren, newChildren, getKey, getKey).add(processNode).update(processNode).remove(zrUtil.curry(processNode, null)).execute();\n      function getKey(node) {\n        return node.getId();\n      }\n      function processNode(newIdx, oldIdx) {\n        var newNode = newIdx == null ? null : newChildren[newIdx];\n        var oldNode = oldIdx == null ? null : oldChildren[oldIdx];\n        doRenderNode(newNode, oldNode);\n      }\n    }\n    function doRenderNode(newNode, oldNode) {\n      if (!renderLabelForZeroData && newNode && !newNode.getValue()) {\n        // Not render data with value 0\n        newNode = null;\n      }\n      if (newNode !== virtualRoot && oldNode !== virtualRoot) {\n        if (oldNode && oldNode.piece) {\n          if (newNode) {\n            // Update\n            oldNode.piece.updateData(false, newNode, seriesModel, ecModel, api); // For tooltip\n\n            data.setItemGraphicEl(newNode.dataIndex, oldNode.piece);\n          } else {\n            // Remove\n            removeNode(oldNode);\n          }\n        } else if (newNode) {\n          // Add\n          var piece = new SunburstPiece(newNode, seriesModel, ecModel, api);\n          group.add(piece); // For tooltip\n\n          data.setItemGraphicEl(newNode.dataIndex, piece);\n        }\n      }\n    }\n    function removeNode(node) {\n      if (!node) {\n        return;\n      }\n      if (node.piece) {\n        group.remove(node.piece);\n        node.piece = null;\n      }\n    }\n    function renderRollUp(virtualRoot, viewRoot) {\n      if (viewRoot.depth > 0) {\n        // Render\n        if (self.virtualPiece) {\n          // Update\n          self.virtualPiece.updateData(false, virtualRoot, seriesModel, ecModel, api);\n        } else {\n          // Add\n          self.virtualPiece = new SunburstPiece(virtualRoot, seriesModel, ecModel, api);\n          group.add(self.virtualPiece);\n        } // TODO event scope\n\n        viewRoot.piece.off('click');\n        self.virtualPiece.on('click', function (e) {\n          self._rootToNode(viewRoot.parentNode);\n        });\n      } else if (self.virtualPiece) {\n        // Remove\n        group.remove(self.virtualPiece);\n        self.virtualPiece = null;\n      }\n    }\n  };\n  /**\r\n   * @private\r\n   */\n\n  SunburstView.prototype._initEvents = function () {\n    var _this = this;\n    this.group.off('click');\n    this.group.on('click', function (e) {\n      var targetFound = false;\n      var viewRoot = _this.seriesModel.getViewRoot();\n      viewRoot.eachNode(function (node) {\n        if (!targetFound && node.piece && node.piece === e.target) {\n          var nodeClick = node.getModel().get('nodeClick');\n          if (nodeClick === 'rootToNode') {\n            _this._rootToNode(node);\n          } else if (nodeClick === 'link') {\n            var itemModel = node.getModel();\n            var link = itemModel.get('link');\n            if (link) {\n              var linkTarget = itemModel.get('target', true) || '_blank';\n              windowOpen(link, linkTarget);\n            }\n          }\n          targetFound = true;\n        }\n      });\n    });\n  };\n  /**\r\n   * @private\r\n   */\n\n  SunburstView.prototype._rootToNode = function (node) {\n    if (node !== this.seriesModel.getViewRoot()) {\n      this.api.dispatchAction({\n        type: ROOT_TO_NODE_ACTION,\n        from: this.uid,\n        seriesId: this.seriesModel.id,\n        targetNode: node\n      });\n    }\n  };\n  /**\r\n   * @implement\r\n   */\n\n  SunburstView.prototype.containPoint = function (point, seriesModel) {\n    var treeRoot = seriesModel.getData();\n    var itemLayout = treeRoot.getItemLayout(0);\n    if (itemLayout) {\n      var dx = point[0] - itemLayout.cx;\n      var dy = point[1] - itemLayout.cy;\n      var radius = Math.sqrt(dx * dx + dy * dy);\n      return radius <= itemLayout.r && radius >= itemLayout.r0;\n    }\n  };\n  SunburstView.type = 'sunburst';\n  return SunburstView;\n}(ChartView);\nexport default SunburstView;", "map": {"version": 3, "names": ["__extends", "zrUtil", "ChartView", "SunburstPiece", "<PERSON><PERSON><PERSON><PERSON>", "ROOT_TO_NODE_ACTION", "windowOpen", "SunburstView", "_super", "_this", "apply", "arguments", "type", "prototype", "render", "seriesModel", "ecModel", "api", "payload", "self", "data", "getData", "virtualRoot", "tree", "root", "newRoot", "getViewRoot", "group", "renderLabelForZeroData", "get", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "eachNode", "node", "push", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "_<PERSON><PERSON><PERSON><PERSON><PERSON>", "dualTravel", "renderRollUp", "_initEvents", "length", "<PERSON><PERSON><PERSON>", "add", "processNode", "update", "remove", "curry", "execute", "getId", "newIdx", "oldIdx", "newNode", "oldNode", "doRenderNode", "getValue", "piece", "updateData", "setItemGraphicEl", "dataIndex", "removeNode", "viewRoot", "depth", "virtualPiece", "off", "on", "e", "_rootToNode", "parentNode", "targetFound", "target", "nodeClick", "getModel", "itemModel", "link", "linkTarget", "dispatchAction", "from", "uid", "seriesId", "id", "targetNode", "containPoint", "point", "treeRoot", "itemLayout", "getItemLayout", "dx", "cx", "dy", "cy", "radius", "Math", "sqrt", "r", "r0"], "sources": ["C:/WolfSystemV2/WolfOldFrontEnd/node_modules/echarts/lib/chart/sunburst/SunburstView.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport ChartView from '../../view/Chart.js';\nimport SunburstPiece from './SunburstPiece.js';\nimport DataDiffer from '../../data/DataDiffer.js';\nimport { ROOT_TO_NODE_ACTION } from './sunburstAction.js';\nimport { windowOpen } from '../../util/format.js';\n\nvar SunburstView =\n/** @class */\nfunction (_super) {\n  __extends(SunburstView, _super);\n\n  function SunburstView() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n\n    _this.type = SunburstView.type;\n    return _this;\n  }\n\n  SunburstView.prototype.render = function (seriesModel, ecModel, api, // @ts-ignore\n  payload) {\n    var self = this;\n    this.seriesModel = seriesModel;\n    this.api = api;\n    this.ecModel = ecModel;\n    var data = seriesModel.getData();\n    var virtualRoot = data.tree.root;\n    var newRoot = seriesModel.getViewRoot();\n    var group = this.group;\n    var renderLabelForZeroData = seriesModel.get('renderLabelForZeroData');\n    var newChildren = [];\n    newRoot.eachNode(function (node) {\n      newChildren.push(node);\n    });\n    var oldChildren = this._oldChildren || [];\n    dualTravel(newChildren, oldChildren);\n    renderRollUp(virtualRoot, newRoot);\n\n    this._initEvents();\n\n    this._oldChildren = newChildren;\n\n    function dualTravel(newChildren, oldChildren) {\n      if (newChildren.length === 0 && oldChildren.length === 0) {\n        return;\n      }\n\n      new DataDiffer(oldChildren, newChildren, getKey, getKey).add(processNode).update(processNode).remove(zrUtil.curry(processNode, null)).execute();\n\n      function getKey(node) {\n        return node.getId();\n      }\n\n      function processNode(newIdx, oldIdx) {\n        var newNode = newIdx == null ? null : newChildren[newIdx];\n        var oldNode = oldIdx == null ? null : oldChildren[oldIdx];\n        doRenderNode(newNode, oldNode);\n      }\n    }\n\n    function doRenderNode(newNode, oldNode) {\n      if (!renderLabelForZeroData && newNode && !newNode.getValue()) {\n        // Not render data with value 0\n        newNode = null;\n      }\n\n      if (newNode !== virtualRoot && oldNode !== virtualRoot) {\n        if (oldNode && oldNode.piece) {\n          if (newNode) {\n            // Update\n            oldNode.piece.updateData(false, newNode, seriesModel, ecModel, api); // For tooltip\n\n            data.setItemGraphicEl(newNode.dataIndex, oldNode.piece);\n          } else {\n            // Remove\n            removeNode(oldNode);\n          }\n        } else if (newNode) {\n          // Add\n          var piece = new SunburstPiece(newNode, seriesModel, ecModel, api);\n          group.add(piece); // For tooltip\n\n          data.setItemGraphicEl(newNode.dataIndex, piece);\n        }\n      }\n    }\n\n    function removeNode(node) {\n      if (!node) {\n        return;\n      }\n\n      if (node.piece) {\n        group.remove(node.piece);\n        node.piece = null;\n      }\n    }\n\n    function renderRollUp(virtualRoot, viewRoot) {\n      if (viewRoot.depth > 0) {\n        // Render\n        if (self.virtualPiece) {\n          // Update\n          self.virtualPiece.updateData(false, virtualRoot, seriesModel, ecModel, api);\n        } else {\n          // Add\n          self.virtualPiece = new SunburstPiece(virtualRoot, seriesModel, ecModel, api);\n          group.add(self.virtualPiece);\n        } // TODO event scope\n\n\n        viewRoot.piece.off('click');\n        self.virtualPiece.on('click', function (e) {\n          self._rootToNode(viewRoot.parentNode);\n        });\n      } else if (self.virtualPiece) {\n        // Remove\n        group.remove(self.virtualPiece);\n        self.virtualPiece = null;\n      }\n    }\n  };\n  /**\r\n   * @private\r\n   */\n\n\n  SunburstView.prototype._initEvents = function () {\n    var _this = this;\n\n    this.group.off('click');\n    this.group.on('click', function (e) {\n      var targetFound = false;\n\n      var viewRoot = _this.seriesModel.getViewRoot();\n\n      viewRoot.eachNode(function (node) {\n        if (!targetFound && node.piece && node.piece === e.target) {\n          var nodeClick = node.getModel().get('nodeClick');\n\n          if (nodeClick === 'rootToNode') {\n            _this._rootToNode(node);\n          } else if (nodeClick === 'link') {\n            var itemModel = node.getModel();\n            var link = itemModel.get('link');\n\n            if (link) {\n              var linkTarget = itemModel.get('target', true) || '_blank';\n              windowOpen(link, linkTarget);\n            }\n          }\n\n          targetFound = true;\n        }\n      });\n    });\n  };\n  /**\r\n   * @private\r\n   */\n\n\n  SunburstView.prototype._rootToNode = function (node) {\n    if (node !== this.seriesModel.getViewRoot()) {\n      this.api.dispatchAction({\n        type: ROOT_TO_NODE_ACTION,\n        from: this.uid,\n        seriesId: this.seriesModel.id,\n        targetNode: node\n      });\n    }\n  };\n  /**\r\n   * @implement\r\n   */\n\n\n  SunburstView.prototype.containPoint = function (point, seriesModel) {\n    var treeRoot = seriesModel.getData();\n    var itemLayout = treeRoot.getItemLayout(0);\n\n    if (itemLayout) {\n      var dx = point[0] - itemLayout.cx;\n      var dy = point[1] - itemLayout.cy;\n      var radius = Math.sqrt(dx * dx + dy * dy);\n      return radius <= itemLayout.r && radius >= itemLayout.r0;\n    }\n  };\n\n  SunburstView.type = 'sunburst';\n  return SunburstView;\n}(ChartView);\n\nexport default SunburstView;"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,SAAS,QAAQ,OAAO;AACjC,OAAO,KAAKC,MAAM,MAAM,0BAA0B;AAClD,OAAOC,SAAS,MAAM,qBAAqB;AAC3C,OAAOC,aAAa,MAAM,oBAAoB;AAC9C,OAAOC,UAAU,MAAM,0BAA0B;AACjD,SAASC,mBAAmB,QAAQ,qBAAqB;AACzD,SAASC,UAAU,QAAQ,sBAAsB;AAEjD,IAAIC,YAAY,GAChB;AACA,UAAUC,MAAM,EAAE;EAChBR,SAAS,CAACO,YAAY,EAAEC,MAAM,CAAC;EAE/B,SAASD,YAAYA,CAAA,EAAG;IACtB,IAAIE,KAAK,GAAGD,MAAM,KAAK,IAAI,IAAIA,MAAM,CAACE,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC,IAAI,IAAI;IAEpEF,KAAK,CAACG,IAAI,GAAGL,YAAY,CAACK,IAAI;IAC9B,OAAOH,KAAK;EACd;EAEAF,YAAY,CAACM,SAAS,CAACC,MAAM,GAAG,UAAUC,WAAW,EAAEC,OAAO,EAAEC,GAAG;EAAE;EACrEC,OAAO,EAAE;IACP,IAAIC,IAAI,GAAG,IAAI;IACf,IAAI,CAACJ,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAACE,GAAG,GAAGA,GAAG;IACd,IAAI,CAACD,OAAO,GAAGA,OAAO;IACtB,IAAII,IAAI,GAAGL,WAAW,CAACM,OAAO,CAAC,CAAC;IAChC,IAAIC,WAAW,GAAGF,IAAI,CAACG,IAAI,CAACC,IAAI;IAChC,IAAIC,OAAO,GAAGV,WAAW,CAACW,WAAW,CAAC,CAAC;IACvC,IAAIC,KAAK,GAAG,IAAI,CAACA,KAAK;IACtB,IAAIC,sBAAsB,GAAGb,WAAW,CAACc,GAAG,CAAC,wBAAwB,CAAC;IACtE,IAAIC,WAAW,GAAG,EAAE;IACpBL,OAAO,CAACM,QAAQ,CAAC,UAAUC,IAAI,EAAE;MAC/BF,WAAW,CAACG,IAAI,CAACD,IAAI,CAAC;IACxB,CAAC,CAAC;IACF,IAAIE,WAAW,GAAG,IAAI,CAACC,YAAY,IAAI,EAAE;IACzCC,UAAU,CAACN,WAAW,EAAEI,WAAW,CAAC;IACpCG,YAAY,CAACf,WAAW,EAAEG,OAAO,CAAC;IAElC,IAAI,CAACa,WAAW,CAAC,CAAC;IAElB,IAAI,CAACH,YAAY,GAAGL,WAAW;IAE/B,SAASM,UAAUA,CAACN,WAAW,EAAEI,WAAW,EAAE;MAC5C,IAAIJ,WAAW,CAACS,MAAM,KAAK,CAAC,IAAIL,WAAW,CAACK,MAAM,KAAK,CAAC,EAAE;QACxD;MACF;MAEA,IAAInC,UAAU,CAAC8B,WAAW,EAAEJ,WAAW,EAAEU,MAAM,EAAEA,MAAM,CAAC,CAACC,GAAG,CAACC,WAAW,CAAC,CAACC,MAAM,CAACD,WAAW,CAAC,CAACE,MAAM,CAAC3C,MAAM,CAAC4C,KAAK,CAACH,WAAW,EAAE,IAAI,CAAC,CAAC,CAACI,OAAO,CAAC,CAAC;MAE/I,SAASN,MAAMA,CAACR,IAAI,EAAE;QACpB,OAAOA,IAAI,CAACe,KAAK,CAAC,CAAC;MACrB;MAEA,SAASL,WAAWA,CAACM,MAAM,EAAEC,MAAM,EAAE;QACnC,IAAIC,OAAO,GAAGF,MAAM,IAAI,IAAI,GAAG,IAAI,GAAGlB,WAAW,CAACkB,MAAM,CAAC;QACzD,IAAIG,OAAO,GAAGF,MAAM,IAAI,IAAI,GAAG,IAAI,GAAGf,WAAW,CAACe,MAAM,CAAC;QACzDG,YAAY,CAACF,OAAO,EAAEC,OAAO,CAAC;MAChC;IACF;IAEA,SAASC,YAAYA,CAACF,OAAO,EAAEC,OAAO,EAAE;MACtC,IAAI,CAACvB,sBAAsB,IAAIsB,OAAO,IAAI,CAACA,OAAO,CAACG,QAAQ,CAAC,CAAC,EAAE;QAC7D;QACAH,OAAO,GAAG,IAAI;MAChB;MAEA,IAAIA,OAAO,KAAK5B,WAAW,IAAI6B,OAAO,KAAK7B,WAAW,EAAE;QACtD,IAAI6B,OAAO,IAAIA,OAAO,CAACG,KAAK,EAAE;UAC5B,IAAIJ,OAAO,EAAE;YACX;YACAC,OAAO,CAACG,KAAK,CAACC,UAAU,CAAC,KAAK,EAAEL,OAAO,EAAEnC,WAAW,EAAEC,OAAO,EAAEC,GAAG,CAAC,CAAC,CAAC;;YAErEG,IAAI,CAACoC,gBAAgB,CAACN,OAAO,CAACO,SAAS,EAAEN,OAAO,CAACG,KAAK,CAAC;UACzD,CAAC,MAAM;YACL;YACAI,UAAU,CAACP,OAAO,CAAC;UACrB;QACF,CAAC,MAAM,IAAID,OAAO,EAAE;UAClB;UACA,IAAII,KAAK,GAAG,IAAInD,aAAa,CAAC+C,OAAO,EAAEnC,WAAW,EAAEC,OAAO,EAAEC,GAAG,CAAC;UACjEU,KAAK,CAACc,GAAG,CAACa,KAAK,CAAC,CAAC,CAAC;;UAElBlC,IAAI,CAACoC,gBAAgB,CAACN,OAAO,CAACO,SAAS,EAAEH,KAAK,CAAC;QACjD;MACF;IACF;IAEA,SAASI,UAAUA,CAAC1B,IAAI,EAAE;MACxB,IAAI,CAACA,IAAI,EAAE;QACT;MACF;MAEA,IAAIA,IAAI,CAACsB,KAAK,EAAE;QACd3B,KAAK,CAACiB,MAAM,CAACZ,IAAI,CAACsB,KAAK,CAAC;QACxBtB,IAAI,CAACsB,KAAK,GAAG,IAAI;MACnB;IACF;IAEA,SAASjB,YAAYA,CAACf,WAAW,EAAEqC,QAAQ,EAAE;MAC3C,IAAIA,QAAQ,CAACC,KAAK,GAAG,CAAC,EAAE;QACtB;QACA,IAAIzC,IAAI,CAAC0C,YAAY,EAAE;UACrB;UACA1C,IAAI,CAAC0C,YAAY,CAACN,UAAU,CAAC,KAAK,EAAEjC,WAAW,EAAEP,WAAW,EAAEC,OAAO,EAAEC,GAAG,CAAC;QAC7E,CAAC,MAAM;UACL;UACAE,IAAI,CAAC0C,YAAY,GAAG,IAAI1D,aAAa,CAACmB,WAAW,EAAEP,WAAW,EAAEC,OAAO,EAAEC,GAAG,CAAC;UAC7EU,KAAK,CAACc,GAAG,CAACtB,IAAI,CAAC0C,YAAY,CAAC;QAC9B,CAAC,CAAC;;QAGFF,QAAQ,CAACL,KAAK,CAACQ,GAAG,CAAC,OAAO,CAAC;QAC3B3C,IAAI,CAAC0C,YAAY,CAACE,EAAE,CAAC,OAAO,EAAE,UAAUC,CAAC,EAAE;UACzC7C,IAAI,CAAC8C,WAAW,CAACN,QAAQ,CAACO,UAAU,CAAC;QACvC,CAAC,CAAC;MACJ,CAAC,MAAM,IAAI/C,IAAI,CAAC0C,YAAY,EAAE;QAC5B;QACAlC,KAAK,CAACiB,MAAM,CAACzB,IAAI,CAAC0C,YAAY,CAAC;QAC/B1C,IAAI,CAAC0C,YAAY,GAAG,IAAI;MAC1B;IACF;EACF,CAAC;EACD;AACF;AACA;;EAGEtD,YAAY,CAACM,SAAS,CAACyB,WAAW,GAAG,YAAY;IAC/C,IAAI7B,KAAK,GAAG,IAAI;IAEhB,IAAI,CAACkB,KAAK,CAACmC,GAAG,CAAC,OAAO,CAAC;IACvB,IAAI,CAACnC,KAAK,CAACoC,EAAE,CAAC,OAAO,EAAE,UAAUC,CAAC,EAAE;MAClC,IAAIG,WAAW,GAAG,KAAK;MAEvB,IAAIR,QAAQ,GAAGlD,KAAK,CAACM,WAAW,CAACW,WAAW,CAAC,CAAC;MAE9CiC,QAAQ,CAAC5B,QAAQ,CAAC,UAAUC,IAAI,EAAE;QAChC,IAAI,CAACmC,WAAW,IAAInC,IAAI,CAACsB,KAAK,IAAItB,IAAI,CAACsB,KAAK,KAAKU,CAAC,CAACI,MAAM,EAAE;UACzD,IAAIC,SAAS,GAAGrC,IAAI,CAACsC,QAAQ,CAAC,CAAC,CAACzC,GAAG,CAAC,WAAW,CAAC;UAEhD,IAAIwC,SAAS,KAAK,YAAY,EAAE;YAC9B5D,KAAK,CAACwD,WAAW,CAACjC,IAAI,CAAC;UACzB,CAAC,MAAM,IAAIqC,SAAS,KAAK,MAAM,EAAE;YAC/B,IAAIE,SAAS,GAAGvC,IAAI,CAACsC,QAAQ,CAAC,CAAC;YAC/B,IAAIE,IAAI,GAAGD,SAAS,CAAC1C,GAAG,CAAC,MAAM,CAAC;YAEhC,IAAI2C,IAAI,EAAE;cACR,IAAIC,UAAU,GAAGF,SAAS,CAAC1C,GAAG,CAAC,QAAQ,EAAE,IAAI,CAAC,IAAI,QAAQ;cAC1DvB,UAAU,CAACkE,IAAI,EAAEC,UAAU,CAAC;YAC9B;UACF;UAEAN,WAAW,GAAG,IAAI;QACpB;MACF,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ,CAAC;EACD;AACF;AACA;;EAGE5D,YAAY,CAACM,SAAS,CAACoD,WAAW,GAAG,UAAUjC,IAAI,EAAE;IACnD,IAAIA,IAAI,KAAK,IAAI,CAACjB,WAAW,CAACW,WAAW,CAAC,CAAC,EAAE;MAC3C,IAAI,CAACT,GAAG,CAACyD,cAAc,CAAC;QACtB9D,IAAI,EAAEP,mBAAmB;QACzBsE,IAAI,EAAE,IAAI,CAACC,GAAG;QACdC,QAAQ,EAAE,IAAI,CAAC9D,WAAW,CAAC+D,EAAE;QAC7BC,UAAU,EAAE/C;MACd,CAAC,CAAC;IACJ;EACF,CAAC;EACD;AACF;AACA;;EAGEzB,YAAY,CAACM,SAAS,CAACmE,YAAY,GAAG,UAAUC,KAAK,EAAElE,WAAW,EAAE;IAClE,IAAImE,QAAQ,GAAGnE,WAAW,CAACM,OAAO,CAAC,CAAC;IACpC,IAAI8D,UAAU,GAAGD,QAAQ,CAACE,aAAa,CAAC,CAAC,CAAC;IAE1C,IAAID,UAAU,EAAE;MACd,IAAIE,EAAE,GAAGJ,KAAK,CAAC,CAAC,CAAC,GAAGE,UAAU,CAACG,EAAE;MACjC,IAAIC,EAAE,GAAGN,KAAK,CAAC,CAAC,CAAC,GAAGE,UAAU,CAACK,EAAE;MACjC,IAAIC,MAAM,GAAGC,IAAI,CAACC,IAAI,CAACN,EAAE,GAAGA,EAAE,GAAGE,EAAE,GAAGA,EAAE,CAAC;MACzC,OAAOE,MAAM,IAAIN,UAAU,CAACS,CAAC,IAAIH,MAAM,IAAIN,UAAU,CAACU,EAAE;IAC1D;EACF,CAAC;EAEDtF,YAAY,CAACK,IAAI,GAAG,UAAU;EAC9B,OAAOL,YAAY;AACrB,CAAC,CAACL,SAAS,CAAC;AAEZ,eAAeK,YAAY"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}