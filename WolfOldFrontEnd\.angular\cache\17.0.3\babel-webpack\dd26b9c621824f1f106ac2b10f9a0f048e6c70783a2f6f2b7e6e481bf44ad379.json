{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport LRU from 'zrender/lib/core/LRU.js';\nimport { extend, indexOf, isArrayLike, isObject, keys, isArray, each, isString, isGradientObject, map } from 'zrender/lib/core/util.js';\nimport { getECData } from './innerStore.js';\nimport * as colorTool from 'zrender/lib/tool/color.js';\nimport { queryDataIndex, makeInner } from './model.js';\nimport Path from 'zrender/lib/graphic/Path.js';\nimport { error } from './log.js'; // Reserve 0 as default.\n\nvar _highlightNextDigit = 1;\nvar _highlightKeyMap = {};\nvar getSavedStates = makeInner();\nvar getComponentStates = makeInner();\nexport var HOVER_STATE_NORMAL = 0;\nexport var HOVER_STATE_BLUR = 1;\nexport var HOVER_STATE_EMPHASIS = 2;\nexport var SPECIAL_STATES = ['emphasis', 'blur', 'select'];\nexport var DISPLAY_STATES = ['normal', 'emphasis', 'blur', 'select'];\nexport var Z2_EMPHASIS_LIFT = 10;\nexport var Z2_SELECT_LIFT = 9;\nexport var HIGHLIGHT_ACTION_TYPE = 'highlight';\nexport var DOWNPLAY_ACTION_TYPE = 'downplay';\nexport var SELECT_ACTION_TYPE = 'select';\nexport var UNSELECT_ACTION_TYPE = 'unselect';\nexport var TOGGLE_SELECT_ACTION_TYPE = 'toggleSelect';\nfunction hasFillOrStroke(fillOrStroke) {\n  return fillOrStroke != null && fillOrStroke !== 'none';\n} // Most lifted color are duplicated.\n\nvar liftedColorCache = new LRU(100);\nfunction liftColor(color) {\n  if (isString(color)) {\n    var liftedColor = liftedColorCache.get(color);\n    if (!liftedColor) {\n      liftedColor = colorTool.lift(color, -0.1);\n      liftedColorCache.put(color, liftedColor);\n    }\n    return liftedColor;\n  } else if (isGradientObject(color)) {\n    var ret = extend({}, color);\n    ret.colorStops = map(color.colorStops, function (stop) {\n      return {\n        offset: stop.offset,\n        color: colorTool.lift(stop.color, -0.1)\n      };\n    });\n    return ret;\n  } // Change nothing.\n\n  return color;\n}\nfunction doChangeHoverState(el, stateName, hoverStateEnum) {\n  if (el.onHoverStateChange && (el.hoverState || 0) !== hoverStateEnum) {\n    el.onHoverStateChange(stateName);\n  }\n  el.hoverState = hoverStateEnum;\n}\nfunction singleEnterEmphasis(el) {\n  // Only mark the flag.\n  // States will be applied in the echarts.ts in next frame.\n  doChangeHoverState(el, 'emphasis', HOVER_STATE_EMPHASIS);\n}\nfunction singleLeaveEmphasis(el) {\n  // Only mark the flag.\n  // States will be applied in the echarts.ts in next frame.\n  if (el.hoverState === HOVER_STATE_EMPHASIS) {\n    doChangeHoverState(el, 'normal', HOVER_STATE_NORMAL);\n  }\n}\nfunction singleEnterBlur(el) {\n  doChangeHoverState(el, 'blur', HOVER_STATE_BLUR);\n}\nfunction singleLeaveBlur(el) {\n  if (el.hoverState === HOVER_STATE_BLUR) {\n    doChangeHoverState(el, 'normal', HOVER_STATE_NORMAL);\n  }\n}\nfunction singleEnterSelect(el) {\n  el.selected = true;\n}\nfunction singleLeaveSelect(el) {\n  el.selected = false;\n}\nfunction updateElementState(el, updater, commonParam) {\n  updater(el, commonParam);\n}\nfunction traverseUpdateState(el, updater, commonParam) {\n  updateElementState(el, updater, commonParam);\n  el.isGroup && el.traverse(function (child) {\n    updateElementState(child, updater, commonParam);\n  });\n}\nexport function setStatesFlag(el, stateName) {\n  switch (stateName) {\n    case 'emphasis':\n      el.hoverState = HOVER_STATE_EMPHASIS;\n      break;\n    case 'normal':\n      el.hoverState = HOVER_STATE_NORMAL;\n      break;\n    case 'blur':\n      el.hoverState = HOVER_STATE_BLUR;\n      break;\n    case 'select':\n      el.selected = true;\n  }\n}\n/**\r\n * If we reuse elements when rerender.\r\n * DON'T forget to clearStates before we update the style and shape.\r\n * Or we may update on the wrong state instead of normal state.\r\n */\n\nexport function clearStates(el) {\n  if (el.isGroup) {\n    el.traverse(function (child) {\n      child.clearStates();\n    });\n  } else {\n    el.clearStates();\n  }\n}\nfunction getFromStateStyle(el, props, toStateName, defaultValue) {\n  var style = el.style;\n  var fromState = {};\n  for (var i = 0; i < props.length; i++) {\n    var propName = props[i];\n    var val = style[propName];\n    fromState[propName] = val == null ? defaultValue && defaultValue[propName] : val;\n  }\n  for (var i = 0; i < el.animators.length; i++) {\n    var animator = el.animators[i];\n    if (animator.__fromStateTransition // Don't consider the animation to emphasis state.\n    && animator.__fromStateTransition.indexOf(toStateName) < 0 && animator.targetName === 'style') {\n      animator.saveTo(fromState, props);\n    }\n  }\n  return fromState;\n}\nfunction createEmphasisDefaultState(el, stateName, targetStates, state) {\n  var hasSelect = targetStates && indexOf(targetStates, 'select') >= 0;\n  var cloned = false;\n  if (el instanceof Path) {\n    var store = getSavedStates(el);\n    var fromFill = hasSelect ? store.selectFill || store.normalFill : store.normalFill;\n    var fromStroke = hasSelect ? store.selectStroke || store.normalStroke : store.normalStroke;\n    if (hasFillOrStroke(fromFill) || hasFillOrStroke(fromStroke)) {\n      state = state || {};\n      var emphasisStyle = state.style || {}; // inherit case\n\n      if (emphasisStyle.fill === 'inherit') {\n        cloned = true;\n        state = extend({}, state);\n        emphasisStyle = extend({}, emphasisStyle);\n        emphasisStyle.fill = fromFill;\n      } // Apply default color lift\n      else if (!hasFillOrStroke(emphasisStyle.fill) && hasFillOrStroke(fromFill)) {\n        cloned = true; // Not modify the original value.\n\n        state = extend({}, state);\n        emphasisStyle = extend({}, emphasisStyle); // Already being applied 'emphasis'. DON'T lift color multiple times.\n\n        emphasisStyle.fill = liftColor(fromFill);\n      } // Not highlight stroke if fill has been highlighted.\n      else if (!hasFillOrStroke(emphasisStyle.stroke) && hasFillOrStroke(fromStroke)) {\n        if (!cloned) {\n          state = extend({}, state);\n          emphasisStyle = extend({}, emphasisStyle);\n        }\n        emphasisStyle.stroke = liftColor(fromStroke);\n      }\n      state.style = emphasisStyle;\n    }\n  }\n  if (state) {\n    // TODO Share with textContent?\n    if (state.z2 == null) {\n      if (!cloned) {\n        state = extend({}, state);\n      }\n      var z2EmphasisLift = el.z2EmphasisLift;\n      state.z2 = el.z2 + (z2EmphasisLift != null ? z2EmphasisLift : Z2_EMPHASIS_LIFT);\n    }\n  }\n  return state;\n}\nfunction createSelectDefaultState(el, stateName, state) {\n  // const hasSelect = indexOf(el.currentStates, stateName) >= 0;\n  if (state) {\n    // TODO Share with textContent?\n    if (state.z2 == null) {\n      state = extend({}, state);\n      var z2SelectLift = el.z2SelectLift;\n      state.z2 = el.z2 + (z2SelectLift != null ? z2SelectLift : Z2_SELECT_LIFT);\n    }\n  }\n  return state;\n}\nfunction createBlurDefaultState(el, stateName, state) {\n  var hasBlur = indexOf(el.currentStates, stateName) >= 0;\n  var currentOpacity = el.style.opacity;\n  var fromState = !hasBlur ? getFromStateStyle(el, ['opacity'], stateName, {\n    opacity: 1\n  }) : null;\n  state = state || {};\n  var blurStyle = state.style || {};\n  if (blurStyle.opacity == null) {\n    // clone state\n    state = extend({}, state);\n    blurStyle = extend({\n      // Already being applied 'emphasis'. DON'T mul opacity multiple times.\n      opacity: hasBlur ? currentOpacity : fromState.opacity * 0.1\n    }, blurStyle);\n    state.style = blurStyle;\n  }\n  return state;\n}\nfunction elementStateProxy(stateName, targetStates) {\n  var state = this.states[stateName];\n  if (this.style) {\n    if (stateName === 'emphasis') {\n      return createEmphasisDefaultState(this, stateName, targetStates, state);\n    } else if (stateName === 'blur') {\n      return createBlurDefaultState(this, stateName, state);\n    } else if (stateName === 'select') {\n      return createSelectDefaultState(this, stateName, state);\n    }\n  }\n  return state;\n}\n/**\r\n * Set hover style (namely \"emphasis style\") of element.\r\n * @param el Should not be `zrender/graphic/Group`.\r\n * @param focus 'self' | 'selfInSeries' | 'series'\r\n */\n\nexport function setDefaultStateProxy(el) {\n  el.stateProxy = elementStateProxy;\n  var textContent = el.getTextContent();\n  var textGuide = el.getTextGuideLine();\n  if (textContent) {\n    textContent.stateProxy = elementStateProxy;\n  }\n  if (textGuide) {\n    textGuide.stateProxy = elementStateProxy;\n  }\n}\nexport function enterEmphasisWhenMouseOver(el, e) {\n  !shouldSilent(el, e) // \"emphasis\" event highlight has higher priority than mouse highlight.\n  && !el.__highByOuter && traverseUpdateState(el, singleEnterEmphasis);\n}\nexport function leaveEmphasisWhenMouseOut(el, e) {\n  !shouldSilent(el, e) // \"emphasis\" event highlight has higher priority than mouse highlight.\n  && !el.__highByOuter && traverseUpdateState(el, singleLeaveEmphasis);\n}\nexport function enterEmphasis(el, highlightDigit) {\n  el.__highByOuter |= 1 << (highlightDigit || 0);\n  traverseUpdateState(el, singleEnterEmphasis);\n}\nexport function leaveEmphasis(el, highlightDigit) {\n  !(el.__highByOuter &= ~(1 << (highlightDigit || 0))) && traverseUpdateState(el, singleLeaveEmphasis);\n}\nexport function enterBlur(el) {\n  traverseUpdateState(el, singleEnterBlur);\n}\nexport function leaveBlur(el) {\n  traverseUpdateState(el, singleLeaveBlur);\n}\nexport function enterSelect(el) {\n  traverseUpdateState(el, singleEnterSelect);\n}\nexport function leaveSelect(el) {\n  traverseUpdateState(el, singleLeaveSelect);\n}\nfunction shouldSilent(el, e) {\n  return el.__highDownSilentOnTouch && e.zrByTouch;\n}\nexport function allLeaveBlur(api) {\n  var model = api.getModel();\n  var leaveBlurredSeries = [];\n  var allComponentViews = [];\n  model.eachComponent(function (componentType, componentModel) {\n    var componentStates = getComponentStates(componentModel);\n    var isSeries = componentType === 'series';\n    var view = isSeries ? api.getViewOfSeriesModel(componentModel) : api.getViewOfComponentModel(componentModel);\n    !isSeries && allComponentViews.push(view);\n    if (componentStates.isBlured) {\n      // Leave blur anyway\n      view.group.traverse(function (child) {\n        singleLeaveBlur(child);\n      });\n      isSeries && leaveBlurredSeries.push(componentModel);\n    }\n    componentStates.isBlured = false;\n  });\n  each(allComponentViews, function (view) {\n    if (view && view.toggleBlurSeries) {\n      view.toggleBlurSeries(leaveBlurredSeries, false, model);\n    }\n  });\n}\nexport function blurSeries(targetSeriesIndex, focus, blurScope, api) {\n  var ecModel = api.getModel();\n  blurScope = blurScope || 'coordinateSystem';\n  function leaveBlurOfIndices(data, dataIndices) {\n    for (var i = 0; i < dataIndices.length; i++) {\n      var itemEl = data.getItemGraphicEl(dataIndices[i]);\n      itemEl && leaveBlur(itemEl);\n    }\n  }\n  if (targetSeriesIndex == null) {\n    return;\n  }\n  if (!focus || focus === 'none') {\n    return;\n  }\n  var targetSeriesModel = ecModel.getSeriesByIndex(targetSeriesIndex);\n  var targetCoordSys = targetSeriesModel.coordinateSystem;\n  if (targetCoordSys && targetCoordSys.master) {\n    targetCoordSys = targetCoordSys.master;\n  }\n  var blurredSeries = [];\n  ecModel.eachSeries(function (seriesModel) {\n    var sameSeries = targetSeriesModel === seriesModel;\n    var coordSys = seriesModel.coordinateSystem;\n    if (coordSys && coordSys.master) {\n      coordSys = coordSys.master;\n    }\n    var sameCoordSys = coordSys && targetCoordSys ? coordSys === targetCoordSys : sameSeries; // If there is no coordinate system. use sameSeries instead.\n\n    if (!(\n    // Not blur other series if blurScope series\n    blurScope === 'series' && !sameSeries // Not blur other coordinate system if blurScope is coordinateSystem\n    || blurScope === 'coordinateSystem' && !sameCoordSys // Not blur self series if focus is series.\n    || focus === 'series' && sameSeries // TODO blurScope: coordinate system\n    )) {\n      var view = api.getViewOfSeriesModel(seriesModel);\n      view.group.traverse(function (child) {\n        // For the elements that have been triggered by other components,\n        // and are still required to be highlighted,\n        // because the current is directly forced to blur the element,\n        // it will cause the focus self to be unable to highlight, so skip the blur of this element.\n        if (child.__highByOuter && sameSeries && focus === 'self') {\n          return;\n        }\n        singleEnterBlur(child);\n      });\n      if (isArrayLike(focus)) {\n        leaveBlurOfIndices(seriesModel.getData(), focus);\n      } else if (isObject(focus)) {\n        var dataTypes = keys(focus);\n        for (var d = 0; d < dataTypes.length; d++) {\n          leaveBlurOfIndices(seriesModel.getData(dataTypes[d]), focus[dataTypes[d]]);\n        }\n      }\n      blurredSeries.push(seriesModel);\n      getComponentStates(seriesModel).isBlured = true;\n    }\n  });\n  ecModel.eachComponent(function (componentType, componentModel) {\n    if (componentType === 'series') {\n      return;\n    }\n    var view = api.getViewOfComponentModel(componentModel);\n    if (view && view.toggleBlurSeries) {\n      view.toggleBlurSeries(blurredSeries, true, ecModel);\n    }\n  });\n}\nexport function blurComponent(componentMainType, componentIndex, api) {\n  if (componentMainType == null || componentIndex == null) {\n    return;\n  }\n  var componentModel = api.getModel().getComponent(componentMainType, componentIndex);\n  if (!componentModel) {\n    return;\n  }\n  getComponentStates(componentModel).isBlured = true;\n  var view = api.getViewOfComponentModel(componentModel);\n  if (!view || !view.focusBlurEnabled) {\n    return;\n  }\n  view.group.traverse(function (child) {\n    singleEnterBlur(child);\n  });\n}\nexport function blurSeriesFromHighlightPayload(seriesModel, payload, api) {\n  var seriesIndex = seriesModel.seriesIndex;\n  var data = seriesModel.getData(payload.dataType);\n  if (!data) {\n    if (process.env.NODE_ENV !== 'production') {\n      error(\"Unknown dataType \" + payload.dataType);\n    }\n    return;\n  }\n  var dataIndex = queryDataIndex(data, payload); // Pick the first one if there is multiple/none exists.\n\n  dataIndex = (isArray(dataIndex) ? dataIndex[0] : dataIndex) || 0;\n  var el = data.getItemGraphicEl(dataIndex);\n  if (!el) {\n    var count = data.count();\n    var current = 0; // If data on dataIndex is NaN.\n\n    while (!el && current < count) {\n      el = data.getItemGraphicEl(current++);\n    }\n  }\n  if (el) {\n    var ecData = getECData(el);\n    blurSeries(seriesIndex, ecData.focus, ecData.blurScope, api);\n  } else {\n    // If there is no element put on the data. Try getting it from raw option\n    // TODO Should put it on seriesModel?\n    var focus_1 = seriesModel.get(['emphasis', 'focus']);\n    var blurScope = seriesModel.get(['emphasis', 'blurScope']);\n    if (focus_1 != null) {\n      blurSeries(seriesIndex, focus_1, blurScope, api);\n    }\n  }\n}\nexport function findComponentHighDownDispatchers(componentMainType, componentIndex, name, api) {\n  var ret = {\n    focusSelf: false,\n    dispatchers: null\n  };\n  if (componentMainType == null || componentMainType === 'series' || componentIndex == null || name == null) {\n    return ret;\n  }\n  var componentModel = api.getModel().getComponent(componentMainType, componentIndex);\n  if (!componentModel) {\n    return ret;\n  }\n  var view = api.getViewOfComponentModel(componentModel);\n  if (!view || !view.findHighDownDispatchers) {\n    return ret;\n  }\n  var dispatchers = view.findHighDownDispatchers(name); // At presnet, the component (like Geo) only blur inside itself.\n  // So we do not use `blurScope` in component.\n\n  var focusSelf;\n  for (var i = 0; i < dispatchers.length; i++) {\n    if (process.env.NODE_ENV !== 'production' && !isHighDownDispatcher(dispatchers[i])) {\n      error('param should be highDownDispatcher');\n    }\n    if (getECData(dispatchers[i]).focus === 'self') {\n      focusSelf = true;\n      break;\n    }\n  }\n  return {\n    focusSelf: focusSelf,\n    dispatchers: dispatchers\n  };\n}\nexport function handleGlobalMouseOverForHighDown(dispatcher, e, api) {\n  if (process.env.NODE_ENV !== 'production' && !isHighDownDispatcher(dispatcher)) {\n    error('param should be highDownDispatcher');\n  }\n  var ecData = getECData(dispatcher);\n  var _a = findComponentHighDownDispatchers(ecData.componentMainType, ecData.componentIndex, ecData.componentHighDownName, api),\n    dispatchers = _a.dispatchers,\n    focusSelf = _a.focusSelf; // If `findHighDownDispatchers` is supported on the component,\n  // highlight/downplay elements with the same name.\n\n  if (dispatchers) {\n    if (focusSelf) {\n      blurComponent(ecData.componentMainType, ecData.componentIndex, api);\n    }\n    each(dispatchers, function (dispatcher) {\n      return enterEmphasisWhenMouseOver(dispatcher, e);\n    });\n  } else {\n    // Try blur all in the related series. Then emphasis the hoverred.\n    // TODO. progressive mode.\n    blurSeries(ecData.seriesIndex, ecData.focus, ecData.blurScope, api);\n    if (ecData.focus === 'self') {\n      blurComponent(ecData.componentMainType, ecData.componentIndex, api);\n    } // Other than series, component that not support `findHighDownDispatcher` will\n    // also use it. But in this case, highlight/downplay are only supported in\n    // mouse hover but not in dispatchAction.\n\n    enterEmphasisWhenMouseOver(dispatcher, e);\n  }\n}\nexport function handleGlobalMouseOutForHighDown(dispatcher, e, api) {\n  if (process.env.NODE_ENV !== 'production' && !isHighDownDispatcher(dispatcher)) {\n    error('param should be highDownDispatcher');\n  }\n  allLeaveBlur(api);\n  var ecData = getECData(dispatcher);\n  var dispatchers = findComponentHighDownDispatchers(ecData.componentMainType, ecData.componentIndex, ecData.componentHighDownName, api).dispatchers;\n  if (dispatchers) {\n    each(dispatchers, function (dispatcher) {\n      return leaveEmphasisWhenMouseOut(dispatcher, e);\n    });\n  } else {\n    leaveEmphasisWhenMouseOut(dispatcher, e);\n  }\n}\nexport function toggleSelectionFromPayload(seriesModel, payload, api) {\n  if (!isSelectChangePayload(payload)) {\n    return;\n  }\n  var dataType = payload.dataType;\n  var data = seriesModel.getData(dataType);\n  var dataIndex = queryDataIndex(data, payload);\n  if (!isArray(dataIndex)) {\n    dataIndex = [dataIndex];\n  }\n  seriesModel[payload.type === TOGGLE_SELECT_ACTION_TYPE ? 'toggleSelect' : payload.type === SELECT_ACTION_TYPE ? 'select' : 'unselect'](dataIndex, dataType);\n}\nexport function updateSeriesElementSelection(seriesModel) {\n  var allData = seriesModel.getAllData();\n  each(allData, function (_a) {\n    var data = _a.data,\n      type = _a.type;\n    data.eachItemGraphicEl(function (el, idx) {\n      seriesModel.isSelected(idx, type) ? enterSelect(el) : leaveSelect(el);\n    });\n  });\n}\nexport function getAllSelectedIndices(ecModel) {\n  var ret = [];\n  ecModel.eachSeries(function (seriesModel) {\n    var allData = seriesModel.getAllData();\n    each(allData, function (_a) {\n      var data = _a.data,\n        type = _a.type;\n      var dataIndices = seriesModel.getSelectedDataIndices();\n      if (dataIndices.length > 0) {\n        var item = {\n          dataIndex: dataIndices,\n          seriesIndex: seriesModel.seriesIndex\n        };\n        if (type != null) {\n          item.dataType = type;\n        }\n        ret.push(item);\n      }\n    });\n  });\n  return ret;\n}\n/**\r\n * Enable the function that mouseover will trigger the emphasis state.\r\n *\r\n * NOTE:\r\n * This function should be used on the element with dataIndex, seriesIndex.\r\n *\r\n */\n\nexport function enableHoverEmphasis(el, focus, blurScope) {\n  setAsHighDownDispatcher(el, true);\n  traverseUpdateState(el, setDefaultStateProxy);\n  enableHoverFocus(el, focus, blurScope);\n}\nexport function disableHoverEmphasis(el) {\n  setAsHighDownDispatcher(el, false);\n}\nexport function toggleHoverEmphasis(el, focus, blurScope, isDisabled) {\n  isDisabled ? disableHoverEmphasis(el) : enableHoverEmphasis(el, focus, blurScope);\n}\nexport function enableHoverFocus(el, focus, blurScope) {\n  var ecData = getECData(el);\n  if (focus != null) {\n    // TODO dataIndex may be set after this function. This check is not useful.\n    // if (ecData.dataIndex == null) {\n    //     if (__DEV__) {\n    //         console.warn('focus can only been set on element with dataIndex');\n    //     }\n    // }\n    // else {\n    ecData.focus = focus;\n    ecData.blurScope = blurScope; // }\n  } else if (ecData.focus) {\n    ecData.focus = null;\n  }\n}\nvar OTHER_STATES = ['emphasis', 'blur', 'select'];\nvar defaultStyleGetterMap = {\n  itemStyle: 'getItemStyle',\n  lineStyle: 'getLineStyle',\n  areaStyle: 'getAreaStyle'\n};\n/**\r\n * Set emphasis/blur/selected states of element.\r\n */\n\nexport function setStatesStylesFromModel(el, itemModel, styleType,\n// default itemStyle\ngetter) {\n  styleType = styleType || 'itemStyle';\n  for (var i = 0; i < OTHER_STATES.length; i++) {\n    var stateName = OTHER_STATES[i];\n    var model = itemModel.getModel([stateName, styleType]);\n    var state = el.ensureState(stateName); // Let it throw error if getterType is not found.\n\n    state.style = getter ? getter(model) : model[defaultStyleGetterMap[styleType]]();\n  }\n}\n/**\r\n *\r\n * Set element as highlight / downplay dispatcher.\r\n * It will be checked when element received mouseover event or from highlight action.\r\n * It's in change of all highlight/downplay behavior of it's children.\r\n *\r\n * @param el\r\n * @param el.highDownSilentOnTouch\r\n *        In touch device, mouseover event will be trigger on touchstart event\r\n *        (see module:zrender/dom/HandlerProxy). By this mechanism, we can\r\n *        conveniently use hoverStyle when tap on touch screen without additional\r\n *        code for compatibility.\r\n *        But if the chart/component has select feature, which usually also use\r\n *        hoverStyle, there might be conflict between 'select-highlight' and\r\n *        'hover-highlight' especially when roam is enabled (see geo for example).\r\n *        In this case, `highDownSilentOnTouch` should be used to disable\r\n *        hover-highlight on touch device.\r\n * @param asDispatcher If `false`, do not set as \"highDownDispatcher\".\r\n */\n\nexport function setAsHighDownDispatcher(el, asDispatcher) {\n  var disable = asDispatcher === false;\n  var extendedEl = el; // Make `highDownSilentOnTouch` and `onStateChange` only work after\n  // `setAsHighDownDispatcher` called. Avoid it is modified by user unexpectedly.\n\n  if (el.highDownSilentOnTouch) {\n    extendedEl.__highDownSilentOnTouch = el.highDownSilentOnTouch;\n  } // Simple optimize, since this method might be\n  // called for each elements of a group in some cases.\n\n  if (!disable || extendedEl.__highDownDispatcher) {\n    // Emphasis, normal can be triggered manually by API or other components like hover link.\n    // el[method]('emphasis', onElementEmphasisEvent)[method]('normal', onElementNormalEvent);\n    // Also keep previous record.\n    extendedEl.__highByOuter = extendedEl.__highByOuter || 0;\n    extendedEl.__highDownDispatcher = !disable;\n  }\n}\nexport function isHighDownDispatcher(el) {\n  return !!(el && el.__highDownDispatcher);\n}\n/**\r\n * Enable component highlight/downplay features:\r\n * + hover link (within the same name)\r\n * + focus blur in component\r\n */\n\nexport function enableComponentHighDownFeatures(el, componentModel, componentHighDownName) {\n  var ecData = getECData(el);\n  ecData.componentMainType = componentModel.mainType;\n  ecData.componentIndex = componentModel.componentIndex;\n  ecData.componentHighDownName = componentHighDownName;\n}\n/**\r\n * Support highlight/downplay record on each elements.\r\n * For the case: hover highlight/downplay (legend, visualMap, ...) and\r\n * user triggered highlight/downplay should not conflict.\r\n * Only all of the highlightDigit cleared, return to normal.\r\n * @param {string} highlightKey\r\n * @return {number} highlightDigit\r\n */\n\nexport function getHighlightDigit(highlightKey) {\n  var highlightDigit = _highlightKeyMap[highlightKey];\n  if (highlightDigit == null && _highlightNextDigit <= 32) {\n    highlightDigit = _highlightKeyMap[highlightKey] = _highlightNextDigit++;\n  }\n  return highlightDigit;\n}\nexport function isSelectChangePayload(payload) {\n  var payloadType = payload.type;\n  return payloadType === SELECT_ACTION_TYPE || payloadType === UNSELECT_ACTION_TYPE || payloadType === TOGGLE_SELECT_ACTION_TYPE;\n}\nexport function isHighDownPayload(payload) {\n  var payloadType = payload.type;\n  return payloadType === HIGHLIGHT_ACTION_TYPE || payloadType === DOWNPLAY_ACTION_TYPE;\n}\nexport function savePathStates(el) {\n  var store = getSavedStates(el);\n  store.normalFill = el.style.fill;\n  store.normalStroke = el.style.stroke;\n  var selectState = el.states.select || {};\n  store.selectFill = selectState.style && selectState.style.fill || null;\n  store.selectStroke = selectState.style && selectState.style.stroke || null;\n}", "map": {"version": 3, "names": ["LRU", "extend", "indexOf", "isArrayLike", "isObject", "keys", "isArray", "each", "isString", "isGradientObject", "map", "getECData", "colorTool", "queryDataIndex", "makeInner", "Path", "error", "_highlightNextDigit", "_highlightKeyMap", "getSavedStates", "getComponentStates", "HOVER_STATE_NORMAL", "HOVER_STATE_BLUR", "HOVER_STATE_EMPHASIS", "SPECIAL_STATES", "DISPLAY_STATES", "Z2_EMPHASIS_LIFT", "Z2_SELECT_LIFT", "HIGHLIGHT_ACTION_TYPE", "DOWNPLAY_ACTION_TYPE", "SELECT_ACTION_TYPE", "UNSELECT_ACTION_TYPE", "TOGGLE_SELECT_ACTION_TYPE", "hasFillOrStroke", "fillOrStroke", "liftedColorCache", "liftColor", "color", "liftedColor", "get", "lift", "put", "ret", "colorStops", "stop", "offset", "doChangeHoverState", "el", "stateName", "hoverStateEnum", "onHoverStateChange", "hoverState", "singleEnterEmphasis", "singleLeaveEmphasis", "singleEnterBlur", "singleLeaveBlur", "singleEnterSelect", "selected", "singleLeaveSelect", "updateElementState", "updater", "commonParam", "traverseUpdateState", "isGroup", "traverse", "child", "setStatesFlag", "clearStates", "getFromStateStyle", "props", "toStateName", "defaultValue", "style", "fromState", "i", "length", "propName", "val", "animators", "animator", "__fromStateTransition", "targetName", "saveTo", "createEmphasisDefaultState", "targetStates", "state", "hasSelect", "cloned", "store", "fromFill", "selectFill", "normalFill", "fromStroke", "selectStroke", "normalStroke", "emphasisStyle", "fill", "stroke", "z2", "z2EmphasisLift", "createSelectDefaultState", "z2SelectLift", "createBlurDefaultState", "<PERSON><PERSON><PERSON><PERSON>", "currentStates", "currentOpacity", "opacity", "blurStyle", "elementStateProxy", "states", "setDefaultStateProxy", "stateProxy", "textContent", "getTextContent", "textGuide", "getTextGuideLine", "enterEmphasisWhenMouseOver", "e", "shouldSilent", "__highByOuter", "leaveEmphasisWhenMouseOut", "enterEmphasis", "highlightDigit", "leaveEmphasis", "enterBlur", "leaveBlur", "enterSelect", "leaveSelect", "__highDownSilentOnTouch", "zrByTouch", "allLeaveBlur", "api", "model", "getModel", "leaveBlurredSeries", "allComponentViews", "eachComponent", "componentType", "componentModel", "componentStates", "isSeries", "view", "getViewOfSeriesModel", "getViewOfComponentModel", "push", "isBlured", "group", "toggleBlurSeries", "blurSeries", "targetSeriesIndex", "focus", "blurScope", "ecModel", "leaveBlurOfIndices", "data", "dataIndices", "itemEl", "getItemGraphicEl", "targetSeriesModel", "getSeriesByIndex", "targetCoordSys", "coordinateSystem", "master", "blurredSeries", "eachSeries", "seriesModel", "sameSeries", "coordSys", "sameCoordSys", "getData", "dataTypes", "d", "blurComponent", "componentMainType", "componentIndex", "getComponent", "focusBlurEnabled", "blurSeriesFromHighlightPayload", "payload", "seriesIndex", "dataType", "process", "env", "NODE_ENV", "dataIndex", "count", "current", "ecData", "focus_1", "findComponentHighDownDispatchers", "name", "focusSelf", "dispatchers", "findHighDownDispatchers", "is<PERSON>ighD<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "handleGlobalMouseOverForHighDown", "dispatcher", "_a", "componentHighDownName", "handleGlobalMouseOutForHighDown", "toggleSelectionFromPayload", "isSelectChangePayload", "type", "updateSeriesElementSelection", "allData", "getAllData", "eachItemGraphicEl", "idx", "isSelected", "getAllSelectedIndices", "getSelectedDataIndices", "item", "enableHoverEmphasis", "setAs<PERSON>ighDownD<PERSON><PERSON><PERSON><PERSON>", "enableHoverFocus", "disableHoverEmphasis", "toggleHoverEmphasis", "isDisabled", "OTHER_STATES", "defaultStyleGetterMap", "itemStyle", "lineStyle", "areaStyle", "setStatesStylesFromModel", "itemModel", "styleType", "getter", "ensureState", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "disable", "extendedEl", "highDownSilentOnTouch", "__highDownD<PERSON><PERSON>tcher", "enableComponentHighDownFeatures", "mainType", "getHighlightDigit", "highlight<PERSON><PERSON>", "payloadType", "isHighDownPayload", "savePathStates", "selectState", "select"], "sources": ["C:/WolfSystemV2/WolfOldFrontEnd/node_modules/echarts/lib/util/states.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport LRU from 'zrender/lib/core/LRU.js';\nimport { extend, indexOf, isArrayLike, isObject, keys, isArray, each, isString, isGradientObject, map } from 'zrender/lib/core/util.js';\nimport { getECData } from './innerStore.js';\nimport * as colorTool from 'zrender/lib/tool/color.js';\nimport { queryDataIndex, makeInner } from './model.js';\nimport Path from 'zrender/lib/graphic/Path.js';\nimport { error } from './log.js'; // Reserve 0 as default.\n\nvar _highlightNextDigit = 1;\nvar _highlightKeyMap = {};\nvar getSavedStates = makeInner();\nvar getComponentStates = makeInner();\nexport var HOVER_STATE_NORMAL = 0;\nexport var HOVER_STATE_BLUR = 1;\nexport var HOVER_STATE_EMPHASIS = 2;\nexport var SPECIAL_STATES = ['emphasis', 'blur', 'select'];\nexport var DISPLAY_STATES = ['normal', 'emphasis', 'blur', 'select'];\nexport var Z2_EMPHASIS_LIFT = 10;\nexport var Z2_SELECT_LIFT = 9;\nexport var HIGHLIGHT_ACTION_TYPE = 'highlight';\nexport var DOWNPLAY_ACTION_TYPE = 'downplay';\nexport var SELECT_ACTION_TYPE = 'select';\nexport var UNSELECT_ACTION_TYPE = 'unselect';\nexport var TOGGLE_SELECT_ACTION_TYPE = 'toggleSelect';\n\nfunction hasFillOrStroke(fillOrStroke) {\n  return fillOrStroke != null && fillOrStroke !== 'none';\n} // Most lifted color are duplicated.\n\n\nvar liftedColorCache = new LRU(100);\n\nfunction liftColor(color) {\n  if (isString(color)) {\n    var liftedColor = liftedColorCache.get(color);\n\n    if (!liftedColor) {\n      liftedColor = colorTool.lift(color, -0.1);\n      liftedColorCache.put(color, liftedColor);\n    }\n\n    return liftedColor;\n  } else if (isGradientObject(color)) {\n    var ret = extend({}, color);\n    ret.colorStops = map(color.colorStops, function (stop) {\n      return {\n        offset: stop.offset,\n        color: colorTool.lift(stop.color, -0.1)\n      };\n    });\n    return ret;\n  } // Change nothing.\n\n\n  return color;\n}\n\nfunction doChangeHoverState(el, stateName, hoverStateEnum) {\n  if (el.onHoverStateChange && (el.hoverState || 0) !== hoverStateEnum) {\n    el.onHoverStateChange(stateName);\n  }\n\n  el.hoverState = hoverStateEnum;\n}\n\nfunction singleEnterEmphasis(el) {\n  // Only mark the flag.\n  // States will be applied in the echarts.ts in next frame.\n  doChangeHoverState(el, 'emphasis', HOVER_STATE_EMPHASIS);\n}\n\nfunction singleLeaveEmphasis(el) {\n  // Only mark the flag.\n  // States will be applied in the echarts.ts in next frame.\n  if (el.hoverState === HOVER_STATE_EMPHASIS) {\n    doChangeHoverState(el, 'normal', HOVER_STATE_NORMAL);\n  }\n}\n\nfunction singleEnterBlur(el) {\n  doChangeHoverState(el, 'blur', HOVER_STATE_BLUR);\n}\n\nfunction singleLeaveBlur(el) {\n  if (el.hoverState === HOVER_STATE_BLUR) {\n    doChangeHoverState(el, 'normal', HOVER_STATE_NORMAL);\n  }\n}\n\nfunction singleEnterSelect(el) {\n  el.selected = true;\n}\n\nfunction singleLeaveSelect(el) {\n  el.selected = false;\n}\n\nfunction updateElementState(el, updater, commonParam) {\n  updater(el, commonParam);\n}\n\nfunction traverseUpdateState(el, updater, commonParam) {\n  updateElementState(el, updater, commonParam);\n  el.isGroup && el.traverse(function (child) {\n    updateElementState(child, updater, commonParam);\n  });\n}\n\nexport function setStatesFlag(el, stateName) {\n  switch (stateName) {\n    case 'emphasis':\n      el.hoverState = HOVER_STATE_EMPHASIS;\n      break;\n\n    case 'normal':\n      el.hoverState = HOVER_STATE_NORMAL;\n      break;\n\n    case 'blur':\n      el.hoverState = HOVER_STATE_BLUR;\n      break;\n\n    case 'select':\n      el.selected = true;\n  }\n}\n/**\r\n * If we reuse elements when rerender.\r\n * DON'T forget to clearStates before we update the style and shape.\r\n * Or we may update on the wrong state instead of normal state.\r\n */\n\nexport function clearStates(el) {\n  if (el.isGroup) {\n    el.traverse(function (child) {\n      child.clearStates();\n    });\n  } else {\n    el.clearStates();\n  }\n}\n\nfunction getFromStateStyle(el, props, toStateName, defaultValue) {\n  var style = el.style;\n  var fromState = {};\n\n  for (var i = 0; i < props.length; i++) {\n    var propName = props[i];\n    var val = style[propName];\n    fromState[propName] = val == null ? defaultValue && defaultValue[propName] : val;\n  }\n\n  for (var i = 0; i < el.animators.length; i++) {\n    var animator = el.animators[i];\n\n    if (animator.__fromStateTransition // Don't consider the animation to emphasis state.\n    && animator.__fromStateTransition.indexOf(toStateName) < 0 && animator.targetName === 'style') {\n      animator.saveTo(fromState, props);\n    }\n  }\n\n  return fromState;\n}\n\nfunction createEmphasisDefaultState(el, stateName, targetStates, state) {\n  var hasSelect = targetStates && indexOf(targetStates, 'select') >= 0;\n  var cloned = false;\n\n  if (el instanceof Path) {\n    var store = getSavedStates(el);\n    var fromFill = hasSelect ? store.selectFill || store.normalFill : store.normalFill;\n    var fromStroke = hasSelect ? store.selectStroke || store.normalStroke : store.normalStroke;\n\n    if (hasFillOrStroke(fromFill) || hasFillOrStroke(fromStroke)) {\n      state = state || {};\n      var emphasisStyle = state.style || {}; // inherit case\n\n      if (emphasisStyle.fill === 'inherit') {\n        cloned = true;\n        state = extend({}, state);\n        emphasisStyle = extend({}, emphasisStyle);\n        emphasisStyle.fill = fromFill;\n      } // Apply default color lift\n      else if (!hasFillOrStroke(emphasisStyle.fill) && hasFillOrStroke(fromFill)) {\n          cloned = true; // Not modify the original value.\n\n          state = extend({}, state);\n          emphasisStyle = extend({}, emphasisStyle); // Already being applied 'emphasis'. DON'T lift color multiple times.\n\n          emphasisStyle.fill = liftColor(fromFill);\n        } // Not highlight stroke if fill has been highlighted.\n        else if (!hasFillOrStroke(emphasisStyle.stroke) && hasFillOrStroke(fromStroke)) {\n            if (!cloned) {\n              state = extend({}, state);\n              emphasisStyle = extend({}, emphasisStyle);\n            }\n\n            emphasisStyle.stroke = liftColor(fromStroke);\n          }\n\n      state.style = emphasisStyle;\n    }\n  }\n\n  if (state) {\n    // TODO Share with textContent?\n    if (state.z2 == null) {\n      if (!cloned) {\n        state = extend({}, state);\n      }\n\n      var z2EmphasisLift = el.z2EmphasisLift;\n      state.z2 = el.z2 + (z2EmphasisLift != null ? z2EmphasisLift : Z2_EMPHASIS_LIFT);\n    }\n  }\n\n  return state;\n}\n\nfunction createSelectDefaultState(el, stateName, state) {\n  // const hasSelect = indexOf(el.currentStates, stateName) >= 0;\n  if (state) {\n    // TODO Share with textContent?\n    if (state.z2 == null) {\n      state = extend({}, state);\n      var z2SelectLift = el.z2SelectLift;\n      state.z2 = el.z2 + (z2SelectLift != null ? z2SelectLift : Z2_SELECT_LIFT);\n    }\n  }\n\n  return state;\n}\n\nfunction createBlurDefaultState(el, stateName, state) {\n  var hasBlur = indexOf(el.currentStates, stateName) >= 0;\n  var currentOpacity = el.style.opacity;\n  var fromState = !hasBlur ? getFromStateStyle(el, ['opacity'], stateName, {\n    opacity: 1\n  }) : null;\n  state = state || {};\n  var blurStyle = state.style || {};\n\n  if (blurStyle.opacity == null) {\n    // clone state\n    state = extend({}, state);\n    blurStyle = extend({\n      // Already being applied 'emphasis'. DON'T mul opacity multiple times.\n      opacity: hasBlur ? currentOpacity : fromState.opacity * 0.1\n    }, blurStyle);\n    state.style = blurStyle;\n  }\n\n  return state;\n}\n\nfunction elementStateProxy(stateName, targetStates) {\n  var state = this.states[stateName];\n\n  if (this.style) {\n    if (stateName === 'emphasis') {\n      return createEmphasisDefaultState(this, stateName, targetStates, state);\n    } else if (stateName === 'blur') {\n      return createBlurDefaultState(this, stateName, state);\n    } else if (stateName === 'select') {\n      return createSelectDefaultState(this, stateName, state);\n    }\n  }\n\n  return state;\n}\n/**\r\n * Set hover style (namely \"emphasis style\") of element.\r\n * @param el Should not be `zrender/graphic/Group`.\r\n * @param focus 'self' | 'selfInSeries' | 'series'\r\n */\n\n\nexport function setDefaultStateProxy(el) {\n  el.stateProxy = elementStateProxy;\n  var textContent = el.getTextContent();\n  var textGuide = el.getTextGuideLine();\n\n  if (textContent) {\n    textContent.stateProxy = elementStateProxy;\n  }\n\n  if (textGuide) {\n    textGuide.stateProxy = elementStateProxy;\n  }\n}\nexport function enterEmphasisWhenMouseOver(el, e) {\n  !shouldSilent(el, e) // \"emphasis\" event highlight has higher priority than mouse highlight.\n  && !el.__highByOuter && traverseUpdateState(el, singleEnterEmphasis);\n}\nexport function leaveEmphasisWhenMouseOut(el, e) {\n  !shouldSilent(el, e) // \"emphasis\" event highlight has higher priority than mouse highlight.\n  && !el.__highByOuter && traverseUpdateState(el, singleLeaveEmphasis);\n}\nexport function enterEmphasis(el, highlightDigit) {\n  el.__highByOuter |= 1 << (highlightDigit || 0);\n  traverseUpdateState(el, singleEnterEmphasis);\n}\nexport function leaveEmphasis(el, highlightDigit) {\n  !(el.__highByOuter &= ~(1 << (highlightDigit || 0))) && traverseUpdateState(el, singleLeaveEmphasis);\n}\nexport function enterBlur(el) {\n  traverseUpdateState(el, singleEnterBlur);\n}\nexport function leaveBlur(el) {\n  traverseUpdateState(el, singleLeaveBlur);\n}\nexport function enterSelect(el) {\n  traverseUpdateState(el, singleEnterSelect);\n}\nexport function leaveSelect(el) {\n  traverseUpdateState(el, singleLeaveSelect);\n}\n\nfunction shouldSilent(el, e) {\n  return el.__highDownSilentOnTouch && e.zrByTouch;\n}\n\nexport function allLeaveBlur(api) {\n  var model = api.getModel();\n  var leaveBlurredSeries = [];\n  var allComponentViews = [];\n  model.eachComponent(function (componentType, componentModel) {\n    var componentStates = getComponentStates(componentModel);\n    var isSeries = componentType === 'series';\n    var view = isSeries ? api.getViewOfSeriesModel(componentModel) : api.getViewOfComponentModel(componentModel);\n    !isSeries && allComponentViews.push(view);\n\n    if (componentStates.isBlured) {\n      // Leave blur anyway\n      view.group.traverse(function (child) {\n        singleLeaveBlur(child);\n      });\n      isSeries && leaveBlurredSeries.push(componentModel);\n    }\n\n    componentStates.isBlured = false;\n  });\n  each(allComponentViews, function (view) {\n    if (view && view.toggleBlurSeries) {\n      view.toggleBlurSeries(leaveBlurredSeries, false, model);\n    }\n  });\n}\nexport function blurSeries(targetSeriesIndex, focus, blurScope, api) {\n  var ecModel = api.getModel();\n  blurScope = blurScope || 'coordinateSystem';\n\n  function leaveBlurOfIndices(data, dataIndices) {\n    for (var i = 0; i < dataIndices.length; i++) {\n      var itemEl = data.getItemGraphicEl(dataIndices[i]);\n      itemEl && leaveBlur(itemEl);\n    }\n  }\n\n  if (targetSeriesIndex == null) {\n    return;\n  }\n\n  if (!focus || focus === 'none') {\n    return;\n  }\n\n  var targetSeriesModel = ecModel.getSeriesByIndex(targetSeriesIndex);\n  var targetCoordSys = targetSeriesModel.coordinateSystem;\n\n  if (targetCoordSys && targetCoordSys.master) {\n    targetCoordSys = targetCoordSys.master;\n  }\n\n  var blurredSeries = [];\n  ecModel.eachSeries(function (seriesModel) {\n    var sameSeries = targetSeriesModel === seriesModel;\n    var coordSys = seriesModel.coordinateSystem;\n\n    if (coordSys && coordSys.master) {\n      coordSys = coordSys.master;\n    }\n\n    var sameCoordSys = coordSys && targetCoordSys ? coordSys === targetCoordSys : sameSeries; // If there is no coordinate system. use sameSeries instead.\n\n    if (!( // Not blur other series if blurScope series\n    blurScope === 'series' && !sameSeries // Not blur other coordinate system if blurScope is coordinateSystem\n    || blurScope === 'coordinateSystem' && !sameCoordSys // Not blur self series if focus is series.\n    || focus === 'series' && sameSeries // TODO blurScope: coordinate system\n    )) {\n      var view = api.getViewOfSeriesModel(seriesModel);\n      view.group.traverse(function (child) {\n        // For the elements that have been triggered by other components,\n        // and are still required to be highlighted,\n        // because the current is directly forced to blur the element,\n        // it will cause the focus self to be unable to highlight, so skip the blur of this element.\n        if (child.__highByOuter && sameSeries && focus === 'self') {\n          return;\n        }\n\n        singleEnterBlur(child);\n      });\n\n      if (isArrayLike(focus)) {\n        leaveBlurOfIndices(seriesModel.getData(), focus);\n      } else if (isObject(focus)) {\n        var dataTypes = keys(focus);\n\n        for (var d = 0; d < dataTypes.length; d++) {\n          leaveBlurOfIndices(seriesModel.getData(dataTypes[d]), focus[dataTypes[d]]);\n        }\n      }\n\n      blurredSeries.push(seriesModel);\n      getComponentStates(seriesModel).isBlured = true;\n    }\n  });\n  ecModel.eachComponent(function (componentType, componentModel) {\n    if (componentType === 'series') {\n      return;\n    }\n\n    var view = api.getViewOfComponentModel(componentModel);\n\n    if (view && view.toggleBlurSeries) {\n      view.toggleBlurSeries(blurredSeries, true, ecModel);\n    }\n  });\n}\nexport function blurComponent(componentMainType, componentIndex, api) {\n  if (componentMainType == null || componentIndex == null) {\n    return;\n  }\n\n  var componentModel = api.getModel().getComponent(componentMainType, componentIndex);\n\n  if (!componentModel) {\n    return;\n  }\n\n  getComponentStates(componentModel).isBlured = true;\n  var view = api.getViewOfComponentModel(componentModel);\n\n  if (!view || !view.focusBlurEnabled) {\n    return;\n  }\n\n  view.group.traverse(function (child) {\n    singleEnterBlur(child);\n  });\n}\nexport function blurSeriesFromHighlightPayload(seriesModel, payload, api) {\n  var seriesIndex = seriesModel.seriesIndex;\n  var data = seriesModel.getData(payload.dataType);\n\n  if (!data) {\n    if (process.env.NODE_ENV !== 'production') {\n      error(\"Unknown dataType \" + payload.dataType);\n    }\n\n    return;\n  }\n\n  var dataIndex = queryDataIndex(data, payload); // Pick the first one if there is multiple/none exists.\n\n  dataIndex = (isArray(dataIndex) ? dataIndex[0] : dataIndex) || 0;\n  var el = data.getItemGraphicEl(dataIndex);\n\n  if (!el) {\n    var count = data.count();\n    var current = 0; // If data on dataIndex is NaN.\n\n    while (!el && current < count) {\n      el = data.getItemGraphicEl(current++);\n    }\n  }\n\n  if (el) {\n    var ecData = getECData(el);\n    blurSeries(seriesIndex, ecData.focus, ecData.blurScope, api);\n  } else {\n    // If there is no element put on the data. Try getting it from raw option\n    // TODO Should put it on seriesModel?\n    var focus_1 = seriesModel.get(['emphasis', 'focus']);\n    var blurScope = seriesModel.get(['emphasis', 'blurScope']);\n\n    if (focus_1 != null) {\n      blurSeries(seriesIndex, focus_1, blurScope, api);\n    }\n  }\n}\nexport function findComponentHighDownDispatchers(componentMainType, componentIndex, name, api) {\n  var ret = {\n    focusSelf: false,\n    dispatchers: null\n  };\n\n  if (componentMainType == null || componentMainType === 'series' || componentIndex == null || name == null) {\n    return ret;\n  }\n\n  var componentModel = api.getModel().getComponent(componentMainType, componentIndex);\n\n  if (!componentModel) {\n    return ret;\n  }\n\n  var view = api.getViewOfComponentModel(componentModel);\n\n  if (!view || !view.findHighDownDispatchers) {\n    return ret;\n  }\n\n  var dispatchers = view.findHighDownDispatchers(name); // At presnet, the component (like Geo) only blur inside itself.\n  // So we do not use `blurScope` in component.\n\n  var focusSelf;\n\n  for (var i = 0; i < dispatchers.length; i++) {\n    if (process.env.NODE_ENV !== 'production' && !isHighDownDispatcher(dispatchers[i])) {\n      error('param should be highDownDispatcher');\n    }\n\n    if (getECData(dispatchers[i]).focus === 'self') {\n      focusSelf = true;\n      break;\n    }\n  }\n\n  return {\n    focusSelf: focusSelf,\n    dispatchers: dispatchers\n  };\n}\nexport function handleGlobalMouseOverForHighDown(dispatcher, e, api) {\n  if (process.env.NODE_ENV !== 'production' && !isHighDownDispatcher(dispatcher)) {\n    error('param should be highDownDispatcher');\n  }\n\n  var ecData = getECData(dispatcher);\n\n  var _a = findComponentHighDownDispatchers(ecData.componentMainType, ecData.componentIndex, ecData.componentHighDownName, api),\n      dispatchers = _a.dispatchers,\n      focusSelf = _a.focusSelf; // If `findHighDownDispatchers` is supported on the component,\n  // highlight/downplay elements with the same name.\n\n\n  if (dispatchers) {\n    if (focusSelf) {\n      blurComponent(ecData.componentMainType, ecData.componentIndex, api);\n    }\n\n    each(dispatchers, function (dispatcher) {\n      return enterEmphasisWhenMouseOver(dispatcher, e);\n    });\n  } else {\n    // Try blur all in the related series. Then emphasis the hoverred.\n    // TODO. progressive mode.\n    blurSeries(ecData.seriesIndex, ecData.focus, ecData.blurScope, api);\n\n    if (ecData.focus === 'self') {\n      blurComponent(ecData.componentMainType, ecData.componentIndex, api);\n    } // Other than series, component that not support `findHighDownDispatcher` will\n    // also use it. But in this case, highlight/downplay are only supported in\n    // mouse hover but not in dispatchAction.\n\n\n    enterEmphasisWhenMouseOver(dispatcher, e);\n  }\n}\nexport function handleGlobalMouseOutForHighDown(dispatcher, e, api) {\n  if (process.env.NODE_ENV !== 'production' && !isHighDownDispatcher(dispatcher)) {\n    error('param should be highDownDispatcher');\n  }\n\n  allLeaveBlur(api);\n  var ecData = getECData(dispatcher);\n  var dispatchers = findComponentHighDownDispatchers(ecData.componentMainType, ecData.componentIndex, ecData.componentHighDownName, api).dispatchers;\n\n  if (dispatchers) {\n    each(dispatchers, function (dispatcher) {\n      return leaveEmphasisWhenMouseOut(dispatcher, e);\n    });\n  } else {\n    leaveEmphasisWhenMouseOut(dispatcher, e);\n  }\n}\nexport function toggleSelectionFromPayload(seriesModel, payload, api) {\n  if (!isSelectChangePayload(payload)) {\n    return;\n  }\n\n  var dataType = payload.dataType;\n  var data = seriesModel.getData(dataType);\n  var dataIndex = queryDataIndex(data, payload);\n\n  if (!isArray(dataIndex)) {\n    dataIndex = [dataIndex];\n  }\n\n  seriesModel[payload.type === TOGGLE_SELECT_ACTION_TYPE ? 'toggleSelect' : payload.type === SELECT_ACTION_TYPE ? 'select' : 'unselect'](dataIndex, dataType);\n}\nexport function updateSeriesElementSelection(seriesModel) {\n  var allData = seriesModel.getAllData();\n  each(allData, function (_a) {\n    var data = _a.data,\n        type = _a.type;\n    data.eachItemGraphicEl(function (el, idx) {\n      seriesModel.isSelected(idx, type) ? enterSelect(el) : leaveSelect(el);\n    });\n  });\n}\nexport function getAllSelectedIndices(ecModel) {\n  var ret = [];\n  ecModel.eachSeries(function (seriesModel) {\n    var allData = seriesModel.getAllData();\n    each(allData, function (_a) {\n      var data = _a.data,\n          type = _a.type;\n      var dataIndices = seriesModel.getSelectedDataIndices();\n\n      if (dataIndices.length > 0) {\n        var item = {\n          dataIndex: dataIndices,\n          seriesIndex: seriesModel.seriesIndex\n        };\n\n        if (type != null) {\n          item.dataType = type;\n        }\n\n        ret.push(item);\n      }\n    });\n  });\n  return ret;\n}\n/**\r\n * Enable the function that mouseover will trigger the emphasis state.\r\n *\r\n * NOTE:\r\n * This function should be used on the element with dataIndex, seriesIndex.\r\n *\r\n */\n\nexport function enableHoverEmphasis(el, focus, blurScope) {\n  setAsHighDownDispatcher(el, true);\n  traverseUpdateState(el, setDefaultStateProxy);\n  enableHoverFocus(el, focus, blurScope);\n}\nexport function disableHoverEmphasis(el) {\n  setAsHighDownDispatcher(el, false);\n}\nexport function toggleHoverEmphasis(el, focus, blurScope, isDisabled) {\n  isDisabled ? disableHoverEmphasis(el) : enableHoverEmphasis(el, focus, blurScope);\n}\nexport function enableHoverFocus(el, focus, blurScope) {\n  var ecData = getECData(el);\n\n  if (focus != null) {\n    // TODO dataIndex may be set after this function. This check is not useful.\n    // if (ecData.dataIndex == null) {\n    //     if (__DEV__) {\n    //         console.warn('focus can only been set on element with dataIndex');\n    //     }\n    // }\n    // else {\n    ecData.focus = focus;\n    ecData.blurScope = blurScope; // }\n  } else if (ecData.focus) {\n    ecData.focus = null;\n  }\n}\nvar OTHER_STATES = ['emphasis', 'blur', 'select'];\nvar defaultStyleGetterMap = {\n  itemStyle: 'getItemStyle',\n  lineStyle: 'getLineStyle',\n  areaStyle: 'getAreaStyle'\n};\n/**\r\n * Set emphasis/blur/selected states of element.\r\n */\n\nexport function setStatesStylesFromModel(el, itemModel, styleType, // default itemStyle\ngetter) {\n  styleType = styleType || 'itemStyle';\n\n  for (var i = 0; i < OTHER_STATES.length; i++) {\n    var stateName = OTHER_STATES[i];\n    var model = itemModel.getModel([stateName, styleType]);\n    var state = el.ensureState(stateName); // Let it throw error if getterType is not found.\n\n    state.style = getter ? getter(model) : model[defaultStyleGetterMap[styleType]]();\n  }\n}\n/**\r\n *\r\n * Set element as highlight / downplay dispatcher.\r\n * It will be checked when element received mouseover event or from highlight action.\r\n * It's in change of all highlight/downplay behavior of it's children.\r\n *\r\n * @param el\r\n * @param el.highDownSilentOnTouch\r\n *        In touch device, mouseover event will be trigger on touchstart event\r\n *        (see module:zrender/dom/HandlerProxy). By this mechanism, we can\r\n *        conveniently use hoverStyle when tap on touch screen without additional\r\n *        code for compatibility.\r\n *        But if the chart/component has select feature, which usually also use\r\n *        hoverStyle, there might be conflict between 'select-highlight' and\r\n *        'hover-highlight' especially when roam is enabled (see geo for example).\r\n *        In this case, `highDownSilentOnTouch` should be used to disable\r\n *        hover-highlight on touch device.\r\n * @param asDispatcher If `false`, do not set as \"highDownDispatcher\".\r\n */\n\nexport function setAsHighDownDispatcher(el, asDispatcher) {\n  var disable = asDispatcher === false;\n  var extendedEl = el; // Make `highDownSilentOnTouch` and `onStateChange` only work after\n  // `setAsHighDownDispatcher` called. Avoid it is modified by user unexpectedly.\n\n  if (el.highDownSilentOnTouch) {\n    extendedEl.__highDownSilentOnTouch = el.highDownSilentOnTouch;\n  } // Simple optimize, since this method might be\n  // called for each elements of a group in some cases.\n\n\n  if (!disable || extendedEl.__highDownDispatcher) {\n    // Emphasis, normal can be triggered manually by API or other components like hover link.\n    // el[method]('emphasis', onElementEmphasisEvent)[method]('normal', onElementNormalEvent);\n    // Also keep previous record.\n    extendedEl.__highByOuter = extendedEl.__highByOuter || 0;\n    extendedEl.__highDownDispatcher = !disable;\n  }\n}\nexport function isHighDownDispatcher(el) {\n  return !!(el && el.__highDownDispatcher);\n}\n/**\r\n * Enable component highlight/downplay features:\r\n * + hover link (within the same name)\r\n * + focus blur in component\r\n */\n\nexport function enableComponentHighDownFeatures(el, componentModel, componentHighDownName) {\n  var ecData = getECData(el);\n  ecData.componentMainType = componentModel.mainType;\n  ecData.componentIndex = componentModel.componentIndex;\n  ecData.componentHighDownName = componentHighDownName;\n}\n/**\r\n * Support highlight/downplay record on each elements.\r\n * For the case: hover highlight/downplay (legend, visualMap, ...) and\r\n * user triggered highlight/downplay should not conflict.\r\n * Only all of the highlightDigit cleared, return to normal.\r\n * @param {string} highlightKey\r\n * @return {number} highlightDigit\r\n */\n\nexport function getHighlightDigit(highlightKey) {\n  var highlightDigit = _highlightKeyMap[highlightKey];\n\n  if (highlightDigit == null && _highlightNextDigit <= 32) {\n    highlightDigit = _highlightKeyMap[highlightKey] = _highlightNextDigit++;\n  }\n\n  return highlightDigit;\n}\nexport function isSelectChangePayload(payload) {\n  var payloadType = payload.type;\n  return payloadType === SELECT_ACTION_TYPE || payloadType === UNSELECT_ACTION_TYPE || payloadType === TOGGLE_SELECT_ACTION_TYPE;\n}\nexport function isHighDownPayload(payload) {\n  var payloadType = payload.type;\n  return payloadType === HIGHLIGHT_ACTION_TYPE || payloadType === DOWNPLAY_ACTION_TYPE;\n}\nexport function savePathStates(el) {\n  var store = getSavedStates(el);\n  store.normalFill = el.style.fill;\n  store.normalStroke = el.style.stroke;\n  var selectState = el.states.select || {};\n  store.selectFill = selectState.style && selectState.style.fill || null;\n  store.selectStroke = selectState.style && selectState.style.stroke || null;\n}"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAOA,GAAG,MAAM,yBAAyB;AACzC,SAASC,MAAM,EAAEC,OAAO,EAAEC,WAAW,EAAEC,QAAQ,EAAEC,IAAI,EAAEC,OAAO,EAAEC,IAAI,EAAEC,QAAQ,EAAEC,gBAAgB,EAAEC,GAAG,QAAQ,0BAA0B;AACvI,SAASC,SAAS,QAAQ,iBAAiB;AAC3C,OAAO,KAAKC,SAAS,MAAM,2BAA2B;AACtD,SAASC,cAAc,EAAEC,SAAS,QAAQ,YAAY;AACtD,OAAOC,IAAI,MAAM,6BAA6B;AAC9C,SAASC,KAAK,QAAQ,UAAU,CAAC,CAAC;;AAElC,IAAIC,mBAAmB,GAAG,CAAC;AAC3B,IAAIC,gBAAgB,GAAG,CAAC,CAAC;AACzB,IAAIC,cAAc,GAAGL,SAAS,CAAC,CAAC;AAChC,IAAIM,kBAAkB,GAAGN,SAAS,CAAC,CAAC;AACpC,OAAO,IAAIO,kBAAkB,GAAG,CAAC;AACjC,OAAO,IAAIC,gBAAgB,GAAG,CAAC;AAC/B,OAAO,IAAIC,oBAAoB,GAAG,CAAC;AACnC,OAAO,IAAIC,cAAc,GAAG,CAAC,UAAU,EAAE,MAAM,EAAE,QAAQ,CAAC;AAC1D,OAAO,IAAIC,cAAc,GAAG,CAAC,QAAQ,EAAE,UAAU,EAAE,MAAM,EAAE,QAAQ,CAAC;AACpE,OAAO,IAAIC,gBAAgB,GAAG,EAAE;AAChC,OAAO,IAAIC,cAAc,GAAG,CAAC;AAC7B,OAAO,IAAIC,qBAAqB,GAAG,WAAW;AAC9C,OAAO,IAAIC,oBAAoB,GAAG,UAAU;AAC5C,OAAO,IAAIC,kBAAkB,GAAG,QAAQ;AACxC,OAAO,IAAIC,oBAAoB,GAAG,UAAU;AAC5C,OAAO,IAAIC,yBAAyB,GAAG,cAAc;AAErD,SAASC,eAAeA,CAACC,YAAY,EAAE;EACrC,OAAOA,YAAY,IAAI,IAAI,IAAIA,YAAY,KAAK,MAAM;AACxD,CAAC,CAAC;;AAGF,IAAIC,gBAAgB,GAAG,IAAInC,GAAG,CAAC,GAAG,CAAC;AAEnC,SAASoC,SAASA,CAACC,KAAK,EAAE;EACxB,IAAI7B,QAAQ,CAAC6B,KAAK,CAAC,EAAE;IACnB,IAAIC,WAAW,GAAGH,gBAAgB,CAACI,GAAG,CAACF,KAAK,CAAC;IAE7C,IAAI,CAACC,WAAW,EAAE;MAChBA,WAAW,GAAG1B,SAAS,CAAC4B,IAAI,CAACH,KAAK,EAAE,CAAC,GAAG,CAAC;MACzCF,gBAAgB,CAACM,GAAG,CAACJ,KAAK,EAAEC,WAAW,CAAC;IAC1C;IAEA,OAAOA,WAAW;EACpB,CAAC,MAAM,IAAI7B,gBAAgB,CAAC4B,KAAK,CAAC,EAAE;IAClC,IAAIK,GAAG,GAAGzC,MAAM,CAAC,CAAC,CAAC,EAAEoC,KAAK,CAAC;IAC3BK,GAAG,CAACC,UAAU,GAAGjC,GAAG,CAAC2B,KAAK,CAACM,UAAU,EAAE,UAAUC,IAAI,EAAE;MACrD,OAAO;QACLC,MAAM,EAAED,IAAI,CAACC,MAAM;QACnBR,KAAK,EAAEzB,SAAS,CAAC4B,IAAI,CAACI,IAAI,CAACP,KAAK,EAAE,CAAC,GAAG;MACxC,CAAC;IACH,CAAC,CAAC;IACF,OAAOK,GAAG;EACZ,CAAC,CAAC;;EAGF,OAAOL,KAAK;AACd;AAEA,SAASS,kBAAkBA,CAACC,EAAE,EAAEC,SAAS,EAAEC,cAAc,EAAE;EACzD,IAAIF,EAAE,CAACG,kBAAkB,IAAI,CAACH,EAAE,CAACI,UAAU,IAAI,CAAC,MAAMF,cAAc,EAAE;IACpEF,EAAE,CAACG,kBAAkB,CAACF,SAAS,CAAC;EAClC;EAEAD,EAAE,CAACI,UAAU,GAAGF,cAAc;AAChC;AAEA,SAASG,mBAAmBA,CAACL,EAAE,EAAE;EAC/B;EACA;EACAD,kBAAkB,CAACC,EAAE,EAAE,UAAU,EAAExB,oBAAoB,CAAC;AAC1D;AAEA,SAAS8B,mBAAmBA,CAACN,EAAE,EAAE;EAC/B;EACA;EACA,IAAIA,EAAE,CAACI,UAAU,KAAK5B,oBAAoB,EAAE;IAC1CuB,kBAAkB,CAACC,EAAE,EAAE,QAAQ,EAAE1B,kBAAkB,CAAC;EACtD;AACF;AAEA,SAASiC,eAAeA,CAACP,EAAE,EAAE;EAC3BD,kBAAkB,CAACC,EAAE,EAAE,MAAM,EAAEzB,gBAAgB,CAAC;AAClD;AAEA,SAASiC,eAAeA,CAACR,EAAE,EAAE;EAC3B,IAAIA,EAAE,CAACI,UAAU,KAAK7B,gBAAgB,EAAE;IACtCwB,kBAAkB,CAACC,EAAE,EAAE,QAAQ,EAAE1B,kBAAkB,CAAC;EACtD;AACF;AAEA,SAASmC,iBAAiBA,CAACT,EAAE,EAAE;EAC7BA,EAAE,CAACU,QAAQ,GAAG,IAAI;AACpB;AAEA,SAASC,iBAAiBA,CAACX,EAAE,EAAE;EAC7BA,EAAE,CAACU,QAAQ,GAAG,KAAK;AACrB;AAEA,SAASE,kBAAkBA,CAACZ,EAAE,EAAEa,OAAO,EAAEC,WAAW,EAAE;EACpDD,OAAO,CAACb,EAAE,EAAEc,WAAW,CAAC;AAC1B;AAEA,SAASC,mBAAmBA,CAACf,EAAE,EAAEa,OAAO,EAAEC,WAAW,EAAE;EACrDF,kBAAkB,CAACZ,EAAE,EAAEa,OAAO,EAAEC,WAAW,CAAC;EAC5Cd,EAAE,CAACgB,OAAO,IAAIhB,EAAE,CAACiB,QAAQ,CAAC,UAAUC,KAAK,EAAE;IACzCN,kBAAkB,CAACM,KAAK,EAAEL,OAAO,EAAEC,WAAW,CAAC;EACjD,CAAC,CAAC;AACJ;AAEA,OAAO,SAASK,aAAaA,CAACnB,EAAE,EAAEC,SAAS,EAAE;EAC3C,QAAQA,SAAS;IACf,KAAK,UAAU;MACbD,EAAE,CAACI,UAAU,GAAG5B,oBAAoB;MACpC;IAEF,KAAK,QAAQ;MACXwB,EAAE,CAACI,UAAU,GAAG9B,kBAAkB;MAClC;IAEF,KAAK,MAAM;MACT0B,EAAE,CAACI,UAAU,GAAG7B,gBAAgB;MAChC;IAEF,KAAK,QAAQ;MACXyB,EAAE,CAACU,QAAQ,GAAG,IAAI;EACtB;AACF;AACA;AACA;AACA;AACA;AACA;;AAEA,OAAO,SAASU,WAAWA,CAACpB,EAAE,EAAE;EAC9B,IAAIA,EAAE,CAACgB,OAAO,EAAE;IACdhB,EAAE,CAACiB,QAAQ,CAAC,UAAUC,KAAK,EAAE;MAC3BA,KAAK,CAACE,WAAW,CAAC,CAAC;IACrB,CAAC,CAAC;EACJ,CAAC,MAAM;IACLpB,EAAE,CAACoB,WAAW,CAAC,CAAC;EAClB;AACF;AAEA,SAASC,iBAAiBA,CAACrB,EAAE,EAAEsB,KAAK,EAAEC,WAAW,EAAEC,YAAY,EAAE;EAC/D,IAAIC,KAAK,GAAGzB,EAAE,CAACyB,KAAK;EACpB,IAAIC,SAAS,GAAG,CAAC,CAAC;EAElB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGL,KAAK,CAACM,MAAM,EAAED,CAAC,EAAE,EAAE;IACrC,IAAIE,QAAQ,GAAGP,KAAK,CAACK,CAAC,CAAC;IACvB,IAAIG,GAAG,GAAGL,KAAK,CAACI,QAAQ,CAAC;IACzBH,SAAS,CAACG,QAAQ,CAAC,GAAGC,GAAG,IAAI,IAAI,GAAGN,YAAY,IAAIA,YAAY,CAACK,QAAQ,CAAC,GAAGC,GAAG;EAClF;EAEA,KAAK,IAAIH,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG3B,EAAE,CAAC+B,SAAS,CAACH,MAAM,EAAED,CAAC,EAAE,EAAE;IAC5C,IAAIK,QAAQ,GAAGhC,EAAE,CAAC+B,SAAS,CAACJ,CAAC,CAAC;IAE9B,IAAIK,QAAQ,CAACC,qBAAqB,CAAC;IAAA,GAChCD,QAAQ,CAACC,qBAAqB,CAAC9E,OAAO,CAACoE,WAAW,CAAC,GAAG,CAAC,IAAIS,QAAQ,CAACE,UAAU,KAAK,OAAO,EAAE;MAC7FF,QAAQ,CAACG,MAAM,CAACT,SAAS,EAAEJ,KAAK,CAAC;IACnC;EACF;EAEA,OAAOI,SAAS;AAClB;AAEA,SAASU,0BAA0BA,CAACpC,EAAE,EAAEC,SAAS,EAAEoC,YAAY,EAAEC,KAAK,EAAE;EACtE,IAAIC,SAAS,GAAGF,YAAY,IAAIlF,OAAO,CAACkF,YAAY,EAAE,QAAQ,CAAC,IAAI,CAAC;EACpE,IAAIG,MAAM,GAAG,KAAK;EAElB,IAAIxC,EAAE,YAAYhC,IAAI,EAAE;IACtB,IAAIyE,KAAK,GAAGrE,cAAc,CAAC4B,EAAE,CAAC;IAC9B,IAAI0C,QAAQ,GAAGH,SAAS,GAAGE,KAAK,CAACE,UAAU,IAAIF,KAAK,CAACG,UAAU,GAAGH,KAAK,CAACG,UAAU;IAClF,IAAIC,UAAU,GAAGN,SAAS,GAAGE,KAAK,CAACK,YAAY,IAAIL,KAAK,CAACM,YAAY,GAAGN,KAAK,CAACM,YAAY;IAE1F,IAAI7D,eAAe,CAACwD,QAAQ,CAAC,IAAIxD,eAAe,CAAC2D,UAAU,CAAC,EAAE;MAC5DP,KAAK,GAAGA,KAAK,IAAI,CAAC,CAAC;MACnB,IAAIU,aAAa,GAAGV,KAAK,CAACb,KAAK,IAAI,CAAC,CAAC,CAAC,CAAC;;MAEvC,IAAIuB,aAAa,CAACC,IAAI,KAAK,SAAS,EAAE;QACpCT,MAAM,GAAG,IAAI;QACbF,KAAK,GAAGpF,MAAM,CAAC,CAAC,CAAC,EAAEoF,KAAK,CAAC;QACzBU,aAAa,GAAG9F,MAAM,CAAC,CAAC,CAAC,EAAE8F,aAAa,CAAC;QACzCA,aAAa,CAACC,IAAI,GAAGP,QAAQ;MAC/B,CAAC,CAAC;MAAA,KACG,IAAI,CAACxD,eAAe,CAAC8D,aAAa,CAACC,IAAI,CAAC,IAAI/D,eAAe,CAACwD,QAAQ,CAAC,EAAE;QACxEF,MAAM,GAAG,IAAI,CAAC,CAAC;;QAEfF,KAAK,GAAGpF,MAAM,CAAC,CAAC,CAAC,EAAEoF,KAAK,CAAC;QACzBU,aAAa,GAAG9F,MAAM,CAAC,CAAC,CAAC,EAAE8F,aAAa,CAAC,CAAC,CAAC;;QAE3CA,aAAa,CAACC,IAAI,GAAG5D,SAAS,CAACqD,QAAQ,CAAC;MAC1C,CAAC,CAAC;MAAA,KACG,IAAI,CAACxD,eAAe,CAAC8D,aAAa,CAACE,MAAM,CAAC,IAAIhE,eAAe,CAAC2D,UAAU,CAAC,EAAE;QAC5E,IAAI,CAACL,MAAM,EAAE;UACXF,KAAK,GAAGpF,MAAM,CAAC,CAAC,CAAC,EAAEoF,KAAK,CAAC;UACzBU,aAAa,GAAG9F,MAAM,CAAC,CAAC,CAAC,EAAE8F,aAAa,CAAC;QAC3C;QAEAA,aAAa,CAACE,MAAM,GAAG7D,SAAS,CAACwD,UAAU,CAAC;MAC9C;MAEJP,KAAK,CAACb,KAAK,GAAGuB,aAAa;IAC7B;EACF;EAEA,IAAIV,KAAK,EAAE;IACT;IACA,IAAIA,KAAK,CAACa,EAAE,IAAI,IAAI,EAAE;MACpB,IAAI,CAACX,MAAM,EAAE;QACXF,KAAK,GAAGpF,MAAM,CAAC,CAAC,CAAC,EAAEoF,KAAK,CAAC;MAC3B;MAEA,IAAIc,cAAc,GAAGpD,EAAE,CAACoD,cAAc;MACtCd,KAAK,CAACa,EAAE,GAAGnD,EAAE,CAACmD,EAAE,IAAIC,cAAc,IAAI,IAAI,GAAGA,cAAc,GAAGzE,gBAAgB,CAAC;IACjF;EACF;EAEA,OAAO2D,KAAK;AACd;AAEA,SAASe,wBAAwBA,CAACrD,EAAE,EAAEC,SAAS,EAAEqC,KAAK,EAAE;EACtD;EACA,IAAIA,KAAK,EAAE;IACT;IACA,IAAIA,KAAK,CAACa,EAAE,IAAI,IAAI,EAAE;MACpBb,KAAK,GAAGpF,MAAM,CAAC,CAAC,CAAC,EAAEoF,KAAK,CAAC;MACzB,IAAIgB,YAAY,GAAGtD,EAAE,CAACsD,YAAY;MAClChB,KAAK,CAACa,EAAE,GAAGnD,EAAE,CAACmD,EAAE,IAAIG,YAAY,IAAI,IAAI,GAAGA,YAAY,GAAG1E,cAAc,CAAC;IAC3E;EACF;EAEA,OAAO0D,KAAK;AACd;AAEA,SAASiB,sBAAsBA,CAACvD,EAAE,EAAEC,SAAS,EAAEqC,KAAK,EAAE;EACpD,IAAIkB,OAAO,GAAGrG,OAAO,CAAC6C,EAAE,CAACyD,aAAa,EAAExD,SAAS,CAAC,IAAI,CAAC;EACvD,IAAIyD,cAAc,GAAG1D,EAAE,CAACyB,KAAK,CAACkC,OAAO;EACrC,IAAIjC,SAAS,GAAG,CAAC8B,OAAO,GAAGnC,iBAAiB,CAACrB,EAAE,EAAE,CAAC,SAAS,CAAC,EAAEC,SAAS,EAAE;IACvE0D,OAAO,EAAE;EACX,CAAC,CAAC,GAAG,IAAI;EACTrB,KAAK,GAAGA,KAAK,IAAI,CAAC,CAAC;EACnB,IAAIsB,SAAS,GAAGtB,KAAK,CAACb,KAAK,IAAI,CAAC,CAAC;EAEjC,IAAImC,SAAS,CAACD,OAAO,IAAI,IAAI,EAAE;IAC7B;IACArB,KAAK,GAAGpF,MAAM,CAAC,CAAC,CAAC,EAAEoF,KAAK,CAAC;IACzBsB,SAAS,GAAG1G,MAAM,CAAC;MACjB;MACAyG,OAAO,EAAEH,OAAO,GAAGE,cAAc,GAAGhC,SAAS,CAACiC,OAAO,GAAG;IAC1D,CAAC,EAAEC,SAAS,CAAC;IACbtB,KAAK,CAACb,KAAK,GAAGmC,SAAS;EACzB;EAEA,OAAOtB,KAAK;AACd;AAEA,SAASuB,iBAAiBA,CAAC5D,SAAS,EAAEoC,YAAY,EAAE;EAClD,IAAIC,KAAK,GAAG,IAAI,CAACwB,MAAM,CAAC7D,SAAS,CAAC;EAElC,IAAI,IAAI,CAACwB,KAAK,EAAE;IACd,IAAIxB,SAAS,KAAK,UAAU,EAAE;MAC5B,OAAOmC,0BAA0B,CAAC,IAAI,EAAEnC,SAAS,EAAEoC,YAAY,EAAEC,KAAK,CAAC;IACzE,CAAC,MAAM,IAAIrC,SAAS,KAAK,MAAM,EAAE;MAC/B,OAAOsD,sBAAsB,CAAC,IAAI,EAAEtD,SAAS,EAAEqC,KAAK,CAAC;IACvD,CAAC,MAAM,IAAIrC,SAAS,KAAK,QAAQ,EAAE;MACjC,OAAOoD,wBAAwB,CAAC,IAAI,EAAEpD,SAAS,EAAEqC,KAAK,CAAC;IACzD;EACF;EAEA,OAAOA,KAAK;AACd;AACA;AACA;AACA;AACA;AACA;;AAGA,OAAO,SAASyB,oBAAoBA,CAAC/D,EAAE,EAAE;EACvCA,EAAE,CAACgE,UAAU,GAAGH,iBAAiB;EACjC,IAAII,WAAW,GAAGjE,EAAE,CAACkE,cAAc,CAAC,CAAC;EACrC,IAAIC,SAAS,GAAGnE,EAAE,CAACoE,gBAAgB,CAAC,CAAC;EAErC,IAAIH,WAAW,EAAE;IACfA,WAAW,CAACD,UAAU,GAAGH,iBAAiB;EAC5C;EAEA,IAAIM,SAAS,EAAE;IACbA,SAAS,CAACH,UAAU,GAAGH,iBAAiB;EAC1C;AACF;AACA,OAAO,SAASQ,0BAA0BA,CAACrE,EAAE,EAAEsE,CAAC,EAAE;EAChD,CAACC,YAAY,CAACvE,EAAE,EAAEsE,CAAC,CAAC,CAAC;EAAA,GAClB,CAACtE,EAAE,CAACwE,aAAa,IAAIzD,mBAAmB,CAACf,EAAE,EAAEK,mBAAmB,CAAC;AACtE;AACA,OAAO,SAASoE,yBAAyBA,CAACzE,EAAE,EAAEsE,CAAC,EAAE;EAC/C,CAACC,YAAY,CAACvE,EAAE,EAAEsE,CAAC,CAAC,CAAC;EAAA,GAClB,CAACtE,EAAE,CAACwE,aAAa,IAAIzD,mBAAmB,CAACf,EAAE,EAAEM,mBAAmB,CAAC;AACtE;AACA,OAAO,SAASoE,aAAaA,CAAC1E,EAAE,EAAE2E,cAAc,EAAE;EAChD3E,EAAE,CAACwE,aAAa,IAAI,CAAC,KAAKG,cAAc,IAAI,CAAC,CAAC;EAC9C5D,mBAAmB,CAACf,EAAE,EAAEK,mBAAmB,CAAC;AAC9C;AACA,OAAO,SAASuE,aAAaA,CAAC5E,EAAE,EAAE2E,cAAc,EAAE;EAChD,EAAE3E,EAAE,CAACwE,aAAa,IAAI,EAAE,CAAC,KAAKG,cAAc,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI5D,mBAAmB,CAACf,EAAE,EAAEM,mBAAmB,CAAC;AACtG;AACA,OAAO,SAASuE,SAASA,CAAC7E,EAAE,EAAE;EAC5Be,mBAAmB,CAACf,EAAE,EAAEO,eAAe,CAAC;AAC1C;AACA,OAAO,SAASuE,SAASA,CAAC9E,EAAE,EAAE;EAC5Be,mBAAmB,CAACf,EAAE,EAAEQ,eAAe,CAAC;AAC1C;AACA,OAAO,SAASuE,WAAWA,CAAC/E,EAAE,EAAE;EAC9Be,mBAAmB,CAACf,EAAE,EAAES,iBAAiB,CAAC;AAC5C;AACA,OAAO,SAASuE,WAAWA,CAAChF,EAAE,EAAE;EAC9Be,mBAAmB,CAACf,EAAE,EAAEW,iBAAiB,CAAC;AAC5C;AAEA,SAAS4D,YAAYA,CAACvE,EAAE,EAAEsE,CAAC,EAAE;EAC3B,OAAOtE,EAAE,CAACiF,uBAAuB,IAAIX,CAAC,CAACY,SAAS;AAClD;AAEA,OAAO,SAASC,YAAYA,CAACC,GAAG,EAAE;EAChC,IAAIC,KAAK,GAAGD,GAAG,CAACE,QAAQ,CAAC,CAAC;EAC1B,IAAIC,kBAAkB,GAAG,EAAE;EAC3B,IAAIC,iBAAiB,GAAG,EAAE;EAC1BH,KAAK,CAACI,aAAa,CAAC,UAAUC,aAAa,EAAEC,cAAc,EAAE;IAC3D,IAAIC,eAAe,GAAGvH,kBAAkB,CAACsH,cAAc,CAAC;IACxD,IAAIE,QAAQ,GAAGH,aAAa,KAAK,QAAQ;IACzC,IAAII,IAAI,GAAGD,QAAQ,GAAGT,GAAG,CAACW,oBAAoB,CAACJ,cAAc,CAAC,GAAGP,GAAG,CAACY,uBAAuB,CAACL,cAAc,CAAC;IAC5G,CAACE,QAAQ,IAAIL,iBAAiB,CAACS,IAAI,CAACH,IAAI,CAAC;IAEzC,IAAIF,eAAe,CAACM,QAAQ,EAAE;MAC5B;MACAJ,IAAI,CAACK,KAAK,CAAClF,QAAQ,CAAC,UAAUC,KAAK,EAAE;QACnCV,eAAe,CAACU,KAAK,CAAC;MACxB,CAAC,CAAC;MACF2E,QAAQ,IAAIN,kBAAkB,CAACU,IAAI,CAACN,cAAc,CAAC;IACrD;IAEAC,eAAe,CAACM,QAAQ,GAAG,KAAK;EAClC,CAAC,CAAC;EACF1I,IAAI,CAACgI,iBAAiB,EAAE,UAAUM,IAAI,EAAE;IACtC,IAAIA,IAAI,IAAIA,IAAI,CAACM,gBAAgB,EAAE;MACjCN,IAAI,CAACM,gBAAgB,CAACb,kBAAkB,EAAE,KAAK,EAAEF,KAAK,CAAC;IACzD;EACF,CAAC,CAAC;AACJ;AACA,OAAO,SAASgB,UAAUA,CAACC,iBAAiB,EAAEC,KAAK,EAAEC,SAAS,EAAEpB,GAAG,EAAE;EACnE,IAAIqB,OAAO,GAAGrB,GAAG,CAACE,QAAQ,CAAC,CAAC;EAC5BkB,SAAS,GAAGA,SAAS,IAAI,kBAAkB;EAE3C,SAASE,kBAAkBA,CAACC,IAAI,EAAEC,WAAW,EAAE;IAC7C,KAAK,IAAIjF,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGiF,WAAW,CAAChF,MAAM,EAAED,CAAC,EAAE,EAAE;MAC3C,IAAIkF,MAAM,GAAGF,IAAI,CAACG,gBAAgB,CAACF,WAAW,CAACjF,CAAC,CAAC,CAAC;MAClDkF,MAAM,IAAI/B,SAAS,CAAC+B,MAAM,CAAC;IAC7B;EACF;EAEA,IAAIP,iBAAiB,IAAI,IAAI,EAAE;IAC7B;EACF;EAEA,IAAI,CAACC,KAAK,IAAIA,KAAK,KAAK,MAAM,EAAE;IAC9B;EACF;EAEA,IAAIQ,iBAAiB,GAAGN,OAAO,CAACO,gBAAgB,CAACV,iBAAiB,CAAC;EACnE,IAAIW,cAAc,GAAGF,iBAAiB,CAACG,gBAAgB;EAEvD,IAAID,cAAc,IAAIA,cAAc,CAACE,MAAM,EAAE;IAC3CF,cAAc,GAAGA,cAAc,CAACE,MAAM;EACxC;EAEA,IAAIC,aAAa,GAAG,EAAE;EACtBX,OAAO,CAACY,UAAU,CAAC,UAAUC,WAAW,EAAE;IACxC,IAAIC,UAAU,GAAGR,iBAAiB,KAAKO,WAAW;IAClD,IAAIE,QAAQ,GAAGF,WAAW,CAACJ,gBAAgB;IAE3C,IAAIM,QAAQ,IAAIA,QAAQ,CAACL,MAAM,EAAE;MAC/BK,QAAQ,GAAGA,QAAQ,CAACL,MAAM;IAC5B;IAEA,IAAIM,YAAY,GAAGD,QAAQ,IAAIP,cAAc,GAAGO,QAAQ,KAAKP,cAAc,GAAGM,UAAU,CAAC,CAAC;;IAE1F,IAAI;IAAG;IACPf,SAAS,KAAK,QAAQ,IAAI,CAACe,UAAU,CAAC;IAAA,GACnCf,SAAS,KAAK,kBAAkB,IAAI,CAACiB,YAAY,CAAC;IAAA,GAClDlB,KAAK,KAAK,QAAQ,IAAIgB,UAAU,CAAC;IAAA,CACnC,EAAE;MACD,IAAIzB,IAAI,GAAGV,GAAG,CAACW,oBAAoB,CAACuB,WAAW,CAAC;MAChDxB,IAAI,CAACK,KAAK,CAAClF,QAAQ,CAAC,UAAUC,KAAK,EAAE;QACnC;QACA;QACA;QACA;QACA,IAAIA,KAAK,CAACsD,aAAa,IAAI+C,UAAU,IAAIhB,KAAK,KAAK,MAAM,EAAE;UACzD;QACF;QAEAhG,eAAe,CAACW,KAAK,CAAC;MACxB,CAAC,CAAC;MAEF,IAAI9D,WAAW,CAACmJ,KAAK,CAAC,EAAE;QACtBG,kBAAkB,CAACY,WAAW,CAACI,OAAO,CAAC,CAAC,EAAEnB,KAAK,CAAC;MAClD,CAAC,MAAM,IAAIlJ,QAAQ,CAACkJ,KAAK,CAAC,EAAE;QAC1B,IAAIoB,SAAS,GAAGrK,IAAI,CAACiJ,KAAK,CAAC;QAE3B,KAAK,IAAIqB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,SAAS,CAAC/F,MAAM,EAAEgG,CAAC,EAAE,EAAE;UACzClB,kBAAkB,CAACY,WAAW,CAACI,OAAO,CAACC,SAAS,CAACC,CAAC,CAAC,CAAC,EAAErB,KAAK,CAACoB,SAAS,CAACC,CAAC,CAAC,CAAC,CAAC;QAC5E;MACF;MAEAR,aAAa,CAACnB,IAAI,CAACqB,WAAW,CAAC;MAC/BjJ,kBAAkB,CAACiJ,WAAW,CAAC,CAACpB,QAAQ,GAAG,IAAI;IACjD;EACF,CAAC,CAAC;EACFO,OAAO,CAAChB,aAAa,CAAC,UAAUC,aAAa,EAAEC,cAAc,EAAE;IAC7D,IAAID,aAAa,KAAK,QAAQ,EAAE;MAC9B;IACF;IAEA,IAAII,IAAI,GAAGV,GAAG,CAACY,uBAAuB,CAACL,cAAc,CAAC;IAEtD,IAAIG,IAAI,IAAIA,IAAI,CAACM,gBAAgB,EAAE;MACjCN,IAAI,CAACM,gBAAgB,CAACgB,aAAa,EAAE,IAAI,EAAEX,OAAO,CAAC;IACrD;EACF,CAAC,CAAC;AACJ;AACA,OAAO,SAASoB,aAAaA,CAACC,iBAAiB,EAAEC,cAAc,EAAE3C,GAAG,EAAE;EACpE,IAAI0C,iBAAiB,IAAI,IAAI,IAAIC,cAAc,IAAI,IAAI,EAAE;IACvD;EACF;EAEA,IAAIpC,cAAc,GAAGP,GAAG,CAACE,QAAQ,CAAC,CAAC,CAAC0C,YAAY,CAACF,iBAAiB,EAAEC,cAAc,CAAC;EAEnF,IAAI,CAACpC,cAAc,EAAE;IACnB;EACF;EAEAtH,kBAAkB,CAACsH,cAAc,CAAC,CAACO,QAAQ,GAAG,IAAI;EAClD,IAAIJ,IAAI,GAAGV,GAAG,CAACY,uBAAuB,CAACL,cAAc,CAAC;EAEtD,IAAI,CAACG,IAAI,IAAI,CAACA,IAAI,CAACmC,gBAAgB,EAAE;IACnC;EACF;EAEAnC,IAAI,CAACK,KAAK,CAAClF,QAAQ,CAAC,UAAUC,KAAK,EAAE;IACnCX,eAAe,CAACW,KAAK,CAAC;EACxB,CAAC,CAAC;AACJ;AACA,OAAO,SAASgH,8BAA8BA,CAACZ,WAAW,EAAEa,OAAO,EAAE/C,GAAG,EAAE;EACxE,IAAIgD,WAAW,GAAGd,WAAW,CAACc,WAAW;EACzC,IAAIzB,IAAI,GAAGW,WAAW,CAACI,OAAO,CAACS,OAAO,CAACE,QAAQ,CAAC;EAEhD,IAAI,CAAC1B,IAAI,EAAE;IACT,IAAI2B,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;MACzCvK,KAAK,CAAC,mBAAmB,GAAGkK,OAAO,CAACE,QAAQ,CAAC;IAC/C;IAEA;EACF;EAEA,IAAII,SAAS,GAAG3K,cAAc,CAAC6I,IAAI,EAAEwB,OAAO,CAAC,CAAC,CAAC;;EAE/CM,SAAS,GAAG,CAAClL,OAAO,CAACkL,SAAS,CAAC,GAAGA,SAAS,CAAC,CAAC,CAAC,GAAGA,SAAS,KAAK,CAAC;EAChE,IAAIzI,EAAE,GAAG2G,IAAI,CAACG,gBAAgB,CAAC2B,SAAS,CAAC;EAEzC,IAAI,CAACzI,EAAE,EAAE;IACP,IAAI0I,KAAK,GAAG/B,IAAI,CAAC+B,KAAK,CAAC,CAAC;IACxB,IAAIC,OAAO,GAAG,CAAC,CAAC,CAAC;;IAEjB,OAAO,CAAC3I,EAAE,IAAI2I,OAAO,GAAGD,KAAK,EAAE;MAC7B1I,EAAE,GAAG2G,IAAI,CAACG,gBAAgB,CAAC6B,OAAO,EAAE,CAAC;IACvC;EACF;EAEA,IAAI3I,EAAE,EAAE;IACN,IAAI4I,MAAM,GAAGhL,SAAS,CAACoC,EAAE,CAAC;IAC1BqG,UAAU,CAAC+B,WAAW,EAAEQ,MAAM,CAACrC,KAAK,EAAEqC,MAAM,CAACpC,SAAS,EAAEpB,GAAG,CAAC;EAC9D,CAAC,MAAM;IACL;IACA;IACA,IAAIyD,OAAO,GAAGvB,WAAW,CAAC9H,GAAG,CAAC,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC;IACpD,IAAIgH,SAAS,GAAGc,WAAW,CAAC9H,GAAG,CAAC,CAAC,UAAU,EAAE,WAAW,CAAC,CAAC;IAE1D,IAAIqJ,OAAO,IAAI,IAAI,EAAE;MACnBxC,UAAU,CAAC+B,WAAW,EAAES,OAAO,EAAErC,SAAS,EAAEpB,GAAG,CAAC;IAClD;EACF;AACF;AACA,OAAO,SAAS0D,gCAAgCA,CAAChB,iBAAiB,EAAEC,cAAc,EAAEgB,IAAI,EAAE3D,GAAG,EAAE;EAC7F,IAAIzF,GAAG,GAAG;IACRqJ,SAAS,EAAE,KAAK;IAChBC,WAAW,EAAE;EACf,CAAC;EAED,IAAInB,iBAAiB,IAAI,IAAI,IAAIA,iBAAiB,KAAK,QAAQ,IAAIC,cAAc,IAAI,IAAI,IAAIgB,IAAI,IAAI,IAAI,EAAE;IACzG,OAAOpJ,GAAG;EACZ;EAEA,IAAIgG,cAAc,GAAGP,GAAG,CAACE,QAAQ,CAAC,CAAC,CAAC0C,YAAY,CAACF,iBAAiB,EAAEC,cAAc,CAAC;EAEnF,IAAI,CAACpC,cAAc,EAAE;IACnB,OAAOhG,GAAG;EACZ;EAEA,IAAImG,IAAI,GAAGV,GAAG,CAACY,uBAAuB,CAACL,cAAc,CAAC;EAEtD,IAAI,CAACG,IAAI,IAAI,CAACA,IAAI,CAACoD,uBAAuB,EAAE;IAC1C,OAAOvJ,GAAG;EACZ;EAEA,IAAIsJ,WAAW,GAAGnD,IAAI,CAACoD,uBAAuB,CAACH,IAAI,CAAC,CAAC,CAAC;EACtD;;EAEA,IAAIC,SAAS;EAEb,KAAK,IAAIrH,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGsH,WAAW,CAACrH,MAAM,EAAED,CAAC,EAAE,EAAE;IAC3C,IAAI2G,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,IAAI,CAACW,oBAAoB,CAACF,WAAW,CAACtH,CAAC,CAAC,CAAC,EAAE;MAClF1D,KAAK,CAAC,oCAAoC,CAAC;IAC7C;IAEA,IAAIL,SAAS,CAACqL,WAAW,CAACtH,CAAC,CAAC,CAAC,CAAC4E,KAAK,KAAK,MAAM,EAAE;MAC9CyC,SAAS,GAAG,IAAI;MAChB;IACF;EACF;EAEA,OAAO;IACLA,SAAS,EAAEA,SAAS;IACpBC,WAAW,EAAEA;EACf,CAAC;AACH;AACA,OAAO,SAASG,gCAAgCA,CAACC,UAAU,EAAE/E,CAAC,EAAEc,GAAG,EAAE;EACnE,IAAIkD,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,IAAI,CAACW,oBAAoB,CAACE,UAAU,CAAC,EAAE;IAC9EpL,KAAK,CAAC,oCAAoC,CAAC;EAC7C;EAEA,IAAI2K,MAAM,GAAGhL,SAAS,CAACyL,UAAU,CAAC;EAElC,IAAIC,EAAE,GAAGR,gCAAgC,CAACF,MAAM,CAACd,iBAAiB,EAAEc,MAAM,CAACb,cAAc,EAAEa,MAAM,CAACW,qBAAqB,EAAEnE,GAAG,CAAC;IACzH6D,WAAW,GAAGK,EAAE,CAACL,WAAW;IAC5BD,SAAS,GAAGM,EAAE,CAACN,SAAS,CAAC,CAAC;EAC9B;;EAGA,IAAIC,WAAW,EAAE;IACf,IAAID,SAAS,EAAE;MACbnB,aAAa,CAACe,MAAM,CAACd,iBAAiB,EAAEc,MAAM,CAACb,cAAc,EAAE3C,GAAG,CAAC;IACrE;IAEA5H,IAAI,CAACyL,WAAW,EAAE,UAAUI,UAAU,EAAE;MACtC,OAAOhF,0BAA0B,CAACgF,UAAU,EAAE/E,CAAC,CAAC;IAClD,CAAC,CAAC;EACJ,CAAC,MAAM;IACL;IACA;IACA+B,UAAU,CAACuC,MAAM,CAACR,WAAW,EAAEQ,MAAM,CAACrC,KAAK,EAAEqC,MAAM,CAACpC,SAAS,EAAEpB,GAAG,CAAC;IAEnE,IAAIwD,MAAM,CAACrC,KAAK,KAAK,MAAM,EAAE;MAC3BsB,aAAa,CAACe,MAAM,CAACd,iBAAiB,EAAEc,MAAM,CAACb,cAAc,EAAE3C,GAAG,CAAC;IACrE,CAAC,CAAC;IACF;IACA;;IAGAf,0BAA0B,CAACgF,UAAU,EAAE/E,CAAC,CAAC;EAC3C;AACF;AACA,OAAO,SAASkF,+BAA+BA,CAACH,UAAU,EAAE/E,CAAC,EAAEc,GAAG,EAAE;EAClE,IAAIkD,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,IAAI,CAACW,oBAAoB,CAACE,UAAU,CAAC,EAAE;IAC9EpL,KAAK,CAAC,oCAAoC,CAAC;EAC7C;EAEAkH,YAAY,CAACC,GAAG,CAAC;EACjB,IAAIwD,MAAM,GAAGhL,SAAS,CAACyL,UAAU,CAAC;EAClC,IAAIJ,WAAW,GAAGH,gCAAgC,CAACF,MAAM,CAACd,iBAAiB,EAAEc,MAAM,CAACb,cAAc,EAAEa,MAAM,CAACW,qBAAqB,EAAEnE,GAAG,CAAC,CAAC6D,WAAW;EAElJ,IAAIA,WAAW,EAAE;IACfzL,IAAI,CAACyL,WAAW,EAAE,UAAUI,UAAU,EAAE;MACtC,OAAO5E,yBAAyB,CAAC4E,UAAU,EAAE/E,CAAC,CAAC;IACjD,CAAC,CAAC;EACJ,CAAC,MAAM;IACLG,yBAAyB,CAAC4E,UAAU,EAAE/E,CAAC,CAAC;EAC1C;AACF;AACA,OAAO,SAASmF,0BAA0BA,CAACnC,WAAW,EAAEa,OAAO,EAAE/C,GAAG,EAAE;EACpE,IAAI,CAACsE,qBAAqB,CAACvB,OAAO,CAAC,EAAE;IACnC;EACF;EAEA,IAAIE,QAAQ,GAAGF,OAAO,CAACE,QAAQ;EAC/B,IAAI1B,IAAI,GAAGW,WAAW,CAACI,OAAO,CAACW,QAAQ,CAAC;EACxC,IAAII,SAAS,GAAG3K,cAAc,CAAC6I,IAAI,EAAEwB,OAAO,CAAC;EAE7C,IAAI,CAAC5K,OAAO,CAACkL,SAAS,CAAC,EAAE;IACvBA,SAAS,GAAG,CAACA,SAAS,CAAC;EACzB;EAEAnB,WAAW,CAACa,OAAO,CAACwB,IAAI,KAAK1K,yBAAyB,GAAG,cAAc,GAAGkJ,OAAO,CAACwB,IAAI,KAAK5K,kBAAkB,GAAG,QAAQ,GAAG,UAAU,CAAC,CAAC0J,SAAS,EAAEJ,QAAQ,CAAC;AAC7J;AACA,OAAO,SAASuB,4BAA4BA,CAACtC,WAAW,EAAE;EACxD,IAAIuC,OAAO,GAAGvC,WAAW,CAACwC,UAAU,CAAC,CAAC;EACtCtM,IAAI,CAACqM,OAAO,EAAE,UAAUP,EAAE,EAAE;IAC1B,IAAI3C,IAAI,GAAG2C,EAAE,CAAC3C,IAAI;MACdgD,IAAI,GAAGL,EAAE,CAACK,IAAI;IAClBhD,IAAI,CAACoD,iBAAiB,CAAC,UAAU/J,EAAE,EAAEgK,GAAG,EAAE;MACxC1C,WAAW,CAAC2C,UAAU,CAACD,GAAG,EAAEL,IAAI,CAAC,GAAG5E,WAAW,CAAC/E,EAAE,CAAC,GAAGgF,WAAW,CAAChF,EAAE,CAAC;IACvE,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ;AACA,OAAO,SAASkK,qBAAqBA,CAACzD,OAAO,EAAE;EAC7C,IAAI9G,GAAG,GAAG,EAAE;EACZ8G,OAAO,CAACY,UAAU,CAAC,UAAUC,WAAW,EAAE;IACxC,IAAIuC,OAAO,GAAGvC,WAAW,CAACwC,UAAU,CAAC,CAAC;IACtCtM,IAAI,CAACqM,OAAO,EAAE,UAAUP,EAAE,EAAE;MAC1B,IAAI3C,IAAI,GAAG2C,EAAE,CAAC3C,IAAI;QACdgD,IAAI,GAAGL,EAAE,CAACK,IAAI;MAClB,IAAI/C,WAAW,GAAGU,WAAW,CAAC6C,sBAAsB,CAAC,CAAC;MAEtD,IAAIvD,WAAW,CAAChF,MAAM,GAAG,CAAC,EAAE;QAC1B,IAAIwI,IAAI,GAAG;UACT3B,SAAS,EAAE7B,WAAW;UACtBwB,WAAW,EAAEd,WAAW,CAACc;QAC3B,CAAC;QAED,IAAIuB,IAAI,IAAI,IAAI,EAAE;UAChBS,IAAI,CAAC/B,QAAQ,GAAGsB,IAAI;QACtB;QAEAhK,GAAG,CAACsG,IAAI,CAACmE,IAAI,CAAC;MAChB;IACF,CAAC,CAAC;EACJ,CAAC,CAAC;EACF,OAAOzK,GAAG;AACZ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,OAAO,SAAS0K,mBAAmBA,CAACrK,EAAE,EAAEuG,KAAK,EAAEC,SAAS,EAAE;EACxD8D,uBAAuB,CAACtK,EAAE,EAAE,IAAI,CAAC;EACjCe,mBAAmB,CAACf,EAAE,EAAE+D,oBAAoB,CAAC;EAC7CwG,gBAAgB,CAACvK,EAAE,EAAEuG,KAAK,EAAEC,SAAS,CAAC;AACxC;AACA,OAAO,SAASgE,oBAAoBA,CAACxK,EAAE,EAAE;EACvCsK,uBAAuB,CAACtK,EAAE,EAAE,KAAK,CAAC;AACpC;AACA,OAAO,SAASyK,mBAAmBA,CAACzK,EAAE,EAAEuG,KAAK,EAAEC,SAAS,EAAEkE,UAAU,EAAE;EACpEA,UAAU,GAAGF,oBAAoB,CAACxK,EAAE,CAAC,GAAGqK,mBAAmB,CAACrK,EAAE,EAAEuG,KAAK,EAAEC,SAAS,CAAC;AACnF;AACA,OAAO,SAAS+D,gBAAgBA,CAACvK,EAAE,EAAEuG,KAAK,EAAEC,SAAS,EAAE;EACrD,IAAIoC,MAAM,GAAGhL,SAAS,CAACoC,EAAE,CAAC;EAE1B,IAAIuG,KAAK,IAAI,IAAI,EAAE;IACjB;IACA;IACA;IACA;IACA;IACA;IACA;IACAqC,MAAM,CAACrC,KAAK,GAAGA,KAAK;IACpBqC,MAAM,CAACpC,SAAS,GAAGA,SAAS,CAAC,CAAC;EAChC,CAAC,MAAM,IAAIoC,MAAM,CAACrC,KAAK,EAAE;IACvBqC,MAAM,CAACrC,KAAK,GAAG,IAAI;EACrB;AACF;AACA,IAAIoE,YAAY,GAAG,CAAC,UAAU,EAAE,MAAM,EAAE,QAAQ,CAAC;AACjD,IAAIC,qBAAqB,GAAG;EAC1BC,SAAS,EAAE,cAAc;EACzBC,SAAS,EAAE,cAAc;EACzBC,SAAS,EAAE;AACb,CAAC;AACD;AACA;AACA;;AAEA,OAAO,SAASC,wBAAwBA,CAAChL,EAAE,EAAEiL,SAAS,EAAEC,SAAS;AAAE;AACnEC,MAAM,EAAE;EACND,SAAS,GAAGA,SAAS,IAAI,WAAW;EAEpC,KAAK,IAAIvJ,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGgJ,YAAY,CAAC/I,MAAM,EAAED,CAAC,EAAE,EAAE;IAC5C,IAAI1B,SAAS,GAAG0K,YAAY,CAAChJ,CAAC,CAAC;IAC/B,IAAI0D,KAAK,GAAG4F,SAAS,CAAC3F,QAAQ,CAAC,CAACrF,SAAS,EAAEiL,SAAS,CAAC,CAAC;IACtD,IAAI5I,KAAK,GAAGtC,EAAE,CAACoL,WAAW,CAACnL,SAAS,CAAC,CAAC,CAAC;;IAEvCqC,KAAK,CAACb,KAAK,GAAG0J,MAAM,GAAGA,MAAM,CAAC9F,KAAK,CAAC,GAAGA,KAAK,CAACuF,qBAAqB,CAACM,SAAS,CAAC,CAAC,CAAC,CAAC;EAClF;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,OAAO,SAASZ,uBAAuBA,CAACtK,EAAE,EAAEqL,YAAY,EAAE;EACxD,IAAIC,OAAO,GAAGD,YAAY,KAAK,KAAK;EACpC,IAAIE,UAAU,GAAGvL,EAAE,CAAC,CAAC;EACrB;;EAEA,IAAIA,EAAE,CAACwL,qBAAqB,EAAE;IAC5BD,UAAU,CAACtG,uBAAuB,GAAGjF,EAAE,CAACwL,qBAAqB;EAC/D,CAAC,CAAC;EACF;;EAGA,IAAI,CAACF,OAAO,IAAIC,UAAU,CAACE,oBAAoB,EAAE;IAC/C;IACA;IACA;IACAF,UAAU,CAAC/G,aAAa,GAAG+G,UAAU,CAAC/G,aAAa,IAAI,CAAC;IACxD+G,UAAU,CAACE,oBAAoB,GAAG,CAACH,OAAO;EAC5C;AACF;AACA,OAAO,SAASnC,oBAAoBA,CAACnJ,EAAE,EAAE;EACvC,OAAO,CAAC,EAAEA,EAAE,IAAIA,EAAE,CAACyL,oBAAoB,CAAC;AAC1C;AACA;AACA;AACA;AACA;AACA;;AAEA,OAAO,SAASC,+BAA+BA,CAAC1L,EAAE,EAAE2F,cAAc,EAAE4D,qBAAqB,EAAE;EACzF,IAAIX,MAAM,GAAGhL,SAAS,CAACoC,EAAE,CAAC;EAC1B4I,MAAM,CAACd,iBAAiB,GAAGnC,cAAc,CAACgG,QAAQ;EAClD/C,MAAM,CAACb,cAAc,GAAGpC,cAAc,CAACoC,cAAc;EACrDa,MAAM,CAACW,qBAAqB,GAAGA,qBAAqB;AACtD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,OAAO,SAASqC,iBAAiBA,CAACC,YAAY,EAAE;EAC9C,IAAIlH,cAAc,GAAGxG,gBAAgB,CAAC0N,YAAY,CAAC;EAEnD,IAAIlH,cAAc,IAAI,IAAI,IAAIzG,mBAAmB,IAAI,EAAE,EAAE;IACvDyG,cAAc,GAAGxG,gBAAgB,CAAC0N,YAAY,CAAC,GAAG3N,mBAAmB,EAAE;EACzE;EAEA,OAAOyG,cAAc;AACvB;AACA,OAAO,SAAS+E,qBAAqBA,CAACvB,OAAO,EAAE;EAC7C,IAAI2D,WAAW,GAAG3D,OAAO,CAACwB,IAAI;EAC9B,OAAOmC,WAAW,KAAK/M,kBAAkB,IAAI+M,WAAW,KAAK9M,oBAAoB,IAAI8M,WAAW,KAAK7M,yBAAyB;AAChI;AACA,OAAO,SAAS8M,iBAAiBA,CAAC5D,OAAO,EAAE;EACzC,IAAI2D,WAAW,GAAG3D,OAAO,CAACwB,IAAI;EAC9B,OAAOmC,WAAW,KAAKjN,qBAAqB,IAAIiN,WAAW,KAAKhN,oBAAoB;AACtF;AACA,OAAO,SAASkN,cAAcA,CAAChM,EAAE,EAAE;EACjC,IAAIyC,KAAK,GAAGrE,cAAc,CAAC4B,EAAE,CAAC;EAC9ByC,KAAK,CAACG,UAAU,GAAG5C,EAAE,CAACyB,KAAK,CAACwB,IAAI;EAChCR,KAAK,CAACM,YAAY,GAAG/C,EAAE,CAACyB,KAAK,CAACyB,MAAM;EACpC,IAAI+I,WAAW,GAAGjM,EAAE,CAAC8D,MAAM,CAACoI,MAAM,IAAI,CAAC,CAAC;EACxCzJ,KAAK,CAACE,UAAU,GAAGsJ,WAAW,CAACxK,KAAK,IAAIwK,WAAW,CAACxK,KAAK,CAACwB,IAAI,IAAI,IAAI;EACtER,KAAK,CAACK,YAAY,GAAGmJ,WAAW,CAACxK,KAAK,IAAIwK,WAAW,CAACxK,KAAK,CAACyB,MAAM,IAAI,IAAI;AAC5E"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}