import { Component, Input } from '@angular/core';
import { CommonModule } from '@angular/common';
import { CountryData } from '../../monitoring.component';

@Component({
  selector: 'app-top-countries',
  standalone: true,
  imports: [CommonModule],
  template: `
    <div class="top-countries-container">
      <div class="countries-list">
        <div 
          *ngFor="let country of countries; let i = index" 
          class="country-item"
          [class.first]="i === 0"
          [class.second]="i === 1"
          [class.third]="i === 2"
        >
          <div class="country-rank">{{ i + 1 }}</div>
          <div class="country-flag">{{ getCountryFlag(country.countryCode) }}</div>
          <div class="country-info">
            <div class="country-name">{{ country.country }}</div>
            <div class="country-stats">
              <span class="connections">{{ country.count }} conexões</span>
              <span class="percentage">{{ country.percentage }}%</span>
            </div>
          </div>
          <div class="country-bar">
            <div 
              class="bar-fill" 
              [style.width.%]="country.percentage"
              [class.high]="country.percentage >= 30"
              [class.medium]="country.percentage >= 15 && country.percentage < 30"
              [class.low]="country.percentage < 15"
            ></div>
          </div>
        </div>
      </div>
      
      <div class="empty-state" *ngIf="countries.length === 0">
        <div class="empty-icon">🌍</div>
        <div class="empty-text">Aguardando dados...</div>
      </div>
    </div>
  `,
  styleUrls: ['./top-countries.component.scss']
})
export class TopCountriesComponent {
  @Input() countries: CountryData[] = [];

  getCountryFlag(countryCode: string): string {
    const flags: { [key: string]: string } = {
      'BR': '🇧🇷',
      'US': '🇺🇸', 
      'DE': '🇩🇪',
      'FR': '🇫🇷',
      'GB': '🇬🇧',
      'CN': '🇨🇳',
      'JP': '🇯🇵',
      'RU': '🇷🇺',
      'CA': '🇨🇦',
      'AU': '🇦🇺',
      'IT': '🇮🇹',
      'ES': '🇪🇸',
      'NL': '🇳🇱',
      'IN': '🇮🇳',
      'MX': '🇲🇽',
      'AR': '🇦🇷',
      'LO': '🏠'
    };
    return flags[countryCode] || '🌍';
  }
}
