{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport * as graphic from '../../util/graphic.js';\nimport { createTextStyle } from '../../label/labelStyle.js';\nimport Model from '../../model/Model.js';\nimport AxisView from './AxisView.js';\nimport AxisBuilder from './AxisBuilder.js';\nimport { getECData } from '../../util/innerStore.js';\nvar elementList = ['axisLine', 'axisLabel', 'axisTick', 'minorTick', 'splitLine', 'minorSplitLine', 'splitArea'];\nfunction getAxisLineShape(polar, rExtent, angle) {\n  rExtent[1] > rExtent[0] && (rExtent = rExtent.slice().reverse());\n  var start = polar.coordToPoint([rExtent[0], angle]);\n  var end = polar.coordToPoint([rExtent[1], angle]);\n  return {\n    x1: start[0],\n    y1: start[1],\n    x2: end[0],\n    y2: end[1]\n  };\n}\nfunction getRadiusIdx(polar) {\n  var radiusAxis = polar.getRadiusAxis();\n  return radiusAxis.inverse ? 0 : 1;\n} // Remove the last tick which will overlap the first tick\n\nfunction fixAngleOverlap(list) {\n  var firstItem = list[0];\n  var lastItem = list[list.length - 1];\n  if (firstItem && lastItem && Math.abs(Math.abs(firstItem.coord - lastItem.coord) - 360) < 1e-4) {\n    list.pop();\n  }\n}\nvar AngleAxisView = /** @class */\nfunction (_super) {\n  __extends(AngleAxisView, _super);\n  function AngleAxisView() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.type = AngleAxisView.type;\n    _this.axisPointerClass = 'PolarAxisPointer';\n    return _this;\n  }\n  AngleAxisView.prototype.render = function (angleAxisModel, ecModel) {\n    this.group.removeAll();\n    if (!angleAxisModel.get('show')) {\n      return;\n    }\n    var angleAxis = angleAxisModel.axis;\n    var polar = angleAxis.polar;\n    var radiusExtent = polar.getRadiusAxis().getExtent();\n    var ticksAngles = angleAxis.getTicksCoords();\n    var minorTickAngles = angleAxis.getMinorTicksCoords();\n    var labels = zrUtil.map(angleAxis.getViewLabels(), function (labelItem) {\n      labelItem = zrUtil.clone(labelItem);\n      var scale = angleAxis.scale;\n      var tickValue = scale.type === 'ordinal' ? scale.getRawOrdinalNumber(labelItem.tickValue) : labelItem.tickValue;\n      labelItem.coord = angleAxis.dataToCoord(tickValue);\n      return labelItem;\n    });\n    fixAngleOverlap(labels);\n    fixAngleOverlap(ticksAngles);\n    zrUtil.each(elementList, function (name) {\n      if (angleAxisModel.get([name, 'show']) && (!angleAxis.scale.isBlank() || name === 'axisLine')) {\n        angelAxisElementsBuilders[name](this.group, angleAxisModel, polar, ticksAngles, minorTickAngles, radiusExtent, labels);\n      }\n    }, this);\n  };\n  AngleAxisView.type = 'angleAxis';\n  return AngleAxisView;\n}(AxisView);\nvar angelAxisElementsBuilders = {\n  axisLine: function (group, angleAxisModel, polar, ticksAngles, minorTickAngles, radiusExtent) {\n    var lineStyleModel = angleAxisModel.getModel(['axisLine', 'lineStyle']); // extent id of the axis radius (r0 and r)\n\n    var rId = getRadiusIdx(polar);\n    var r0Id = rId ? 0 : 1;\n    var shape;\n    if (radiusExtent[r0Id] === 0) {\n      shape = new graphic.Circle({\n        shape: {\n          cx: polar.cx,\n          cy: polar.cy,\n          r: radiusExtent[rId]\n        },\n        style: lineStyleModel.getLineStyle(),\n        z2: 1,\n        silent: true\n      });\n    } else {\n      shape = new graphic.Ring({\n        shape: {\n          cx: polar.cx,\n          cy: polar.cy,\n          r: radiusExtent[rId],\n          r0: radiusExtent[r0Id]\n        },\n        style: lineStyleModel.getLineStyle(),\n        z2: 1,\n        silent: true\n      });\n    }\n    shape.style.fill = null;\n    group.add(shape);\n  },\n  axisTick: function (group, angleAxisModel, polar, ticksAngles, minorTickAngles, radiusExtent) {\n    var tickModel = angleAxisModel.getModel('axisTick');\n    var tickLen = (tickModel.get('inside') ? -1 : 1) * tickModel.get('length');\n    var radius = radiusExtent[getRadiusIdx(polar)];\n    var lines = zrUtil.map(ticksAngles, function (tickAngleItem) {\n      return new graphic.Line({\n        shape: getAxisLineShape(polar, [radius, radius + tickLen], tickAngleItem.coord)\n      });\n    });\n    group.add(graphic.mergePath(lines, {\n      style: zrUtil.defaults(tickModel.getModel('lineStyle').getLineStyle(), {\n        stroke: angleAxisModel.get(['axisLine', 'lineStyle', 'color'])\n      })\n    }));\n  },\n  minorTick: function (group, angleAxisModel, polar, tickAngles, minorTickAngles, radiusExtent) {\n    if (!minorTickAngles.length) {\n      return;\n    }\n    var tickModel = angleAxisModel.getModel('axisTick');\n    var minorTickModel = angleAxisModel.getModel('minorTick');\n    var tickLen = (tickModel.get('inside') ? -1 : 1) * minorTickModel.get('length');\n    var radius = radiusExtent[getRadiusIdx(polar)];\n    var lines = [];\n    for (var i = 0; i < minorTickAngles.length; i++) {\n      for (var k = 0; k < minorTickAngles[i].length; k++) {\n        lines.push(new graphic.Line({\n          shape: getAxisLineShape(polar, [radius, radius + tickLen], minorTickAngles[i][k].coord)\n        }));\n      }\n    }\n    group.add(graphic.mergePath(lines, {\n      style: zrUtil.defaults(minorTickModel.getModel('lineStyle').getLineStyle(), zrUtil.defaults(tickModel.getLineStyle(), {\n        stroke: angleAxisModel.get(['axisLine', 'lineStyle', 'color'])\n      }))\n    }));\n  },\n  axisLabel: function (group, angleAxisModel, polar, ticksAngles, minorTickAngles, radiusExtent, labels) {\n    var rawCategoryData = angleAxisModel.getCategories(true);\n    var commonLabelModel = angleAxisModel.getModel('axisLabel');\n    var labelMargin = commonLabelModel.get('margin');\n    var triggerEvent = angleAxisModel.get('triggerEvent'); // Use length of ticksAngles because it may remove the last tick to avoid overlapping\n\n    zrUtil.each(labels, function (labelItem, idx) {\n      var labelModel = commonLabelModel;\n      var tickValue = labelItem.tickValue;\n      var r = radiusExtent[getRadiusIdx(polar)];\n      var p = polar.coordToPoint([r + labelMargin, labelItem.coord]);\n      var cx = polar.cx;\n      var cy = polar.cy;\n      var labelTextAlign = Math.abs(p[0] - cx) / r < 0.3 ? 'center' : p[0] > cx ? 'left' : 'right';\n      var labelTextVerticalAlign = Math.abs(p[1] - cy) / r < 0.3 ? 'middle' : p[1] > cy ? 'top' : 'bottom';\n      if (rawCategoryData && rawCategoryData[tickValue]) {\n        var rawCategoryItem = rawCategoryData[tickValue];\n        if (zrUtil.isObject(rawCategoryItem) && rawCategoryItem.textStyle) {\n          labelModel = new Model(rawCategoryItem.textStyle, commonLabelModel, commonLabelModel.ecModel);\n        }\n      }\n      var textEl = new graphic.Text({\n        silent: AxisBuilder.isLabelSilent(angleAxisModel),\n        style: createTextStyle(labelModel, {\n          x: p[0],\n          y: p[1],\n          fill: labelModel.getTextColor() || angleAxisModel.get(['axisLine', 'lineStyle', 'color']),\n          text: labelItem.formattedLabel,\n          align: labelTextAlign,\n          verticalAlign: labelTextVerticalAlign\n        })\n      });\n      group.add(textEl); // Pack data for mouse event\n\n      if (triggerEvent) {\n        var eventData = AxisBuilder.makeAxisEventDataBase(angleAxisModel);\n        eventData.targetType = 'axisLabel';\n        eventData.value = labelItem.rawLabel;\n        getECData(textEl).eventData = eventData;\n      }\n    }, this);\n  },\n  splitLine: function (group, angleAxisModel, polar, ticksAngles, minorTickAngles, radiusExtent) {\n    var splitLineModel = angleAxisModel.getModel('splitLine');\n    var lineStyleModel = splitLineModel.getModel('lineStyle');\n    var lineColors = lineStyleModel.get('color');\n    var lineCount = 0;\n    lineColors = lineColors instanceof Array ? lineColors : [lineColors];\n    var splitLines = [];\n    for (var i = 0; i < ticksAngles.length; i++) {\n      var colorIndex = lineCount++ % lineColors.length;\n      splitLines[colorIndex] = splitLines[colorIndex] || [];\n      splitLines[colorIndex].push(new graphic.Line({\n        shape: getAxisLineShape(polar, radiusExtent, ticksAngles[i].coord)\n      }));\n    } // Simple optimization\n    // Batching the lines if color are the same\n\n    for (var i = 0; i < splitLines.length; i++) {\n      group.add(graphic.mergePath(splitLines[i], {\n        style: zrUtil.defaults({\n          stroke: lineColors[i % lineColors.length]\n        }, lineStyleModel.getLineStyle()),\n        silent: true,\n        z: angleAxisModel.get('z')\n      }));\n    }\n  },\n  minorSplitLine: function (group, angleAxisModel, polar, ticksAngles, minorTickAngles, radiusExtent) {\n    if (!minorTickAngles.length) {\n      return;\n    }\n    var minorSplitLineModel = angleAxisModel.getModel('minorSplitLine');\n    var lineStyleModel = minorSplitLineModel.getModel('lineStyle');\n    var lines = [];\n    for (var i = 0; i < minorTickAngles.length; i++) {\n      for (var k = 0; k < minorTickAngles[i].length; k++) {\n        lines.push(new graphic.Line({\n          shape: getAxisLineShape(polar, radiusExtent, minorTickAngles[i][k].coord)\n        }));\n      }\n    }\n    group.add(graphic.mergePath(lines, {\n      style: lineStyleModel.getLineStyle(),\n      silent: true,\n      z: angleAxisModel.get('z')\n    }));\n  },\n  splitArea: function (group, angleAxisModel, polar, ticksAngles, minorTickAngles, radiusExtent) {\n    if (!ticksAngles.length) {\n      return;\n    }\n    var splitAreaModel = angleAxisModel.getModel('splitArea');\n    var areaStyleModel = splitAreaModel.getModel('areaStyle');\n    var areaColors = areaStyleModel.get('color');\n    var lineCount = 0;\n    areaColors = areaColors instanceof Array ? areaColors : [areaColors];\n    var splitAreas = [];\n    var RADIAN = Math.PI / 180;\n    var prevAngle = -ticksAngles[0].coord * RADIAN;\n    var r0 = Math.min(radiusExtent[0], radiusExtent[1]);\n    var r1 = Math.max(radiusExtent[0], radiusExtent[1]);\n    var clockwise = angleAxisModel.get('clockwise');\n    for (var i = 1, len = ticksAngles.length; i <= len; i++) {\n      var coord = i === len ? ticksAngles[0].coord : ticksAngles[i].coord;\n      var colorIndex = lineCount++ % areaColors.length;\n      splitAreas[colorIndex] = splitAreas[colorIndex] || [];\n      splitAreas[colorIndex].push(new graphic.Sector({\n        shape: {\n          cx: polar.cx,\n          cy: polar.cy,\n          r0: r0,\n          r: r1,\n          startAngle: prevAngle,\n          endAngle: -coord * RADIAN,\n          clockwise: clockwise\n        },\n        silent: true\n      }));\n      prevAngle = -coord * RADIAN;\n    } // Simple optimization\n    // Batching the lines if color are the same\n\n    for (var i = 0; i < splitAreas.length; i++) {\n      group.add(graphic.mergePath(splitAreas[i], {\n        style: zrUtil.defaults({\n          fill: areaColors[i % areaColors.length]\n        }, areaStyleModel.getAreaStyle()),\n        silent: true\n      }));\n    }\n  }\n};\nexport default AngleAxisView;", "map": {"version": 3, "names": ["__extends", "zrUtil", "graphic", "createTextStyle", "Model", "AxisView", "AxisBuilder", "getECData", "elementList", "getAxisLineShape", "polar", "rExtent", "angle", "slice", "reverse", "start", "coordToPoint", "end", "x1", "y1", "x2", "y2", "getRadiusIdx", "radiusAxis", "getRadiusAxis", "inverse", "fixAngleOverlap", "list", "firstItem", "lastItem", "length", "Math", "abs", "coord", "pop", "AngleAxisView", "_super", "_this", "apply", "arguments", "type", "axisPointerClass", "prototype", "render", "angleAxisModel", "ecModel", "group", "removeAll", "get", "angleAxis", "axis", "radiusExtent", "getExtent", "ticksAngles", "getTicksCoords", "minorTickAngles", "getMinorTicksCoords", "labels", "map", "getViewLabels", "labelItem", "clone", "scale", "tickValue", "getRawOrdinalNumber", "dataToCoord", "each", "name", "isBlank", "angelAxisElementsBuilders", "axisLine", "lineStyleModel", "getModel", "rId", "r0Id", "shape", "Circle", "cx", "cy", "r", "style", "getLineStyle", "z2", "silent", "Ring", "r0", "fill", "add", "axisTick", "tickModel", "tickLen", "radius", "lines", "tickAngleItem", "Line", "mergePath", "defaults", "stroke", "minor<PERSON><PERSON>", "tickAngles", "minorTickModel", "i", "k", "push", "axisLabel", "rawCategoryData", "getCategories", "commonLabelModel", "labelMargin", "triggerEvent", "idx", "labelModel", "p", "labelTextAlign", "labelTextVerticalAlign", "rawCategoryItem", "isObject", "textStyle", "textEl", "Text", "isLabelSilent", "x", "y", "getTextColor", "text", "formattedLabel", "align", "verticalAlign", "eventData", "makeAxisEventDataBase", "targetType", "value", "rawLabel", "splitLine", "splitLineModel", "lineColors", "lineCount", "Array", "splitLines", "colorIndex", "z", "minorSplitLine", "minorSplitLineModel", "splitArea", "splitAreaModel", "areaStyleModel", "areaColors", "splitAreas", "RADIAN", "PI", "prevAngle", "min", "r1", "max", "clockwise", "len", "Sector", "startAngle", "endAngle", "getAreaStyle"], "sources": ["C:/WolfSystemV2/WolfOldFrontEnd/node_modules/echarts/lib/component/axis/AngleAxisView.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport * as graphic from '../../util/graphic.js';\nimport { createTextStyle } from '../../label/labelStyle.js';\nimport Model from '../../model/Model.js';\nimport AxisView from './AxisView.js';\nimport AxisBuilder from './AxisBuilder.js';\nimport { getECData } from '../../util/innerStore.js';\nvar elementList = ['axisLine', 'axisLabel', 'axisTick', 'minorTick', 'splitLine', 'minorSplitLine', 'splitArea'];\n\nfunction getAxisLineShape(polar, rExtent, angle) {\n  rExtent[1] > rExtent[0] && (rExtent = rExtent.slice().reverse());\n  var start = polar.coordToPoint([rExtent[0], angle]);\n  var end = polar.coordToPoint([rExtent[1], angle]);\n  return {\n    x1: start[0],\n    y1: start[1],\n    x2: end[0],\n    y2: end[1]\n  };\n}\n\nfunction getRadiusIdx(polar) {\n  var radiusAxis = polar.getRadiusAxis();\n  return radiusAxis.inverse ? 0 : 1;\n} // Remove the last tick which will overlap the first tick\n\n\nfunction fixAngleOverlap(list) {\n  var firstItem = list[0];\n  var lastItem = list[list.length - 1];\n\n  if (firstItem && lastItem && Math.abs(Math.abs(firstItem.coord - lastItem.coord) - 360) < 1e-4) {\n    list.pop();\n  }\n}\n\nvar AngleAxisView =\n/** @class */\nfunction (_super) {\n  __extends(AngleAxisView, _super);\n\n  function AngleAxisView() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n\n    _this.type = AngleAxisView.type;\n    _this.axisPointerClass = 'PolarAxisPointer';\n    return _this;\n  }\n\n  AngleAxisView.prototype.render = function (angleAxisModel, ecModel) {\n    this.group.removeAll();\n\n    if (!angleAxisModel.get('show')) {\n      return;\n    }\n\n    var angleAxis = angleAxisModel.axis;\n    var polar = angleAxis.polar;\n    var radiusExtent = polar.getRadiusAxis().getExtent();\n    var ticksAngles = angleAxis.getTicksCoords();\n    var minorTickAngles = angleAxis.getMinorTicksCoords();\n    var labels = zrUtil.map(angleAxis.getViewLabels(), function (labelItem) {\n      labelItem = zrUtil.clone(labelItem);\n      var scale = angleAxis.scale;\n      var tickValue = scale.type === 'ordinal' ? scale.getRawOrdinalNumber(labelItem.tickValue) : labelItem.tickValue;\n      labelItem.coord = angleAxis.dataToCoord(tickValue);\n      return labelItem;\n    });\n    fixAngleOverlap(labels);\n    fixAngleOverlap(ticksAngles);\n    zrUtil.each(elementList, function (name) {\n      if (angleAxisModel.get([name, 'show']) && (!angleAxis.scale.isBlank() || name === 'axisLine')) {\n        angelAxisElementsBuilders[name](this.group, angleAxisModel, polar, ticksAngles, minorTickAngles, radiusExtent, labels);\n      }\n    }, this);\n  };\n\n  AngleAxisView.type = 'angleAxis';\n  return AngleAxisView;\n}(AxisView);\n\nvar angelAxisElementsBuilders = {\n  axisLine: function (group, angleAxisModel, polar, ticksAngles, minorTickAngles, radiusExtent) {\n    var lineStyleModel = angleAxisModel.getModel(['axisLine', 'lineStyle']); // extent id of the axis radius (r0 and r)\n\n    var rId = getRadiusIdx(polar);\n    var r0Id = rId ? 0 : 1;\n    var shape;\n\n    if (radiusExtent[r0Id] === 0) {\n      shape = new graphic.Circle({\n        shape: {\n          cx: polar.cx,\n          cy: polar.cy,\n          r: radiusExtent[rId]\n        },\n        style: lineStyleModel.getLineStyle(),\n        z2: 1,\n        silent: true\n      });\n    } else {\n      shape = new graphic.Ring({\n        shape: {\n          cx: polar.cx,\n          cy: polar.cy,\n          r: radiusExtent[rId],\n          r0: radiusExtent[r0Id]\n        },\n        style: lineStyleModel.getLineStyle(),\n        z2: 1,\n        silent: true\n      });\n    }\n\n    shape.style.fill = null;\n    group.add(shape);\n  },\n  axisTick: function (group, angleAxisModel, polar, ticksAngles, minorTickAngles, radiusExtent) {\n    var tickModel = angleAxisModel.getModel('axisTick');\n    var tickLen = (tickModel.get('inside') ? -1 : 1) * tickModel.get('length');\n    var radius = radiusExtent[getRadiusIdx(polar)];\n    var lines = zrUtil.map(ticksAngles, function (tickAngleItem) {\n      return new graphic.Line({\n        shape: getAxisLineShape(polar, [radius, radius + tickLen], tickAngleItem.coord)\n      });\n    });\n    group.add(graphic.mergePath(lines, {\n      style: zrUtil.defaults(tickModel.getModel('lineStyle').getLineStyle(), {\n        stroke: angleAxisModel.get(['axisLine', 'lineStyle', 'color'])\n      })\n    }));\n  },\n  minorTick: function (group, angleAxisModel, polar, tickAngles, minorTickAngles, radiusExtent) {\n    if (!minorTickAngles.length) {\n      return;\n    }\n\n    var tickModel = angleAxisModel.getModel('axisTick');\n    var minorTickModel = angleAxisModel.getModel('minorTick');\n    var tickLen = (tickModel.get('inside') ? -1 : 1) * minorTickModel.get('length');\n    var radius = radiusExtent[getRadiusIdx(polar)];\n    var lines = [];\n\n    for (var i = 0; i < minorTickAngles.length; i++) {\n      for (var k = 0; k < minorTickAngles[i].length; k++) {\n        lines.push(new graphic.Line({\n          shape: getAxisLineShape(polar, [radius, radius + tickLen], minorTickAngles[i][k].coord)\n        }));\n      }\n    }\n\n    group.add(graphic.mergePath(lines, {\n      style: zrUtil.defaults(minorTickModel.getModel('lineStyle').getLineStyle(), zrUtil.defaults(tickModel.getLineStyle(), {\n        stroke: angleAxisModel.get(['axisLine', 'lineStyle', 'color'])\n      }))\n    }));\n  },\n  axisLabel: function (group, angleAxisModel, polar, ticksAngles, minorTickAngles, radiusExtent, labels) {\n    var rawCategoryData = angleAxisModel.getCategories(true);\n    var commonLabelModel = angleAxisModel.getModel('axisLabel');\n    var labelMargin = commonLabelModel.get('margin');\n    var triggerEvent = angleAxisModel.get('triggerEvent'); // Use length of ticksAngles because it may remove the last tick to avoid overlapping\n\n    zrUtil.each(labels, function (labelItem, idx) {\n      var labelModel = commonLabelModel;\n      var tickValue = labelItem.tickValue;\n      var r = radiusExtent[getRadiusIdx(polar)];\n      var p = polar.coordToPoint([r + labelMargin, labelItem.coord]);\n      var cx = polar.cx;\n      var cy = polar.cy;\n      var labelTextAlign = Math.abs(p[0] - cx) / r < 0.3 ? 'center' : p[0] > cx ? 'left' : 'right';\n      var labelTextVerticalAlign = Math.abs(p[1] - cy) / r < 0.3 ? 'middle' : p[1] > cy ? 'top' : 'bottom';\n\n      if (rawCategoryData && rawCategoryData[tickValue]) {\n        var rawCategoryItem = rawCategoryData[tickValue];\n\n        if (zrUtil.isObject(rawCategoryItem) && rawCategoryItem.textStyle) {\n          labelModel = new Model(rawCategoryItem.textStyle, commonLabelModel, commonLabelModel.ecModel);\n        }\n      }\n\n      var textEl = new graphic.Text({\n        silent: AxisBuilder.isLabelSilent(angleAxisModel),\n        style: createTextStyle(labelModel, {\n          x: p[0],\n          y: p[1],\n          fill: labelModel.getTextColor() || angleAxisModel.get(['axisLine', 'lineStyle', 'color']),\n          text: labelItem.formattedLabel,\n          align: labelTextAlign,\n          verticalAlign: labelTextVerticalAlign\n        })\n      });\n      group.add(textEl); // Pack data for mouse event\n\n      if (triggerEvent) {\n        var eventData = AxisBuilder.makeAxisEventDataBase(angleAxisModel);\n        eventData.targetType = 'axisLabel';\n        eventData.value = labelItem.rawLabel;\n        getECData(textEl).eventData = eventData;\n      }\n    }, this);\n  },\n  splitLine: function (group, angleAxisModel, polar, ticksAngles, minorTickAngles, radiusExtent) {\n    var splitLineModel = angleAxisModel.getModel('splitLine');\n    var lineStyleModel = splitLineModel.getModel('lineStyle');\n    var lineColors = lineStyleModel.get('color');\n    var lineCount = 0;\n    lineColors = lineColors instanceof Array ? lineColors : [lineColors];\n    var splitLines = [];\n\n    for (var i = 0; i < ticksAngles.length; i++) {\n      var colorIndex = lineCount++ % lineColors.length;\n      splitLines[colorIndex] = splitLines[colorIndex] || [];\n      splitLines[colorIndex].push(new graphic.Line({\n        shape: getAxisLineShape(polar, radiusExtent, ticksAngles[i].coord)\n      }));\n    } // Simple optimization\n    // Batching the lines if color are the same\n\n\n    for (var i = 0; i < splitLines.length; i++) {\n      group.add(graphic.mergePath(splitLines[i], {\n        style: zrUtil.defaults({\n          stroke: lineColors[i % lineColors.length]\n        }, lineStyleModel.getLineStyle()),\n        silent: true,\n        z: angleAxisModel.get('z')\n      }));\n    }\n  },\n  minorSplitLine: function (group, angleAxisModel, polar, ticksAngles, minorTickAngles, radiusExtent) {\n    if (!minorTickAngles.length) {\n      return;\n    }\n\n    var minorSplitLineModel = angleAxisModel.getModel('minorSplitLine');\n    var lineStyleModel = minorSplitLineModel.getModel('lineStyle');\n    var lines = [];\n\n    for (var i = 0; i < minorTickAngles.length; i++) {\n      for (var k = 0; k < minorTickAngles[i].length; k++) {\n        lines.push(new graphic.Line({\n          shape: getAxisLineShape(polar, radiusExtent, minorTickAngles[i][k].coord)\n        }));\n      }\n    }\n\n    group.add(graphic.mergePath(lines, {\n      style: lineStyleModel.getLineStyle(),\n      silent: true,\n      z: angleAxisModel.get('z')\n    }));\n  },\n  splitArea: function (group, angleAxisModel, polar, ticksAngles, minorTickAngles, radiusExtent) {\n    if (!ticksAngles.length) {\n      return;\n    }\n\n    var splitAreaModel = angleAxisModel.getModel('splitArea');\n    var areaStyleModel = splitAreaModel.getModel('areaStyle');\n    var areaColors = areaStyleModel.get('color');\n    var lineCount = 0;\n    areaColors = areaColors instanceof Array ? areaColors : [areaColors];\n    var splitAreas = [];\n    var RADIAN = Math.PI / 180;\n    var prevAngle = -ticksAngles[0].coord * RADIAN;\n    var r0 = Math.min(radiusExtent[0], radiusExtent[1]);\n    var r1 = Math.max(radiusExtent[0], radiusExtent[1]);\n    var clockwise = angleAxisModel.get('clockwise');\n\n    for (var i = 1, len = ticksAngles.length; i <= len; i++) {\n      var coord = i === len ? ticksAngles[0].coord : ticksAngles[i].coord;\n      var colorIndex = lineCount++ % areaColors.length;\n      splitAreas[colorIndex] = splitAreas[colorIndex] || [];\n      splitAreas[colorIndex].push(new graphic.Sector({\n        shape: {\n          cx: polar.cx,\n          cy: polar.cy,\n          r0: r0,\n          r: r1,\n          startAngle: prevAngle,\n          endAngle: -coord * RADIAN,\n          clockwise: clockwise\n        },\n        silent: true\n      }));\n      prevAngle = -coord * RADIAN;\n    } // Simple optimization\n    // Batching the lines if color are the same\n\n\n    for (var i = 0; i < splitAreas.length; i++) {\n      group.add(graphic.mergePath(splitAreas[i], {\n        style: zrUtil.defaults({\n          fill: areaColors[i % areaColors.length]\n        }, areaStyleModel.getAreaStyle()),\n        silent: true\n      }));\n    }\n  }\n};\nexport default AngleAxisView;"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,SAAS,QAAQ,OAAO;AACjC,OAAO,KAAKC,MAAM,MAAM,0BAA0B;AAClD,OAAO,KAAKC,OAAO,MAAM,uBAAuB;AAChD,SAASC,eAAe,QAAQ,2BAA2B;AAC3D,OAAOC,KAAK,MAAM,sBAAsB;AACxC,OAAOC,QAAQ,MAAM,eAAe;AACpC,OAAOC,WAAW,MAAM,kBAAkB;AAC1C,SAASC,SAAS,QAAQ,0BAA0B;AACpD,IAAIC,WAAW,GAAG,CAAC,UAAU,EAAE,WAAW,EAAE,UAAU,EAAE,WAAW,EAAE,WAAW,EAAE,gBAAgB,EAAE,WAAW,CAAC;AAEhH,SAASC,gBAAgBA,CAACC,KAAK,EAAEC,OAAO,EAAEC,KAAK,EAAE;EAC/CD,OAAO,CAAC,CAAC,CAAC,GAAGA,OAAO,CAAC,CAAC,CAAC,KAAKA,OAAO,GAAGA,OAAO,CAACE,KAAK,CAAC,CAAC,CAACC,OAAO,CAAC,CAAC,CAAC;EAChE,IAAIC,KAAK,GAAGL,KAAK,CAACM,YAAY,CAAC,CAACL,OAAO,CAAC,CAAC,CAAC,EAAEC,KAAK,CAAC,CAAC;EACnD,IAAIK,GAAG,GAAGP,KAAK,CAACM,YAAY,CAAC,CAACL,OAAO,CAAC,CAAC,CAAC,EAAEC,KAAK,CAAC,CAAC;EACjD,OAAO;IACLM,EAAE,EAAEH,KAAK,CAAC,CAAC,CAAC;IACZI,EAAE,EAAEJ,KAAK,CAAC,CAAC,CAAC;IACZK,EAAE,EAAEH,GAAG,CAAC,CAAC,CAAC;IACVI,EAAE,EAAEJ,GAAG,CAAC,CAAC;EACX,CAAC;AACH;AAEA,SAASK,YAAYA,CAACZ,KAAK,EAAE;EAC3B,IAAIa,UAAU,GAAGb,KAAK,CAACc,aAAa,CAAC,CAAC;EACtC,OAAOD,UAAU,CAACE,OAAO,GAAG,CAAC,GAAG,CAAC;AACnC,CAAC,CAAC;;AAGF,SAASC,eAAeA,CAACC,IAAI,EAAE;EAC7B,IAAIC,SAAS,GAAGD,IAAI,CAAC,CAAC,CAAC;EACvB,IAAIE,QAAQ,GAAGF,IAAI,CAACA,IAAI,CAACG,MAAM,GAAG,CAAC,CAAC;EAEpC,IAAIF,SAAS,IAAIC,QAAQ,IAAIE,IAAI,CAACC,GAAG,CAACD,IAAI,CAACC,GAAG,CAACJ,SAAS,CAACK,KAAK,GAAGJ,QAAQ,CAACI,KAAK,CAAC,GAAG,GAAG,CAAC,GAAG,IAAI,EAAE;IAC9FN,IAAI,CAACO,GAAG,CAAC,CAAC;EACZ;AACF;AAEA,IAAIC,aAAa,GACjB;AACA,UAAUC,MAAM,EAAE;EAChBpC,SAAS,CAACmC,aAAa,EAAEC,MAAM,CAAC;EAEhC,SAASD,aAAaA,CAAA,EAAG;IACvB,IAAIE,KAAK,GAAGD,MAAM,KAAK,IAAI,IAAIA,MAAM,CAACE,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC,IAAI,IAAI;IAEpEF,KAAK,CAACG,IAAI,GAAGL,aAAa,CAACK,IAAI;IAC/BH,KAAK,CAACI,gBAAgB,GAAG,kBAAkB;IAC3C,OAAOJ,KAAK;EACd;EAEAF,aAAa,CAACO,SAAS,CAACC,MAAM,GAAG,UAAUC,cAAc,EAAEC,OAAO,EAAE;IAClE,IAAI,CAACC,KAAK,CAACC,SAAS,CAAC,CAAC;IAEtB,IAAI,CAACH,cAAc,CAACI,GAAG,CAAC,MAAM,CAAC,EAAE;MAC/B;IACF;IAEA,IAAIC,SAAS,GAAGL,cAAc,CAACM,IAAI;IACnC,IAAIxC,KAAK,GAAGuC,SAAS,CAACvC,KAAK;IAC3B,IAAIyC,YAAY,GAAGzC,KAAK,CAACc,aAAa,CAAC,CAAC,CAAC4B,SAAS,CAAC,CAAC;IACpD,IAAIC,WAAW,GAAGJ,SAAS,CAACK,cAAc,CAAC,CAAC;IAC5C,IAAIC,eAAe,GAAGN,SAAS,CAACO,mBAAmB,CAAC,CAAC;IACrD,IAAIC,MAAM,GAAGxD,MAAM,CAACyD,GAAG,CAACT,SAAS,CAACU,aAAa,CAAC,CAAC,EAAE,UAAUC,SAAS,EAAE;MACtEA,SAAS,GAAG3D,MAAM,CAAC4D,KAAK,CAACD,SAAS,CAAC;MACnC,IAAIE,KAAK,GAAGb,SAAS,CAACa,KAAK;MAC3B,IAAIC,SAAS,GAAGD,KAAK,CAACtB,IAAI,KAAK,SAAS,GAAGsB,KAAK,CAACE,mBAAmB,CAACJ,SAAS,CAACG,SAAS,CAAC,GAAGH,SAAS,CAACG,SAAS;MAC/GH,SAAS,CAAC3B,KAAK,GAAGgB,SAAS,CAACgB,WAAW,CAACF,SAAS,CAAC;MAClD,OAAOH,SAAS;IAClB,CAAC,CAAC;IACFlC,eAAe,CAAC+B,MAAM,CAAC;IACvB/B,eAAe,CAAC2B,WAAW,CAAC;IAC5BpD,MAAM,CAACiE,IAAI,CAAC1D,WAAW,EAAE,UAAU2D,IAAI,EAAE;MACvC,IAAIvB,cAAc,CAACI,GAAG,CAAC,CAACmB,IAAI,EAAE,MAAM,CAAC,CAAC,KAAK,CAAClB,SAAS,CAACa,KAAK,CAACM,OAAO,CAAC,CAAC,IAAID,IAAI,KAAK,UAAU,CAAC,EAAE;QAC7FE,yBAAyB,CAACF,IAAI,CAAC,CAAC,IAAI,CAACrB,KAAK,EAAEF,cAAc,EAAElC,KAAK,EAAE2C,WAAW,EAAEE,eAAe,EAAEJ,YAAY,EAAEM,MAAM,CAAC;MACxH;IACF,CAAC,EAAE,IAAI,CAAC;EACV,CAAC;EAEDtB,aAAa,CAACK,IAAI,GAAG,WAAW;EAChC,OAAOL,aAAa;AACtB,CAAC,CAAC9B,QAAQ,CAAC;AAEX,IAAIgE,yBAAyB,GAAG;EAC9BC,QAAQ,EAAE,SAAAA,CAAUxB,KAAK,EAAEF,cAAc,EAAElC,KAAK,EAAE2C,WAAW,EAAEE,eAAe,EAAEJ,YAAY,EAAE;IAC5F,IAAIoB,cAAc,GAAG3B,cAAc,CAAC4B,QAAQ,CAAC,CAAC,UAAU,EAAE,WAAW,CAAC,CAAC,CAAC,CAAC;;IAEzE,IAAIC,GAAG,GAAGnD,YAAY,CAACZ,KAAK,CAAC;IAC7B,IAAIgE,IAAI,GAAGD,GAAG,GAAG,CAAC,GAAG,CAAC;IACtB,IAAIE,KAAK;IAET,IAAIxB,YAAY,CAACuB,IAAI,CAAC,KAAK,CAAC,EAAE;MAC5BC,KAAK,GAAG,IAAIzE,OAAO,CAAC0E,MAAM,CAAC;QACzBD,KAAK,EAAE;UACLE,EAAE,EAAEnE,KAAK,CAACmE,EAAE;UACZC,EAAE,EAAEpE,KAAK,CAACoE,EAAE;UACZC,CAAC,EAAE5B,YAAY,CAACsB,GAAG;QACrB,CAAC;QACDO,KAAK,EAAET,cAAc,CAACU,YAAY,CAAC,CAAC;QACpCC,EAAE,EAAE,CAAC;QACLC,MAAM,EAAE;MACV,CAAC,CAAC;IACJ,CAAC,MAAM;MACLR,KAAK,GAAG,IAAIzE,OAAO,CAACkF,IAAI,CAAC;QACvBT,KAAK,EAAE;UACLE,EAAE,EAAEnE,KAAK,CAACmE,EAAE;UACZC,EAAE,EAAEpE,KAAK,CAACoE,EAAE;UACZC,CAAC,EAAE5B,YAAY,CAACsB,GAAG,CAAC;UACpBY,EAAE,EAAElC,YAAY,CAACuB,IAAI;QACvB,CAAC;QACDM,KAAK,EAAET,cAAc,CAACU,YAAY,CAAC,CAAC;QACpCC,EAAE,EAAE,CAAC;QACLC,MAAM,EAAE;MACV,CAAC,CAAC;IACJ;IAEAR,KAAK,CAACK,KAAK,CAACM,IAAI,GAAG,IAAI;IACvBxC,KAAK,CAACyC,GAAG,CAACZ,KAAK,CAAC;EAClB,CAAC;EACDa,QAAQ,EAAE,SAAAA,CAAU1C,KAAK,EAAEF,cAAc,EAAElC,KAAK,EAAE2C,WAAW,EAAEE,eAAe,EAAEJ,YAAY,EAAE;IAC5F,IAAIsC,SAAS,GAAG7C,cAAc,CAAC4B,QAAQ,CAAC,UAAU,CAAC;IACnD,IAAIkB,OAAO,GAAG,CAACD,SAAS,CAACzC,GAAG,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,IAAIyC,SAAS,CAACzC,GAAG,CAAC,QAAQ,CAAC;IAC1E,IAAI2C,MAAM,GAAGxC,YAAY,CAAC7B,YAAY,CAACZ,KAAK,CAAC,CAAC;IAC9C,IAAIkF,KAAK,GAAG3F,MAAM,CAACyD,GAAG,CAACL,WAAW,EAAE,UAAUwC,aAAa,EAAE;MAC3D,OAAO,IAAI3F,OAAO,CAAC4F,IAAI,CAAC;QACtBnB,KAAK,EAAElE,gBAAgB,CAACC,KAAK,EAAE,CAACiF,MAAM,EAAEA,MAAM,GAAGD,OAAO,CAAC,EAAEG,aAAa,CAAC5D,KAAK;MAChF,CAAC,CAAC;IACJ,CAAC,CAAC;IACFa,KAAK,CAACyC,GAAG,CAACrF,OAAO,CAAC6F,SAAS,CAACH,KAAK,EAAE;MACjCZ,KAAK,EAAE/E,MAAM,CAAC+F,QAAQ,CAACP,SAAS,CAACjB,QAAQ,CAAC,WAAW,CAAC,CAACS,YAAY,CAAC,CAAC,EAAE;QACrEgB,MAAM,EAAErD,cAAc,CAACI,GAAG,CAAC,CAAC,UAAU,EAAE,WAAW,EAAE,OAAO,CAAC;MAC/D,CAAC;IACH,CAAC,CAAC,CAAC;EACL,CAAC;EACDkD,SAAS,EAAE,SAAAA,CAAUpD,KAAK,EAAEF,cAAc,EAAElC,KAAK,EAAEyF,UAAU,EAAE5C,eAAe,EAAEJ,YAAY,EAAE;IAC5F,IAAI,CAACI,eAAe,CAACzB,MAAM,EAAE;MAC3B;IACF;IAEA,IAAI2D,SAAS,GAAG7C,cAAc,CAAC4B,QAAQ,CAAC,UAAU,CAAC;IACnD,IAAI4B,cAAc,GAAGxD,cAAc,CAAC4B,QAAQ,CAAC,WAAW,CAAC;IACzD,IAAIkB,OAAO,GAAG,CAACD,SAAS,CAACzC,GAAG,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,IAAIoD,cAAc,CAACpD,GAAG,CAAC,QAAQ,CAAC;IAC/E,IAAI2C,MAAM,GAAGxC,YAAY,CAAC7B,YAAY,CAACZ,KAAK,CAAC,CAAC;IAC9C,IAAIkF,KAAK,GAAG,EAAE;IAEd,KAAK,IAAIS,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG9C,eAAe,CAACzB,MAAM,EAAEuE,CAAC,EAAE,EAAE;MAC/C,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG/C,eAAe,CAAC8C,CAAC,CAAC,CAACvE,MAAM,EAAEwE,CAAC,EAAE,EAAE;QAClDV,KAAK,CAACW,IAAI,CAAC,IAAIrG,OAAO,CAAC4F,IAAI,CAAC;UAC1BnB,KAAK,EAAElE,gBAAgB,CAACC,KAAK,EAAE,CAACiF,MAAM,EAAEA,MAAM,GAAGD,OAAO,CAAC,EAAEnC,eAAe,CAAC8C,CAAC,CAAC,CAACC,CAAC,CAAC,CAACrE,KAAK;QACxF,CAAC,CAAC,CAAC;MACL;IACF;IAEAa,KAAK,CAACyC,GAAG,CAACrF,OAAO,CAAC6F,SAAS,CAACH,KAAK,EAAE;MACjCZ,KAAK,EAAE/E,MAAM,CAAC+F,QAAQ,CAACI,cAAc,CAAC5B,QAAQ,CAAC,WAAW,CAAC,CAACS,YAAY,CAAC,CAAC,EAAEhF,MAAM,CAAC+F,QAAQ,CAACP,SAAS,CAACR,YAAY,CAAC,CAAC,EAAE;QACpHgB,MAAM,EAAErD,cAAc,CAACI,GAAG,CAAC,CAAC,UAAU,EAAE,WAAW,EAAE,OAAO,CAAC;MAC/D,CAAC,CAAC;IACJ,CAAC,CAAC,CAAC;EACL,CAAC;EACDwD,SAAS,EAAE,SAAAA,CAAU1D,KAAK,EAAEF,cAAc,EAAElC,KAAK,EAAE2C,WAAW,EAAEE,eAAe,EAAEJ,YAAY,EAAEM,MAAM,EAAE;IACrG,IAAIgD,eAAe,GAAG7D,cAAc,CAAC8D,aAAa,CAAC,IAAI,CAAC;IACxD,IAAIC,gBAAgB,GAAG/D,cAAc,CAAC4B,QAAQ,CAAC,WAAW,CAAC;IAC3D,IAAIoC,WAAW,GAAGD,gBAAgB,CAAC3D,GAAG,CAAC,QAAQ,CAAC;IAChD,IAAI6D,YAAY,GAAGjE,cAAc,CAACI,GAAG,CAAC,cAAc,CAAC,CAAC,CAAC;;IAEvD/C,MAAM,CAACiE,IAAI,CAACT,MAAM,EAAE,UAAUG,SAAS,EAAEkD,GAAG,EAAE;MAC5C,IAAIC,UAAU,GAAGJ,gBAAgB;MACjC,IAAI5C,SAAS,GAAGH,SAAS,CAACG,SAAS;MACnC,IAAIgB,CAAC,GAAG5B,YAAY,CAAC7B,YAAY,CAACZ,KAAK,CAAC,CAAC;MACzC,IAAIsG,CAAC,GAAGtG,KAAK,CAACM,YAAY,CAAC,CAAC+D,CAAC,GAAG6B,WAAW,EAAEhD,SAAS,CAAC3B,KAAK,CAAC,CAAC;MAC9D,IAAI4C,EAAE,GAAGnE,KAAK,CAACmE,EAAE;MACjB,IAAIC,EAAE,GAAGpE,KAAK,CAACoE,EAAE;MACjB,IAAImC,cAAc,GAAGlF,IAAI,CAACC,GAAG,CAACgF,CAAC,CAAC,CAAC,CAAC,GAAGnC,EAAE,CAAC,GAAGE,CAAC,GAAG,GAAG,GAAG,QAAQ,GAAGiC,CAAC,CAAC,CAAC,CAAC,GAAGnC,EAAE,GAAG,MAAM,GAAG,OAAO;MAC5F,IAAIqC,sBAAsB,GAAGnF,IAAI,CAACC,GAAG,CAACgF,CAAC,CAAC,CAAC,CAAC,GAAGlC,EAAE,CAAC,GAAGC,CAAC,GAAG,GAAG,GAAG,QAAQ,GAAGiC,CAAC,CAAC,CAAC,CAAC,GAAGlC,EAAE,GAAG,KAAK,GAAG,QAAQ;MAEpG,IAAI2B,eAAe,IAAIA,eAAe,CAAC1C,SAAS,CAAC,EAAE;QACjD,IAAIoD,eAAe,GAAGV,eAAe,CAAC1C,SAAS,CAAC;QAEhD,IAAI9D,MAAM,CAACmH,QAAQ,CAACD,eAAe,CAAC,IAAIA,eAAe,CAACE,SAAS,EAAE;UACjEN,UAAU,GAAG,IAAI3G,KAAK,CAAC+G,eAAe,CAACE,SAAS,EAAEV,gBAAgB,EAAEA,gBAAgB,CAAC9D,OAAO,CAAC;QAC/F;MACF;MAEA,IAAIyE,MAAM,GAAG,IAAIpH,OAAO,CAACqH,IAAI,CAAC;QAC5BpC,MAAM,EAAE7E,WAAW,CAACkH,aAAa,CAAC5E,cAAc,CAAC;QACjDoC,KAAK,EAAE7E,eAAe,CAAC4G,UAAU,EAAE;UACjCU,CAAC,EAAET,CAAC,CAAC,CAAC,CAAC;UACPU,CAAC,EAAEV,CAAC,CAAC,CAAC,CAAC;UACP1B,IAAI,EAAEyB,UAAU,CAACY,YAAY,CAAC,CAAC,IAAI/E,cAAc,CAACI,GAAG,CAAC,CAAC,UAAU,EAAE,WAAW,EAAE,OAAO,CAAC,CAAC;UACzF4E,IAAI,EAAEhE,SAAS,CAACiE,cAAc;UAC9BC,KAAK,EAAEb,cAAc;UACrBc,aAAa,EAAEb;QACjB,CAAC;MACH,CAAC,CAAC;MACFpE,KAAK,CAACyC,GAAG,CAAC+B,MAAM,CAAC,CAAC,CAAC;;MAEnB,IAAIT,YAAY,EAAE;QAChB,IAAImB,SAAS,GAAG1H,WAAW,CAAC2H,qBAAqB,CAACrF,cAAc,CAAC;QACjEoF,SAAS,CAACE,UAAU,GAAG,WAAW;QAClCF,SAAS,CAACG,KAAK,GAAGvE,SAAS,CAACwE,QAAQ;QACpC7H,SAAS,CAAC+G,MAAM,CAAC,CAACU,SAAS,GAAGA,SAAS;MACzC;IACF,CAAC,EAAE,IAAI,CAAC;EACV,CAAC;EACDK,SAAS,EAAE,SAAAA,CAAUvF,KAAK,EAAEF,cAAc,EAAElC,KAAK,EAAE2C,WAAW,EAAEE,eAAe,EAAEJ,YAAY,EAAE;IAC7F,IAAImF,cAAc,GAAG1F,cAAc,CAAC4B,QAAQ,CAAC,WAAW,CAAC;IACzD,IAAID,cAAc,GAAG+D,cAAc,CAAC9D,QAAQ,CAAC,WAAW,CAAC;IACzD,IAAI+D,UAAU,GAAGhE,cAAc,CAACvB,GAAG,CAAC,OAAO,CAAC;IAC5C,IAAIwF,SAAS,GAAG,CAAC;IACjBD,UAAU,GAAGA,UAAU,YAAYE,KAAK,GAAGF,UAAU,GAAG,CAACA,UAAU,CAAC;IACpE,IAAIG,UAAU,GAAG,EAAE;IAEnB,KAAK,IAAIrC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGhD,WAAW,CAACvB,MAAM,EAAEuE,CAAC,EAAE,EAAE;MAC3C,IAAIsC,UAAU,GAAGH,SAAS,EAAE,GAAGD,UAAU,CAACzG,MAAM;MAChD4G,UAAU,CAACC,UAAU,CAAC,GAAGD,UAAU,CAACC,UAAU,CAAC,IAAI,EAAE;MACrDD,UAAU,CAACC,UAAU,CAAC,CAACpC,IAAI,CAAC,IAAIrG,OAAO,CAAC4F,IAAI,CAAC;QAC3CnB,KAAK,EAAElE,gBAAgB,CAACC,KAAK,EAAEyC,YAAY,EAAEE,WAAW,CAACgD,CAAC,CAAC,CAACpE,KAAK;MACnE,CAAC,CAAC,CAAC;IACL,CAAC,CAAC;IACF;;IAGA,KAAK,IAAIoE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGqC,UAAU,CAAC5G,MAAM,EAAEuE,CAAC,EAAE,EAAE;MAC1CvD,KAAK,CAACyC,GAAG,CAACrF,OAAO,CAAC6F,SAAS,CAAC2C,UAAU,CAACrC,CAAC,CAAC,EAAE;QACzCrB,KAAK,EAAE/E,MAAM,CAAC+F,QAAQ,CAAC;UACrBC,MAAM,EAAEsC,UAAU,CAAClC,CAAC,GAAGkC,UAAU,CAACzG,MAAM;QAC1C,CAAC,EAAEyC,cAAc,CAACU,YAAY,CAAC,CAAC,CAAC;QACjCE,MAAM,EAAE,IAAI;QACZyD,CAAC,EAAEhG,cAAc,CAACI,GAAG,CAAC,GAAG;MAC3B,CAAC,CAAC,CAAC;IACL;EACF,CAAC;EACD6F,cAAc,EAAE,SAAAA,CAAU/F,KAAK,EAAEF,cAAc,EAAElC,KAAK,EAAE2C,WAAW,EAAEE,eAAe,EAAEJ,YAAY,EAAE;IAClG,IAAI,CAACI,eAAe,CAACzB,MAAM,EAAE;MAC3B;IACF;IAEA,IAAIgH,mBAAmB,GAAGlG,cAAc,CAAC4B,QAAQ,CAAC,gBAAgB,CAAC;IACnE,IAAID,cAAc,GAAGuE,mBAAmB,CAACtE,QAAQ,CAAC,WAAW,CAAC;IAC9D,IAAIoB,KAAK,GAAG,EAAE;IAEd,KAAK,IAAIS,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG9C,eAAe,CAACzB,MAAM,EAAEuE,CAAC,EAAE,EAAE;MAC/C,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG/C,eAAe,CAAC8C,CAAC,CAAC,CAACvE,MAAM,EAAEwE,CAAC,EAAE,EAAE;QAClDV,KAAK,CAACW,IAAI,CAAC,IAAIrG,OAAO,CAAC4F,IAAI,CAAC;UAC1BnB,KAAK,EAAElE,gBAAgB,CAACC,KAAK,EAAEyC,YAAY,EAAEI,eAAe,CAAC8C,CAAC,CAAC,CAACC,CAAC,CAAC,CAACrE,KAAK;QAC1E,CAAC,CAAC,CAAC;MACL;IACF;IAEAa,KAAK,CAACyC,GAAG,CAACrF,OAAO,CAAC6F,SAAS,CAACH,KAAK,EAAE;MACjCZ,KAAK,EAAET,cAAc,CAACU,YAAY,CAAC,CAAC;MACpCE,MAAM,EAAE,IAAI;MACZyD,CAAC,EAAEhG,cAAc,CAACI,GAAG,CAAC,GAAG;IAC3B,CAAC,CAAC,CAAC;EACL,CAAC;EACD+F,SAAS,EAAE,SAAAA,CAAUjG,KAAK,EAAEF,cAAc,EAAElC,KAAK,EAAE2C,WAAW,EAAEE,eAAe,EAAEJ,YAAY,EAAE;IAC7F,IAAI,CAACE,WAAW,CAACvB,MAAM,EAAE;MACvB;IACF;IAEA,IAAIkH,cAAc,GAAGpG,cAAc,CAAC4B,QAAQ,CAAC,WAAW,CAAC;IACzD,IAAIyE,cAAc,GAAGD,cAAc,CAACxE,QAAQ,CAAC,WAAW,CAAC;IACzD,IAAI0E,UAAU,GAAGD,cAAc,CAACjG,GAAG,CAAC,OAAO,CAAC;IAC5C,IAAIwF,SAAS,GAAG,CAAC;IACjBU,UAAU,GAAGA,UAAU,YAAYT,KAAK,GAAGS,UAAU,GAAG,CAACA,UAAU,CAAC;IACpE,IAAIC,UAAU,GAAG,EAAE;IACnB,IAAIC,MAAM,GAAGrH,IAAI,CAACsH,EAAE,GAAG,GAAG;IAC1B,IAAIC,SAAS,GAAG,CAACjG,WAAW,CAAC,CAAC,CAAC,CAACpB,KAAK,GAAGmH,MAAM;IAC9C,IAAI/D,EAAE,GAAGtD,IAAI,CAACwH,GAAG,CAACpG,YAAY,CAAC,CAAC,CAAC,EAAEA,YAAY,CAAC,CAAC,CAAC,CAAC;IACnD,IAAIqG,EAAE,GAAGzH,IAAI,CAAC0H,GAAG,CAACtG,YAAY,CAAC,CAAC,CAAC,EAAEA,YAAY,CAAC,CAAC,CAAC,CAAC;IACnD,IAAIuG,SAAS,GAAG9G,cAAc,CAACI,GAAG,CAAC,WAAW,CAAC;IAE/C,KAAK,IAAIqD,CAAC,GAAG,CAAC,EAAEsD,GAAG,GAAGtG,WAAW,CAACvB,MAAM,EAAEuE,CAAC,IAAIsD,GAAG,EAAEtD,CAAC,EAAE,EAAE;MACvD,IAAIpE,KAAK,GAAGoE,CAAC,KAAKsD,GAAG,GAAGtG,WAAW,CAAC,CAAC,CAAC,CAACpB,KAAK,GAAGoB,WAAW,CAACgD,CAAC,CAAC,CAACpE,KAAK;MACnE,IAAI0G,UAAU,GAAGH,SAAS,EAAE,GAAGU,UAAU,CAACpH,MAAM;MAChDqH,UAAU,CAACR,UAAU,CAAC,GAAGQ,UAAU,CAACR,UAAU,CAAC,IAAI,EAAE;MACrDQ,UAAU,CAACR,UAAU,CAAC,CAACpC,IAAI,CAAC,IAAIrG,OAAO,CAAC0J,MAAM,CAAC;QAC7CjF,KAAK,EAAE;UACLE,EAAE,EAAEnE,KAAK,CAACmE,EAAE;UACZC,EAAE,EAAEpE,KAAK,CAACoE,EAAE;UACZO,EAAE,EAAEA,EAAE;UACNN,CAAC,EAAEyE,EAAE;UACLK,UAAU,EAAEP,SAAS;UACrBQ,QAAQ,EAAE,CAAC7H,KAAK,GAAGmH,MAAM;UACzBM,SAAS,EAAEA;QACb,CAAC;QACDvE,MAAM,EAAE;MACV,CAAC,CAAC,CAAC;MACHmE,SAAS,GAAG,CAACrH,KAAK,GAAGmH,MAAM;IAC7B,CAAC,CAAC;IACF;;IAGA,KAAK,IAAI/C,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG8C,UAAU,CAACrH,MAAM,EAAEuE,CAAC,EAAE,EAAE;MAC1CvD,KAAK,CAACyC,GAAG,CAACrF,OAAO,CAAC6F,SAAS,CAACoD,UAAU,CAAC9C,CAAC,CAAC,EAAE;QACzCrB,KAAK,EAAE/E,MAAM,CAAC+F,QAAQ,CAAC;UACrBV,IAAI,EAAE4D,UAAU,CAAC7C,CAAC,GAAG6C,UAAU,CAACpH,MAAM;QACxC,CAAC,EAAEmH,cAAc,CAACc,YAAY,CAAC,CAAC,CAAC;QACjC5E,MAAM,EAAE;MACV,CAAC,CAAC,CAAC;IACL;EACF;AACF,CAAC;AACD,eAAehD,aAAa"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}