{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nvar actionInfo = {\n  type: 'axisAreaSelect',\n  event: 'axisAreaSelected' // update: 'updateVisual'\n};\n\nexport function installParallelActions(registers) {\n  registers.registerAction(actionInfo, function (payload, ecModel) {\n    ecModel.eachComponent({\n      mainType: 'parallelAxis',\n      query: payload\n    }, function (parallelAxisModel) {\n      parallelAxisModel.axis.model.setActiveIntervals(payload.intervals);\n    });\n  });\n  /**\r\n   * @payload\r\n   */\n\n  registers.registerAction('parallelAxisExpand', function (payload, ecModel) {\n    ecModel.eachComponent({\n      mainType: 'parallel',\n      query: payload\n    }, function (parallelModel) {\n      parallelModel.setAxisExpand(payload);\n    });\n  });\n}", "map": {"version": 3, "names": ["actionInfo", "type", "event", "installParallelActions", "registers", "registerAction", "payload", "ecModel", "eachComponent", "mainType", "query", "parallelAxisModel", "axis", "model", "setActiveIntervals", "intervals", "parallelModel", "setAxisExpand"], "sources": ["C:/WolfSystemV2/WolfOldFrontEnd/node_modules/echarts/lib/component/axis/parallelAxisAction.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nvar actionInfo = {\n  type: 'axisAreaSelect',\n  event: 'axisAreaSelected' // update: 'updateVisual'\n\n};\nexport function installParallelActions(registers) {\n  registers.registerAction(actionInfo, function (payload, ecModel) {\n    ecModel.eachComponent({\n      mainType: 'parallelAxis',\n      query: payload\n    }, function (parallelAxisModel) {\n      parallelAxisModel.axis.model.setActiveIntervals(payload.intervals);\n    });\n  });\n  /**\r\n   * @payload\r\n   */\n\n  registers.registerAction('parallelAxisExpand', function (payload, ecModel) {\n    ecModel.eachComponent({\n      mainType: 'parallel',\n      query: payload\n    }, function (parallelModel) {\n      parallelModel.setAxisExpand(payload);\n    });\n  });\n}"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIA,UAAU,GAAG;EACfC,IAAI,EAAE,gBAAgB;EACtBC,KAAK,EAAE,kBAAkB,CAAC;AAE5B,CAAC;;AACD,OAAO,SAASC,sBAAsBA,CAACC,SAAS,EAAE;EAChDA,SAAS,CAACC,cAAc,CAACL,UAAU,EAAE,UAAUM,OAAO,EAAEC,OAAO,EAAE;IAC/DA,OAAO,CAACC,aAAa,CAAC;MACpBC,QAAQ,EAAE,cAAc;MACxBC,KAAK,EAAEJ;IACT,CAAC,EAAE,UAAUK,iBAAiB,EAAE;MAC9BA,iBAAiB,CAACC,IAAI,CAACC,KAAK,CAACC,kBAAkB,CAACR,OAAO,CAACS,SAAS,CAAC;IACpE,CAAC,CAAC;EACJ,CAAC,CAAC;EACF;AACF;AACA;;EAEEX,SAAS,CAACC,cAAc,CAAC,oBAAoB,EAAE,UAAUC,OAAO,EAAEC,OAAO,EAAE;IACzEA,OAAO,CAACC,aAAa,CAAC;MACpBC,QAAQ,EAAE,UAAU;MACpBC,KAAK,EAAEJ;IACT,CAAC,EAAE,UAAUU,aAAa,EAAE;MAC1BA,aAAa,CAACC,aAAa,CAACX,OAAO,CAAC;IACtC,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}