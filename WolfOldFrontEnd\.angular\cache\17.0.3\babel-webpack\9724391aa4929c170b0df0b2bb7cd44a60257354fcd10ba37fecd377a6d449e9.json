{"ast": null, "code": "//! moment.js locale configuration\n//! locale : Italian (Switzerland) [it-ch]\n//! author : xfh : https://github.com/xfh\n\n;\n(function (global, factory) {\n  typeof exports === 'object' && typeof module !== 'undefined' && typeof require === 'function' ? factory(require('../moment')) : typeof define === 'function' && define.amd ? define(['../moment'], factory) : factory(global.moment);\n})(this, function (moment) {\n  'use strict';\n\n  //! moment.js locale configuration\n  var itCh = moment.defineLocale('it-ch', {\n    months: 'gennaio_febbraio_marzo_aprile_maggio_giugno_luglio_agosto_settembre_ottobre_novembre_dicembre'.split('_'),\n    monthsShort: 'gen_feb_mar_apr_mag_giu_lug_ago_set_ott_nov_dic'.split('_'),\n    weekdays: 'domenica_lunedì_martedì_mercoledì_giovedì_venerdì_sabato'.split('_'),\n    weekdaysShort: 'dom_lun_mar_mer_gio_ven_sab'.split('_'),\n    weekdaysMin: 'do_lu_ma_me_gi_ve_sa'.split('_'),\n    longDateFormat: {\n      LT: 'HH:mm',\n      LTS: 'HH:mm:ss',\n      L: 'DD.MM.YYYY',\n      LL: 'D MMMM YYYY',\n      LLL: 'D MMMM YYYY HH:mm',\n      LLLL: 'dddd D MMMM YYYY HH:mm'\n    },\n    calendar: {\n      sameDay: '[Oggi alle] LT',\n      nextDay: '[Domani alle] LT',\n      nextWeek: 'dddd [alle] LT',\n      lastDay: '[Ieri alle] LT',\n      lastWeek: function () {\n        switch (this.day()) {\n          case 0:\n            return '[la scorsa] dddd [alle] LT';\n          default:\n            return '[lo scorso] dddd [alle] LT';\n        }\n      },\n      sameElse: 'L'\n    },\n    relativeTime: {\n      future: function (s) {\n        return (/^[0-9].+$/.test(s) ? 'tra' : 'in') + ' ' + s;\n      },\n      past: '%s fa',\n      s: 'alcuni secondi',\n      ss: '%d secondi',\n      m: 'un minuto',\n      mm: '%d minuti',\n      h: \"un'ora\",\n      hh: '%d ore',\n      d: 'un giorno',\n      dd: '%d giorni',\n      M: 'un mese',\n      MM: '%d mesi',\n      y: 'un anno',\n      yy: '%d anni'\n    },\n    dayOfMonthOrdinalParse: /\\d{1,2}º/,\n    ordinal: '%dº',\n    week: {\n      dow: 1,\n      // Monday is the first day of the week.\n      doy: 4 // The week that contains Jan 4th is the first week of the year.\n    }\n  });\n\n  return itCh;\n});", "map": {"version": 3, "names": ["global", "factory", "exports", "module", "require", "define", "amd", "moment", "itCh", "defineLocale", "months", "split", "monthsShort", "weekdays", "weekdaysShort", "weekdaysMin", "longDateFormat", "LT", "LTS", "L", "LL", "LLL", "LLLL", "calendar", "sameDay", "nextDay", "nextWeek", "lastDay", "lastWeek", "day", "same<PERSON><PERSON><PERSON>", "relativeTime", "future", "s", "test", "past", "ss", "m", "mm", "h", "hh", "d", "dd", "M", "MM", "y", "yy", "dayOfMonthOrdinalParse", "ordinal", "week", "dow", "doy"], "sources": ["C:/WolfSystemV2/WolfOldFrontEnd/node_modules/moment/locale/it-ch.js"], "sourcesContent": ["//! moment.js locale configuration\n//! locale : Italian (Switzerland) [it-ch]\n//! author : xfh : https://github.com/xfh\n\n;(function (global, factory) {\n   typeof exports === 'object' && typeof module !== 'undefined'\n       && typeof require === 'function' ? factory(require('../moment')) :\n   typeof define === 'function' && define.amd ? define(['../moment'], factory) :\n   factory(global.moment)\n}(this, (function (moment) { 'use strict';\n\n    //! moment.js locale configuration\n\n    var itCh = moment.defineLocale('it-ch', {\n        months: 'gennaio_febbraio_marzo_aprile_maggio_giugno_luglio_agosto_settembre_ottobre_novembre_dicembre'.split(\n            '_'\n        ),\n        monthsShort: 'gen_feb_mar_apr_mag_giu_lug_ago_set_ott_nov_dic'.split('_'),\n        weekdays: 'domenica_lunedì_martedì_mercoledì_giovedì_venerdì_sabato'.split(\n            '_'\n        ),\n        weekdaysShort: 'dom_lun_mar_mer_gio_ven_sab'.split('_'),\n        weekdaysMin: 'do_lu_ma_me_gi_ve_sa'.split('_'),\n        longDateFormat: {\n            LT: 'HH:mm',\n            LTS: 'HH:mm:ss',\n            L: 'DD.MM.YYYY',\n            LL: 'D MMMM YYYY',\n            LLL: 'D MMMM YYYY HH:mm',\n            LLLL: 'dddd D MMMM YYYY HH:mm',\n        },\n        calendar: {\n            sameDay: '[Oggi alle] LT',\n            nextDay: '[Domani alle] LT',\n            nextWeek: 'dddd [alle] LT',\n            lastDay: '[Ieri alle] LT',\n            lastWeek: function () {\n                switch (this.day()) {\n                    case 0:\n                        return '[la scorsa] dddd [alle] LT';\n                    default:\n                        return '[lo scorso] dddd [alle] LT';\n                }\n            },\n            sameElse: 'L',\n        },\n        relativeTime: {\n            future: function (s) {\n                return (/^[0-9].+$/.test(s) ? 'tra' : 'in') + ' ' + s;\n            },\n            past: '%s fa',\n            s: 'alcuni secondi',\n            ss: '%d secondi',\n            m: 'un minuto',\n            mm: '%d minuti',\n            h: \"un'ora\",\n            hh: '%d ore',\n            d: 'un giorno',\n            dd: '%d giorni',\n            M: 'un mese',\n            MM: '%d mesi',\n            y: 'un anno',\n            yy: '%d anni',\n        },\n        dayOfMonthOrdinalParse: /\\d{1,2}º/,\n        ordinal: '%dº',\n        week: {\n            dow: 1, // Monday is the first day of the week.\n            doy: 4, // The week that contains Jan 4th is the first week of the year.\n        },\n    });\n\n    return itCh;\n\n})));\n"], "mappings": "AAAA;AACA;AACA;;AAEA;AAAE,WAAUA,MAAM,EAAEC,OAAO,EAAE;EAC1B,OAAOC,OAAO,KAAK,QAAQ,IAAI,OAAOC,MAAM,KAAK,WAAW,IACrD,OAAOC,OAAO,KAAK,UAAU,GAAGH,OAAO,CAACG,OAAO,CAAC,WAAW,CAAC,CAAC,GACpE,OAAOC,MAAM,KAAK,UAAU,IAAIA,MAAM,CAACC,GAAG,GAAGD,MAAM,CAAC,CAAC,WAAW,CAAC,EAAEJ,OAAO,CAAC,GAC3EA,OAAO,CAACD,MAAM,CAACO,MAAM,CAAC;AACzB,CAAC,EAAC,IAAI,EAAG,UAAUA,MAAM,EAAE;EAAE,YAAY;;EAErC;EAEA,IAAIC,IAAI,GAAGD,MAAM,CAACE,YAAY,CAAC,OAAO,EAAE;IACpCC,MAAM,EAAE,+FAA+F,CAACC,KAAK,CACzG,GACJ,CAAC;IACDC,WAAW,EAAE,iDAAiD,CAACD,KAAK,CAAC,GAAG,CAAC;IACzEE,QAAQ,EAAE,0DAA0D,CAACF,KAAK,CACtE,GACJ,CAAC;IACDG,aAAa,EAAE,6BAA6B,CAACH,KAAK,CAAC,GAAG,CAAC;IACvDI,WAAW,EAAE,sBAAsB,CAACJ,KAAK,CAAC,GAAG,CAAC;IAC9CK,cAAc,EAAE;MACZC,EAAE,EAAE,OAAO;MACXC,GAAG,EAAE,UAAU;MACfC,CAAC,EAAE,YAAY;MACfC,EAAE,EAAE,aAAa;MACjBC,GAAG,EAAE,mBAAmB;MACxBC,IAAI,EAAE;IACV,CAAC;IACDC,QAAQ,EAAE;MACNC,OAAO,EAAE,gBAAgB;MACzBC,OAAO,EAAE,kBAAkB;MAC3BC,QAAQ,EAAE,gBAAgB;MAC1BC,OAAO,EAAE,gBAAgB;MACzBC,QAAQ,EAAE,SAAAA,CAAA,EAAY;QAClB,QAAQ,IAAI,CAACC,GAAG,CAAC,CAAC;UACd,KAAK,CAAC;YACF,OAAO,4BAA4B;UACvC;YACI,OAAO,4BAA4B;QAC3C;MACJ,CAAC;MACDC,QAAQ,EAAE;IACd,CAAC;IACDC,YAAY,EAAE;MACVC,MAAM,EAAE,SAAAA,CAAUC,CAAC,EAAE;QACjB,OAAO,CAAC,WAAW,CAACC,IAAI,CAACD,CAAC,CAAC,GAAG,KAAK,GAAG,IAAI,IAAI,GAAG,GAAGA,CAAC;MACzD,CAAC;MACDE,IAAI,EAAE,OAAO;MACbF,CAAC,EAAE,gBAAgB;MACnBG,EAAE,EAAE,YAAY;MAChBC,CAAC,EAAE,WAAW;MACdC,EAAE,EAAE,WAAW;MACfC,CAAC,EAAE,QAAQ;MACXC,EAAE,EAAE,QAAQ;MACZC,CAAC,EAAE,WAAW;MACdC,EAAE,EAAE,WAAW;MACfC,CAAC,EAAE,SAAS;MACZC,EAAE,EAAE,SAAS;MACbC,CAAC,EAAE,SAAS;MACZC,EAAE,EAAE;IACR,CAAC;IACDC,sBAAsB,EAAE,UAAU;IAClCC,OAAO,EAAE,KAAK;IACdC,IAAI,EAAE;MACFC,GAAG,EAAE,CAAC;MAAE;MACRC,GAAG,EAAE,CAAC,CAAE;IACZ;EACJ,CAAC,CAAC;;EAEF,OAAO3C,IAAI;AAEf,CAAE,CAAC"}, "metadata": {}, "sourceType": "script", "externalDependencies": []}