.ban-management-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.ban-input-section {
  padding-bottom: 15px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

.input-group {
  display: flex;
  gap: 10px;
  align-items: center;
}

.ip-input {
  flex: 1;
  padding: 10px 15px;
  background: rgba(255, 255, 255, 0.1);
  border: 2px solid rgba(78, 205, 196, 0.3);
  border-radius: 25px;
  color: white;
  font-size: 0.9rem;
  font-family: 'Courier New', monospace;
  transition: all 0.3s ease;

  &::placeholder {
    color: rgba(255, 255, 255, 0.5);
  }

  &:focus {
    outline: none;
    border-color: #4ecdc4;
    background: rgba(255, 255, 255, 0.15);
    box-shadow: 0 0 15px rgba(78, 205, 196, 0.3);
  }

  &:invalid {
    border-color: #ff6b6b;
  }
}

.ban-button {
  padding: 10px 20px;
  background: linear-gradient(135deg, #ff6b6b, #ff5252);
  border: none;
  border-radius: 25px;
  color: white;
  font-weight: bold;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.3s ease;
  text-transform: uppercase;
  letter-spacing: 1px;
  box-shadow: 0 4px 15px rgba(255, 107, 107, 0.3);

  &:hover:not(:disabled) {
    background: linear-gradient(135deg, #ff5252, #f44336);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(255, 107, 107, 0.4);
  }

  &:active:not(:disabled) {
    transform: translateY(0);
  }

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
  }
}

.banned-list {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.list-header {
  margin-bottom: 10px;
}

.banned-count {
  color: #ff6b6b;
  font-weight: 600;
  font-size: 0.9rem;
  background: rgba(255, 107, 107, 0.2);
  padding: 4px 12px;
  border-radius: 15px;
  border: 1px solid rgba(255, 107, 107, 0.3);
}

.banned-items {
  flex: 1;
  overflow-y: auto;
  padding-right: 8px;

  /* Custom scrollbar */
  &::-webkit-scrollbar {
    width: 6px;
  }
  
  &::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 3px;
  }
  
  &::-webkit-scrollbar-thumb {
    background: rgba(255, 107, 107, 0.5);
    border-radius: 3px;
    
    &:hover {
      background: rgba(255, 107, 107, 0.7);
    }
  }
}

.banned-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  margin-bottom: 8px;
  background: rgba(255, 107, 107, 0.1);
  border: 1px solid rgba(255, 107, 107, 0.3);
  border-radius: 10px;
  transition: all 0.3s ease;
  animation: slideInLeft 0.5s ease;

  &:hover {
    background: rgba(255, 107, 107, 0.15);
    transform: translateX(3px);
    box-shadow: 0 4px 15px rgba(255, 107, 107, 0.2);
  }
}

.banned-info {
  display: flex;
  flex-direction: column;
  gap: 6px;
  flex: 1;
}

.banned-ip {
  font-family: 'Courier New', monospace;
  font-weight: bold;
  font-size: 1rem;
  color: #ff6b6b;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
}

.banned-details {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 0.8rem;
}

.banned-flag {
  font-size: 1rem;
  filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.3));
}

.banned-country {
  color: #ccc;
  font-weight: 500;
}

.banned-attempts {
  color: #ffa726;
  font-weight: 600;
  background: rgba(255, 167, 38, 0.2);
  padding: 2px 8px;
  border-radius: 10px;
  border: 1px solid rgba(255, 167, 38, 0.3);
}

.unban-button {
  padding: 6px 15px;
  background: linear-gradient(135deg, #4ecdc4, #44a08d);
  border: none;
  border-radius: 20px;
  color: white;
  font-weight: bold;
  font-size: 0.8rem;
  cursor: pointer;
  transition: all 0.3s ease;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  box-shadow: 0 2px 8px rgba(78, 205, 196, 0.3);

  &:hover {
    background: linear-gradient(135deg, #44a08d, #26a69a);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(78, 205, 196, 0.4);
  }

  &:active {
    transform: translateY(0);
  }
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  flex: 1;
  color: rgba(255, 255, 255, 0.6);
  text-align: center;
}

.empty-icon {
  font-size: 3rem;
  margin-bottom: 10px;
  opacity: 0.7;
}

.empty-text {
  font-size: 1rem;
  font-weight: 500;
}

.ban-stats {
  display: flex;
  justify-content: space-around;
  padding-top: 15px;
  border-top: 1px solid rgba(255, 255, 255, 0.2);
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
}

.stat-value {
  font-size: 1.5rem;
  font-weight: bold;
  color: #ff6b6b;
  text-shadow: 0 0 10px rgba(255, 107, 107, 0.5);
}

.stat-label {
  font-size: 0.8rem;
  color: #ccc;
  text-transform: uppercase;
  letter-spacing: 1px;
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Responsividade */
@media (max-width: 768px) {
  .input-group {
    flex-direction: column;
    gap: 8px;
  }
  
  .ip-input {
    width: 100%;
  }
  
  .ban-button {
    width: 100%;
    padding: 12px;
  }
  
  .banned-item {
    flex-direction: column;
    gap: 10px;
    align-items: flex-start;
  }
  
  .banned-info {
    width: 100%;
  }
  
  .unban-button {
    align-self: flex-end;
  }
}
