{"ast": null, "code": "import Eventful from './Eventful.js';\nimport env from './env.js';\nimport { isCanvasEl, transformCoordWithViewport } from './dom.js';\nvar MOUSE_EVENT_REG = /^(?:mouse|pointer|contextmenu|drag|drop)|click/;\nvar _calcOut = [];\nvar firefoxNotSupportOffsetXY = env.browser.firefox && +env.browser.version.split('.')[0] < 39;\nexport function clientToLocal(el, e, out, calculate) {\n  out = out || {};\n  if (calculate) {\n    calculateZrXY(el, e, out);\n  } else if (firefoxNotSupportOffsetXY && e.layerX != null && e.layerX !== e.offsetX) {\n    out.zrX = e.layerX;\n    out.zrY = e.layerY;\n  } else if (e.offsetX != null) {\n    out.zrX = e.offsetX;\n    out.zrY = e.offsetY;\n  } else {\n    calculateZrXY(el, e, out);\n  }\n  return out;\n}\nfunction calculateZrXY(el, e, out) {\n  if (env.domSupported && el.getBoundingClientRect) {\n    var ex = e.clientX;\n    var ey = e.clientY;\n    if (isCanvasEl(el)) {\n      var box = el.getBoundingClientRect();\n      out.zrX = ex - box.left;\n      out.zrY = ey - box.top;\n      return;\n    } else {\n      if (transformCoordWithViewport(_calcOut, el, ex, ey)) {\n        out.zrX = _calcOut[0];\n        out.zrY = _calcOut[1];\n        return;\n      }\n    }\n  }\n  out.zrX = out.zrY = 0;\n}\nexport function getNativeEvent(e) {\n  return e || window.event;\n}\nexport function normalizeEvent(el, e, calculate) {\n  e = getNativeEvent(e);\n  if (e.zrX != null) {\n    return e;\n  }\n  var eventType = e.type;\n  var isTouch = eventType && eventType.indexOf('touch') >= 0;\n  if (!isTouch) {\n    clientToLocal(el, e, e, calculate);\n    var wheelDelta = getWheelDeltaMayPolyfill(e);\n    e.zrDelta = wheelDelta ? wheelDelta / 120 : -(e.detail || 0) / 3;\n  } else {\n    var touch = eventType !== 'touchend' ? e.targetTouches[0] : e.changedTouches[0];\n    touch && clientToLocal(el, touch, e, calculate);\n  }\n  var button = e.button;\n  if (e.which == null && button !== undefined && MOUSE_EVENT_REG.test(e.type)) {\n    e.which = button & 1 ? 1 : button & 2 ? 3 : button & 4 ? 2 : 0;\n  }\n  return e;\n}\nfunction getWheelDeltaMayPolyfill(e) {\n  var rawWheelDelta = e.wheelDelta;\n  if (rawWheelDelta) {\n    return rawWheelDelta;\n  }\n  var deltaX = e.deltaX;\n  var deltaY = e.deltaY;\n  if (deltaX == null || deltaY == null) {\n    return rawWheelDelta;\n  }\n  var delta = deltaY !== 0 ? Math.abs(deltaY) : Math.abs(deltaX);\n  var sign = deltaY > 0 ? -1 : deltaY < 0 ? 1 : deltaX > 0 ? -1 : 1;\n  return 3 * delta * sign;\n}\nexport function addEventListener(el, name, handler, opt) {\n  el.addEventListener(name, handler, opt);\n}\nexport function removeEventListener(el, name, handler, opt) {\n  el.removeEventListener(name, handler, opt);\n}\nexport var stop = function (e) {\n  e.preventDefault();\n  e.stopPropagation();\n  e.cancelBubble = true;\n};\nexport function isMiddleOrRightButtonOnMouseUpDown(e) {\n  return e.which === 2 || e.which === 3;\n}\nexport { Eventful as Dispatcher };", "map": {"version": 3, "names": ["Eventful", "env", "isCanvasEl", "transformCoordWithViewport", "MOUSE_EVENT_REG", "_calcOut", "firefoxNotSupportOffsetXY", "browser", "firefox", "version", "split", "clientToLocal", "el", "e", "out", "calculate", "calculateZrXY", "layerX", "offsetX", "zrX", "zrY", "layerY", "offsetY", "domSupported", "getBoundingClientRect", "ex", "clientX", "ey", "clientY", "box", "left", "top", "getNativeEvent", "window", "event", "normalizeEvent", "eventType", "type", "is<PERSON><PERSON>ch", "indexOf", "wheelDelta", "getWheelDeltaMayPolyfill", "zrDelta", "detail", "touch", "targetTouches", "changedTouches", "button", "which", "undefined", "test", "rawWheelDelta", "deltaX", "deltaY", "delta", "Math", "abs", "sign", "addEventListener", "name", "handler", "opt", "removeEventListener", "stop", "preventDefault", "stopPropagation", "cancelBubble", "isMiddleOrRightButtonOnMouseUpDown", "Di<PERSON>atcher"], "sources": ["C:/WolfSystemV2/WolfOldFrontEnd/node_modules/zrender/lib/core/event.js"], "sourcesContent": ["import Eventful from './Eventful.js';\nimport env from './env.js';\nimport { isCanvasEl, transformCoordWithViewport } from './dom.js';\nvar MOUSE_EVENT_REG = /^(?:mouse|pointer|contextmenu|drag|drop)|click/;\nvar _calcOut = [];\nvar firefoxNotSupportOffsetXY = env.browser.firefox\n    && +env.browser.version.split('.')[0] < 39;\nexport function clientToLocal(el, e, out, calculate) {\n    out = out || {};\n    if (calculate) {\n        calculateZrXY(el, e, out);\n    }\n    else if (firefoxNotSupportOffsetXY\n        && e.layerX != null\n        && e.layerX !== e.offsetX) {\n        out.zrX = e.layerX;\n        out.zrY = e.layerY;\n    }\n    else if (e.offsetX != null) {\n        out.zrX = e.offsetX;\n        out.zrY = e.offsetY;\n    }\n    else {\n        calculateZrXY(el, e, out);\n    }\n    return out;\n}\nfunction calculateZrXY(el, e, out) {\n    if (env.domSupported && el.getBoundingClientRect) {\n        var ex = e.clientX;\n        var ey = e.clientY;\n        if (isCanvasEl(el)) {\n            var box = el.getBoundingClientRect();\n            out.zrX = ex - box.left;\n            out.zrY = ey - box.top;\n            return;\n        }\n        else {\n            if (transformCoordWithViewport(_calcOut, el, ex, ey)) {\n                out.zrX = _calcOut[0];\n                out.zrY = _calcOut[1];\n                return;\n            }\n        }\n    }\n    out.zrX = out.zrY = 0;\n}\nexport function getNativeEvent(e) {\n    return e\n        || window.event;\n}\nexport function normalizeEvent(el, e, calculate) {\n    e = getNativeEvent(e);\n    if (e.zrX != null) {\n        return e;\n    }\n    var eventType = e.type;\n    var isTouch = eventType && eventType.indexOf('touch') >= 0;\n    if (!isTouch) {\n        clientToLocal(el, e, e, calculate);\n        var wheelDelta = getWheelDeltaMayPolyfill(e);\n        e.zrDelta = wheelDelta ? wheelDelta / 120 : -(e.detail || 0) / 3;\n    }\n    else {\n        var touch = eventType !== 'touchend'\n            ? e.targetTouches[0]\n            : e.changedTouches[0];\n        touch && clientToLocal(el, touch, e, calculate);\n    }\n    var button = e.button;\n    if (e.which == null && button !== undefined && MOUSE_EVENT_REG.test(e.type)) {\n        e.which = (button & 1 ? 1 : (button & 2 ? 3 : (button & 4 ? 2 : 0)));\n    }\n    return e;\n}\nfunction getWheelDeltaMayPolyfill(e) {\n    var rawWheelDelta = e.wheelDelta;\n    if (rawWheelDelta) {\n        return rawWheelDelta;\n    }\n    var deltaX = e.deltaX;\n    var deltaY = e.deltaY;\n    if (deltaX == null || deltaY == null) {\n        return rawWheelDelta;\n    }\n    var delta = deltaY !== 0 ? Math.abs(deltaY) : Math.abs(deltaX);\n    var sign = deltaY > 0 ? -1\n        : deltaY < 0 ? 1\n            : deltaX > 0 ? -1\n                : 1;\n    return 3 * delta * sign;\n}\nexport function addEventListener(el, name, handler, opt) {\n    el.addEventListener(name, handler, opt);\n}\nexport function removeEventListener(el, name, handler, opt) {\n    el.removeEventListener(name, handler, opt);\n}\nexport var stop = function (e) {\n    e.preventDefault();\n    e.stopPropagation();\n    e.cancelBubble = true;\n};\nexport function isMiddleOrRightButtonOnMouseUpDown(e) {\n    return e.which === 2 || e.which === 3;\n}\nexport { Eventful as Dispatcher };\n"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,eAAe;AACpC,OAAOC,GAAG,MAAM,UAAU;AAC1B,SAASC,UAAU,EAAEC,0BAA0B,QAAQ,UAAU;AACjE,IAAIC,eAAe,GAAG,gDAAgD;AACtE,IAAIC,QAAQ,GAAG,EAAE;AACjB,IAAIC,yBAAyB,GAAGL,GAAG,CAACM,OAAO,CAACC,OAAO,IAC5C,CAACP,GAAG,CAACM,OAAO,CAACE,OAAO,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE;AAC9C,OAAO,SAASC,aAAaA,CAACC,EAAE,EAAEC,CAAC,EAAEC,GAAG,EAAEC,SAAS,EAAE;EACjDD,GAAG,GAAGA,GAAG,IAAI,CAAC,CAAC;EACf,IAAIC,SAAS,EAAE;IACXC,aAAa,CAACJ,EAAE,EAAEC,CAAC,EAAEC,GAAG,CAAC;EAC7B,CAAC,MACI,IAAIR,yBAAyB,IAC3BO,CAAC,CAACI,MAAM,IAAI,IAAI,IAChBJ,CAAC,CAACI,MAAM,KAAKJ,CAAC,CAACK,OAAO,EAAE;IAC3BJ,GAAG,CAACK,GAAG,GAAGN,CAAC,CAACI,MAAM;IAClBH,GAAG,CAACM,GAAG,GAAGP,CAAC,CAACQ,MAAM;EACtB,CAAC,MACI,IAAIR,CAAC,CAACK,OAAO,IAAI,IAAI,EAAE;IACxBJ,GAAG,CAACK,GAAG,GAAGN,CAAC,CAACK,OAAO;IACnBJ,GAAG,CAACM,GAAG,GAAGP,CAAC,CAACS,OAAO;EACvB,CAAC,MACI;IACDN,aAAa,CAACJ,EAAE,EAAEC,CAAC,EAAEC,GAAG,CAAC;EAC7B;EACA,OAAOA,GAAG;AACd;AACA,SAASE,aAAaA,CAACJ,EAAE,EAAEC,CAAC,EAAEC,GAAG,EAAE;EAC/B,IAAIb,GAAG,CAACsB,YAAY,IAAIX,EAAE,CAACY,qBAAqB,EAAE;IAC9C,IAAIC,EAAE,GAAGZ,CAAC,CAACa,OAAO;IAClB,IAAIC,EAAE,GAAGd,CAAC,CAACe,OAAO;IAClB,IAAI1B,UAAU,CAACU,EAAE,CAAC,EAAE;MAChB,IAAIiB,GAAG,GAAGjB,EAAE,CAACY,qBAAqB,CAAC,CAAC;MACpCV,GAAG,CAACK,GAAG,GAAGM,EAAE,GAAGI,GAAG,CAACC,IAAI;MACvBhB,GAAG,CAACM,GAAG,GAAGO,EAAE,GAAGE,GAAG,CAACE,GAAG;MACtB;IACJ,CAAC,MACI;MACD,IAAI5B,0BAA0B,CAACE,QAAQ,EAAEO,EAAE,EAAEa,EAAE,EAAEE,EAAE,CAAC,EAAE;QAClDb,GAAG,CAACK,GAAG,GAAGd,QAAQ,CAAC,CAAC,CAAC;QACrBS,GAAG,CAACM,GAAG,GAAGf,QAAQ,CAAC,CAAC,CAAC;QACrB;MACJ;IACJ;EACJ;EACAS,GAAG,CAACK,GAAG,GAAGL,GAAG,CAACM,GAAG,GAAG,CAAC;AACzB;AACA,OAAO,SAASY,cAAcA,CAACnB,CAAC,EAAE;EAC9B,OAAOA,CAAC,IACDoB,MAAM,CAACC,KAAK;AACvB;AACA,OAAO,SAASC,cAAcA,CAACvB,EAAE,EAAEC,CAAC,EAAEE,SAAS,EAAE;EAC7CF,CAAC,GAAGmB,cAAc,CAACnB,CAAC,CAAC;EACrB,IAAIA,CAAC,CAACM,GAAG,IAAI,IAAI,EAAE;IACf,OAAON,CAAC;EACZ;EACA,IAAIuB,SAAS,GAAGvB,CAAC,CAACwB,IAAI;EACtB,IAAIC,OAAO,GAAGF,SAAS,IAAIA,SAAS,CAACG,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC;EAC1D,IAAI,CAACD,OAAO,EAAE;IACV3B,aAAa,CAACC,EAAE,EAAEC,CAAC,EAAEA,CAAC,EAAEE,SAAS,CAAC;IAClC,IAAIyB,UAAU,GAAGC,wBAAwB,CAAC5B,CAAC,CAAC;IAC5CA,CAAC,CAAC6B,OAAO,GAAGF,UAAU,GAAGA,UAAU,GAAG,GAAG,GAAG,EAAE3B,CAAC,CAAC8B,MAAM,IAAI,CAAC,CAAC,GAAG,CAAC;EACpE,CAAC,MACI;IACD,IAAIC,KAAK,GAAGR,SAAS,KAAK,UAAU,GAC9BvB,CAAC,CAACgC,aAAa,CAAC,CAAC,CAAC,GAClBhC,CAAC,CAACiC,cAAc,CAAC,CAAC,CAAC;IACzBF,KAAK,IAAIjC,aAAa,CAACC,EAAE,EAAEgC,KAAK,EAAE/B,CAAC,EAAEE,SAAS,CAAC;EACnD;EACA,IAAIgC,MAAM,GAAGlC,CAAC,CAACkC,MAAM;EACrB,IAAIlC,CAAC,CAACmC,KAAK,IAAI,IAAI,IAAID,MAAM,KAAKE,SAAS,IAAI7C,eAAe,CAAC8C,IAAI,CAACrC,CAAC,CAACwB,IAAI,CAAC,EAAE;IACzExB,CAAC,CAACmC,KAAK,GAAID,MAAM,GAAG,CAAC,GAAG,CAAC,GAAIA,MAAM,GAAG,CAAC,GAAG,CAAC,GAAIA,MAAM,GAAG,CAAC,GAAG,CAAC,GAAG,CAAI;EACxE;EACA,OAAOlC,CAAC;AACZ;AACA,SAAS4B,wBAAwBA,CAAC5B,CAAC,EAAE;EACjC,IAAIsC,aAAa,GAAGtC,CAAC,CAAC2B,UAAU;EAChC,IAAIW,aAAa,EAAE;IACf,OAAOA,aAAa;EACxB;EACA,IAAIC,MAAM,GAAGvC,CAAC,CAACuC,MAAM;EACrB,IAAIC,MAAM,GAAGxC,CAAC,CAACwC,MAAM;EACrB,IAAID,MAAM,IAAI,IAAI,IAAIC,MAAM,IAAI,IAAI,EAAE;IAClC,OAAOF,aAAa;EACxB;EACA,IAAIG,KAAK,GAAGD,MAAM,KAAK,CAAC,GAAGE,IAAI,CAACC,GAAG,CAACH,MAAM,CAAC,GAAGE,IAAI,CAACC,GAAG,CAACJ,MAAM,CAAC;EAC9D,IAAIK,IAAI,GAAGJ,MAAM,GAAG,CAAC,GAAG,CAAC,CAAC,GACpBA,MAAM,GAAG,CAAC,GAAG,CAAC,GACVD,MAAM,GAAG,CAAC,GAAG,CAAC,CAAC,GACX,CAAC;EACf,OAAO,CAAC,GAAGE,KAAK,GAAGG,IAAI;AAC3B;AACA,OAAO,SAASC,gBAAgBA,CAAC9C,EAAE,EAAE+C,IAAI,EAAEC,OAAO,EAAEC,GAAG,EAAE;EACrDjD,EAAE,CAAC8C,gBAAgB,CAACC,IAAI,EAAEC,OAAO,EAAEC,GAAG,CAAC;AAC3C;AACA,OAAO,SAASC,mBAAmBA,CAAClD,EAAE,EAAE+C,IAAI,EAAEC,OAAO,EAAEC,GAAG,EAAE;EACxDjD,EAAE,CAACkD,mBAAmB,CAACH,IAAI,EAAEC,OAAO,EAAEC,GAAG,CAAC;AAC9C;AACA,OAAO,IAAIE,IAAI,GAAG,SAAAA,CAAUlD,CAAC,EAAE;EAC3BA,CAAC,CAACmD,cAAc,CAAC,CAAC;EAClBnD,CAAC,CAACoD,eAAe,CAAC,CAAC;EACnBpD,CAAC,CAACqD,YAAY,GAAG,IAAI;AACzB,CAAC;AACD,OAAO,SAASC,kCAAkCA,CAACtD,CAAC,EAAE;EAClD,OAAOA,CAAC,CAACmC,KAAK,KAAK,CAAC,IAAInC,CAAC,CAACmC,KAAK,KAAK,CAAC;AACzC;AACA,SAAShD,QAAQ,IAAIoE,UAAU"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}