{"ast": null, "code": "//! moment.js locale configuration\n//! locale : Uzbek [uz]\n//! author : <PERSON><PERSON><PERSON> : https://github.com/muminoff\n\n;\n(function (global, factory) {\n  typeof exports === 'object' && typeof module !== 'undefined' && typeof require === 'function' ? factory(require('../moment')) : typeof define === 'function' && define.amd ? define(['../moment'], factory) : factory(global.moment);\n})(this, function (moment) {\n  'use strict';\n\n  //! moment.js locale configuration\n  var uz = moment.defineLocale('uz', {\n    months: 'январ_феврал_март_апрел_май_июн_июл_август_сентябр_октябр_ноябр_декабр'.split('_'),\n    monthsShort: 'янв_фев_мар_апр_май_июн_июл_авг_сен_окт_ноя_дек'.split('_'),\n    weekdays: 'Якшанба_Душанба_Сешанба_Чоршанба_Пайшанба_Жума_Шанба'.split('_'),\n    weekdaysShort: 'Якш_Душ_Сеш_Чор_Пай_Жум_Шан'.split('_'),\n    weekdaysMin: 'Як_Ду_Се_Чо_Па_Жу_Ша'.split('_'),\n    longDateFormat: {\n      LT: 'HH:mm',\n      LTS: 'HH:mm:ss',\n      L: 'DD/MM/YYYY',\n      LL: 'D MMMM YYYY',\n      LLL: 'D MMMM YYYY HH:mm',\n      LLLL: 'D MMMM YYYY, dddd HH:mm'\n    },\n    calendar: {\n      sameDay: '[Бугун соат] LT [да]',\n      nextDay: '[Эртага] LT [да]',\n      nextWeek: 'dddd [куни соат] LT [да]',\n      lastDay: '[Кеча соат] LT [да]',\n      lastWeek: '[Утган] dddd [куни соат] LT [да]',\n      sameElse: 'L'\n    },\n    relativeTime: {\n      future: 'Якин %s ичида',\n      past: 'Бир неча %s олдин',\n      s: 'фурсат',\n      ss: '%d фурсат',\n      m: 'бир дакика',\n      mm: '%d дакика',\n      h: 'бир соат',\n      hh: '%d соат',\n      d: 'бир кун',\n      dd: '%d кун',\n      M: 'бир ой',\n      MM: '%d ой',\n      y: 'бир йил',\n      yy: '%d йил'\n    },\n    week: {\n      dow: 1,\n      // Monday is the first day of the week.\n      doy: 7 // The week that contains Jan 4th is the first week of the year.\n    }\n  });\n\n  return uz;\n});", "map": {"version": 3, "names": ["global", "factory", "exports", "module", "require", "define", "amd", "moment", "uz", "defineLocale", "months", "split", "monthsShort", "weekdays", "weekdaysShort", "weekdaysMin", "longDateFormat", "LT", "LTS", "L", "LL", "LLL", "LLLL", "calendar", "sameDay", "nextDay", "nextWeek", "lastDay", "lastWeek", "same<PERSON><PERSON><PERSON>", "relativeTime", "future", "past", "s", "ss", "m", "mm", "h", "hh", "d", "dd", "M", "MM", "y", "yy", "week", "dow", "doy"], "sources": ["C:/WolfSystemV2/WolfOldFrontEnd/node_modules/moment/locale/uz.js"], "sourcesContent": ["//! moment.js locale configuration\n//! locale : Uzbek [uz]\n//! author : <PERSON><PERSON><PERSON> : https://github.com/muminoff\n\n;(function (global, factory) {\n   typeof exports === 'object' && typeof module !== 'undefined'\n       && typeof require === 'function' ? factory(require('../moment')) :\n   typeof define === 'function' && define.amd ? define(['../moment'], factory) :\n   factory(global.moment)\n}(this, (function (moment) { 'use strict';\n\n    //! moment.js locale configuration\n\n    var uz = moment.defineLocale('uz', {\n        months: 'январ_феврал_март_апрел_май_июн_июл_август_сентябр_октябр_ноябр_декабр'.split(\n            '_'\n        ),\n        monthsShort: 'янв_фев_мар_апр_май_июн_июл_авг_сен_окт_ноя_дек'.split('_'),\n        weekdays: 'Якшанба_Душанба_Сешанба_Чоршанба_Пайшанба_Жума_Шанба'.split('_'),\n        weekdaysShort: 'Якш_Душ_Сеш_Чор_Пай_Жум_Шан'.split('_'),\n        weekdaysMin: 'Як_Ду_Се_Чо_Па_Жу_Ша'.split('_'),\n        longDateFormat: {\n            LT: 'HH:mm',\n            LTS: 'HH:mm:ss',\n            L: 'DD/MM/YYYY',\n            LL: 'D MMMM YYYY',\n            LLL: 'D MMMM YYYY HH:mm',\n            LLLL: 'D MMMM YYYY, dddd HH:mm',\n        },\n        calendar: {\n            sameDay: '[Бугун соат] LT [да]',\n            nextDay: '[Эртага] LT [да]',\n            nextWeek: 'dddd [куни соат] LT [да]',\n            lastDay: '[Кеча соат] LT [да]',\n            lastWeek: '[Утган] dddd [куни соат] LT [да]',\n            sameElse: 'L',\n        },\n        relativeTime: {\n            future: 'Якин %s ичида',\n            past: 'Бир неча %s олдин',\n            s: 'фурсат',\n            ss: '%d фурсат',\n            m: 'бир дакика',\n            mm: '%d дакика',\n            h: 'бир соат',\n            hh: '%d соат',\n            d: 'бир кун',\n            dd: '%d кун',\n            M: 'бир ой',\n            MM: '%d ой',\n            y: 'бир йил',\n            yy: '%d йил',\n        },\n        week: {\n            dow: 1, // Monday is the first day of the week.\n            doy: 7, // The week that contains Jan 4th is the first week of the year.\n        },\n    });\n\n    return uz;\n\n})));\n"], "mappings": "AAAA;AACA;AACA;;AAEA;AAAE,WAAUA,MAAM,EAAEC,OAAO,EAAE;EAC1B,OAAOC,OAAO,KAAK,QAAQ,IAAI,OAAOC,MAAM,KAAK,WAAW,IACrD,OAAOC,OAAO,KAAK,UAAU,GAAGH,OAAO,CAACG,OAAO,CAAC,WAAW,CAAC,CAAC,GACpE,OAAOC,MAAM,KAAK,UAAU,IAAIA,MAAM,CAACC,GAAG,GAAGD,MAAM,CAAC,CAAC,WAAW,CAAC,EAAEJ,OAAO,CAAC,GAC3EA,OAAO,CAACD,MAAM,CAACO,MAAM,CAAC;AACzB,CAAC,EAAC,IAAI,EAAG,UAAUA,MAAM,EAAE;EAAE,YAAY;;EAErC;EAEA,IAAIC,EAAE,GAAGD,MAAM,CAACE,YAAY,CAAC,IAAI,EAAE;IAC/BC,MAAM,EAAE,wEAAwE,CAACC,KAAK,CAClF,GACJ,CAAC;IACDC,WAAW,EAAE,iDAAiD,CAACD,KAAK,CAAC,GAAG,CAAC;IACzEE,QAAQ,EAAE,sDAAsD,CAACF,KAAK,CAAC,GAAG,CAAC;IAC3EG,aAAa,EAAE,6BAA6B,CAACH,KAAK,CAAC,GAAG,CAAC;IACvDI,WAAW,EAAE,sBAAsB,CAACJ,KAAK,CAAC,GAAG,CAAC;IAC9CK,cAAc,EAAE;MACZC,EAAE,EAAE,OAAO;MACXC,GAAG,EAAE,UAAU;MACfC,CAAC,EAAE,YAAY;MACfC,EAAE,EAAE,aAAa;MACjBC,GAAG,EAAE,mBAAmB;MACxBC,IAAI,EAAE;IACV,CAAC;IACDC,QAAQ,EAAE;MACNC,OAAO,EAAE,sBAAsB;MAC/BC,OAAO,EAAE,kBAAkB;MAC3BC,QAAQ,EAAE,0BAA0B;MACpCC,OAAO,EAAE,qBAAqB;MAC9BC,QAAQ,EAAE,kCAAkC;MAC5CC,QAAQ,EAAE;IACd,CAAC;IACDC,YAAY,EAAE;MACVC,MAAM,EAAE,eAAe;MACvBC,IAAI,EAAE,mBAAmB;MACzBC,CAAC,EAAE,QAAQ;MACXC,EAAE,EAAE,WAAW;MACfC,CAAC,EAAE,YAAY;MACfC,EAAE,EAAE,WAAW;MACfC,CAAC,EAAE,UAAU;MACbC,EAAE,EAAE,SAAS;MACbC,CAAC,EAAE,SAAS;MACZC,EAAE,EAAE,QAAQ;MACZC,CAAC,EAAE,QAAQ;MACXC,EAAE,EAAE,OAAO;MACXC,CAAC,EAAE,SAAS;MACZC,EAAE,EAAE;IACR,CAAC;IACDC,IAAI,EAAE;MACFC,GAAG,EAAE,CAAC;MAAE;MACRC,GAAG,EAAE,CAAC,CAAE;IACZ;EACJ,CAAC,CAAC;;EAEF,OAAOvC,EAAE;AAEb,CAAE,CAAC"}, "metadata": {}, "sourceType": "script", "externalDependencies": []}