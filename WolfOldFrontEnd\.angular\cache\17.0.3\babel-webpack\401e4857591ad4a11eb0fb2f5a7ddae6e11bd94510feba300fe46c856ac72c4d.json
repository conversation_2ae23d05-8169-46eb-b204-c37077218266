{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport createSeriesDataSimply from './createSeriesDataSimply.js';\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport { getDimensionTypeByAxis } from '../../data/helper/dimensionHelper.js';\nimport { makeSeriesEncodeForAxisCoordSys } from '../../data/helper/sourceHelper.js';\nvar WhiskerBoxCommonMixin = /** @class */\nfunction () {\n  function WhiskerBoxCommonMixin() {}\n  /**\r\n   * @override\r\n   */\n\n  WhiskerBoxCommonMixin.prototype.getInitialData = function (option, ecModel) {\n    // When both types of xAxis and yAxis are 'value', layout is\n    // needed to be specified by user. Otherwise, layout can be\n    // judged by which axis is category.\n    var ordinalMeta;\n    var xAxisModel = ecModel.getComponent('xAxis', this.get('xAxisIndex'));\n    var yAxisModel = ecModel.getComponent('yAxis', this.get('yAxisIndex'));\n    var xAxisType = xAxisModel.get('type');\n    var yAxisType = yAxisModel.get('type');\n    var addOrdinal; // FIXME\n    // Consider time axis.\n\n    if (xAxisType === 'category') {\n      option.layout = 'horizontal';\n      ordinalMeta = xAxisModel.getOrdinalMeta();\n      addOrdinal = true;\n    } else if (yAxisType === 'category') {\n      option.layout = 'vertical';\n      ordinalMeta = yAxisModel.getOrdinalMeta();\n      addOrdinal = true;\n    } else {\n      option.layout = option.layout || 'horizontal';\n    }\n    var coordDims = ['x', 'y'];\n    var baseAxisDimIndex = option.layout === 'horizontal' ? 0 : 1;\n    var baseAxisDim = this._baseAxisDim = coordDims[baseAxisDimIndex];\n    var otherAxisDim = coordDims[1 - baseAxisDimIndex];\n    var axisModels = [xAxisModel, yAxisModel];\n    var baseAxisType = axisModels[baseAxisDimIndex].get('type');\n    var otherAxisType = axisModels[1 - baseAxisDimIndex].get('type');\n    var data = option.data; // Clone a new data for next setOption({}) usage.\n    // Avoid modifying current data will affect further update.\n\n    if (data && addOrdinal) {\n      var newOptionData_1 = [];\n      zrUtil.each(data, function (item, index) {\n        var newItem;\n        if (zrUtil.isArray(item)) {\n          newItem = item.slice(); // Modify current using data.\n\n          item.unshift(index);\n        } else if (zrUtil.isArray(item.value)) {\n          newItem = zrUtil.extend({}, item);\n          newItem.value = newItem.value.slice(); // Modify current using data.\n\n          item.value.unshift(index);\n        } else {\n          newItem = item;\n        }\n        newOptionData_1.push(newItem);\n      });\n      option.data = newOptionData_1;\n    }\n    var defaultValueDimensions = this.defaultValueDimensions;\n    var coordDimensions = [{\n      name: baseAxisDim,\n      type: getDimensionTypeByAxis(baseAxisType),\n      ordinalMeta: ordinalMeta,\n      otherDims: {\n        tooltip: false,\n        itemName: 0\n      },\n      dimsDef: ['base']\n    }, {\n      name: otherAxisDim,\n      type: getDimensionTypeByAxis(otherAxisType),\n      dimsDef: defaultValueDimensions.slice()\n    }];\n    return createSeriesDataSimply(this, {\n      coordDimensions: coordDimensions,\n      dimensionsCount: defaultValueDimensions.length + 1,\n      encodeDefaulter: zrUtil.curry(makeSeriesEncodeForAxisCoordSys, coordDimensions, this)\n    });\n  };\n  /**\r\n   * If horizontal, base axis is x, otherwise y.\r\n   * @override\r\n   */\n\n  WhiskerBoxCommonMixin.prototype.getBaseAxis = function () {\n    var dim = this._baseAxisDim;\n    return this.ecModel.getComponent(dim + 'Axis', this.get(dim + 'AxisIndex')).axis;\n  };\n  return WhiskerBoxCommonMixin;\n}();\n;\nexport { WhiskerBoxCommonMixin };", "map": {"version": 3, "names": ["createSeriesDataSimply", "zrUtil", "getDimensionTypeByAxis", "makeSeriesEncodeForAxisCoordSys", "WhiskerBoxCommonMixin", "prototype", "getInitialData", "option", "ecModel", "ordinalMeta", "xAxisModel", "getComponent", "get", "yAxisModel", "xAxisType", "yAxisType", "addOrdinal", "layout", "getOrdinalMeta", "coordDims", "baseAxisDimIndex", "baseAxisDim", "_baseAxisDim", "otherAxisDim", "axisModels", "baseAxisType", "otherAxisType", "data", "newOptionData_1", "each", "item", "index", "newItem", "isArray", "slice", "unshift", "value", "extend", "push", "defaultValueDimensions", "coordDimensions", "name", "type", "otherDims", "tooltip", "itemName", "dimsDef", "dimensionsCount", "length", "encodeDefaulter", "curry", "getBaseAxis", "dim", "axis"], "sources": ["C:/WolfSystemV2/WolfOldFrontEnd/node_modules/echarts/lib/chart/helper/whiskerBoxCommon.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport createSeriesDataSimply from './createSeriesDataSimply.js';\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport { getDimensionTypeByAxis } from '../../data/helper/dimensionHelper.js';\nimport { makeSeriesEncodeForAxisCoordSys } from '../../data/helper/sourceHelper.js';\n\nvar WhiskerBoxCommonMixin =\n/** @class */\nfunction () {\n  function WhiskerBoxCommonMixin() {}\n  /**\r\n   * @override\r\n   */\n\n\n  WhiskerBoxCommonMixin.prototype.getInitialData = function (option, ecModel) {\n    // When both types of xAxis and yAxis are 'value', layout is\n    // needed to be specified by user. Otherwise, layout can be\n    // judged by which axis is category.\n    var ordinalMeta;\n    var xAxisModel = ecModel.getComponent('xAxis', this.get('xAxisIndex'));\n    var yAxisModel = ecModel.getComponent('yAxis', this.get('yAxisIndex'));\n    var xAxisType = xAxisModel.get('type');\n    var yAxisType = yAxisModel.get('type');\n    var addOrdinal; // FIXME\n    // Consider time axis.\n\n    if (xAxisType === 'category') {\n      option.layout = 'horizontal';\n      ordinalMeta = xAxisModel.getOrdinalMeta();\n      addOrdinal = true;\n    } else if (yAxisType === 'category') {\n      option.layout = 'vertical';\n      ordinalMeta = yAxisModel.getOrdinalMeta();\n      addOrdinal = true;\n    } else {\n      option.layout = option.layout || 'horizontal';\n    }\n\n    var coordDims = ['x', 'y'];\n    var baseAxisDimIndex = option.layout === 'horizontal' ? 0 : 1;\n    var baseAxisDim = this._baseAxisDim = coordDims[baseAxisDimIndex];\n    var otherAxisDim = coordDims[1 - baseAxisDimIndex];\n    var axisModels = [xAxisModel, yAxisModel];\n    var baseAxisType = axisModels[baseAxisDimIndex].get('type');\n    var otherAxisType = axisModels[1 - baseAxisDimIndex].get('type');\n    var data = option.data; // Clone a new data for next setOption({}) usage.\n    // Avoid modifying current data will affect further update.\n\n    if (data && addOrdinal) {\n      var newOptionData_1 = [];\n      zrUtil.each(data, function (item, index) {\n        var newItem;\n\n        if (zrUtil.isArray(item)) {\n          newItem = item.slice(); // Modify current using data.\n\n          item.unshift(index);\n        } else if (zrUtil.isArray(item.value)) {\n          newItem = zrUtil.extend({}, item);\n          newItem.value = newItem.value.slice(); // Modify current using data.\n\n          item.value.unshift(index);\n        } else {\n          newItem = item;\n        }\n\n        newOptionData_1.push(newItem);\n      });\n      option.data = newOptionData_1;\n    }\n\n    var defaultValueDimensions = this.defaultValueDimensions;\n    var coordDimensions = [{\n      name: baseAxisDim,\n      type: getDimensionTypeByAxis(baseAxisType),\n      ordinalMeta: ordinalMeta,\n      otherDims: {\n        tooltip: false,\n        itemName: 0\n      },\n      dimsDef: ['base']\n    }, {\n      name: otherAxisDim,\n      type: getDimensionTypeByAxis(otherAxisType),\n      dimsDef: defaultValueDimensions.slice()\n    }];\n    return createSeriesDataSimply(this, {\n      coordDimensions: coordDimensions,\n      dimensionsCount: defaultValueDimensions.length + 1,\n      encodeDefaulter: zrUtil.curry(makeSeriesEncodeForAxisCoordSys, coordDimensions, this)\n    });\n  };\n  /**\r\n   * If horizontal, base axis is x, otherwise y.\r\n   * @override\r\n   */\n\n\n  WhiskerBoxCommonMixin.prototype.getBaseAxis = function () {\n    var dim = this._baseAxisDim;\n    return this.ecModel.getComponent(dim + 'Axis', this.get(dim + 'AxisIndex')).axis;\n  };\n\n  return WhiskerBoxCommonMixin;\n}();\n\n;\nexport { WhiskerBoxCommonMixin };"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAOA,sBAAsB,MAAM,6BAA6B;AAChE,OAAO,KAAKC,MAAM,MAAM,0BAA0B;AAClD,SAASC,sBAAsB,QAAQ,sCAAsC;AAC7E,SAASC,+BAA+B,QAAQ,mCAAmC;AAEnF,IAAIC,qBAAqB,GACzB;AACA,YAAY;EACV,SAASA,qBAAqBA,CAAA,EAAG,CAAC;EAClC;AACF;AACA;;EAGEA,qBAAqB,CAACC,SAAS,CAACC,cAAc,GAAG,UAAUC,MAAM,EAAEC,OAAO,EAAE;IAC1E;IACA;IACA;IACA,IAAIC,WAAW;IACf,IAAIC,UAAU,GAAGF,OAAO,CAACG,YAAY,CAAC,OAAO,EAAE,IAAI,CAACC,GAAG,CAAC,YAAY,CAAC,CAAC;IACtE,IAAIC,UAAU,GAAGL,OAAO,CAACG,YAAY,CAAC,OAAO,EAAE,IAAI,CAACC,GAAG,CAAC,YAAY,CAAC,CAAC;IACtE,IAAIE,SAAS,GAAGJ,UAAU,CAACE,GAAG,CAAC,MAAM,CAAC;IACtC,IAAIG,SAAS,GAAGF,UAAU,CAACD,GAAG,CAAC,MAAM,CAAC;IACtC,IAAII,UAAU,CAAC,CAAC;IAChB;;IAEA,IAAIF,SAAS,KAAK,UAAU,EAAE;MAC5BP,MAAM,CAACU,MAAM,GAAG,YAAY;MAC5BR,WAAW,GAAGC,UAAU,CAACQ,cAAc,CAAC,CAAC;MACzCF,UAAU,GAAG,IAAI;IACnB,CAAC,MAAM,IAAID,SAAS,KAAK,UAAU,EAAE;MACnCR,MAAM,CAACU,MAAM,GAAG,UAAU;MAC1BR,WAAW,GAAGI,UAAU,CAACK,cAAc,CAAC,CAAC;MACzCF,UAAU,GAAG,IAAI;IACnB,CAAC,MAAM;MACLT,MAAM,CAACU,MAAM,GAAGV,MAAM,CAACU,MAAM,IAAI,YAAY;IAC/C;IAEA,IAAIE,SAAS,GAAG,CAAC,GAAG,EAAE,GAAG,CAAC;IAC1B,IAAIC,gBAAgB,GAAGb,MAAM,CAACU,MAAM,KAAK,YAAY,GAAG,CAAC,GAAG,CAAC;IAC7D,IAAII,WAAW,GAAG,IAAI,CAACC,YAAY,GAAGH,SAAS,CAACC,gBAAgB,CAAC;IACjE,IAAIG,YAAY,GAAGJ,SAAS,CAAC,CAAC,GAAGC,gBAAgB,CAAC;IAClD,IAAII,UAAU,GAAG,CAACd,UAAU,EAAEG,UAAU,CAAC;IACzC,IAAIY,YAAY,GAAGD,UAAU,CAACJ,gBAAgB,CAAC,CAACR,GAAG,CAAC,MAAM,CAAC;IAC3D,IAAIc,aAAa,GAAGF,UAAU,CAAC,CAAC,GAAGJ,gBAAgB,CAAC,CAACR,GAAG,CAAC,MAAM,CAAC;IAChE,IAAIe,IAAI,GAAGpB,MAAM,CAACoB,IAAI,CAAC,CAAC;IACxB;;IAEA,IAAIA,IAAI,IAAIX,UAAU,EAAE;MACtB,IAAIY,eAAe,GAAG,EAAE;MACxB3B,MAAM,CAAC4B,IAAI,CAACF,IAAI,EAAE,UAAUG,IAAI,EAAEC,KAAK,EAAE;QACvC,IAAIC,OAAO;QAEX,IAAI/B,MAAM,CAACgC,OAAO,CAACH,IAAI,CAAC,EAAE;UACxBE,OAAO,GAAGF,IAAI,CAACI,KAAK,CAAC,CAAC,CAAC,CAAC;;UAExBJ,IAAI,CAACK,OAAO,CAACJ,KAAK,CAAC;QACrB,CAAC,MAAM,IAAI9B,MAAM,CAACgC,OAAO,CAACH,IAAI,CAACM,KAAK,CAAC,EAAE;UACrCJ,OAAO,GAAG/B,MAAM,CAACoC,MAAM,CAAC,CAAC,CAAC,EAAEP,IAAI,CAAC;UACjCE,OAAO,CAACI,KAAK,GAAGJ,OAAO,CAACI,KAAK,CAACF,KAAK,CAAC,CAAC,CAAC,CAAC;;UAEvCJ,IAAI,CAACM,KAAK,CAACD,OAAO,CAACJ,KAAK,CAAC;QAC3B,CAAC,MAAM;UACLC,OAAO,GAAGF,IAAI;QAChB;QAEAF,eAAe,CAACU,IAAI,CAACN,OAAO,CAAC;MAC/B,CAAC,CAAC;MACFzB,MAAM,CAACoB,IAAI,GAAGC,eAAe;IAC/B;IAEA,IAAIW,sBAAsB,GAAG,IAAI,CAACA,sBAAsB;IACxD,IAAIC,eAAe,GAAG,CAAC;MACrBC,IAAI,EAAEpB,WAAW;MACjBqB,IAAI,EAAExC,sBAAsB,CAACuB,YAAY,CAAC;MAC1ChB,WAAW,EAAEA,WAAW;MACxBkC,SAAS,EAAE;QACTC,OAAO,EAAE,KAAK;QACdC,QAAQ,EAAE;MACZ,CAAC;MACDC,OAAO,EAAE,CAAC,MAAM;IAClB,CAAC,EAAE;MACDL,IAAI,EAAElB,YAAY;MAClBmB,IAAI,EAAExC,sBAAsB,CAACwB,aAAa,CAAC;MAC3CoB,OAAO,EAAEP,sBAAsB,CAACL,KAAK,CAAC;IACxC,CAAC,CAAC;IACF,OAAOlC,sBAAsB,CAAC,IAAI,EAAE;MAClCwC,eAAe,EAAEA,eAAe;MAChCO,eAAe,EAAER,sBAAsB,CAACS,MAAM,GAAG,CAAC;MAClDC,eAAe,EAAEhD,MAAM,CAACiD,KAAK,CAAC/C,+BAA+B,EAAEqC,eAAe,EAAE,IAAI;IACtF,CAAC,CAAC;EACJ,CAAC;EACD;AACF;AACA;AACA;;EAGEpC,qBAAqB,CAACC,SAAS,CAAC8C,WAAW,GAAG,YAAY;IACxD,IAAIC,GAAG,GAAG,IAAI,CAAC9B,YAAY;IAC3B,OAAO,IAAI,CAACd,OAAO,CAACG,YAAY,CAACyC,GAAG,GAAG,MAAM,EAAE,IAAI,CAACxC,GAAG,CAACwC,GAAG,GAAG,WAAW,CAAC,CAAC,CAACC,IAAI;EAClF,CAAC;EAED,OAAOjD,qBAAqB;AAC9B,CAAC,CAAC,CAAC;AAEH;AACA,SAASA,qBAAqB"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}