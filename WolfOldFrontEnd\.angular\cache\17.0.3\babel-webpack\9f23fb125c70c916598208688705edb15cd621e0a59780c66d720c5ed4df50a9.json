{"ast": null, "code": "import { __extends } from \"tslib\";\nimport Path from '../Path.js';\nimport * as vec2 from '../../core/vector.js';\nimport { quadraticSubdivide, cubicSubdivide, quadraticAt, cubicAt, quadraticDerivativeAt, cubicDerivativeAt } from '../../core/curve.js';\nvar out = [];\nvar BezierCurveShape = function () {\n  function BezierCurveShape() {\n    this.x1 = 0;\n    this.y1 = 0;\n    this.x2 = 0;\n    this.y2 = 0;\n    this.cpx1 = 0;\n    this.cpy1 = 0;\n    this.percent = 1;\n  }\n  return BezierCurveShape;\n}();\nexport { BezierCurveShape };\nfunction someVectorAt(shape, t, isTangent) {\n  var cpx2 = shape.cpx2;\n  var cpy2 = shape.cpy2;\n  if (cpx2 != null || cpy2 != null) {\n    return [(isTangent ? cubicDerivativeAt : cubicAt)(shape.x1, shape.cpx1, shape.cpx2, shape.x2, t), (isTangent ? cubicDerivativeAt : cubicAt)(shape.y1, shape.cpy1, shape.cpy2, shape.y2, t)];\n  } else {\n    return [(isTangent ? quadraticDerivativeAt : quadraticAt)(shape.x1, shape.cpx1, shape.x2, t), (isTangent ? quadraticDerivativeAt : quadraticAt)(shape.y1, shape.cpy1, shape.y2, t)];\n  }\n}\nvar BezierCurve = function (_super) {\n  __extends(BezierCurve, _super);\n  function BezierCurve(opts) {\n    return _super.call(this, opts) || this;\n  }\n  BezierCurve.prototype.getDefaultStyle = function () {\n    return {\n      stroke: '#000',\n      fill: null\n    };\n  };\n  BezierCurve.prototype.getDefaultShape = function () {\n    return new BezierCurveShape();\n  };\n  BezierCurve.prototype.buildPath = function (ctx, shape) {\n    var x1 = shape.x1;\n    var y1 = shape.y1;\n    var x2 = shape.x2;\n    var y2 = shape.y2;\n    var cpx1 = shape.cpx1;\n    var cpy1 = shape.cpy1;\n    var cpx2 = shape.cpx2;\n    var cpy2 = shape.cpy2;\n    var percent = shape.percent;\n    if (percent === 0) {\n      return;\n    }\n    ctx.moveTo(x1, y1);\n    if (cpx2 == null || cpy2 == null) {\n      if (percent < 1) {\n        quadraticSubdivide(x1, cpx1, x2, percent, out);\n        cpx1 = out[1];\n        x2 = out[2];\n        quadraticSubdivide(y1, cpy1, y2, percent, out);\n        cpy1 = out[1];\n        y2 = out[2];\n      }\n      ctx.quadraticCurveTo(cpx1, cpy1, x2, y2);\n    } else {\n      if (percent < 1) {\n        cubicSubdivide(x1, cpx1, cpx2, x2, percent, out);\n        cpx1 = out[1];\n        cpx2 = out[2];\n        x2 = out[3];\n        cubicSubdivide(y1, cpy1, cpy2, y2, percent, out);\n        cpy1 = out[1];\n        cpy2 = out[2];\n        y2 = out[3];\n      }\n      ctx.bezierCurveTo(cpx1, cpy1, cpx2, cpy2, x2, y2);\n    }\n  };\n  BezierCurve.prototype.pointAt = function (t) {\n    return someVectorAt(this.shape, t, false);\n  };\n  BezierCurve.prototype.tangentAt = function (t) {\n    var p = someVectorAt(this.shape, t, true);\n    return vec2.normalize(p, p);\n  };\n  return BezierCurve;\n}(Path);\n;\nBezierCurve.prototype.type = 'bezier-curve';\nexport default BezierCurve;", "map": {"version": 3, "names": ["__extends", "Path", "vec2", "quadraticSubdivide", "cubicSubdivide", "quadraticAt", "cubicAt", "quadraticDerivativeAt", "cubicDerivativeAt", "out", "BezierCurveShape", "x1", "y1", "x2", "y2", "cpx1", "cpy1", "percent", "someVectorAt", "shape", "t", "isTangent", "cpx2", "cpy2", "BezierCurve", "_super", "opts", "call", "prototype", "getDefaultStyle", "stroke", "fill", "getDefaultShape", "buildPath", "ctx", "moveTo", "quadraticCurveTo", "bezierCurveTo", "pointAt", "tangentAt", "p", "normalize", "type"], "sources": ["C:/WolfSystemV2/WolfOldFrontEnd/node_modules/zrender/lib/graphic/shape/BezierCurve.js"], "sourcesContent": ["import { __extends } from \"tslib\";\nimport Path from '../Path.js';\nimport * as vec2 from '../../core/vector.js';\nimport { quadraticSubdivide, cubicSubdivide, quadraticAt, cubicAt, quadraticDerivativeAt, cubicDerivativeAt } from '../../core/curve.js';\nvar out = [];\nvar BezierCurveShape = (function () {\n    function BezierCurveShape() {\n        this.x1 = 0;\n        this.y1 = 0;\n        this.x2 = 0;\n        this.y2 = 0;\n        this.cpx1 = 0;\n        this.cpy1 = 0;\n        this.percent = 1;\n    }\n    return BezierCurveShape;\n}());\nexport { BezierCurveShape };\nfunction someVectorAt(shape, t, isTangent) {\n    var cpx2 = shape.cpx2;\n    var cpy2 = shape.cpy2;\n    if (cpx2 != null || cpy2 != null) {\n        return [\n            (isTangent ? cubicDerivativeAt : cubicAt)(shape.x1, shape.cpx1, shape.cpx2, shape.x2, t),\n            (isTangent ? cubicDerivativeAt : cubicAt)(shape.y1, shape.cpy1, shape.cpy2, shape.y2, t)\n        ];\n    }\n    else {\n        return [\n            (isTangent ? quadraticDerivativeAt : quadraticAt)(shape.x1, shape.cpx1, shape.x2, t),\n            (isTangent ? quadraticDerivativeAt : quadraticAt)(shape.y1, shape.cpy1, shape.y2, t)\n        ];\n    }\n}\nvar BezierCurve = (function (_super) {\n    __extends(BezierCurve, _super);\n    function BezierCurve(opts) {\n        return _super.call(this, opts) || this;\n    }\n    BezierCurve.prototype.getDefaultStyle = function () {\n        return {\n            stroke: '#000',\n            fill: null\n        };\n    };\n    BezierCurve.prototype.getDefaultShape = function () {\n        return new BezierCurveShape();\n    };\n    BezierCurve.prototype.buildPath = function (ctx, shape) {\n        var x1 = shape.x1;\n        var y1 = shape.y1;\n        var x2 = shape.x2;\n        var y2 = shape.y2;\n        var cpx1 = shape.cpx1;\n        var cpy1 = shape.cpy1;\n        var cpx2 = shape.cpx2;\n        var cpy2 = shape.cpy2;\n        var percent = shape.percent;\n        if (percent === 0) {\n            return;\n        }\n        ctx.moveTo(x1, y1);\n        if (cpx2 == null || cpy2 == null) {\n            if (percent < 1) {\n                quadraticSubdivide(x1, cpx1, x2, percent, out);\n                cpx1 = out[1];\n                x2 = out[2];\n                quadraticSubdivide(y1, cpy1, y2, percent, out);\n                cpy1 = out[1];\n                y2 = out[2];\n            }\n            ctx.quadraticCurveTo(cpx1, cpy1, x2, y2);\n        }\n        else {\n            if (percent < 1) {\n                cubicSubdivide(x1, cpx1, cpx2, x2, percent, out);\n                cpx1 = out[1];\n                cpx2 = out[2];\n                x2 = out[3];\n                cubicSubdivide(y1, cpy1, cpy2, y2, percent, out);\n                cpy1 = out[1];\n                cpy2 = out[2];\n                y2 = out[3];\n            }\n            ctx.bezierCurveTo(cpx1, cpy1, cpx2, cpy2, x2, y2);\n        }\n    };\n    BezierCurve.prototype.pointAt = function (t) {\n        return someVectorAt(this.shape, t, false);\n    };\n    BezierCurve.prototype.tangentAt = function (t) {\n        var p = someVectorAt(this.shape, t, true);\n        return vec2.normalize(p, p);\n    };\n    return BezierCurve;\n}(Path));\n;\nBezierCurve.prototype.type = 'bezier-curve';\nexport default BezierCurve;\n"], "mappings": "AAAA,SAASA,SAAS,QAAQ,OAAO;AACjC,OAAOC,IAAI,MAAM,YAAY;AAC7B,OAAO,KAAKC,IAAI,MAAM,sBAAsB;AAC5C,SAASC,kBAAkB,EAAEC,cAAc,EAAEC,WAAW,EAAEC,OAAO,EAAEC,qBAAqB,EAAEC,iBAAiB,QAAQ,qBAAqB;AACxI,IAAIC,GAAG,GAAG,EAAE;AACZ,IAAIC,gBAAgB,GAAI,YAAY;EAChC,SAASA,gBAAgBA,CAAA,EAAG;IACxB,IAAI,CAACC,EAAE,GAAG,CAAC;IACX,IAAI,CAACC,EAAE,GAAG,CAAC;IACX,IAAI,CAACC,EAAE,GAAG,CAAC;IACX,IAAI,CAACC,EAAE,GAAG,CAAC;IACX,IAAI,CAACC,IAAI,GAAG,CAAC;IACb,IAAI,CAACC,IAAI,GAAG,CAAC;IACb,IAAI,CAACC,OAAO,GAAG,CAAC;EACpB;EACA,OAAOP,gBAAgB;AAC3B,CAAC,CAAC,CAAE;AACJ,SAASA,gBAAgB;AACzB,SAASQ,YAAYA,CAACC,KAAK,EAAEC,CAAC,EAAEC,SAAS,EAAE;EACvC,IAAIC,IAAI,GAAGH,KAAK,CAACG,IAAI;EACrB,IAAIC,IAAI,GAAGJ,KAAK,CAACI,IAAI;EACrB,IAAID,IAAI,IAAI,IAAI,IAAIC,IAAI,IAAI,IAAI,EAAE;IAC9B,OAAO,CACH,CAACF,SAAS,GAAGb,iBAAiB,GAAGF,OAAO,EAAEa,KAAK,CAACR,EAAE,EAAEQ,KAAK,CAACJ,IAAI,EAAEI,KAAK,CAACG,IAAI,EAAEH,KAAK,CAACN,EAAE,EAAEO,CAAC,CAAC,EACxF,CAACC,SAAS,GAAGb,iBAAiB,GAAGF,OAAO,EAAEa,KAAK,CAACP,EAAE,EAAEO,KAAK,CAACH,IAAI,EAAEG,KAAK,CAACI,IAAI,EAAEJ,KAAK,CAACL,EAAE,EAAEM,CAAC,CAAC,CAC3F;EACL,CAAC,MACI;IACD,OAAO,CACH,CAACC,SAAS,GAAGd,qBAAqB,GAAGF,WAAW,EAAEc,KAAK,CAACR,EAAE,EAAEQ,KAAK,CAACJ,IAAI,EAAEI,KAAK,CAACN,EAAE,EAAEO,CAAC,CAAC,EACpF,CAACC,SAAS,GAAGd,qBAAqB,GAAGF,WAAW,EAAEc,KAAK,CAACP,EAAE,EAAEO,KAAK,CAACH,IAAI,EAAEG,KAAK,CAACL,EAAE,EAAEM,CAAC,CAAC,CACvF;EACL;AACJ;AACA,IAAII,WAAW,GAAI,UAAUC,MAAM,EAAE;EACjCzB,SAAS,CAACwB,WAAW,EAAEC,MAAM,CAAC;EAC9B,SAASD,WAAWA,CAACE,IAAI,EAAE;IACvB,OAAOD,MAAM,CAACE,IAAI,CAAC,IAAI,EAAED,IAAI,CAAC,IAAI,IAAI;EAC1C;EACAF,WAAW,CAACI,SAAS,CAACC,eAAe,GAAG,YAAY;IAChD,OAAO;MACHC,MAAM,EAAE,MAAM;MACdC,IAAI,EAAE;IACV,CAAC;EACL,CAAC;EACDP,WAAW,CAACI,SAAS,CAACI,eAAe,GAAG,YAAY;IAChD,OAAO,IAAItB,gBAAgB,CAAC,CAAC;EACjC,CAAC;EACDc,WAAW,CAACI,SAAS,CAACK,SAAS,GAAG,UAAUC,GAAG,EAAEf,KAAK,EAAE;IACpD,IAAIR,EAAE,GAAGQ,KAAK,CAACR,EAAE;IACjB,IAAIC,EAAE,GAAGO,KAAK,CAACP,EAAE;IACjB,IAAIC,EAAE,GAAGM,KAAK,CAACN,EAAE;IACjB,IAAIC,EAAE,GAAGK,KAAK,CAACL,EAAE;IACjB,IAAIC,IAAI,GAAGI,KAAK,CAACJ,IAAI;IACrB,IAAIC,IAAI,GAAGG,KAAK,CAACH,IAAI;IACrB,IAAIM,IAAI,GAAGH,KAAK,CAACG,IAAI;IACrB,IAAIC,IAAI,GAAGJ,KAAK,CAACI,IAAI;IACrB,IAAIN,OAAO,GAAGE,KAAK,CAACF,OAAO;IAC3B,IAAIA,OAAO,KAAK,CAAC,EAAE;MACf;IACJ;IACAiB,GAAG,CAACC,MAAM,CAACxB,EAAE,EAAEC,EAAE,CAAC;IAClB,IAAIU,IAAI,IAAI,IAAI,IAAIC,IAAI,IAAI,IAAI,EAAE;MAC9B,IAAIN,OAAO,GAAG,CAAC,EAAE;QACbd,kBAAkB,CAACQ,EAAE,EAAEI,IAAI,EAAEF,EAAE,EAAEI,OAAO,EAAER,GAAG,CAAC;QAC9CM,IAAI,GAAGN,GAAG,CAAC,CAAC,CAAC;QACbI,EAAE,GAAGJ,GAAG,CAAC,CAAC,CAAC;QACXN,kBAAkB,CAACS,EAAE,EAAEI,IAAI,EAAEF,EAAE,EAAEG,OAAO,EAAER,GAAG,CAAC;QAC9CO,IAAI,GAAGP,GAAG,CAAC,CAAC,CAAC;QACbK,EAAE,GAAGL,GAAG,CAAC,CAAC,CAAC;MACf;MACAyB,GAAG,CAACE,gBAAgB,CAACrB,IAAI,EAAEC,IAAI,EAAEH,EAAE,EAAEC,EAAE,CAAC;IAC5C,CAAC,MACI;MACD,IAAIG,OAAO,GAAG,CAAC,EAAE;QACbb,cAAc,CAACO,EAAE,EAAEI,IAAI,EAAEO,IAAI,EAAET,EAAE,EAAEI,OAAO,EAAER,GAAG,CAAC;QAChDM,IAAI,GAAGN,GAAG,CAAC,CAAC,CAAC;QACba,IAAI,GAAGb,GAAG,CAAC,CAAC,CAAC;QACbI,EAAE,GAAGJ,GAAG,CAAC,CAAC,CAAC;QACXL,cAAc,CAACQ,EAAE,EAAEI,IAAI,EAAEO,IAAI,EAAET,EAAE,EAAEG,OAAO,EAAER,GAAG,CAAC;QAChDO,IAAI,GAAGP,GAAG,CAAC,CAAC,CAAC;QACbc,IAAI,GAAGd,GAAG,CAAC,CAAC,CAAC;QACbK,EAAE,GAAGL,GAAG,CAAC,CAAC,CAAC;MACf;MACAyB,GAAG,CAACG,aAAa,CAACtB,IAAI,EAAEC,IAAI,EAAEM,IAAI,EAAEC,IAAI,EAAEV,EAAE,EAAEC,EAAE,CAAC;IACrD;EACJ,CAAC;EACDU,WAAW,CAACI,SAAS,CAACU,OAAO,GAAG,UAAUlB,CAAC,EAAE;IACzC,OAAOF,YAAY,CAAC,IAAI,CAACC,KAAK,EAAEC,CAAC,EAAE,KAAK,CAAC;EAC7C,CAAC;EACDI,WAAW,CAACI,SAAS,CAACW,SAAS,GAAG,UAAUnB,CAAC,EAAE;IAC3C,IAAIoB,CAAC,GAAGtB,YAAY,CAAC,IAAI,CAACC,KAAK,EAAEC,CAAC,EAAE,IAAI,CAAC;IACzC,OAAOlB,IAAI,CAACuC,SAAS,CAACD,CAAC,EAAEA,CAAC,CAAC;EAC/B,CAAC;EACD,OAAOhB,WAAW;AACtB,CAAC,CAACvB,IAAI,CAAE;AACR;AACAuB,WAAW,CAACI,SAAS,CAACc,IAAI,GAAG,cAAc;AAC3C,eAAelB,WAAW"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}