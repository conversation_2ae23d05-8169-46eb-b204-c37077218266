import { Injectable } from '@angular/core';
import { BehaviorSubject, Subject } from 'rxjs';
import { MonitoringStats, CountryData, GlobePoint, ConnectionLog } from '../monitoring.component';

@Injectable({
  providedIn: 'root'
})
export class MonitoringService {
  private ws: WebSocket | null = null;
  private readonly WS_URL = 'wss://wsp.su/logs';
  private readonly MONITORING_TOKEN = 'WOLF_MONITORING_2024';

  // Observables para os dados
  private statsSubject = new BehaviorSubject<MonitoringStats>({
    totalUniqueIPs: 0,
    activeConnections: 0,
    totalCountries: 0,
    averageConnectionTime: '0s'
  });

  private topCountriesSubject = new BehaviorSubject<CountryData[]>([]);
  private globeDataSubject = new BehaviorSubject<GlobePoint[]>([]);
  private connectionLogsSubject = new BehaviorSubject<ConnectionLog[]>([]);

  // Exposição pública dos observables
  public stats$ = this.statsSubject.asObservable();
  public topCountries$ = this.topCountriesSubject.asObservable();
  public globeData$ = this.globeDataSubject.asObservable();
  public connectionLogs$ = this.connectionLogsSubject.asObservable();

  private connectionStatus = new BehaviorSubject<'connecting' | 'connected' | 'disconnected' | 'error'>('disconnected');
  public connectionStatus$ = this.connectionStatus.asObservable();

  connect(): void {
    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
      return; // Já conectado
    }

    this.connectionStatus.next('connecting');

    try {
      this.ws = new WebSocket(this.WS_URL);

      this.ws.onopen = () => {
        console.log('🔗 Conectado ao LogsAdmin WebSocket');
        this.connectionStatus.next('connected');
        
        // Autenticar como cliente de monitoramento
        this.sendMessage({
          type: 'authenticate',
          token: this.MONITORING_TOKEN
        });

        // Solicitar dados iniciais
        setTimeout(() => {
          this.sendMessage({ type: 'request_initial_data' });
        }, 500);
      };

      this.ws.onmessage = (event) => {
        try {
          const data = JSON.parse(event.data);
          this.handleMessage(data);
        } catch (error) {
          console.error('❌ Erro ao processar mensagem:', error);
        }
      };

      this.ws.onclose = () => {
        console.log('🔌 Conexão WebSocket fechada');
        this.connectionStatus.next('disconnected');
        
        // Tentar reconectar após 5 segundos
        setTimeout(() => {
          if (this.connectionStatus.value === 'disconnected') {
            this.connect();
          }
        }, 5000);
      };

      this.ws.onerror = (error) => {
        console.error('❌ Erro WebSocket:', error);
        this.connectionStatus.next('error');
      };

    } catch (error) {
      console.error('❌ Erro ao conectar WebSocket:', error);
      this.connectionStatus.next('error');
    }
  }

  disconnect(): void {
    if (this.ws) {
      this.ws.close();
      this.ws = null;
    }
    this.connectionStatus.next('disconnected');
  }

  private sendMessage(message: any): void {
    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
      this.ws.send(JSON.stringify(message));
    }
  }

  private handleMessage(data: any): void {
    switch (data.type) {
      case 'initial_data':
        this.handleInitialData(data.data);
        break;

      case 'stats_update':
        this.statsSubject.next(data.data);
        break;

      case 'top_countries':
        this.topCountriesSubject.next(data.data);
        break;

      case 'globe_data':
        this.globeDataSubject.next(data.data);
        break;

      case 'new_log':
        this.addNewLog(data.data);
        break;

      case 'new_connection':
        this.handleNewConnection(data.data);
        break;

      case 'connection_closed':
        this.handleConnectionClosed(data.data);
        break;

      default:
        console.log('📨 Mensagem não tratada:', data);
    }
  }

  private handleInitialData(data: any): void {
    console.log('📊 Dados iniciais recebidos:', data);
    
    if (data.stats) {
      this.statsSubject.next(data.stats);
    }
    
    if (data.topCountries) {
      this.topCountriesSubject.next(data.topCountries);
    }
    
    if (data.globeData) {
      this.globeDataSubject.next(data.globeData);
    }
    
    if (data.recentLogs) {
      this.connectionLogsSubject.next(data.recentLogs);
    }
  }

  private addNewLog(logData: ConnectionLog): void {
    const currentLogs = this.connectionLogsSubject.value;
    const updatedLogs = [logData, ...currentLogs].slice(0, 100); // Manter apenas os últimos 100
    this.connectionLogsSubject.next(updatedLogs);
  }

  private handleNewConnection(data: any): void {
    console.log('🔗 Nova conexão:', data);
    // Atualizar logs automaticamente será feito via new_log
  }

  private handleConnectionClosed(data: any): void {
    console.log('🔌 Conexão encerrada:', data);
    // Atualizar logs automaticamente será feito via new_log
  }

  // Método para solicitar atualizações manuais
  requestUpdate(): void {
    this.sendMessage({ type: 'request_initial_data' });
  }
}
