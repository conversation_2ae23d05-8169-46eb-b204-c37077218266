{"ast": null, "code": "/* Built-in method references for those with the same name as other `lodash` methods. */\nvar nativeMax = Math.max,\n  nativeMin = Math.min;\n\n/**\n * Gets the view, applying any `transforms` to the `start` and `end` positions.\n *\n * @private\n * @param {number} start The start of the view.\n * @param {number} end The end of the view.\n * @param {Array} transforms The transformations to apply to the view.\n * @returns {Object} Returns an object containing the `start` and `end`\n *  positions of the view.\n */\nfunction getView(start, end, transforms) {\n  var index = -1,\n    length = transforms.length;\n  while (++index < length) {\n    var data = transforms[index],\n      size = data.size;\n    switch (data.type) {\n      case 'drop':\n        start += size;\n        break;\n      case 'dropRight':\n        end -= size;\n        break;\n      case 'take':\n        end = nativeMin(end, start + size);\n        break;\n      case 'takeRight':\n        start = nativeMax(start, end - size);\n        break;\n    }\n  }\n  return {\n    'start': start,\n    'end': end\n  };\n}\nexport default getView;", "map": {"version": 3, "names": ["nativeMax", "Math", "max", "nativeMin", "min", "get<PERSON>iew", "start", "end", "transforms", "index", "length", "data", "size", "type"], "sources": ["C:/WolfSystemV2/WolfOldFrontEnd/node_modules/lodash-es/_getView.js"], "sourcesContent": ["/* Built-in method references for those with the same name as other `lodash` methods. */\nvar nativeMax = Math.max,\n    nativeMin = Math.min;\n\n/**\n * Gets the view, applying any `transforms` to the `start` and `end` positions.\n *\n * @private\n * @param {number} start The start of the view.\n * @param {number} end The end of the view.\n * @param {Array} transforms The transformations to apply to the view.\n * @returns {Object} Returns an object containing the `start` and `end`\n *  positions of the view.\n */\nfunction getView(start, end, transforms) {\n  var index = -1,\n      length = transforms.length;\n\n  while (++index < length) {\n    var data = transforms[index],\n        size = data.size;\n\n    switch (data.type) {\n      case 'drop':      start += size; break;\n      case 'dropRight': end -= size; break;\n      case 'take':      end = nativeMin(end, start + size); break;\n      case 'takeRight': start = nativeMax(start, end - size); break;\n    }\n  }\n  return { 'start': start, 'end': end };\n}\n\nexport default getView;\n"], "mappings": "AAAA;AACA,IAAIA,SAAS,GAAGC,IAAI,CAACC,GAAG;EACpBC,SAAS,GAAGF,IAAI,CAACG,GAAG;;AAExB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,OAAOA,CAACC,KAAK,EAAEC,GAAG,EAAEC,UAAU,EAAE;EACvC,IAAIC,KAAK,GAAG,CAAC,CAAC;IACVC,MAAM,GAAGF,UAAU,CAACE,MAAM;EAE9B,OAAO,EAAED,KAAK,GAAGC,MAAM,EAAE;IACvB,IAAIC,IAAI,GAAGH,UAAU,CAACC,KAAK,CAAC;MACxBG,IAAI,GAAGD,IAAI,CAACC,IAAI;IAEpB,QAAQD,IAAI,CAACE,IAAI;MACf,KAAK,MAAM;QAAOP,KAAK,IAAIM,IAAI;QAAE;MACjC,KAAK,WAAW;QAAEL,GAAG,IAAIK,IAAI;QAAE;MAC/B,KAAK,MAAM;QAAOL,GAAG,GAAGJ,SAAS,CAACI,GAAG,EAAED,KAAK,GAAGM,IAAI,CAAC;QAAE;MACtD,KAAK,WAAW;QAAEN,KAAK,GAAGN,SAAS,CAACM,KAAK,EAAEC,GAAG,GAAGK,IAAI,CAAC;QAAE;IAC1D;EACF;EACA,OAAO;IAAE,OAAO,EAAEN,KAAK;IAAE,KAAK,EAAEC;EAAI,CAAC;AACvC;AAEA,eAAeF,OAAO"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}