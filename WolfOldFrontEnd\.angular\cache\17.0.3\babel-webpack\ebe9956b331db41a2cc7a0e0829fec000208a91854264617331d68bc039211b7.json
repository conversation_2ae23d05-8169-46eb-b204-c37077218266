{"ast": null, "code": "import { __extends } from \"tslib\";\nimport Path from '../Path.js';\nvar EllipseShape = function () {\n  function EllipseShape() {\n    this.cx = 0;\n    this.cy = 0;\n    this.rx = 0;\n    this.ry = 0;\n  }\n  return EllipseShape;\n}();\nexport { EllipseShape };\nvar Ellipse = function (_super) {\n  __extends(Ellipse, _super);\n  function Ellipse(opts) {\n    return _super.call(this, opts) || this;\n  }\n  Ellipse.prototype.getDefaultShape = function () {\n    return new EllipseShape();\n  };\n  Ellipse.prototype.buildPath = function (ctx, shape) {\n    var k = 0.5522848;\n    var x = shape.cx;\n    var y = shape.cy;\n    var a = shape.rx;\n    var b = shape.ry;\n    var ox = a * k;\n    var oy = b * k;\n    ctx.moveTo(x - a, y);\n    ctx.bezierCurveTo(x - a, y - oy, x - ox, y - b, x, y - b);\n    ctx.bezierCurveTo(x + ox, y - b, x + a, y - oy, x + a, y);\n    ctx.bezierCurveTo(x + a, y + oy, x + ox, y + b, x, y + b);\n    ctx.bezierCurveTo(x - ox, y + b, x - a, y + oy, x - a, y);\n    ctx.closePath();\n  };\n  return Ellipse;\n}(Path);\nEllipse.prototype.type = 'ellipse';\nexport default Ellipse;", "map": {"version": 3, "names": ["__extends", "Path", "EllipseShape", "cx", "cy", "rx", "ry", "Ellipse", "_super", "opts", "call", "prototype", "getDefaultShape", "buildPath", "ctx", "shape", "k", "x", "y", "a", "b", "ox", "oy", "moveTo", "bezierCurveTo", "closePath", "type"], "sources": ["C:/WolfSystemV2/WolfOldFrontEnd/node_modules/zrender/lib/graphic/shape/Ellipse.js"], "sourcesContent": ["import { __extends } from \"tslib\";\nimport Path from '../Path.js';\nvar EllipseShape = (function () {\n    function EllipseShape() {\n        this.cx = 0;\n        this.cy = 0;\n        this.rx = 0;\n        this.ry = 0;\n    }\n    return EllipseShape;\n}());\nexport { EllipseShape };\nvar Ellipse = (function (_super) {\n    __extends(Ellipse, _super);\n    function Ellipse(opts) {\n        return _super.call(this, opts) || this;\n    }\n    Ellipse.prototype.getDefaultShape = function () {\n        return new EllipseShape();\n    };\n    Ellipse.prototype.buildPath = function (ctx, shape) {\n        var k = 0.5522848;\n        var x = shape.cx;\n        var y = shape.cy;\n        var a = shape.rx;\n        var b = shape.ry;\n        var ox = a * k;\n        var oy = b * k;\n        ctx.moveTo(x - a, y);\n        ctx.bezierCurveTo(x - a, y - oy, x - ox, y - b, x, y - b);\n        ctx.bezierCurveTo(x + ox, y - b, x + a, y - oy, x + a, y);\n        ctx.bezierCurveTo(x + a, y + oy, x + ox, y + b, x, y + b);\n        ctx.bezierCurveTo(x - ox, y + b, x - a, y + oy, x - a, y);\n        ctx.closePath();\n    };\n    return Ellipse;\n}(Path));\nEllipse.prototype.type = 'ellipse';\nexport default Ellipse;\n"], "mappings": "AAAA,SAASA,SAAS,QAAQ,OAAO;AACjC,OAAOC,IAAI,MAAM,YAAY;AAC7B,IAAIC,YAAY,GAAI,YAAY;EAC5B,SAASA,YAAYA,CAAA,EAAG;IACpB,IAAI,CAACC,EAAE,GAAG,CAAC;IACX,IAAI,CAACC,EAAE,GAAG,CAAC;IACX,IAAI,CAACC,EAAE,GAAG,CAAC;IACX,IAAI,CAACC,EAAE,GAAG,CAAC;EACf;EACA,OAAOJ,YAAY;AACvB,CAAC,CAAC,CAAE;AACJ,SAASA,YAAY;AACrB,IAAIK,OAAO,GAAI,UAAUC,MAAM,EAAE;EAC7BR,SAAS,CAACO,OAAO,EAAEC,MAAM,CAAC;EAC1B,SAASD,OAAOA,CAACE,IAAI,EAAE;IACnB,OAAOD,MAAM,CAACE,IAAI,CAAC,IAAI,EAAED,IAAI,CAAC,IAAI,IAAI;EAC1C;EACAF,OAAO,CAACI,SAAS,CAACC,eAAe,GAAG,YAAY;IAC5C,OAAO,IAAIV,YAAY,CAAC,CAAC;EAC7B,CAAC;EACDK,OAAO,CAACI,SAAS,CAACE,SAAS,GAAG,UAAUC,GAAG,EAAEC,KAAK,EAAE;IAChD,IAAIC,CAAC,GAAG,SAAS;IACjB,IAAIC,CAAC,GAAGF,KAAK,CAACZ,EAAE;IAChB,IAAIe,CAAC,GAAGH,KAAK,CAACX,EAAE;IAChB,IAAIe,CAAC,GAAGJ,KAAK,CAACV,EAAE;IAChB,IAAIe,CAAC,GAAGL,KAAK,CAACT,EAAE;IAChB,IAAIe,EAAE,GAAGF,CAAC,GAAGH,CAAC;IACd,IAAIM,EAAE,GAAGF,CAAC,GAAGJ,CAAC;IACdF,GAAG,CAACS,MAAM,CAACN,CAAC,GAAGE,CAAC,EAAED,CAAC,CAAC;IACpBJ,GAAG,CAACU,aAAa,CAACP,CAAC,GAAGE,CAAC,EAAED,CAAC,GAAGI,EAAE,EAAEL,CAAC,GAAGI,EAAE,EAAEH,CAAC,GAAGE,CAAC,EAAEH,CAAC,EAAEC,CAAC,GAAGE,CAAC,CAAC;IACzDN,GAAG,CAACU,aAAa,CAACP,CAAC,GAAGI,EAAE,EAAEH,CAAC,GAAGE,CAAC,EAAEH,CAAC,GAAGE,CAAC,EAAED,CAAC,GAAGI,EAAE,EAAEL,CAAC,GAAGE,CAAC,EAAED,CAAC,CAAC;IACzDJ,GAAG,CAACU,aAAa,CAACP,CAAC,GAAGE,CAAC,EAAED,CAAC,GAAGI,EAAE,EAAEL,CAAC,GAAGI,EAAE,EAAEH,CAAC,GAAGE,CAAC,EAAEH,CAAC,EAAEC,CAAC,GAAGE,CAAC,CAAC;IACzDN,GAAG,CAACU,aAAa,CAACP,CAAC,GAAGI,EAAE,EAAEH,CAAC,GAAGE,CAAC,EAAEH,CAAC,GAAGE,CAAC,EAAED,CAAC,GAAGI,EAAE,EAAEL,CAAC,GAAGE,CAAC,EAAED,CAAC,CAAC;IACzDJ,GAAG,CAACW,SAAS,CAAC,CAAC;EACnB,CAAC;EACD,OAAOlB,OAAO;AAClB,CAAC,CAACN,IAAI,CAAE;AACRM,OAAO,CAACI,SAAS,CAACe,IAAI,GAAG,SAAS;AAClC,eAAenB,OAAO"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}