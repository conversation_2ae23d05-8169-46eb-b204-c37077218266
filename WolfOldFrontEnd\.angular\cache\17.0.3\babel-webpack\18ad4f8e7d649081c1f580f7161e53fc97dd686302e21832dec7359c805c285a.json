{"ast": null, "code": "import * as matrix from './matrix.js';\nimport Point from './Point.js';\nvar mathMin = Math.min;\nvar mathMax = Math.max;\nvar lt = new Point();\nvar rb = new Point();\nvar lb = new Point();\nvar rt = new Point();\nvar minTv = new Point();\nvar maxTv = new Point();\nvar BoundingRect = function () {\n  function BoundingRect(x, y, width, height) {\n    if (width < 0) {\n      x = x + width;\n      width = -width;\n    }\n    if (height < 0) {\n      y = y + height;\n      height = -height;\n    }\n    this.x = x;\n    this.y = y;\n    this.width = width;\n    this.height = height;\n  }\n  BoundingRect.prototype.union = function (other) {\n    var x = mathMin(other.x, this.x);\n    var y = mathMin(other.y, this.y);\n    if (isFinite(this.x) && isFinite(this.width)) {\n      this.width = mathMax(other.x + other.width, this.x + this.width) - x;\n    } else {\n      this.width = other.width;\n    }\n    if (isFinite(this.y) && isFinite(this.height)) {\n      this.height = mathMax(other.y + other.height, this.y + this.height) - y;\n    } else {\n      this.height = other.height;\n    }\n    this.x = x;\n    this.y = y;\n  };\n  BoundingRect.prototype.applyTransform = function (m) {\n    BoundingRect.applyTransform(this, this, m);\n  };\n  BoundingRect.prototype.calculateTransform = function (b) {\n    var a = this;\n    var sx = b.width / a.width;\n    var sy = b.height / a.height;\n    var m = matrix.create();\n    matrix.translate(m, m, [-a.x, -a.y]);\n    matrix.scale(m, m, [sx, sy]);\n    matrix.translate(m, m, [b.x, b.y]);\n    return m;\n  };\n  BoundingRect.prototype.intersect = function (b, mtv) {\n    if (!b) {\n      return false;\n    }\n    if (!(b instanceof BoundingRect)) {\n      b = BoundingRect.create(b);\n    }\n    var a = this;\n    var ax0 = a.x;\n    var ax1 = a.x + a.width;\n    var ay0 = a.y;\n    var ay1 = a.y + a.height;\n    var bx0 = b.x;\n    var bx1 = b.x + b.width;\n    var by0 = b.y;\n    var by1 = b.y + b.height;\n    var overlap = !(ax1 < bx0 || bx1 < ax0 || ay1 < by0 || by1 < ay0);\n    if (mtv) {\n      var dMin = Infinity;\n      var dMax = 0;\n      var d0 = Math.abs(ax1 - bx0);\n      var d1 = Math.abs(bx1 - ax0);\n      var d2 = Math.abs(ay1 - by0);\n      var d3 = Math.abs(by1 - ay0);\n      var dx = Math.min(d0, d1);\n      var dy = Math.min(d2, d3);\n      if (ax1 < bx0 || bx1 < ax0) {\n        if (dx > dMax) {\n          dMax = dx;\n          if (d0 < d1) {\n            Point.set(maxTv, -d0, 0);\n          } else {\n            Point.set(maxTv, d1, 0);\n          }\n        }\n      } else {\n        if (dx < dMin) {\n          dMin = dx;\n          if (d0 < d1) {\n            Point.set(minTv, d0, 0);\n          } else {\n            Point.set(minTv, -d1, 0);\n          }\n        }\n      }\n      if (ay1 < by0 || by1 < ay0) {\n        if (dy > dMax) {\n          dMax = dy;\n          if (d2 < d3) {\n            Point.set(maxTv, 0, -d2);\n          } else {\n            Point.set(maxTv, 0, d3);\n          }\n        }\n      } else {\n        if (dx < dMin) {\n          dMin = dx;\n          if (d2 < d3) {\n            Point.set(minTv, 0, d2);\n          } else {\n            Point.set(minTv, 0, -d3);\n          }\n        }\n      }\n    }\n    if (mtv) {\n      Point.copy(mtv, overlap ? minTv : maxTv);\n    }\n    return overlap;\n  };\n  BoundingRect.prototype.contain = function (x, y) {\n    var rect = this;\n    return x >= rect.x && x <= rect.x + rect.width && y >= rect.y && y <= rect.y + rect.height;\n  };\n  BoundingRect.prototype.clone = function () {\n    return new BoundingRect(this.x, this.y, this.width, this.height);\n  };\n  BoundingRect.prototype.copy = function (other) {\n    BoundingRect.copy(this, other);\n  };\n  BoundingRect.prototype.plain = function () {\n    return {\n      x: this.x,\n      y: this.y,\n      width: this.width,\n      height: this.height\n    };\n  };\n  BoundingRect.prototype.isFinite = function () {\n    return isFinite(this.x) && isFinite(this.y) && isFinite(this.width) && isFinite(this.height);\n  };\n  BoundingRect.prototype.isZero = function () {\n    return this.width === 0 || this.height === 0;\n  };\n  BoundingRect.create = function (rect) {\n    return new BoundingRect(rect.x, rect.y, rect.width, rect.height);\n  };\n  BoundingRect.copy = function (target, source) {\n    target.x = source.x;\n    target.y = source.y;\n    target.width = source.width;\n    target.height = source.height;\n  };\n  BoundingRect.applyTransform = function (target, source, m) {\n    if (!m) {\n      if (target !== source) {\n        BoundingRect.copy(target, source);\n      }\n      return;\n    }\n    if (m[1] < 1e-5 && m[1] > -1e-5 && m[2] < 1e-5 && m[2] > -1e-5) {\n      var sx = m[0];\n      var sy = m[3];\n      var tx = m[4];\n      var ty = m[5];\n      target.x = source.x * sx + tx;\n      target.y = source.y * sy + ty;\n      target.width = source.width * sx;\n      target.height = source.height * sy;\n      if (target.width < 0) {\n        target.x += target.width;\n        target.width = -target.width;\n      }\n      if (target.height < 0) {\n        target.y += target.height;\n        target.height = -target.height;\n      }\n      return;\n    }\n    lt.x = lb.x = source.x;\n    lt.y = rt.y = source.y;\n    rb.x = rt.x = source.x + source.width;\n    rb.y = lb.y = source.y + source.height;\n    lt.transform(m);\n    rt.transform(m);\n    rb.transform(m);\n    lb.transform(m);\n    target.x = mathMin(lt.x, rb.x, lb.x, rt.x);\n    target.y = mathMin(lt.y, rb.y, lb.y, rt.y);\n    var maxX = mathMax(lt.x, rb.x, lb.x, rt.x);\n    var maxY = mathMax(lt.y, rb.y, lb.y, rt.y);\n    target.width = maxX - target.x;\n    target.height = maxY - target.y;\n  };\n  return BoundingRect;\n}();\nexport default BoundingRect;", "map": {"version": 3, "names": ["matrix", "Point", "mathMin", "Math", "min", "mathMax", "max", "lt", "rb", "lb", "rt", "minTv", "maxTv", "BoundingRect", "x", "y", "width", "height", "prototype", "union", "other", "isFinite", "applyTransform", "m", "calculateTransform", "b", "a", "sx", "sy", "create", "translate", "scale", "intersect", "mtv", "ax0", "ax1", "ay0", "ay1", "bx0", "bx1", "by0", "by1", "overlap", "dMin", "Infinity", "dMax", "d0", "abs", "d1", "d2", "d3", "dx", "dy", "set", "copy", "contain", "rect", "clone", "plain", "isZero", "target", "source", "tx", "ty", "transform", "maxX", "maxY"], "sources": ["C:/WolfSystemV2/WolfOldFrontEnd/node_modules/zrender/lib/core/BoundingRect.js"], "sourcesContent": ["import * as matrix from './matrix.js';\nimport Point from './Point.js';\nvar mathMin = Math.min;\nvar mathMax = Math.max;\nvar lt = new Point();\nvar rb = new Point();\nvar lb = new Point();\nvar rt = new Point();\nvar minTv = new Point();\nvar maxTv = new Point();\nvar BoundingRect = (function () {\n    function BoundingRect(x, y, width, height) {\n        if (width < 0) {\n            x = x + width;\n            width = -width;\n        }\n        if (height < 0) {\n            y = y + height;\n            height = -height;\n        }\n        this.x = x;\n        this.y = y;\n        this.width = width;\n        this.height = height;\n    }\n    BoundingRect.prototype.union = function (other) {\n        var x = mathMin(other.x, this.x);\n        var y = mathMin(other.y, this.y);\n        if (isFinite(this.x) && isFinite(this.width)) {\n            this.width = mathMax(other.x + other.width, this.x + this.width) - x;\n        }\n        else {\n            this.width = other.width;\n        }\n        if (isFinite(this.y) && isFinite(this.height)) {\n            this.height = mathMax(other.y + other.height, this.y + this.height) - y;\n        }\n        else {\n            this.height = other.height;\n        }\n        this.x = x;\n        this.y = y;\n    };\n    BoundingRect.prototype.applyTransform = function (m) {\n        BoundingRect.applyTransform(this, this, m);\n    };\n    BoundingRect.prototype.calculateTransform = function (b) {\n        var a = this;\n        var sx = b.width / a.width;\n        var sy = b.height / a.height;\n        var m = matrix.create();\n        matrix.translate(m, m, [-a.x, -a.y]);\n        matrix.scale(m, m, [sx, sy]);\n        matrix.translate(m, m, [b.x, b.y]);\n        return m;\n    };\n    BoundingRect.prototype.intersect = function (b, mtv) {\n        if (!b) {\n            return false;\n        }\n        if (!(b instanceof BoundingRect)) {\n            b = BoundingRect.create(b);\n        }\n        var a = this;\n        var ax0 = a.x;\n        var ax1 = a.x + a.width;\n        var ay0 = a.y;\n        var ay1 = a.y + a.height;\n        var bx0 = b.x;\n        var bx1 = b.x + b.width;\n        var by0 = b.y;\n        var by1 = b.y + b.height;\n        var overlap = !(ax1 < bx0 || bx1 < ax0 || ay1 < by0 || by1 < ay0);\n        if (mtv) {\n            var dMin = Infinity;\n            var dMax = 0;\n            var d0 = Math.abs(ax1 - bx0);\n            var d1 = Math.abs(bx1 - ax0);\n            var d2 = Math.abs(ay1 - by0);\n            var d3 = Math.abs(by1 - ay0);\n            var dx = Math.min(d0, d1);\n            var dy = Math.min(d2, d3);\n            if (ax1 < bx0 || bx1 < ax0) {\n                if (dx > dMax) {\n                    dMax = dx;\n                    if (d0 < d1) {\n                        Point.set(maxTv, -d0, 0);\n                    }\n                    else {\n                        Point.set(maxTv, d1, 0);\n                    }\n                }\n            }\n            else {\n                if (dx < dMin) {\n                    dMin = dx;\n                    if (d0 < d1) {\n                        Point.set(minTv, d0, 0);\n                    }\n                    else {\n                        Point.set(minTv, -d1, 0);\n                    }\n                }\n            }\n            if (ay1 < by0 || by1 < ay0) {\n                if (dy > dMax) {\n                    dMax = dy;\n                    if (d2 < d3) {\n                        Point.set(maxTv, 0, -d2);\n                    }\n                    else {\n                        Point.set(maxTv, 0, d3);\n                    }\n                }\n            }\n            else {\n                if (dx < dMin) {\n                    dMin = dx;\n                    if (d2 < d3) {\n                        Point.set(minTv, 0, d2);\n                    }\n                    else {\n                        Point.set(minTv, 0, -d3);\n                    }\n                }\n            }\n        }\n        if (mtv) {\n            Point.copy(mtv, overlap ? minTv : maxTv);\n        }\n        return overlap;\n    };\n    BoundingRect.prototype.contain = function (x, y) {\n        var rect = this;\n        return x >= rect.x\n            && x <= (rect.x + rect.width)\n            && y >= rect.y\n            && y <= (rect.y + rect.height);\n    };\n    BoundingRect.prototype.clone = function () {\n        return new BoundingRect(this.x, this.y, this.width, this.height);\n    };\n    BoundingRect.prototype.copy = function (other) {\n        BoundingRect.copy(this, other);\n    };\n    BoundingRect.prototype.plain = function () {\n        return {\n            x: this.x,\n            y: this.y,\n            width: this.width,\n            height: this.height\n        };\n    };\n    BoundingRect.prototype.isFinite = function () {\n        return isFinite(this.x)\n            && isFinite(this.y)\n            && isFinite(this.width)\n            && isFinite(this.height);\n    };\n    BoundingRect.prototype.isZero = function () {\n        return this.width === 0 || this.height === 0;\n    };\n    BoundingRect.create = function (rect) {\n        return new BoundingRect(rect.x, rect.y, rect.width, rect.height);\n    };\n    BoundingRect.copy = function (target, source) {\n        target.x = source.x;\n        target.y = source.y;\n        target.width = source.width;\n        target.height = source.height;\n    };\n    BoundingRect.applyTransform = function (target, source, m) {\n        if (!m) {\n            if (target !== source) {\n                BoundingRect.copy(target, source);\n            }\n            return;\n        }\n        if (m[1] < 1e-5 && m[1] > -1e-5 && m[2] < 1e-5 && m[2] > -1e-5) {\n            var sx = m[0];\n            var sy = m[3];\n            var tx = m[4];\n            var ty = m[5];\n            target.x = source.x * sx + tx;\n            target.y = source.y * sy + ty;\n            target.width = source.width * sx;\n            target.height = source.height * sy;\n            if (target.width < 0) {\n                target.x += target.width;\n                target.width = -target.width;\n            }\n            if (target.height < 0) {\n                target.y += target.height;\n                target.height = -target.height;\n            }\n            return;\n        }\n        lt.x = lb.x = source.x;\n        lt.y = rt.y = source.y;\n        rb.x = rt.x = source.x + source.width;\n        rb.y = lb.y = source.y + source.height;\n        lt.transform(m);\n        rt.transform(m);\n        rb.transform(m);\n        lb.transform(m);\n        target.x = mathMin(lt.x, rb.x, lb.x, rt.x);\n        target.y = mathMin(lt.y, rb.y, lb.y, rt.y);\n        var maxX = mathMax(lt.x, rb.x, lb.x, rt.x);\n        var maxY = mathMax(lt.y, rb.y, lb.y, rt.y);\n        target.width = maxX - target.x;\n        target.height = maxY - target.y;\n    };\n    return BoundingRect;\n}());\nexport default BoundingRect;\n"], "mappings": "AAAA,OAAO,KAAKA,MAAM,MAAM,aAAa;AACrC,OAAOC,KAAK,MAAM,YAAY;AAC9B,IAAIC,OAAO,GAAGC,IAAI,CAACC,GAAG;AACtB,IAAIC,OAAO,GAAGF,IAAI,CAACG,GAAG;AACtB,IAAIC,EAAE,GAAG,IAAIN,KAAK,CAAC,CAAC;AACpB,IAAIO,EAAE,GAAG,IAAIP,KAAK,CAAC,CAAC;AACpB,IAAIQ,EAAE,GAAG,IAAIR,KAAK,CAAC,CAAC;AACpB,IAAIS,EAAE,GAAG,IAAIT,KAAK,CAAC,CAAC;AACpB,IAAIU,KAAK,GAAG,IAAIV,KAAK,CAAC,CAAC;AACvB,IAAIW,KAAK,GAAG,IAAIX,KAAK,CAAC,CAAC;AACvB,IAAIY,YAAY,GAAI,YAAY;EAC5B,SAASA,YAAYA,CAACC,CAAC,EAAEC,CAAC,EAAEC,KAAK,EAAEC,MAAM,EAAE;IACvC,IAAID,KAAK,GAAG,CAAC,EAAE;MACXF,CAAC,GAAGA,CAAC,GAAGE,KAAK;MACbA,KAAK,GAAG,CAACA,KAAK;IAClB;IACA,IAAIC,MAAM,GAAG,CAAC,EAAE;MACZF,CAAC,GAAGA,CAAC,GAAGE,MAAM;MACdA,MAAM,GAAG,CAACA,MAAM;IACpB;IACA,IAAI,CAACH,CAAC,GAAGA,CAAC;IACV,IAAI,CAACC,CAAC,GAAGA,CAAC;IACV,IAAI,CAACC,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACC,MAAM,GAAGA,MAAM;EACxB;EACAJ,YAAY,CAACK,SAAS,CAACC,KAAK,GAAG,UAAUC,KAAK,EAAE;IAC5C,IAAIN,CAAC,GAAGZ,OAAO,CAACkB,KAAK,CAACN,CAAC,EAAE,IAAI,CAACA,CAAC,CAAC;IAChC,IAAIC,CAAC,GAAGb,OAAO,CAACkB,KAAK,CAACL,CAAC,EAAE,IAAI,CAACA,CAAC,CAAC;IAChC,IAAIM,QAAQ,CAAC,IAAI,CAACP,CAAC,CAAC,IAAIO,QAAQ,CAAC,IAAI,CAACL,KAAK,CAAC,EAAE;MAC1C,IAAI,CAACA,KAAK,GAAGX,OAAO,CAACe,KAAK,CAACN,CAAC,GAAGM,KAAK,CAACJ,KAAK,EAAE,IAAI,CAACF,CAAC,GAAG,IAAI,CAACE,KAAK,CAAC,GAAGF,CAAC;IACxE,CAAC,MACI;MACD,IAAI,CAACE,KAAK,GAAGI,KAAK,CAACJ,KAAK;IAC5B;IACA,IAAIK,QAAQ,CAAC,IAAI,CAACN,CAAC,CAAC,IAAIM,QAAQ,CAAC,IAAI,CAACJ,MAAM,CAAC,EAAE;MAC3C,IAAI,CAACA,MAAM,GAAGZ,OAAO,CAACe,KAAK,CAACL,CAAC,GAAGK,KAAK,CAACH,MAAM,EAAE,IAAI,CAACF,CAAC,GAAG,IAAI,CAACE,MAAM,CAAC,GAAGF,CAAC;IAC3E,CAAC,MACI;MACD,IAAI,CAACE,MAAM,GAAGG,KAAK,CAACH,MAAM;IAC9B;IACA,IAAI,CAACH,CAAC,GAAGA,CAAC;IACV,IAAI,CAACC,CAAC,GAAGA,CAAC;EACd,CAAC;EACDF,YAAY,CAACK,SAAS,CAACI,cAAc,GAAG,UAAUC,CAAC,EAAE;IACjDV,YAAY,CAACS,cAAc,CAAC,IAAI,EAAE,IAAI,EAAEC,CAAC,CAAC;EAC9C,CAAC;EACDV,YAAY,CAACK,SAAS,CAACM,kBAAkB,GAAG,UAAUC,CAAC,EAAE;IACrD,IAAIC,CAAC,GAAG,IAAI;IACZ,IAAIC,EAAE,GAAGF,CAAC,CAACT,KAAK,GAAGU,CAAC,CAACV,KAAK;IAC1B,IAAIY,EAAE,GAAGH,CAAC,CAACR,MAAM,GAAGS,CAAC,CAACT,MAAM;IAC5B,IAAIM,CAAC,GAAGvB,MAAM,CAAC6B,MAAM,CAAC,CAAC;IACvB7B,MAAM,CAAC8B,SAAS,CAACP,CAAC,EAAEA,CAAC,EAAE,CAAC,CAACG,CAAC,CAACZ,CAAC,EAAE,CAACY,CAAC,CAACX,CAAC,CAAC,CAAC;IACpCf,MAAM,CAAC+B,KAAK,CAACR,CAAC,EAAEA,CAAC,EAAE,CAACI,EAAE,EAAEC,EAAE,CAAC,CAAC;IAC5B5B,MAAM,CAAC8B,SAAS,CAACP,CAAC,EAAEA,CAAC,EAAE,CAACE,CAAC,CAACX,CAAC,EAAEW,CAAC,CAACV,CAAC,CAAC,CAAC;IAClC,OAAOQ,CAAC;EACZ,CAAC;EACDV,YAAY,CAACK,SAAS,CAACc,SAAS,GAAG,UAAUP,CAAC,EAAEQ,GAAG,EAAE;IACjD,IAAI,CAACR,CAAC,EAAE;MACJ,OAAO,KAAK;IAChB;IACA,IAAI,EAAEA,CAAC,YAAYZ,YAAY,CAAC,EAAE;MAC9BY,CAAC,GAAGZ,YAAY,CAACgB,MAAM,CAACJ,CAAC,CAAC;IAC9B;IACA,IAAIC,CAAC,GAAG,IAAI;IACZ,IAAIQ,GAAG,GAAGR,CAAC,CAACZ,CAAC;IACb,IAAIqB,GAAG,GAAGT,CAAC,CAACZ,CAAC,GAAGY,CAAC,CAACV,KAAK;IACvB,IAAIoB,GAAG,GAAGV,CAAC,CAACX,CAAC;IACb,IAAIsB,GAAG,GAAGX,CAAC,CAACX,CAAC,GAAGW,CAAC,CAACT,MAAM;IACxB,IAAIqB,GAAG,GAAGb,CAAC,CAACX,CAAC;IACb,IAAIyB,GAAG,GAAGd,CAAC,CAACX,CAAC,GAAGW,CAAC,CAACT,KAAK;IACvB,IAAIwB,GAAG,GAAGf,CAAC,CAACV,CAAC;IACb,IAAI0B,GAAG,GAAGhB,CAAC,CAACV,CAAC,GAAGU,CAAC,CAACR,MAAM;IACxB,IAAIyB,OAAO,GAAG,EAAEP,GAAG,GAAGG,GAAG,IAAIC,GAAG,GAAGL,GAAG,IAAIG,GAAG,GAAGG,GAAG,IAAIC,GAAG,GAAGL,GAAG,CAAC;IACjE,IAAIH,GAAG,EAAE;MACL,IAAIU,IAAI,GAAGC,QAAQ;MACnB,IAAIC,IAAI,GAAG,CAAC;MACZ,IAAIC,EAAE,GAAG3C,IAAI,CAAC4C,GAAG,CAACZ,GAAG,GAAGG,GAAG,CAAC;MAC5B,IAAIU,EAAE,GAAG7C,IAAI,CAAC4C,GAAG,CAACR,GAAG,GAAGL,GAAG,CAAC;MAC5B,IAAIe,EAAE,GAAG9C,IAAI,CAAC4C,GAAG,CAACV,GAAG,GAAGG,GAAG,CAAC;MAC5B,IAAIU,EAAE,GAAG/C,IAAI,CAAC4C,GAAG,CAACN,GAAG,GAAGL,GAAG,CAAC;MAC5B,IAAIe,EAAE,GAAGhD,IAAI,CAACC,GAAG,CAAC0C,EAAE,EAAEE,EAAE,CAAC;MACzB,IAAII,EAAE,GAAGjD,IAAI,CAACC,GAAG,CAAC6C,EAAE,EAAEC,EAAE,CAAC;MACzB,IAAIf,GAAG,GAAGG,GAAG,IAAIC,GAAG,GAAGL,GAAG,EAAE;QACxB,IAAIiB,EAAE,GAAGN,IAAI,EAAE;UACXA,IAAI,GAAGM,EAAE;UACT,IAAIL,EAAE,GAAGE,EAAE,EAAE;YACT/C,KAAK,CAACoD,GAAG,CAACzC,KAAK,EAAE,CAACkC,EAAE,EAAE,CAAC,CAAC;UAC5B,CAAC,MACI;YACD7C,KAAK,CAACoD,GAAG,CAACzC,KAAK,EAAEoC,EAAE,EAAE,CAAC,CAAC;UAC3B;QACJ;MACJ,CAAC,MACI;QACD,IAAIG,EAAE,GAAGR,IAAI,EAAE;UACXA,IAAI,GAAGQ,EAAE;UACT,IAAIL,EAAE,GAAGE,EAAE,EAAE;YACT/C,KAAK,CAACoD,GAAG,CAAC1C,KAAK,EAAEmC,EAAE,EAAE,CAAC,CAAC;UAC3B,CAAC,MACI;YACD7C,KAAK,CAACoD,GAAG,CAAC1C,KAAK,EAAE,CAACqC,EAAE,EAAE,CAAC,CAAC;UAC5B;QACJ;MACJ;MACA,IAAIX,GAAG,GAAGG,GAAG,IAAIC,GAAG,GAAGL,GAAG,EAAE;QACxB,IAAIgB,EAAE,GAAGP,IAAI,EAAE;UACXA,IAAI,GAAGO,EAAE;UACT,IAAIH,EAAE,GAAGC,EAAE,EAAE;YACTjD,KAAK,CAACoD,GAAG,CAACzC,KAAK,EAAE,CAAC,EAAE,CAACqC,EAAE,CAAC;UAC5B,CAAC,MACI;YACDhD,KAAK,CAACoD,GAAG,CAACzC,KAAK,EAAE,CAAC,EAAEsC,EAAE,CAAC;UAC3B;QACJ;MACJ,CAAC,MACI;QACD,IAAIC,EAAE,GAAGR,IAAI,EAAE;UACXA,IAAI,GAAGQ,EAAE;UACT,IAAIF,EAAE,GAAGC,EAAE,EAAE;YACTjD,KAAK,CAACoD,GAAG,CAAC1C,KAAK,EAAE,CAAC,EAAEsC,EAAE,CAAC;UAC3B,CAAC,MACI;YACDhD,KAAK,CAACoD,GAAG,CAAC1C,KAAK,EAAE,CAAC,EAAE,CAACuC,EAAE,CAAC;UAC5B;QACJ;MACJ;IACJ;IACA,IAAIjB,GAAG,EAAE;MACLhC,KAAK,CAACqD,IAAI,CAACrB,GAAG,EAAES,OAAO,GAAG/B,KAAK,GAAGC,KAAK,CAAC;IAC5C;IACA,OAAO8B,OAAO;EAClB,CAAC;EACD7B,YAAY,CAACK,SAAS,CAACqC,OAAO,GAAG,UAAUzC,CAAC,EAAEC,CAAC,EAAE;IAC7C,IAAIyC,IAAI,GAAG,IAAI;IACf,OAAO1C,CAAC,IAAI0C,IAAI,CAAC1C,CAAC,IACXA,CAAC,IAAK0C,IAAI,CAAC1C,CAAC,GAAG0C,IAAI,CAACxC,KAAM,IAC1BD,CAAC,IAAIyC,IAAI,CAACzC,CAAC,IACXA,CAAC,IAAKyC,IAAI,CAACzC,CAAC,GAAGyC,IAAI,CAACvC,MAAO;EACtC,CAAC;EACDJ,YAAY,CAACK,SAAS,CAACuC,KAAK,GAAG,YAAY;IACvC,OAAO,IAAI5C,YAAY,CAAC,IAAI,CAACC,CAAC,EAAE,IAAI,CAACC,CAAC,EAAE,IAAI,CAACC,KAAK,EAAE,IAAI,CAACC,MAAM,CAAC;EACpE,CAAC;EACDJ,YAAY,CAACK,SAAS,CAACoC,IAAI,GAAG,UAAUlC,KAAK,EAAE;IAC3CP,YAAY,CAACyC,IAAI,CAAC,IAAI,EAAElC,KAAK,CAAC;EAClC,CAAC;EACDP,YAAY,CAACK,SAAS,CAACwC,KAAK,GAAG,YAAY;IACvC,OAAO;MACH5C,CAAC,EAAE,IAAI,CAACA,CAAC;MACTC,CAAC,EAAE,IAAI,CAACA,CAAC;MACTC,KAAK,EAAE,IAAI,CAACA,KAAK;MACjBC,MAAM,EAAE,IAAI,CAACA;IACjB,CAAC;EACL,CAAC;EACDJ,YAAY,CAACK,SAAS,CAACG,QAAQ,GAAG,YAAY;IAC1C,OAAOA,QAAQ,CAAC,IAAI,CAACP,CAAC,CAAC,IAChBO,QAAQ,CAAC,IAAI,CAACN,CAAC,CAAC,IAChBM,QAAQ,CAAC,IAAI,CAACL,KAAK,CAAC,IACpBK,QAAQ,CAAC,IAAI,CAACJ,MAAM,CAAC;EAChC,CAAC;EACDJ,YAAY,CAACK,SAAS,CAACyC,MAAM,GAAG,YAAY;IACxC,OAAO,IAAI,CAAC3C,KAAK,KAAK,CAAC,IAAI,IAAI,CAACC,MAAM,KAAK,CAAC;EAChD,CAAC;EACDJ,YAAY,CAACgB,MAAM,GAAG,UAAU2B,IAAI,EAAE;IAClC,OAAO,IAAI3C,YAAY,CAAC2C,IAAI,CAAC1C,CAAC,EAAE0C,IAAI,CAACzC,CAAC,EAAEyC,IAAI,CAACxC,KAAK,EAAEwC,IAAI,CAACvC,MAAM,CAAC;EACpE,CAAC;EACDJ,YAAY,CAACyC,IAAI,GAAG,UAAUM,MAAM,EAAEC,MAAM,EAAE;IAC1CD,MAAM,CAAC9C,CAAC,GAAG+C,MAAM,CAAC/C,CAAC;IACnB8C,MAAM,CAAC7C,CAAC,GAAG8C,MAAM,CAAC9C,CAAC;IACnB6C,MAAM,CAAC5C,KAAK,GAAG6C,MAAM,CAAC7C,KAAK;IAC3B4C,MAAM,CAAC3C,MAAM,GAAG4C,MAAM,CAAC5C,MAAM;EACjC,CAAC;EACDJ,YAAY,CAACS,cAAc,GAAG,UAAUsC,MAAM,EAAEC,MAAM,EAAEtC,CAAC,EAAE;IACvD,IAAI,CAACA,CAAC,EAAE;MACJ,IAAIqC,MAAM,KAAKC,MAAM,EAAE;QACnBhD,YAAY,CAACyC,IAAI,CAACM,MAAM,EAAEC,MAAM,CAAC;MACrC;MACA;IACJ;IACA,IAAItC,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,IAAIA,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,IAAIA,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,IAAIA,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,EAAE;MAC5D,IAAII,EAAE,GAAGJ,CAAC,CAAC,CAAC,CAAC;MACb,IAAIK,EAAE,GAAGL,CAAC,CAAC,CAAC,CAAC;MACb,IAAIuC,EAAE,GAAGvC,CAAC,CAAC,CAAC,CAAC;MACb,IAAIwC,EAAE,GAAGxC,CAAC,CAAC,CAAC,CAAC;MACbqC,MAAM,CAAC9C,CAAC,GAAG+C,MAAM,CAAC/C,CAAC,GAAGa,EAAE,GAAGmC,EAAE;MAC7BF,MAAM,CAAC7C,CAAC,GAAG8C,MAAM,CAAC9C,CAAC,GAAGa,EAAE,GAAGmC,EAAE;MAC7BH,MAAM,CAAC5C,KAAK,GAAG6C,MAAM,CAAC7C,KAAK,GAAGW,EAAE;MAChCiC,MAAM,CAAC3C,MAAM,GAAG4C,MAAM,CAAC5C,MAAM,GAAGW,EAAE;MAClC,IAAIgC,MAAM,CAAC5C,KAAK,GAAG,CAAC,EAAE;QAClB4C,MAAM,CAAC9C,CAAC,IAAI8C,MAAM,CAAC5C,KAAK;QACxB4C,MAAM,CAAC5C,KAAK,GAAG,CAAC4C,MAAM,CAAC5C,KAAK;MAChC;MACA,IAAI4C,MAAM,CAAC3C,MAAM,GAAG,CAAC,EAAE;QACnB2C,MAAM,CAAC7C,CAAC,IAAI6C,MAAM,CAAC3C,MAAM;QACzB2C,MAAM,CAAC3C,MAAM,GAAG,CAAC2C,MAAM,CAAC3C,MAAM;MAClC;MACA;IACJ;IACAV,EAAE,CAACO,CAAC,GAAGL,EAAE,CAACK,CAAC,GAAG+C,MAAM,CAAC/C,CAAC;IACtBP,EAAE,CAACQ,CAAC,GAAGL,EAAE,CAACK,CAAC,GAAG8C,MAAM,CAAC9C,CAAC;IACtBP,EAAE,CAACM,CAAC,GAAGJ,EAAE,CAACI,CAAC,GAAG+C,MAAM,CAAC/C,CAAC,GAAG+C,MAAM,CAAC7C,KAAK;IACrCR,EAAE,CAACO,CAAC,GAAGN,EAAE,CAACM,CAAC,GAAG8C,MAAM,CAAC9C,CAAC,GAAG8C,MAAM,CAAC5C,MAAM;IACtCV,EAAE,CAACyD,SAAS,CAACzC,CAAC,CAAC;IACfb,EAAE,CAACsD,SAAS,CAACzC,CAAC,CAAC;IACff,EAAE,CAACwD,SAAS,CAACzC,CAAC,CAAC;IACfd,EAAE,CAACuD,SAAS,CAACzC,CAAC,CAAC;IACfqC,MAAM,CAAC9C,CAAC,GAAGZ,OAAO,CAACK,EAAE,CAACO,CAAC,EAAEN,EAAE,CAACM,CAAC,EAAEL,EAAE,CAACK,CAAC,EAAEJ,EAAE,CAACI,CAAC,CAAC;IAC1C8C,MAAM,CAAC7C,CAAC,GAAGb,OAAO,CAACK,EAAE,CAACQ,CAAC,EAAEP,EAAE,CAACO,CAAC,EAAEN,EAAE,CAACM,CAAC,EAAEL,EAAE,CAACK,CAAC,CAAC;IAC1C,IAAIkD,IAAI,GAAG5D,OAAO,CAACE,EAAE,CAACO,CAAC,EAAEN,EAAE,CAACM,CAAC,EAAEL,EAAE,CAACK,CAAC,EAAEJ,EAAE,CAACI,CAAC,CAAC;IAC1C,IAAIoD,IAAI,GAAG7D,OAAO,CAACE,EAAE,CAACQ,CAAC,EAAEP,EAAE,CAACO,CAAC,EAAEN,EAAE,CAACM,CAAC,EAAEL,EAAE,CAACK,CAAC,CAAC;IAC1C6C,MAAM,CAAC5C,KAAK,GAAGiD,IAAI,GAAGL,MAAM,CAAC9C,CAAC;IAC9B8C,MAAM,CAAC3C,MAAM,GAAGiD,IAAI,GAAGN,MAAM,CAAC7C,CAAC;EACnC,CAAC;EACD,OAAOF,YAAY;AACvB,CAAC,CAAC,CAAE;AACJ,eAAeA,YAAY"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}