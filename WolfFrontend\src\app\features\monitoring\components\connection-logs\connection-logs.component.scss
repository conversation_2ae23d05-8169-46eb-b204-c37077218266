.logs-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.logs-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 0 15px 0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  margin-bottom: 15px;
}

.logs-count {
  display: flex;
  align-items: center;
  gap: 8px;
}

.count {
  background: linear-gradient(135deg, #4ecdc4, #44a08d);
  color: white;
  padding: 4px 12px;
  border-radius: 15px;
  font-weight: bold;
  font-size: 0.9rem;
  box-shadow: 0 2px 8px rgba(78, 205, 196, 0.3);
}

.label {
  color: #ccc;
  font-size: 0.8rem;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.auto-scroll-toggle {
  display: flex;
  align-items: center;
  gap: 6px;
  cursor: pointer;
  padding: 6px 12px;
  border-radius: 20px;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;

  &:hover {
    background: rgba(255, 255, 255, 0.15);
    transform: translateY(-1px);
  }
}

.toggle-icon {
  font-size: 1rem;
  transition: all 0.3s ease;
  
  &.active {
    transform: scale(1.2);
    filter: drop-shadow(0 0 5px rgba(78, 205, 196, 0.7));
  }
}

.toggle-text {
  font-size: 0.8rem;
  color: #ccc;
  font-weight: 500;
}

.logs-list {
  flex: 1;
  overflow-y: auto;
  padding-right: 8px;
  
  /* Custom scrollbar */
  &::-webkit-scrollbar {
    width: 6px;
  }
  
  &::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 3px;
  }
  
  &::-webkit-scrollbar-thumb {
    background: rgba(78, 205, 196, 0.5);
    border-radius: 3px;
    
    &:hover {
      background: rgba(78, 205, 196, 0.7);
    }
  }
}

.log-entry {
  display: grid;
  grid-template-columns: 60px 30px 1fr 8px;
  gap: 10px;
  align-items: flex-start;
  padding: 10px;
  margin-bottom: 8px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 8px;
  border-left: 3px solid transparent;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;

  &:hover {
    background: rgba(255, 255, 255, 0.1);
    transform: translateX(3px);
  }

  &.success {
    border-left-color: #4ecdc4;
    
    .log-indicator {
      background: #4ecdc4;
      box-shadow: 0 0 8px rgba(78, 205, 196, 0.5);
    }
  }

  &.warning {
    border-left-color: #ffa726;
    
    .log-indicator {
      background: #ffa726;
      box-shadow: 0 0 8px rgba(255, 167, 38, 0.5);
    }
  }

  &.error {
    border-left-color: #ff6b6b;
    
    .log-indicator {
      background: #ff6b6b;
      box-shadow: 0 0 8px rgba(255, 107, 107, 0.5);
    }
  }

  &.info {
    border-left-color: #64b5f6;
    
    .log-indicator {
      background: #64b5f6;
      box-shadow: 0 0 8px rgba(100, 181, 246, 0.5);
    }
  }

  &.repeated {
    background: rgba(255, 167, 38, 0.1);
    border: 1px solid rgba(255, 167, 38, 0.3);
  }

  &.new {
    animation: slideInRight 0.5s ease;
    background: rgba(78, 205, 196, 0.1);
    border: 1px solid rgba(78, 205, 196, 0.3);
  }
}

.log-time {
  font-size: 0.75rem;
  color: #999;
  font-family: 'Courier New', monospace;
  font-weight: 500;
  line-height: 1.2;
}

.log-flag {
  font-size: 1.2rem;
  text-align: center;
  filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.3));
}

.log-content {
  display: flex;
  flex-direction: column;
  gap: 4px;
  min-width: 0;
}

.log-main {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
  align-items: center;
  font-size: 0.85rem;
  line-height: 1.3;
}

.log-country {
  color: #4ecdc4;
  font-weight: 600;
}

.log-ip {
  color: #ff6b6b;
  font-family: 'Courier New', monospace;
  font-size: 0.8rem;
}

.log-action {
  color: #ccc;
  font-style: italic;
}

.log-page {
  color: #ffa726;
  font-weight: 500;
  background: rgba(255, 167, 38, 0.2);
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 0.8rem;
}

.log-details {
  display: flex;
  gap: 8px;
  align-items: center;
}

.access-count {
  font-size: 0.75rem;
  color: #ff6b6b;
  font-weight: 500;
}

.repeated-badge {
  background: linear-gradient(135deg, #ffa726, #ff8f00);
  color: white;
  padding: 2px 8px;
  border-radius: 10px;
  font-size: 0.7rem;
  font-weight: bold;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  box-shadow: 0 2px 4px rgba(255, 167, 38, 0.3);
}

.log-indicator {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  margin-top: 6px;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.7;
    transform: scale(1.2);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: rgba(255, 255, 255, 0.6);
  text-align: center;
}

.empty-icon {
  font-size: 3rem;
  margin-bottom: 10px;
  opacity: 0.7;
}

.empty-text {
  font-size: 1rem;
  font-weight: 500;
}

/* Responsividade */
@media (max-width: 768px) {
  .log-entry {
    grid-template-columns: 50px 25px 1fr 6px;
    gap: 8px;
    padding: 8px;
  }
  
  .log-time {
    font-size: 0.7rem;
  }
  
  .log-flag {
    font-size: 1rem;
  }
  
  .log-main {
    font-size: 0.8rem;
    gap: 4px;
  }
  
  .logs-header {
    flex-direction: column;
    gap: 10px;
    align-items: flex-start;
  }
}
