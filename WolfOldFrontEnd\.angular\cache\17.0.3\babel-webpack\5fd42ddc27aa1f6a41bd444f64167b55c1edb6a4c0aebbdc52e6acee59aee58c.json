{"ast": null, "code": "var PI2 = Math.PI * 2;\nexport function normalizeRadian(angle) {\n  angle %= PI2;\n  if (angle < 0) {\n    angle += PI2;\n  }\n  return angle;\n}", "map": {"version": 3, "names": ["PI2", "Math", "PI", "normalizeRadian", "angle"], "sources": ["C:/WolfSystemV2/WolfOldFrontEnd/node_modules/zrender/lib/contain/util.js"], "sourcesContent": ["var PI2 = Math.PI * 2;\nexport function normalizeRadian(angle) {\n    angle %= PI2;\n    if (angle < 0) {\n        angle += PI2;\n    }\n    return angle;\n}\n"], "mappings": "AAAA,IAAIA,GAAG,GAAGC,IAAI,CAACC,EAAE,GAAG,CAAC;AACrB,OAAO,SAASC,eAAeA,CAACC,KAAK,EAAE;EACnCA,KAAK,IAAIJ,GAAG;EACZ,IAAII,KAAK,GAAG,CAAC,EAAE;IACXA,KAAK,IAAIJ,GAAG;EAChB;EACA,OAAOI,KAAK;AAChB"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}