{"ast": null, "code": "/** Used to match template delimiters. */\nvar reEscape = /<%-([\\s\\S]+?)%>/g;\nexport default reEscape;", "map": {"version": 3, "names": ["reEscape"], "sources": ["C:/WolfSystemV2/WolfOldFrontEnd/node_modules/lodash-es/_reEscape.js"], "sourcesContent": ["/** Used to match template delimiters. */\nvar reEscape = /<%-([\\s\\S]+?)%>/g;\n\nexport default reEscape;\n"], "mappings": "AAAA;AACA,IAAIA,QAAQ,GAAG,kBAAkB;AAEjC,eAAeA,QAAQ"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}