{"ast": null, "code": "/**\n * @license Copyright (c) 2003-2023, CKSource Holding sp. z o.o. All rights reserved.\n * For licensing, see LICENSE.md or https://ckeditor.com/legal/ckeditor-oss-license\n */\n/**\n * @module watchdog/utils/areconnectedthroughproperties\n */\n/* globals console */\nimport getSubNodes from './getsubnodes';\n/**\n * Traverses both structures to find out whether there is a reference that is shared between both structures.\n */\nexport default function areConnectedThroughProperties(target1, target2, excludedNodes = new Set()) {\n  if (target1 === target2 && isObject(target1)) {\n    return true;\n  }\n  // @if CK_DEBUG_WATCHDOG // return checkConnectionBetweenProps( target1, target2, excludedNodes );\n  const subNodes1 = getSubNodes(target1, excludedNodes);\n  const subNodes2 = getSubNodes(target2, excludedNodes);\n  for (const node of subNodes1) {\n    if (subNodes2.has(node)) {\n      return true;\n    }\n  }\n  return false;\n}\n/* istanbul ignore next -- @preserve */\n// eslint-disable-next-line\nfunction checkConnectionBetweenProps(target1, target2, excludedNodes) {\n  const {\n    subNodes: subNodes1,\n    prevNodeMap: prevNodeMap1\n  } = getSubNodes(target1, excludedNodes.subNodes);\n  const {\n    subNodes: subNodes2,\n    prevNodeMap: prevNodeMap2\n  } = getSubNodes(target2, excludedNodes.subNodes);\n  for (const sharedNode of subNodes1) {\n    if (subNodes2.has(sharedNode)) {\n      const connection = [];\n      connection.push(sharedNode);\n      let node = prevNodeMap1.get(sharedNode);\n      while (node && node !== target1) {\n        connection.push(node);\n        node = prevNodeMap1.get(node);\n      }\n      node = prevNodeMap2.get(sharedNode);\n      while (node && node !== target2) {\n        connection.unshift(node);\n        node = prevNodeMap2.get(node);\n      }\n      console.log('--------');\n      console.log({\n        target1\n      });\n      console.log({\n        sharedNode\n      });\n      console.log({\n        target2\n      });\n      console.log({\n        connection\n      });\n      return true;\n    }\n  }\n  return false;\n}\nfunction isObject(structure) {\n  return typeof structure === 'object' && structure !== null;\n}", "map": {"version": 3, "names": ["getSubNodes", "areConnectedThroughProperties", "target1", "target2", "excludedNodes", "Set", "isObject", "subNodes1", "subNodes2", "node", "has", "checkConnectionBetweenProps", "subNodes", "prevNodeMap", "prevNodeMap1", "prevNodeMap2", "sharedNode", "connection", "push", "get", "unshift", "console", "log", "structure"], "sources": ["C:/WolfSystemV2/WolfOldFrontEnd/node_modules/@ckeditor/ckeditor5-watchdog/src/utils/areconnectedthroughproperties.js"], "sourcesContent": ["/**\n * @license Copyright (c) 2003-2023, CKSource Holding sp. z o.o. All rights reserved.\n * For licensing, see LICENSE.md or https://ckeditor.com/legal/ckeditor-oss-license\n */\n/**\n * @module watchdog/utils/areconnectedthroughproperties\n */\n/* globals console */\nimport getSubNodes from './getsubnodes';\n/**\n * Traverses both structures to find out whether there is a reference that is shared between both structures.\n */\nexport default function areConnectedThroughProperties(target1, target2, excludedNodes = new Set()) {\n    if (target1 === target2 && isObject(target1)) {\n        return true;\n    }\n    // @if CK_DEBUG_WATCHDOG // return checkConnectionBetweenProps( target1, target2, excludedNodes );\n    const subNodes1 = getSubNodes(target1, excludedNodes);\n    const subNodes2 = getSubNodes(target2, excludedNodes);\n    for (const node of subNodes1) {\n        if (subNodes2.has(node)) {\n            return true;\n        }\n    }\n    return false;\n}\n/* istanbul ignore next -- @preserve */\n// eslint-disable-next-line\nfunction checkConnectionBetweenProps(target1, target2, excludedNodes) {\n    const { subNodes: subNodes1, prevNodeMap: prevNodeMap1 } = getSubNodes(target1, excludedNodes.subNodes);\n    const { subNodes: subNodes2, prevNodeMap: prevNodeMap2 } = getSubNodes(target2, excludedNodes.subNodes);\n    for (const sharedNode of subNodes1) {\n        if (subNodes2.has(sharedNode)) {\n            const connection = [];\n            connection.push(sharedNode);\n            let node = prevNodeMap1.get(sharedNode);\n            while (node && node !== target1) {\n                connection.push(node);\n                node = prevNodeMap1.get(node);\n            }\n            node = prevNodeMap2.get(sharedNode);\n            while (node && node !== target2) {\n                connection.unshift(node);\n                node = prevNodeMap2.get(node);\n            }\n            console.log('--------');\n            console.log({ target1 });\n            console.log({ sharedNode });\n            console.log({ target2 });\n            console.log({ connection });\n            return true;\n        }\n    }\n    return false;\n}\nfunction isObject(structure) {\n    return typeof structure === 'object' && structure !== null;\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAOA,WAAW,MAAM,eAAe;AACvC;AACA;AACA;AACA,eAAe,SAASC,6BAA6BA,CAACC,OAAO,EAAEC,OAAO,EAAEC,aAAa,GAAG,IAAIC,GAAG,CAAC,CAAC,EAAE;EAC/F,IAAIH,OAAO,KAAKC,OAAO,IAAIG,QAAQ,CAACJ,OAAO,CAAC,EAAE;IAC1C,OAAO,IAAI;EACf;EACA;EACA,MAAMK,SAAS,GAAGP,WAAW,CAACE,OAAO,EAAEE,aAAa,CAAC;EACrD,MAAMI,SAAS,GAAGR,WAAW,CAACG,OAAO,EAAEC,aAAa,CAAC;EACrD,KAAK,MAAMK,IAAI,IAAIF,SAAS,EAAE;IAC1B,IAAIC,SAAS,CAACE,GAAG,CAACD,IAAI,CAAC,EAAE;MACrB,OAAO,IAAI;IACf;EACJ;EACA,OAAO,KAAK;AAChB;AACA;AACA;AACA,SAASE,2BAA2BA,CAACT,OAAO,EAAEC,OAAO,EAAEC,aAAa,EAAE;EAClE,MAAM;IAAEQ,QAAQ,EAAEL,SAAS;IAAEM,WAAW,EAAEC;EAAa,CAAC,GAAGd,WAAW,CAACE,OAAO,EAAEE,aAAa,CAACQ,QAAQ,CAAC;EACvG,MAAM;IAAEA,QAAQ,EAAEJ,SAAS;IAAEK,WAAW,EAAEE;EAAa,CAAC,GAAGf,WAAW,CAACG,OAAO,EAAEC,aAAa,CAACQ,QAAQ,CAAC;EACvG,KAAK,MAAMI,UAAU,IAAIT,SAAS,EAAE;IAChC,IAAIC,SAAS,CAACE,GAAG,CAACM,UAAU,CAAC,EAAE;MAC3B,MAAMC,UAAU,GAAG,EAAE;MACrBA,UAAU,CAACC,IAAI,CAACF,UAAU,CAAC;MAC3B,IAAIP,IAAI,GAAGK,YAAY,CAACK,GAAG,CAACH,UAAU,CAAC;MACvC,OAAOP,IAAI,IAAIA,IAAI,KAAKP,OAAO,EAAE;QAC7Be,UAAU,CAACC,IAAI,CAACT,IAAI,CAAC;QACrBA,IAAI,GAAGK,YAAY,CAACK,GAAG,CAACV,IAAI,CAAC;MACjC;MACAA,IAAI,GAAGM,YAAY,CAACI,GAAG,CAACH,UAAU,CAAC;MACnC,OAAOP,IAAI,IAAIA,IAAI,KAAKN,OAAO,EAAE;QAC7Bc,UAAU,CAACG,OAAO,CAACX,IAAI,CAAC;QACxBA,IAAI,GAAGM,YAAY,CAACI,GAAG,CAACV,IAAI,CAAC;MACjC;MACAY,OAAO,CAACC,GAAG,CAAC,UAAU,CAAC;MACvBD,OAAO,CAACC,GAAG,CAAC;QAAEpB;MAAQ,CAAC,CAAC;MACxBmB,OAAO,CAACC,GAAG,CAAC;QAAEN;MAAW,CAAC,CAAC;MAC3BK,OAAO,CAACC,GAAG,CAAC;QAAEnB;MAAQ,CAAC,CAAC;MACxBkB,OAAO,CAACC,GAAG,CAAC;QAAEL;MAAW,CAAC,CAAC;MAC3B,OAAO,IAAI;IACf;EACJ;EACA,OAAO,KAAK;AAChB;AACA,SAASX,QAAQA,CAACiB,SAAS,EAAE;EACzB,OAAO,OAAOA,SAAS,KAAK,QAAQ,IAAIA,SAAS,KAAK,IAAI;AAC9D"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}