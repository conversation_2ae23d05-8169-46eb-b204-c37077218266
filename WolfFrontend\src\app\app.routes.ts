import { Routes } from '@angular/router';
import { LoginComponent } from './features/login/login.component';
import { OperationsComponent } from './features/operations/operations.component';
import { MonitoringComponent } from './features/monitoring/monitoring.component';
import { AuthGuard } from './core/guards/auth.guard';

export const routes: Routes = [
  { path: 'login', component: LoginComponent },
  { path: 'monitoring', component: MonitoringComponent },
  { path: '', component: OperationsComponent, canActivate: [AuthGuard] },
  { path: '**', redirectTo: '' } // Redireciona rotas não encontradas para a raiz
];
