{"ast": null, "code": "import baseGet from './_baseGet.js';\nimport baseSlice from './_baseSlice.js';\n\n/**\n * Gets the parent value at `path` of `object`.\n *\n * @private\n * @param {Object} object The object to query.\n * @param {Array} path The path to get the parent value of.\n * @returns {*} Returns the parent value.\n */\nfunction parent(object, path) {\n  return path.length < 2 ? object : baseGet(object, baseSlice(path, 0, -1));\n}\nexport default parent;", "map": {"version": 3, "names": ["baseGet", "baseSlice", "parent", "object", "path", "length"], "sources": ["C:/WolfSystemV2/WolfOldFrontEnd/node_modules/lodash-es/_parent.js"], "sourcesContent": ["import baseGet from './_baseGet.js';\nimport baseSlice from './_baseSlice.js';\n\n/**\n * Gets the parent value at `path` of `object`.\n *\n * @private\n * @param {Object} object The object to query.\n * @param {Array} path The path to get the parent value of.\n * @returns {*} Returns the parent value.\n */\nfunction parent(object, path) {\n  return path.length < 2 ? object : baseGet(object, baseSlice(path, 0, -1));\n}\n\nexport default parent;\n"], "mappings": "AAAA,OAAOA,OAAO,MAAM,eAAe;AACnC,OAAOC,SAAS,MAAM,iBAAiB;;AAEvC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,MAAMA,CAACC,MAAM,EAAEC,IAAI,EAAE;EAC5B,OAAOA,IAAI,CAACC,MAAM,GAAG,CAAC,GAAGF,MAAM,GAAGH,OAAO,CAACG,MAAM,EAAEF,SAAS,CAACG,IAAI,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;AAC3E;AAEA,eAAeF,MAAM"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}