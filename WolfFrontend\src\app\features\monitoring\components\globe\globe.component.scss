.globe-container {
  position: relative;
  width: 100%;
  height: 100%;
  min-height: 400px;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  border-radius: 10px;
}

.globe-canvas {
  width: 100%;
  height: 100%;
  position: relative;
  cursor: grab;
  
  &:active {
    cursor: grabbing;
  }
}

.loading {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 15px;
  color: #4ecdc4;
  font-size: 1.1rem;
  z-index: 10;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 3px solid rgba(78, 205, 196, 0.3);
  border-top: 3px solid #4ecdc4;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.connection-counter {
  position: absolute;
  top: 20px;
  right: 20px;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(10px);
  padding: 12px 20px;
  border-radius: 25px;
  border: 2px solid #4ecdc4;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
  z-index: 5;
}

.counter-value {
  font-size: 1.8rem;
  font-weight: bold;
  color: #4ecdc4;
  text-shadow: 0 0 10px rgba(78, 205, 196, 0.5);
  line-height: 1;
}

.counter-label {
  font-size: 0.8rem;
  color: #ccc;
  text-transform: uppercase;
  letter-spacing: 1px;
  line-height: 1;
}

/* Efeitos visuais para os marcadores */
.globe-canvas canvas {
  filter: drop-shadow(0 0 20px rgba(78, 205, 196, 0.3));
}

/* Responsividade */
@media (max-width: 768px) {
  .connection-counter {
    top: 10px;
    right: 10px;
    padding: 8px 15px;
  }
  
  .counter-value {
    font-size: 1.4rem;
  }
  
  .counter-label {
    font-size: 0.7rem;
  }
}

/* Animações para os marcadores */
@keyframes pulse {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.2);
    opacity: 0.7;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

/* Estilo para indicar interatividade */
.globe-canvas:hover {
  filter: brightness(1.1);
}

/* Overlay para mostrar informações dos países */
.country-info {
  position: absolute;
  background: rgba(0, 0, 0, 0.9);
  color: white;
  padding: 10px 15px;
  border-radius: 8px;
  border: 1px solid #4ecdc4;
  font-size: 0.9rem;
  pointer-events: none;
  z-index: 20;
  transform: translate(-50%, -100%);
  margin-top: -10px;
  
  &::after {
    content: '';
    position: absolute;
    top: 100%;
    left: 50%;
    transform: translateX(-50%);
    border: 5px solid transparent;
    border-top-color: #4ecdc4;
  }
}

.country-name {
  font-weight: bold;
  color: #4ecdc4;
  margin-bottom: 4px;
}

.country-connections {
  color: #ff6b6b;
  font-size: 0.8rem;
}
