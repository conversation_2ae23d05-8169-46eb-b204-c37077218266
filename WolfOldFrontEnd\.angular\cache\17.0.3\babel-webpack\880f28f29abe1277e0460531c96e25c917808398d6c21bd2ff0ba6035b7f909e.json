{"ast": null, "code": "import getNative from './_getNative.js';\n\n/* Built-in method references that are verified to be native. */\nvar nativeCreate = getNative(Object, 'create');\nexport default nativeCreate;", "map": {"version": 3, "names": ["getNative", "nativeCreate", "Object"], "sources": ["C:/WolfSystemV2/WolfOldFrontEnd/node_modules/lodash-es/_nativeCreate.js"], "sourcesContent": ["import getNative from './_getNative.js';\n\n/* Built-in method references that are verified to be native. */\nvar nativeCreate = getNative(Object, 'create');\n\nexport default nativeCreate;\n"], "mappings": "AAAA,OAAOA,SAAS,MAAM,iBAAiB;;AAEvC;AACA,IAAIC,YAAY,GAAGD,SAAS,CAACE,MAAM,EAAE,QAAQ,CAAC;AAE9C,eAAeD,YAAY"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}