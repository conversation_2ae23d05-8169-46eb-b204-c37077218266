{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport ZRText from 'zrender/lib/graphic/Text.js';\nimport { getPaddingFromTooltipModel } from './tooltipMarkup.js';\nimport { throwError } from '../../util/log.js';\nvar TooltipRichContent = /** @class */\nfunction () {\n  function TooltipRichContent(api) {\n    this._show = false;\n    this._styleCoord = [0, 0, 0, 0];\n    this._alwaysShowContent = false;\n    this._enterable = true;\n    this._zr = api.getZr();\n    makeStyleCoord(this._styleCoord, this._zr, api.getWidth() / 2, api.getHeight() / 2);\n  }\n  /**\r\n   * Update when tooltip is rendered\r\n   */\n\n  TooltipRichContent.prototype.update = function (tooltipModel) {\n    var alwaysShowContent = tooltipModel.get('alwaysShowContent');\n    alwaysShowContent && this._moveIfResized(); // update alwaysShowContent\n\n    this._alwaysShowContent = alwaysShowContent;\n  };\n  TooltipRichContent.prototype.show = function () {\n    if (this._hideTimeout) {\n      clearTimeout(this._hideTimeout);\n    }\n    this.el.show();\n    this._show = true;\n  };\n  /**\r\n   * Set tooltip content\r\n   */\n\n  TooltipRichContent.prototype.setContent = function (content, markupStyleCreator, tooltipModel, borderColor, arrowPosition) {\n    var _this = this;\n    if (zrUtil.isObject(content)) {\n      throwError(process.env.NODE_ENV !== 'production' ? 'Passing DOM nodes as content is not supported in richText tooltip!' : '');\n    }\n    if (this.el) {\n      this._zr.remove(this.el);\n    }\n    var textStyleModel = tooltipModel.getModel('textStyle');\n    this.el = new ZRText({\n      style: {\n        rich: markupStyleCreator.richTextStyles,\n        text: content,\n        lineHeight: 22,\n        borderWidth: 1,\n        borderColor: borderColor,\n        textShadowColor: textStyleModel.get('textShadowColor'),\n        fill: tooltipModel.get(['textStyle', 'color']),\n        padding: getPaddingFromTooltipModel(tooltipModel, 'richText'),\n        verticalAlign: 'top',\n        align: 'left'\n      },\n      z: tooltipModel.get('z')\n    });\n    zrUtil.each(['backgroundColor', 'borderRadius', 'shadowColor', 'shadowBlur', 'shadowOffsetX', 'shadowOffsetY'], function (propName) {\n      _this.el.style[propName] = tooltipModel.get(propName);\n    });\n    zrUtil.each(['textShadowBlur', 'textShadowOffsetX', 'textShadowOffsetY'], function (propName) {\n      _this.el.style[propName] = textStyleModel.get(propName) || 0;\n    });\n    this._zr.add(this.el);\n    var self = this;\n    this.el.on('mouseover', function () {\n      // clear the timeout in hideLater and keep showing tooltip\n      if (self._enterable) {\n        clearTimeout(self._hideTimeout);\n        self._show = true;\n      }\n      self._inContent = true;\n    });\n    this.el.on('mouseout', function () {\n      if (self._enterable) {\n        if (self._show) {\n          self.hideLater(self._hideDelay);\n        }\n      }\n      self._inContent = false;\n    });\n  };\n  TooltipRichContent.prototype.setEnterable = function (enterable) {\n    this._enterable = enterable;\n  };\n  TooltipRichContent.prototype.getSize = function () {\n    var el = this.el;\n    var bounding = this.el.getBoundingRect(); // bounding rect does not include shadow. For renderMode richText,\n    // if overflow, it will be cut. So calculate them accurately.\n\n    var shadowOuterSize = calcShadowOuterSize(el.style);\n    return [bounding.width + shadowOuterSize.left + shadowOuterSize.right, bounding.height + shadowOuterSize.top + shadowOuterSize.bottom];\n  };\n  TooltipRichContent.prototype.moveTo = function (x, y) {\n    var el = this.el;\n    if (el) {\n      var styleCoord = this._styleCoord;\n      makeStyleCoord(styleCoord, this._zr, x, y);\n      x = styleCoord[0];\n      y = styleCoord[1];\n      var style = el.style;\n      var borderWidth = mathMaxWith0(style.borderWidth || 0);\n      var shadowOuterSize = calcShadowOuterSize(style); // rich text x, y do not include border.\n\n      el.x = x + borderWidth + shadowOuterSize.left;\n      el.y = y + borderWidth + shadowOuterSize.top;\n      el.markRedraw();\n    }\n  };\n  /**\r\n   * when `alwaysShowContent` is true,\r\n   * move the tooltip after chart resized\r\n   */\n\n  TooltipRichContent.prototype._moveIfResized = function () {\n    // The ratio of left to width\n    var ratioX = this._styleCoord[2]; // The ratio of top to height\n\n    var ratioY = this._styleCoord[3];\n    this.moveTo(ratioX * this._zr.getWidth(), ratioY * this._zr.getHeight());\n  };\n  TooltipRichContent.prototype.hide = function () {\n    if (this.el) {\n      this.el.hide();\n    }\n    this._show = false;\n  };\n  TooltipRichContent.prototype.hideLater = function (time) {\n    if (this._show && !(this._inContent && this._enterable) && !this._alwaysShowContent) {\n      if (time) {\n        this._hideDelay = time; // Set show false to avoid invoke hideLater multiple times\n\n        this._show = false;\n        this._hideTimeout = setTimeout(zrUtil.bind(this.hide, this), time);\n      } else {\n        this.hide();\n      }\n    }\n  };\n  TooltipRichContent.prototype.isShow = function () {\n    return this._show;\n  };\n  TooltipRichContent.prototype.dispose = function () {\n    this._zr.remove(this.el);\n  };\n  return TooltipRichContent;\n}();\nfunction mathMaxWith0(val) {\n  return Math.max(0, val);\n}\nfunction calcShadowOuterSize(style) {\n  var shadowBlur = mathMaxWith0(style.shadowBlur || 0);\n  var shadowOffsetX = mathMaxWith0(style.shadowOffsetX || 0);\n  var shadowOffsetY = mathMaxWith0(style.shadowOffsetY || 0);\n  return {\n    left: mathMaxWith0(shadowBlur - shadowOffsetX),\n    right: mathMaxWith0(shadowBlur + shadowOffsetX),\n    top: mathMaxWith0(shadowBlur - shadowOffsetY),\n    bottom: mathMaxWith0(shadowBlur + shadowOffsetY)\n  };\n}\nfunction makeStyleCoord(out, zr, zrX, zrY) {\n  out[0] = zrX;\n  out[1] = zrY;\n  out[2] = out[0] / zr.getWidth();\n  out[3] = out[1] / zr.getHeight();\n}\nexport default TooltipRichContent;", "map": {"version": 3, "names": ["zrUtil", "ZRText", "getPaddingFromTooltipModel", "throwError", "TooltipRichContent", "api", "_show", "_styleCoord", "_alwaysShowContent", "_enterable", "_zr", "getZr", "makeStyleCoord", "getWidth", "getHeight", "prototype", "update", "tooltipModel", "alwaysS<PERSON><PERSON><PERSON>nt", "get", "_moveIfResized", "show", "_hideTimeout", "clearTimeout", "el", "<PERSON><PERSON><PERSON><PERSON>", "content", "markupStyleCreator", "borderColor", "arrowPosition", "_this", "isObject", "process", "env", "NODE_ENV", "remove", "textStyleModel", "getModel", "style", "rich", "richTextStyles", "text", "lineHeight", "borderWidth", "textShadowColor", "fill", "padding", "verticalAlign", "align", "z", "each", "propName", "add", "self", "on", "_inContent", "hideLater", "_hideDelay", "setEnterable", "enterable", "getSize", "bounding", "getBoundingRect", "shadowOuterSize", "calcShadowOuterSize", "width", "left", "right", "height", "top", "bottom", "moveTo", "x", "y", "styleCoord", "mathMaxWith0", "mark<PERSON><PERSON><PERSON>", "ratioX", "ratioY", "hide", "time", "setTimeout", "bind", "isShow", "dispose", "val", "Math", "max", "<PERSON><PERSON><PERSON><PERSON>", "shadowOffsetX", "shadowOffsetY", "out", "zr", "zrX", "zrY"], "sources": ["C:/WolfSystemV2/WolfOldFrontEnd/node_modules/echarts/lib/component/tooltip/TooltipRichContent.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport ZRText from 'zrender/lib/graphic/Text.js';\nimport { getPaddingFromTooltipModel } from './tooltipMarkup.js';\nimport { throwError } from '../../util/log.js';\n\nvar TooltipRichContent =\n/** @class */\nfunction () {\n  function TooltipRichContent(api) {\n    this._show = false;\n    this._styleCoord = [0, 0, 0, 0];\n    this._alwaysShowContent = false;\n    this._enterable = true;\n    this._zr = api.getZr();\n    makeStyleCoord(this._styleCoord, this._zr, api.getWidth() / 2, api.getHeight() / 2);\n  }\n  /**\r\n   * Update when tooltip is rendered\r\n   */\n\n\n  TooltipRichContent.prototype.update = function (tooltipModel) {\n    var alwaysShowContent = tooltipModel.get('alwaysShowContent');\n    alwaysShowContent && this._moveIfResized(); // update alwaysShowContent\n\n    this._alwaysShowContent = alwaysShowContent;\n  };\n\n  TooltipRichContent.prototype.show = function () {\n    if (this._hideTimeout) {\n      clearTimeout(this._hideTimeout);\n    }\n\n    this.el.show();\n    this._show = true;\n  };\n  /**\r\n   * Set tooltip content\r\n   */\n\n\n  TooltipRichContent.prototype.setContent = function (content, markupStyleCreator, tooltipModel, borderColor, arrowPosition) {\n    var _this = this;\n\n    if (zrUtil.isObject(content)) {\n      throwError(process.env.NODE_ENV !== 'production' ? 'Passing DOM nodes as content is not supported in richText tooltip!' : '');\n    }\n\n    if (this.el) {\n      this._zr.remove(this.el);\n    }\n\n    var textStyleModel = tooltipModel.getModel('textStyle');\n    this.el = new ZRText({\n      style: {\n        rich: markupStyleCreator.richTextStyles,\n        text: content,\n        lineHeight: 22,\n        borderWidth: 1,\n        borderColor: borderColor,\n        textShadowColor: textStyleModel.get('textShadowColor'),\n        fill: tooltipModel.get(['textStyle', 'color']),\n        padding: getPaddingFromTooltipModel(tooltipModel, 'richText'),\n        verticalAlign: 'top',\n        align: 'left'\n      },\n      z: tooltipModel.get('z')\n    });\n    zrUtil.each(['backgroundColor', 'borderRadius', 'shadowColor', 'shadowBlur', 'shadowOffsetX', 'shadowOffsetY'], function (propName) {\n      _this.el.style[propName] = tooltipModel.get(propName);\n    });\n    zrUtil.each(['textShadowBlur', 'textShadowOffsetX', 'textShadowOffsetY'], function (propName) {\n      _this.el.style[propName] = textStyleModel.get(propName) || 0;\n    });\n\n    this._zr.add(this.el);\n\n    var self = this;\n    this.el.on('mouseover', function () {\n      // clear the timeout in hideLater and keep showing tooltip\n      if (self._enterable) {\n        clearTimeout(self._hideTimeout);\n        self._show = true;\n      }\n\n      self._inContent = true;\n    });\n    this.el.on('mouseout', function () {\n      if (self._enterable) {\n        if (self._show) {\n          self.hideLater(self._hideDelay);\n        }\n      }\n\n      self._inContent = false;\n    });\n  };\n\n  TooltipRichContent.prototype.setEnterable = function (enterable) {\n    this._enterable = enterable;\n  };\n\n  TooltipRichContent.prototype.getSize = function () {\n    var el = this.el;\n    var bounding = this.el.getBoundingRect(); // bounding rect does not include shadow. For renderMode richText,\n    // if overflow, it will be cut. So calculate them accurately.\n\n    var shadowOuterSize = calcShadowOuterSize(el.style);\n    return [bounding.width + shadowOuterSize.left + shadowOuterSize.right, bounding.height + shadowOuterSize.top + shadowOuterSize.bottom];\n  };\n\n  TooltipRichContent.prototype.moveTo = function (x, y) {\n    var el = this.el;\n\n    if (el) {\n      var styleCoord = this._styleCoord;\n      makeStyleCoord(styleCoord, this._zr, x, y);\n      x = styleCoord[0];\n      y = styleCoord[1];\n      var style = el.style;\n      var borderWidth = mathMaxWith0(style.borderWidth || 0);\n      var shadowOuterSize = calcShadowOuterSize(style); // rich text x, y do not include border.\n\n      el.x = x + borderWidth + shadowOuterSize.left;\n      el.y = y + borderWidth + shadowOuterSize.top;\n      el.markRedraw();\n    }\n  };\n  /**\r\n   * when `alwaysShowContent` is true,\r\n   * move the tooltip after chart resized\r\n   */\n\n\n  TooltipRichContent.prototype._moveIfResized = function () {\n    // The ratio of left to width\n    var ratioX = this._styleCoord[2]; // The ratio of top to height\n\n    var ratioY = this._styleCoord[3];\n    this.moveTo(ratioX * this._zr.getWidth(), ratioY * this._zr.getHeight());\n  };\n\n  TooltipRichContent.prototype.hide = function () {\n    if (this.el) {\n      this.el.hide();\n    }\n\n    this._show = false;\n  };\n\n  TooltipRichContent.prototype.hideLater = function (time) {\n    if (this._show && !(this._inContent && this._enterable) && !this._alwaysShowContent) {\n      if (time) {\n        this._hideDelay = time; // Set show false to avoid invoke hideLater multiple times\n\n        this._show = false;\n        this._hideTimeout = setTimeout(zrUtil.bind(this.hide, this), time);\n      } else {\n        this.hide();\n      }\n    }\n  };\n\n  TooltipRichContent.prototype.isShow = function () {\n    return this._show;\n  };\n\n  TooltipRichContent.prototype.dispose = function () {\n    this._zr.remove(this.el);\n  };\n\n  return TooltipRichContent;\n}();\n\nfunction mathMaxWith0(val) {\n  return Math.max(0, val);\n}\n\nfunction calcShadowOuterSize(style) {\n  var shadowBlur = mathMaxWith0(style.shadowBlur || 0);\n  var shadowOffsetX = mathMaxWith0(style.shadowOffsetX || 0);\n  var shadowOffsetY = mathMaxWith0(style.shadowOffsetY || 0);\n  return {\n    left: mathMaxWith0(shadowBlur - shadowOffsetX),\n    right: mathMaxWith0(shadowBlur + shadowOffsetX),\n    top: mathMaxWith0(shadowBlur - shadowOffsetY),\n    bottom: mathMaxWith0(shadowBlur + shadowOffsetY)\n  };\n}\n\nfunction makeStyleCoord(out, zr, zrX, zrY) {\n  out[0] = zrX;\n  out[1] = zrY;\n  out[2] = out[0] / zr.getWidth();\n  out[3] = out[1] / zr.getHeight();\n}\n\nexport default TooltipRichContent;"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,KAAKA,MAAM,MAAM,0BAA0B;AAClD,OAAOC,MAAM,MAAM,6BAA6B;AAChD,SAASC,0BAA0B,QAAQ,oBAAoB;AAC/D,SAASC,UAAU,QAAQ,mBAAmB;AAE9C,IAAIC,kBAAkB,GACtB;AACA,YAAY;EACV,SAASA,kBAAkBA,CAACC,GAAG,EAAE;IAC/B,IAAI,CAACC,KAAK,GAAG,KAAK;IAClB,IAAI,CAACC,WAAW,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IAC/B,IAAI,CAACC,kBAAkB,GAAG,KAAK;IAC/B,IAAI,CAACC,UAAU,GAAG,IAAI;IACtB,IAAI,CAACC,GAAG,GAAGL,GAAG,CAACM,KAAK,CAAC,CAAC;IACtBC,cAAc,CAAC,IAAI,CAACL,WAAW,EAAE,IAAI,CAACG,GAAG,EAAEL,GAAG,CAACQ,QAAQ,CAAC,CAAC,GAAG,CAAC,EAAER,GAAG,CAACS,SAAS,CAAC,CAAC,GAAG,CAAC,CAAC;EACrF;EACA;AACF;AACA;;EAGEV,kBAAkB,CAACW,SAAS,CAACC,MAAM,GAAG,UAAUC,YAAY,EAAE;IAC5D,IAAIC,iBAAiB,GAAGD,YAAY,CAACE,GAAG,CAAC,mBAAmB,CAAC;IAC7DD,iBAAiB,IAAI,IAAI,CAACE,cAAc,CAAC,CAAC,CAAC,CAAC;;IAE5C,IAAI,CAACZ,kBAAkB,GAAGU,iBAAiB;EAC7C,CAAC;EAEDd,kBAAkB,CAACW,SAAS,CAACM,IAAI,GAAG,YAAY;IAC9C,IAAI,IAAI,CAACC,YAAY,EAAE;MACrBC,YAAY,CAAC,IAAI,CAACD,YAAY,CAAC;IACjC;IAEA,IAAI,CAACE,EAAE,CAACH,IAAI,CAAC,CAAC;IACd,IAAI,CAACf,KAAK,GAAG,IAAI;EACnB,CAAC;EACD;AACF;AACA;;EAGEF,kBAAkB,CAACW,SAAS,CAACU,UAAU,GAAG,UAAUC,OAAO,EAAEC,kBAAkB,EAAEV,YAAY,EAAEW,WAAW,EAAEC,aAAa,EAAE;IACzH,IAAIC,KAAK,GAAG,IAAI;IAEhB,IAAI9B,MAAM,CAAC+B,QAAQ,CAACL,OAAO,CAAC,EAAE;MAC5BvB,UAAU,CAAC6B,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAG,oEAAoE,GAAG,EAAE,CAAC;IAC/H;IAEA,IAAI,IAAI,CAACV,EAAE,EAAE;MACX,IAAI,CAACd,GAAG,CAACyB,MAAM,CAAC,IAAI,CAACX,EAAE,CAAC;IAC1B;IAEA,IAAIY,cAAc,GAAGnB,YAAY,CAACoB,QAAQ,CAAC,WAAW,CAAC;IACvD,IAAI,CAACb,EAAE,GAAG,IAAIvB,MAAM,CAAC;MACnBqC,KAAK,EAAE;QACLC,IAAI,EAAEZ,kBAAkB,CAACa,cAAc;QACvCC,IAAI,EAAEf,OAAO;QACbgB,UAAU,EAAE,EAAE;QACdC,WAAW,EAAE,CAAC;QACdf,WAAW,EAAEA,WAAW;QACxBgB,eAAe,EAAER,cAAc,CAACjB,GAAG,CAAC,iBAAiB,CAAC;QACtD0B,IAAI,EAAE5B,YAAY,CAACE,GAAG,CAAC,CAAC,WAAW,EAAE,OAAO,CAAC,CAAC;QAC9C2B,OAAO,EAAE5C,0BAA0B,CAACe,YAAY,EAAE,UAAU,CAAC;QAC7D8B,aAAa,EAAE,KAAK;QACpBC,KAAK,EAAE;MACT,CAAC;MACDC,CAAC,EAAEhC,YAAY,CAACE,GAAG,CAAC,GAAG;IACzB,CAAC,CAAC;IACFnB,MAAM,CAACkD,IAAI,CAAC,CAAC,iBAAiB,EAAE,cAAc,EAAE,aAAa,EAAE,YAAY,EAAE,eAAe,EAAE,eAAe,CAAC,EAAE,UAAUC,QAAQ,EAAE;MAClIrB,KAAK,CAACN,EAAE,CAACc,KAAK,CAACa,QAAQ,CAAC,GAAGlC,YAAY,CAACE,GAAG,CAACgC,QAAQ,CAAC;IACvD,CAAC,CAAC;IACFnD,MAAM,CAACkD,IAAI,CAAC,CAAC,gBAAgB,EAAE,mBAAmB,EAAE,mBAAmB,CAAC,EAAE,UAAUC,QAAQ,EAAE;MAC5FrB,KAAK,CAACN,EAAE,CAACc,KAAK,CAACa,QAAQ,CAAC,GAAGf,cAAc,CAACjB,GAAG,CAACgC,QAAQ,CAAC,IAAI,CAAC;IAC9D,CAAC,CAAC;IAEF,IAAI,CAACzC,GAAG,CAAC0C,GAAG,CAAC,IAAI,CAAC5B,EAAE,CAAC;IAErB,IAAI6B,IAAI,GAAG,IAAI;IACf,IAAI,CAAC7B,EAAE,CAAC8B,EAAE,CAAC,WAAW,EAAE,YAAY;MAClC;MACA,IAAID,IAAI,CAAC5C,UAAU,EAAE;QACnBc,YAAY,CAAC8B,IAAI,CAAC/B,YAAY,CAAC;QAC/B+B,IAAI,CAAC/C,KAAK,GAAG,IAAI;MACnB;MAEA+C,IAAI,CAACE,UAAU,GAAG,IAAI;IACxB,CAAC,CAAC;IACF,IAAI,CAAC/B,EAAE,CAAC8B,EAAE,CAAC,UAAU,EAAE,YAAY;MACjC,IAAID,IAAI,CAAC5C,UAAU,EAAE;QACnB,IAAI4C,IAAI,CAAC/C,KAAK,EAAE;UACd+C,IAAI,CAACG,SAAS,CAACH,IAAI,CAACI,UAAU,CAAC;QACjC;MACF;MAEAJ,IAAI,CAACE,UAAU,GAAG,KAAK;IACzB,CAAC,CAAC;EACJ,CAAC;EAEDnD,kBAAkB,CAACW,SAAS,CAAC2C,YAAY,GAAG,UAAUC,SAAS,EAAE;IAC/D,IAAI,CAAClD,UAAU,GAAGkD,SAAS;EAC7B,CAAC;EAEDvD,kBAAkB,CAACW,SAAS,CAAC6C,OAAO,GAAG,YAAY;IACjD,IAAIpC,EAAE,GAAG,IAAI,CAACA,EAAE;IAChB,IAAIqC,QAAQ,GAAG,IAAI,CAACrC,EAAE,CAACsC,eAAe,CAAC,CAAC,CAAC,CAAC;IAC1C;;IAEA,IAAIC,eAAe,GAAGC,mBAAmB,CAACxC,EAAE,CAACc,KAAK,CAAC;IACnD,OAAO,CAACuB,QAAQ,CAACI,KAAK,GAAGF,eAAe,CAACG,IAAI,GAAGH,eAAe,CAACI,KAAK,EAAEN,QAAQ,CAACO,MAAM,GAAGL,eAAe,CAACM,GAAG,GAAGN,eAAe,CAACO,MAAM,CAAC;EACxI,CAAC;EAEDlE,kBAAkB,CAACW,SAAS,CAACwD,MAAM,GAAG,UAAUC,CAAC,EAAEC,CAAC,EAAE;IACpD,IAAIjD,EAAE,GAAG,IAAI,CAACA,EAAE;IAEhB,IAAIA,EAAE,EAAE;MACN,IAAIkD,UAAU,GAAG,IAAI,CAACnE,WAAW;MACjCK,cAAc,CAAC8D,UAAU,EAAE,IAAI,CAAChE,GAAG,EAAE8D,CAAC,EAAEC,CAAC,CAAC;MAC1CD,CAAC,GAAGE,UAAU,CAAC,CAAC,CAAC;MACjBD,CAAC,GAAGC,UAAU,CAAC,CAAC,CAAC;MACjB,IAAIpC,KAAK,GAAGd,EAAE,CAACc,KAAK;MACpB,IAAIK,WAAW,GAAGgC,YAAY,CAACrC,KAAK,CAACK,WAAW,IAAI,CAAC,CAAC;MACtD,IAAIoB,eAAe,GAAGC,mBAAmB,CAAC1B,KAAK,CAAC,CAAC,CAAC;;MAElDd,EAAE,CAACgD,CAAC,GAAGA,CAAC,GAAG7B,WAAW,GAAGoB,eAAe,CAACG,IAAI;MAC7C1C,EAAE,CAACiD,CAAC,GAAGA,CAAC,GAAG9B,WAAW,GAAGoB,eAAe,CAACM,GAAG;MAC5C7C,EAAE,CAACoD,UAAU,CAAC,CAAC;IACjB;EACF,CAAC;EACD;AACF;AACA;AACA;;EAGExE,kBAAkB,CAACW,SAAS,CAACK,cAAc,GAAG,YAAY;IACxD;IACA,IAAIyD,MAAM,GAAG,IAAI,CAACtE,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC;;IAElC,IAAIuE,MAAM,GAAG,IAAI,CAACvE,WAAW,CAAC,CAAC,CAAC;IAChC,IAAI,CAACgE,MAAM,CAACM,MAAM,GAAG,IAAI,CAACnE,GAAG,CAACG,QAAQ,CAAC,CAAC,EAAEiE,MAAM,GAAG,IAAI,CAACpE,GAAG,CAACI,SAAS,CAAC,CAAC,CAAC;EAC1E,CAAC;EAEDV,kBAAkB,CAACW,SAAS,CAACgE,IAAI,GAAG,YAAY;IAC9C,IAAI,IAAI,CAACvD,EAAE,EAAE;MACX,IAAI,CAACA,EAAE,CAACuD,IAAI,CAAC,CAAC;IAChB;IAEA,IAAI,CAACzE,KAAK,GAAG,KAAK;EACpB,CAAC;EAEDF,kBAAkB,CAACW,SAAS,CAACyC,SAAS,GAAG,UAAUwB,IAAI,EAAE;IACvD,IAAI,IAAI,CAAC1E,KAAK,IAAI,EAAE,IAAI,CAACiD,UAAU,IAAI,IAAI,CAAC9C,UAAU,CAAC,IAAI,CAAC,IAAI,CAACD,kBAAkB,EAAE;MACnF,IAAIwE,IAAI,EAAE;QACR,IAAI,CAACvB,UAAU,GAAGuB,IAAI,CAAC,CAAC;;QAExB,IAAI,CAAC1E,KAAK,GAAG,KAAK;QAClB,IAAI,CAACgB,YAAY,GAAG2D,UAAU,CAACjF,MAAM,CAACkF,IAAI,CAAC,IAAI,CAACH,IAAI,EAAE,IAAI,CAAC,EAAEC,IAAI,CAAC;MACpE,CAAC,MAAM;QACL,IAAI,CAACD,IAAI,CAAC,CAAC;MACb;IACF;EACF,CAAC;EAED3E,kBAAkB,CAACW,SAAS,CAACoE,MAAM,GAAG,YAAY;IAChD,OAAO,IAAI,CAAC7E,KAAK;EACnB,CAAC;EAEDF,kBAAkB,CAACW,SAAS,CAACqE,OAAO,GAAG,YAAY;IACjD,IAAI,CAAC1E,GAAG,CAACyB,MAAM,CAAC,IAAI,CAACX,EAAE,CAAC;EAC1B,CAAC;EAED,OAAOpB,kBAAkB;AAC3B,CAAC,CAAC,CAAC;AAEH,SAASuE,YAAYA,CAACU,GAAG,EAAE;EACzB,OAAOC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAEF,GAAG,CAAC;AACzB;AAEA,SAASrB,mBAAmBA,CAAC1B,KAAK,EAAE;EAClC,IAAIkD,UAAU,GAAGb,YAAY,CAACrC,KAAK,CAACkD,UAAU,IAAI,CAAC,CAAC;EACpD,IAAIC,aAAa,GAAGd,YAAY,CAACrC,KAAK,CAACmD,aAAa,IAAI,CAAC,CAAC;EAC1D,IAAIC,aAAa,GAAGf,YAAY,CAACrC,KAAK,CAACoD,aAAa,IAAI,CAAC,CAAC;EAC1D,OAAO;IACLxB,IAAI,EAAES,YAAY,CAACa,UAAU,GAAGC,aAAa,CAAC;IAC9CtB,KAAK,EAAEQ,YAAY,CAACa,UAAU,GAAGC,aAAa,CAAC;IAC/CpB,GAAG,EAAEM,YAAY,CAACa,UAAU,GAAGE,aAAa,CAAC;IAC7CpB,MAAM,EAAEK,YAAY,CAACa,UAAU,GAAGE,aAAa;EACjD,CAAC;AACH;AAEA,SAAS9E,cAAcA,CAAC+E,GAAG,EAAEC,EAAE,EAAEC,GAAG,EAAEC,GAAG,EAAE;EACzCH,GAAG,CAAC,CAAC,CAAC,GAAGE,GAAG;EACZF,GAAG,CAAC,CAAC,CAAC,GAAGG,GAAG;EACZH,GAAG,CAAC,CAAC,CAAC,GAAGA,GAAG,CAAC,CAAC,CAAC,GAAGC,EAAE,CAAC/E,QAAQ,CAAC,CAAC;EAC/B8E,GAAG,CAAC,CAAC,CAAC,GAAGA,GAAG,CAAC,CAAC,CAAC,GAAGC,EAAE,CAAC9E,SAAS,CAAC,CAAC;AAClC;AAEA,eAAeV,kBAAkB"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}