import { Component, Input, OnInit, OnDestroy, ElementRef, ViewChild, OnChanges, SimpleChanges } from '@angular/core';
import { CommonModule } from '@angular/common';
import { GlobePoint } from '../../monitoring.component';

declare global {
  interface Window {
    THREE: any;
  }
}

@Component({
  selector: 'app-globe',
  standalone: true,
  imports: [CommonModule],
  template: `
    <div class="globe-container">
      <div #globeCanvas class="globe-canvas"></div>
      <div class="loading" *ngIf="isLoading">
        <div class="spinner"></div>
        <span>Carregando globo...</span>
      </div>
      <div class="connection-counter">
        <span class="counter-value">{{ totalConnections }}</span>
        <span class="counter-label">conexões ativas</span>
      </div>
    </div>
  `,
  styleUrls: ['./globe.component.scss']
})
export class GlobeComponent implements OnInit, OnDestroy, OnChanges {
  @Input() globeData: GlobePoint[] = [];
  @ViewChild('globeCanvas', { static: true }) canvasRef!: ElementRef;

  private scene: any;
  private camera: any;
  private renderer: any;
  private globe: any;
  private markers: any;
  private controls: any;
  private animationId: number = 0;
  private clock: any;
  private globalUniforms: any;
  
  isLoading = true;
  totalConnections = 0;

  ngOnInit() {
    this.loadThreeJS().then(() => {
      this.initGlobe();
      this.isLoading = false;
    });
  }

  ngOnChanges(changes: SimpleChanges) {
    if (changes['globeData'] && !changes['globeData'].firstChange) {
      this.updateMarkers();
      this.updateConnectionCounter();
    }
  }

  ngOnDestroy() {
    if (this.animationId) {
      cancelAnimationFrame(this.animationId);
    }
    if (this.renderer) {
      this.renderer.dispose();
    }
  }

  private async loadThreeJS(): Promise<void> {
    return new Promise((resolve) => {
      if (window.THREE) {
        resolve();
        return;
      }

      // Carregar Three.js via CDN
      const script = document.createElement('script');
      script.src = 'https://cdn.skypack.dev/three@0.136.0';
      script.onload = () => {
        // Carregar OrbitControls
        const controlsScript = document.createElement('script');
        controlsScript.src = 'https://cdn.skypack.dev/three@0.136.0/examples/jsm/controls/OrbitControls';
        controlsScript.onload = () => resolve();
        document.head.appendChild(controlsScript);
      };
      document.head.appendChild(script);
    });
  }

  private initGlobe() {
    const container = this.canvasRef.nativeElement;
    const width = container.clientWidth;
    const height = container.clientHeight;

    // Scene
    this.scene = new window.THREE.Scene();

    // Camera
    this.camera = new window.THREE.PerspectiveCamera(45, width / height, 1, 2000);
    this.camera.position.set(0.5, 0.5, 1).setLength(14);

    // Renderer
    this.renderer = new window.THREE.WebGLRenderer({ antialias: true, alpha: true });
    this.renderer.setSize(width, height);
    this.renderer.setClearColor(0x000000, 0);
    container.appendChild(this.renderer.domElement);

    // Global uniforms
    this.globalUniforms = {
      time: { value: 0 }
    };

    // Clock
    this.clock = new window.THREE.Clock();

    this.createGlobe();
    this.createMarkers();
    this.setupControls();
    this.animate();

    // Handle resize
    window.addEventListener('resize', () => this.onWindowResize());
  }

  private createGlobe() {
    const rad = 5;
    const counter = 50000; // Reduzido para melhor performance

    // Criar pontos distribuídos na esfera usando algoritmo de Fibonacci
    const pts = [];
    const clr = [];
    const uvs = [];
    const c = new window.THREE.Color();

    let r = 0;
    const dlong = Math.PI * (3 - Math.sqrt(5));
    const dz = 2 / counter;
    let long = 0;
    let z = 1 - dz / 2;

    for (let i = 0; i < counter; i++) {
      r = Math.sqrt(1 - z * z);
      const p = new window.THREE.Vector3(
        Math.cos(long) * r,
        z,
        -Math.sin(long) * r
      ).multiplyScalar(rad);

      pts.push(p);
      z = z - dz;
      long = long + dlong;

      // Cor verde para simular continentes
      c.setHSL(0.3, 0.7, Math.random() * 0.3 + 0.4);
      c.toArray(clr, i * 3);

      // UVs para textura
      const sph = new window.THREE.Spherical();
      sph.setFromVector3(p);
      uvs.push((sph.theta + Math.PI) / (Math.PI * 2), 1.0 - sph.phi / Math.PI);
    }

    const geometry = new window.THREE.BufferGeometry().setFromPoints(pts);
    geometry.setAttribute("color", new window.THREE.Float32BufferAttribute(clr, 3));
    geometry.setAttribute("uv", new window.THREE.Float32BufferAttribute(uvs, 2));

    const material = new window.THREE.PointsMaterial({
      size: 0.08,
      vertexColors: true,
      transparent: true,
      opacity: 0.8
    });

    this.globe = new window.THREE.Points(geometry, material);
    this.scene.add(this.globe);

    // Adicionar wireframe icosahedron
    const icosahedron = new window.THREE.Mesh(
      new window.THREE.IcosahedronGeometry(rad, 1),
      new window.THREE.MeshBasicMaterial({
        color: 0x4ecdc4,
        wireframe: true,
        transparent: true,
        opacity: 0.3
      })
    );
    this.globe.add(icosahedron);
  }

  private createMarkers() {
    // Será atualizado quando recebermos dados
    this.updateMarkers();
  }

  private updateMarkers() {
    if (!this.scene || !this.globeData.length) return;

    // Remover markers existentes
    if (this.markers) {
      this.scene.remove(this.markers);
    }

    const markerGeometry = new window.THREE.PlaneGeometry(0.3, 0.3);
    const markerMaterial = new window.THREE.MeshBasicMaterial({
      color: 0xff3232,
      transparent: true,
      opacity: 0.8
    });

    this.markers = new window.THREE.InstancedMesh(
      markerGeometry,
      markerMaterial,
      this.globeData.length
    );

    const dummy = new window.THREE.Object3D();
    const rad = 5.1; // Ligeiramente acima da superfície do globo

    this.globeData.forEach((point, index) => {
      // Converter lat/lng para posição 3D
      const lat = (point.lat * Math.PI) / 180;
      const lng = (point.lng * Math.PI) / 180;

      const x = rad * Math.cos(lat) * Math.cos(lng);
      const y = rad * Math.sin(lat);
      const z = rad * Math.cos(lat) * Math.sin(lng);

      dummy.position.set(x, y, z);
      dummy.lookAt(dummy.position.clone().setLength(rad + 1));
      dummy.updateMatrix();
      
      this.markers.setMatrixAt(index, dummy.matrix);

      // Definir cor baseada na intensidade
      const color = this.getIntensityColor(point.intensity);
      this.markers.setColorAt(index, color);
    });

    this.scene.add(this.markers);
  }

  private getIntensityColor(intensity: string): any {
    const color = new window.THREE.Color();
    switch (intensity) {
      case 'high':
        return color.setHex(0xff0000); // Vermelho
      case 'medium':
        return color.setHex(0xff8800); // Laranja
      case 'low':
      default:
        return color.setHex(0x00ff00); // Verde
    }
  }

  private setupControls() {
    // Simular OrbitControls básico
    let mouseDown = false;
    let mouseX = 0;
    let mouseY = 0;

    this.renderer.domElement.addEventListener('mousedown', (event: MouseEvent) => {
      mouseDown = true;
      mouseX = event.clientX;
      mouseY = event.clientY;
    });

    this.renderer.domElement.addEventListener('mouseup', () => {
      mouseDown = false;
    });

    this.renderer.domElement.addEventListener('mousemove', (event: MouseEvent) => {
      if (!mouseDown) return;

      const deltaX = event.clientX - mouseX;
      const deltaY = event.clientY - mouseY;

      this.globe.rotation.y += deltaX * 0.01;
      this.globe.rotation.x += deltaY * 0.01;

      mouseX = event.clientX;
      mouseY = event.clientY;
    });

    // Auto-rotação
    setInterval(() => {
      if (!mouseDown) {
        this.globe.rotation.y += 0.005;
      }
    }, 16);
  }

  private animate() {
    this.animationId = requestAnimationFrame(() => this.animate());

    const t = this.clock.getElapsedTime();
    this.globalUniforms.time.value = t;

    this.renderer.render(this.scene, this.camera);
  }

  private onWindowResize() {
    const container = this.canvasRef.nativeElement;
    const width = container.clientWidth;
    const height = container.clientHeight;

    this.camera.aspect = width / height;
    this.camera.updateProjectionMatrix();
    this.renderer.setSize(width, height);
  }

  private updateConnectionCounter() {
    this.totalConnections = this.globeData.reduce((sum, point) => sum + point.connections, 0);
  }
}
