{"ast": null, "code": "/**\n * matchesSelector v2.0.2\n * matchesSelector( element, '.selector' )\n * MIT license\n */\n\n/*jshint browser: true, strict: true, undef: true, unused: true */\n\n(function (window, factory) {\n  /*global define: false, module: false */\n  'use strict';\n\n  // universal module definition\n  if (typeof define == 'function' && define.amd) {\n    // AMD\n    define(factory);\n  } else if (typeof module == 'object' && module.exports) {\n    // CommonJS\n    module.exports = factory();\n  } else {\n    // browser global\n    window.matchesSelector = factory();\n  }\n})(window, function factory() {\n  'use strict';\n\n  var matchesMethod = function () {\n    var ElemProto = window.Element.prototype;\n    // check for the standard method name first\n    if (ElemProto.matches) {\n      return 'matches';\n    }\n    // check un-prefixed\n    if (ElemProto.matchesSelector) {\n      return 'matchesSelector';\n    }\n    // check vendor prefixes\n    var prefixes = ['webkit', 'moz', 'ms', 'o'];\n    for (var i = 0; i < prefixes.length; i++) {\n      var prefix = prefixes[i];\n      var method = prefix + 'MatchesSelector';\n      if (ElemProto[method]) {\n        return method;\n      }\n    }\n  }();\n  return function matchesSelector(elem, selector) {\n    return elem[matchesMethod](selector);\n  };\n});", "map": {"version": 3, "names": ["window", "factory", "define", "amd", "module", "exports", "matchesSelector", "matchesMethod", "ElemProto", "Element", "prototype", "matches", "prefixes", "i", "length", "prefix", "method", "elem", "selector"], "sources": ["C:/WolfSystemV2/WolfOldFrontEnd/node_modules/desandro-matches-selector/matches-selector.js"], "sourcesContent": ["/**\n * matchesSelector v2.0.2\n * matchesSelector( element, '.selector' )\n * MIT license\n */\n\n/*jshint browser: true, strict: true, undef: true, unused: true */\n\n( function( window, factory ) {\n  /*global define: false, module: false */\n  'use strict';\n  // universal module definition\n  if ( typeof define == 'function' && define.amd ) {\n    // AMD\n    define( factory );\n  } else if ( typeof module == 'object' && module.exports ) {\n    // CommonJS\n    module.exports = factory();\n  } else {\n    // browser global\n    window.matchesSelector = factory();\n  }\n\n}( window, function factory() {\n  'use strict';\n\n  var matchesMethod = ( function() {\n    var ElemProto = window.Element.prototype;\n    // check for the standard method name first\n    if ( ElemProto.matches ) {\n      return 'matches';\n    }\n    // check un-prefixed\n    if ( ElemProto.matchesSelector ) {\n      return 'matchesSelector';\n    }\n    // check vendor prefixes\n    var prefixes = [ 'webkit', 'moz', 'ms', 'o' ];\n\n    for ( var i=0; i < prefixes.length; i++ ) {\n      var prefix = prefixes[i];\n      var method = prefix + 'MatchesSelector';\n      if ( ElemProto[ method ] ) {\n        return method;\n      }\n    }\n  })();\n\n  return function matchesSelector( elem, selector ) {\n    return elem[ matchesMethod ]( selector );\n  };\n\n}));\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;;AAEA;;AAEE,WAAUA,MAAM,EAAEC,OAAO,EAAG;EAC5B;EACA,YAAY;;EACZ;EACA,IAAK,OAAOC,MAAM,IAAI,UAAU,IAAIA,MAAM,CAACC,GAAG,EAAG;IAC/C;IACAD,MAAM,CAAED,OAAQ,CAAC;EACnB,CAAC,MAAM,IAAK,OAAOG,MAAM,IAAI,QAAQ,IAAIA,MAAM,CAACC,OAAO,EAAG;IACxD;IACAD,MAAM,CAACC,OAAO,GAAGJ,OAAO,CAAC,CAAC;EAC5B,CAAC,MAAM;IACL;IACAD,MAAM,CAACM,eAAe,GAAGL,OAAO,CAAC,CAAC;EACpC;AAEF,CAAC,EAAED,MAAM,EAAE,SAASC,OAAOA,CAAA,EAAG;EAC5B,YAAY;;EAEZ,IAAIM,aAAa,GAAK,YAAW;IAC/B,IAAIC,SAAS,GAAGR,MAAM,CAACS,OAAO,CAACC,SAAS;IACxC;IACA,IAAKF,SAAS,CAACG,OAAO,EAAG;MACvB,OAAO,SAAS;IAClB;IACA;IACA,IAAKH,SAAS,CAACF,eAAe,EAAG;MAC/B,OAAO,iBAAiB;IAC1B;IACA;IACA,IAAIM,QAAQ,GAAG,CAAE,QAAQ,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,CAAE;IAE7C,KAAM,IAAIC,CAAC,GAAC,CAAC,EAAEA,CAAC,GAAGD,QAAQ,CAACE,MAAM,EAAED,CAAC,EAAE,EAAG;MACxC,IAAIE,MAAM,GAAGH,QAAQ,CAACC,CAAC,CAAC;MACxB,IAAIG,MAAM,GAAGD,MAAM,GAAG,iBAAiB;MACvC,IAAKP,SAAS,CAAEQ,MAAM,CAAE,EAAG;QACzB,OAAOA,MAAM;MACf;IACF;EACF,CAAC,CAAE,CAAC;EAEJ,OAAO,SAASV,eAAeA,CAAEW,IAAI,EAAEC,QAAQ,EAAG;IAChD,OAAOD,IAAI,CAAEV,aAAa,CAAE,CAAEW,QAAS,CAAC;EAC1C,CAAC;AAEH,CAAC,CAAC"}, "metadata": {}, "sourceType": "script", "externalDependencies": []}