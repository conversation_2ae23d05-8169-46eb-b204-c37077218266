{"ast": null, "code": "import LazyWrapper from './_LazyWrapper.js';\n\n/**\n * Reverses the direction of lazy iteration.\n *\n * @private\n * @name reverse\n * @memberOf LazyWrapper\n * @returns {Object} Returns the new reversed `LazyWrapper` object.\n */\nfunction lazyReverse() {\n  if (this.__filtered__) {\n    var result = new LazyWrapper(this);\n    result.__dir__ = -1;\n    result.__filtered__ = true;\n  } else {\n    result = this.clone();\n    result.__dir__ *= -1;\n  }\n  return result;\n}\nexport default lazyReverse;", "map": {"version": 3, "names": ["LazyWrapper", "lazyReverse", "__filtered__", "result", "__dir__", "clone"], "sources": ["C:/WolfSystemV2/WolfOldFrontEnd/node_modules/lodash-es/_lazyReverse.js"], "sourcesContent": ["import LazyWrapper from './_LazyWrapper.js';\n\n/**\n * Reverses the direction of lazy iteration.\n *\n * @private\n * @name reverse\n * @memberOf LazyWrapper\n * @returns {Object} Returns the new reversed `LazyWrapper` object.\n */\nfunction lazyReverse() {\n  if (this.__filtered__) {\n    var result = new LazyWrapper(this);\n    result.__dir__ = -1;\n    result.__filtered__ = true;\n  } else {\n    result = this.clone();\n    result.__dir__ *= -1;\n  }\n  return result;\n}\n\nexport default lazyReverse;\n"], "mappings": "AAAA,OAAOA,WAAW,MAAM,mBAAmB;;AAE3C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,WAAWA,CAAA,EAAG;EACrB,IAAI,IAAI,CAACC,YAAY,EAAE;IACrB,IAAIC,MAAM,GAAG,IAAIH,WAAW,CAAC,IAAI,CAAC;IAClCG,MAAM,CAACC,OAAO,GAAG,CAAC,CAAC;IACnBD,MAAM,CAACD,YAAY,GAAG,IAAI;EAC5B,CAAC,MAAM;IACLC,MAAM,GAAG,IAAI,CAACE,KAAK,CAAC,CAAC;IACrBF,MAAM,CAACC,OAAO,IAAI,CAAC,CAAC;EACtB;EACA,OAAOD,MAAM;AACf;AAEA,eAAeF,WAAW"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}