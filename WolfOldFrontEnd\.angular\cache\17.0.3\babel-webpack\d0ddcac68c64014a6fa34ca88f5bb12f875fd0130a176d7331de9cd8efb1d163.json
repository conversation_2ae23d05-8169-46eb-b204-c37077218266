{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport * as graphic from '../../util/graphic.js';\nimport LineGroup from './Line.js';\nimport { getLabelStatesModels } from '../../label/labelStyle.js';\nvar LineDraw = /** @class */\nfunction () {\n  function LineDraw(LineCtor) {\n    this.group = new graphic.Group();\n    this._LineCtor = LineCtor || LineGroup;\n  }\n  LineDraw.prototype.updateData = function (lineData) {\n    var _this = this; // Remove progressive els.\n\n    this._progressiveEls = null;\n    var lineDraw = this;\n    var group = lineDraw.group;\n    var oldLineData = lineDraw._lineData;\n    lineDraw._lineData = lineData; // There is no oldLineData only when first rendering or switching from\n    // stream mode to normal mode, where previous elements should be removed.\n\n    if (!oldLineData) {\n      group.removeAll();\n    }\n    var seriesScope = makeSeriesScope(lineData);\n    lineData.diff(oldLineData).add(function (idx) {\n      _this._doAdd(lineData, idx, seriesScope);\n    }).update(function (newIdx, oldIdx) {\n      _this._doUpdate(oldLineData, lineData, oldIdx, newIdx, seriesScope);\n    }).remove(function (idx) {\n      group.remove(oldLineData.getItemGraphicEl(idx));\n    }).execute();\n  };\n  ;\n  LineDraw.prototype.updateLayout = function () {\n    var lineData = this._lineData; // Do not support update layout in incremental mode.\n\n    if (!lineData) {\n      return;\n    }\n    lineData.eachItemGraphicEl(function (el, idx) {\n      el.updateLayout(lineData, idx);\n    }, this);\n  };\n  ;\n  LineDraw.prototype.incrementalPrepareUpdate = function (lineData) {\n    this._seriesScope = makeSeriesScope(lineData);\n    this._lineData = null;\n    this.group.removeAll();\n  };\n  ;\n  LineDraw.prototype.incrementalUpdate = function (taskParams, lineData) {\n    this._progressiveEls = [];\n    function updateIncrementalAndHover(el) {\n      if (!el.isGroup && !isEffectObject(el)) {\n        el.incremental = true;\n        el.ensureState('emphasis').hoverLayer = true;\n      }\n    }\n    for (var idx = taskParams.start; idx < taskParams.end; idx++) {\n      var itemLayout = lineData.getItemLayout(idx);\n      if (lineNeedsDraw(itemLayout)) {\n        var el = new this._LineCtor(lineData, idx, this._seriesScope);\n        el.traverse(updateIncrementalAndHover);\n        this.group.add(el);\n        lineData.setItemGraphicEl(idx, el);\n        this._progressiveEls.push(el);\n      }\n    }\n  };\n  ;\n  LineDraw.prototype.remove = function () {\n    this.group.removeAll();\n  };\n  ;\n  LineDraw.prototype.eachRendered = function (cb) {\n    graphic.traverseElements(this._progressiveEls || this.group, cb);\n  };\n  LineDraw.prototype._doAdd = function (lineData, idx, seriesScope) {\n    var itemLayout = lineData.getItemLayout(idx);\n    if (!lineNeedsDraw(itemLayout)) {\n      return;\n    }\n    var el = new this._LineCtor(lineData, idx, seriesScope);\n    lineData.setItemGraphicEl(idx, el);\n    this.group.add(el);\n  };\n  LineDraw.prototype._doUpdate = function (oldLineData, newLineData, oldIdx, newIdx, seriesScope) {\n    var itemEl = oldLineData.getItemGraphicEl(oldIdx);\n    if (!lineNeedsDraw(newLineData.getItemLayout(newIdx))) {\n      this.group.remove(itemEl);\n      return;\n    }\n    if (!itemEl) {\n      itemEl = new this._LineCtor(newLineData, newIdx, seriesScope);\n    } else {\n      itemEl.updateData(newLineData, newIdx, seriesScope);\n    }\n    newLineData.setItemGraphicEl(newIdx, itemEl);\n    this.group.add(itemEl);\n  };\n  return LineDraw;\n}();\nfunction isEffectObject(el) {\n  return el.animators && el.animators.length > 0;\n}\nfunction makeSeriesScope(lineData) {\n  var hostModel = lineData.hostModel;\n  var emphasisModel = hostModel.getModel('emphasis');\n  return {\n    lineStyle: hostModel.getModel('lineStyle').getLineStyle(),\n    emphasisLineStyle: emphasisModel.getModel(['lineStyle']).getLineStyle(),\n    blurLineStyle: hostModel.getModel(['blur', 'lineStyle']).getLineStyle(),\n    selectLineStyle: hostModel.getModel(['select', 'lineStyle']).getLineStyle(),\n    emphasisDisabled: emphasisModel.get('disabled'),\n    blurScope: emphasisModel.get('blurScope'),\n    focus: emphasisModel.get('focus'),\n    labelStatesModels: getLabelStatesModels(hostModel)\n  };\n}\nfunction isPointNaN(pt) {\n  return isNaN(pt[0]) || isNaN(pt[1]);\n}\nfunction lineNeedsDraw(pts) {\n  return pts && !isPointNaN(pts[0]) && !isPointNaN(pts[1]);\n}\nexport default LineDraw;", "map": {"version": 3, "names": ["graphic", "LineGroup", "getLabelStatesModels", "LineDraw", "LineCtor", "group", "Group", "_LineCtor", "prototype", "updateData", "lineData", "_this", "_progressiveEls", "lineDraw", "oldLineData", "_lineData", "removeAll", "seriesScope", "makeSeriesScope", "diff", "add", "idx", "_doAdd", "update", "newIdx", "oldIdx", "_doUpdate", "remove", "getItemGraphicEl", "execute", "updateLayout", "eachItemGraphicEl", "el", "incrementalPrepareUpdate", "_seriesScope", "incrementalUpdate", "taskParams", "updateIncrementalAndHover", "isGroup", "isEffectObject", "incremental", "ensureState", "hoverLayer", "start", "end", "itemLayout", "getItemLayout", "lineNeedsDraw", "traverse", "setItemGraphicEl", "push", "eachRendered", "cb", "traverseElements", "newLineData", "itemEl", "animators", "length", "hostModel", "emphasisModel", "getModel", "lineStyle", "getLineStyle", "emphasisLineStyle", "blurLineStyle", "selectLineStyle", "emphasisDisabled", "get", "blurScope", "focus", "labelStatesModels", "isPointNaN", "pt", "isNaN", "pts"], "sources": ["C:/WolfSystemV2/WolfOldFrontEnd/node_modules/echarts/lib/chart/helper/LineDraw.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport * as graphic from '../../util/graphic.js';\nimport LineGroup from './Line.js';\nimport { getLabelStatesModels } from '../../label/labelStyle.js';\n\nvar LineDraw =\n/** @class */\nfunction () {\n  function LineDraw(LineCtor) {\n    this.group = new graphic.Group();\n    this._LineCtor = LineCtor || LineGroup;\n  }\n\n  LineDraw.prototype.updateData = function (lineData) {\n    var _this = this; // Remove progressive els.\n\n\n    this._progressiveEls = null;\n    var lineDraw = this;\n    var group = lineDraw.group;\n    var oldLineData = lineDraw._lineData;\n    lineDraw._lineData = lineData; // There is no oldLineData only when first rendering or switching from\n    // stream mode to normal mode, where previous elements should be removed.\n\n    if (!oldLineData) {\n      group.removeAll();\n    }\n\n    var seriesScope = makeSeriesScope(lineData);\n    lineData.diff(oldLineData).add(function (idx) {\n      _this._doAdd(lineData, idx, seriesScope);\n    }).update(function (newIdx, oldIdx) {\n      _this._doUpdate(oldLineData, lineData, oldIdx, newIdx, seriesScope);\n    }).remove(function (idx) {\n      group.remove(oldLineData.getItemGraphicEl(idx));\n    }).execute();\n  };\n\n  ;\n\n  LineDraw.prototype.updateLayout = function () {\n    var lineData = this._lineData; // Do not support update layout in incremental mode.\n\n    if (!lineData) {\n      return;\n    }\n\n    lineData.eachItemGraphicEl(function (el, idx) {\n      el.updateLayout(lineData, idx);\n    }, this);\n  };\n\n  ;\n\n  LineDraw.prototype.incrementalPrepareUpdate = function (lineData) {\n    this._seriesScope = makeSeriesScope(lineData);\n    this._lineData = null;\n    this.group.removeAll();\n  };\n\n  ;\n\n  LineDraw.prototype.incrementalUpdate = function (taskParams, lineData) {\n    this._progressiveEls = [];\n\n    function updateIncrementalAndHover(el) {\n      if (!el.isGroup && !isEffectObject(el)) {\n        el.incremental = true;\n        el.ensureState('emphasis').hoverLayer = true;\n      }\n    }\n\n    for (var idx = taskParams.start; idx < taskParams.end; idx++) {\n      var itemLayout = lineData.getItemLayout(idx);\n\n      if (lineNeedsDraw(itemLayout)) {\n        var el = new this._LineCtor(lineData, idx, this._seriesScope);\n        el.traverse(updateIncrementalAndHover);\n        this.group.add(el);\n        lineData.setItemGraphicEl(idx, el);\n\n        this._progressiveEls.push(el);\n      }\n    }\n  };\n\n  ;\n\n  LineDraw.prototype.remove = function () {\n    this.group.removeAll();\n  };\n\n  ;\n\n  LineDraw.prototype.eachRendered = function (cb) {\n    graphic.traverseElements(this._progressiveEls || this.group, cb);\n  };\n\n  LineDraw.prototype._doAdd = function (lineData, idx, seriesScope) {\n    var itemLayout = lineData.getItemLayout(idx);\n\n    if (!lineNeedsDraw(itemLayout)) {\n      return;\n    }\n\n    var el = new this._LineCtor(lineData, idx, seriesScope);\n    lineData.setItemGraphicEl(idx, el);\n    this.group.add(el);\n  };\n\n  LineDraw.prototype._doUpdate = function (oldLineData, newLineData, oldIdx, newIdx, seriesScope) {\n    var itemEl = oldLineData.getItemGraphicEl(oldIdx);\n\n    if (!lineNeedsDraw(newLineData.getItemLayout(newIdx))) {\n      this.group.remove(itemEl);\n      return;\n    }\n\n    if (!itemEl) {\n      itemEl = new this._LineCtor(newLineData, newIdx, seriesScope);\n    } else {\n      itemEl.updateData(newLineData, newIdx, seriesScope);\n    }\n\n    newLineData.setItemGraphicEl(newIdx, itemEl);\n    this.group.add(itemEl);\n  };\n\n  return LineDraw;\n}();\n\nfunction isEffectObject(el) {\n  return el.animators && el.animators.length > 0;\n}\n\nfunction makeSeriesScope(lineData) {\n  var hostModel = lineData.hostModel;\n  var emphasisModel = hostModel.getModel('emphasis');\n  return {\n    lineStyle: hostModel.getModel('lineStyle').getLineStyle(),\n    emphasisLineStyle: emphasisModel.getModel(['lineStyle']).getLineStyle(),\n    blurLineStyle: hostModel.getModel(['blur', 'lineStyle']).getLineStyle(),\n    selectLineStyle: hostModel.getModel(['select', 'lineStyle']).getLineStyle(),\n    emphasisDisabled: emphasisModel.get('disabled'),\n    blurScope: emphasisModel.get('blurScope'),\n    focus: emphasisModel.get('focus'),\n    labelStatesModels: getLabelStatesModels(hostModel)\n  };\n}\n\nfunction isPointNaN(pt) {\n  return isNaN(pt[0]) || isNaN(pt[1]);\n}\n\nfunction lineNeedsDraw(pts) {\n  return pts && !isPointNaN(pts[0]) && !isPointNaN(pts[1]);\n}\n\nexport default LineDraw;"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,KAAKA,OAAO,MAAM,uBAAuB;AAChD,OAAOC,SAAS,MAAM,WAAW;AACjC,SAASC,oBAAoB,QAAQ,2BAA2B;AAEhE,IAAIC,QAAQ,GACZ;AACA,YAAY;EACV,SAASA,QAAQA,CAACC,QAAQ,EAAE;IAC1B,IAAI,CAACC,KAAK,GAAG,IAAIL,OAAO,CAACM,KAAK,CAAC,CAAC;IAChC,IAAI,CAACC,SAAS,GAAGH,QAAQ,IAAIH,SAAS;EACxC;EAEAE,QAAQ,CAACK,SAAS,CAACC,UAAU,GAAG,UAAUC,QAAQ,EAAE;IAClD,IAAIC,KAAK,GAAG,IAAI,CAAC,CAAC;;IAGlB,IAAI,CAACC,eAAe,GAAG,IAAI;IAC3B,IAAIC,QAAQ,GAAG,IAAI;IACnB,IAAIR,KAAK,GAAGQ,QAAQ,CAACR,KAAK;IAC1B,IAAIS,WAAW,GAAGD,QAAQ,CAACE,SAAS;IACpCF,QAAQ,CAACE,SAAS,GAAGL,QAAQ,CAAC,CAAC;IAC/B;;IAEA,IAAI,CAACI,WAAW,EAAE;MAChBT,KAAK,CAACW,SAAS,CAAC,CAAC;IACnB;IAEA,IAAIC,WAAW,GAAGC,eAAe,CAACR,QAAQ,CAAC;IAC3CA,QAAQ,CAACS,IAAI,CAACL,WAAW,CAAC,CAACM,GAAG,CAAC,UAAUC,GAAG,EAAE;MAC5CV,KAAK,CAACW,MAAM,CAACZ,QAAQ,EAAEW,GAAG,EAAEJ,WAAW,CAAC;IAC1C,CAAC,CAAC,CAACM,MAAM,CAAC,UAAUC,MAAM,EAAEC,MAAM,EAAE;MAClCd,KAAK,CAACe,SAAS,CAACZ,WAAW,EAAEJ,QAAQ,EAAEe,MAAM,EAAED,MAAM,EAAEP,WAAW,CAAC;IACrE,CAAC,CAAC,CAACU,MAAM,CAAC,UAAUN,GAAG,EAAE;MACvBhB,KAAK,CAACsB,MAAM,CAACb,WAAW,CAACc,gBAAgB,CAACP,GAAG,CAAC,CAAC;IACjD,CAAC,CAAC,CAACQ,OAAO,CAAC,CAAC;EACd,CAAC;EAED;EAEA1B,QAAQ,CAACK,SAAS,CAACsB,YAAY,GAAG,YAAY;IAC5C,IAAIpB,QAAQ,GAAG,IAAI,CAACK,SAAS,CAAC,CAAC;;IAE/B,IAAI,CAACL,QAAQ,EAAE;MACb;IACF;IAEAA,QAAQ,CAACqB,iBAAiB,CAAC,UAAUC,EAAE,EAAEX,GAAG,EAAE;MAC5CW,EAAE,CAACF,YAAY,CAACpB,QAAQ,EAAEW,GAAG,CAAC;IAChC,CAAC,EAAE,IAAI,CAAC;EACV,CAAC;EAED;EAEAlB,QAAQ,CAACK,SAAS,CAACyB,wBAAwB,GAAG,UAAUvB,QAAQ,EAAE;IAChE,IAAI,CAACwB,YAAY,GAAGhB,eAAe,CAACR,QAAQ,CAAC;IAC7C,IAAI,CAACK,SAAS,GAAG,IAAI;IACrB,IAAI,CAACV,KAAK,CAACW,SAAS,CAAC,CAAC;EACxB,CAAC;EAED;EAEAb,QAAQ,CAACK,SAAS,CAAC2B,iBAAiB,GAAG,UAAUC,UAAU,EAAE1B,QAAQ,EAAE;IACrE,IAAI,CAACE,eAAe,GAAG,EAAE;IAEzB,SAASyB,yBAAyBA,CAACL,EAAE,EAAE;MACrC,IAAI,CAACA,EAAE,CAACM,OAAO,IAAI,CAACC,cAAc,CAACP,EAAE,CAAC,EAAE;QACtCA,EAAE,CAACQ,WAAW,GAAG,IAAI;QACrBR,EAAE,CAACS,WAAW,CAAC,UAAU,CAAC,CAACC,UAAU,GAAG,IAAI;MAC9C;IACF;IAEA,KAAK,IAAIrB,GAAG,GAAGe,UAAU,CAACO,KAAK,EAAEtB,GAAG,GAAGe,UAAU,CAACQ,GAAG,EAAEvB,GAAG,EAAE,EAAE;MAC5D,IAAIwB,UAAU,GAAGnC,QAAQ,CAACoC,aAAa,CAACzB,GAAG,CAAC;MAE5C,IAAI0B,aAAa,CAACF,UAAU,CAAC,EAAE;QAC7B,IAAIb,EAAE,GAAG,IAAI,IAAI,CAACzB,SAAS,CAACG,QAAQ,EAAEW,GAAG,EAAE,IAAI,CAACa,YAAY,CAAC;QAC7DF,EAAE,CAACgB,QAAQ,CAACX,yBAAyB,CAAC;QACtC,IAAI,CAAChC,KAAK,CAACe,GAAG,CAACY,EAAE,CAAC;QAClBtB,QAAQ,CAACuC,gBAAgB,CAAC5B,GAAG,EAAEW,EAAE,CAAC;QAElC,IAAI,CAACpB,eAAe,CAACsC,IAAI,CAAClB,EAAE,CAAC;MAC/B;IACF;EACF,CAAC;EAED;EAEA7B,QAAQ,CAACK,SAAS,CAACmB,MAAM,GAAG,YAAY;IACtC,IAAI,CAACtB,KAAK,CAACW,SAAS,CAAC,CAAC;EACxB,CAAC;EAED;EAEAb,QAAQ,CAACK,SAAS,CAAC2C,YAAY,GAAG,UAAUC,EAAE,EAAE;IAC9CpD,OAAO,CAACqD,gBAAgB,CAAC,IAAI,CAACzC,eAAe,IAAI,IAAI,CAACP,KAAK,EAAE+C,EAAE,CAAC;EAClE,CAAC;EAEDjD,QAAQ,CAACK,SAAS,CAACc,MAAM,GAAG,UAAUZ,QAAQ,EAAEW,GAAG,EAAEJ,WAAW,EAAE;IAChE,IAAI4B,UAAU,GAAGnC,QAAQ,CAACoC,aAAa,CAACzB,GAAG,CAAC;IAE5C,IAAI,CAAC0B,aAAa,CAACF,UAAU,CAAC,EAAE;MAC9B;IACF;IAEA,IAAIb,EAAE,GAAG,IAAI,IAAI,CAACzB,SAAS,CAACG,QAAQ,EAAEW,GAAG,EAAEJ,WAAW,CAAC;IACvDP,QAAQ,CAACuC,gBAAgB,CAAC5B,GAAG,EAAEW,EAAE,CAAC;IAClC,IAAI,CAAC3B,KAAK,CAACe,GAAG,CAACY,EAAE,CAAC;EACpB,CAAC;EAED7B,QAAQ,CAACK,SAAS,CAACkB,SAAS,GAAG,UAAUZ,WAAW,EAAEwC,WAAW,EAAE7B,MAAM,EAAED,MAAM,EAAEP,WAAW,EAAE;IAC9F,IAAIsC,MAAM,GAAGzC,WAAW,CAACc,gBAAgB,CAACH,MAAM,CAAC;IAEjD,IAAI,CAACsB,aAAa,CAACO,WAAW,CAACR,aAAa,CAACtB,MAAM,CAAC,CAAC,EAAE;MACrD,IAAI,CAACnB,KAAK,CAACsB,MAAM,CAAC4B,MAAM,CAAC;MACzB;IACF;IAEA,IAAI,CAACA,MAAM,EAAE;MACXA,MAAM,GAAG,IAAI,IAAI,CAAChD,SAAS,CAAC+C,WAAW,EAAE9B,MAAM,EAAEP,WAAW,CAAC;IAC/D,CAAC,MAAM;MACLsC,MAAM,CAAC9C,UAAU,CAAC6C,WAAW,EAAE9B,MAAM,EAAEP,WAAW,CAAC;IACrD;IAEAqC,WAAW,CAACL,gBAAgB,CAACzB,MAAM,EAAE+B,MAAM,CAAC;IAC5C,IAAI,CAAClD,KAAK,CAACe,GAAG,CAACmC,MAAM,CAAC;EACxB,CAAC;EAED,OAAOpD,QAAQ;AACjB,CAAC,CAAC,CAAC;AAEH,SAASoC,cAAcA,CAACP,EAAE,EAAE;EAC1B,OAAOA,EAAE,CAACwB,SAAS,IAAIxB,EAAE,CAACwB,SAAS,CAACC,MAAM,GAAG,CAAC;AAChD;AAEA,SAASvC,eAAeA,CAACR,QAAQ,EAAE;EACjC,IAAIgD,SAAS,GAAGhD,QAAQ,CAACgD,SAAS;EAClC,IAAIC,aAAa,GAAGD,SAAS,CAACE,QAAQ,CAAC,UAAU,CAAC;EAClD,OAAO;IACLC,SAAS,EAAEH,SAAS,CAACE,QAAQ,CAAC,WAAW,CAAC,CAACE,YAAY,CAAC,CAAC;IACzDC,iBAAiB,EAAEJ,aAAa,CAACC,QAAQ,CAAC,CAAC,WAAW,CAAC,CAAC,CAACE,YAAY,CAAC,CAAC;IACvEE,aAAa,EAAEN,SAAS,CAACE,QAAQ,CAAC,CAAC,MAAM,EAAE,WAAW,CAAC,CAAC,CAACE,YAAY,CAAC,CAAC;IACvEG,eAAe,EAAEP,SAAS,CAACE,QAAQ,CAAC,CAAC,QAAQ,EAAE,WAAW,CAAC,CAAC,CAACE,YAAY,CAAC,CAAC;IAC3EI,gBAAgB,EAAEP,aAAa,CAACQ,GAAG,CAAC,UAAU,CAAC;IAC/CC,SAAS,EAAET,aAAa,CAACQ,GAAG,CAAC,WAAW,CAAC;IACzCE,KAAK,EAAEV,aAAa,CAACQ,GAAG,CAAC,OAAO,CAAC;IACjCG,iBAAiB,EAAEpE,oBAAoB,CAACwD,SAAS;EACnD,CAAC;AACH;AAEA,SAASa,UAAUA,CAACC,EAAE,EAAE;EACtB,OAAOC,KAAK,CAACD,EAAE,CAAC,CAAC,CAAC,CAAC,IAAIC,KAAK,CAACD,EAAE,CAAC,CAAC,CAAC,CAAC;AACrC;AAEA,SAASzB,aAAaA,CAAC2B,GAAG,EAAE;EAC1B,OAAOA,GAAG,IAAI,CAACH,UAAU,CAACG,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAACH,UAAU,CAACG,GAAG,CAAC,CAAC,CAAC,CAAC;AAC1D;AAEA,eAAevE,QAAQ"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}