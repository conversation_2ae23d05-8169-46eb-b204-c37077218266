{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nexport default function installScrollableLegendAction(registers) {\n  /**\r\n   * @event legendScroll\r\n   * @type {Object}\r\n   * @property {string} type 'legendScroll'\r\n   * @property {string} scrollDataIndex\r\n   */\n  registers.registerAction('legendScroll', 'legendscroll', function (payload, ecModel) {\n    var scrollDataIndex = payload.scrollDataIndex;\n    scrollDataIndex != null && ecModel.eachComponent({\n      mainType: 'legend',\n      subType: 'scroll',\n      query: payload\n    }, function (legendModel) {\n      legendModel.setScrollDataIndex(scrollDataIndex);\n    });\n  });\n}", "map": {"version": 3, "names": ["installScrollableLegendAction", "registers", "registerAction", "payload", "ecModel", "scrollDataIndex", "eachComponent", "mainType", "subType", "query", "legend<PERSON><PERSON><PERSON>", "setScrollDataIndex"], "sources": ["C:/WolfSystemV2/WolfOldFrontEnd/node_modules/echarts/lib/component/legend/scrollableLegendAction.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nexport default function installScrollableLegendAction(registers) {\n  /**\r\n   * @event legendScroll\r\n   * @type {Object}\r\n   * @property {string} type 'legendScroll'\r\n   * @property {string} scrollDataIndex\r\n   */\n  registers.registerAction('legendScroll', 'legendscroll', function (payload, ecModel) {\n    var scrollDataIndex = payload.scrollDataIndex;\n    scrollDataIndex != null && ecModel.eachComponent({\n      mainType: 'legend',\n      subType: 'scroll',\n      query: payload\n    }, function (legendModel) {\n      legendModel.setScrollDataIndex(scrollDataIndex);\n    });\n  });\n}"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,SAASA,6BAA6BA,CAACC,SAAS,EAAE;EAC/D;AACF;AACA;AACA;AACA;AACA;EACEA,SAAS,CAACC,cAAc,CAAC,cAAc,EAAE,cAAc,EAAE,UAAUC,OAAO,EAAEC,OAAO,EAAE;IACnF,IAAIC,eAAe,GAAGF,OAAO,CAACE,eAAe;IAC7CA,eAAe,IAAI,IAAI,IAAID,OAAO,CAACE,aAAa,CAAC;MAC/CC,QAAQ,EAAE,QAAQ;MAClBC,OAAO,EAAE,QAAQ;MACjBC,KAAK,EAAEN;IACT,CAAC,EAAE,UAAUO,WAAW,EAAE;MACxBA,WAAW,CAACC,kBAAkB,CAACN,eAAe,CAAC;IACjD,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}