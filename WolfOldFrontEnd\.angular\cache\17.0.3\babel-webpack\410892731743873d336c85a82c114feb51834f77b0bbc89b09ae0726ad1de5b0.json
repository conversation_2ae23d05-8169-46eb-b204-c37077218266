{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport { parse, stringify } from 'zrender/lib/tool/color.js';\nimport * as graphic from '../../util/graphic.js';\nimport { enableHoverEmphasis } from '../../util/states.js';\nimport { setLabelStyle, createTextStyle } from '../../label/labelStyle.js';\nimport { makeBackground } from '../helper/listComponent.js';\nimport * as layoutUtil from '../../util/layout.js';\nimport ComponentView from '../../view/Component.js';\nimport { createSymbol } from '../../util/symbol.js';\nimport { createOrUpdatePatternFromDecal } from '../../util/decal.js';\nvar curry = zrUtil.curry;\nvar each = zrUtil.each;\nvar Group = graphic.Group;\nvar LegendView = /** @class */\nfunction (_super) {\n  __extends(LegendView, _super);\n  function LegendView() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.type = LegendView.type;\n    _this.newlineDisabled = false;\n    return _this;\n  }\n  LegendView.prototype.init = function () {\n    this.group.add(this._contentGroup = new Group());\n    this.group.add(this._selectorGroup = new Group());\n    this._isFirstRender = true;\n  };\n  /**\r\n   * @protected\r\n   */\n\n  LegendView.prototype.getContentGroup = function () {\n    return this._contentGroup;\n  };\n  /**\r\n   * @protected\r\n   */\n\n  LegendView.prototype.getSelectorGroup = function () {\n    return this._selectorGroup;\n  };\n  /**\r\n   * @override\r\n   */\n\n  LegendView.prototype.render = function (legendModel, ecModel, api) {\n    var isFirstRender = this._isFirstRender;\n    this._isFirstRender = false;\n    this.resetInner();\n    if (!legendModel.get('show', true)) {\n      return;\n    }\n    var itemAlign = legendModel.get('align');\n    var orient = legendModel.get('orient');\n    if (!itemAlign || itemAlign === 'auto') {\n      itemAlign = legendModel.get('left') === 'right' && orient === 'vertical' ? 'right' : 'left';\n    } // selector has been normalized to an array in model\n\n    var selector = legendModel.get('selector', true);\n    var selectorPosition = legendModel.get('selectorPosition', true);\n    if (selector && (!selectorPosition || selectorPosition === 'auto')) {\n      selectorPosition = orient === 'horizontal' ? 'end' : 'start';\n    }\n    this.renderInner(itemAlign, legendModel, ecModel, api, selector, orient, selectorPosition); // Perform layout.\n\n    var positionInfo = legendModel.getBoxLayoutParams();\n    var viewportSize = {\n      width: api.getWidth(),\n      height: api.getHeight()\n    };\n    var padding = legendModel.get('padding');\n    var maxSize = layoutUtil.getLayoutRect(positionInfo, viewportSize, padding);\n    var mainRect = this.layoutInner(legendModel, itemAlign, maxSize, isFirstRender, selector, selectorPosition); // Place mainGroup, based on the calculated `mainRect`.\n\n    var layoutRect = layoutUtil.getLayoutRect(zrUtil.defaults({\n      width: mainRect.width,\n      height: mainRect.height\n    }, positionInfo), viewportSize, padding);\n    this.group.x = layoutRect.x - mainRect.x;\n    this.group.y = layoutRect.y - mainRect.y;\n    this.group.markRedraw(); // Render background after group is layout.\n\n    this.group.add(this._backgroundEl = makeBackground(mainRect, legendModel));\n  };\n  LegendView.prototype.resetInner = function () {\n    this.getContentGroup().removeAll();\n    this._backgroundEl && this.group.remove(this._backgroundEl);\n    this.getSelectorGroup().removeAll();\n  };\n  LegendView.prototype.renderInner = function (itemAlign, legendModel, ecModel, api, selector, orient, selectorPosition) {\n    var contentGroup = this.getContentGroup();\n    var legendDrawnMap = zrUtil.createHashMap();\n    var selectMode = legendModel.get('selectedMode');\n    var excludeSeriesId = [];\n    ecModel.eachRawSeries(function (seriesModel) {\n      !seriesModel.get('legendHoverLink') && excludeSeriesId.push(seriesModel.id);\n    });\n    each(legendModel.getData(), function (legendItemModel, dataIndex) {\n      var name = legendItemModel.get('name'); // Use empty string or \\n as a newline string\n\n      if (!this.newlineDisabled && (name === '' || name === '\\n')) {\n        var g = new Group(); // @ts-ignore\n\n        g.newline = true;\n        contentGroup.add(g);\n        return;\n      } // Representitive series.\n\n      var seriesModel = ecModel.getSeriesByName(name)[0];\n      if (legendDrawnMap.get(name)) {\n        // Have been drawn\n        return;\n      } // Legend to control series.\n\n      if (seriesModel) {\n        var data = seriesModel.getData();\n        var lineVisualStyle = data.getVisual('legendLineStyle') || {};\n        var legendIcon = data.getVisual('legendIcon');\n        /**\r\n         * `data.getVisual('style')` may be the color from the register\r\n         * in series. For example, for line series,\r\n         */\n\n        var style = data.getVisual('style');\n        var itemGroup = this._createItem(seriesModel, name, dataIndex, legendItemModel, legendModel, itemAlign, lineVisualStyle, style, legendIcon, selectMode, api);\n        itemGroup.on('click', curry(dispatchSelectAction, name, null, api, excludeSeriesId)).on('mouseover', curry(dispatchHighlightAction, seriesModel.name, null, api, excludeSeriesId)).on('mouseout', curry(dispatchDownplayAction, seriesModel.name, null, api, excludeSeriesId));\n        legendDrawnMap.set(name, true);\n      } else {\n        // Legend to control data. In pie and funnel.\n        ecModel.eachRawSeries(function (seriesModel) {\n          // In case multiple series has same data name\n          if (legendDrawnMap.get(name)) {\n            return;\n          }\n          if (seriesModel.legendVisualProvider) {\n            var provider = seriesModel.legendVisualProvider;\n            if (!provider.containName(name)) {\n              return;\n            }\n            var idx = provider.indexOfName(name);\n            var style = provider.getItemVisual(idx, 'style');\n            var legendIcon = provider.getItemVisual(idx, 'legendIcon');\n            var colorArr = parse(style.fill); // Color may be set to transparent in visualMap when data is out of range.\n            // Do not show nothing.\n\n            if (colorArr && colorArr[3] === 0) {\n              colorArr[3] = 0.2; // TODO color is set to 0, 0, 0, 0. Should show correct RGBA\n\n              style = zrUtil.extend(zrUtil.extend({}, style), {\n                fill: stringify(colorArr, 'rgba')\n              });\n            }\n            var itemGroup = this._createItem(seriesModel, name, dataIndex, legendItemModel, legendModel, itemAlign, {}, style, legendIcon, selectMode, api); // FIXME: consider different series has items with the same name.\n\n            itemGroup.on('click', curry(dispatchSelectAction, null, name, api, excludeSeriesId)) // Should not specify the series name, consider legend controls\n            // more than one pie series.\n            .on('mouseover', curry(dispatchHighlightAction, null, name, api, excludeSeriesId)).on('mouseout', curry(dispatchDownplayAction, null, name, api, excludeSeriesId));\n            legendDrawnMap.set(name, true);\n          }\n        }, this);\n      }\n      if (process.env.NODE_ENV !== 'production') {\n        if (!legendDrawnMap.get(name)) {\n          console.warn(name + ' series not exists. Legend data should be same with series name or data name.');\n        }\n      }\n    }, this);\n    if (selector) {\n      this._createSelector(selector, legendModel, api, orient, selectorPosition);\n    }\n  };\n  LegendView.prototype._createSelector = function (selector, legendModel, api, orient, selectorPosition) {\n    var selectorGroup = this.getSelectorGroup();\n    each(selector, function createSelectorButton(selectorItem) {\n      var type = selectorItem.type;\n      var labelText = new graphic.Text({\n        style: {\n          x: 0,\n          y: 0,\n          align: 'center',\n          verticalAlign: 'middle'\n        },\n        onclick: function () {\n          api.dispatchAction({\n            type: type === 'all' ? 'legendAllSelect' : 'legendInverseSelect'\n          });\n        }\n      });\n      selectorGroup.add(labelText);\n      var labelModel = legendModel.getModel('selectorLabel');\n      var emphasisLabelModel = legendModel.getModel(['emphasis', 'selectorLabel']);\n      setLabelStyle(labelText, {\n        normal: labelModel,\n        emphasis: emphasisLabelModel\n      }, {\n        defaultText: selectorItem.title\n      });\n      enableHoverEmphasis(labelText);\n    });\n  };\n  LegendView.prototype._createItem = function (seriesModel, name, dataIndex, legendItemModel, legendModel, itemAlign, lineVisualStyle, itemVisualStyle, legendIcon, selectMode, api) {\n    var drawType = seriesModel.visualDrawType;\n    var itemWidth = legendModel.get('itemWidth');\n    var itemHeight = legendModel.get('itemHeight');\n    var isSelected = legendModel.isSelected(name);\n    var iconRotate = legendItemModel.get('symbolRotate');\n    var symbolKeepAspect = legendItemModel.get('symbolKeepAspect');\n    var legendIconType = legendItemModel.get('icon');\n    legendIcon = legendIconType || legendIcon || 'roundRect';\n    var style = getLegendStyle(legendIcon, legendItemModel, lineVisualStyle, itemVisualStyle, drawType, isSelected, api);\n    var itemGroup = new Group();\n    var textStyleModel = legendItemModel.getModel('textStyle');\n    if (zrUtil.isFunction(seriesModel.getLegendIcon) && (!legendIconType || legendIconType === 'inherit')) {\n      // Series has specific way to define legend icon\n      itemGroup.add(seriesModel.getLegendIcon({\n        itemWidth: itemWidth,\n        itemHeight: itemHeight,\n        icon: legendIcon,\n        iconRotate: iconRotate,\n        itemStyle: style.itemStyle,\n        lineStyle: style.lineStyle,\n        symbolKeepAspect: symbolKeepAspect\n      }));\n    } else {\n      // Use default legend icon policy for most series\n      var rotate = legendIconType === 'inherit' && seriesModel.getData().getVisual('symbol') ? iconRotate === 'inherit' ? seriesModel.getData().getVisual('symbolRotate') : iconRotate : 0; // No rotation for no icon\n\n      itemGroup.add(getDefaultLegendIcon({\n        itemWidth: itemWidth,\n        itemHeight: itemHeight,\n        icon: legendIcon,\n        iconRotate: rotate,\n        itemStyle: style.itemStyle,\n        lineStyle: style.lineStyle,\n        symbolKeepAspect: symbolKeepAspect\n      }));\n    }\n    var textX = itemAlign === 'left' ? itemWidth + 5 : -5;\n    var textAlign = itemAlign;\n    var formatter = legendModel.get('formatter');\n    var content = name;\n    if (zrUtil.isString(formatter) && formatter) {\n      content = formatter.replace('{name}', name != null ? name : '');\n    } else if (zrUtil.isFunction(formatter)) {\n      content = formatter(name);\n    }\n    var textColor = isSelected ? textStyleModel.getTextColor() : legendItemModel.get('inactiveColor');\n    itemGroup.add(new graphic.Text({\n      style: createTextStyle(textStyleModel, {\n        text: content,\n        x: textX,\n        y: itemHeight / 2,\n        fill: textColor,\n        align: textAlign,\n        verticalAlign: 'middle'\n      }, {\n        inheritColor: textColor\n      })\n    })); // Add a invisible rect to increase the area of mouse hover\n\n    var hitRect = new graphic.Rect({\n      shape: itemGroup.getBoundingRect(),\n      invisible: true\n    });\n    var tooltipModel = legendItemModel.getModel('tooltip');\n    if (tooltipModel.get('show')) {\n      graphic.setTooltipConfig({\n        el: hitRect,\n        componentModel: legendModel,\n        itemName: name,\n        itemTooltipOption: tooltipModel.option\n      });\n    }\n    itemGroup.add(hitRect);\n    itemGroup.eachChild(function (child) {\n      child.silent = true;\n    });\n    hitRect.silent = !selectMode;\n    this.getContentGroup().add(itemGroup);\n    enableHoverEmphasis(itemGroup); // @ts-ignore\n\n    itemGroup.__legendDataIndex = dataIndex;\n    return itemGroup;\n  };\n  LegendView.prototype.layoutInner = function (legendModel, itemAlign, maxSize, isFirstRender, selector, selectorPosition) {\n    var contentGroup = this.getContentGroup();\n    var selectorGroup = this.getSelectorGroup(); // Place items in contentGroup.\n\n    layoutUtil.box(legendModel.get('orient'), contentGroup, legendModel.get('itemGap'), maxSize.width, maxSize.height);\n    var contentRect = contentGroup.getBoundingRect();\n    var contentPos = [-contentRect.x, -contentRect.y];\n    selectorGroup.markRedraw();\n    contentGroup.markRedraw();\n    if (selector) {\n      // Place buttons in selectorGroup\n      layoutUtil.box(\n      // Buttons in selectorGroup always layout horizontally\n      'horizontal', selectorGroup, legendModel.get('selectorItemGap', true));\n      var selectorRect = selectorGroup.getBoundingRect();\n      var selectorPos = [-selectorRect.x, -selectorRect.y];\n      var selectorButtonGap = legendModel.get('selectorButtonGap', true);\n      var orientIdx = legendModel.getOrient().index;\n      var wh = orientIdx === 0 ? 'width' : 'height';\n      var hw = orientIdx === 0 ? 'height' : 'width';\n      var yx = orientIdx === 0 ? 'y' : 'x';\n      if (selectorPosition === 'end') {\n        selectorPos[orientIdx] += contentRect[wh] + selectorButtonGap;\n      } else {\n        contentPos[orientIdx] += selectorRect[wh] + selectorButtonGap;\n      } // Always align selector to content as 'middle'\n\n      selectorPos[1 - orientIdx] += contentRect[hw] / 2 - selectorRect[hw] / 2;\n      selectorGroup.x = selectorPos[0];\n      selectorGroup.y = selectorPos[1];\n      contentGroup.x = contentPos[0];\n      contentGroup.y = contentPos[1];\n      var mainRect = {\n        x: 0,\n        y: 0\n      };\n      mainRect[wh] = contentRect[wh] + selectorButtonGap + selectorRect[wh];\n      mainRect[hw] = Math.max(contentRect[hw], selectorRect[hw]);\n      mainRect[yx] = Math.min(0, selectorRect[yx] + selectorPos[1 - orientIdx]);\n      return mainRect;\n    } else {\n      contentGroup.x = contentPos[0];\n      contentGroup.y = contentPos[1];\n      return this.group.getBoundingRect();\n    }\n  };\n  /**\r\n   * @protected\r\n   */\n\n  LegendView.prototype.remove = function () {\n    this.getContentGroup().removeAll();\n    this._isFirstRender = true;\n  };\n  LegendView.type = 'legend.plain';\n  return LegendView;\n}(ComponentView);\nfunction getLegendStyle(iconType, legendItemModel, lineVisualStyle, itemVisualStyle, drawType, isSelected, api) {\n  /**\r\n   * Use series style if is inherit;\r\n   * elsewise, use legend style\r\n   */\n  function handleCommonProps(style, visualStyle) {\n    // If lineStyle.width is 'auto', it is set to be 2 if series has border\n    if (style.lineWidth === 'auto') {\n      style.lineWidth = visualStyle.lineWidth > 0 ? 2 : 0;\n    }\n    each(style, function (propVal, propName) {\n      style[propName] === 'inherit' && (style[propName] = visualStyle[propName]);\n    });\n  } // itemStyle\n\n  var itemStyleModel = legendItemModel.getModel('itemStyle');\n  var itemStyle = itemStyleModel.getItemStyle();\n  var iconBrushType = iconType.lastIndexOf('empty', 0) === 0 ? 'fill' : 'stroke';\n  var decalStyle = itemStyleModel.getShallow('decal');\n  itemStyle.decal = !decalStyle || decalStyle === 'inherit' ? itemVisualStyle.decal : createOrUpdatePatternFromDecal(decalStyle, api);\n  if (itemStyle.fill === 'inherit') {\n    /**\r\n     * Series with visualDrawType as 'stroke' should have\r\n     * series stroke as legend fill\r\n     */\n    itemStyle.fill = itemVisualStyle[drawType];\n  }\n  if (itemStyle.stroke === 'inherit') {\n    /**\r\n     * icon type with \"emptyXXX\" should use fill color\r\n     * in visual style\r\n     */\n    itemStyle.stroke = itemVisualStyle[iconBrushType];\n  }\n  if (itemStyle.opacity === 'inherit') {\n    /**\r\n     * Use lineStyle.opacity if drawType is stroke\r\n     */\n    itemStyle.opacity = (drawType === 'fill' ? itemVisualStyle : lineVisualStyle).opacity;\n  }\n  handleCommonProps(itemStyle, itemVisualStyle); // lineStyle\n\n  var legendLineModel = legendItemModel.getModel('lineStyle');\n  var lineStyle = legendLineModel.getLineStyle();\n  handleCommonProps(lineStyle, lineVisualStyle); // Fix auto color to real color\n\n  itemStyle.fill === 'auto' && (itemStyle.fill = itemVisualStyle.fill);\n  itemStyle.stroke === 'auto' && (itemStyle.stroke = itemVisualStyle.fill);\n  lineStyle.stroke === 'auto' && (lineStyle.stroke = itemVisualStyle.fill);\n  if (!isSelected) {\n    var borderWidth = legendItemModel.get('inactiveBorderWidth');\n    /**\r\n     * Since stroke is set to be inactiveBorderColor, it may occur that\r\n     * there is no border in series but border in legend, so we need to\r\n     * use border only when series has border if is set to be auto\r\n     */\n\n    var visualHasBorder = itemStyle[iconBrushType];\n    itemStyle.lineWidth = borderWidth === 'auto' ? itemVisualStyle.lineWidth > 0 && visualHasBorder ? 2 : 0 : itemStyle.lineWidth;\n    itemStyle.fill = legendItemModel.get('inactiveColor');\n    itemStyle.stroke = legendItemModel.get('inactiveBorderColor');\n    lineStyle.stroke = legendLineModel.get('inactiveColor');\n    lineStyle.lineWidth = legendLineModel.get('inactiveWidth');\n  }\n  return {\n    itemStyle: itemStyle,\n    lineStyle: lineStyle\n  };\n}\nfunction getDefaultLegendIcon(opt) {\n  var symboType = opt.icon || 'roundRect';\n  var icon = createSymbol(symboType, 0, 0, opt.itemWidth, opt.itemHeight, opt.itemStyle.fill, opt.symbolKeepAspect);\n  icon.setStyle(opt.itemStyle);\n  icon.rotation = (opt.iconRotate || 0) * Math.PI / 180;\n  icon.setOrigin([opt.itemWidth / 2, opt.itemHeight / 2]);\n  if (symboType.indexOf('empty') > -1) {\n    icon.style.stroke = icon.style.fill;\n    icon.style.fill = '#fff';\n    icon.style.lineWidth = 2;\n  }\n  return icon;\n}\nfunction dispatchSelectAction(seriesName, dataName, api, excludeSeriesId) {\n  // downplay before unselect\n  dispatchDownplayAction(seriesName, dataName, api, excludeSeriesId);\n  api.dispatchAction({\n    type: 'legendToggleSelect',\n    name: seriesName != null ? seriesName : dataName\n  }); // highlight after select\n  // TODO highlight immediately may cause animation loss.\n\n  dispatchHighlightAction(seriesName, dataName, api, excludeSeriesId);\n}\nfunction isUseHoverLayer(api) {\n  var list = api.getZr().storage.getDisplayList();\n  var emphasisState;\n  var i = 0;\n  var len = list.length;\n  while (i < len && !(emphasisState = list[i].states.emphasis)) {\n    i++;\n  }\n  return emphasisState && emphasisState.hoverLayer;\n}\nfunction dispatchHighlightAction(seriesName, dataName, api, excludeSeriesId) {\n  // If element hover will move to a hoverLayer.\n  if (!isUseHoverLayer(api)) {\n    api.dispatchAction({\n      type: 'highlight',\n      seriesName: seriesName,\n      name: dataName,\n      excludeSeriesId: excludeSeriesId\n    });\n  }\n}\nfunction dispatchDownplayAction(seriesName, dataName, api, excludeSeriesId) {\n  // If element hover will move to a hoverLayer.\n  if (!isUseHoverLayer(api)) {\n    api.dispatchAction({\n      type: 'downplay',\n      seriesName: seriesName,\n      name: dataName,\n      excludeSeriesId: excludeSeriesId\n    });\n  }\n}\nexport default LegendView;", "map": {"version": 3, "names": ["__extends", "zrUtil", "parse", "stringify", "graphic", "enableHoverEmphasis", "setLabelStyle", "createTextStyle", "makeBackground", "layoutUtil", "ComponentView", "createSymbol", "createOrUpdatePatternFromDecal", "curry", "each", "Group", "LegendView", "_super", "_this", "apply", "arguments", "type", "newlineDisabled", "prototype", "init", "group", "add", "_contentGroup", "_selectorGroup", "_isFirstRender", "getContentGroup", "getSelectorGroup", "render", "legend<PERSON><PERSON><PERSON>", "ecModel", "api", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "resetInner", "get", "itemAlign", "orient", "selector", "selectorPosition", "renderInner", "positionInfo", "getBoxLayoutParams", "viewportSize", "width", "getWidth", "height", "getHeight", "padding", "maxSize", "getLayoutRect", "mainRect", "layoutInner", "layoutRect", "defaults", "x", "y", "mark<PERSON><PERSON><PERSON>", "_backgroundEl", "removeAll", "remove", "contentGroup", "legendDrawnMap", "createHashMap", "selectMode", "excludeSeriesId", "eachRawSeries", "seriesModel", "push", "id", "getData", "legendItemModel", "dataIndex", "name", "g", "newline", "getSeriesByName", "data", "lineVisualStyle", "getVisual", "legendIcon", "style", "itemGroup", "_createItem", "on", "dispatchSelectAction", "dispatchHighlightAction", "dispatchDownplayAction", "set", "legend<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "provider", "containName", "idx", "indexOfName", "getItemVisual", "colorArr", "fill", "extend", "process", "env", "NODE_ENV", "console", "warn", "_createSelector", "selectorGroup", "createSelectorButton", "selectorItem", "labelText", "Text", "align", "verticalAlign", "onclick", "dispatchAction", "labelModel", "getModel", "emphasisLabelModel", "normal", "emphasis", "defaultText", "title", "itemVisualStyle", "drawType", "visualDrawType", "itemWidth", "itemHeight", "isSelected", "iconRotate", "symbolKeepAspect", "legendIconType", "getLegendStyle", "textStyleModel", "isFunction", "getLegendIcon", "icon", "itemStyle", "lineStyle", "rotate", "getDefaultLegendIcon", "textX", "textAlign", "formatter", "content", "isString", "replace", "textColor", "getTextColor", "text", "inheritColor", "hitRect", "Rect", "shape", "getBoundingRect", "invisible", "tooltipModel", "setTooltipConfig", "el", "componentModel", "itemName", "itemTooltipOption", "option", "<PERSON><PERSON><PERSON><PERSON>", "child", "silent", "__legendDataIndex", "box", "contentRect", "contentPos", "selectorRect", "selectorPos", "selectorButtonGap", "orientIdx", "getOrient", "index", "wh", "hw", "yx", "Math", "max", "min", "iconType", "handleCommonProps", "visualStyle", "lineWidth", "propVal", "propName", "itemStyleModel", "getItemStyle", "iconBrushType", "lastIndexOf", "decalStyle", "getShallow", "decal", "stroke", "opacity", "legendLineModel", "getLineStyle", "borderWidth", "visualHasBorder", "opt", "symboType", "setStyle", "rotation", "PI", "<PERSON><PERSON><PERSON><PERSON>", "indexOf", "seriesName", "dataName", "isUseHoverLayer", "list", "getZr", "storage", "getDisplayList", "emphasisState", "i", "len", "length", "states", "hoverLayer"], "sources": ["C:/WolfSystemV2/WolfOldFrontEnd/node_modules/echarts/lib/component/legend/LegendView.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport { parse, stringify } from 'zrender/lib/tool/color.js';\nimport * as graphic from '../../util/graphic.js';\nimport { enableHoverEmphasis } from '../../util/states.js';\nimport { setLabelStyle, createTextStyle } from '../../label/labelStyle.js';\nimport { makeBackground } from '../helper/listComponent.js';\nimport * as layoutUtil from '../../util/layout.js';\nimport ComponentView from '../../view/Component.js';\nimport { createSymbol } from '../../util/symbol.js';\nimport { createOrUpdatePatternFromDecal } from '../../util/decal.js';\nvar curry = zrUtil.curry;\nvar each = zrUtil.each;\nvar Group = graphic.Group;\n\nvar LegendView =\n/** @class */\nfunction (_super) {\n  __extends(LegendView, _super);\n\n  function LegendView() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n\n    _this.type = LegendView.type;\n    _this.newlineDisabled = false;\n    return _this;\n  }\n\n  LegendView.prototype.init = function () {\n    this.group.add(this._contentGroup = new Group());\n    this.group.add(this._selectorGroup = new Group());\n    this._isFirstRender = true;\n  };\n  /**\r\n   * @protected\r\n   */\n\n\n  LegendView.prototype.getContentGroup = function () {\n    return this._contentGroup;\n  };\n  /**\r\n   * @protected\r\n   */\n\n\n  LegendView.prototype.getSelectorGroup = function () {\n    return this._selectorGroup;\n  };\n  /**\r\n   * @override\r\n   */\n\n\n  LegendView.prototype.render = function (legendModel, ecModel, api) {\n    var isFirstRender = this._isFirstRender;\n    this._isFirstRender = false;\n    this.resetInner();\n\n    if (!legendModel.get('show', true)) {\n      return;\n    }\n\n    var itemAlign = legendModel.get('align');\n    var orient = legendModel.get('orient');\n\n    if (!itemAlign || itemAlign === 'auto') {\n      itemAlign = legendModel.get('left') === 'right' && orient === 'vertical' ? 'right' : 'left';\n    } // selector has been normalized to an array in model\n\n\n    var selector = legendModel.get('selector', true);\n    var selectorPosition = legendModel.get('selectorPosition', true);\n\n    if (selector && (!selectorPosition || selectorPosition === 'auto')) {\n      selectorPosition = orient === 'horizontal' ? 'end' : 'start';\n    }\n\n    this.renderInner(itemAlign, legendModel, ecModel, api, selector, orient, selectorPosition); // Perform layout.\n\n    var positionInfo = legendModel.getBoxLayoutParams();\n    var viewportSize = {\n      width: api.getWidth(),\n      height: api.getHeight()\n    };\n    var padding = legendModel.get('padding');\n    var maxSize = layoutUtil.getLayoutRect(positionInfo, viewportSize, padding);\n    var mainRect = this.layoutInner(legendModel, itemAlign, maxSize, isFirstRender, selector, selectorPosition); // Place mainGroup, based on the calculated `mainRect`.\n\n    var layoutRect = layoutUtil.getLayoutRect(zrUtil.defaults({\n      width: mainRect.width,\n      height: mainRect.height\n    }, positionInfo), viewportSize, padding);\n    this.group.x = layoutRect.x - mainRect.x;\n    this.group.y = layoutRect.y - mainRect.y;\n    this.group.markRedraw(); // Render background after group is layout.\n\n    this.group.add(this._backgroundEl = makeBackground(mainRect, legendModel));\n  };\n\n  LegendView.prototype.resetInner = function () {\n    this.getContentGroup().removeAll();\n    this._backgroundEl && this.group.remove(this._backgroundEl);\n    this.getSelectorGroup().removeAll();\n  };\n\n  LegendView.prototype.renderInner = function (itemAlign, legendModel, ecModel, api, selector, orient, selectorPosition) {\n    var contentGroup = this.getContentGroup();\n    var legendDrawnMap = zrUtil.createHashMap();\n    var selectMode = legendModel.get('selectedMode');\n    var excludeSeriesId = [];\n    ecModel.eachRawSeries(function (seriesModel) {\n      !seriesModel.get('legendHoverLink') && excludeSeriesId.push(seriesModel.id);\n    });\n    each(legendModel.getData(), function (legendItemModel, dataIndex) {\n      var name = legendItemModel.get('name'); // Use empty string or \\n as a newline string\n\n      if (!this.newlineDisabled && (name === '' || name === '\\n')) {\n        var g = new Group(); // @ts-ignore\n\n        g.newline = true;\n        contentGroup.add(g);\n        return;\n      } // Representitive series.\n\n\n      var seriesModel = ecModel.getSeriesByName(name)[0];\n\n      if (legendDrawnMap.get(name)) {\n        // Have been drawn\n        return;\n      } // Legend to control series.\n\n\n      if (seriesModel) {\n        var data = seriesModel.getData();\n        var lineVisualStyle = data.getVisual('legendLineStyle') || {};\n        var legendIcon = data.getVisual('legendIcon');\n        /**\r\n         * `data.getVisual('style')` may be the color from the register\r\n         * in series. For example, for line series,\r\n         */\n\n        var style = data.getVisual('style');\n\n        var itemGroup = this._createItem(seriesModel, name, dataIndex, legendItemModel, legendModel, itemAlign, lineVisualStyle, style, legendIcon, selectMode, api);\n\n        itemGroup.on('click', curry(dispatchSelectAction, name, null, api, excludeSeriesId)).on('mouseover', curry(dispatchHighlightAction, seriesModel.name, null, api, excludeSeriesId)).on('mouseout', curry(dispatchDownplayAction, seriesModel.name, null, api, excludeSeriesId));\n        legendDrawnMap.set(name, true);\n      } else {\n        // Legend to control data. In pie and funnel.\n        ecModel.eachRawSeries(function (seriesModel) {\n          // In case multiple series has same data name\n          if (legendDrawnMap.get(name)) {\n            return;\n          }\n\n          if (seriesModel.legendVisualProvider) {\n            var provider = seriesModel.legendVisualProvider;\n\n            if (!provider.containName(name)) {\n              return;\n            }\n\n            var idx = provider.indexOfName(name);\n            var style = provider.getItemVisual(idx, 'style');\n            var legendIcon = provider.getItemVisual(idx, 'legendIcon');\n            var colorArr = parse(style.fill); // Color may be set to transparent in visualMap when data is out of range.\n            // Do not show nothing.\n\n            if (colorArr && colorArr[3] === 0) {\n              colorArr[3] = 0.2; // TODO color is set to 0, 0, 0, 0. Should show correct RGBA\n\n              style = zrUtil.extend(zrUtil.extend({}, style), {\n                fill: stringify(colorArr, 'rgba')\n              });\n            }\n\n            var itemGroup = this._createItem(seriesModel, name, dataIndex, legendItemModel, legendModel, itemAlign, {}, style, legendIcon, selectMode, api); // FIXME: consider different series has items with the same name.\n\n\n            itemGroup.on('click', curry(dispatchSelectAction, null, name, api, excludeSeriesId)) // Should not specify the series name, consider legend controls\n            // more than one pie series.\n            .on('mouseover', curry(dispatchHighlightAction, null, name, api, excludeSeriesId)).on('mouseout', curry(dispatchDownplayAction, null, name, api, excludeSeriesId));\n            legendDrawnMap.set(name, true);\n          }\n        }, this);\n      }\n\n      if (process.env.NODE_ENV !== 'production') {\n        if (!legendDrawnMap.get(name)) {\n          console.warn(name + ' series not exists. Legend data should be same with series name or data name.');\n        }\n      }\n    }, this);\n\n    if (selector) {\n      this._createSelector(selector, legendModel, api, orient, selectorPosition);\n    }\n  };\n\n  LegendView.prototype._createSelector = function (selector, legendModel, api, orient, selectorPosition) {\n    var selectorGroup = this.getSelectorGroup();\n    each(selector, function createSelectorButton(selectorItem) {\n      var type = selectorItem.type;\n      var labelText = new graphic.Text({\n        style: {\n          x: 0,\n          y: 0,\n          align: 'center',\n          verticalAlign: 'middle'\n        },\n        onclick: function () {\n          api.dispatchAction({\n            type: type === 'all' ? 'legendAllSelect' : 'legendInverseSelect'\n          });\n        }\n      });\n      selectorGroup.add(labelText);\n      var labelModel = legendModel.getModel('selectorLabel');\n      var emphasisLabelModel = legendModel.getModel(['emphasis', 'selectorLabel']);\n      setLabelStyle(labelText, {\n        normal: labelModel,\n        emphasis: emphasisLabelModel\n      }, {\n        defaultText: selectorItem.title\n      });\n      enableHoverEmphasis(labelText);\n    });\n  };\n\n  LegendView.prototype._createItem = function (seriesModel, name, dataIndex, legendItemModel, legendModel, itemAlign, lineVisualStyle, itemVisualStyle, legendIcon, selectMode, api) {\n    var drawType = seriesModel.visualDrawType;\n    var itemWidth = legendModel.get('itemWidth');\n    var itemHeight = legendModel.get('itemHeight');\n    var isSelected = legendModel.isSelected(name);\n    var iconRotate = legendItemModel.get('symbolRotate');\n    var symbolKeepAspect = legendItemModel.get('symbolKeepAspect');\n    var legendIconType = legendItemModel.get('icon');\n    legendIcon = legendIconType || legendIcon || 'roundRect';\n    var style = getLegendStyle(legendIcon, legendItemModel, lineVisualStyle, itemVisualStyle, drawType, isSelected, api);\n    var itemGroup = new Group();\n    var textStyleModel = legendItemModel.getModel('textStyle');\n\n    if (zrUtil.isFunction(seriesModel.getLegendIcon) && (!legendIconType || legendIconType === 'inherit')) {\n      // Series has specific way to define legend icon\n      itemGroup.add(seriesModel.getLegendIcon({\n        itemWidth: itemWidth,\n        itemHeight: itemHeight,\n        icon: legendIcon,\n        iconRotate: iconRotate,\n        itemStyle: style.itemStyle,\n        lineStyle: style.lineStyle,\n        symbolKeepAspect: symbolKeepAspect\n      }));\n    } else {\n      // Use default legend icon policy for most series\n      var rotate = legendIconType === 'inherit' && seriesModel.getData().getVisual('symbol') ? iconRotate === 'inherit' ? seriesModel.getData().getVisual('symbolRotate') : iconRotate : 0; // No rotation for no icon\n\n      itemGroup.add(getDefaultLegendIcon({\n        itemWidth: itemWidth,\n        itemHeight: itemHeight,\n        icon: legendIcon,\n        iconRotate: rotate,\n        itemStyle: style.itemStyle,\n        lineStyle: style.lineStyle,\n        symbolKeepAspect: symbolKeepAspect\n      }));\n    }\n\n    var textX = itemAlign === 'left' ? itemWidth + 5 : -5;\n    var textAlign = itemAlign;\n    var formatter = legendModel.get('formatter');\n    var content = name;\n\n    if (zrUtil.isString(formatter) && formatter) {\n      content = formatter.replace('{name}', name != null ? name : '');\n    } else if (zrUtil.isFunction(formatter)) {\n      content = formatter(name);\n    }\n\n    var textColor = isSelected ? textStyleModel.getTextColor() : legendItemModel.get('inactiveColor');\n    itemGroup.add(new graphic.Text({\n      style: createTextStyle(textStyleModel, {\n        text: content,\n        x: textX,\n        y: itemHeight / 2,\n        fill: textColor,\n        align: textAlign,\n        verticalAlign: 'middle'\n      }, {\n        inheritColor: textColor\n      })\n    })); // Add a invisible rect to increase the area of mouse hover\n\n    var hitRect = new graphic.Rect({\n      shape: itemGroup.getBoundingRect(),\n      invisible: true\n    });\n    var tooltipModel = legendItemModel.getModel('tooltip');\n\n    if (tooltipModel.get('show')) {\n      graphic.setTooltipConfig({\n        el: hitRect,\n        componentModel: legendModel,\n        itemName: name,\n        itemTooltipOption: tooltipModel.option\n      });\n    }\n\n    itemGroup.add(hitRect);\n    itemGroup.eachChild(function (child) {\n      child.silent = true;\n    });\n    hitRect.silent = !selectMode;\n    this.getContentGroup().add(itemGroup);\n    enableHoverEmphasis(itemGroup); // @ts-ignore\n\n    itemGroup.__legendDataIndex = dataIndex;\n    return itemGroup;\n  };\n\n  LegendView.prototype.layoutInner = function (legendModel, itemAlign, maxSize, isFirstRender, selector, selectorPosition) {\n    var contentGroup = this.getContentGroup();\n    var selectorGroup = this.getSelectorGroup(); // Place items in contentGroup.\n\n    layoutUtil.box(legendModel.get('orient'), contentGroup, legendModel.get('itemGap'), maxSize.width, maxSize.height);\n    var contentRect = contentGroup.getBoundingRect();\n    var contentPos = [-contentRect.x, -contentRect.y];\n    selectorGroup.markRedraw();\n    contentGroup.markRedraw();\n\n    if (selector) {\n      // Place buttons in selectorGroup\n      layoutUtil.box( // Buttons in selectorGroup always layout horizontally\n      'horizontal', selectorGroup, legendModel.get('selectorItemGap', true));\n      var selectorRect = selectorGroup.getBoundingRect();\n      var selectorPos = [-selectorRect.x, -selectorRect.y];\n      var selectorButtonGap = legendModel.get('selectorButtonGap', true);\n      var orientIdx = legendModel.getOrient().index;\n      var wh = orientIdx === 0 ? 'width' : 'height';\n      var hw = orientIdx === 0 ? 'height' : 'width';\n      var yx = orientIdx === 0 ? 'y' : 'x';\n\n      if (selectorPosition === 'end') {\n        selectorPos[orientIdx] += contentRect[wh] + selectorButtonGap;\n      } else {\n        contentPos[orientIdx] += selectorRect[wh] + selectorButtonGap;\n      } // Always align selector to content as 'middle'\n\n\n      selectorPos[1 - orientIdx] += contentRect[hw] / 2 - selectorRect[hw] / 2;\n      selectorGroup.x = selectorPos[0];\n      selectorGroup.y = selectorPos[1];\n      contentGroup.x = contentPos[0];\n      contentGroup.y = contentPos[1];\n      var mainRect = {\n        x: 0,\n        y: 0\n      };\n      mainRect[wh] = contentRect[wh] + selectorButtonGap + selectorRect[wh];\n      mainRect[hw] = Math.max(contentRect[hw], selectorRect[hw]);\n      mainRect[yx] = Math.min(0, selectorRect[yx] + selectorPos[1 - orientIdx]);\n      return mainRect;\n    } else {\n      contentGroup.x = contentPos[0];\n      contentGroup.y = contentPos[1];\n      return this.group.getBoundingRect();\n    }\n  };\n  /**\r\n   * @protected\r\n   */\n\n\n  LegendView.prototype.remove = function () {\n    this.getContentGroup().removeAll();\n    this._isFirstRender = true;\n  };\n\n  LegendView.type = 'legend.plain';\n  return LegendView;\n}(ComponentView);\n\nfunction getLegendStyle(iconType, legendItemModel, lineVisualStyle, itemVisualStyle, drawType, isSelected, api) {\n  /**\r\n   * Use series style if is inherit;\r\n   * elsewise, use legend style\r\n   */\n  function handleCommonProps(style, visualStyle) {\n    // If lineStyle.width is 'auto', it is set to be 2 if series has border\n    if (style.lineWidth === 'auto') {\n      style.lineWidth = visualStyle.lineWidth > 0 ? 2 : 0;\n    }\n\n    each(style, function (propVal, propName) {\n      style[propName] === 'inherit' && (style[propName] = visualStyle[propName]);\n    });\n  } // itemStyle\n\n\n  var itemStyleModel = legendItemModel.getModel('itemStyle');\n  var itemStyle = itemStyleModel.getItemStyle();\n  var iconBrushType = iconType.lastIndexOf('empty', 0) === 0 ? 'fill' : 'stroke';\n  var decalStyle = itemStyleModel.getShallow('decal');\n  itemStyle.decal = !decalStyle || decalStyle === 'inherit' ? itemVisualStyle.decal : createOrUpdatePatternFromDecal(decalStyle, api);\n\n  if (itemStyle.fill === 'inherit') {\n    /**\r\n     * Series with visualDrawType as 'stroke' should have\r\n     * series stroke as legend fill\r\n     */\n    itemStyle.fill = itemVisualStyle[drawType];\n  }\n\n  if (itemStyle.stroke === 'inherit') {\n    /**\r\n     * icon type with \"emptyXXX\" should use fill color\r\n     * in visual style\r\n     */\n    itemStyle.stroke = itemVisualStyle[iconBrushType];\n  }\n\n  if (itemStyle.opacity === 'inherit') {\n    /**\r\n     * Use lineStyle.opacity if drawType is stroke\r\n     */\n    itemStyle.opacity = (drawType === 'fill' ? itemVisualStyle : lineVisualStyle).opacity;\n  }\n\n  handleCommonProps(itemStyle, itemVisualStyle); // lineStyle\n\n  var legendLineModel = legendItemModel.getModel('lineStyle');\n  var lineStyle = legendLineModel.getLineStyle();\n  handleCommonProps(lineStyle, lineVisualStyle); // Fix auto color to real color\n\n  itemStyle.fill === 'auto' && (itemStyle.fill = itemVisualStyle.fill);\n  itemStyle.stroke === 'auto' && (itemStyle.stroke = itemVisualStyle.fill);\n  lineStyle.stroke === 'auto' && (lineStyle.stroke = itemVisualStyle.fill);\n\n  if (!isSelected) {\n    var borderWidth = legendItemModel.get('inactiveBorderWidth');\n    /**\r\n     * Since stroke is set to be inactiveBorderColor, it may occur that\r\n     * there is no border in series but border in legend, so we need to\r\n     * use border only when series has border if is set to be auto\r\n     */\n\n    var visualHasBorder = itemStyle[iconBrushType];\n    itemStyle.lineWidth = borderWidth === 'auto' ? itemVisualStyle.lineWidth > 0 && visualHasBorder ? 2 : 0 : itemStyle.lineWidth;\n    itemStyle.fill = legendItemModel.get('inactiveColor');\n    itemStyle.stroke = legendItemModel.get('inactiveBorderColor');\n    lineStyle.stroke = legendLineModel.get('inactiveColor');\n    lineStyle.lineWidth = legendLineModel.get('inactiveWidth');\n  }\n\n  return {\n    itemStyle: itemStyle,\n    lineStyle: lineStyle\n  };\n}\n\nfunction getDefaultLegendIcon(opt) {\n  var symboType = opt.icon || 'roundRect';\n  var icon = createSymbol(symboType, 0, 0, opt.itemWidth, opt.itemHeight, opt.itemStyle.fill, opt.symbolKeepAspect);\n  icon.setStyle(opt.itemStyle);\n  icon.rotation = (opt.iconRotate || 0) * Math.PI / 180;\n  icon.setOrigin([opt.itemWidth / 2, opt.itemHeight / 2]);\n\n  if (symboType.indexOf('empty') > -1) {\n    icon.style.stroke = icon.style.fill;\n    icon.style.fill = '#fff';\n    icon.style.lineWidth = 2;\n  }\n\n  return icon;\n}\n\nfunction dispatchSelectAction(seriesName, dataName, api, excludeSeriesId) {\n  // downplay before unselect\n  dispatchDownplayAction(seriesName, dataName, api, excludeSeriesId);\n  api.dispatchAction({\n    type: 'legendToggleSelect',\n    name: seriesName != null ? seriesName : dataName\n  }); // highlight after select\n  // TODO highlight immediately may cause animation loss.\n\n  dispatchHighlightAction(seriesName, dataName, api, excludeSeriesId);\n}\n\nfunction isUseHoverLayer(api) {\n  var list = api.getZr().storage.getDisplayList();\n  var emphasisState;\n  var i = 0;\n  var len = list.length;\n\n  while (i < len && !(emphasisState = list[i].states.emphasis)) {\n    i++;\n  }\n\n  return emphasisState && emphasisState.hoverLayer;\n}\n\nfunction dispatchHighlightAction(seriesName, dataName, api, excludeSeriesId) {\n  // If element hover will move to a hoverLayer.\n  if (!isUseHoverLayer(api)) {\n    api.dispatchAction({\n      type: 'highlight',\n      seriesName: seriesName,\n      name: dataName,\n      excludeSeriesId: excludeSeriesId\n    });\n  }\n}\n\nfunction dispatchDownplayAction(seriesName, dataName, api, excludeSeriesId) {\n  // If element hover will move to a hoverLayer.\n  if (!isUseHoverLayer(api)) {\n    api.dispatchAction({\n      type: 'downplay',\n      seriesName: seriesName,\n      name: dataName,\n      excludeSeriesId: excludeSeriesId\n    });\n  }\n}\n\nexport default LegendView;"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,SAAS,QAAQ,OAAO;AACjC,OAAO,KAAKC,MAAM,MAAM,0BAA0B;AAClD,SAASC,KAAK,EAAEC,SAAS,QAAQ,2BAA2B;AAC5D,OAAO,KAAKC,OAAO,MAAM,uBAAuB;AAChD,SAASC,mBAAmB,QAAQ,sBAAsB;AAC1D,SAASC,aAAa,EAAEC,eAAe,QAAQ,2BAA2B;AAC1E,SAASC,cAAc,QAAQ,4BAA4B;AAC3D,OAAO,KAAKC,UAAU,MAAM,sBAAsB;AAClD,OAAOC,aAAa,MAAM,yBAAyB;AACnD,SAASC,YAAY,QAAQ,sBAAsB;AACnD,SAASC,8BAA8B,QAAQ,qBAAqB;AACpE,IAAIC,KAAK,GAAGZ,MAAM,CAACY,KAAK;AACxB,IAAIC,IAAI,GAAGb,MAAM,CAACa,IAAI;AACtB,IAAIC,KAAK,GAAGX,OAAO,CAACW,KAAK;AAEzB,IAAIC,UAAU,GACd;AACA,UAAUC,MAAM,EAAE;EAChBjB,SAAS,CAACgB,UAAU,EAAEC,MAAM,CAAC;EAE7B,SAASD,UAAUA,CAAA,EAAG;IACpB,IAAIE,KAAK,GAAGD,MAAM,KAAK,IAAI,IAAIA,MAAM,CAACE,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC,IAAI,IAAI;IAEpEF,KAAK,CAACG,IAAI,GAAGL,UAAU,CAACK,IAAI;IAC5BH,KAAK,CAACI,eAAe,GAAG,KAAK;IAC7B,OAAOJ,KAAK;EACd;EAEAF,UAAU,CAACO,SAAS,CAACC,IAAI,GAAG,YAAY;IACtC,IAAI,CAACC,KAAK,CAACC,GAAG,CAAC,IAAI,CAACC,aAAa,GAAG,IAAIZ,KAAK,CAAC,CAAC,CAAC;IAChD,IAAI,CAACU,KAAK,CAACC,GAAG,CAAC,IAAI,CAACE,cAAc,GAAG,IAAIb,KAAK,CAAC,CAAC,CAAC;IACjD,IAAI,CAACc,cAAc,GAAG,IAAI;EAC5B,CAAC;EACD;AACF;AACA;;EAGEb,UAAU,CAACO,SAAS,CAACO,eAAe,GAAG,YAAY;IACjD,OAAO,IAAI,CAACH,aAAa;EAC3B,CAAC;EACD;AACF;AACA;;EAGEX,UAAU,CAACO,SAAS,CAACQ,gBAAgB,GAAG,YAAY;IAClD,OAAO,IAAI,CAACH,cAAc;EAC5B,CAAC;EACD;AACF;AACA;;EAGEZ,UAAU,CAACO,SAAS,CAACS,MAAM,GAAG,UAAUC,WAAW,EAAEC,OAAO,EAAEC,GAAG,EAAE;IACjE,IAAIC,aAAa,GAAG,IAAI,CAACP,cAAc;IACvC,IAAI,CAACA,cAAc,GAAG,KAAK;IAC3B,IAAI,CAACQ,UAAU,CAAC,CAAC;IAEjB,IAAI,CAACJ,WAAW,CAACK,GAAG,CAAC,MAAM,EAAE,IAAI,CAAC,EAAE;MAClC;IACF;IAEA,IAAIC,SAAS,GAAGN,WAAW,CAACK,GAAG,CAAC,OAAO,CAAC;IACxC,IAAIE,MAAM,GAAGP,WAAW,CAACK,GAAG,CAAC,QAAQ,CAAC;IAEtC,IAAI,CAACC,SAAS,IAAIA,SAAS,KAAK,MAAM,EAAE;MACtCA,SAAS,GAAGN,WAAW,CAACK,GAAG,CAAC,MAAM,CAAC,KAAK,OAAO,IAAIE,MAAM,KAAK,UAAU,GAAG,OAAO,GAAG,MAAM;IAC7F,CAAC,CAAC;;IAGF,IAAIC,QAAQ,GAAGR,WAAW,CAACK,GAAG,CAAC,UAAU,EAAE,IAAI,CAAC;IAChD,IAAII,gBAAgB,GAAGT,WAAW,CAACK,GAAG,CAAC,kBAAkB,EAAE,IAAI,CAAC;IAEhE,IAAIG,QAAQ,KAAK,CAACC,gBAAgB,IAAIA,gBAAgB,KAAK,MAAM,CAAC,EAAE;MAClEA,gBAAgB,GAAGF,MAAM,KAAK,YAAY,GAAG,KAAK,GAAG,OAAO;IAC9D;IAEA,IAAI,CAACG,WAAW,CAACJ,SAAS,EAAEN,WAAW,EAAEC,OAAO,EAAEC,GAAG,EAAEM,QAAQ,EAAED,MAAM,EAAEE,gBAAgB,CAAC,CAAC,CAAC;;IAE5F,IAAIE,YAAY,GAAGX,WAAW,CAACY,kBAAkB,CAAC,CAAC;IACnD,IAAIC,YAAY,GAAG;MACjBC,KAAK,EAAEZ,GAAG,CAACa,QAAQ,CAAC,CAAC;MACrBC,MAAM,EAAEd,GAAG,CAACe,SAAS,CAAC;IACxB,CAAC;IACD,IAAIC,OAAO,GAAGlB,WAAW,CAACK,GAAG,CAAC,SAAS,CAAC;IACxC,IAAIc,OAAO,GAAG3C,UAAU,CAAC4C,aAAa,CAACT,YAAY,EAAEE,YAAY,EAAEK,OAAO,CAAC;IAC3E,IAAIG,QAAQ,GAAG,IAAI,CAACC,WAAW,CAACtB,WAAW,EAAEM,SAAS,EAAEa,OAAO,EAAEhB,aAAa,EAAEK,QAAQ,EAAEC,gBAAgB,CAAC,CAAC,CAAC;;IAE7G,IAAIc,UAAU,GAAG/C,UAAU,CAAC4C,aAAa,CAACpD,MAAM,CAACwD,QAAQ,CAAC;MACxDV,KAAK,EAAEO,QAAQ,CAACP,KAAK;MACrBE,MAAM,EAAEK,QAAQ,CAACL;IACnB,CAAC,EAAEL,YAAY,CAAC,EAAEE,YAAY,EAAEK,OAAO,CAAC;IACxC,IAAI,CAAC1B,KAAK,CAACiC,CAAC,GAAGF,UAAU,CAACE,CAAC,GAAGJ,QAAQ,CAACI,CAAC;IACxC,IAAI,CAACjC,KAAK,CAACkC,CAAC,GAAGH,UAAU,CAACG,CAAC,GAAGL,QAAQ,CAACK,CAAC;IACxC,IAAI,CAAClC,KAAK,CAACmC,UAAU,CAAC,CAAC,CAAC,CAAC;;IAEzB,IAAI,CAACnC,KAAK,CAACC,GAAG,CAAC,IAAI,CAACmC,aAAa,GAAGrD,cAAc,CAAC8C,QAAQ,EAAErB,WAAW,CAAC,CAAC;EAC5E,CAAC;EAEDjB,UAAU,CAACO,SAAS,CAACc,UAAU,GAAG,YAAY;IAC5C,IAAI,CAACP,eAAe,CAAC,CAAC,CAACgC,SAAS,CAAC,CAAC;IAClC,IAAI,CAACD,aAAa,IAAI,IAAI,CAACpC,KAAK,CAACsC,MAAM,CAAC,IAAI,CAACF,aAAa,CAAC;IAC3D,IAAI,CAAC9B,gBAAgB,CAAC,CAAC,CAAC+B,SAAS,CAAC,CAAC;EACrC,CAAC;EAED9C,UAAU,CAACO,SAAS,CAACoB,WAAW,GAAG,UAAUJ,SAAS,EAAEN,WAAW,EAAEC,OAAO,EAAEC,GAAG,EAAEM,QAAQ,EAAED,MAAM,EAAEE,gBAAgB,EAAE;IACrH,IAAIsB,YAAY,GAAG,IAAI,CAAClC,eAAe,CAAC,CAAC;IACzC,IAAImC,cAAc,GAAGhE,MAAM,CAACiE,aAAa,CAAC,CAAC;IAC3C,IAAIC,UAAU,GAAGlC,WAAW,CAACK,GAAG,CAAC,cAAc,CAAC;IAChD,IAAI8B,eAAe,GAAG,EAAE;IACxBlC,OAAO,CAACmC,aAAa,CAAC,UAAUC,WAAW,EAAE;MAC3C,CAACA,WAAW,CAAChC,GAAG,CAAC,iBAAiB,CAAC,IAAI8B,eAAe,CAACG,IAAI,CAACD,WAAW,CAACE,EAAE,CAAC;IAC7E,CAAC,CAAC;IACF1D,IAAI,CAACmB,WAAW,CAACwC,OAAO,CAAC,CAAC,EAAE,UAAUC,eAAe,EAAEC,SAAS,EAAE;MAChE,IAAIC,IAAI,GAAGF,eAAe,CAACpC,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC;;MAExC,IAAI,CAAC,IAAI,CAAChB,eAAe,KAAKsD,IAAI,KAAK,EAAE,IAAIA,IAAI,KAAK,IAAI,CAAC,EAAE;QAC3D,IAAIC,CAAC,GAAG,IAAI9D,KAAK,CAAC,CAAC,CAAC,CAAC;;QAErB8D,CAAC,CAACC,OAAO,GAAG,IAAI;QAChBd,YAAY,CAACtC,GAAG,CAACmD,CAAC,CAAC;QACnB;MACF,CAAC,CAAC;;MAGF,IAAIP,WAAW,GAAGpC,OAAO,CAAC6C,eAAe,CAACH,IAAI,CAAC,CAAC,CAAC,CAAC;MAElD,IAAIX,cAAc,CAAC3B,GAAG,CAACsC,IAAI,CAAC,EAAE;QAC5B;QACA;MACF,CAAC,CAAC;;MAGF,IAAIN,WAAW,EAAE;QACf,IAAIU,IAAI,GAAGV,WAAW,CAACG,OAAO,CAAC,CAAC;QAChC,IAAIQ,eAAe,GAAGD,IAAI,CAACE,SAAS,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;QAC7D,IAAIC,UAAU,GAAGH,IAAI,CAACE,SAAS,CAAC,YAAY,CAAC;QAC7C;AACR;AACA;AACA;;QAEQ,IAAIE,KAAK,GAAGJ,IAAI,CAACE,SAAS,CAAC,OAAO,CAAC;QAEnC,IAAIG,SAAS,GAAG,IAAI,CAACC,WAAW,CAAChB,WAAW,EAAEM,IAAI,EAAED,SAAS,EAAED,eAAe,EAAEzC,WAAW,EAAEM,SAAS,EAAE0C,eAAe,EAAEG,KAAK,EAAED,UAAU,EAAEhB,UAAU,EAAEhC,GAAG,CAAC;QAE5JkD,SAAS,CAACE,EAAE,CAAC,OAAO,EAAE1E,KAAK,CAAC2E,oBAAoB,EAAEZ,IAAI,EAAE,IAAI,EAAEzC,GAAG,EAAEiC,eAAe,CAAC,CAAC,CAACmB,EAAE,CAAC,WAAW,EAAE1E,KAAK,CAAC4E,uBAAuB,EAAEnB,WAAW,CAACM,IAAI,EAAE,IAAI,EAAEzC,GAAG,EAAEiC,eAAe,CAAC,CAAC,CAACmB,EAAE,CAAC,UAAU,EAAE1E,KAAK,CAAC6E,sBAAsB,EAAEpB,WAAW,CAACM,IAAI,EAAE,IAAI,EAAEzC,GAAG,EAAEiC,eAAe,CAAC,CAAC;QAC9QH,cAAc,CAAC0B,GAAG,CAACf,IAAI,EAAE,IAAI,CAAC;MAChC,CAAC,MAAM;QACL;QACA1C,OAAO,CAACmC,aAAa,CAAC,UAAUC,WAAW,EAAE;UAC3C;UACA,IAAIL,cAAc,CAAC3B,GAAG,CAACsC,IAAI,CAAC,EAAE;YAC5B;UACF;UAEA,IAAIN,WAAW,CAACsB,oBAAoB,EAAE;YACpC,IAAIC,QAAQ,GAAGvB,WAAW,CAACsB,oBAAoB;YAE/C,IAAI,CAACC,QAAQ,CAACC,WAAW,CAAClB,IAAI,CAAC,EAAE;cAC/B;YACF;YAEA,IAAImB,GAAG,GAAGF,QAAQ,CAACG,WAAW,CAACpB,IAAI,CAAC;YACpC,IAAIQ,KAAK,GAAGS,QAAQ,CAACI,aAAa,CAACF,GAAG,EAAE,OAAO,CAAC;YAChD,IAAIZ,UAAU,GAAGU,QAAQ,CAACI,aAAa,CAACF,GAAG,EAAE,YAAY,CAAC;YAC1D,IAAIG,QAAQ,GAAGhG,KAAK,CAACkF,KAAK,CAACe,IAAI,CAAC,CAAC,CAAC;YAClC;;YAEA,IAAID,QAAQ,IAAIA,QAAQ,CAAC,CAAC,CAAC,KAAK,CAAC,EAAE;cACjCA,QAAQ,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC;;cAEnBd,KAAK,GAAGnF,MAAM,CAACmG,MAAM,CAACnG,MAAM,CAACmG,MAAM,CAAC,CAAC,CAAC,EAAEhB,KAAK,CAAC,EAAE;gBAC9Ce,IAAI,EAAEhG,SAAS,CAAC+F,QAAQ,EAAE,MAAM;cAClC,CAAC,CAAC;YACJ;YAEA,IAAIb,SAAS,GAAG,IAAI,CAACC,WAAW,CAAChB,WAAW,EAAEM,IAAI,EAAED,SAAS,EAAED,eAAe,EAAEzC,WAAW,EAAEM,SAAS,EAAE,CAAC,CAAC,EAAE6C,KAAK,EAAED,UAAU,EAAEhB,UAAU,EAAEhC,GAAG,CAAC,CAAC,CAAC;;YAGjJkD,SAAS,CAACE,EAAE,CAAC,OAAO,EAAE1E,KAAK,CAAC2E,oBAAoB,EAAE,IAAI,EAAEZ,IAAI,EAAEzC,GAAG,EAAEiC,eAAe,CAAC,CAAC,CAAC;YACrF;YAAA,CACCmB,EAAE,CAAC,WAAW,EAAE1E,KAAK,CAAC4E,uBAAuB,EAAE,IAAI,EAAEb,IAAI,EAAEzC,GAAG,EAAEiC,eAAe,CAAC,CAAC,CAACmB,EAAE,CAAC,UAAU,EAAE1E,KAAK,CAAC6E,sBAAsB,EAAE,IAAI,EAAEd,IAAI,EAAEzC,GAAG,EAAEiC,eAAe,CAAC,CAAC;YAClKH,cAAc,CAAC0B,GAAG,CAACf,IAAI,EAAE,IAAI,CAAC;UAChC;QACF,CAAC,EAAE,IAAI,CAAC;MACV;MAEA,IAAIyB,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;QACzC,IAAI,CAACtC,cAAc,CAAC3B,GAAG,CAACsC,IAAI,CAAC,EAAE;UAC7B4B,OAAO,CAACC,IAAI,CAAC7B,IAAI,GAAG,+EAA+E,CAAC;QACtG;MACF;IACF,CAAC,EAAE,IAAI,CAAC;IAER,IAAInC,QAAQ,EAAE;MACZ,IAAI,CAACiE,eAAe,CAACjE,QAAQ,EAAER,WAAW,EAAEE,GAAG,EAAEK,MAAM,EAAEE,gBAAgB,CAAC;IAC5E;EACF,CAAC;EAED1B,UAAU,CAACO,SAAS,CAACmF,eAAe,GAAG,UAAUjE,QAAQ,EAAER,WAAW,EAAEE,GAAG,EAAEK,MAAM,EAAEE,gBAAgB,EAAE;IACrG,IAAIiE,aAAa,GAAG,IAAI,CAAC5E,gBAAgB,CAAC,CAAC;IAC3CjB,IAAI,CAAC2B,QAAQ,EAAE,SAASmE,oBAAoBA,CAACC,YAAY,EAAE;MACzD,IAAIxF,IAAI,GAAGwF,YAAY,CAACxF,IAAI;MAC5B,IAAIyF,SAAS,GAAG,IAAI1G,OAAO,CAAC2G,IAAI,CAAC;QAC/B3B,KAAK,EAAE;UACL1B,CAAC,EAAE,CAAC;UACJC,CAAC,EAAE,CAAC;UACJqD,KAAK,EAAE,QAAQ;UACfC,aAAa,EAAE;QACjB,CAAC;QACDC,OAAO,EAAE,SAAAA,CAAA,EAAY;UACnB/E,GAAG,CAACgF,cAAc,CAAC;YACjB9F,IAAI,EAAEA,IAAI,KAAK,KAAK,GAAG,iBAAiB,GAAG;UAC7C,CAAC,CAAC;QACJ;MACF,CAAC,CAAC;MACFsF,aAAa,CAACjF,GAAG,CAACoF,SAAS,CAAC;MAC5B,IAAIM,UAAU,GAAGnF,WAAW,CAACoF,QAAQ,CAAC,eAAe,CAAC;MACtD,IAAIC,kBAAkB,GAAGrF,WAAW,CAACoF,QAAQ,CAAC,CAAC,UAAU,EAAE,eAAe,CAAC,CAAC;MAC5E/G,aAAa,CAACwG,SAAS,EAAE;QACvBS,MAAM,EAAEH,UAAU;QAClBI,QAAQ,EAAEF;MACZ,CAAC,EAAE;QACDG,WAAW,EAAEZ,YAAY,CAACa;MAC5B,CAAC,CAAC;MACFrH,mBAAmB,CAACyG,SAAS,CAAC;IAChC,CAAC,CAAC;EACJ,CAAC;EAED9F,UAAU,CAACO,SAAS,CAAC+D,WAAW,GAAG,UAAUhB,WAAW,EAAEM,IAAI,EAAED,SAAS,EAAED,eAAe,EAAEzC,WAAW,EAAEM,SAAS,EAAE0C,eAAe,EAAE0C,eAAe,EAAExC,UAAU,EAAEhB,UAAU,EAAEhC,GAAG,EAAE;IACjL,IAAIyF,QAAQ,GAAGtD,WAAW,CAACuD,cAAc;IACzC,IAAIC,SAAS,GAAG7F,WAAW,CAACK,GAAG,CAAC,WAAW,CAAC;IAC5C,IAAIyF,UAAU,GAAG9F,WAAW,CAACK,GAAG,CAAC,YAAY,CAAC;IAC9C,IAAI0F,UAAU,GAAG/F,WAAW,CAAC+F,UAAU,CAACpD,IAAI,CAAC;IAC7C,IAAIqD,UAAU,GAAGvD,eAAe,CAACpC,GAAG,CAAC,cAAc,CAAC;IACpD,IAAI4F,gBAAgB,GAAGxD,eAAe,CAACpC,GAAG,CAAC,kBAAkB,CAAC;IAC9D,IAAI6F,cAAc,GAAGzD,eAAe,CAACpC,GAAG,CAAC,MAAM,CAAC;IAChD6C,UAAU,GAAGgD,cAAc,IAAIhD,UAAU,IAAI,WAAW;IACxD,IAAIC,KAAK,GAAGgD,cAAc,CAACjD,UAAU,EAAET,eAAe,EAAEO,eAAe,EAAE0C,eAAe,EAAEC,QAAQ,EAAEI,UAAU,EAAE7F,GAAG,CAAC;IACpH,IAAIkD,SAAS,GAAG,IAAItE,KAAK,CAAC,CAAC;IAC3B,IAAIsH,cAAc,GAAG3D,eAAe,CAAC2C,QAAQ,CAAC,WAAW,CAAC;IAE1D,IAAIpH,MAAM,CAACqI,UAAU,CAAChE,WAAW,CAACiE,aAAa,CAAC,KAAK,CAACJ,cAAc,IAAIA,cAAc,KAAK,SAAS,CAAC,EAAE;MACrG;MACA9C,SAAS,CAAC3D,GAAG,CAAC4C,WAAW,CAACiE,aAAa,CAAC;QACtCT,SAAS,EAAEA,SAAS;QACpBC,UAAU,EAAEA,UAAU;QACtBS,IAAI,EAAErD,UAAU;QAChB8C,UAAU,EAAEA,UAAU;QACtBQ,SAAS,EAAErD,KAAK,CAACqD,SAAS;QAC1BC,SAAS,EAAEtD,KAAK,CAACsD,SAAS;QAC1BR,gBAAgB,EAAEA;MACpB,CAAC,CAAC,CAAC;IACL,CAAC,MAAM;MACL;MACA,IAAIS,MAAM,GAAGR,cAAc,KAAK,SAAS,IAAI7D,WAAW,CAACG,OAAO,CAAC,CAAC,CAACS,SAAS,CAAC,QAAQ,CAAC,GAAG+C,UAAU,KAAK,SAAS,GAAG3D,WAAW,CAACG,OAAO,CAAC,CAAC,CAACS,SAAS,CAAC,cAAc,CAAC,GAAG+C,UAAU,GAAG,CAAC,CAAC,CAAC;;MAEtL5C,SAAS,CAAC3D,GAAG,CAACkH,oBAAoB,CAAC;QACjCd,SAAS,EAAEA,SAAS;QACpBC,UAAU,EAAEA,UAAU;QACtBS,IAAI,EAAErD,UAAU;QAChB8C,UAAU,EAAEU,MAAM;QAClBF,SAAS,EAAErD,KAAK,CAACqD,SAAS;QAC1BC,SAAS,EAAEtD,KAAK,CAACsD,SAAS;QAC1BR,gBAAgB,EAAEA;MACpB,CAAC,CAAC,CAAC;IACL;IAEA,IAAIW,KAAK,GAAGtG,SAAS,KAAK,MAAM,GAAGuF,SAAS,GAAG,CAAC,GAAG,CAAC,CAAC;IACrD,IAAIgB,SAAS,GAAGvG,SAAS;IACzB,IAAIwG,SAAS,GAAG9G,WAAW,CAACK,GAAG,CAAC,WAAW,CAAC;IAC5C,IAAI0G,OAAO,GAAGpE,IAAI;IAElB,IAAI3E,MAAM,CAACgJ,QAAQ,CAACF,SAAS,CAAC,IAAIA,SAAS,EAAE;MAC3CC,OAAO,GAAGD,SAAS,CAACG,OAAO,CAAC,QAAQ,EAAEtE,IAAI,IAAI,IAAI,GAAGA,IAAI,GAAG,EAAE,CAAC;IACjE,CAAC,MAAM,IAAI3E,MAAM,CAACqI,UAAU,CAACS,SAAS,CAAC,EAAE;MACvCC,OAAO,GAAGD,SAAS,CAACnE,IAAI,CAAC;IAC3B;IAEA,IAAIuE,SAAS,GAAGnB,UAAU,GAAGK,cAAc,CAACe,YAAY,CAAC,CAAC,GAAG1E,eAAe,CAACpC,GAAG,CAAC,eAAe,CAAC;IACjG+C,SAAS,CAAC3D,GAAG,CAAC,IAAItB,OAAO,CAAC2G,IAAI,CAAC;MAC7B3B,KAAK,EAAE7E,eAAe,CAAC8H,cAAc,EAAE;QACrCgB,IAAI,EAAEL,OAAO;QACbtF,CAAC,EAAEmF,KAAK;QACRlF,CAAC,EAAEoE,UAAU,GAAG,CAAC;QACjB5B,IAAI,EAAEgD,SAAS;QACfnC,KAAK,EAAE8B,SAAS;QAChB7B,aAAa,EAAE;MACjB,CAAC,EAAE;QACDqC,YAAY,EAAEH;MAChB,CAAC;IACH,CAAC,CAAC,CAAC,CAAC,CAAC;;IAEL,IAAII,OAAO,GAAG,IAAInJ,OAAO,CAACoJ,IAAI,CAAC;MAC7BC,KAAK,EAAEpE,SAAS,CAACqE,eAAe,CAAC,CAAC;MAClCC,SAAS,EAAE;IACb,CAAC,CAAC;IACF,IAAIC,YAAY,GAAGlF,eAAe,CAAC2C,QAAQ,CAAC,SAAS,CAAC;IAEtD,IAAIuC,YAAY,CAACtH,GAAG,CAAC,MAAM,CAAC,EAAE;MAC5BlC,OAAO,CAACyJ,gBAAgB,CAAC;QACvBC,EAAE,EAAEP,OAAO;QACXQ,cAAc,EAAE9H,WAAW;QAC3B+H,QAAQ,EAAEpF,IAAI;QACdqF,iBAAiB,EAAEL,YAAY,CAACM;MAClC,CAAC,CAAC;IACJ;IAEA7E,SAAS,CAAC3D,GAAG,CAAC6H,OAAO,CAAC;IACtBlE,SAAS,CAAC8E,SAAS,CAAC,UAAUC,KAAK,EAAE;MACnCA,KAAK,CAACC,MAAM,GAAG,IAAI;IACrB,CAAC,CAAC;IACFd,OAAO,CAACc,MAAM,GAAG,CAAClG,UAAU;IAC5B,IAAI,CAACrC,eAAe,CAAC,CAAC,CAACJ,GAAG,CAAC2D,SAAS,CAAC;IACrChF,mBAAmB,CAACgF,SAAS,CAAC,CAAC,CAAC;;IAEhCA,SAAS,CAACiF,iBAAiB,GAAG3F,SAAS;IACvC,OAAOU,SAAS;EAClB,CAAC;EAEDrE,UAAU,CAACO,SAAS,CAACgC,WAAW,GAAG,UAAUtB,WAAW,EAAEM,SAAS,EAAEa,OAAO,EAAEhB,aAAa,EAAEK,QAAQ,EAAEC,gBAAgB,EAAE;IACvH,IAAIsB,YAAY,GAAG,IAAI,CAAClC,eAAe,CAAC,CAAC;IACzC,IAAI6E,aAAa,GAAG,IAAI,CAAC5E,gBAAgB,CAAC,CAAC,CAAC,CAAC;;IAE7CtB,UAAU,CAAC8J,GAAG,CAACtI,WAAW,CAACK,GAAG,CAAC,QAAQ,CAAC,EAAE0B,YAAY,EAAE/B,WAAW,CAACK,GAAG,CAAC,SAAS,CAAC,EAAEc,OAAO,CAACL,KAAK,EAAEK,OAAO,CAACH,MAAM,CAAC;IAClH,IAAIuH,WAAW,GAAGxG,YAAY,CAAC0F,eAAe,CAAC,CAAC;IAChD,IAAIe,UAAU,GAAG,CAAC,CAACD,WAAW,CAAC9G,CAAC,EAAE,CAAC8G,WAAW,CAAC7G,CAAC,CAAC;IACjDgD,aAAa,CAAC/C,UAAU,CAAC,CAAC;IAC1BI,YAAY,CAACJ,UAAU,CAAC,CAAC;IAEzB,IAAInB,QAAQ,EAAE;MACZ;MACAhC,UAAU,CAAC8J,GAAG;MAAE;MAChB,YAAY,EAAE5D,aAAa,EAAE1E,WAAW,CAACK,GAAG,CAAC,iBAAiB,EAAE,IAAI,CAAC,CAAC;MACtE,IAAIoI,YAAY,GAAG/D,aAAa,CAAC+C,eAAe,CAAC,CAAC;MAClD,IAAIiB,WAAW,GAAG,CAAC,CAACD,YAAY,CAAChH,CAAC,EAAE,CAACgH,YAAY,CAAC/G,CAAC,CAAC;MACpD,IAAIiH,iBAAiB,GAAG3I,WAAW,CAACK,GAAG,CAAC,mBAAmB,EAAE,IAAI,CAAC;MAClE,IAAIuI,SAAS,GAAG5I,WAAW,CAAC6I,SAAS,CAAC,CAAC,CAACC,KAAK;MAC7C,IAAIC,EAAE,GAAGH,SAAS,KAAK,CAAC,GAAG,OAAO,GAAG,QAAQ;MAC7C,IAAII,EAAE,GAAGJ,SAAS,KAAK,CAAC,GAAG,QAAQ,GAAG,OAAO;MAC7C,IAAIK,EAAE,GAAGL,SAAS,KAAK,CAAC,GAAG,GAAG,GAAG,GAAG;MAEpC,IAAInI,gBAAgB,KAAK,KAAK,EAAE;QAC9BiI,WAAW,CAACE,SAAS,CAAC,IAAIL,WAAW,CAACQ,EAAE,CAAC,GAAGJ,iBAAiB;MAC/D,CAAC,MAAM;QACLH,UAAU,CAACI,SAAS,CAAC,IAAIH,YAAY,CAACM,EAAE,CAAC,GAAGJ,iBAAiB;MAC/D,CAAC,CAAC;;MAGFD,WAAW,CAAC,CAAC,GAAGE,SAAS,CAAC,IAAIL,WAAW,CAACS,EAAE,CAAC,GAAG,CAAC,GAAGP,YAAY,CAACO,EAAE,CAAC,GAAG,CAAC;MACxEtE,aAAa,CAACjD,CAAC,GAAGiH,WAAW,CAAC,CAAC,CAAC;MAChChE,aAAa,CAAChD,CAAC,GAAGgH,WAAW,CAAC,CAAC,CAAC;MAChC3G,YAAY,CAACN,CAAC,GAAG+G,UAAU,CAAC,CAAC,CAAC;MAC9BzG,YAAY,CAACL,CAAC,GAAG8G,UAAU,CAAC,CAAC,CAAC;MAC9B,IAAInH,QAAQ,GAAG;QACbI,CAAC,EAAE,CAAC;QACJC,CAAC,EAAE;MACL,CAAC;MACDL,QAAQ,CAAC0H,EAAE,CAAC,GAAGR,WAAW,CAACQ,EAAE,CAAC,GAAGJ,iBAAiB,GAAGF,YAAY,CAACM,EAAE,CAAC;MACrE1H,QAAQ,CAAC2H,EAAE,CAAC,GAAGE,IAAI,CAACC,GAAG,CAACZ,WAAW,CAACS,EAAE,CAAC,EAAEP,YAAY,CAACO,EAAE,CAAC,CAAC;MAC1D3H,QAAQ,CAAC4H,EAAE,CAAC,GAAGC,IAAI,CAACE,GAAG,CAAC,CAAC,EAAEX,YAAY,CAACQ,EAAE,CAAC,GAAGP,WAAW,CAAC,CAAC,GAAGE,SAAS,CAAC,CAAC;MACzE,OAAOvH,QAAQ;IACjB,CAAC,MAAM;MACLU,YAAY,CAACN,CAAC,GAAG+G,UAAU,CAAC,CAAC,CAAC;MAC9BzG,YAAY,CAACL,CAAC,GAAG8G,UAAU,CAAC,CAAC,CAAC;MAC9B,OAAO,IAAI,CAAChJ,KAAK,CAACiI,eAAe,CAAC,CAAC;IACrC;EACF,CAAC;EACD;AACF;AACA;;EAGE1I,UAAU,CAACO,SAAS,CAACwC,MAAM,GAAG,YAAY;IACxC,IAAI,CAACjC,eAAe,CAAC,CAAC,CAACgC,SAAS,CAAC,CAAC;IAClC,IAAI,CAACjC,cAAc,GAAG,IAAI;EAC5B,CAAC;EAEDb,UAAU,CAACK,IAAI,GAAG,cAAc;EAChC,OAAOL,UAAU;AACnB,CAAC,CAACN,aAAa,CAAC;AAEhB,SAAS0H,cAAcA,CAACkD,QAAQ,EAAE5G,eAAe,EAAEO,eAAe,EAAE0C,eAAe,EAAEC,QAAQ,EAAEI,UAAU,EAAE7F,GAAG,EAAE;EAC9G;AACF;AACA;AACA;EACE,SAASoJ,iBAAiBA,CAACnG,KAAK,EAAEoG,WAAW,EAAE;IAC7C;IACA,IAAIpG,KAAK,CAACqG,SAAS,KAAK,MAAM,EAAE;MAC9BrG,KAAK,CAACqG,SAAS,GAAGD,WAAW,CAACC,SAAS,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC;IACrD;IAEA3K,IAAI,CAACsE,KAAK,EAAE,UAAUsG,OAAO,EAAEC,QAAQ,EAAE;MACvCvG,KAAK,CAACuG,QAAQ,CAAC,KAAK,SAAS,KAAKvG,KAAK,CAACuG,QAAQ,CAAC,GAAGH,WAAW,CAACG,QAAQ,CAAC,CAAC;IAC5E,CAAC,CAAC;EACJ,CAAC,CAAC;;EAGF,IAAIC,cAAc,GAAGlH,eAAe,CAAC2C,QAAQ,CAAC,WAAW,CAAC;EAC1D,IAAIoB,SAAS,GAAGmD,cAAc,CAACC,YAAY,CAAC,CAAC;EAC7C,IAAIC,aAAa,GAAGR,QAAQ,CAACS,WAAW,CAAC,OAAO,EAAE,CAAC,CAAC,KAAK,CAAC,GAAG,MAAM,GAAG,QAAQ;EAC9E,IAAIC,UAAU,GAAGJ,cAAc,CAACK,UAAU,CAAC,OAAO,CAAC;EACnDxD,SAAS,CAACyD,KAAK,GAAG,CAACF,UAAU,IAAIA,UAAU,KAAK,SAAS,GAAGrE,eAAe,CAACuE,KAAK,GAAGtL,8BAA8B,CAACoL,UAAU,EAAE7J,GAAG,CAAC;EAEnI,IAAIsG,SAAS,CAACtC,IAAI,KAAK,SAAS,EAAE;IAChC;AACJ;AACA;AACA;IACIsC,SAAS,CAACtC,IAAI,GAAGwB,eAAe,CAACC,QAAQ,CAAC;EAC5C;EAEA,IAAIa,SAAS,CAAC0D,MAAM,KAAK,SAAS,EAAE;IAClC;AACJ;AACA;AACA;IACI1D,SAAS,CAAC0D,MAAM,GAAGxE,eAAe,CAACmE,aAAa,CAAC;EACnD;EAEA,IAAIrD,SAAS,CAAC2D,OAAO,KAAK,SAAS,EAAE;IACnC;AACJ;AACA;IACI3D,SAAS,CAAC2D,OAAO,GAAG,CAACxE,QAAQ,KAAK,MAAM,GAAGD,eAAe,GAAG1C,eAAe,EAAEmH,OAAO;EACvF;EAEAb,iBAAiB,CAAC9C,SAAS,EAAEd,eAAe,CAAC,CAAC,CAAC;;EAE/C,IAAI0E,eAAe,GAAG3H,eAAe,CAAC2C,QAAQ,CAAC,WAAW,CAAC;EAC3D,IAAIqB,SAAS,GAAG2D,eAAe,CAACC,YAAY,CAAC,CAAC;EAC9Cf,iBAAiB,CAAC7C,SAAS,EAAEzD,eAAe,CAAC,CAAC,CAAC;;EAE/CwD,SAAS,CAACtC,IAAI,KAAK,MAAM,KAAKsC,SAAS,CAACtC,IAAI,GAAGwB,eAAe,CAACxB,IAAI,CAAC;EACpEsC,SAAS,CAAC0D,MAAM,KAAK,MAAM,KAAK1D,SAAS,CAAC0D,MAAM,GAAGxE,eAAe,CAACxB,IAAI,CAAC;EACxEuC,SAAS,CAACyD,MAAM,KAAK,MAAM,KAAKzD,SAAS,CAACyD,MAAM,GAAGxE,eAAe,CAACxB,IAAI,CAAC;EAExE,IAAI,CAAC6B,UAAU,EAAE;IACf,IAAIuE,WAAW,GAAG7H,eAAe,CAACpC,GAAG,CAAC,qBAAqB,CAAC;IAC5D;AACJ;AACA;AACA;AACA;;IAEI,IAAIkK,eAAe,GAAG/D,SAAS,CAACqD,aAAa,CAAC;IAC9CrD,SAAS,CAACgD,SAAS,GAAGc,WAAW,KAAK,MAAM,GAAG5E,eAAe,CAAC8D,SAAS,GAAG,CAAC,IAAIe,eAAe,GAAG,CAAC,GAAG,CAAC,GAAG/D,SAAS,CAACgD,SAAS;IAC7HhD,SAAS,CAACtC,IAAI,GAAGzB,eAAe,CAACpC,GAAG,CAAC,eAAe,CAAC;IACrDmG,SAAS,CAAC0D,MAAM,GAAGzH,eAAe,CAACpC,GAAG,CAAC,qBAAqB,CAAC;IAC7DoG,SAAS,CAACyD,MAAM,GAAGE,eAAe,CAAC/J,GAAG,CAAC,eAAe,CAAC;IACvDoG,SAAS,CAAC+C,SAAS,GAAGY,eAAe,CAAC/J,GAAG,CAAC,eAAe,CAAC;EAC5D;EAEA,OAAO;IACLmG,SAAS,EAAEA,SAAS;IACpBC,SAAS,EAAEA;EACb,CAAC;AACH;AAEA,SAASE,oBAAoBA,CAAC6D,GAAG,EAAE;EACjC,IAAIC,SAAS,GAAGD,GAAG,CAACjE,IAAI,IAAI,WAAW;EACvC,IAAIA,IAAI,GAAG7H,YAAY,CAAC+L,SAAS,EAAE,CAAC,EAAE,CAAC,EAAED,GAAG,CAAC3E,SAAS,EAAE2E,GAAG,CAAC1E,UAAU,EAAE0E,GAAG,CAAChE,SAAS,CAACtC,IAAI,EAAEsG,GAAG,CAACvE,gBAAgB,CAAC;EACjHM,IAAI,CAACmE,QAAQ,CAACF,GAAG,CAAChE,SAAS,CAAC;EAC5BD,IAAI,CAACoE,QAAQ,GAAG,CAACH,GAAG,CAACxE,UAAU,IAAI,CAAC,IAAIkD,IAAI,CAAC0B,EAAE,GAAG,GAAG;EACrDrE,IAAI,CAACsE,SAAS,CAAC,CAACL,GAAG,CAAC3E,SAAS,GAAG,CAAC,EAAE2E,GAAG,CAAC1E,UAAU,GAAG,CAAC,CAAC,CAAC;EAEvD,IAAI2E,SAAS,CAACK,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,EAAE;IACnCvE,IAAI,CAACpD,KAAK,CAAC+G,MAAM,GAAG3D,IAAI,CAACpD,KAAK,CAACe,IAAI;IACnCqC,IAAI,CAACpD,KAAK,CAACe,IAAI,GAAG,MAAM;IACxBqC,IAAI,CAACpD,KAAK,CAACqG,SAAS,GAAG,CAAC;EAC1B;EAEA,OAAOjD,IAAI;AACb;AAEA,SAAShD,oBAAoBA,CAACwH,UAAU,EAAEC,QAAQ,EAAE9K,GAAG,EAAEiC,eAAe,EAAE;EACxE;EACAsB,sBAAsB,CAACsH,UAAU,EAAEC,QAAQ,EAAE9K,GAAG,EAAEiC,eAAe,CAAC;EAClEjC,GAAG,CAACgF,cAAc,CAAC;IACjB9F,IAAI,EAAE,oBAAoB;IAC1BuD,IAAI,EAAEoI,UAAU,IAAI,IAAI,GAAGA,UAAU,GAAGC;EAC1C,CAAC,CAAC,CAAC,CAAC;EACJ;;EAEAxH,uBAAuB,CAACuH,UAAU,EAAEC,QAAQ,EAAE9K,GAAG,EAAEiC,eAAe,CAAC;AACrE;AAEA,SAAS8I,eAAeA,CAAC/K,GAAG,EAAE;EAC5B,IAAIgL,IAAI,GAAGhL,GAAG,CAACiL,KAAK,CAAC,CAAC,CAACC,OAAO,CAACC,cAAc,CAAC,CAAC;EAC/C,IAAIC,aAAa;EACjB,IAAIC,CAAC,GAAG,CAAC;EACT,IAAIC,GAAG,GAAGN,IAAI,CAACO,MAAM;EAErB,OAAOF,CAAC,GAAGC,GAAG,IAAI,EAAEF,aAAa,GAAGJ,IAAI,CAACK,CAAC,CAAC,CAACG,MAAM,CAACnG,QAAQ,CAAC,EAAE;IAC5DgG,CAAC,EAAE;EACL;EAEA,OAAOD,aAAa,IAAIA,aAAa,CAACK,UAAU;AAClD;AAEA,SAASnI,uBAAuBA,CAACuH,UAAU,EAAEC,QAAQ,EAAE9K,GAAG,EAAEiC,eAAe,EAAE;EAC3E;EACA,IAAI,CAAC8I,eAAe,CAAC/K,GAAG,CAAC,EAAE;IACzBA,GAAG,CAACgF,cAAc,CAAC;MACjB9F,IAAI,EAAE,WAAW;MACjB2L,UAAU,EAAEA,UAAU;MACtBpI,IAAI,EAAEqI,QAAQ;MACd7I,eAAe,EAAEA;IACnB,CAAC,CAAC;EACJ;AACF;AAEA,SAASsB,sBAAsBA,CAACsH,UAAU,EAAEC,QAAQ,EAAE9K,GAAG,EAAEiC,eAAe,EAAE;EAC1E;EACA,IAAI,CAAC8I,eAAe,CAAC/K,GAAG,CAAC,EAAE;IACzBA,GAAG,CAACgF,cAAc,CAAC;MACjB9F,IAAI,EAAE,UAAU;MAChB2L,UAAU,EAAEA,UAAU;MACtBpI,IAAI,EAAEqI,QAAQ;MACd7I,eAAe,EAAEA;IACnB,CAAC,CAAC;EACJ;AACF;AAEA,eAAepD,UAAU"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}