{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { map, isString, isFunction, eqNaN, isRegExp } from 'zrender/lib/core/util.js';\nvar ECHARTS_PREFIX = '[ECharts] ';\nvar storedLogs = {};\nvar hasConsole = typeof console !== 'undefined' // eslint-disable-next-line\n&& console.warn && console.log;\nfunction outputLog(type, str, onlyOnce) {\n  if (hasConsole) {\n    if (onlyOnce) {\n      if (storedLogs[str]) {\n        return;\n      }\n      storedLogs[str] = true;\n    } // eslint-disable-next-line\n\n    console[type](ECHARTS_PREFIX + str);\n  }\n}\nexport function log(str, onlyOnce) {\n  outputLog('log', str, onlyOnce);\n}\nexport function warn(str, onlyOnce) {\n  outputLog('warn', str, onlyOnce);\n}\nexport function error(str, onlyOnce) {\n  outputLog('error', str, onlyOnce);\n}\nexport function deprecateLog(str) {\n  if (process.env.NODE_ENV !== 'production') {\n    // Not display duplicate message.\n    outputLog('warn', 'DEPRECATED: ' + str, true);\n  }\n}\nexport function deprecateReplaceLog(oldOpt, newOpt, scope) {\n  if (process.env.NODE_ENV !== 'production') {\n    deprecateLog((scope ? \"[\" + scope + \"]\" : '') + (oldOpt + \" is deprecated, use \" + newOpt + \" instead.\"));\n  }\n}\n/**\r\n * If in __DEV__ environment, get console printable message for users hint.\r\n * Parameters are separated by ' '.\r\n * @usage\r\n * makePrintable('This is an error on', someVar, someObj);\r\n *\r\n * @param hintInfo anything about the current execution context to hint users.\r\n * @throws Error\r\n */\n\nexport function makePrintable() {\n  var hintInfo = [];\n  for (var _i = 0; _i < arguments.length; _i++) {\n    hintInfo[_i] = arguments[_i];\n  }\n  var msg = '';\n  if (process.env.NODE_ENV !== 'production') {\n    // Fuzzy stringify for print.\n    // This code only exist in dev environment.\n    var makePrintableStringIfPossible_1 = function (val) {\n      return val === void 0 ? 'undefined' : val === Infinity ? 'Infinity' : val === -Infinity ? '-Infinity' : eqNaN(val) ? 'NaN' : val instanceof Date ? 'Date(' + val.toISOString() + ')' : isFunction(val) ? 'function () { ... }' : isRegExp(val) ? val + '' : null;\n    };\n    msg = map(hintInfo, function (arg) {\n      if (isString(arg)) {\n        // Print without quotation mark for some statement.\n        return arg;\n      } else {\n        var printableStr = makePrintableStringIfPossible_1(arg);\n        if (printableStr != null) {\n          return printableStr;\n        } else if (typeof JSON !== 'undefined' && JSON.stringify) {\n          try {\n            return JSON.stringify(arg, function (n, val) {\n              var printableStr = makePrintableStringIfPossible_1(val);\n              return printableStr == null ? val : printableStr;\n            }); // In most cases the info object is small, so do not line break.\n          } catch (err) {\n            return '?';\n          }\n        } else {\n          return '?';\n        }\n      }\n    }).join(' ');\n  }\n  return msg;\n}\n/**\r\n * @throws Error\r\n */\n\nexport function throwError(msg) {\n  throw new Error(msg);\n}", "map": {"version": 3, "names": ["map", "isString", "isFunction", "eqNaN", "isRegExp", "ECHARTS_PREFIX", "storedLogs", "hasConsole", "console", "warn", "log", "outputLog", "type", "str", "onlyOnce", "error", "deprecateLog", "process", "env", "NODE_ENV", "deprecateReplaceLog", "oldOpt", "newOpt", "scope", "makePrintable", "hintInfo", "_i", "arguments", "length", "msg", "makePrintableStringIfPossible_1", "val", "Infinity", "Date", "toISOString", "arg", "printableStr", "JSON", "stringify", "n", "err", "join", "throwError", "Error"], "sources": ["C:/WolfSystemV2/WolfOldFrontEnd/node_modules/echarts/lib/util/log.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { map, isString, isFunction, eqNaN, isRegExp } from 'zrender/lib/core/util.js';\nvar ECHARTS_PREFIX = '[ECharts] ';\nvar storedLogs = {};\nvar hasConsole = typeof console !== 'undefined' // eslint-disable-next-line\n&& console.warn && console.log;\n\nfunction outputLog(type, str, onlyOnce) {\n  if (hasConsole) {\n    if (onlyOnce) {\n      if (storedLogs[str]) {\n        return;\n      }\n\n      storedLogs[str] = true;\n    } // eslint-disable-next-line\n\n\n    console[type](ECHARTS_PREFIX + str);\n  }\n}\n\nexport function log(str, onlyOnce) {\n  outputLog('log', str, onlyOnce);\n}\nexport function warn(str, onlyOnce) {\n  outputLog('warn', str, onlyOnce);\n}\nexport function error(str, onlyOnce) {\n  outputLog('error', str, onlyOnce);\n}\nexport function deprecateLog(str) {\n  if (process.env.NODE_ENV !== 'production') {\n    // Not display duplicate message.\n    outputLog('warn', 'DEPRECATED: ' + str, true);\n  }\n}\nexport function deprecateReplaceLog(oldOpt, newOpt, scope) {\n  if (process.env.NODE_ENV !== 'production') {\n    deprecateLog((scope ? \"[\" + scope + \"]\" : '') + (oldOpt + \" is deprecated, use \" + newOpt + \" instead.\"));\n  }\n}\n/**\r\n * If in __DEV__ environment, get console printable message for users hint.\r\n * Parameters are separated by ' '.\r\n * @usage\r\n * makePrintable('This is an error on', someVar, someObj);\r\n *\r\n * @param hintInfo anything about the current execution context to hint users.\r\n * @throws Error\r\n */\n\nexport function makePrintable() {\n  var hintInfo = [];\n\n  for (var _i = 0; _i < arguments.length; _i++) {\n    hintInfo[_i] = arguments[_i];\n  }\n\n  var msg = '';\n\n  if (process.env.NODE_ENV !== 'production') {\n    // Fuzzy stringify for print.\n    // This code only exist in dev environment.\n    var makePrintableStringIfPossible_1 = function (val) {\n      return val === void 0 ? 'undefined' : val === Infinity ? 'Infinity' : val === -Infinity ? '-Infinity' : eqNaN(val) ? 'NaN' : val instanceof Date ? 'Date(' + val.toISOString() + ')' : isFunction(val) ? 'function () { ... }' : isRegExp(val) ? val + '' : null;\n    };\n\n    msg = map(hintInfo, function (arg) {\n      if (isString(arg)) {\n        // Print without quotation mark for some statement.\n        return arg;\n      } else {\n        var printableStr = makePrintableStringIfPossible_1(arg);\n\n        if (printableStr != null) {\n          return printableStr;\n        } else if (typeof JSON !== 'undefined' && JSON.stringify) {\n          try {\n            return JSON.stringify(arg, function (n, val) {\n              var printableStr = makePrintableStringIfPossible_1(val);\n              return printableStr == null ? val : printableStr;\n            }); // In most cases the info object is small, so do not line break.\n          } catch (err) {\n            return '?';\n          }\n        } else {\n          return '?';\n        }\n      }\n    }).join(' ');\n  }\n\n  return msg;\n}\n/**\r\n * @throws Error\r\n */\n\nexport function throwError(msg) {\n  throw new Error(msg);\n}"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,GAAG,EAAEC,QAAQ,EAAEC,UAAU,EAAEC,KAAK,EAAEC,QAAQ,QAAQ,0BAA0B;AACrF,IAAIC,cAAc,GAAG,YAAY;AACjC,IAAIC,UAAU,GAAG,CAAC,CAAC;AACnB,IAAIC,UAAU,GAAG,OAAOC,OAAO,KAAK,WAAW,CAAC;AAAA,GAC7CA,OAAO,CAACC,IAAI,IAAID,OAAO,CAACE,GAAG;AAE9B,SAASC,SAASA,CAACC,IAAI,EAAEC,GAAG,EAAEC,QAAQ,EAAE;EACtC,IAAIP,UAAU,EAAE;IACd,IAAIO,QAAQ,EAAE;MACZ,IAAIR,UAAU,CAACO,GAAG,CAAC,EAAE;QACnB;MACF;MAEAP,UAAU,CAACO,GAAG,CAAC,GAAG,IAAI;IACxB,CAAC,CAAC;;IAGFL,OAAO,CAACI,IAAI,CAAC,CAACP,cAAc,GAAGQ,GAAG,CAAC;EACrC;AACF;AAEA,OAAO,SAASH,GAAGA,CAACG,GAAG,EAAEC,QAAQ,EAAE;EACjCH,SAAS,CAAC,KAAK,EAAEE,GAAG,EAAEC,QAAQ,CAAC;AACjC;AACA,OAAO,SAASL,IAAIA,CAACI,GAAG,EAAEC,QAAQ,EAAE;EAClCH,SAAS,CAAC,MAAM,EAAEE,GAAG,EAAEC,QAAQ,CAAC;AAClC;AACA,OAAO,SAASC,KAAKA,CAACF,GAAG,EAAEC,QAAQ,EAAE;EACnCH,SAAS,CAAC,OAAO,EAAEE,GAAG,EAAEC,QAAQ,CAAC;AACnC;AACA,OAAO,SAASE,YAAYA,CAACH,GAAG,EAAE;EAChC,IAAII,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IACzC;IACAR,SAAS,CAAC,MAAM,EAAE,cAAc,GAAGE,GAAG,EAAE,IAAI,CAAC;EAC/C;AACF;AACA,OAAO,SAASO,mBAAmBA,CAACC,MAAM,EAAEC,MAAM,EAAEC,KAAK,EAAE;EACzD,IAAIN,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IACzCH,YAAY,CAAC,CAACO,KAAK,GAAG,GAAG,GAAGA,KAAK,GAAG,GAAG,GAAG,EAAE,KAAKF,MAAM,GAAG,sBAAsB,GAAGC,MAAM,GAAG,WAAW,CAAC,CAAC;EAC3G;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,OAAO,SAASE,aAAaA,CAAA,EAAG;EAC9B,IAAIC,QAAQ,GAAG,EAAE;EAEjB,KAAK,IAAIC,EAAE,GAAG,CAAC,EAAEA,EAAE,GAAGC,SAAS,CAACC,MAAM,EAAEF,EAAE,EAAE,EAAE;IAC5CD,QAAQ,CAACC,EAAE,CAAC,GAAGC,SAAS,CAACD,EAAE,CAAC;EAC9B;EAEA,IAAIG,GAAG,GAAG,EAAE;EAEZ,IAAIZ,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IACzC;IACA;IACA,IAAIW,+BAA+B,GAAG,SAAAA,CAAUC,GAAG,EAAE;MACnD,OAAOA,GAAG,KAAK,KAAK,CAAC,GAAG,WAAW,GAAGA,GAAG,KAAKC,QAAQ,GAAG,UAAU,GAAGD,GAAG,KAAK,CAACC,QAAQ,GAAG,WAAW,GAAG7B,KAAK,CAAC4B,GAAG,CAAC,GAAG,KAAK,GAAGA,GAAG,YAAYE,IAAI,GAAG,OAAO,GAAGF,GAAG,CAACG,WAAW,CAAC,CAAC,GAAG,GAAG,GAAGhC,UAAU,CAAC6B,GAAG,CAAC,GAAG,qBAAqB,GAAG3B,QAAQ,CAAC2B,GAAG,CAAC,GAAGA,GAAG,GAAG,EAAE,GAAG,IAAI;IAClQ,CAAC;IAEDF,GAAG,GAAG7B,GAAG,CAACyB,QAAQ,EAAE,UAAUU,GAAG,EAAE;MACjC,IAAIlC,QAAQ,CAACkC,GAAG,CAAC,EAAE;QACjB;QACA,OAAOA,GAAG;MACZ,CAAC,MAAM;QACL,IAAIC,YAAY,GAAGN,+BAA+B,CAACK,GAAG,CAAC;QAEvD,IAAIC,YAAY,IAAI,IAAI,EAAE;UACxB,OAAOA,YAAY;QACrB,CAAC,MAAM,IAAI,OAAOC,IAAI,KAAK,WAAW,IAAIA,IAAI,CAACC,SAAS,EAAE;UACxD,IAAI;YACF,OAAOD,IAAI,CAACC,SAAS,CAACH,GAAG,EAAE,UAAUI,CAAC,EAAER,GAAG,EAAE;cAC3C,IAAIK,YAAY,GAAGN,+BAA+B,CAACC,GAAG,CAAC;cACvD,OAAOK,YAAY,IAAI,IAAI,GAAGL,GAAG,GAAGK,YAAY;YAClD,CAAC,CAAC,CAAC,CAAC;UACN,CAAC,CAAC,OAAOI,GAAG,EAAE;YACZ,OAAO,GAAG;UACZ;QACF,CAAC,MAAM;UACL,OAAO,GAAG;QACZ;MACF;IACF,CAAC,CAAC,CAACC,IAAI,CAAC,GAAG,CAAC;EACd;EAEA,OAAOZ,GAAG;AACZ;AACA;AACA;AACA;;AAEA,OAAO,SAASa,UAAUA,CAACb,GAAG,EAAE;EAC9B,MAAM,IAAIc,KAAK,CAACd,GAAG,CAAC;AACtB"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}